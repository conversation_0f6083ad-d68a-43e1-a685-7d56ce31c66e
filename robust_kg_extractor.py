"""
RobustKGExtractor - Enhanced Knowledge Graph Extractor with resumability and error handling.

This module provides a robust wrapper around the existing KnowledgeGraphExtractor
with comprehensive error handling, resume functionality, and progress tracking.
"""

import os
import json
import glob
import time
from typing import Dict, List, Optional, Any, Set
from pathlib import Path
from atlas_rag.kg_construction.triple_extraction import KnowledgeGraphExtractor
from atlas_rag.kg_construction.triple_config import ProcessingConfig
from atlas_rag.llm_generator import LLMGenerator
from atlas_rag.vectorstore.embedding_model import BaseEmbeddingModel

from pipeline_state_manager import PipelineStateManager, PipelineState
from progress_tracker import ProgressTracker
from error_handler import <PERSON>rrorHandler, ErrorConfig


class RobustKGExtractor:
    """
    Enhanced Knowledge Graph Extractor with resumability, error handling, and progress tracking.
    
    This class wraps the existing KnowledgeGraphExtractor and adds:
    - Resume functionality from checkpoints
    - Comprehensive error handling with retry logic
    - Real-time progress tracking and logging
    - State management for long-running operations
    """
    
    def __init__(self, 
                 model: LLMGenerator, 
                 config: ProcessingConfig,
                 resume_enabled: bool = True,
                 error_config: ErrorConfig = None,
                 progress_log_level: str = "INFO"):
        """
        Initialize the robust knowledge graph extractor.
        
        Args:
            model: LLM generator for extraction
            config: Processing configuration
            resume_enabled: Whether to enable resume functionality
            error_config: Error handling configuration
            progress_log_level: Logging level for progress tracking
        """
        # Core components
        self.model = model
        self.config = config
        self.kg_extractor = KnowledgeGraphExtractor(model, config)
        
        # Enhanced components
        self.resume_enabled = resume_enabled
        self.state_manager = PipelineStateManager(
            dataset_name=self._extract_dataset_name(),
            state_file=f"pipeline_state_{self._extract_dataset_name()}.json"
        )
        
        self.progress_tracker = ProgressTracker(
            dataset_name=self._extract_dataset_name(),
            total_files=0,  # Will be updated when files are loaded
            log_level=progress_log_level
        )
        
        self.error_handler = ErrorHandler(
            config=error_config or ErrorConfig(),
            logger=self.progress_tracker.logger
        )
        
        # Processing state
        self.input_files: List[str] = []
        self.processed_documents: Set[str] = set()
        self.current_stage = ""
        self.pipeline_started = False
        
        self.progress_tracker.log_info("🛡️ RobustKGExtractor initialized with enhanced error handling and resumability")
    
    def _extract_dataset_name(self) -> str:
        """Extract dataset name from output directory."""
        output_path = Path(self.config.output_directory)
        return output_path.name
    
    def initialize_pipeline(self, force_restart: bool = False) -> bool:
        """
        Initialize the pipeline, loading existing state or creating new state.
        
        Args:
            force_restart: If True, ignore existing state and start fresh
            
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.progress_tracker.start_stage("initialization", "Initializing robust pipeline")
            
            # Load input files
            self.input_files = self._discover_input_files()
            if not self.input_files:
                self.progress_tracker.log_error("No input files found")
                return False
            
            # Update total files in progress tracker
            self.progress_tracker.total_files = len(self.input_files)
            self.progress_tracker.log_info(f"📁 Discovered {len(self.input_files)} input files")
            
            # Handle state management
            if force_restart:
                self.progress_tracker.log_info("🔄 Force restart requested - clearing existing state")
                self.state_manager.cleanup_state_files()
                pipeline_state = self.state_manager.create_new_state(
                    input_files=self.input_files,
                    config=self._config_to_dict()
                )
            else:
                # Try to load existing state
                pipeline_state = self.state_manager.load_state()
                
                if pipeline_state and self.resume_enabled:
                    self.progress_tracker.log_info("📋 Existing pipeline state found - checking resumability")
                    
                    if self.state_manager.can_resume():
                        self.progress_tracker.log_info("✅ Pipeline can be resumed from previous state")
                        self._load_existing_progress()
                    else:
                        self.progress_tracker.log_info("⚠️ Existing state not resumable - starting fresh")
                        pipeline_state = self.state_manager.create_new_state(
                            input_files=self.input_files,
                            config=self._config_to_dict()
                        )
                else:
                    # Create new state
                    self.progress_tracker.log_info("🆕 Creating new pipeline state")
                    pipeline_state = self.state_manager.create_new_state(
                        input_files=self.input_files,
                        config=self._config_to_dict()
                    )
            
            # Save initial state
            self.state_manager.save_state(force=True)
            self.progress_tracker.complete_stage("initialization")
            self.pipeline_started = True
            
            return True
            
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': 'initialization',
                'operation': 'initialize_pipeline'
            })
            self.progress_tracker.log_error(f"Pipeline initialization failed: {e}")
            return False
    
    def _discover_input_files(self) -> List[str]:
        """Discover input files for processing."""
        data_path = Path(self.config.data_directory)
        
        if not data_path.exists():
            raise FileNotFoundError(f"Data directory does not exist: {self.config.data_directory}")
        
        all_files = os.listdir(data_path)
        valid_files = [
            filename for filename in all_files
            if filename.startswith(self.config.filename_pattern) and
            (filename.endswith(".json.gz") or filename.endswith(".json") or 
             filename.endswith(".jsonl") or filename.endswith(".jsonl.gz"))
        ]
        
        return valid_files
    
    def _config_to_dict(self) -> Dict[str, Any]:
        """Convert processing config to dictionary."""
        return {
            'model_path': self.config.model_path,
            'data_directory': self.config.data_directory,
            'filename_pattern': self.config.filename_pattern,
            'output_directory': self.config.output_directory,
            'batch_size_triple': self.config.batch_size_triple,
            'batch_size_concept': self.config.batch_size_concept,
            'max_new_tokens': self.config.max_new_tokens,
            'max_workers': self.config.max_workers
        }
    
    def _load_existing_progress(self):
        """Load progress from existing output files."""
        try:
            existing_progress = self.state_manager.detect_existing_progress(self._extract_dataset_name())
            
            if existing_progress['processed_documents']:
                self.processed_documents = existing_progress['processed_documents']
                self.progress_tracker.log_info(f"📊 Found {len(self.processed_documents)} previously processed documents")
                
                # Update file progress in tracker
                for filename in self.input_files:
                    # For simplicity, mark files as completed if any documents from them were processed
                    # In a more sophisticated implementation, we'd track per-file document completion
                    if self.processed_documents:  # If any documents were processed
                        self.progress_tracker.complete_file_processing(
                            filename, 
                            documents_processed=len(self.processed_documents)
                        )
            
        except Exception as e:
            self.progress_tracker.log_warning(f"Could not load existing progress: {e}")
    
    def run_extraction(self, retry_failed: bool = False) -> bool:
        """
        Run the knowledge graph extraction with enhanced error handling and resumability.
        
        Args:
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if extraction completed successfully, False otherwise
        """
        if not self.pipeline_started:
            raise RuntimeError("Pipeline not initialized. Call initialize_pipeline() first.")
        
        try:
            self.current_stage = "triple_extraction"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Extracting triples from documents")
            
            # Check if extraction already completed
            if self._is_stage_completed("triple_extraction") and not retry_failed:
                self.progress_tracker.log_info("✅ Triple extraction already completed - skipping")
                return True
            
            # Run extraction with error handling
            success = self.error_handler.execute_with_retry(
                operation=self._run_extraction_with_progress,
                context={
                    'stage': self.current_stage,
                    'total_files': len(self.input_files)
                },
                operation_name="triple_extraction"
            )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "Extraction failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'run_extraction'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"Triple extraction failed: {e}")
            return False
    
    def _run_extraction_with_progress(self):
        """Run extraction with progress tracking."""
        # Create a custom progress callback for the underlying extractor
        original_output_file = self.kg_extractor.create_output_filename()
        
        self.progress_tracker.log_info(f"📄 Starting extraction - output file: {original_output_file}")
        
        # Start progress tracking for each file
        for filename in self.input_files:
            self.progress_tracker.start_file_processing(filename)
            self.state_manager.start_file_processing(filename, self.current_stage)
        
        # Run the actual extraction
        self.kg_extractor.run_extraction()
        
        # Update progress
        for filename in self.input_files:
            self.progress_tracker.complete_file_processing(filename)
            self.state_manager.complete_file_processing(filename, self.current_stage)
        
        self.progress_tracker.log_info("✅ Triple extraction completed successfully")
        return True
    
    def convert_json_to_csv(self, retry_failed: bool = False) -> bool:
        """
        Convert JSON extraction results to CSV format with error handling.
        Handles both original JSONL format and Gemini single JSON array format.
        
        Args:
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if conversion successful, False otherwise
        """
        try:
            self.current_stage = "csv_conversion"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Converting JSON to CSV format")
            
            if self._is_stage_completed("csv_conversion") and not retry_failed:
                self.progress_tracker.log_info("✅ CSV conversion already completed - skipping")
                return True
            
            # Check if we need to use Gemini-compatible converter
            if self._needs_gemini_converter():
                self.progress_tracker.log_info("🔄 Using Gemini-compatible CSV converter")
                success = self.error_handler.execute_with_retry(
                    operation=self._convert_gemini_json_to_csv,
                    context={'stage': self.current_stage},
                    operation_name="csv_conversion"
                )
            else:
                self.progress_tracker.log_info("🔄 Using original CSV converter")
                success = self.error_handler.execute_with_retry(
                    operation=self.kg_extractor.convert_json_to_csv,
                    context={'stage': self.current_stage},
                    operation_name="csv_conversion"
                )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                self.progress_tracker.log_info("✅ CSV conversion completed successfully")
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "CSV conversion failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'convert_json_to_csv'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"CSV conversion failed: {e}")
            return False
    
    def _needs_gemini_converter(self) -> bool:
        """
        Detect if the extraction files are in Gemini format (single JSON array) 
        vs original format (JSONL - one JSON object per line).
        
        Returns:
            True if Gemini format detected, False if original JSONL format
        """
        try:
            import os
            import json
            
            data_dir = os.path.join(self.config.output_directory, "kg_extraction")
            if not os.path.exists(data_dir):
                return False
            
            # Check first few files
            for filename in os.listdir(data_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(data_dir, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            # Read first line to determine format
                            first_line = f.readline().strip()
                            if not first_line:
                                continue
                            
                            # Try to parse first line
                            data = json.loads(first_line)
                            
                            # If first line parses as a list/array, it's Gemini format
                            # If it parses as a dict/object, it's JSONL format
                            if isinstance(data, list):
                                self.progress_tracker.log_info(f"🔍 Detected Gemini format in {filename}")
                                return True
                            elif isinstance(data, dict):
                                # Check if it has the required keys for extraction data
                                required_keys = ['id', 'original_text', 'entity_relation_dict', 'event_entity_relation_dict', 'event_relation_dict']
                                if all(key in data for key in required_keys):
                                    self.progress_tracker.log_info(f"🔍 Detected original JSONL format in {filename}")
                                    return False
                                else:
                                    # Continue checking other files
                                    continue
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        # Try reading as full file for Gemini format
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read().strip()
                                if content.startswith('[') and content.endswith(']'):
                                    self.progress_tracker.log_info(f"🔍 Detected Gemini format (full array) in {filename}")
                                    return True
                        except:
                            continue
            
            # Default to original format if no files found or parsing fails
            self.progress_tracker.log_info("🔍 Defaulting to original JSONL format")
            return False
            
        except Exception as e:
            self.progress_tracker.log_warning(f"⚠️  Error detecting format, defaulting to original: {e}")
            return False
    
    def _convert_gemini_json_to_csv(self) -> bool:
        """
        Convert Gemini extraction format (single JSON array) to CSV format.
        This is a Gemini-compatible version that handles the actual structure correctly.
        
        Returns:
            True if conversion successful, False otherwise
        """
        try:
            import os
            import json
            import csv
            import hashlib
            import re
            from tqdm import tqdm
            
            self.progress_tracker.log_info("🔄 Starting Gemini-compatible CSV conversion")
            
            # Setup directories and files
            data_dir = os.path.join(self.config.output_directory, "kg_extraction")
            output_dir = os.path.join(self.config.output_directory, "triples_csv")
            os.makedirs(output_dir, exist_ok=True)
            
            dataset = self.config.filename_pattern
            
            # Define output file paths
            node_csv_without_emb = os.path.join(output_dir, f"triple_nodes_{dataset}_from_json_without_emb.csv")
            edge_csv_without_emb = os.path.join(output_dir, f"triple_edges_{dataset}_from_json_without_emb.csv")
            node_text_file = os.path.join(output_dir, f"text_nodes_{dataset}_from_json.csv")
            edge_text_file = os.path.join(output_dir, f"text_edges_{dataset}_from_json.csv")
            missing_concepts_file = os.path.join(output_dir, f"missing_concepts_{dataset}_from_json.csv")
            
            # Track visited nodes and hashes to avoid duplicates
            visited_nodes = set()
            visited_hashes = set()
            all_entities = set()
            all_events = set()
            all_relations = set()
            
            # Helper functions (copied from original json2csv)
            def compute_hash_id(text):
                hash_object = hashlib.sha256(text.encode('utf-8'))
                return hash_object.hexdigest()
            
            def clean_text(text):
                new_text = text.replace("\n", " ").replace("\r", " ").replace("\t", " ").replace("\v", " ").replace("\f", " ").replace("\b", " ").replace("\a", " ").replace("\e", " ").replace(";", ",")
                new_text = new_text.replace("\x00", "")
                new_text = re.sub(r'\s+', ' ', new_text).strip()
                return new_text
            
            def remove_NUL(text):
                return text.replace("\x00", "")
            
            # Open CSV files for writing
            with open(node_text_file, "w", newline='', encoding='utf-8', errors='ignore') as csvfile_node_text, \
                 open(edge_text_file, "w", newline='', encoding='utf-8', errors='ignore') as csvfile_edge_text, \
                 open(node_csv_without_emb, "w", newline='', encoding='utf-8', errors='ignore') as csvfile_node, \
                 open(edge_csv_without_emb, "w", newline='', encoding='utf-8', errors='ignore') as csvfile_edge:
                
                csv_writer_node_text = csv.writer(csvfile_node_text)
                csv_writer_edge_text = csv.writer(csvfile_edge_text)
                writer_node = csv.writer(csvfile_node)
                writer_edge = csv.writer(csvfile_edge)
                
                # Write headers
                csv_writer_node_text.writerow(["text_id:ID", "original_text", ":LABEL"])
                csv_writer_edge_text.writerow([":START_ID", ":END_ID", ":TYPE"])
                writer_node.writerow(["name:ID", "type", "concepts", "synsets", ":LABEL"])
                writer_edge.writerow([":START_ID", ":END_ID", "relation", "concepts", "synsets", ":TYPE"])
                
                # Process each file
                file_list = [f for f in os.listdir(data_dir) if dataset in f and f.endswith('.json')]
                file_list = sorted(file_list)
                
                self.progress_tracker.log_info(f"📁 Processing {len(file_list)} files")
                
                for file_name in tqdm(file_list, desc="Converting files"):
                    file_path = os.path.join(data_dir, file_name)
                    self.progress_tracker.log_info(f"📄 Processing: {file_name}")
                    
                    try:
                        # Read the entire file as JSON array
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            
                            # Handle both formats: [obj1, obj2, ...] or individual objects
                            if content.startswith('[') and content.endswith(']'):
                                # Gemini format: single JSON array
                                data_list = json.loads(content)
                                self.progress_tracker.log_info(f"   📊 Found {len(data_list)} objects in array format")
                            else:
                                # Try to parse as JSONL (one object per line)
                                data_list = []
                                for line_num, line in enumerate(content.split('\n'), 1):
                                    line = line.strip()
                                    if line:
                                        try:
                                            obj = json.loads(line)
                                            data_list.append(obj)
                                        except json.JSONDecodeError as e:
                                            self.progress_tracker.log_warning(f"   ⚠️  Skipping invalid JSON on line {line_num}: {e}")
                                
                                self.progress_tracker.log_info(f"   📊 Found {len(data_list)} objects in JSONL format")
                        
                        # Process each data object
                        for i, data in enumerate(data_list):
                            try:
                                # Validate required fields
                                required_keys = ['id', 'original_text', 'entity_relation_dict', 'event_entity_relation_dict', 'event_relation_dict']
                                if not all(key in data for key in required_keys):
                                    self.progress_tracker.log_warning(f"   ⚠️  Skipping object {i} - missing required fields")
                                    continue
                                
                                original_text = data["original_text"]
                                original_text = remove_NUL(original_text)
                                if "Here is the passage." in original_text:
                                    original_text = original_text.split("Here is the passage.")[-1]
                                eot_token = "<|eot_id|>"
                                original_text = original_text.split(eot_token)[0]
                                
                                text_hash_id = compute_hash_id(original_text)
                                
                                # Write the original text as nodes
                                if text_hash_id not in visited_hashes:
                                    visited_hashes.add(text_hash_id)
                                    csv_writer_node_text.writerow([text_hash_id, original_text, "Text"])
                                
                                file_id = str(data["id"])
                                entity_relation_dict = data["entity_relation_dict"]
                                event_entity_relation_dict = data["event_entity_relation_dict"]
                                event_relation_dict = data["event_relation_dict"]
                                
                                # Process entity triples
                                entity_triples = []
                                for entity_triple in entity_relation_dict:
                                    try:
                                        assert isinstance(entity_triple["Head"], str)
                                        assert isinstance(entity_triple["Relation"], str)
                                        assert isinstance(entity_triple["Tail"], str)
                                        
                                        head_entity = entity_triple["Head"]
                                        relation = entity_triple["Relation"]
                                        tail_entity = entity_triple["Tail"]
                                        
                                        # Clean the text
                                        head_entity = clean_text(head_entity)
                                        relation = clean_text(relation)
                                        tail_entity = clean_text(tail_entity)
                                        
                                        if head_entity.isspace() or len(head_entity) == 0 or tail_entity.isspace() or len(tail_entity) == 0:
                                            continue
                                        
                                        entity_triples.append((head_entity, relation, tail_entity))
                                    except Exception as e:
                                        self.progress_tracker.log_warning(f"   ⚠️  Error processing entity triple: {e}")
                                        continue
                                
                                # Process event triples
                                event_triples = []
                                for event_triple in event_relation_dict:
                                    try:
                                        assert isinstance(event_triple["Head"], str)
                                        assert isinstance(event_triple["Relation"], str)
                                        assert isinstance(event_triple["Tail"], str)
                                        head_event = event_triple["Head"]
                                        relation = event_triple["Relation"]
                                        tail_event = event_triple["Tail"]
                                        
                                        # Clean the text
                                        head_event = clean_text(head_event)
                                        relation = clean_text(relation)
                                        tail_event = clean_text(tail_event)
                                        
                                        if head_event.isspace() or len(head_event) == 0 or tail_event.isspace() or len(tail_event) == 0:
                                            continue
                                        
                                        event_triples.append((head_event, relation, tail_event))
                                    except Exception as e:
                                        self.progress_tracker.log_warning(f"   ⚠️  Error processing event triple: {e}")
                                
                                # Process event-entity triples
                                event_entity_triples = []
                                for event_entity_participations in event_entity_relation_dict:
                                    if "Event" not in event_entity_participations or "Entity" not in event_entity_participations:
                                        continue
                                    if not isinstance(event_entity_participations["Event"], str) or not isinstance(event_entity_participations["Entity"], list):
                                        continue
                                    
                                    for entity in event_entity_participations["Entity"]:
                                        if not isinstance(entity, str):
                                            continue
                                        
                                        entity = clean_text(entity)
                                        event = clean_text(event_entity_participations["Event"])
                                        
                                        if event.isspace() or len(event) == 0 or entity.isspace() or len(entity) == 0:
                                            continue
                                        
                                        event_entity_triples.append((event, "is participated by", entity))
                                
                                # Write nodes and edges to CSV files
                                for entity_triple in entity_triples:
                                    head_entity, relation, tail_entity = entity_triple
                                    if head_entity is None or tail_entity is None or relation is None:
                                        continue
                                    if head_entity.isspace() or tail_entity.isspace() or relation.isspace():
                                        continue
                                    if len(head_entity) == 0 or len(tail_entity) == 0 or len(relation) == 0:
                                        continue
                                    
                                    # Add nodes to files
                                    if head_entity not in visited_nodes:
                                        visited_nodes.add(head_entity)
                                        all_entities.add(head_entity)
                                        writer_node.writerow([head_entity, "entity", "[]", "[]", "Node"])
                                        csv_writer_edge_text.writerow([head_entity, text_hash_id, "Source"])
                                    
                                    if tail_entity not in visited_nodes:
                                        visited_nodes.add(tail_entity)
                                        all_entities.add(tail_entity)
                                        writer_node.writerow([tail_entity, "entity", "[]", "[]", "Node"])
                                        csv_writer_edge_text.writerow([tail_entity, text_hash_id, "Source"])
                                    
                                    all_relations.add(relation)
                                    writer_edge.writerow([head_entity, tail_entity, relation, "[]", "[]", "Relation"])
                                
                                for event_triple in event_triples:
                                    head_event, relation, tail_event = event_triple
                                    if head_event is None or tail_event is None or relation is None:
                                        continue
                                    if head_event.isspace() or tail_event.isspace() or relation.isspace():
                                        continue
                                    if len(head_event) == 0 or len(tail_event) == 0 or len(relation) == 0:
                                        continue
                                    
                                    # Add nodes to files
                                    if head_event not in visited_nodes:
                                        visited_nodes.add(head_event)
                                        all_events.add(head_event)
                                        writer_node.writerow([head_event, "event", "[]", "[]", "Node"])
                                        csv_writer_edge_text.writerow([head_event, text_hash_id, "Source"])
                                    
                                    if tail_event not in visited_nodes:
                                        visited_nodes.add(tail_event)
                                        all_events.add(tail_event)
                                        writer_node.writerow([tail_event, "event", "[]", "[]", "Node"])
                                        csv_writer_edge_text.writerow([tail_event, text_hash_id, "Source"])
                                    
                                    all_relations.add(relation)
                                    writer_edge.writerow([head_event, tail_event, relation, "[]", "[]", "Relation"])
                                
                                for event_entity_triple in event_entity_triples:
                                    head_event, relation, tail_entity = event_entity_triple
                                    if head_event is None or tail_entity is None or relation is None:
                                        continue
                                    if head_event.isspace() or tail_entity.isspace() or relation.isspace():
                                        continue
                                    if len(head_event) == 0 or len(tail_entity) == 0 or len(relation) == 0:
                                        continue
                                    
                                    # Add nodes to files
                                    if head_event not in visited_nodes:
                                        visited_nodes.add(head_event)
                                        all_events.add(head_event)
                                        writer_node.writerow([head_event, "event", "[]", "[]", "Node"])
                                        csv_writer_edge_text.writerow([head_event, text_hash_id, "Source"])
                                    
                                    if tail_entity not in visited_nodes:
                                        visited_nodes.add(tail_entity)
                                        all_entities.add(tail_entity)
                                        writer_node.writerow([tail_entity, "entity", "[]", "[]", "Node"])
                                        csv_writer_edge_text.writerow([tail_entity, text_hash_id, "Source"])
                                    
                                    all_relations.add(relation)
                                    writer_edge.writerow([head_event, tail_entity, relation, "[]", "[]", "Relation"])
                                    
                            except Exception as e:
                                self.progress_tracker.log_warning(f"   ⚠️  Error processing data object {i}: {e}")
                                continue
                    
                    except Exception as e:
                        self.progress_tracker.log_error(f"   ❌ Error reading file {file_name}: {e}")
                        continue
            
            # Write missing concepts to CSV
            with open(missing_concepts_file, "w", newline='', encoding='utf-8', errors='ignore') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(["Name", "Type"])
                for entity in all_entities:
                    writer.writerow([entity, "Entity"])
                for event in all_events:
                    writer.writerow([event, "Event"])
                for relation in all_relations:
                    writer.writerow([relation, "Relation"])
            
            self.progress_tracker.log_info("✅ Gemini-compatible CSV conversion completed successfully")
            return True
            
        except Exception as e:
            self.progress_tracker.log_error(f"❌ Gemini-compatible CSV conversion failed: {e}")
            return False
    
    def generate_concept_csv(self, batch_size: int = None, retry_failed: bool = False, **kwargs) -> bool:
        """
        Generate concepts from extracted triples with error handling.
        
        Args:
            batch_size: Batch size for concept generation
            retry_failed: If True, retry previously failed operations
            **kwargs: Additional arguments for concept generation
            
        Returns:
            True if concept generation successful, False otherwise
        """
        try:
            self.current_stage = "concept_generation"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Generating concepts from triples")
            
            if self._is_stage_completed("concept_generation") and not retry_failed:
                self.progress_tracker.log_info("✅ Concept generation already completed - skipping")
                return True
            
            # Use provided batch size or config default
            effective_batch_size = batch_size or self.config.batch_size_concept
            self.progress_tracker.log_info(f"🧠 Using batch size: {effective_batch_size}")
            
            success = self.error_handler.execute_with_retry(
                operation=lambda: self.kg_extractor.generate_concept_csv(
                    batch_size=effective_batch_size, **kwargs
                ),
                context={
                    'stage': self.current_stage,
                    'batch_size': effective_batch_size
                },
                operation_name="concept_generation"
            )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                self.progress_tracker.log_info("✅ Concept generation completed successfully")
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "Concept generation failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'generate_concept_csv'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"Concept generation failed: {e}")
            return False
    
    def create_concept_csv(self, retry_failed: bool = False) -> bool:
        """
        Create concept CSV files with error handling.
        
        Args:
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if creation successful, False otherwise
        """
        try:
            self.current_stage = "concept_csv_creation"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Creating concept CSV files")
            
            if self._is_stage_completed("concept_csv_creation") and not retry_failed:
                self.progress_tracker.log_info("✅ Concept CSV creation already completed - skipping")
                return True
            
            success = self.error_handler.execute_with_retry(
                operation=self.kg_extractor.create_concept_csv,
                context={'stage': self.current_stage},
                operation_name="concept_csv_creation"
            )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                self.progress_tracker.log_info("✅ Concept CSV creation completed successfully")
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "Concept CSV creation failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'create_concept_csv'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"Concept CSV creation failed: {e}")
            return False
    
    def add_numeric_id(self, retry_failed: bool = False) -> bool:
        """
        Add numeric IDs to CSV files with error handling.
        
        Args:
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.current_stage = "numeric_id_addition"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Adding numeric IDs to CSV files")
            
            if self._is_stage_completed("numeric_id_addition") and not retry_failed:
                self.progress_tracker.log_info("✅ Numeric ID addition already completed - skipping")
                return True
            
            success = self.error_handler.execute_with_retry(
                operation=self.kg_extractor.add_numeric_id,
                context={'stage': self.current_stage},
                operation_name="numeric_id_addition"
            )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                self.progress_tracker.log_info("✅ Numeric ID addition completed successfully")
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "Numeric ID addition failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'add_numeric_id'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"Numeric ID addition failed: {e}")
            return False
    
    def convert_to_graphml(self, retry_failed: bool = False) -> bool:
        """
        Convert CSV files to GraphML format with error handling.
        
        Args:
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if conversion successful, False otherwise
        """
        try:
            self.current_stage = "graphml_conversion"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Converting to GraphML format")
            
            if self._is_stage_completed("graphml_conversion") and not retry_failed:
                self.progress_tracker.log_info("✅ GraphML conversion already completed - skipping")
                return True
            
            success = self.error_handler.execute_with_retry(
                operation=self.kg_extractor.convert_to_graphml,
                context={'stage': self.current_stage},
                operation_name="graphml_conversion"
            )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                self.progress_tracker.log_info("✅ GraphML conversion completed successfully")
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "GraphML conversion failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'convert_to_graphml'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"GraphML conversion failed: {e}")
            return False
    
    def compute_kg_embedding(self, sentence_encoder: BaseEmbeddingModel, retry_failed: bool = False) -> bool:
        """
        Compute knowledge graph embeddings with error handling.
        
        Args:
            sentence_encoder: Embedding model for vector generation
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if embedding computation successful, False otherwise
        """
        try:
            self.current_stage = "embedding_generation"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Computing knowledge graph embeddings")
            
            if self._is_stage_completed("embedding_generation") and not retry_failed:
                self.progress_tracker.log_info("✅ Embedding generation already completed - skipping")
                return True
            
            success = self.error_handler.execute_with_retry(
                operation=lambda: self.kg_extractor.compute_kg_embedding(sentence_encoder),
                context={
                    'stage': self.current_stage,
                    'model': sentence_encoder.model_name
                },
                operation_name="embedding_generation"
            )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                self.progress_tracker.log_info("✅ Embedding generation completed successfully")
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "Embedding generation failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'compute_kg_embedding'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"Embedding generation failed: {e}")
            return False
    
    def create_faiss_index(self, retry_failed: bool = False) -> bool:
        """
        Create FAISS indexes with error handling.
        
        Args:
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if index creation successful, False otherwise
        """
        try:
            self.current_stage = "faiss_index_creation"
            self.state_manager.start_stage(self.current_stage)
            self.progress_tracker.start_stage(self.current_stage, "Creating FAISS indexes")
            
            if self._is_stage_completed("faiss_index_creation") and not retry_failed:
                self.progress_tracker.log_info("✅ FAISS index creation already completed - skipping")
                return True
            
            success = self.error_handler.execute_with_retry(
                operation=self.kg_extractor.create_faiss_index,
                context={'stage': self.current_stage},
                operation_name="faiss_index_creation"
            )
            
            if success:
                self.state_manager.complete_stage(self.current_stage)
                self.progress_tracker.complete_stage(self.current_stage)
                self.progress_tracker.log_info("✅ FAISS index creation completed successfully")
                return True
            else:
                self.state_manager.fail_stage(self.current_stage, "FAISS index creation failed")
                return False
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': self.current_stage,
                'operation': 'create_faiss_index'
            })
            self.state_manager.fail_stage(self.current_stage, str(e))
            self.progress_tracker.log_error(f"FAISS index creation failed: {e}")
            return False
    
    def _is_stage_completed(self, stage_name: str) -> bool:
        """Check if a stage has been completed."""
        if not self.state_manager.state:
            return False
        
        stage = self.state_manager.state.stages.get(stage_name)
        return stage and stage.status == 'completed'
    
    def run_complete_pipeline(self, 
                            sentence_encoder: BaseEmbeddingModel,
                            force_restart: bool = False,
                            retry_failed: bool = False) -> bool:
        """
        Run the complete knowledge graph extraction pipeline with enhanced robustness.
        
        Args:
            sentence_encoder: Embedding model for vector generation
            force_restart: If True, ignore existing state and start fresh
            retry_failed: If True, retry previously failed operations
            
        Returns:
            True if pipeline completed successfully, False otherwise
        """
        pipeline_start_time = time.time()
        
        try:
            with self.progress_tracker:
                self.progress_tracker.log_info("🚀 Starting robust knowledge graph extraction pipeline")
                
                # Initialize pipeline
                if not self.initialize_pipeline(force_restart=force_restart):
                    return False
                
                # Run all stages
                stages = [
                    ("Triple Extraction", lambda: self.run_extraction(retry_failed=retry_failed)),
                    ("CSV Conversion", lambda: self.convert_json_to_csv(retry_failed=retry_failed)),
                    ("Concept Generation", lambda: self.generate_concept_csv(retry_failed=retry_failed)),
                    ("Concept CSV Creation", lambda: self.create_concept_csv(retry_failed=retry_failed)),
                    ("Numeric ID Addition", lambda: self.add_numeric_id(retry_failed=retry_failed)),
                    ("GraphML Conversion", lambda: self.convert_to_graphml(retry_failed=retry_failed)),
                    ("Embedding Generation", lambda: self.compute_kg_embedding(sentence_encoder, retry_failed=retry_failed)),
                    ("FAISS Index Creation", lambda: self.create_faiss_index(retry_failed=retry_failed))
                ]
                
                for stage_name, stage_func in stages:
                    self.progress_tracker.log_info(f"▶️ Starting: {stage_name}")
                    if not stage_func():
                        self.progress_tracker.log_error(f"❌ Failed: {stage_name}")
                        return False
                    self.progress_tracker.log_info(f"✅ Completed: {stage_name}")
                
                # Mark overall pipeline as completed
                if self.state_manager.state:
                    self.state_manager.state.overall_status = 'completed'
                    self.state_manager.save_state(force=True)
                
                pipeline_duration = time.time() - pipeline_start_time
                self.progress_tracker.log_info(f"🎉 Complete pipeline finished successfully in {pipeline_duration/60:.1f} minutes")
                
                return True
                
        except Exception as e:
            error_record = self.error_handler.record_error(e, {
                'stage': 'complete_pipeline',
                'operation': 'run_complete_pipeline'
            })
            
            if self.state_manager.state:
                self.state_manager.state.overall_status = 'failed'
                self.state_manager.save_state(force=True)
            
            self.progress_tracker.log_error(f"💥 Complete pipeline failed: {e}")
            return False
    
    def get_pipeline_summary(self) -> Dict[str, Any]:
        """Get comprehensive pipeline processing summary."""
        summary = {
            'state_manager': self.state_manager.get_processing_summary(),
            'progress_tracker': self.progress_tracker.get_processing_summary(),
            'error_handler': self.error_handler.get_error_summary(),
            'resume_enabled': self.resume_enabled,
            'pipeline_started': self.pipeline_started,
            'current_stage': self.current_stage,
            'input_files_count': len(self.input_files),
            'processed_documents_count': len(self.processed_documents)
        }
        
        return summary
    
    def cleanup_state(self):
        """Clean up state files (useful for fresh starts)."""
        self.state_manager.cleanup_state_files()
        self.progress_tracker.log_info("🧹 Pipeline state cleaned up")
    
    def retry_failed_operations(self) -> bool:
        """Retry operations that previously failed."""
        self.progress_tracker.log_info("🔄 Retrying failed operations")
        return self.run_complete_pipeline(
            sentence_encoder=None,  # Will need to be provided by caller
            retry_failed=True
        )
