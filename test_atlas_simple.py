#!/usr/bin/env python3
"""
Simple test for ATLAS GraphML Pipeline with minimal data
Tests the pipeline with a single small JSON file to validate the complete workflow.
"""

import sys
import os
import json
from pathlib import Path

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from atlas_graphml_pipeline import ATLASGraphMLPipeline

def create_test_json():
    """
    Create a small test JSON file for pipeline testing.
    """
    test_data = [
        {
            "id": "test_doc_1",
            "text": "Apple Inc. is a technology company founded by <PERSON>. The company develops innovative products like the iPhone and iPad. Apple focuses on environmental sustainability and renewable energy.",
            "metadata": {
                "source": "test_document",
                "page": 1,
                "chunk_id": "chunk_1"
            }
        },
        {
            "id": "test_doc_2", 
            "text": "<PERSON> was the co-founder and CEO of Apple Inc. He was known for his innovative vision and leadership in the technology industry. <PERSON><PERSON> introduced revolutionary products that changed how people interact with technology.",
            "metadata": {
                "source": "test_document",
                "page": 1,
                "chunk_id": "chunk_2"
            }
        }
    ]
    
    # Save test data
    test_file = project_root / "example_data" / "test_simple.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created test file: {test_file}")
    return test_file

def cleanup_test_files():
    """
    Clean up test files and directories.
    """
    # Remove test JSON file
    test_file = project_root / "example_data" / "test_simple.json"
    if test_file.exists():
        test_file.unlink()
        print(f"🗑️  Removed test file: {test_file}")
    
    # Remove test output directory
    test_output_dir = project_root / "import" / "test_simple"
    if test_output_dir.exists():
        import shutil
        shutil.rmtree(test_output_dir)
        print(f"🗑️  Removed test output directory: {test_output_dir}")

def test_simple_pipeline():
    """
    Test the ATLAS GraphML pipeline with minimal data.
    """
    print("🧪 Simple ATLAS GraphML Pipeline Test")
    print("=" * 50)
    
    try:
        # Create test data
        print("📝 Creating test data...")
        test_file = create_test_json()
        
        # Create pipeline with specific filename pattern for our test file
        pipeline = ATLASGraphMLPipeline(
            dataset_name="test_simple",
            filename_pattern="test_simple"  # This will match our test_simple.json file
        )
        
        # Test basic setup
        print("\n1️⃣ Testing Prerequisites...")
        if not pipeline.validate_prerequisites():
            print("❌ Prerequisites validation failed")
            return False
        
        print("\n2️⃣ Testing Model Setup...")
        if not pipeline.setup_models():
            print("❌ Model setup failed")
            return False
        
        print("\n3️⃣ Testing Configuration...")
        if not pipeline.setup_configuration():
            print("❌ Configuration setup failed")
            return False
        
        print("\n🎉 Basic setup successful!")
        print("💡 All components are working correctly")
        
        # Ask if user wants to run full pipeline
        print("\n" + "=" * 50)
        print("🚀 Run the complete pipeline with test data?")
        print("This will test the full workflow with minimal data.")
        print("Estimated time: 2-5 minutes")
        
        response = input("\nRun full test pipeline? (y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            print("\n🚀 Running complete test pipeline...")
            
            # Run individual steps for better error tracking
            steps = [
                ("Triple Extraction", pipeline.run_triple_extraction),
                ("CSV Conversion", pipeline.convert_to_csv),
                ("Concept Generation", pipeline.generate_concepts),
                ("GraphML Conversion", pipeline.convert_to_graphml),
            ]
            
            for step_name, step_function in steps:
                print(f"\n🔄 Running {step_name}...")
                if not step_function():
                    print(f"❌ {step_name} failed - stopping pipeline")
                    return False
                print(f"✅ {step_name} completed")
            
            # Generate summary
            pipeline.generate_summary()
            
            print("\n🎉 Complete test pipeline successful!")
            print("✅ All steps completed successfully")
            return True
        else:
            print("\n✅ Basic tests completed successfully!")
            return True
            
    except Exception as e:
        print(f"\n💥 Test error: {str(e)}")
        return False
    finally:
        # Clean up test files
        print("\n🧹 Cleaning up test files...")
        cleanup_test_files()

def main():
    """
    Main test function.
    """
    try:
        success = test_simple_pipeline()
        if success:
            print("\n✅ Simple pipeline test passed!")
            print("💡 The ATLAS GraphML pipeline is working correctly")
            print("💡 You can now run 'python atlas_graphml_pipeline.py' for full processing")
            sys.exit(0)
        else:
            print("\n❌ Simple pipeline test failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        cleanup_test_files()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected test error: {str(e)}")
        cleanup_test_files()
        sys.exit(1)

if __name__ == "__main__":
    main()
