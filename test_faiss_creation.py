#!/usr/bin/env python3
"""
Simple test script to create FAISS indexes.
"""

import traceback
from pathlib import Path

def test_faiss_creation():
    """Test FAISS index creation."""
    try:
        from atlas_rag.vectorstore.create_neo4j_index import create_faiss_index
        
        base_dir = Path("import/pdf_dataset")
        print(f"Base directory: {base_dir}")
        print(f"Directory exists: {base_dir.exists()}")
        
        # Check if input files exist
        triples_csv = base_dir / "triples_csv"
        required_files = [
            "triple_nodes__from_json_with_emb.csv",
            "text_nodes__from_json_with_emb.csv"
        ]
        
        for file in required_files:
            file_path = triples_csv / file
            print(f"File {file}: exists={file_path.exists()}")
            if file_path.exists():
                print(f"  Size: {file_path.stat().st_size} bytes")
        
        # Check concept file
        concept_file = base_dir / "concept_csv" / "triple_edges__from_json_with_concept.csv"
        print(f"Concept file exists: {concept_file.exists()}")
        
        # Try to create FAISS index
        print("\nAttempting to create FAISS index...")
        create_faiss_index(
            output_directory=str(base_dir),
            filename_pattern="",
            index_type="HNSW,Flat"
        )
        print("✅ FAISS index creation successful!")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("\nFull traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    test_faiss_creation()