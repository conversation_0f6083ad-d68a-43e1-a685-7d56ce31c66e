{"dataset_name": "pdf_dataset", "pipeline_version": "2.0", "created_at": "2025-08-03T09:58:48.888919", "updated_at": "2025-08-03T09:58:48.888956", "overall_status": "not_started", "total_files": 37, "completed_files": 0, "failed_files": 0, "stages": {"initialization": {"name": "initialization", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "triple_extraction": {"name": "triple_extraction", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "csv_conversion": {"name": "csv_conversion", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "concept_generation": {"name": "concept_generation", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "concept_csv_creation": {"name": "concept_csv_creation", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "numeric_id_addition": {"name": "numeric_id_addition", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "graphml_conversion": {"name": "graphml_conversion", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "embedding_generation": {"name": "embedding_generation", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}, "faiss_index_creation": {"name": "faiss_index_creation", "status": "pending", "start_time": null, "end_time": null, "files_completed": 0, "files_total": 37, "last_processed_file": "", "error_message": ""}}, "files": {"RFS_FIX_MarketTaker_v12.json": {"filename": "RFS_FIX_MarketTaker_v12.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Bridge Counterpart Relationship Management Tool User Guide_Market Maker.json": {"filename": "Bridge Counterpart Relationship Management Tool User Guide_Market Maker.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Bridge Order Book User Guide_Market Taker.json": {"filename": "Bridge Order Book User Guide_Market Taker.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "ADS_Market Maker Cockpit - R3 19.json": {"filename": "ADS_Market Maker Cockpit - R3 19.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "SupersonicTrader.json": {"filename": "SupersonicTrader.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "PS Mapping (Bridge Admin).json": {"filename": "PS Mapping (Bridge Admin).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "SEF Market Maker.json": {"filename": "SEF Market Maker.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "RFS Market Taker Bridge.json": {"filename": "RFS Market Taker Bridge.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "ADS_Market Maker Cockpit (HTML GUI).json": {"filename": "ADS_Market Maker Cockpit (HTML GUI).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "RFS_FIX_MarketTaker_v13.json": {"filename": "RFS_FIX_MarketTaker_v13.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Business Configuration Tool BRIDGE.json": {"filename": "Business Configuration Tool BRIDGE.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Limits Monitor.json": {"filename": "Limits Monitor.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "RFS_FIX_MarketTaker_v13_DRAFT.json": {"filename": "RFS_FIX_MarketTaker_v13_DRAFT.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "ADS.json": {"filename": "ADS.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "SEF Market Taker.json": {"filename": "SEF Market Taker.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Regulatory Data (Company Admin Control).json": {"filename": "Regulatory Data (Company Admin Control).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Order_MT_XML_API_v2.1.json": {"filename": "Order_MT_XML_API_v2.1.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Change Requests (Bridge Admin).json": {"filename": "Change Requests (Bridge Admin).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "User_Guide_SEF_Market_Taker_Web.json": {"filename": "User_Guide_SEF_Market_Taker_Web.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "ADS_Market Maker Cockpit  (Swing GUI).json": {"filename": "ADS_Market Maker Cockpit  (Swing GUI).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "EMS.json": {"filename": "EMS.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "TWS Market Maker (BRIDGE).json": {"filename": "TWS Market Maker (BRIDGE).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "FX Futures SupersonicTrader.json": {"filename": "FX Futures SupersonicTrader.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Accessing the ECN via Supersonic.json": {"filename": "Accessing the ECN via Supersonic.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Bridge Administration User Manual.json": {"filename": "Bridge Administration User Manual.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "EMS - csv File extract.json": {"filename": "EMS - csv File extract.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Data Disclosure Configuration (Bridge Admin).json": {"filename": "Data Disclosure Configuration (Bridge Admin).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Limit_REST_API_v2.0.json": {"filename": "Limit_REST_API_v2.0.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "HTML Auto Dealing Suite.json": {"filename": "HTML Auto Dealing Suite.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Bridge Administration - SEF Data.json": {"filename": "Bridge Administration - SEF Data.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Limits Monitor_Trading Limits Profile.json": {"filename": "Limits Monitor_Trading Limits Profile.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "User_Guide_SEF_Market_Maker_Web.json": {"filename": "User_Guide_SEF_Market_Maker_Web.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Order_MM_FIX_API_v7.0.json": {"filename": "Order_MM_FIX_API_v7.0.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Cash management Tool_GCT_configuration.json": {"filename": "Cash management Tool_GCT_configuration.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Bridge Administration I-TEX.json": {"filename": "Bridge Administration I-TEX.json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Bank Basket Configuration (Bridge Admin).json": {"filename": "Bank Basket Configuration (Bridge Admin).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}, "Institution Configuration (Bridge Admin).json": {"filename": "Institution Configuration (Bridge Admin).json", "status": "pending", "stages_completed": [], "last_processed_stage": "", "start_time": null, "end_time": null, "error_message": "", "document_count": 0, "last_document_id": ""}}, "config": {"model_path": "gemini-2.5-flash", "data_directory": "example_data", "filename_pattern": "", "output_directory": "import/pdf_dataset", "batch_size_triple": 8, "batch_size_concept": 64, "max_new_tokens": 8192, "max_workers": 8}, "resume_point": {}}