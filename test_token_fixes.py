#!/usr/bin/env python3
"""
Test script to validate token limit fixes for AutoSchemaKG pipeline.
This script tests the configuration changes without running the full pipeline.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from setup_processing_config import create_processing_config, create_processing_config_tier1, show_tier1_recommendations
from atlas_rag.kg_construction.triple_config import ProcessingConfig

def test_default_config():
    """Test the updated default configuration."""
    print("🔍 Testing Default Configuration (Updated)")
    print("=" * 50)
    
    # Create default config using the ProcessingConfig class directly
    default_config = ProcessingConfig(
        model_path="test-model",
        data_directory="test-data", 
        filename_pattern="test"
    )
    
    print(f"✅ Default max_new_tokens: {default_config.max_new_tokens} (should be 1500)")
    print(f"✅ Default batch_size_triple: {default_config.batch_size_triple} (should be 2)")
    print(f"✅ Default max_workers: {default_config.max_workers}")
    
    # Validate the fixes
    assert default_config.max_new_tokens == 1500, f"Expected 1500, got {default_config.max_new_tokens}"
    assert default_config.batch_size_triple == 2, f"Expected 2, got {default_config.batch_size_triple}"
    
    print("✅ Default configuration tests passed!")
    print()

def test_tier1_config():
    """Test the Tier 1 optimized configuration."""
    print("🔧 Testing Tier 1 Configuration")
    print("=" * 40)
    
    tier1_config = create_processing_config_tier1(
        dataset_name="test_dataset",
        filename_pattern="test"
    )
    
    print(f"✅ Tier 1 max_new_tokens: {tier1_config.max_new_tokens} (should be 1000)")
    print(f"✅ Tier 1 batch_size_triple: {tier1_config.batch_size_triple} (should be 1)")
    print(f"✅ Tier 1 max_workers: {tier1_config.max_workers} (should be 1)")
    print(f"✅ Tier 1 batch_size_concept: {tier1_config.batch_size_concept} (should be 8)")
    
    # Validate Tier 1 optimizations
    assert tier1_config.max_new_tokens == 1000, f"Expected 1000, got {tier1_config.max_new_tokens}"
    assert tier1_config.batch_size_triple == 1, f"Expected 1, got {tier1_config.batch_size_triple}"
    assert tier1_config.max_workers == 1, f"Expected 1, got {tier1_config.max_workers}"
    assert tier1_config.batch_size_concept == 8, f"Expected 8, got {tier1_config.batch_size_concept}"
    
    print("✅ Tier 1 configuration tests passed!")
    print()

def test_standard_config():
    """Test the standard configuration for comparison."""
    print("💡 Testing Standard Configuration")
    print("=" * 40)
    
    standard_config = create_processing_config(
        dataset_name="test_dataset", 
        filename_pattern="test"
    )
    
    print(f"✅ Standard max_new_tokens: {standard_config.max_new_tokens}")
    print(f"✅ Standard batch_size_triple: {standard_config.batch_size_triple}")
    print(f"✅ Standard max_workers: {standard_config.max_workers}")
    
    print("✅ Standard configuration tests passed!")
    print()

def show_comparison():
    """Show side-by-side comparison of configurations."""
    print("📊 Configuration Comparison")
    print("=" * 50)
    
    # Create both configurations
    standard = create_processing_config("test", "test")
    tier1 = create_processing_config_tier1("test", "test")
    
    print(f"{'Parameter':<25} {'Standard':<12} {'Tier 1':<12} {'Impact'}")
    print("-" * 70)
    print(f"{'max_new_tokens':<25} {standard.max_new_tokens:<12} {tier1.max_new_tokens:<12} Token limit safe")
    print(f"{'batch_size_triple':<25} {standard.batch_size_triple:<12} {tier1.batch_size_triple:<12} Smaller prompts")
    print(f"{'max_workers':<25} {standard.max_workers:<12} {tier1.max_workers:<12} No rate limits")
    print(f"{'batch_size_concept':<25} {standard.batch_size_concept:<12} {tier1.batch_size_concept:<12} Reduced load")
    print()

def main():
    """Run all configuration tests."""
    print("🧪 AutoSchemaKG Token Limit Fix Validation")
    print("=" * 60)
    print("Testing the fixes for finish_reason='length' token limit issues")
    print()
    
    try:
        # Run all tests
        test_default_config()
        test_tier1_config() 
        test_standard_config()
        show_comparison()
        
        # Show recommendations
        show_tier1_recommendations()
        
        print("\n🎉 ALL TESTS PASSED!")
        print("\n💡 How to use the fixes:")
        print("1. For Tier 1 users experiencing token limits:")
        print("   python atlas_graphml_pipeline.py --tier1")
        print()
        print("2. The default configuration is now more conservative:")
        print("   - max_new_tokens reduced from 8192 → 1500")
        print("   - batch_size_triple reduced from 16 → 2") 
        print()
        print("3. Monitor logs for 'TOKEN LIMIT HIT' messages")
        print("4. Adjust max_new_tokens further if needed (800-600)")
        
    except AssertionError as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()