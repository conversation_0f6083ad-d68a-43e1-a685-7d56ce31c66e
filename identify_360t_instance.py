#!/usr/bin/env python3
"""
Identify the correct Neo4j Desktop instance for 360TKnowledgeBaseInstance
and generate the specific import command.
"""

import os
import json
from pathlib import Path

def find_360t_instance():
    """
    Find the 360TKnowledgeBaseInstance in Neo4j Desktop.
    """
    # Neo4j Desktop stores project information in different locations
    desktop_data_path = Path.home() / "Library/Application Support/neo4j-desktop/Application/Data"
    
    # Look for project files
    projects_path = desktop_data_path / "projects"
    if projects_path.exists():
        print(f"📁 Checking projects directory: {projects_path}")
        for project_file in projects_path.glob("*.json"):
            try:
                with open(project_file, 'r') as f:
                    project_data = json.load(f)
                    print(f"   Project file: {project_file.name}")
                    if 'dbmss' in project_data:
                        for dbms in project_data['dbmss']:
                            if dbms.get('name') == '360TKnowledgeBaseInstance':
                                return dbms.get('id'), project_data
            except Exception as e:
                print(f"   Error reading {project_file}: {e}")
    
    # Alternative: check dbmss directory for instance names
    dbmss_path = desktop_data_path / "dbmss"
    if dbmss_path.exists():
        print(f"\n📁 Checking dbmss directory: {dbmss_path}")
        instances = []
        for instance_dir in dbmss_path.iterdir():
            if instance_dir.is_dir() and instance_dir.name.startswith("dbms-"):
                # Check for any configuration or metadata files
                conf_file = instance_dir / "conf" / "neo4j.conf"
                if conf_file.exists():
                    instances.append({
                        'id': instance_dir.name,
                        'path': instance_dir,
                        'conf': conf_file
                    })
        
        print(f"   Found {len(instances)} instances:")
        for i, instance in enumerate(instances, 1):
            print(f"   {i}. {instance['id']}")
        
        return instances
    
    return None

def generate_targeted_import_command(instance_id=None):
    """
    Generate import command for specific instance or provide options.
    """
    current_dir = Path.cwd()
    csv_base_path = current_dir / "import" / "360t_guide_direct_api_v2"
    
    print("\n" + "=" * 80)
    print("🎯 TARGETED IMPORT FOR 360TKnowledgeBaseInstance")
    print("=" * 80)
    
    print("\n📋 **STEP-BY-STEP IMPORT PROCESS**")
    
    print("\n1️⃣ **Stop your 360TKnowledgeBaseInstance**")
    print("   - In Neo4j Desktop, find '360TKnowledgeBaseInstance'")
    print("   - Click the 'Stop' button")
    print("   - Wait until status shows 'STOPPED'")
    
    print("\n2️⃣ **Identify your instance directory**")
    print("   Your instance is one of these:")
    print("   - dbms-20aa5f10-df67-4e6f-a3df-5777b3eaa93e")
    print("   - dbms-47e656e4-6473-4155-b149-3ffda6ca4945") 
    print("   - dbms-0266d0b9-194f-413b-83b5-a59fde36e43b")
    print("\n   To identify which one:")
    print("   - Right-click on '360TKnowledgeBaseInstance' in Neo4j Desktop")
    print("   - Select 'Open folder' or check the instance ID in settings")
    
    print("\n3️⃣ **Run the import command**")
    print("   Replace [INSTANCE-ID] with your actual instance ID:")
    print()
    
    # Generate the import command template
    cmd = f"""   neo4j-admin database import full autoschemakg \\
    --nodes={csv_base_path}/concept_csv/concept_nodes__from_json_with_concept.csv \\
    --nodes={csv_base_path}/triples_csv/triple_nodes__from_json_without_emb.csv \\
    --nodes={csv_base_path}/triples_csv/text_nodes__from_json.csv \\
    --relationships={csv_base_path}/concept_csv/concept_edges__from_json_with_concept.csv \\
    --relationships={csv_base_path}/triples_csv/triple_edges__from_json_without_emb.csv \\
    --overwrite-destination \\
    --multiline-fields=true \\
    --id-type=string \\
    --verbose \\
    --skip-bad-relationships=true"""
    
    print(cmd)
    
    print("\n4️⃣ **Start instance and create autoschemakg database**")
    print("   - Start '360TKnowledgeBaseInstance' in Neo4j Desktop")
    print("   - Click 'Open' to access Neo4j Browser")
    print("   - Run: CREATE DATABASE autoschemakg IF NOT EXISTS")
    print("   - Run: :use autoschemakg")
    
    print("\n5️⃣ **Verify the import**")
    print("   Run these queries in Neo4j Browser:")
    print("   MATCH (n) RETURN count(n) as total_nodes        // Expected: 1,090")
    print("   MATCH ()-[r]->() RETURN count(r) as total_rels  // Expected: 2,901")
    print("   MATCH (n:Concept) RETURN count(n)               // Expected: 599")
    print("   MATCH (n:Node) RETURN count(n)                  // Expected: 479")
    print("   MATCH (n:Text) RETURN count(n)                  // Expected: 12")
    
    print("\n✅ **Expected Results:**")
    print("   - 599 Concept nodes")
    print("   - 479 Regular nodes") 
    print("   - 12 Text nodes")
    print("   - 2,364 Concept relationships")
    print("   - 537 Relation relationships")
    print("   - Total: 1,090 nodes, 2,901 relationships")

def main():
    """
    Main function.
    """
    print("🔍 360TKnowledgeBaseInstance Import Helper")
    print("=" * 50)
    
    # Try to find the specific instance
    result = find_360t_instance()
    
    # Generate import instructions
    generate_targeted_import_command()
    
    print("\n" + "=" * 80)
    print("⚠️  **IMPORTANT REMINDERS**")
    print("=" * 80)
    print("• NEVER touch the default 'neo4j' database")
    print("• ONLY work with the 'autoschemakg' database") 
    print("• Use credentials: username='neo4j', password='1979@rabu'")
    print("• Stop the instance BEFORE running neo4j-admin import")
    print("• The import will create the autoschemakg database automatically")

if __name__ == "__main__":
    main()
