"""
Resume Extraction Analyzer - Analyzes existing extraction output to determine precise resume point.

This module analyzes the existing Gemini-2.5 Flash output file to calculate exactly where
the extraction process stopped and where it should resume from.
"""

import json
import os
import glob
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
from dataclasses import dataclass
from collections import defaultdict


@dataclass
class ResumePoint:
    """Information about where to resume extraction."""
    total_chunks_processed: int
    total_characters_processed: int
    current_file: str
    current_file_position: int
    current_file_total_chars: int
    current_file_progress_percent: float
    files_completed: List[str]
    files_remaining: List[str]
    batch_resume_index: int
    last_document_id: str
    resume_recommendation: str


class ResumeExtractionAnalyzer:
    """Analyzes existing extraction output to determine precise resume point."""
    
    def __init__(self, existing_output_file: str, data_directory: str, chunk_size: int = 2884):
        """
        Initialize the resume analyzer.
        
        Args:
            existing_output_file: Path to existing extraction JSON file
            data_directory: Directory containing input JSON files
            chunk_size: Size of text chunks used in extraction (default: 2884)
        """
        self.existing_output_file = existing_output_file
        self.data_directory = data_directory
        self.chunk_size = chunk_size
        
        # Analysis results
        self.processed_chunks: List[Dict[str, Any]] = []
        self.input_files: List[str] = []
        self.file_character_counts: Dict[str, int] = {}
        
    def load_existing_output(self) -> List[Dict[str, Any]]:
        """Load and parse the existing extraction output file."""
        chunks = []
        
        if not os.path.exists(self.existing_output_file):
            raise FileNotFoundError(f"Existing output file not found: {self.existing_output_file}")
        
        print(f"📖 Loading existing extraction output: {self.existing_output_file}")
        
        with open(self.existing_output_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        chunk = json.loads(line)
                        chunks.append(chunk)
                    except json.JSONDecodeError as e:
                        print(f"⚠️ Warning: Skipping malformed JSON at line {line_num}: {e}")
        
        print(f"✅ Loaded {len(chunks)} processed chunks")
        self.processed_chunks = chunks
        return chunks
    
    def discover_input_files(self) -> List[str]:
        """Discover all input JSON files in the data directory."""
        data_path = Path(self.data_directory)
        
        if not data_path.exists():
            raise FileNotFoundError(f"Data directory not found: {self.data_directory}")
        
        # Find all JSON files (excluding .jsonl files)
        json_files = []
        for file_path in data_path.glob("*.json"):
            if not file_path.name.endswith('.jsonl'):
                json_files.append(file_path.name)
        
        # Sort alphabetically to match processing order
        json_files.sort()
        
        print(f"📁 Discovered {len(json_files)} input files")
        for i, filename in enumerate(json_files, 1):
            print(f"  {i:2d}. {filename}")
        
        self.input_files = json_files
        return json_files
    
    def calculate_file_character_counts(self) -> Dict[str, int]:
        """Calculate character count for each input file."""
        file_counts = {}
        
        print("📊 Calculating character counts for input files...")
        
        for filename in self.input_files:
            file_path = os.path.join(self.data_directory, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    # Assume input files are JSON with a 'text' field
                    data = json.load(f)
                    
                    if isinstance(data, dict) and 'text' in data:
                        char_count = len(data['text'])
                    elif isinstance(data, list):
                        # Handle multiple documents in a single file
                        char_count = sum(len(doc.get('text', '')) for doc in data if isinstance(doc, dict))
                    else:
                        # Fallback: count entire file content
                        f.seek(0)
                        content = f.read()
                        char_count = len(content)
                    
                    file_counts[filename] = char_count
                    print(f"  {filename}: {char_count:,} characters")
                    
            except Exception as e:
                print(f"⚠️ Warning: Could not read {filename}: {e}")
                file_counts[filename] = 0
        
        self.file_character_counts = file_counts
        return file_counts
    
    def analyze_processing_pattern(self) -> Dict[str, Any]:
        """Analyze the processing pattern from existing chunks."""
        if not self.processed_chunks:
            return {}
        
        # Group chunks by document ID
        chunks_by_id = defaultdict(list)
        for chunk in self.processed_chunks:
            doc_id = chunk.get('id', 'unknown')
            chunks_by_id[doc_id].append(chunk)
        
        # Analyze chunk sizes and patterns
        chunk_sizes = [len(chunk.get('original_text', '')) for chunk in self.processed_chunks]
        
        analysis = {
            'total_chunks': len(self.processed_chunks),
            'unique_document_ids': len(chunks_by_id),
            'chunk_sizes': {
                'min': min(chunk_sizes) if chunk_sizes else 0,
                'max': max(chunk_sizes) if chunk_sizes else 0,
                'avg': sum(chunk_sizes) / len(chunk_sizes) if chunk_sizes else 0
            },
            'total_characters_extracted': sum(chunk_sizes),
            'document_id_distribution': {doc_id: len(chunks) for doc_id, chunks in chunks_by_id.items()}
        }
        
        return analysis
    
    def map_chunks_to_files(self) -> Dict[str, Any]:
        """Map processed chunks to their source files."""
        # This is a simplified mapping - in practice, you'd need to track
        # the exact file-to-chunk mapping during extraction
        
        total_chars_processed = sum(len(chunk.get('original_text', '')) for chunk in self.processed_chunks)
        
        # Calculate cumulative character counts for files
        cumulative_chars = 0
        file_mapping = {}
        
        for filename in self.input_files:
            file_chars = self.file_character_counts.get(filename, 0)
            file_mapping[filename] = {
                'start_char': cumulative_chars,
                'end_char': cumulative_chars + file_chars,
                'total_chars': file_chars,
                'status': 'pending'
            }
            cumulative_chars += file_chars
        
        # Determine which files have been completed
        chars_processed = 0
        current_file = None
        current_file_position = 0
        
        for filename in self.input_files:
            file_info = file_mapping[filename]
            file_chars = file_info['total_chars']
            
            if chars_processed + file_chars <= total_chars_processed:
                # File completely processed
                file_info['status'] = 'completed'
                chars_processed += file_chars
            elif chars_processed < total_chars_processed:
                # File partially processed - this is where we resume
                file_info['status'] = 'in_progress'
                current_file = filename
                current_file_position = total_chars_processed - chars_processed
                break
            else:
                # File not yet processed
                file_info['status'] = 'pending'
        
        return {
            'file_mapping': file_mapping,
            'current_file': current_file,
            'current_file_position': current_file_position,
            'total_chars_processed': total_chars_processed
        }
    
    def calculate_resume_point(self, batch_size: int = 16) -> ResumePoint:
        """Calculate the exact resume point for the extraction."""
        print("\n🔍 Calculating precise resume point...")
        
        # Load and analyze existing data
        self.load_existing_output()
        self.discover_input_files()
        self.calculate_file_character_counts()
        
        # Analyze processing patterns
        pattern_analysis = self.analyze_processing_pattern()
        file_mapping = self.map_chunks_to_files()
        
        # Calculate resume point
        total_chunks = len(self.processed_chunks)
        total_chars = pattern_analysis['total_characters_extracted']
        
        # Determine batch resume index
        batch_resume_index = (total_chunks // batch_size)
        
        # Get current file status
        current_file = file_mapping['current_file']
        current_file_position = file_mapping['current_file_position']
        
        if current_file:
            current_file_total = self.file_character_counts.get(current_file, 0)
            current_file_progress = (current_file_position / current_file_total * 100) if current_file_total > 0 else 0
        else:
            current_file_total = 0
            current_file_progress = 100.0  # All files completed
        
        # Categorize files
        files_completed = []
        files_remaining = []
        
        for filename, info in file_mapping['file_mapping'].items():
            if info['status'] == 'completed':
                files_completed.append(filename)
            elif info['status'] in ['in_progress', 'pending']:
                files_remaining.append(filename)
        
        # Get last document ID
        last_doc_id = self.processed_chunks[-1].get('id', 'unknown') if self.processed_chunks else 'none'
        
        # Generate recommendation
        if current_file:
            recommendation = f"Resume from batch {batch_resume_index}, continue processing {current_file} at position {current_file_position:,}"
        else:
            recommendation = "All files completed - no resume needed"
        
        resume_point = ResumePoint(
            total_chunks_processed=total_chunks,
            total_characters_processed=total_chars,
            current_file=current_file or "None",
            current_file_position=current_file_position,
            current_file_total_chars=current_file_total,
            current_file_progress_percent=current_file_progress,
            files_completed=files_completed,
            files_remaining=files_remaining,
            batch_resume_index=batch_resume_index,
            last_document_id=last_doc_id,
            resume_recommendation=recommendation
        )
        
        return resume_point
    
    def print_resume_analysis(self, resume_point: ResumePoint):
        """Print detailed resume analysis."""
        print("\n📊 RESUME POINT ANALYSIS")
        print("=" * 60)
        
        print(f"📁 Existing Output File: {os.path.basename(self.existing_output_file)}")
        print(f"📂 Data Directory: {self.data_directory}")
        print()
        
        print("🔢 Processing Progress:")
        print(f"   Total chunks processed: {resume_point.total_chunks_processed:,}")
        print(f"   Total characters processed: {resume_point.total_characters_processed:,}")
        print(f"   Last document ID: {resume_point.last_document_id}")
        print()
        
        print("📈 Current File Status:")
        if resume_point.current_file != "None":
            print(f"   Current file: {resume_point.current_file}")
            print(f"   Position: {resume_point.current_file_position:,} / {resume_point.current_file_total_chars:,} chars")
            print(f"   Progress: {resume_point.current_file_progress_percent:.1f}% complete")
        else:
            print("   All files completed")
        print()
        
        print("📋 File Summary:")
        print(f"   ✅ Completed files: {len(resume_point.files_completed)}")
        for filename in resume_point.files_completed:
            print(f"      - {filename}")
        
        print(f"   ⏳ Remaining files: {len(resume_point.files_remaining)}")
        for filename in resume_point.files_remaining[:5]:  # Show first 5
            print(f"      - {filename}")
        if len(resume_point.files_remaining) > 5:
            print(f"      ... and {len(resume_point.files_remaining) - 5} more")
        print()
        
        print("🎯 Resume Instructions:")
        print(f"   Resume from batch index: {resume_point.batch_resume_index}")
        print(f"   Recommendation: {resume_point.resume_recommendation}")
        print()
        
        # Calculate remaining work
        remaining_chars = sum(self.file_character_counts.get(f, 0) for f in resume_point.files_remaining)
        if resume_point.current_file != "None":
            remaining_chars += (resume_point.current_file_total_chars - resume_point.current_file_position)
        
        print("⚡ Remaining Work:")
        print(f"   Characters remaining: {remaining_chars:,}")
        total_chars = sum(self.file_character_counts.values())
        if total_chars > 0:
            completion_percent = (resume_point.total_characters_processed / total_chars) * 100
            print(f"   Overall completion: {completion_percent:.1f}%")
        print()


def main():
    """Main function for standalone testing."""
    # Default paths - update these for your setup
    existing_output = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    data_directory = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data"
    
    print("🔍 Resume Extraction Analyzer")
    print("=" * 50)
    
    try:
        analyzer = ResumeExtractionAnalyzer(existing_output, data_directory)
        resume_point = analyzer.calculate_resume_point(batch_size=16)
        analyzer.print_resume_analysis(resume_point)
        
        return resume_point
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()