#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create FAISS indexes from existing NPY files.
"""

import numpy as np
import faiss
from pathlib import Path

def build_faiss_from_npy_fixed(npy_path, index_path, index_type="HNSW,Flat"):
    """Build FAISS index from NPY file with correct API."""
    print(f"Building FAISS index: {index_path}")
    
    # Load embeddings from NPY file
    embeddings_list = []
    with open(npy_path, "rb") as f:
        try:
            while True:
                embeddings = np.load(f)
                embeddings_list.append(embeddings)
        except:
            pass  # End of file
    
    # Combine all embeddings
    all_embeddings = np.vstack(embeddings_list)
    print(f"  Loaded {all_embeddings.shape[0]} embeddings with dimension {all_embeddings.shape[1]}")
    
    # Create FAISS index
    dimension = all_embeddings.shape[1]
    index = faiss.index_factory(dimension, index_type, faiss.METRIC_INNER_PRODUCT)
    
    # Add embeddings to index
    if index_type.startswith("IVF"):
        # For IVF indexes, train first
        index.train(all_embeddings)
    
    index.add(all_embeddings)
    
    # Save index with correct API
    faiss.write_index(index, str(index_path))  # Convert Path to string
    print(f"  ✅ Created FAISS index with {index.ntotal} vectors")

def main():
    """Create FAISS indexes for existing NPY files."""
    
    base_dir = Path("import/pdf_dataset")
    vector_index_dir = base_dir / "vector_index"
    
    print("🚀 Creating FAISS indexes from NPY files")
    print("=" * 50)
    
    # Define files to index
    filename_pattern = ""
    
    index_tasks = [
        {
            'name': 'Triple Nodes',
            'npy': vector_index_dir / f"triple_nodes_{filename_pattern}_from_json_with_emb.npy",
            'index': vector_index_dir / f"triple_nodes_{filename_pattern}_from_json_with_emb_non_norm.index"
        },
        {
            'name': 'Text Nodes', 
            'npy': vector_index_dir / f"text_nodes_{filename_pattern}_from_json_with_emb.npy",
            'index': vector_index_dir / f"text_nodes_{filename_pattern}_from_json_with_emb_non_norm.index"
        },
        {
            'name': 'Triple Edges',
            'npy': vector_index_dir / f"triple_edges_{filename_pattern}_from_json_with_concept_with_emb.npy",
            'index': vector_index_dir / f"triple_edges_{filename_pattern}_from_json_with_concept_with_emb_non_norm.index"
        }
    ]
    
    # Create each index
    for i, task in enumerate(index_tasks, 1):
        print(f"\n🔧 Task {i}/3: {task['name']}")
        
        if not task['npy'].exists():
            print(f"  ❌ NPY file not found: {task['npy']}")
            continue
        
        if task['index'].exists():
            print(f"  ✅ Index already exists, skipping")
            continue
        
        try:
            build_faiss_from_npy_fixed(task['npy'], task['index'])
            print(f"  ✅ Successfully created index for {task['name']}")
            
        except Exception as e:
            print(f"  ❌ Error creating index for {task['name']}: {str(e)}")
            continue
    
    # Verify all files
    print(f"\n📁 Final verification - Files in {vector_index_dir}:")
    if vector_index_dir.exists():
        for file in sorted(vector_index_dir.iterdir()):
            size = file.stat().st_size
            print(f"  ✅ {file.name} ({size:,} bytes)")
    
    print(f"\n✅ FAISS index creation completed!")

if __name__ == "__main__":
    try:
        main()
        print("\n🎉 Successfully created all FAISS indexes!")
    except Exception as e:
        print(f"\n❌ Failed to create FAISS indexes: {str(e)}")
        import traceback
        traceback.print_exc()