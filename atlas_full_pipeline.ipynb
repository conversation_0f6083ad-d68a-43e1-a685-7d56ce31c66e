import os 
os.environ['CUDA_VISIBLE_DEVICES'] = '1'
from atlas_rag.kg_construction.triple_extraction import KnowledgeGraphExtractor
from atlas_rag.kg_construction.triple_config import ProcessingConfig
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
from transformers import pipeline
from configparser import ConfigParser
# Load OpenRouter API key from config file
config = ConfigParser()
config.read('config.ini')
# model_name = "meta-llama/Llama-3.3-70B-Instruct"
client = OpenAI(
  base_url="https://api.deepinfra.com/v1/openai",
  api_key=config['settings']['DEEPINFRA_API_KEY'],
)

model_name = "meta-llama/Meta-Llama-3.1-8B-Instruct"
# model_name = "meta-llama/Llama-3.2-3B-Instruct"
# client = pipeline(
#     "text-generation",
#     model=model_name,
#     device_map="auto",
# )
filename_pattern = 'CICGPC_Glazing_ver1.0a'
output_directory = f'import/{filename_pattern}'
triple_generator = LLMGenerator(client, model_name=model_name)

kg_extraction_config = ProcessingConfig(
      model_path=model_name,
      data_directory="example_data",
      filename_pattern=filename_pattern,
      batch_size_triple=3,
      batch_size_concept=16,
      output_directory=f"{output_directory}",
      max_new_tokens=2048,
      max_workers=3,
      remove_doc_spaces=True, # For removing duplicated spaces in the document text
)
kg_extractor = KnowledgeGraphExtractor(model=triple_generator, config=kg_extraction_config)

# construct entity&event graph
kg_extractor.run_extraction()

# Convert Triples Json to CSV
kg_extractor.convert_json_to_csv()

# Concept Generation
kg_extractor.generate_concept_csv_temp()

kg_extractor.create_concept_csv()

# convert csv to graphml for networkx
kg_extractor.convert_to_graphml()

import os
os.environ['CUDA_VISIBLE_DEVICES'] = '1'
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import NvEmbed, SentenceEmbedding
from transformers import AutoModel
# Load the SentenceTransformer model
encoder_model_name = "sentence-transformers/all-MiniLM-L6-v2"
sentence_model = SentenceTransformer(encoder_model_name, trust_remote_code=True, model_kwargs={'device_map': "auto"})
sentence_encoder = SentenceEmbedding(sentence_model)
# sentence_model.max_seq_length = 32768
# sentence_model.tokenizer.padding_side="right"
# sentence_model = AutoModel.from_pretrained(encoder_model_name, trust_remote_code=True, device_map="auto")
# sentence_encoder = NvEmbed(sentence_model)

from openai import OpenAI
from atlas_rag.llm_generator import LLMGenerator
from configparser import ConfigParser
# Load OpenRouter API key from config file
config = ConfigParser()
config.read('config.ini')
# reader_model_name = "meta-llama/llama-3.3-70b-instruct"
reader_model_name = "meta-llama/Llama-3.3-70B-Instruct"
client = OpenAI(
  # base_url="https://openrouter.ai/api/v1",
  # api_key=config['settings']['OPENROUTER_API_KEY'],
  base_url="https://api.deepinfra.com/v1/openai",
  api_key=config['settings']['DEEPINFRA_API_KEY'],
)
llm_generator = LLMGenerator(client=client, model_name=reader_model_name)

from atlas_rag.vectorstore import create_embeddings_and_index
keyword = 'CICGPC_Glazing_ver1.0a'
working_directory = f'import/{keyword}'
data = create_embeddings_and_index(
    sentence_encoder=sentence_encoder,
    model_name = encoder_model_name,
    working_directory=working_directory,
    keyword=keyword,
    include_concept=True,
    include_events=True,
    normalize_embeddings= True,
    text_batch_size=64,
    node_and_edge_batch_size=64,
)

# Initialize desired RAG method for benchmarking
from atlas_rag.retriever import HippoRAG2Retriever
from atlas_rag import setup_logger

hipporag2_retriever = HippoRAG2Retriever(
    llm_generator=llm_generator,
    sentence_encoder=sentence_encoder,
    data = data,
)

# perform retrieval
content, sorted_context_ids = hipporag2_retriever.retrieve("How is the U-value relevant to thermal insulation performance in glazing products?", topN=3)
print(f"Retrieved content: {content}")

# start benchmarking
sorted_context = "\n".join(content)
llm_generator.generate_with_context("How is the U-value relevant to thermal insulation performance in glazing products?", sorted_context, max_new_tokens=2048, temperature=0.5)

from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
# use sentence embedding if you want to use sentence transformer
# use NvEmbed if you want to use NvEmbed-v2 model
sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
sentence_encoder = SentenceEmbedding(sentence_model)

# add numeric id to the csv so that we can use vector indices
kg_extractor.add_numeric_id()

# compute embedding
kg_extractor.compute_kg_embedding(sentence_encoder) # default encoder_model_name="all-MiniLM-L12-v2", only compute all embeddings except any concept related embeddings
# kg_extractor.compute_embedding(encoder_model_name="all-MiniLM-L12-v2")
# kg_extractor.compute_embedding(encoder_model_name="nvidia/NV-Embed-v2")

# create faiss index
kg_extractor.create_faiss_index() # default index_type="HNSW,Flat", other options: "IVF65536_HNSW32,Flat" for large KG
# kg_extractor.create_faiss_index(index_type="HNSW,Flat")
# kg_extractor.create_faiss_index(index_type="IVF65536_HNSW32,Flat")


from openai import OpenAI

base_url ="http://0.0.0.0:10085/v1/"
client = OpenAI(api_key="EMPTY", base_url=base_url)

# knowledge graph en_simple_wiki_v0
message = [
    {
        "role": "system",
        "content": "You are a helpful assistant that answers questions based on the knowledge graph.",
    },
    {
        "role": "user",
        "content": "Question: Who is Alex Mercer?",
    }
]
response = client.chat.completions.create(
    model="llama",
    messages=message,
    max_tokens=2048,
    temperature=0.5
)
print(response.choices[0].message.content)