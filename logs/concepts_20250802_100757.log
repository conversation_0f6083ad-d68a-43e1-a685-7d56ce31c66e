[Preflight] OLLAMA_BASE_URL=http://localhost:11434
[Preflight] OLLAMA_MODEL=qwen3:30b-a3b-instruct-2507-q4_K_M
[Preflight] Ollama reachable.
[Preflight] Model present: qwen3:30b-a3b-instruct-2507-q4_K_M
[Run] Starting: python pdf_kg_extraction_pipeline_robust.py --stage concepts
🎯 Robust AutoSchemaKG Pipeline
Enhanced with resumability, error handling, and progress tracking
From PDF Processing to Interactive Knowledge Graph Q&A

🔍 Validating Prerequisites...
------------------------------
✅ config.ini found
✅ Found 37 input files
✅ Import directory ready
✅ All prerequisites validated

🧠 Running Concept Generation
=== Generate Concepts from Triples ===
Dataset: pdf_dataset

🧪 Using Ollama backend locally: qwen3:30b-a3b-instruct-2507-q4_K_M @ http://localhost:11434
🔧 Using standard configuration with robust optimizations
🔧 Creating processing configuration for pdf_dataset
Model: gemini-2.5-flash
Quality-first configuration: Using original defaults for maximum quality
✅ Processing configuration created with following settings:
  - Batch size (triples): 16
  - Batch size (concepts): 64
  - Max tokens: 8192
  - Max workers: 2
  - Output directory: import/pdf_dataset
10:08:12 | INFO     | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250802_100812.log, Errors: pipeline_errors_pdf_dataset_20250802_100812.log
10:08:12 | INFO     | 🚀 Progress tracking initialized for dataset: pdf_dataset
10:08:12 | INFO     | 📊 Total files to process: 0
10:08:12 | INFO     | 🛡️ ErrorHandler initialized with robust recovery strategies
10:08:12 | INFO     | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
▶️ Starting: Concept Generation
10:08:12 | INFO     | ============================================================
10:08:12 | INFO     | 🎯 Starting Stage: concept_generation
10:08:12 | INFO     | 📋 Description: Generating concepts from triples
10:08:12 | INFO     | ============================================================
10:08:12 | INFO     | 🧠 Using batch size: 16
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:12,945 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:12 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:12,948 - ERROR - 🚨 Error recorded: unknown_error
10:08:12 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:12,948 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:12 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:12,948 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:12 | WARNING  | 🔄 Retrying concept_generation in 1.3s (attempt 1/3)
2025-08-02 10:08:12,948 - WARNING - 🔄 Retrying concept_generation in 1.3s (attempt 1/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:14,392 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:14 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:14,393 - ERROR - 🚨 Error recorded: unknown_error
10:08:14 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:14,393 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:14 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:14,393 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:14 | WARNING  | 🔄 Retrying concept_generation in 3.2s (attempt 2/3)
2025-08-02 10:08:14,393 - WARNING - 🔄 Retrying concept_generation in 3.2s (attempt 2/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:17,901 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:17 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:17,902 - ERROR - 🚨 Error recorded: unknown_error
10:08:17 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:17,902 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:17 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:17,902 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:17 | WARNING  | 🔄 Retrying concept_generation in 5.8s (attempt 3/3)
2025-08-02 10:08:17,902 - WARNING - 🔄 Retrying concept_generation in 5.8s (attempt 3/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:23,832 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:23 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:23,833 - ERROR - 🚨 Error recorded: unknown_error
10:08:23 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:23,833 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:23 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:23,833 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:23 | ERROR    | 💥 All retries exhausted for concept_generation
2025-08-02 10:08:23,833 - ERROR - 💥 All retries exhausted for concept_generation
10:08:23 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:23,834 - ERROR - 🚨 Error recorded: unknown_error
10:08:23 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:23,834 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:23 | ERROR    |    Context: {'stage': 'concept_generation', 'operation': 'generate_concept_csv'}
2025-08-02 10:08:23,834 - ERROR -    Context: {'stage': 'concept_generation', 'operation': 'generate_concept_csv'}
10:08:23 | ERROR    | Concept generation failed: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:23,834 - ERROR - Concept generation failed: '_OllamaGenerator' object has no attribute 'generate_response'
❌ Failed: Concept Generation

❌ Pipeline failed. Check logs for details.
[Done] Concepts stage completed successfully. Log: ./logs/concepts_20250802_100757.log
