🎯 Robust AutoSchemaKG Pipeline
Enhanced with resumability, error handling, and progress tracking
From PDF Processing to Interactive Knowledge Graph Q&A

🔍 Validating Prerequisites...
------------------------------
✅ config.ini found
✅ Found 37 input files
✅ Import directory ready
✅ All prerequisites validated

🧠 Running Concept Generation
=== Generate Concepts from Triples ===
Dataset: pdf_dataset

🧪 Using Ollama backend locally: qwen3:30b-a3b-instruct-2507-q4_K_M @ {OLLAMA_HOST:-http://localhost:11434}
🔧 Using standard configuration with robust optimizations
🔧 Creating processing configuration for pdf_dataset
Model: gemini-2.5-flash
Quality-first configuration: Using original defaults for maximum quality
✅ Processing configuration created with following settings:
  - Batch size (triples): 16
  - Batch size (concepts): 64
  - Max tokens: 8192
  - Max workers: 2
  - Output directory: import/pdf_dataset
10:15:42 | INFO     | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250802_101542.log, Errors: pipeline_errors_pdf_dataset_20250802_101542.log
10:15:42 | INFO     | 🚀 Progress tracking initialized for dataset: pdf_dataset
10:15:42 | INFO     | 📊 Total files to process: 0
10:15:42 | INFO     | 🛡️ ErrorHandler initialized with robust recovery strategies
10:15:42 | INFO     | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
▶️ Starting: Concept Generation
10:15:42 | INFO     | ============================================================
10:15:42 | INFO     | 🎯 Starting Stage: concept_generation
10:15:42 | INFO     | 📋 Description: Generating concepts from triples
10:15:42 | INFO     | ============================================================
10:15:42 | INFO     | 🧠 Using batch size: 16
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:15:42,137 - ERROR - Error processing event batch: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:15:42 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:15:42,141 - ERROR - 🚨 Error recorded: unknown_error
10:15:42 | ERROR    |    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'
2025-08-02 10:15:42,141 - ERROR -    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'
10:15:42 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:15:42,141 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:15:42 | WARNING  | 🔄 Retrying concept_generation in 1.5s (attempt 1/3)
2025-08-02 10:15:42,141 - WARNING - 🔄 Retrying concept_generation in 1.5s (attempt 1/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:15:43,794 - ERROR - Error processing event batch: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:15:43 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:15:43,794 - ERROR - 🚨 Error recorded: unknown_error
10:15:43 | ERROR    |    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'
2025-08-02 10:15:43,795 - ERROR -    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'
10:15:43 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:15:43,795 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:15:43 | WARNING  | 🔄 Retrying concept_generation in 2.9s (attempt 2/3)
2025-08-02 10:15:43,795 - WARNING - 🔄 Retrying concept_generation in 2.9s (attempt 2/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:15:46,916 - ERROR - Error processing event batch: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:15:46 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:15:46,917 - ERROR - 🚨 Error recorded: unknown_error
10:15:46 | ERROR    |    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'
2025-08-02 10:15:46,917 - ERROR -    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'return_text_only'
10:15:46 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:15:46,917 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:15:46 | WARNING  | 🔄 Retrying concept_generation in 7.1s (attempt 3/3)
2025-08-02 10:15:46,917 - WARNING - 🔄 Retrying concept_generation in 7.1s (attempt 3/3)
