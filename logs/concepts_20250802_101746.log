🎯 Robust AutoSchemaKG Pipeline
Enhanced with resumability, error handling, and progress tracking
From PDF Processing to Interactive Knowledge Graph Q&A

🔍 Validating Prerequisites...
------------------------------
✅ config.ini found
✅ Found 37 input files
✅ Import directory ready
✅ All prerequisites validated

🧠 Running Concept Generation
=== Generate Concepts from Triples ===
Dataset: pdf_dataset

🧪 Using Ollama backend locally: qwen3:30b-a3b-instruct-2507-q4_K_M @ http://localhost:11434
🔧 Using standard configuration with robust optimizations
🔧 Creating processing configuration for pdf_dataset
Model: gemini-2.5-flash
Quality-first configuration: Using original defaults for maximum quality
✅ Processing configuration created with following settings:
  - Batch size (triples): 16
  - Batch size (concepts): 64
  - Max tokens: 8192
  - Max workers: 2
  - Output directory: import/pdf_dataset
10:17:50 | INFO     | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250802_101750.log, Errors: pipeline_errors_pdf_dataset_20250802_101750.log
10:17:50 | INFO     | 🚀 Progress tracking initialized for dataset: pdf_dataset
10:17:50 | INFO     | 📊 Total files to process: 0
10:17:50 | INFO     | 🛡️ ErrorHandler initialized with robust recovery strategies
10:17:50 | INFO     | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
▶️ Starting: Concept Generation
10:17:50 | INFO     | ============================================================
10:17:50 | INFO     | 🎯 Starting Stage: concept_generation
10:17:50 | INFO     | 📋 Description: Generating concepts from triples
10:17:50 | INFO     | ============================================================
10:17:50 | INFO     | 🧠 Using batch size: 16
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:17:50,865 - ERROR - Error processing event batch: _OllamaGenerator.generate_response() got an unexpected keyword argument 'max_workers'. Did you mean 'max_tokens'?

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:17:50 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:17:50,867 - ERROR - 🚨 Error recorded: unknown_error
10:17:50 | ERROR    |    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'max_workers'. Did you mean 'max_tokens'?
2025-08-02 10:17:50,867 - ERROR -    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'max_workers'. Did you mean 'max_tokens'?
10:17:50 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:17:50,867 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:17:50 | WARNING  | 🔄 Retrying concept_generation in 1.5s (attempt 1/3)
2025-08-02 10:17:50,867 - WARNING - 🔄 Retrying concept_generation in 1.5s (attempt 1/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:17:52,487 - ERROR - Error processing event batch: _OllamaGenerator.generate_response() got an unexpected keyword argument 'max_workers'. Did you mean 'max_tokens'?

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:17:52 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:17:52,488 - ERROR - 🚨 Error recorded: unknown_error
10:17:52 | ERROR    |    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'max_workers'. Did you mean 'max_tokens'?
2025-08-02 10:17:52,488 - ERROR -    Message: _OllamaGenerator.generate_response() got an unexpected keyword argument 'max_workers'. Did you mean 'max_tokens'?
10:17:52 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:17:52,488 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:17:52 | WARNING  | 🔄 Retrying concept_generation in 2.2s (attempt 2/3)
2025-08-02 10:17:52,488 - WARNING - 🔄 Retrying concept_generation in 2.2s (attempt 2/3)
