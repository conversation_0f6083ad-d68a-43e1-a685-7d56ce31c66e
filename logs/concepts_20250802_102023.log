🎯 Robust AutoSchemaKG Pipeline
Enhanced with resumability, error handling, and progress tracking
From PDF Processing to Interactive Knowledge Graph Q&A

🔍 Validating Prerequisites...
------------------------------
✅ config.ini found
✅ Found 37 input files
✅ Import directory ready
✅ All prerequisites validated

🧠 Running Concept Generation
=== Generate Concepts from Triples ===
Dataset: pdf_dataset

🧪 Using Ollama backend locally: qwen3:30b-a3b-instruct-2507-q4_K_M @ http://localhost:11434
🔧 Using standard configuration with robust optimizations
🔧 Creating processing configuration for pdf_dataset
Model: gemini-2.5-flash
Quality-first configuration: Using original defaults for maximum quality
✅ Processing configuration created with following settings:
  - Batch size (triples): 16
  - Batch size (concepts): 64
  - Max tokens: 8192
  - Max workers: 2
  - Output directory: import/pdf_dataset
10:20:27 | INFO     | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250802_102027.log, Errors: pipeline_errors_pdf_dataset_20250802_102027.log
10:20:27 | INFO     | 🚀 Progress tracking initialized for dataset: pdf_dataset
10:20:27 | INFO     | 📊 Total files to process: 0
10:20:27 | INFO     | 🛡️ ErrorHandler initialized with robust recovery strategies
10:20:27 | INFO     | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
▶️ Starting: Concept Generation
10:20:27 | INFO     | ============================================================
10:20:27 | INFO     | 🎯 Starting Stage: concept_generation
10:20:27 | INFO     | 📋 Description: Generating concepts from triples
10:20:27 | INFO     | ============================================================
10:20:27 | INFO     | 🧠 Using batch size: 16
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:20:27,519 - ERROR - Error processing event batch: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:20:27 | ERROR    | 🚨 Error recorded: api_error
2025-08-02 10:20:27,532 - ERROR - 🚨 Error recorded: api_error
10:20:27 | ERROR    |    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
2025-08-02 10:20:27,533 - ERROR -    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
10:20:27 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:20:27,533 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:20:27 | WARNING  | 🔄 Retrying concept_generation in 1.1s (attempt 1/3)
2025-08-02 10:20:27,533 - WARNING - 🔄 Retrying concept_generation in 1.1s (attempt 1/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:20:28,798 - ERROR - Error processing event batch: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:20:28 | ERROR    | 🚨 Error recorded: api_error
2025-08-02 10:20:28,799 - ERROR - 🚨 Error recorded: api_error
10:20:28 | ERROR    |    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
2025-08-02 10:20:28,799 - ERROR -    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
10:20:28 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:20:28,799 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:20:28 | WARNING  | 🔄 Retrying concept_generation in 2.9s (attempt 2/3)
2025-08-02 10:20:28,800 - WARNING - 🔄 Retrying concept_generation in 2.9s (attempt 2/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:20:31,976 - ERROR - Error processing event batch: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:20:31 | ERROR    | 🚨 Error recorded: api_error
2025-08-02 10:20:31,977 - ERROR - 🚨 Error recorded: api_error
10:20:31 | ERROR    |    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
2025-08-02 10:20:31,977 - ERROR -    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
10:20:31 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:20:31,977 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:20:31 | WARNING  | 🔄 Retrying concept_generation in 7.4s (attempt 3/3)
2025-08-02 10:20:31,977 - WARNING - 🔄 Retrying concept_generation in 7.4s (attempt 3/3)
