/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/atlas_rag/kg_construction/utils/json_processing/json_to_csv.py:22: SyntaxWarning: invalid escape sequence '\e'
  new_text = text.replace("\n", " ").replace("\r", " ").replace("\t", " ").replace("\v", " ").replace("\f", " ").replace("\b", " ").replace("\a", " ").replace("\e", " ").replace(";", ",")
/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/robust_kg_extractor.py:444: SyntaxWarning: invalid escape sequence '\e'
  new_text = text.replace("\n", " ").replace("\r", " ").replace("\t", " ").replace("\v", " ").replace("\f", " ").replace("\b", " ").replace("\a", " ").replace("\e", " ").replace(";", ",")
🎯 Robust AutoSchemaKG Pipeline
Enhanced with resumability, error handling, and progress tracking
From PDF Processing to Interactive Knowledge Graph Q&A

🔍 Validating Prerequisites...
------------------------------
✅ config.ini found
✅ Found 37 input files
✅ Import directory ready
✅ All prerequisites validated

🧠 Running Concept Generation
=== Generate Concepts from Triples ===
Dataset: pdf_dataset

🧪 Using Ollama backend locally: qwen3:30b-a3b-instruct-2507-q4_K_M @ http://localhost:11434
🔧 Using standard configuration with robust optimizations
🔧 Creating processing configuration for pdf_dataset
Model: gemini-2.5-flash
Quality-first configuration: Using original defaults for maximum quality
✅ Processing configuration created with following settings:
  - Batch size (triples): 16
  - Batch size (concepts): 64
  - Max tokens: 8192
  - Max workers: 2
  - Output directory: import/pdf_dataset
10:08:07 | INFO     | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250802_100807.log, Errors: pipeline_errors_pdf_dataset_20250802_100807.log
10:08:07 | INFO     | 🚀 Progress tracking initialized for dataset: pdf_dataset
10:08:07 | INFO     | 📊 Total files to process: 0
10:08:07 | INFO     | 🛡️ ErrorHandler initialized with robust recovery strategies
10:08:07 | INFO     | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
▶️ Starting: Concept Generation
10:08:07 | INFO     | ============================================================
10:08:07 | INFO     | 🎯 Starting Stage: concept_generation
10:08:07 | INFO     | 📋 Description: Generating concepts from triples
10:08:07 | INFO     | ============================================================
10:08:07 | INFO     | 🧠 Using batch size: 16
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:07,900 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:08 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:08,916 - ERROR - 🚨 Error recorded: unknown_error
10:08:08 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:08,916 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:08 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:08,916 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:08 | WARNING  | 🔄 Retrying concept_generation in 1.8s (attempt 1/3)
2025-08-02 10:08:08,916 - WARNING - 🔄 Retrying concept_generation in 1.8s (attempt 1/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:10,948 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:10 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:10,949 - ERROR - 🚨 Error recorded: unknown_error
10:08:10 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:10,949 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:10 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:10,949 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:10 | WARNING  | 🔄 Retrying concept_generation in 3.1s (attempt 2/3)
2025-08-02 10:08:10,949 - WARNING - 🔄 Retrying concept_generation in 3.1s (attempt 2/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:14,269 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:14 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:14,270 - ERROR - 🚨 Error recorded: unknown_error
10:08:14 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:14,270 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:14 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:14,270 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:14 | WARNING  | 🔄 Retrying concept_generation in 6.9s (attempt 3/3)
2025-08-02 10:08:14,270 - WARNING - 🔄 Retrying concept_generation in 6.9s (attempt 3/3)
all_batches 4282

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]2025-08-02 10:08:21,262 - ERROR - Error processing event batch: '_OllamaGenerator' object has no attribute 'generate_response'

Shard_0:   0%|          | 0/4282 [00:00<?, ?it/s]
10:08:21 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:21,262 - ERROR - 🚨 Error recorded: unknown_error
10:08:21 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:21,262 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:21 | ERROR    |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:08:21,262 - ERROR -    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
10:08:21 | ERROR    | 💥 All retries exhausted for concept_generation
2025-08-02 10:08:21,262 - ERROR - 💥 All retries exhausted for concept_generation
10:08:21 | ERROR    | 🚨 Error recorded: unknown_error
2025-08-02 10:08:21,263 - ERROR - 🚨 Error recorded: unknown_error
10:08:21 | ERROR    |    Message: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:21,263 - ERROR -    Message: '_OllamaGenerator' object has no attribute 'generate_response'
10:08:21 | ERROR    |    Context: {'stage': 'concept_generation', 'operation': 'generate_concept_csv'}
2025-08-02 10:08:21,263 - ERROR -    Context: {'stage': 'concept_generation', 'operation': 'generate_concept_csv'}
10:08:21 | ERROR    | Concept generation failed: '_OllamaGenerator' object has no attribute 'generate_response'
2025-08-02 10:08:21,263 - ERROR - Concept generation failed: '_OllamaGenerator' object has no attribute 'generate_response'
❌ Failed: Concept Generation

❌ Pipeline failed. Check logs for details.
