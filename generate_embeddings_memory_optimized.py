#!/usr/bin/env python3
"""
Memory-optimized script to generate missing embedding CSV files for pdf_dataset.
This script uses smaller batch sizes and memory management to avoid OOM issues.
"""

import csv
import gc
import torch
from pathlib import Path
from setup_embedding_model import setup_qwen_embedding_model
from atlas_rag.vectorstore.embedding_model import BaseEmbeddingModel

def clear_memory():
    """Clear GPU/MPS memory."""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    elif torch.backends.mps.is_available():
        torch.mps.empty_cache()

def generate_text_embeddings_optimized(input_file: Path, output_file: Path, sentence_encoder: BaseEmbeddingModel):
    """Generate text node embeddings CSV file with memory optimization."""
    print(f"📊 Generating text embeddings (memory optimized)...")
    print(f"  Input:  {input_file}")
    print(f"  Output: {output_file}")
    
    # Use much smaller batch size to avoid memory issues
    batch_size = 256  # Reduced from 2048
    processed_count = 0
    
    with open(input_file, "r", encoding='utf-8') as csvfile_text_node:
        with open(output_file, "w", newline='', encoding='utf-8') as csvfile_text_node_emb:
            reader_text_node = csv.reader(csvfile_text_node)
            writer_text_node = csv.writer(csvfile_text_node_emb)
            
            # Write header
            writer_text_node.writerow(["text_id:ID", "original_text", ":LABEL", "embedding:STRING"])
            
            batch_text_nodes = []
            batch_rows = []
            
            for row in reader_text_node:
                if row[0] == "text_id:ID":
                    continue
                
                batch_text_nodes.append(row[1])  # original_text is in column 1
                batch_rows.append(row)
                
                if len(batch_text_nodes) == batch_size:
                    try:
                        # Generate embeddings for batch
                        text_node_embeddings = sentence_encoder.encode(batch_text_nodes, batch_size=batch_size, show_progress_bar=False)
                        text_node_embedding_dict = dict(zip(batch_text_nodes, text_node_embeddings))
                        
                        # Write batch to file
                        for row in batch_rows:
                            embedding = text_node_embedding_dict[row[1]].tolist()
                            new_row = [row[0], row[1], row[2], embedding]
                            writer_text_node.writerow(new_row)
                        
                        processed_count += len(batch_text_nodes)
                        print(f"    Processed {processed_count} text nodes...")
                        
                        # Clear memory after each batch
                        del text_node_embeddings, text_node_embedding_dict
                        clear_memory()
                        
                    except Exception as e:
                        print(f"    ❌ Error processing batch at {processed_count}: {str(e)}")
                        # Skip this batch and continue
                        pass
                    
                    batch_text_nodes = []
                    batch_rows = []
            
            # Process remaining text nodes
            if len(batch_text_nodes) > 0:
                try:
                    text_node_embeddings = sentence_encoder.encode(batch_text_nodes, batch_size=batch_size, show_progress_bar=False)
                    text_node_embedding_dict = dict(zip(batch_text_nodes, text_node_embeddings))
                    
                    for row in batch_rows:
                        embedding = text_node_embedding_dict[row[1]].tolist()
                        new_row = [row[0], row[1], row[2], embedding]
                        writer_text_node.writerow(new_row)
                    
                    processed_count += len(batch_text_nodes)
                    
                    # Clear memory
                    del text_node_embeddings, text_node_embedding_dict
                    clear_memory()
                    
                except Exception as e:
                    print(f"    ❌ Error processing final batch: {str(e)}")
            
    print(f"  ✅ Generated embeddings for {processed_count} text nodes")

def generate_edge_embeddings_optimized(input_file: Path, output_file: Path, sentence_encoder: BaseEmbeddingModel):
    """Generate edge embeddings CSV file with memory optimization."""
    print(f"📊 Generating edge embeddings (memory optimized)...")
    print(f"  Input:  {input_file}")
    print(f"  Output: {output_file}")
    
    # Use smaller batch size to avoid memory issues
    batch_size = 512  # Reduced from 2048
    processed_count = 0
    
    with open(input_file, "r", encoding='utf-8') as csvfile_edge:
        with open(output_file, "w", newline='', encoding='utf-8') as csvfile_edge_emb:
            reader_edge = csv.reader(csvfile_edge)
            writer_edge = csv.writer(csvfile_edge_emb)
            
            # Write header
            writer_edge.writerow([":START_ID", ":END_ID", "relation", "file_id", "concepts", "synsets", "embedding:STRING", ":TYPE"])
            
            batch_edges = []
            batch_rows = []
            
            for row in reader_edge:
                if row[0] == ":START_ID":
                    continue
                
                # Create edge string: "start_id relation end_id"
                edge_string = " ".join([row[0], row[2], row[1]])
                batch_edges.append(edge_string)
                batch_rows.append(row)
                
                if len(batch_edges) == batch_size:
                    try:
                        # Generate embeddings for batch
                        edge_embeddings = sentence_encoder.encode(batch_edges, batch_size=batch_size, show_progress_bar=False)
                        edge_embedding_dict = dict(zip(batch_edges, edge_embeddings))
                        
                        # Write batch to file
                        for row in batch_rows:
                            edge_string = " ".join([row[0], row[2], row[1]])
                            new_row = [row[0], row[1], row[2], "", row[3], row[4], edge_embedding_dict[edge_string].tolist(), row[5]]
                            writer_edge.writerow(new_row)
                        
                        processed_count += len(batch_edges)
                        print(f"    Processed {processed_count} edges...")
                        
                        # Clear memory after each batch
                        del edge_embeddings, edge_embedding_dict
                        clear_memory()
                        
                    except Exception as e:
                        print(f"    ❌ Error processing batch at {processed_count}: {str(e)}")
                        # Skip this batch and continue
                        pass
                    
                    batch_edges = []
                    batch_rows = []
            
            # Process remaining edges
            if len(batch_edges) > 0:
                try:
                    edge_embeddings = sentence_encoder.encode(batch_edges, batch_size=batch_size, show_progress_bar=False)
                    edge_embedding_dict = dict(zip(batch_edges, edge_embeddings))
                    
                    for row in batch_rows:
                        edge_string = " ".join([row[0], row[2], row[1]])
                        new_row = [row[0], row[1], row[2], "", row[3], row[4], edge_embedding_dict[edge_string].tolist(), row[5]]
                        writer_edge.writerow(new_row)
                    
                    processed_count += len(batch_edges)
                    
                    # Clear memory
                    del edge_embeddings, edge_embedding_dict
                    clear_memory()
                    
                except Exception as e:
                    print(f"    ❌ Error processing final batch: {str(e)}")
            
    print(f"  ✅ Generated embeddings for {processed_count} edges")

def main():
    """Main function to generate missing embedding files."""
    
    base_dir = Path("import/pdf_dataset")
    
    print("🚀 Generating missing embedding CSV files (Memory Optimized)")
    print("=" * 70)
    
    # Check if base directory exists
    if not base_dir.exists():
        print(f"❌ Base directory {base_dir} not found!")
        return False
    
    # Setup embedding model
    print("📦 Setting up embedding model...")
    try:
        sentence_encoder = setup_qwen_embedding_model()
        print("✅ Embedding model loaded successfully")
        
        # Clear memory after loading
        clear_memory()
        
    except Exception as e:
        print(f"❌ Failed to load embedding model: {str(e)}")
        return False
    
    triples_csv_dir = base_dir / "triples_csv"
    concept_csv_dir = base_dir / "concept_csv"
    
    # Only generate the files that failed previously
    embedding_tasks = [
        {
            'input': triples_csv_dir / "text_nodes__from_json.csv", 
            'output': triples_csv_dir / "text_nodes__from_json_with_emb.csv",
            'function': generate_text_embeddings_optimized,
            'name': 'Text Node Embeddings'
        },
        {
            'input': concept_csv_dir / "triple_edges__from_json_with_concept.csv",
            'output': triples_csv_dir / "triple_edges__from_json_with_concept_with_emb.csv", 
            'function': generate_edge_embeddings_optimized,
            'name': 'Edge Embeddings'
        }
    ]
    
    for i, task in enumerate(embedding_tasks, 1):
        print(f"\n🔧 Task {i}/2: {task['name']}")
        
        if not task['input'].exists():
            print(f"  ❌ Input file not found: {task['input']}")
            continue
            
        if task['output'].exists():
            print(f"  ✅ Output file already exists, skipping: {task['output']}")
            continue
            
        # Ensure output directory exists
        task['output'].parent.mkdir(exist_ok=True)
        
        try:
            task['function'](task['input'], task['output'], sentence_encoder)
            print(f"  ✅ Successfully generated {task['name']}")
            
        except Exception as e:
            print(f"  ❌ Error generating {task['name']}: {str(e)}")
            continue
    
    print(f"\n✅ All embedding generation tasks completed!")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Successfully generated missing embedding files!")
    else:
        print("\n❌ Failed to generate missing embedding files!")