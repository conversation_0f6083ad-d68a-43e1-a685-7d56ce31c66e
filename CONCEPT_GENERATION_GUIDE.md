# AutoSchemaKG Concept Generation Guide

This guide explains two approaches for concept generation in AutoSchemaKG, each optimized for different use cases.

## Overview

AutoSchemaKG provides two concept generation methods:

1. **Real-time Processing** (`run_concept_generation_robust.py`)
2. **Batch API Processing** (`run_concept_generation_batch_api.py`)

## Approach 1: Real-time Processing

### When to Use
- ✅ Need immediate results
- ✅ Interactive development workflow  
- ✅ Small to medium datasets
- ✅ Real-time monitoring and debugging

### Characteristics
- **Batch Size**: 10 concepts per batch
- **Total Batches**: ~55 batches for typical dataset
- **Sessions Needed**: 11 sessions (due to AFC rate limiting)
- **Processing Time**: ~1-2 hours total
- **Cost**: Standard API pricing
- **Rate Limits**: Subject to AFC "max remote calls: 10"

### Usage
```bash
python run_concept_generation_robust.py
```

### Features
- ✅ Checkpoint system for resume capability
- ✅ Real-time progress monitoring
- ✅ Immediate error handling and feedback
- ✅ AFC-aware rate limit management
- ✅ Backup creation for safety

### Configuration
```python
ProcessingConfig(
    batch_size_concept=10,      # Medium batches for balance
    max_new_tokens=8192,        # Full context for quality
    max_workers=1,              # Sequential processing
    record=True                 # Detailed logging
)
```

### Expected Output
```
🎯 Starting concept generation with AFC optimization...
⚡ Large batches (10 concepts each) minimize API calls for AFC efficiency

📊 Final Results:
  - Total concepts generated: 550
  - Processing time: 3600.0 seconds
  - Sessions completed: 11/11
```

## Approach 2: Batch API Processing

### When to Use
- ✅ Cost optimization priority (50% savings)
- ✅ Large-scale processing (1000+ concepts)
- ✅ Non-urgent processing (up to 24 hours)
- ✅ No rate limit constraints needed

### Characteristics
- **Processing Model**: Asynchronous batch jobs
- **Total API Calls**: 1 batch job submission
- **Sessions Needed**: 1 session 
- **Processing Time**: Up to 24 hours (asynchronous)
- **Cost**: 50% reduction vs individual calls
- **Rate Limits**: No AFC limitations

### Usage
```bash
python run_concept_generation_batch_api.py
```

### Features
- ✅ 50% cost reduction
- ✅ No AFC rate limiting constraints
- ✅ Handles unlimited concept volumes
- ✅ BigQuery/GCS integration ready
- ✅ Asynchronous processing

### Workflow
1. **Data Preparation**: Convert CSV to JSONL batch format
2. **Job Submission**: Submit to Google's Batch API
3. **Monitoring**: Poll job status until completion
4. **Result Processing**: Download and convert results to CSV

### Expected Output
```
🎯 AutoSchemaKG Batch API Concept Generation
📊 Results:
  - Total requests processed: 550
  - Concepts generated: 550
  - Cost savings: 50% vs individual API calls
  - AFC limits: Bypassed (no rate limiting)
  - Processing time: Asynchronous (up to 24 hours)
```

## Comparison Matrix

| Feature | Real-time Processing | Batch API Processing |
|---------|---------------------|---------------------|
| **Speed** | 1-2 hours | Up to 24 hours |
| **Cost** | Standard | 50% savings |
| **Rate Limits** | AFC limited | No limits |
| **Monitoring** | Real-time | Polling required |
| **Resume** | Checkpoint system | Job-level retry |
| **Scalability** | Limited by AFC | Unlimited |
| **Complexity** | Simple | Moderate |
| **Debugging** | Interactive | Batch logs |

## Decision Guide

### Choose Real-time Processing When:
- You need results within hours
- Working with datasets < 1000 concepts
- Developing and testing workflows
- Prefer interactive monitoring

### Choose Batch API Processing When:
- Cost optimization is priority
- Processing 1000+ concepts
- Can wait up to 24 hours for results
- Need to bypass rate limiting

## Implementation Notes

### Real-time Processing
- Uses `batch_size_concept=10` for AFC compliance
- Requires 11 sessions due to "max remote calls: 10" limit
- Each session processes ~5 batches (10 API calls)
- Checkpoint system allows safe interruption/resume

### Batch API Processing  
- Requires Google Cloud Storage for input/output files
- Uses JSONL format for batch requests
- Implements job polling for status monitoring
- Returns results in structured format for CSV conversion

## File Outputs

Both approaches create:
- `concept_shard_0.csv`: Main concept output file
- Backup files for data safety
- Detailed logs for troubleshooting

## Next Steps

After concept generation completion:
1. **Verify Results**: Check `import/{dataset}/concepts/concept_shard_0.csv`
2. **Generate GraphML**: Include concepts in graph representation
3. **Neo4j Import**: Load complete knowledge graph with concepts
4. **Quality Check**: Review concept quality and coverage

## Troubleshooting

### Real-time Processing Issues
- **AFC Rate Limits**: Script automatically handles with delays
- **Timeouts**: Use checkpoint system to resume
- **Empty Results**: Check API key and network connectivity

### Batch API Processing Issues
- **Job Submission Fails**: Verify GCS bucket permissions
- **Long Processing**: Normal for batch jobs (up to 24 hours)
- **Result Parsing**: Check JSONL format and structure

## Configuration Files

Key files for customization:
- `triple_config.py`: Batch size and processing parameters
- `setup_llm_generator_direct.py`: API configuration
- `config.ini`: API keys and model settings