2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250803_095848.log, Errors: pipeline_errors_pdf_dataset_20250803_095848.log
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 🚀 Progress tracking initialized for dataset: pdf_dataset
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📊 Total files to process: 0
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 🛡️ ErrorHandler initialized with robust recovery strategies
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 🚀 Starting robust knowledge graph extraction pipeline
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 🎯 Starting Stage: initialization
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📋 Description: Initializing robust pipeline
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Discovered 37 input files
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📋 Existing pipeline state found - checking resumability
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | ⚠️ Existing state not resumable - starting fresh
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | ✅ Stage completed: initialization
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | ▶️ Starting: Triple Extraction
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 🎯 Starting Stage: triple_extraction
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📋 Description: Extracting triples from documents
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📄 Starting extraction - output file: import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250803095848_1_in_1.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v12.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Counterpart Relationship Management Tool User Guide_Market Maker.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Order Book User Guide_Market Taker.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit - R3 19.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SupersonicTrader.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: PS Mapping (Bridge Admin).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Maker.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS Market Taker Bridge.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit (HTML GUI).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Business Configuration Tool BRIDGE.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13_DRAFT.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Taker.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Regulatory Data (Company Admin Control).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MT_XML_API_v2.1.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Change Requests (Bridge Admin).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Taker_Web.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit  (Swing GUI).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: TWS Market Maker (BRIDGE).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: FX Futures SupersonicTrader.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Accessing the ECN via Supersonic.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration User Manual.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS - csv File extract.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Data Disclosure Configuration (Bridge Admin).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limit_REST_API_v2.0.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: HTML Auto Dealing Suite.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration - SEF Data.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor_Trading Limits Profile.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Maker_Web.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MM_FIX_API_v7.0.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Cash management Tool_GCT_configuration.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration I-TEX.json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bank Basket Configuration (Bridge Admin).json
2025-08-03 09:58:48 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Institution Configuration (Bridge Admin).json
2025-08-03 09:58:49 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: unknown_error
2025-08-03 09:58:49 | ERROR    | pipeline_pdf_dataset |    Message: '_OllamaGenerator' object has no attribute 'triple_extraction'
2025-08-03 09:58:49 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'triple_extraction', 'total_files': 37, 'operation': 'triple_extraction'}
2025-08-03 09:58:49 | WARNING  | pipeline_pdf_dataset | 🔄 Retrying triple_extraction in 1.3s (attempt 1/3)
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📄 Starting extraction - output file: import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250803095851_1_in_1.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v12.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Counterpart Relationship Management Tool User Guide_Market Maker.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Order Book User Guide_Market Taker.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit - R3 19.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SupersonicTrader.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: PS Mapping (Bridge Admin).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Maker.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS Market Taker Bridge.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit (HTML GUI).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Business Configuration Tool BRIDGE.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13_DRAFT.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Taker.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Regulatory Data (Company Admin Control).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MT_XML_API_v2.1.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Change Requests (Bridge Admin).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Taker_Web.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit  (Swing GUI).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: TWS Market Maker (BRIDGE).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: FX Futures SupersonicTrader.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Accessing the ECN via Supersonic.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration User Manual.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS - csv File extract.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Data Disclosure Configuration (Bridge Admin).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limit_REST_API_v2.0.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: HTML Auto Dealing Suite.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration - SEF Data.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor_Trading Limits Profile.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Maker_Web.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MM_FIX_API_v7.0.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Cash management Tool_GCT_configuration.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration I-TEX.json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bank Basket Configuration (Bridge Admin).json
2025-08-03 09:58:51 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Institution Configuration (Bridge Admin).json
2025-08-03 09:58:51 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: unknown_error
2025-08-03 09:58:51 | ERROR    | pipeline_pdf_dataset |    Message: '_OllamaGenerator' object has no attribute 'triple_extraction'
2025-08-03 09:58:51 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'triple_extraction', 'total_files': 37, 'operation': 'triple_extraction'}
2025-08-03 09:58:51 | WARNING  | pipeline_pdf_dataset | 🔄 Retrying triple_extraction in 2.9s (attempt 2/3)
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📄 Starting extraction - output file: import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250803095854_1_in_1.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v12.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Counterpart Relationship Management Tool User Guide_Market Maker.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Order Book User Guide_Market Taker.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit - R3 19.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SupersonicTrader.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: PS Mapping (Bridge Admin).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Maker.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS Market Taker Bridge.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit (HTML GUI).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Business Configuration Tool BRIDGE.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13_DRAFT.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Taker.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Regulatory Data (Company Admin Control).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MT_XML_API_v2.1.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Change Requests (Bridge Admin).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Taker_Web.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit  (Swing GUI).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: TWS Market Maker (BRIDGE).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: FX Futures SupersonicTrader.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Accessing the ECN via Supersonic.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration User Manual.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS - csv File extract.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Data Disclosure Configuration (Bridge Admin).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limit_REST_API_v2.0.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: HTML Auto Dealing Suite.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration - SEF Data.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor_Trading Limits Profile.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Maker_Web.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MM_FIX_API_v7.0.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Cash management Tool_GCT_configuration.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration I-TEX.json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bank Basket Configuration (Bridge Admin).json
2025-08-03 09:58:54 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Institution Configuration (Bridge Admin).json
2025-08-03 09:58:54 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: unknown_error
2025-08-03 09:58:54 | ERROR    | pipeline_pdf_dataset |    Message: '_OllamaGenerator' object has no attribute 'triple_extraction'
2025-08-03 09:58:54 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'triple_extraction', 'total_files': 37, 'operation': 'triple_extraction'}
2025-08-03 09:58:54 | WARNING  | pipeline_pdf_dataset | 🔄 Retrying triple_extraction in 4.3s (attempt 3/3)
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📄 Starting extraction - output file: import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250803095858_1_in_1.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v12.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Counterpart Relationship Management Tool User Guide_Market Maker.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Order Book User Guide_Market Taker.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit - R3 19.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SupersonicTrader.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: PS Mapping (Bridge Admin).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Maker.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS Market Taker Bridge.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit (HTML GUI).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Business Configuration Tool BRIDGE.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: RFS_FIX_MarketTaker_v13_DRAFT.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: SEF Market Taker.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Regulatory Data (Company Admin Control).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MT_XML_API_v2.1.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Change Requests (Bridge Admin).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Taker_Web.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: ADS_Market Maker Cockpit  (Swing GUI).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: TWS Market Maker (BRIDGE).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: FX Futures SupersonicTrader.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Accessing the ECN via Supersonic.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration User Manual.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: EMS - csv File extract.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Data Disclosure Configuration (Bridge Admin).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limit_REST_API_v2.0.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: HTML Auto Dealing Suite.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration - SEF Data.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Limits Monitor_Trading Limits Profile.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: User_Guide_SEF_Market_Maker_Web.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Order_MM_FIX_API_v7.0.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Cash management Tool_GCT_configuration.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bridge Administration I-TEX.json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Bank Basket Configuration (Bridge Admin).json
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📁 Processing file: Institution Configuration (Bridge Admin).json
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: unknown_error
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset |    Message: '_OllamaGenerator' object has no attribute 'triple_extraction'
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'triple_extraction', 'total_files': 37, 'operation': 'triple_extraction'}
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset | 💥 All retries exhausted for triple_extraction
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: unknown_error
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset |    Message: '_OllamaGenerator' object has no attribute 'triple_extraction'
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'triple_extraction', 'operation': 'run_extraction'}
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset | Triple extraction failed: '_OllamaGenerator' object has no attribute 'triple_extraction'
2025-08-03 09:58:58 | ERROR    | pipeline_pdf_dataset | ❌ Failed: Triple Extraction
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 🎉 Pipeline Processing Complete!
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | 📊 Final Summary for pdf_dataset:
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset |    Total files: 37
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset |    ✅ Completed: 0
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset |    ❌ Failed: 0
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset |    📈 Success rate: 0.0%
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset |    ⏱️  Total time: 9.9s
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset |    💾 Peak memory: 319.1MB
2025-08-03 09:58:58 | INFO     | pipeline_pdf_dataset | ============================================================
