#!/usr/bin/env python3
"""
Script to generate missing embedding CSV files for pdf_dataset.
This script creates the embedding versions of CSV files that are required for the pipeline.
"""

import os
import sys
from pathlib import Path
from setup_embedding_model import setup_qwen_embedding_model
from atlas_rag.vectorstore.embedding_model import BaseEmbeddingModel

def generate_missing_embedding_files(base_dir: Path, sentence_encoder: BaseEmbeddingModel):
    """Generate all missing embedding CSV files."""
    
    print("🔧 Generating missing embedding CSV files...")
    print("=" * 60)
    
    triples_csv_dir = base_dir / "triples_csv"
    concept_csv_dir = base_dir / "concept_csv"
    
    # Files to generate embeddings for
    embedding_tasks = [
        {
            'input': triples_csv_dir / "triple_nodes__from_json_without_emb.csv",
            'output': triples_csv_dir / "triple_nodes__from_json_with_emb.csv",
            'type': 'nodes'
        },
        {
            'input': triples_csv_dir / "text_nodes__from_json.csv", 
            'output': triples_csv_dir / "text_nodes__from_json_with_emb.csv",
            'type': 'text_nodes'
        },
        {
            'input': concept_csv_dir / "triple_edges__from_json_with_concept.csv",
            'output': triples_csv_dir / "triple_edges__from_json_with_concept_with_emb.csv", 
            'type': 'edges'
        }
    ]
    
    for i, task in enumerate(embedding_tasks, 1):
        print(f"\n📊 Task {i}/3: Generating embeddings for {task['type']}")
        print(f"  Input:  {task['input']}")
        print(f"  Output: {task['output']}")
        
        if not task['input'].exists():
            print(f"  ❌ Input file not found: {task['input']}")
            continue
            
        if task['output'].exists():
            print(f"  ✅ Output file already exists, skipping: {task['output']}")
            continue
            
        # Ensure output directory exists
        task['output'].parent.mkdir(exist_ok=True)
        
        try:
            if task['type'] == 'nodes':
                # Generate node embeddings
                sentence_encoder.compute_kg_embedding(
                    node_csv_without_emb=str(task['input']),
                    node_csv_file=str(task['output']),
                    edge_csv_without_emb=str(concept_csv_dir / "triple_edges__from_json_with_concept.csv"),
                    edge_csv_file=str(triples_csv_dir / "temp_edges.csv"),  # temp file
                    text_node_csv_without_emb=str(triples_csv_dir / "text_nodes__from_json.csv"),
                    text_node_csv=str(triples_csv_dir / "temp_text.csv"),  # temp file
                    batch_size=2048
                )
                # Clean up temp files
                temp_edges = triples_csv_dir / "temp_edges.csv"
                temp_text = triples_csv_dir / "temp_text.csv"
                if temp_edges.exists():
                    temp_edges.unlink()
                if temp_text.exists():
                    temp_text.unlink()
                    
            elif task['type'] == 'text_nodes':
                # Generate text node embeddings only
                sentence_encoder.compute_kg_embedding(
                    node_csv_without_emb=str(triples_csv_dir / "triple_nodes__from_json_without_emb.csv"),
                    node_csv_file=str(triples_csv_dir / "temp_nodes.csv"),  # temp file
                    edge_csv_without_emb=str(concept_csv_dir / "triple_edges__from_json_with_concept.csv"),
                    edge_csv_file=str(triples_csv_dir / "temp_edges.csv"),  # temp file
                    text_node_csv_without_emb=str(task['input']),
                    text_node_csv=str(task['output']),
                    batch_size=2048
                )
                # Clean up temp files
                temp_nodes = triples_csv_dir / "temp_nodes.csv"
                temp_edges = triples_csv_dir / "temp_edges.csv"
                if temp_nodes.exists():
                    temp_nodes.unlink()
                if temp_edges.exists():
                    temp_edges.unlink()
                    
            elif task['type'] == 'edges':
                # Generate edge embeddings only
                sentence_encoder.compute_kg_embedding(
                    node_csv_without_emb=str(triples_csv_dir / "triple_nodes__from_json_without_emb.csv"),
                    node_csv_file=str(triples_csv_dir / "temp_nodes.csv"),  # temp file
                    edge_csv_without_emb=str(task['input']),
                    edge_csv_file=str(task['output']),
                    text_node_csv_without_emb=str(triples_csv_dir / "text_nodes__from_json.csv"),
                    text_node_csv=str(triples_csv_dir / "temp_text.csv"),  # temp file
                    batch_size=2048
                )
                # Clean up temp files
                temp_nodes = triples_csv_dir / "temp_nodes.csv"
                temp_text = triples_csv_dir / "temp_text.csv"
                if temp_nodes.exists():
                    temp_nodes.unlink()
                if temp_text.exists():
                    temp_text.unlink()
            
            print(f"  ✅ Successfully generated embeddings for {task['type']}")
            
        except Exception as e:
            print(f"  ❌ Error generating embeddings for {task['type']}: {str(e)}")
            continue
    
    print(f"\n✅ Embedding generation completed!")

def main():
    """Main function to generate missing embedding files."""
    
    base_dir = Path("import/pdf_dataset")
    
    print("🚀 Generating missing embedding CSV files for pdf_dataset")
    print("=" * 70)
    
    # Check if base directory exists
    if not base_dir.exists():
        print(f"❌ Base directory {base_dir} not found!")
        return False
    
    # Setup embedding model
    print("📦 Setting up embedding model...")
    try:
        sentence_encoder = setup_qwen_embedding_model()
        print("✅ Embedding model loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load embedding model: {str(e)}")
        return False
    
    # Generate missing embedding files
    generate_missing_embedding_files(base_dir, sentence_encoder)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Successfully generated missing embedding files!")
    else:
        print("\n❌ Failed to generate missing embedding files!")