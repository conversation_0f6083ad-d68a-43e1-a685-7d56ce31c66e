#!/usr/bin/env python3
"""
Monitor the resume extraction progress.
"""

import os
import time
import json
from datetime import datetime

def monitor_extraction():
    """Monitor the extraction progress."""
    output_file = "import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    
    print("🔍 Resume Extraction Monitor")
    print("=" * 50)
    print(f"Monitoring: {os.path.basename(output_file)}")
    print()
    
    last_size = 0
    last_lines = 0
    start_time = time.time()
    
    while True:
        try:
            if os.path.exists(output_file):
                # Get file stats
                stat = os.stat(output_file)
                current_size = stat.st_size
                
                # Count lines
                with open(output_file, 'r') as f:
                    current_lines = sum(1 for _ in f)
                
                # Calculate progress
                size_mb = current_size / (1024 * 1024)
                new_lines = current_lines - last_lines
                size_growth = current_size - last_size
                
                # Time calculations
                elapsed = time.time() - start_time
                elapsed_min = elapsed / 60
                
                print(f"📊 {datetime.now().strftime('%H:%M:%S')} | "
                      f"Lines: {current_lines:,} (+{new_lines}) | "
                      f"Size: {size_mb:.1f}MB (+{size_growth/1024:.1f}KB) | "
                      f"Time: {elapsed_min:.1f}min")
                
                # Try to read last few lines for progress info
                try:
                    with open(output_file, 'r') as f:
                        lines = f.readlines()
                        if lines:
                            last_line = lines[-1].strip()
                            if last_line:
                                # Try to parse as JSON to see if it's a valid extraction
                                try:
                                    data = json.loads(last_line)
                                    if 'chunk_id' in data:
                                        print(f"   📝 Latest: Chunk {data.get('chunk_id', 'N/A')} from {data.get('source_file', 'N/A')}")
                                except:
                                    pass
                except:
                    pass
                
                last_size = current_size
                last_lines = current_lines
            else:
                print(f"❌ Output file not found: {output_file}")
            
            print()
            time.sleep(30)  # Check every 30 seconds
            
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_extraction()
