#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the vector_index directory structure for pdf_dataset.
This script creates the required .npy and .index files in the vector_index directory.
"""

import os
import sys
from pathlib import Path
from atlas_rag.vectorstore.create_neo4j_index import create_faiss_index

def populate_vector_index_directory(base_dir: Path):
    """Populate the vector_index directory with the required files."""
    
    print("🔧 Fixing vector_index directory structure...")
    print("=" * 60)
    
    # Ensure vector_index directory exists
    vector_index_dir = base_dir / "vector_index"
    vector_index_dir.mkdir(exist_ok=True)
    
    # The filename pattern is empty ("") which creates double underscores
    filename_pattern = ""
    
    try:
        # Use the create_faiss_index function which handles the conversion and indexing
        print("📊 Converting CSV embeddings to NPY and creating FAISS indexes...")
        print(f"  Output directory: {base_dir}")
        print(f"  Filename pattern: '{filename_pattern}' (empty)")
        
        create_faiss_index(
            output_directory=str(base_dir),
            filename_pattern=filename_pattern,
            index_type="HNSW,Flat"  # Use HNSW for better performance
        )
        
        print("✅ Successfully created vector_index files")
        
        # Verify files were created
        expected_files = [
            f"triple_nodes_{filename_pattern}_from_json_with_emb.npy",
            f"text_nodes_{filename_pattern}_from_json_with_emb.npy", 
            f"triple_edges_{filename_pattern}_from_json_with_concept_with_emb.npy",
            f"triple_nodes_{filename_pattern}_from_json_with_emb_non_norm.index",
            f"text_nodes_{filename_pattern}_from_json_with_emb_non_norm.index",
            f"triple_edges_{filename_pattern}_from_json_with_concept_with_emb_non_norm.index"
        ]
        
        print("\n📁 Checking created files:")
        for filename in expected_files:
            file_path = vector_index_dir / filename
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"  ✅ {filename} ({size:,} bytes)")
            else:
                print(f"  ❌ {filename} (missing)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating vector_index files: {str(e)}")
        return False

def verify_input_files(base_dir: Path):
    """Verify that all required input files exist."""
    
    print("🔍 Verifying input files...")
    
    required_files = [
        "triples_csv/triple_nodes__from_json_with_emb.csv",
        "triples_csv/text_nodes__from_json_with_emb.csv",
        "triples_csv/triple_edges__from_json_with_concept_with_emb.csv"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = base_dir / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"  ✅ {file_path} ({size:,} bytes)")
        else:
            print(f"  ❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def main():
    """Main function to fix the vector_index structure."""
    
    base_dir = Path("import/pdf_dataset")
    
    print("🚀 Fixing vector_index directory structure for pdf_dataset")
    print("=" * 70)
    
    # Check if base directory exists
    if not base_dir.exists():
        print(f"❌ Base directory {base_dir} not found!")
        return False
    
    # Verify input files exist
    if not verify_input_files(base_dir):
        print("❌ Required input files are missing!")
        return False
    
    # Populate vector_index directory
    success = populate_vector_index_directory(base_dir)
    
    if success:
        print("\n✅ Vector index structure fixed successfully!")
        print("📁 The vector_index directory now contains:")
        print("  - .npy files (numpy arrays of embeddings)")
        print("  - .index files (FAISS indexes for fast similarity search)")
        return True
    else:
        print("\n❌ Failed to fix vector index structure!")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Successfully fixed vector_index directory structure!")
    else:
        print("\n❌ Failed to fix vector_index directory structure!")