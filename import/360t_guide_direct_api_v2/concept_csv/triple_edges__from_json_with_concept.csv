":START_ID",":END_ID","relation","concepts","synsets",":TYPE"
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.","image","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.","logo","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.","360T","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.","green rectangle","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.","rounded corners","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The text ""360"" is in white with a green outline.","text","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The text ""360"" is in white with a green outline.","360","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The text ""360"" is in white with a green outline.","white","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The text ""360"" is in white with a green outline.","green outline","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The ""T"" is also in white with a green outline.","T","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The ""T"" is also in white with a green outline.","white","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The ""T"" is also in white with a green outline.","green outline","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".","two arrows","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".","0","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".","360","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.","THIS FILE","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.","PROPRIETARY AND CONFIDENTIAL INFORMATION","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.","TRADE SECRETS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.","THIS FILE","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.","THIRD PARTY","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.","PRIOR WRITTEN APPROVAL","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.","360 TREASURY SYSTEMS AG","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"user manual","Bank Basket feature","describes","['defines', 'explains', 'characterizes', 'portrays', 'details']","[]","Relation"
"Bank Basket feature","360T Bridge Administration tool","is part of","['subset of', 'component of', 'member of', 'belongs to', 'inclusion']","[]","Relation"
"Bank Basket feature","improved rule management capabilities","enhanced to provide","['optimize', 'upgrade', 'enable', 'facilitate']","[]","Relation"
"improved rule management capabilities","configuration groups","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"configuration groups","currency","based on","['source', 'origin', 'reliance', 'foundation', 'derivation']","[]","Relation"
"configuration groups","currency couple","based on","['source', 'origin', 'reliance', 'foundation', 'derivation']","[]","Relation"
"configuration groups","time period","based on","['source', 'origin', 'reliance', 'foundation', 'derivation']","[]","Relation"
"configuration groups","product(s)","based on","['source', 'origin', 'reliance', 'foundation', 'derivation']","[]","Relation"
"improved rule management capabilities","configuration of separate baskets","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"separate baskets","request type (RFS, Order, SEP)","configured by","['arranged by', 'set by', 'defined by', 'determined by']","[]","Relation"
"improved rule management capabilities","ability to apply and remove temporary blocks","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"temporary blocks","configured rules","do not affect","['have no bearing', 'no impact', 'be independent', 'remain unchanged', 'not alter']","[]","Relation"
"temporary blocks","counterpart relationship(s)","do not affect","['have no bearing', 'no impact', 'be independent', 'remain unchanged', 'not alter']","[]","Relation"
"360T enhanced Bank Basket feature","entities","available to","['can obtain', 'for use', 'access to', 'open for', 'provided for']","[]","Relation"
"entities","EMS application","have","['possess', 'contain', 'own', 'experience', 'include']","[]","Relation"
"entities","Bridge application","have","['possess', 'contain', 'own', 'experience', 'include']","[]","Relation"
"users","corresponding user rights","have","['possess', 'contain', 'own', 'experience', 'include']","[]","Relation"
"users","company's Bank Baskets","administer","['control', 'direct', 'oversee', 'govern', 'manage']","[]","Relation"
"<EMAIL>","relevant administrative rights","can set up","['organize', 'establish', 'initiate', 'form', 'create']","[]","Relation"
"customer relationship manager","relevant administrative rights","can set up","['organize', 'establish', 'initiate', 'form', 'create']","[]","Relation"
"Bank Basket configuration","Bridge Administration tool","found within","['inside', 'located in', 'contained in', 'exists in', 'present in']","[]","Relation"
"Bridge Administration","menu option ""Administration""","accessed via","['path', 'through', 'method', 'means', 'channel']","[]","Relation"
"menu option ""Administration""","screen header","located in","['situated', 'placement', 'contained', 'resides', 'position']","[]","Relation"
"screen header","Bridge application","is part of","['subset of', 'component of', 'member of', 'belongs to', 'inclusion']","[]","Relation"
"Bridge Administration feature","homepage","opens to","['lead to', 'provide access', 'reveal', 'allow entry', 'expose']","[]","Relation"
"homepage","available shortcuts","has","['comprise', 'possess', 'contain', 'feature', 'exhibit']","[]","Relation"
"available shortcuts","different configuration tools","lead to","['produce', 'result', 'cause', 'generate', 'effect']","[]","Relation"
"different configuration tools","particular user","are for","['purpose', 'support', 'function', 'intent', 'beneficiary']","[]","Relation"
"quick navigation toolbar","active homepage icon","shows","['reveal', 'make known', 'indicate', 'display', 'exhibit', 'express', 'convey', 'point out', 'demonstrate']","[]","Relation"
"quick navigation toolbar","left side of the homepage","available on","['accessible via', 'obtainable from', 'present at', 'supplied by', 'located at']","[]","Relation"
"image","screenshot","shows","['reveal', 'make known', 'indicate', 'display', 'exhibit', 'express', 'convey', 'point out', 'demonstrate']","[]","Relation"
"screenshot","360T Bank Baskets Configuration user guide","is of","['origin', 'relation', 'characteristic', 'composition', 'attribute']","[]","Relation"
"screen","'Administration Start' page","displays","['reveal', 'show', 'present', 'exhibit', 'manifest']","[]","Relation"
"'Administration Start' page","'Regulatory Data'","has option for","['provision', 'choice', 'alternative', 'availability', 'selection']","[]","Relation"
"'Administration Start' page","'Bank Baskets'","has option for","['provision', 'choice', 'alternative', 'availability', 'selection']","[]","Relation"
"'Administration Start' page","'Change Request'","has option for","['provision', 'choice', 'alternative', 'availability', 'selection']","[]","Relation"
"'Administration Start' page","'Wizards'","has option for","['provision', 'choice', 'alternative', 'availability', 'selection']","[]","Relation"
"'Administration Start' page","'Evaluator Tools'","has option for","['provision', 'choice', 'alternative', 'availability', 'selection']","[]","Relation"
"top of the screen","tabs","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"tabs","'RFS REQUESTER'","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"tabs","'DEAL TRACKING'","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"tabs","'BRIDGE ADMINISTRATION'","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"top of the screen","preferences options","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"top of the screen","help options","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.","user manual","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.","Bank Basket feature","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.","360T Bridge Administration tool","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Bank Basket feature has been enhanced.","Bank Basket feature","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The enhancement provides improved rule management capabilities.","enhancement","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The enhancement provides improved rule management capabilities.","rule management capabilities","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.","configuration groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.","currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.","currency couple","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.","time period","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.","product(s)","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Separate baskets can be configured by request type.","separate baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Separate baskets can be configured by request type.","request type","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Request types include RFS.","RFS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Request types include RFS.","request types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Request types include Order.","Order","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Request types include Order.","request types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Request types include SEP.","SEP","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Request types include SEP.","request types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Temporary blocks can be applied.","temporary blocks","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Temporary blocks can be removed.","temporary blocks","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Applying temporary blocks does not affect the configured rules.","temporary blocks","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Applying temporary blocks does not affect the configured rules.","configured rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Figure 2","Bridge Administration Homepage","depicts","['represent', 'show', 'describe', 'portray', 'illustrate']","[]","Relation"
"""Bank Baskets"" quick link","navigation panel","opens","['reveals', 'starts', 'establishes', 'unlocks', 'initiates']","[]","Relation"
"navigation panel","institution tree","contains","['comprises', 'includes', 'encloses', 'has', 'holds']","[]","Relation"
"institution tree","single TEX entity","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"institution tree","TEX main entity","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"trade-as entities","TEX main entity","configured under","['managed by', 'contained in', 'part of', 'controlled by', 'subordinate to']","[]","Relation"
"trade-on-behalf entities","TEX main entity","configured under","['managed by', 'contained in', 'part of', 'controlled by', 'subordinate to']","[]","Relation"
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.","Bank Baskets quick link","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.","navigation panel","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.","institution tree","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","tree","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","TEX entity","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","TEX main entity","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","trade-as","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","trade-on-behalf","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","ITEX entities","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","main entity","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.","institution","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.","single-click","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.","institution tree","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.","new form/sheet","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.","Bank Basket configuration details","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.","entity","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"It is possible to open multiple forms/sheets at a time.","multiple forms/sheets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selected item will be highlighted as an active task inside the taskbar.","selected item","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selected item will be highlighted as an active task inside the taskbar.","active task","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selected item will be highlighted as an active task inside the taskbar.","taskbar","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:","set of icons","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:","single-click","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:","navigation panel","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A search field will open and the user can type in an alphanumeric value in order to find the desired institution.","search field","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A search field will open and the user can type in an alphanumeric value in order to find the desired institution.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A search field will open and the user can type in an alphanumeric value in order to find the desired institution.","alphanumeric value","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A search field will open and the user can type in an alphanumeric value in order to find the desired institution.","institution","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.","This feature","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.","active task/sheet","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.","navigation panel","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.","selected tree item","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.","active institution","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.","taskbar","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.","scroll from source","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This icon is deactivated when using the Bank Basket configuration.","This icon","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This icon is deactivated when using the Bank Basket configuration.","Bank Basket configuration","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.","other configuration tools","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.","toggle option","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.","individuals","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.","navigation panel","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each entity tab has a Live Audit Log which tracks all unsaved changes.","Individual unsaved changes can be reverted by clicking on the arrow icon.","because","['cause', 'explanation', 'reason', 'grounds']","[]","Relation"
"Each entity tab has a Live Audit Log which tracks all unsaved changes.","Clicking on the ""Discard all changes"" button will revert all unsaved changes.","because","['cause', 'explanation', 'reason', 'grounds']","[]","Relation"
"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.","Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).","Configuration Groups allow users to create a group one single time and reuse it across various rules.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"The toggle option allows the user to display only institutions in the navigation panel.","toggle option","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The toggle option allows the user to display only institutions in the navigation panel.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The toggle option allows the user to display only institutions in the navigation panel.","institutions","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The toggle option allows the user to display only institutions in the navigation panel.","navigation panel","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The toggle option allows the user to display only institutions in the navigation panel.","configuration tools","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.","navigation panel","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.","minimize icon","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.","panel","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each entity tab has a Live Audit Log which tracks all unsaved changes.","entity tab","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each entity tab has a Live Audit Log which tracks all unsaved changes.","Live Audit Log","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each entity tab has a Live Audit Log which tracks all unsaved changes.","unsaved changes","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Individual unsaved changes can be reverted by clicking on the arrow icon.","unsaved changes","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Individual unsaved changes can be reverted by clicking on the arrow icon.","arrow icon","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.","""Discard all changes"" button","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.","unsaved changes","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image contains the text 'User Guide 360T Bank Baskets Configuration'.","image","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image contains the text 'User Guide 360T Bank Baskets Configuration'.","text 'User Guide 360T Bank Baskets Configuration'","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.","entity","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.","institution tree","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.","Configuration Groups tab","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.","data tabs","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.","Configuration Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.","parameters","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.","Bank Basket configuration","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.","RFS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.","Order","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.","SEP","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups allow users to create a group one single time and reuse it across various rules.","Configuration Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups allow users to create a group one single time and reuse it across various rules.","users","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups allow users to create a group one single time and reuse it across various rules.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups allow users to create a group one single time and reuse it across various rules.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for four separate request types are configured on this tab.","Bank Basket rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for four separate request types are configured on this tab.","request types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for four separate request types are configured on this tab.","this tab","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for four separate request types are configured on this tab.","RFS FX Bank Baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for four separate request types are configured on this tab.","RFS MM Bank Baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for four separate request types are configured on this tab.","RFS Commodity Bank Baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for four separate request types are configured on this tab.","RFS Cross Currency Netting Bank Baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.","Provider Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.","Blocked Providers","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.","Orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.","Order Bank Baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for Forward or Spot orders are configured on this tab.","Bank Basket rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for Forward or Spot orders are configured on this tab.","Forward orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for Forward or Spot orders are configured on this tab.","Spot orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for Forward or Spot orders are configured on this tab.","this tab","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).","Provider Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).","Blocked Providers","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).","Supersonic (SEP)","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).","SEP Bank Baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for SEP streaming spot executions are configured on this tab.","Bank Basket rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for SEP streaming spot executions are configured on this tab.","SEP streaming spot executions","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket rules for SEP streaming spot executions are configured on this tab.","this tab","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.","Bank Basket Configurations","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.","RFS requests","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.","Orders requests","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.","SEP requests","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An RFS configuration will have no impact on SEP trading.","RFS configuration","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An RFS configuration will have no impact on SEP trading.","SEP trading","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups","centralized management","facilitate","['enable', 'support', 'aid', 'ease', 'help']","[]","Relation"
"parameters","Bank Basket configuration","used in","['purpose', 'employ', 'application', 'function', 'utility']","[]","Relation"
"Groups","each parameter","configured in","['setup', 'structure', 'organized into', 'arrangement', 'defined as']","[]","Relation"
"Groups","creating rules","reused for","['recycle', 'reclaim', 'repurpose', 'reapply', 're-employ']","[]","Relation"
"groups","set of rules","edited without changing","['review', 'proofread', 'check', 'examine', 'verify']","[]","Relation"
"available parameters","Currency Groups","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"available parameters","Currency Couple Groups","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"available parameters","FX Time Period Groups","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"available parameters","MM Time Period Groups","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"icons","create new group","allow user to","['enable', 'permit', 'authorize', 'provide', 'grant']","[]","Relation"
"icons","edit name of existing group","allow user to","['enable', 'permit', 'authorize', 'provide', 'grant']","[]","Relation"
"icons","delete group","allow user to","['enable', 'permit', 'authorize', 'provide', 'grant']","[]","Relation"
"icons","save changes","allow user to","['enable', 'permit', 'authorize', 'provide', 'grant']","[]","Relation"
"Rename Group","Default Group","cannot be used on","['unsuitability', 'incompatibility', 'restriction', 'exclusion', 'prohibition']","[]","Relation"
"Remove Group","Default Group","cannot be used on","['unsuitability', 'incompatibility', 'restriction', 'exclusion', 'prohibition']","[]","Relation"
"removed group","Default Group","replaced by","['substituted', 'supplanted', 'exchanged for', 'succeeded', 'superseded']","[]","Relation"
"Configuration Groups","configuring complex bank basket rules","useful for","['purpose', 'application', 'function', 'utility', 'benefit']","[]","Relation"
"Users","individual custom rules","set","['place', 'determine', 'establish', 'arrange', 'create']","[]","Relation"
"Individual custom rules","users with less complex bank basket setups","preferable for","['better for', 'ideal for', 'beneficial for', 'suitable for', 'good for']","[]","Relation"
"Bank Basket Configuration","Default Group","contains","['comprises', 'includes', 'encloses', 'has', 'holds']","[]","Relation"
"Default Group","existing values","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"Default Groups","values","modified by removing","['deletion', 'reduction', 'exclusion', 'alteration', 'removal']","[]","Relation"
"new values","Default Group","not added to","['omit', 'exclude', 'lack', 'separate', 'withhold']","[]","Relation"
"new currency","Default Currency Group","not included in","['omit', 'exclude', 'separate from', 'not part', 'left out']","[]","Relation"
"new currency","user","selected by","['picked', 'elected', 'chosen', 'appointed', 'designated']","[]","Relation"
"Currency Groups","single currencies","allow classification of","['identifies class', 'enables sorting', 'groups', 'categorizes', 'defines type']","[]","Relation"
"Currency Groups","single rule for group of currencies","allow setting","['enable configuration', 'grant permission', 'permit adjustment', 'offer options']","[]","Relation"
"Currencies","group","added or removed from","['modification', 'alteration', 'reconfigure', 'update', 'change', 'adjust']","[]","Relation"
"currency group","rule creation for interest rate products","simplifies","['clarify', 'uncomplicate', 'ease', 'facilitate', 'streamline']","[]","Relation"
"interest rate products","Loan","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"interest rate products","Deposit","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"interest rate products","Interest Rate Swap","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"interest rate products","FRA","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"interest rate products","CapFloor","include","['contain', 'comprise', 'incorporate', 'encompass']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters.","Configuration Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters.","centralized management","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters.","parameters","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups facilitate centralized management of parameters.","Bank Basket configuration","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","parameters","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","requests","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","RFS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","Orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","SEP","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The groups themselves can be edited without changing a set of rules based on those groups.","groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The groups themselves can be edited without changing a set of rules based on those groups.","values","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The groups themselves can be edited without changing a set of rules based on those groups.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.","parameters","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.","Currency Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.","Currency Couple Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.","FX Time Period Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.","MM Time Period Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.","icons","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.","Configuration Group area","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.","name","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.","changes","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Create Group adds a new group.","Create Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Create Group adds a new group.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Rename Group changes the name of a group.","Rename Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Rename Group changes the name of a group.","name","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Rename Group changes the name of a group.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Rename Group cannot be used on the Default Group.","Rename Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Rename Group cannot be used on the Default Group.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Remove Group deletes an individual group.","Remove Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Remove Group deletes an individual group.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Remove Group cannot be used on the Default Group.","Remove Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Remove Group cannot be used on the Default Group.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"If the removed group is used in any configured rules this group is replaced by the Default Group.","removed group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"If the removed group is used in any configured rules this group is replaced by the Default Group.","configured rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"If the removed group is used in any configured rules this group is replaced by the Default Group.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Users are reminded to save changes to configurations.","users","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Users are reminded to save changes to configurations.","changes","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Users are reminded to save changes to configurations.","configurations","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups are particularly useful when configuring complex bank basket rules.","Configuration Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Configuration Groups are particularly useful when configuring complex bank basket rules.","complex bank basket rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"It is not required to configure groups based on the above parameters.","groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"It is not required to configure groups based on the above parameters.","parameters","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Users may still set individual custom rules without utilizing the Configuration Groups.","Users","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Users may still set individual custom rules without utilizing the Configuration Groups.","individual custom rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Users may still set individual custom rules without utilizing the Configuration Groups.","Configuration Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Individual custom rules may be preferable for some users with less complex bank basket setups.","Individual custom rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Individual custom rules may be preferable for some users with less complex bank basket setups.","users","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Individual custom rules may be preferable for some users with less complex bank basket setups.","bank basket setups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.","Bank Basket Configuration","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.","parameters","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group will include all existing values.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group will include all existing values.","existing values","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"All Default Groups can be modified.","Default Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"All Default Groups can be modified.","values","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.","tool","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.","values","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.","versions","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.","new currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.","360T platform","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.","Default Currency Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The new currency must be selected by the user.","new currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The new currency must be selected by the user.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Groups are intended to allow the classification of single currencies into customized groups.","Currency Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Groups are intended to allow the classification of single currencies into customized groups.","single currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Groups are intended to allow the classification of single currencies into customized groups.","customized groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This allows setting one single rule for each group of currencies rather than many rules for individual currencies.","rule","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This allows setting one single rule for each group of currencies rather than many rules for individual currencies.","group of currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This allows setting one single rule for each group of currencies rather than many rules for individual currencies.","individual currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currencies can be added or removed from the group without editing the rules themselves.","Currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currencies can be added or removed from the group without editing the rules themselves.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currencies can be added or removed from the group without editing the rules themselves.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a user interface for managing currency groups in a financial application.","image","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a user interface for managing currency groups in a financial application.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A currency is highlighted with a single-click.","The single arrow is activated.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"The single arrow is activated.","Clicking the single arrow moves the desired currency.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"A user clicks Create Group.","A user types the desired name.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"A user types the desired name.","A user clicks Create Group again.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"A user clicks Create Group again.","A user clicks Save.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"A user clicks on the Currency Group name.","The currencies configured for the group are viewed.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"A Currency Couple Group is created.","The Currency Couple Group can be used to simplify rules.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"Currencies may be added or removed from the default group.","Currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currencies may be added or removed from the default group.","default group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A currency is highlighted with a single-click.","A currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A currency is highlighted with a single-click.","single-click","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Highlighting a currency activates the single arrow.","Highlighting a currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Highlighting a currency activates the single arrow.","single arrow","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.","single arrow","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.","desired currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.","Available","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.","Selected","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"All currencies can be moved in either direction by using the double arrows.","All currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"All currencies can be moved in either direction by using the double arrows.","double arrows","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.","new group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.","Create Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.","desired name","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.","Save","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currencies configured for the group can be viewed by clicking on the Currency Group name.","currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currencies configured for the group can be viewed by clicking on the Currency Group name.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currencies configured for the group can be viewed by clicking on the Currency Group name.","Currency Group name","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The same currency can be added to many different groups.","currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The same currency can be added to many different groups.","groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The system does not restrict the creation of groups with overlapping sets of currencies.","system","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The system does not restrict the creation of groups with overlapping sets of currencies.","creation of groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The system does not restrict the creation of groups with overlapping sets of currencies.","overlapping sets of currencies","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.","Currency Couple Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.","buckets of currency pairs","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Currency Couple Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","FX products","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","RFS FX Bank Baskets area","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","FX Spot","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Forwards","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Swaps","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","NDF","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","NDS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Options","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Block Trades","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Energy Asian Swaps","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Bullet Swaps","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","RFS Commodity Bank Baskets area","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Order Spot","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Forwards","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","Orders Bank Basket area","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"RFS REQUESTER","DEAL TRACKING","manages","['control', 'directs', 'leads', 'administers', 'oversees']","[]","Relation"
"The Default Group contains all currency pairs.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all currency pairs.","currency pairs","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all currency pairs.","base currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all currency pairs.","quote currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups can be created.","Currency Couple Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups can be created.","Configuration Group icons","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups can be renamed.","Currency Couple Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups can be renamed.","Configuration Group icons","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups can be removed.","Currency Couple Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Currency Couple Groups can be removed.","Configuration Group icons","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A user can add currency pairs within a group.","user","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A user can add currency pairs within a group.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A user can add currency pairs within a group.","currency pairs","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Add Currency Couple button is clicked.","Add Currency Couple button","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The ISO code is chosen in the drop down list.","ISO code","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The ISO code is chosen in the drop down list.","drop down list","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The desired currency is typed.","desired currency","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection is confirmed by clicking the green check mark.","selection","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The selection is confirmed by clicking the green check mark.","green check mark","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Save is clicked.","Save","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products with varying maturities or tenors may be configured into maturity ranges.","Products","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products with varying maturities or tenors may be configured into maturity ranges.","maturities","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products with varying maturities or tenors may be configured into maturity ranges.","tenors","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products with varying maturities or tenors may be configured into maturity ranges.","maturity ranges","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products with varying maturities or tenors may be configured into maturity ranges.","FX Time Period Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An FX Time Period Group can simplify rules for FX products.","FX Time Period Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An FX Time Period Group can simplify rules for FX products.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An FX Time Period Group can simplify rules for FX products.","FX products","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An FX Time Period Group can simplify rules for FX products.","RFS FX Bank Baskets area","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An FX Time Period Group can simplify rules for FX products.","Forwards","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"An FX Time Period Group can simplify rules for FX products.","Swaps","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Add FX Time Period button is clicked.","The desired time period is chosen.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"The desired time period is chosen.","The selection is confirmed by clicking the green check mark.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"The selection is confirmed by clicking the green check mark.","The Save button is clicked.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"The Add MM Time Period button is clicked.","The desired time period is selected.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"The desired time period is selected.","The selection is confirmed by clicking the green check mark.","before","['prior', 'temporal', 'precedence', 'sequence', 'earlier']","[]","Relation"
"An MM Time Period Group is created.","Rules for interest rate products can be simplified.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"The same tenors are used in various groups.","They are used for different sets of rules.","because","['cause', 'explanation', 'reason', 'grounds']","[]","Relation"
"Tenors","range of maturities","defined as","['specify', 'characterize', 'explain', 'describe', 'identify']","[]","Relation"
"Tenors","various groups","used in","['purpose', 'employ', 'application', 'function', 'utility']","[]","Relation"
"Product Groups","product types","classify","['categorize', 'organize', 'sort', 'label', 'group']","[]","Relation"
"Product Groups","single rule","allow setting of","['specify', 'define', 'enable', 'permit', 'configure']","[]","Relation"
"Products","group","added or removed from","['modification', 'alteration', 'reconfigure', 'update', 'change', 'adjust']","[]","Relation"
"product group","rule creation","simplifies","['clarify', 'uncomplicate', 'ease', 'facilitate', 'streamline']","[]","Relation"
"rule creation","product types","for","['purpose', 'on behalf', 'intended for', 'due to', 'in return']","[]","Relation"
"default group","all products","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"default group","removed or renamed","cannot be","['prohibited', 'denied', 'excluded', 'impossible', 'unattainable']","[]","Relation"
"product types","group","altered in","['transformed', 'varied', 'modified', 'adjusted', 'change']","[]","Relation"
"Default Group","all product types","contains","['comprises', 'includes', 'encloses', 'has', 'holds']","[]","Relation"
"relevant products","RFS, Orders or SEP","apply for","['seek', 'solicit', 'submit', 'request']","[]","Relation"
"rule","default Product Group","utilizes","['leverage', 'use', 'employ', 'access', 'apply']","[]","Relation"
"Bank Basket areas","individual Provider Groups","allow creation of","['enable', 'support', 'make possible', 'authorize', 'facilitate']","[]","Relation"
"Bank Basket areas","providers","allow blocking of","['enable restriction', 'grant control', 'authorize stopping', 'regulate access', 'permit prevention']","[]","Relation"
"image","user interface","shows","['reveal', 'make known', 'indicate', 'display', 'exhibit', 'express', 'convey', 'point out', 'demonstrate']","[]","Relation"
"user interface","360T Bank Baskets","configures","['customize', 'define', 'prepare', 'set up', 'arrange']","[]","Relation"
"user interface","RFS Requester","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"user interface","Deal Tracking","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"user interface","Bridge Administration","includes","['comprise', 'encompass', 'contain', 'has', 'incorporate']","[]","Relation"
"interface","currency groups","manages","['control', 'directs', 'leads', 'administers', 'oversees']","[]","Relation"
"interface","FX time period groups","manages","['control', 'directs', 'leads', 'administers', 'oversees']","[]","Relation"
"interface","MM time period groups","manages","['control', 'directs', 'leads', 'administers', 'oversees']","[]","Relation"
"interface","product groups","manages","['control', 'directs', 'leads', 'administers', 'oversees']","[]","Relation"
"section","Select Member for 'FX Spot and Forward'","titled","['label', 'identify', 'name', 'designate', 'classify']","[]","Relation"
"section","available product types","has options for","['alternatives', 'provides', 'offers', 'choices', 'allows']","[]","Relation"
"section","selected product types","has options for","['alternatives', 'provides', 'offers', 'choices', 'allows']","[]","Relation"
"Figure 16","Bank Basket Product Groups","depicts","['represent', 'show', 'describe', 'portray', 'illustrate']","[]","Relation"
"Figure 17","Bank Basket Product Groups Create Group","depicts","['represent', 'show', 'describe', 'portray', 'illustrate']","[]","Relation"
"image","green button","shows","['reveal', 'make known', 'indicate', 'display', 'exhibit', 'express', 'convey', 'point out', 'demonstrate']","[]","Relation"
"green button","360T","displays text","['output', 'convey', 'show', 'render']","[]","Relation"
"User Guide","DEUTSCHE BÖRSE GROUP","from","['source', 'derivation', 'origin', 'separation', 'start']","[]","Relation"
"Product Groups are intended to allow the classification of product types into customized groups.","One single rule can be set for each group of products rather than many rules for individual product types.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"A new group is created by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.","A product group can be used to simplify rule creation for all relevant product types.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"A default group exists which includes all products.","The default group cannot be removed or renamed.","at the same time","['concurrent', 'coincident', 'synchronous', 'together', 'simultaneous']","[]","Relation"
"A default group exists which includes all products.","The product types in the default group can be altered.","at the same time","['concurrent', 'coincident', 'synchronous', 'together', 'simultaneous']","[]","Relation"
"The Default Group contains all product types across RFS, Orders and SEP.","Only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","at the same time","['concurrent', 'coincident', 'synchronous', 'together', 'simultaneous']","[]","Relation"
"Products can be added or removed from a product group without editing the rules themselves.","A product group can be used to simplify rule creation for all relevant product types.","at the same time","['concurrent', 'coincident', 'synchronous', 'together', 'simultaneous']","[]","Relation"
"Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.","periods","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.","OVERNIGHT","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.","1 WEEK","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.","1 MONTH","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.","6 MONTHS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Tenors are defined as a range of maturities, with both start and end values included.","Tenors","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Tenors are defined as a range of maturities, with both start and end values included.","range of maturities","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Tenors are defined as a range of maturities, with both start and end values included.","start values","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Tenors are defined as a range of maturities, with both start and end values included.","end values","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The same tenors may be used in various groups in order to be used for different sets of rules.","tenors","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The same tenors may be used in various groups in order to be used for different sets of rules.","groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The same tenors may be used in various groups in order to be used for different sets of rules.","sets of rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Product Groups are intended to allow the classification of product types into customized groups.","Product Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Product Groups are intended to allow the classification of product types into customized groups.","product types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Product Groups are intended to allow the classification of product types into customized groups.","customized groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This allows setting one single rule for each group of products rather than many rules for individual product types.","one single rule","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This allows setting one single rule for each group of products rather than many rules for individual product types.","group of products","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This allows setting one single rule for each group of products rather than many rules for individual product types.","many rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This allows setting one single rule for each group of products rather than many rules for individual product types.","individual product types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a user interface for configuring 360T Bank Baskets.","image","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a user interface for configuring 360T Bank Baskets.","user interface","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a user interface for configuring 360T Bank Baskets.","360T Bank Baskets","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.","user interface","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.","RFS Requester","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.","Deal Tracking","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.","Bridge Administration","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.","interface","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.","users","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.","currency groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.","FX time period groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.","MM time period groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.","product groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.","section","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.","Select Member for 'FX Spot and Forward'","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.","options","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.","available product types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.","selected product types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products can be added or removed from the group without editing the rules themselves.","Products","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products can be added or removed from the group without editing the rules themselves.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Products can be added or removed from the group without editing the rules themselves.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.","product group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.","rule creation","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.","product types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.","RFS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.","Orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.","SEP Bank Basket areas","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.","new group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.","Create Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.","desired name","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.","Save","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A default group exists which includes all products.","default group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A default group exists which includes all products.","products","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"This group cannot be removed or renamed.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, the product types in the group can be altered.","product types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, the product types in the group can be altered.","group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all product types across RFS, Orders and SEP.","Default Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all product types across RFS, Orders and SEP.","product types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all product types across RFS, Orders and SEP.","RFS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all product types across RFS, Orders and SEP.","Orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Default Group contains all product types across RFS, Orders and SEP.","SEP","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","relevant products","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","RFS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","Orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","SEP","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","rule","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","default Product Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.","Bank Basket areas","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.","RFS","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.","Orders","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.","SEP","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.","Provider Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.","providers","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a green button with the text ""360T"" on it.","image","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a green button with the text ""360T"" on it.","green button","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The image shows a green button with the text ""360T"" on it.","text ""360T""","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The button has a white outline and a white background.","button","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The button has a white outline and a white background.","white outline","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The button has a white outline and a white background.","white background","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The text is in a stylized font.","text","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The text is in a stylized font.","stylized font","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Providers are temporarily blocked from particular request types.","A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.","as a result","['outcome', 'causation', 'product', 'consequence', 'effect']","[]","Relation"
"A Provider is removed completely and its relationship is rejected using the Counterpart Relationship Management tool.","Please refer to the relevant user guide.","because","['cause', 'explanation', 'reason', 'grounds']","[]","Relation"
"The Provider Groups can be edited.","Provider Groups","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Provider Groups can be edited.","values","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The Provider Groups can be edited.","rules","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Providers may be temporarily blocked from particular request types.","Providers","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Providers may be temporarily blocked from particular request types.","request types","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Providers may be temporarily blocked from particular request types.","Provider Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Blocked Provider will remain in a Provider Group.","Blocked Provider","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Blocked Provider will remain in a Provider Group.","Provider Group","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Blocked Provider will appear with a blocked symbol.","Blocked Provider","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Blocked Provider will appear with a blocked symbol.","blocked symbol","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"A Provider should be removed completely.","Provider","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The relationship should be rejected using the Counterpart Relationship Management tool.","relationship","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"The relationship should be rejected using the Counterpart Relationship Management tool.","Counterpart Relationship Management tool","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
"Refer to the relevant user guide.","user guide","is participated by","['focus', 'event', 'target', 'activity', 'subject']","[]","Relation"
