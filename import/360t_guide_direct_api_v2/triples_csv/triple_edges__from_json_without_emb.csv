:START_ID,:END_ID,relation,concepts,synsets,:TYPE
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.",image,is participated by,[],[],Relation
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.",logo,is participated by,[],[],Relation
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.",360T,is participated by,[],[],Relation
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.",green rectangle,is participated by,[],[],Relation
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.",rounded corners,is participated by,[],[],Relation
"The text ""360"" is in white with a green outline.",text,is participated by,[],[],Relation
"The text ""360"" is in white with a green outline.",360,is participated by,[],[],Relation
"The text ""360"" is in white with a green outline.",white,is participated by,[],[],Relation
"The text ""360"" is in white with a green outline.",green outline,is participated by,[],[],Relation
"The ""T"" is also in white with a green outline.",T,is participated by,[],[],Relation
"The ""T"" is also in white with a green outline.",white,is participated by,[],[],Relation
"The ""T"" is also in white with a green outline.",green outline,is participated by,[],[],Relation
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".",two arrows,is participated by,[],[],Relation
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".",0,is participated by,[],[],Relation
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".",360,is participated by,[],[],Relation
THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.,THIS FILE,is participated by,[],[],Relation
THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.,PROPRIETARY AND CONFIDENTIAL INFORMATION,is participated by,[],[],Relation
THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.,TRADE SECRETS,is participated by,[],[],Relation
THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.,THIS FILE,is participated by,[],[],Relation
THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.,THIRD PARTY,is participated by,[],[],Relation
THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.,PRIOR WRITTEN APPROVAL,is participated by,[],[],Relation
THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.,360 TREASURY SYSTEMS AG,is participated by,[],[],Relation
user manual,Bank Basket feature,describes,[],[],Relation
Bank Basket feature,360T Bridge Administration tool,is part of,[],[],Relation
Bank Basket feature,improved rule management capabilities,enhanced to provide,[],[],Relation
improved rule management capabilities,configuration groups,include,[],[],Relation
configuration groups,currency,based on,[],[],Relation
configuration groups,currency couple,based on,[],[],Relation
configuration groups,time period,based on,[],[],Relation
configuration groups,product(s),based on,[],[],Relation
improved rule management capabilities,configuration of separate baskets,include,[],[],Relation
separate baskets,"request type (RFS, Order, SEP)",configured by,[],[],Relation
improved rule management capabilities,ability to apply and remove temporary blocks,include,[],[],Relation
temporary blocks,configured rules,do not affect,[],[],Relation
temporary blocks,counterpart relationship(s),do not affect,[],[],Relation
360T enhanced Bank Basket feature,entities,available to,[],[],Relation
entities,EMS application,have,[],[],Relation
entities,Bridge application,have,[],[],Relation
users,corresponding user rights,have,[],[],Relation
users,company's Bank Baskets,administer,[],[],Relation
<EMAIL>,relevant administrative rights,can set up,[],[],Relation
customer relationship manager,relevant administrative rights,can set up,[],[],Relation
Bank Basket configuration,Bridge Administration tool,found within,[],[],Relation
Bridge Administration,"menu option ""Administration""",accessed via,[],[],Relation
"menu option ""Administration""",screen header,located in,[],[],Relation
screen header,Bridge application,is part of,[],[],Relation
Bridge Administration feature,homepage,opens to,[],[],Relation
homepage,available shortcuts,has,[],[],Relation
available shortcuts,different configuration tools,lead to,[],[],Relation
different configuration tools,particular user,are for,[],[],Relation
quick navigation toolbar,active homepage icon,shows,[],[],Relation
quick navigation toolbar,left side of the homepage,available on,[],[],Relation
image,screenshot,shows,[],[],Relation
screenshot,360T Bank Baskets Configuration user guide,is of,[],[],Relation
screen,'Administration Start' page,displays,[],[],Relation
'Administration Start' page,'Regulatory Data',has option for,[],[],Relation
'Administration Start' page,'Bank Baskets',has option for,[],[],Relation
'Administration Start' page,'Change Request',has option for,[],[],Relation
'Administration Start' page,'Wizards',has option for,[],[],Relation
'Administration Start' page,'Evaluator Tools',has option for,[],[],Relation
top of the screen,tabs,includes,[],[],Relation
tabs,'RFS REQUESTER',include,[],[],Relation
tabs,'DEAL TRACKING',include,[],[],Relation
tabs,'BRIDGE ADMINISTRATION',include,[],[],Relation
top of the screen,preferences options,includes,[],[],Relation
top of the screen,help options,includes,[],[],Relation
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,user manual,is participated by,[],[],Relation
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,Bank Basket feature,is participated by,[],[],Relation
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,360T Bridge Administration tool,is participated by,[],[],Relation
The Bank Basket feature has been enhanced.,Bank Basket feature,is participated by,[],[],Relation
The enhancement provides improved rule management capabilities.,enhancement,is participated by,[],[],Relation
The enhancement provides improved rule management capabilities.,rule management capabilities,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",configuration groups,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",currency,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",currency couple,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",time period,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",product(s),is participated by,[],[],Relation
Separate baskets can be configured by request type.,separate baskets,is participated by,[],[],Relation
Separate baskets can be configured by request type.,request type,is participated by,[],[],Relation
Request types include RFS.,RFS,is participated by,[],[],Relation
Request types include RFS.,request types,is participated by,[],[],Relation
Request types include Order.,Order,is participated by,[],[],Relation
Request types include Order.,request types,is participated by,[],[],Relation
Request types include SEP.,SEP,is participated by,[],[],Relation
Request types include SEP.,request types,is participated by,[],[],Relation
Temporary blocks can be applied.,temporary blocks,is participated by,[],[],Relation
Temporary blocks can be removed.,temporary blocks,is participated by,[],[],Relation
Applying temporary blocks does not affect the configured rules.,temporary blocks,is participated by,[],[],Relation
Applying temporary blocks does not affect the configured rules.,configured rules,is participated by,[],[],Relation
Figure 2,Bridge Administration Homepage,depicts,[],[],Relation
"""Bank Baskets"" quick link",navigation panel,opens,[],[],Relation
navigation panel,institution tree,contains,[],[],Relation
institution tree,single TEX entity,includes,[],[],Relation
institution tree,TEX main entity,includes,[],[],Relation
trade-as entities,TEX main entity,configured under,[],[],Relation
trade-on-behalf entities,TEX main entity,configured under,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",Bank Baskets quick link,is participated by,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",navigation panel,is participated by,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",institution tree,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",tree,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",TEX entity,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",TEX main entity,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",trade-as,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",trade-on-behalf,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",ITEX entities,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",main entity,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,institution,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,single-click,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,institution tree,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,new form/sheet,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,Bank Basket configuration details,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,entity,is participated by,[],[],Relation
It is possible to open multiple forms/sheets at a time.,multiple forms/sheets,is participated by,[],[],Relation
The selected item will be highlighted as an active task inside the taskbar.,selected item,is participated by,[],[],Relation
The selected item will be highlighted as an active task inside the taskbar.,active task,is participated by,[],[],Relation
The selected item will be highlighted as an active task inside the taskbar.,taskbar,is participated by,[],[],Relation
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,set of icons,is participated by,[],[],Relation
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,single-click,is participated by,[],[],Relation
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,navigation panel,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,search field,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,user,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,alphanumeric value,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,institution,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,This feature,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,user,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,active task/sheet,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,navigation panel,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,selected tree item,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,active institution,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,taskbar,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,scroll from source,is participated by,[],[],Relation
This icon is deactivated when using the Bank Basket configuration.,This icon,is participated by,[],[],Relation
This icon is deactivated when using the Bank Basket configuration.,Bank Basket configuration,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,other configuration tools,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,toggle option,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,user,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,individuals,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,navigation panel,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Individual unsaved changes can be reverted by clicking on the arrow icon.,because,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",because,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).",before,[],[],Relation
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).",Configuration Groups allow users to create a group one single time and reuse it across various rules.,as a result,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,toggle option,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,user,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,institutions,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,navigation panel,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,configuration tools,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,navigation panel,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,minimize icon,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,panel,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,entity tab,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Live Audit Log,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,unsaved changes,is participated by,[],[],Relation
Individual unsaved changes can be reverted by clicking on the arrow icon.,unsaved changes,is participated by,[],[],Relation
Individual unsaved changes can be reverted by clicking on the arrow icon.,arrow icon,is participated by,[],[],Relation
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.","""Discard all changes"" button",is participated by,[],[],Relation
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",unsaved changes,is participated by,[],[],Relation
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,image,is participated by,[],[],Relation
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,text 'User Guide 360T Bank Baskets Configuration',is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,entity,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,institution tree,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,Configuration Groups tab,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,data tabs,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,parameters,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Bank Basket configuration,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,RFS,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Order,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,SEP,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,users,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,group,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,rules,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,request types,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,this tab,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS FX Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS MM Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS Commodity Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS Cross Currency Netting Bank Baskets,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Provider Groups,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Blocked Providers,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Orders,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Order Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Forward orders,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Spot orders,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,this tab,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Provider Groups,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Blocked Providers,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Supersonic (SEP),is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,SEP Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,SEP streaming spot executions,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,this tab,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",Bank Basket Configurations,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",RFS requests,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",Orders requests,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",SEP requests,is participated by,[],[],Relation
An RFS configuration will have no impact on SEP trading.,RFS configuration,is participated by,[],[],Relation
An RFS configuration will have no impact on SEP trading.,SEP trading,is participated by,[],[],Relation
Configuration Groups,centralized management,facilitate,[],[],Relation
parameters,Bank Basket configuration,used in,[],[],Relation
Groups,each parameter,configured in,[],[],Relation
Groups,creating rules,reused for,[],[],Relation
groups,set of rules,edited without changing,[],[],Relation
available parameters,Currency Groups,include,[],[],Relation
available parameters,Currency Couple Groups,include,[],[],Relation
available parameters,FX Time Period Groups,include,[],[],Relation
available parameters,MM Time Period Groups,include,[],[],Relation
icons,create new group,allow user to,[],[],Relation
icons,edit name of existing group,allow user to,[],[],Relation
icons,delete group,allow user to,[],[],Relation
icons,save changes,allow user to,[],[],Relation
Rename Group,Default Group,cannot be used on,[],[],Relation
Remove Group,Default Group,cannot be used on,[],[],Relation
removed group,Default Group,replaced by,[],[],Relation
Configuration Groups,configuring complex bank basket rules,useful for,[],[],Relation
Users,individual custom rules,set,[],[],Relation
Individual custom rules,users with less complex bank basket setups,preferable for,[],[],Relation
Bank Basket Configuration,Default Group,contains,[],[],Relation
Default Group,existing values,includes,[],[],Relation
Default Groups,values,modified by removing,[],[],Relation
new values,Default Group,not added to,[],[],Relation
new currency,Default Currency Group,not included in,[],[],Relation
new currency,user,selected by,[],[],Relation
Currency Groups,single currencies,allow classification of,[],[],Relation
Currency Groups,single rule for group of currencies,allow setting,[],[],Relation
Currencies,group,added or removed from,[],[],Relation
currency group,rule creation for interest rate products,simplifies,[],[],Relation
interest rate products,Loan,include,[],[],Relation
interest rate products,Deposit,include,[],[],Relation
interest rate products,Interest Rate Swap,include,[],[],Relation
interest rate products,FRA,include,[],[],Relation
interest rate products,CapFloor,include,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,centralized management,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,parameters,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,Bank Basket configuration,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Groups,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",parameters,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",rules,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",requests,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",RFS,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Orders,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",SEP,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,groups,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,values,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,rules,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",parameters,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Currency Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Currency Couple Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",FX Time Period Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",MM Time Period Groups,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",icons,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",Configuration Group area,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",user,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",group,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",name,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",changes,is participated by,[],[],Relation
Create Group adds a new group.,Create Group,is participated by,[],[],Relation
Create Group adds a new group.,group,is participated by,[],[],Relation
Rename Group changes the name of a group.,Rename Group,is participated by,[],[],Relation
Rename Group changes the name of a group.,name,is participated by,[],[],Relation
Rename Group changes the name of a group.,group,is participated by,[],[],Relation
Rename Group cannot be used on the Default Group.,Rename Group,is participated by,[],[],Relation
Rename Group cannot be used on the Default Group.,Default Group,is participated by,[],[],Relation
Remove Group deletes an individual group.,Remove Group,is participated by,[],[],Relation
Remove Group deletes an individual group.,group,is participated by,[],[],Relation
Remove Group cannot be used on the Default Group.,Remove Group,is participated by,[],[],Relation
Remove Group cannot be used on the Default Group.,Default Group,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,removed group,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,configured rules,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,Default Group,is participated by,[],[],Relation
Users are reminded to save changes to configurations.,users,is participated by,[],[],Relation
Users are reminded to save changes to configurations.,changes,is participated by,[],[],Relation
Users are reminded to save changes to configurations.,configurations,is participated by,[],[],Relation
Configuration Groups are particularly useful when configuring complex bank basket rules.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups are particularly useful when configuring complex bank basket rules.,complex bank basket rules,is participated by,[],[],Relation
It is not required to configure groups based on the above parameters.,groups,is participated by,[],[],Relation
It is not required to configure groups based on the above parameters.,parameters,is participated by,[],[],Relation
Users may still set individual custom rules without utilizing the Configuration Groups.,Users,is participated by,[],[],Relation
Users may still set individual custom rules without utilizing the Configuration Groups.,individual custom rules,is participated by,[],[],Relation
Users may still set individual custom rules without utilizing the Configuration Groups.,Configuration Groups,is participated by,[],[],Relation
Individual custom rules may be preferable for some users with less complex bank basket setups.,Individual custom rules,is participated by,[],[],Relation
Individual custom rules may be preferable for some users with less complex bank basket setups.,users,is participated by,[],[],Relation
Individual custom rules may be preferable for some users with less complex bank basket setups.,bank basket setups,is participated by,[],[],Relation
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",Bank Basket Configuration,is participated by,[],[],Relation
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",parameters,is participated by,[],[],Relation
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",Default Group,is participated by,[],[],Relation
The Default Group will include all existing values.,Default Group,is participated by,[],[],Relation
The Default Group will include all existing values.,existing values,is participated by,[],[],Relation
All Default Groups can be modified.,Default Groups,is participated by,[],[],Relation
All Default Groups can be modified.,values,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",tool,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",values,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",versions,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",Default Group,is participated by,[],[],Relation
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,new currency,is participated by,[],[],Relation
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,360T platform,is participated by,[],[],Relation
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,Default Currency Group,is participated by,[],[],Relation
The new currency must be selected by the user.,new currency,is participated by,[],[],Relation
The new currency must be selected by the user.,user,is participated by,[],[],Relation
Currency Groups are intended to allow the classification of single currencies into customized groups.,Currency Groups,is participated by,[],[],Relation
Currency Groups are intended to allow the classification of single currencies into customized groups.,single currencies,is participated by,[],[],Relation
Currency Groups are intended to allow the classification of single currencies into customized groups.,customized groups,is participated by,[],[],Relation
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,rule,is participated by,[],[],Relation
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,group of currencies,is participated by,[],[],Relation
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,individual currencies,is participated by,[],[],Relation
Currencies can be added or removed from the group without editing the rules themselves.,Currencies,is participated by,[],[],Relation
Currencies can be added or removed from the group without editing the rules themselves.,group,is participated by,[],[],Relation
Currencies can be added or removed from the group without editing the rules themselves.,rules,is participated by,[],[],Relation
The image shows a user interface for managing currency groups in a financial application.,image,is participated by,[],[],Relation
The image shows a user interface for managing currency groups in a financial application.,user,is participated by,[],[],Relation
A currency is highlighted with a single-click.,The single arrow is activated.,as a result,[],[],Relation
The single arrow is activated.,Clicking the single arrow moves the desired currency.,before,[],[],Relation
A user clicks Create Group.,A user types the desired name.,before,[],[],Relation
A user types the desired name.,A user clicks Create Group again.,before,[],[],Relation
A user clicks Create Group again.,A user clicks Save.,before,[],[],Relation
A user clicks on the Currency Group name.,The currencies configured for the group are viewed.,as a result,[],[],Relation
A Currency Couple Group is created.,The Currency Couple Group can be used to simplify rules.,as a result,[],[],Relation
Currencies may be added or removed from the default group.,Currencies,is participated by,[],[],Relation
Currencies may be added or removed from the default group.,default group,is participated by,[],[],Relation
A currency is highlighted with a single-click.,A currency,is participated by,[],[],Relation
A currency is highlighted with a single-click.,single-click,is participated by,[],[],Relation
Highlighting a currency activates the single arrow.,Highlighting a currency,is participated by,[],[],Relation
Highlighting a currency activates the single arrow.,single arrow,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,single arrow,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,desired currency,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Available,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Selected,is participated by,[],[],Relation
All currencies can be moved in either direction by using the double arrows.,All currencies,is participated by,[],[],Relation
All currencies can be moved in either direction by using the double arrows.,double arrows,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",new group,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Create Group,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",desired name,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Save,is participated by,[],[],Relation
Currencies configured for the group can be viewed by clicking on the Currency Group name.,currencies,is participated by,[],[],Relation
Currencies configured for the group can be viewed by clicking on the Currency Group name.,group,is participated by,[],[],Relation
Currencies configured for the group can be viewed by clicking on the Currency Group name.,Currency Group name,is participated by,[],[],Relation
The same currency can be added to many different groups.,currency,is participated by,[],[],Relation
The same currency can be added to many different groups.,groups,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,system,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,creation of groups,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,overlapping sets of currencies,is participated by,[],[],Relation
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",Currency Couple Groups,is participated by,[],[],Relation
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",buckets of currency pairs,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Currency Couple Group,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",rules,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",FX products,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",RFS FX Bank Baskets area,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",FX Spot,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Forwards,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Swaps,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",NDF,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",NDS,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Options,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Block Trades,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Energy Asian Swaps,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Bullet Swaps,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",RFS Commodity Bank Baskets area,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Order Spot,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Forwards,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Orders Bank Basket area,is participated by,[],[],Relation
RFS REQUESTER,DEAL TRACKING,manages,[],[],Relation
The Default Group contains all currency pairs.,Default Group,is participated by,[],[],Relation
The Default Group contains all currency pairs.,currency pairs,is participated by,[],[],Relation
The Default Group contains all currency pairs.,base currency,is participated by,[],[],Relation
The Default Group contains all currency pairs.,quote currency,is participated by,[],[],Relation
Currency Couple Groups can be created.,Currency Couple Groups,is participated by,[],[],Relation
Currency Couple Groups can be created.,Configuration Group icons,is participated by,[],[],Relation
Currency Couple Groups can be renamed.,Currency Couple Groups,is participated by,[],[],Relation
Currency Couple Groups can be renamed.,Configuration Group icons,is participated by,[],[],Relation
Currency Couple Groups can be removed.,Currency Couple Groups,is participated by,[],[],Relation
Currency Couple Groups can be removed.,Configuration Group icons,is participated by,[],[],Relation
A user can add currency pairs within a group.,user,is participated by,[],[],Relation
A user can add currency pairs within a group.,group,is participated by,[],[],Relation
A user can add currency pairs within a group.,currency pairs,is participated by,[],[],Relation
The Add Currency Couple button is clicked.,Add Currency Couple button,is participated by,[],[],Relation
The ISO code is chosen in the drop down list.,ISO code,is participated by,[],[],Relation
The ISO code is chosen in the drop down list.,drop down list,is participated by,[],[],Relation
The desired currency is typed.,desired currency,is participated by,[],[],Relation
The selection is confirmed by clicking the green check mark.,selection,is participated by,[],[],Relation
The selection is confirmed by clicking the green check mark.,green check mark,is participated by,[],[],Relation
Save is clicked.,Save,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,Products,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,maturities,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,tenors,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,maturity ranges,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,FX Time Period Groups,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,FX Time Period Group,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,rules,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,FX products,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,RFS FX Bank Baskets area,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,Forwards,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,Swaps,is participated by,[],[],Relation
The Add FX Time Period button is clicked.,The desired time period is chosen.,before,[],[],Relation
The desired time period is chosen.,The selection is confirmed by clicking the green check mark.,before,[],[],Relation
The selection is confirmed by clicking the green check mark.,The Save button is clicked.,before,[],[],Relation
The Add MM Time Period button is clicked.,The desired time period is selected.,before,[],[],Relation
The desired time period is selected.,The selection is confirmed by clicking the green check mark.,before,[],[],Relation
An MM Time Period Group is created.,Rules for interest rate products can be simplified.,as a result,[],[],Relation
The same tenors are used in various groups.,They are used for different sets of rules.,because,[],[],Relation
Tenors,range of maturities,defined as,[],[],Relation
Tenors,various groups,used in,[],[],Relation
Product Groups,product types,classify,[],[],Relation
Product Groups,single rule,allow setting of,[],[],Relation
Products,group,added or removed from,[],[],Relation
product group,rule creation,simplifies,[],[],Relation
rule creation,product types,for,[],[],Relation
default group,all products,includes,[],[],Relation
default group,removed or renamed,cannot be,[],[],Relation
product types,group,altered in,[],[],Relation
Default Group,all product types,contains,[],[],Relation
relevant products,"RFS, Orders or SEP",apply for,[],[],Relation
rule,default Product Group,utilizes,[],[],Relation
Bank Basket areas,individual Provider Groups,allow creation of,[],[],Relation
Bank Basket areas,providers,allow blocking of,[],[],Relation
image,user interface,shows,[],[],Relation
user interface,360T Bank Baskets,configures,[],[],Relation
user interface,RFS Requester,includes,[],[],Relation
user interface,Deal Tracking,includes,[],[],Relation
user interface,Bridge Administration,includes,[],[],Relation
interface,currency groups,manages,[],[],Relation
interface,FX time period groups,manages,[],[],Relation
interface,MM time period groups,manages,[],[],Relation
interface,product groups,manages,[],[],Relation
section,Select Member for 'FX Spot and Forward',titled,[],[],Relation
section,available product types,has options for,[],[],Relation
section,selected product types,has options for,[],[],Relation
Figure 16,Bank Basket Product Groups,depicts,[],[],Relation
Figure 17,Bank Basket Product Groups Create Group,depicts,[],[],Relation
image,green button,shows,[],[],Relation
green button,360T,displays text,[],[],Relation
User Guide,DEUTSCHE BÖRSE GROUP,from,[],[],Relation
Product Groups are intended to allow the classification of product types into customized groups.,One single rule can be set for each group of products rather than many rules for individual product types.,as a result,[],[],Relation
"A new group is created by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",A product group can be used to simplify rule creation for all relevant product types.,as a result,[],[],Relation
A default group exists which includes all products.,The default group cannot be removed or renamed.,at the same time,[],[],Relation
A default group exists which includes all products.,The product types in the default group can be altered.,at the same time,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.","Only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",at the same time,[],[],Relation
Products can be added or removed from a product group without editing the rules themselves.,A product group can be used to simplify rule creation for all relevant product types.,at the same time,[],[],Relation
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,periods,is participated by,[],[],Relation
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,OVERNIGHT,is participated by,[],[],Relation
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,1 WEEK,is participated by,[],[],Relation
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,1 MONTH,is participated by,[],[],Relation
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,6 MONTHS,is participated by,[],[],Relation
"Tenors are defined as a range of maturities, with both start and end values included.",Tenors,is participated by,[],[],Relation
"Tenors are defined as a range of maturities, with both start and end values included.",range of maturities,is participated by,[],[],Relation
"Tenors are defined as a range of maturities, with both start and end values included.",start values,is participated by,[],[],Relation
"Tenors are defined as a range of maturities, with both start and end values included.",end values,is participated by,[],[],Relation
The same tenors may be used in various groups in order to be used for different sets of rules.,tenors,is participated by,[],[],Relation
The same tenors may be used in various groups in order to be used for different sets of rules.,groups,is participated by,[],[],Relation
The same tenors may be used in various groups in order to be used for different sets of rules.,sets of rules,is participated by,[],[],Relation
Product Groups are intended to allow the classification of product types into customized groups.,Product Groups,is participated by,[],[],Relation
Product Groups are intended to allow the classification of product types into customized groups.,product types,is participated by,[],[],Relation
Product Groups are intended to allow the classification of product types into customized groups.,customized groups,is participated by,[],[],Relation
This allows setting one single rule for each group of products rather than many rules for individual product types.,one single rule,is participated by,[],[],Relation
This allows setting one single rule for each group of products rather than many rules for individual product types.,group of products,is participated by,[],[],Relation
This allows setting one single rule for each group of products rather than many rules for individual product types.,many rules,is participated by,[],[],Relation
This allows setting one single rule for each group of products rather than many rules for individual product types.,individual product types,is participated by,[],[],Relation
The image shows a user interface for configuring 360T Bank Baskets.,image,is participated by,[],[],Relation
The image shows a user interface for configuring 360T Bank Baskets.,user interface,is participated by,[],[],Relation
The image shows a user interface for configuring 360T Bank Baskets.,360T Bank Baskets,is participated by,[],[],Relation
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.",user interface,is participated by,[],[],Relation
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.",RFS Requester,is participated by,[],[],Relation
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.",Deal Tracking,is participated by,[],[],Relation
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.",Bridge Administration,is participated by,[],[],Relation
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",interface,is participated by,[],[],Relation
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",users,is participated by,[],[],Relation
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",currency groups,is participated by,[],[],Relation
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",FX time period groups,is participated by,[],[],Relation
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",MM time period groups,is participated by,[],[],Relation
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",product groups,is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.",section,is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.",Select Member for 'FX Spot and Forward',is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.",options,is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.",available product types,is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.",selected product types,is participated by,[],[],Relation
Products can be added or removed from the group without editing the rules themselves.,Products,is participated by,[],[],Relation
Products can be added or removed from the group without editing the rules themselves.,group,is participated by,[],[],Relation
Products can be added or removed from the group without editing the rules themselves.,rules,is participated by,[],[],Relation
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",product group,is participated by,[],[],Relation
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",rule creation,is participated by,[],[],Relation
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",product types,is participated by,[],[],Relation
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",RFS,is participated by,[],[],Relation
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",Orders,is participated by,[],[],Relation
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",SEP Bank Basket areas,is participated by,[],[],Relation
To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.,new group,is participated by,[],[],Relation
To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.,Create Group,is participated by,[],[],Relation
To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.,desired name,is participated by,[],[],Relation
To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.,Save,is participated by,[],[],Relation
A default group exists which includes all products.,default group,is participated by,[],[],Relation
A default group exists which includes all products.,products,is participated by,[],[],Relation
This group cannot be removed or renamed.,group,is participated by,[],[],Relation
"However, the product types in the group can be altered.",product types,is participated by,[],[],Relation
"However, the product types in the group can be altered.",group,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",Default Group,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",product types,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",RFS,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",Orders,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",SEP,is participated by,[],[],Relation
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",relevant products,is participated by,[],[],Relation
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",RFS,is participated by,[],[],Relation
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",Orders,is participated by,[],[],Relation
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",SEP,is participated by,[],[],Relation
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",rule,is participated by,[],[],Relation
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",default Product Group,is participated by,[],[],Relation
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",Bank Basket areas,is participated by,[],[],Relation
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",RFS,is participated by,[],[],Relation
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",Orders,is participated by,[],[],Relation
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",SEP,is participated by,[],[],Relation
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",Provider Groups,is participated by,[],[],Relation
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",providers,is participated by,[],[],Relation
"The image shows a green button with the text ""360T"" on it.",image,is participated by,[],[],Relation
"The image shows a green button with the text ""360T"" on it.",green button,is participated by,[],[],Relation
"The image shows a green button with the text ""360T"" on it.","text ""360T""",is participated by,[],[],Relation
The button has a white outline and a white background.,button,is participated by,[],[],Relation
The button has a white outline and a white background.,white outline,is participated by,[],[],Relation
The button has a white outline and a white background.,white background,is participated by,[],[],Relation
The text is in a stylized font.,text,is participated by,[],[],Relation
The text is in a stylized font.,stylized font,is participated by,[],[],Relation
Providers are temporarily blocked from particular request types.,"A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.",as a result,[],[],Relation
A Provider is removed completely and its relationship is rejected using the Counterpart Relationship Management tool.,Please refer to the relevant user guide.,because,[],[],Relation
The Provider Groups can be edited.,Provider Groups,is participated by,[],[],Relation
The Provider Groups can be edited.,values,is participated by,[],[],Relation
The Provider Groups can be edited.,rules,is participated by,[],[],Relation
Providers may be temporarily blocked from particular request types.,Providers,is participated by,[],[],Relation
Providers may be temporarily blocked from particular request types.,request types,is participated by,[],[],Relation
Providers may be temporarily blocked from particular request types.,Provider Group,is participated by,[],[],Relation
A Blocked Provider will remain in a Provider Group.,Blocked Provider,is participated by,[],[],Relation
A Blocked Provider will remain in a Provider Group.,Provider Group,is participated by,[],[],Relation
A Blocked Provider will appear with a blocked symbol.,Blocked Provider,is participated by,[],[],Relation
A Blocked Provider will appear with a blocked symbol.,blocked symbol,is participated by,[],[],Relation
A Provider should be removed completely.,Provider,is participated by,[],[],Relation
The relationship should be rejected using the Counterpart Relationship Management tool.,relationship,is participated by,[],[],Relation
The relationship should be rejected using the Counterpart Relationship Management tool.,Counterpart Relationship Management tool,is participated by,[],[],Relation
Refer to the relevant user guide.,user guide,is participated by,[],[],Relation
