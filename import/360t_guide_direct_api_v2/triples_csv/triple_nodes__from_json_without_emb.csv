name:ID,type,concepts,synsets,:LABEL
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.",event,[],[],Node
image,entity,[],[],Node
logo,entity,[],[],Node
360T,entity,[],[],Node
green rectangle,entity,[],[],Node
rounded corners,entity,[],[],Node
"The text ""360"" is in white with a green outline.",event,[],[],Node
text,entity,[],[],Node
360,entity,[],[],Node
white,entity,[],[],Node
green outline,entity,[],[],Node
"The ""T"" is also in white with a green outline.",event,[],[],Node
T,entity,[],[],Node
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".",event,[],[],Node
two arrows,entity,[],[],Node
0,entity,[],[],Node
THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.,event,[],[],Node
THIS FILE,entity,[],[],Node
PROPRIETARY AND CONFIDENTIAL INFORMATION,entity,[],[],Node
TRADE SECRETS,entity,[],[],Node
THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.,event,[],[],Node
THIRD PARTY,entity,[],[],Node
PRIOR WRITTEN APPROVAL,entity,[],[],Node
360 TREASURY SYSTEMS AG,entity,[],[],Node
user manual,entity,[],[],Node
Bank Basket feature,entity,[],[],Node
360T Bridge Administration tool,entity,[],[],Node
improved rule management capabilities,entity,[],[],Node
configuration groups,entity,[],[],Node
currency,entity,[],[],Node
currency couple,entity,[],[],Node
time period,entity,[],[],Node
product(s),entity,[],[],Node
configuration of separate baskets,entity,[],[],Node
separate baskets,entity,[],[],Node
"request type (RFS, Order, SEP)",entity,[],[],Node
ability to apply and remove temporary blocks,entity,[],[],Node
temporary blocks,entity,[],[],Node
configured rules,entity,[],[],Node
counterpart relationship(s),entity,[],[],Node
360T enhanced Bank Basket feature,entity,[],[],Node
entities,entity,[],[],Node
EMS application,entity,[],[],Node
Bridge application,entity,[],[],Node
users,entity,[],[],Node
corresponding user rights,entity,[],[],Node
company's Bank Baskets,entity,[],[],Node
<EMAIL>,entity,[],[],Node
relevant administrative rights,entity,[],[],Node
customer relationship manager,entity,[],[],Node
Bank Basket configuration,entity,[],[],Node
Bridge Administration tool,entity,[],[],Node
Bridge Administration,entity,[],[],Node
"menu option ""Administration""",entity,[],[],Node
screen header,entity,[],[],Node
Bridge Administration feature,entity,[],[],Node
homepage,entity,[],[],Node
available shortcuts,entity,[],[],Node
different configuration tools,entity,[],[],Node
particular user,entity,[],[],Node
quick navigation toolbar,entity,[],[],Node
active homepage icon,entity,[],[],Node
left side of the homepage,entity,[],[],Node
screenshot,entity,[],[],Node
360T Bank Baskets Configuration user guide,entity,[],[],Node
screen,entity,[],[],Node
'Administration Start' page,entity,[],[],Node
'Regulatory Data',entity,[],[],Node
'Bank Baskets',entity,[],[],Node
'Change Request',entity,[],[],Node
'Wizards',entity,[],[],Node
'Evaluator Tools',entity,[],[],Node
top of the screen,entity,[],[],Node
tabs,entity,[],[],Node
'RFS REQUESTER',entity,[],[],Node
'DEAL TRACKING',entity,[],[],Node
'BRIDGE ADMINISTRATION',entity,[],[],Node
preferences options,entity,[],[],Node
help options,entity,[],[],Node
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,event,[],[],Node
The Bank Basket feature has been enhanced.,event,[],[],Node
The enhancement provides improved rule management capabilities.,event,[],[],Node
enhancement,entity,[],[],Node
rule management capabilities,entity,[],[],Node
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",event,[],[],Node
Separate baskets can be configured by request type.,event,[],[],Node
request type,entity,[],[],Node
Request types include RFS.,event,[],[],Node
RFS,entity,[],[],Node
request types,entity,[],[],Node
Request types include Order.,event,[],[],Node
Order,entity,[],[],Node
Request types include SEP.,event,[],[],Node
SEP,entity,[],[],Node
Temporary blocks can be applied.,event,[],[],Node
Temporary blocks can be removed.,event,[],[],Node
Applying temporary blocks does not affect the configured rules.,event,[],[],Node
Figure 2,entity,[],[],Node
Bridge Administration Homepage,entity,[],[],Node
"""Bank Baskets"" quick link",entity,[],[],Node
navigation panel,entity,[],[],Node
institution tree,entity,[],[],Node
single TEX entity,entity,[],[],Node
TEX main entity,entity,[],[],Node
trade-as entities,entity,[],[],Node
trade-on-behalf entities,entity,[],[],Node
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",event,[],[],Node
Bank Baskets quick link,entity,[],[],Node
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",event,[],[],Node
tree,entity,[],[],Node
TEX entity,entity,[],[],Node
trade-as,entity,[],[],Node
trade-on-behalf,entity,[],[],Node
ITEX entities,entity,[],[],Node
main entity,entity,[],[],Node
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,event,[],[],Node
institution,entity,[],[],Node
single-click,entity,[],[],Node
new form/sheet,entity,[],[],Node
Bank Basket configuration details,entity,[],[],Node
entity,entity,[],[],Node
It is possible to open multiple forms/sheets at a time.,event,[],[],Node
multiple forms/sheets,entity,[],[],Node
The selected item will be highlighted as an active task inside the taskbar.,event,[],[],Node
selected item,entity,[],[],Node
active task,entity,[],[],Node
taskbar,entity,[],[],Node
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,event,[],[],Node
set of icons,entity,[],[],Node
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,event,[],[],Node
search field,entity,[],[],Node
user,entity,[],[],Node
alphanumeric value,entity,[],[],Node
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,event,[],[],Node
This feature,entity,[],[],Node
active task/sheet,entity,[],[],Node
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,event,[],[],Node
selected tree item,entity,[],[],Node
active institution,entity,[],[],Node
scroll from source,entity,[],[],Node
This icon is deactivated when using the Bank Basket configuration.,event,[],[],Node
This icon,entity,[],[],Node
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,event,[],[],Node
other configuration tools,entity,[],[],Node
toggle option,entity,[],[],Node
individuals,entity,[],[],Node
Each entity tab has a Live Audit Log which tracks all unsaved changes.,event,[],[],Node
Individual unsaved changes can be reverted by clicking on the arrow icon.,event,[],[],Node
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",event,[],[],Node
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,event,[],[],Node
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).",event,[],[],Node
Configuration Groups allow users to create a group one single time and reuse it across various rules.,event,[],[],Node
The toggle option allows the user to display only institutions in the navigation panel.,event,[],[],Node
institutions,entity,[],[],Node
configuration tools,entity,[],[],Node
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,event,[],[],Node
minimize icon,entity,[],[],Node
panel,entity,[],[],Node
entity tab,entity,[],[],Node
Live Audit Log,entity,[],[],Node
unsaved changes,entity,[],[],Node
arrow icon,entity,[],[],Node
"""Discard all changes"" button",entity,[],[],Node
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,event,[],[],Node
text 'User Guide 360T Bank Baskets Configuration',entity,[],[],Node
Configuration Groups tab,entity,[],[],Node
data tabs,entity,[],[],Node
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,event,[],[],Node
Configuration Groups,entity,[],[],Node
parameters,entity,[],[],Node
group,entity,[],[],Node
rules,entity,[],[],Node
Bank Basket rules for four separate request types are configured on this tab.,event,[],[],Node
Bank Basket rules,entity,[],[],Node
this tab,entity,[],[],Node
RFS FX Bank Baskets,entity,[],[],Node
RFS MM Bank Baskets,entity,[],[],Node
RFS Commodity Bank Baskets,entity,[],[],Node
RFS Cross Currency Netting Bank Baskets,entity,[],[],Node
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,event,[],[],Node
Provider Groups,entity,[],[],Node
Blocked Providers,entity,[],[],Node
Orders,entity,[],[],Node
Order Bank Baskets,entity,[],[],Node
Bank Basket rules for Forward or Spot orders are configured on this tab.,event,[],[],Node
Forward orders,entity,[],[],Node
Spot orders,entity,[],[],Node
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,event,[],[],Node
Supersonic (SEP),entity,[],[],Node
SEP Bank Baskets,entity,[],[],Node
Bank Basket rules for SEP streaming spot executions are configured on this tab.,event,[],[],Node
SEP streaming spot executions,entity,[],[],Node
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",event,[],[],Node
Bank Basket Configurations,entity,[],[],Node
RFS requests,entity,[],[],Node
Orders requests,entity,[],[],Node
SEP requests,entity,[],[],Node
An RFS configuration will have no impact on SEP trading.,event,[],[],Node
RFS configuration,entity,[],[],Node
SEP trading,entity,[],[],Node
centralized management,entity,[],[],Node
Groups,entity,[],[],Node
each parameter,entity,[],[],Node
creating rules,entity,[],[],Node
groups,entity,[],[],Node
set of rules,entity,[],[],Node
available parameters,entity,[],[],Node
Currency Groups,entity,[],[],Node
Currency Couple Groups,entity,[],[],Node
FX Time Period Groups,entity,[],[],Node
MM Time Period Groups,entity,[],[],Node
icons,entity,[],[],Node
create new group,entity,[],[],Node
edit name of existing group,entity,[],[],Node
delete group,entity,[],[],Node
save changes,entity,[],[],Node
Rename Group,entity,[],[],Node
Default Group,entity,[],[],Node
Remove Group,entity,[],[],Node
removed group,entity,[],[],Node
configuring complex bank basket rules,entity,[],[],Node
Users,entity,[],[],Node
individual custom rules,entity,[],[],Node
Individual custom rules,entity,[],[],Node
users with less complex bank basket setups,entity,[],[],Node
Bank Basket Configuration,entity,[],[],Node
existing values,entity,[],[],Node
Default Groups,entity,[],[],Node
values,entity,[],[],Node
new values,entity,[],[],Node
new currency,entity,[],[],Node
Default Currency Group,entity,[],[],Node
single currencies,entity,[],[],Node
single rule for group of currencies,entity,[],[],Node
Currencies,entity,[],[],Node
currency group,entity,[],[],Node
rule creation for interest rate products,entity,[],[],Node
interest rate products,entity,[],[],Node
Loan,entity,[],[],Node
Deposit,entity,[],[],Node
Interest Rate Swap,entity,[],[],Node
FRA,entity,[],[],Node
CapFloor,entity,[],[],Node
Configuration Groups facilitate centralized management of parameters.,event,[],[],Node
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",event,[],[],Node
requests,entity,[],[],Node
The groups themselves can be edited without changing a set of rules based on those groups.,event,[],[],Node
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",event,[],[],Node
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",event,[],[],Node
Configuration Group area,entity,[],[],Node
name,entity,[],[],Node
changes,entity,[],[],Node
Create Group adds a new group.,event,[],[],Node
Create Group,entity,[],[],Node
Rename Group changes the name of a group.,event,[],[],Node
Rename Group cannot be used on the Default Group.,event,[],[],Node
Remove Group deletes an individual group.,event,[],[],Node
Remove Group cannot be used on the Default Group.,event,[],[],Node
If the removed group is used in any configured rules this group is replaced by the Default Group.,event,[],[],Node
Users are reminded to save changes to configurations.,event,[],[],Node
configurations,entity,[],[],Node
Configuration Groups are particularly useful when configuring complex bank basket rules.,event,[],[],Node
complex bank basket rules,entity,[],[],Node
It is not required to configure groups based on the above parameters.,event,[],[],Node
Users may still set individual custom rules without utilizing the Configuration Groups.,event,[],[],Node
Individual custom rules may be preferable for some users with less complex bank basket setups.,event,[],[],Node
bank basket setups,entity,[],[],Node
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",event,[],[],Node
The Default Group will include all existing values.,event,[],[],Node
All Default Groups can be modified.,event,[],[],Node
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",event,[],[],Node
tool,entity,[],[],Node
versions,entity,[],[],Node
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,event,[],[],Node
360T platform,entity,[],[],Node
The new currency must be selected by the user.,event,[],[],Node
Currency Groups are intended to allow the classification of single currencies into customized groups.,event,[],[],Node
customized groups,entity,[],[],Node
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,event,[],[],Node
rule,entity,[],[],Node
group of currencies,entity,[],[],Node
individual currencies,entity,[],[],Node
Currencies can be added or removed from the group without editing the rules themselves.,event,[],[],Node
The image shows a user interface for managing currency groups in a financial application.,event,[],[],Node
A currency is highlighted with a single-click.,event,[],[],Node
The single arrow is activated.,event,[],[],Node
Clicking the single arrow moves the desired currency.,event,[],[],Node
A user clicks Create Group.,event,[],[],Node
A user types the desired name.,event,[],[],Node
A user clicks Create Group again.,event,[],[],Node
A user clicks Save.,event,[],[],Node
A user clicks on the Currency Group name.,event,[],[],Node
The currencies configured for the group are viewed.,event,[],[],Node
A Currency Couple Group is created.,event,[],[],Node
The Currency Couple Group can be used to simplify rules.,event,[],[],Node
Currencies may be added or removed from the default group.,event,[],[],Node
default group,entity,[],[],Node
A currency,entity,[],[],Node
Highlighting a currency activates the single arrow.,event,[],[],Node
Highlighting a currency,entity,[],[],Node
single arrow,entity,[],[],Node
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,event,[],[],Node
desired currency,entity,[],[],Node
Available,entity,[],[],Node
Selected,entity,[],[],Node
All currencies can be moved in either direction by using the double arrows.,event,[],[],Node
All currencies,entity,[],[],Node
double arrows,entity,[],[],Node
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",event,[],[],Node
new group,entity,[],[],Node
desired name,entity,[],[],Node
Save,entity,[],[],Node
Currencies configured for the group can be viewed by clicking on the Currency Group name.,event,[],[],Node
currencies,entity,[],[],Node
Currency Group name,entity,[],[],Node
The same currency can be added to many different groups.,event,[],[],Node
The system does not restrict the creation of groups with overlapping sets of currencies.,event,[],[],Node
system,entity,[],[],Node
creation of groups,entity,[],[],Node
overlapping sets of currencies,entity,[],[],Node
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",event,[],[],Node
buckets of currency pairs,entity,[],[],Node
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",event,[],[],Node
Currency Couple Group,entity,[],[],Node
FX products,entity,[],[],Node
RFS FX Bank Baskets area,entity,[],[],Node
FX Spot,entity,[],[],Node
Forwards,entity,[],[],Node
Swaps,entity,[],[],Node
NDF,entity,[],[],Node
NDS,entity,[],[],Node
Options,entity,[],[],Node
Block Trades,entity,[],[],Node
Energy Asian Swaps,entity,[],[],Node
Bullet Swaps,entity,[],[],Node
RFS Commodity Bank Baskets area,entity,[],[],Node
Order Spot,entity,[],[],Node
Orders Bank Basket area,entity,[],[],Node
RFS REQUESTER,entity,[],[],Node
DEAL TRACKING,entity,[],[],Node
The Default Group contains all currency pairs.,event,[],[],Node
currency pairs,entity,[],[],Node
base currency,entity,[],[],Node
quote currency,entity,[],[],Node
Currency Couple Groups can be created.,event,[],[],Node
Configuration Group icons,entity,[],[],Node
Currency Couple Groups can be renamed.,event,[],[],Node
Currency Couple Groups can be removed.,event,[],[],Node
A user can add currency pairs within a group.,event,[],[],Node
The Add Currency Couple button is clicked.,event,[],[],Node
Add Currency Couple button,entity,[],[],Node
The ISO code is chosen in the drop down list.,event,[],[],Node
ISO code,entity,[],[],Node
drop down list,entity,[],[],Node
The desired currency is typed.,event,[],[],Node
The selection is confirmed by clicking the green check mark.,event,[],[],Node
selection,entity,[],[],Node
green check mark,entity,[],[],Node
Save is clicked.,event,[],[],Node
Products with varying maturities or tenors may be configured into maturity ranges.,event,[],[],Node
Products,entity,[],[],Node
maturities,entity,[],[],Node
tenors,entity,[],[],Node
maturity ranges,entity,[],[],Node
An FX Time Period Group can simplify rules for FX products.,event,[],[],Node
FX Time Period Group,entity,[],[],Node
The Add FX Time Period button is clicked.,event,[],[],Node
The desired time period is chosen.,event,[],[],Node
The Save button is clicked.,event,[],[],Node
The Add MM Time Period button is clicked.,event,[],[],Node
The desired time period is selected.,event,[],[],Node
An MM Time Period Group is created.,event,[],[],Node
Rules for interest rate products can be simplified.,event,[],[],Node
The same tenors are used in various groups.,event,[],[],Node
They are used for different sets of rules.,event,[],[],Node
Tenors,entity,[],[],Node
range of maturities,entity,[],[],Node
various groups,entity,[],[],Node
Product Groups,entity,[],[],Node
product types,entity,[],[],Node
single rule,entity,[],[],Node
product group,entity,[],[],Node
rule creation,entity,[],[],Node
all products,entity,[],[],Node
removed or renamed,entity,[],[],Node
all product types,entity,[],[],Node
relevant products,entity,[],[],Node
"RFS, Orders or SEP",entity,[],[],Node
default Product Group,entity,[],[],Node
Bank Basket areas,entity,[],[],Node
individual Provider Groups,entity,[],[],Node
providers,entity,[],[],Node
user interface,entity,[],[],Node
360T Bank Baskets,entity,[],[],Node
RFS Requester,entity,[],[],Node
Deal Tracking,entity,[],[],Node
interface,entity,[],[],Node
currency groups,entity,[],[],Node
FX time period groups,entity,[],[],Node
MM time period groups,entity,[],[],Node
product groups,entity,[],[],Node
section,entity,[],[],Node
Select Member for 'FX Spot and Forward',entity,[],[],Node
available product types,entity,[],[],Node
selected product types,entity,[],[],Node
Figure 16,entity,[],[],Node
Bank Basket Product Groups,entity,[],[],Node
Figure 17,entity,[],[],Node
Bank Basket Product Groups Create Group,entity,[],[],Node
green button,entity,[],[],Node
User Guide,entity,[],[],Node
DEUTSCHE BÖRSE GROUP,entity,[],[],Node
Product Groups are intended to allow the classification of product types into customized groups.,event,[],[],Node
One single rule can be set for each group of products rather than many rules for individual product types.,event,[],[],Node
"A new group is created by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",event,[],[],Node
A product group can be used to simplify rule creation for all relevant product types.,event,[],[],Node
A default group exists which includes all products.,event,[],[],Node
The default group cannot be removed or renamed.,event,[],[],Node
The product types in the default group can be altered.,event,[],[],Node
"The Default Group contains all product types across RFS, Orders and SEP.",event,[],[],Node
"Only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",event,[],[],Node
Products can be added or removed from a product group without editing the rules themselves.,event,[],[],Node
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,event,[],[],Node
periods,entity,[],[],Node
OVERNIGHT,entity,[],[],Node
1 WEEK,entity,[],[],Node
1 MONTH,entity,[],[],Node
6 MONTHS,entity,[],[],Node
"Tenors are defined as a range of maturities, with both start and end values included.",event,[],[],Node
start values,entity,[],[],Node
end values,entity,[],[],Node
The same tenors may be used in various groups in order to be used for different sets of rules.,event,[],[],Node
sets of rules,entity,[],[],Node
This allows setting one single rule for each group of products rather than many rules for individual product types.,event,[],[],Node
one single rule,entity,[],[],Node
group of products,entity,[],[],Node
many rules,entity,[],[],Node
individual product types,entity,[],[],Node
The image shows a user interface for configuring 360T Bank Baskets.,event,[],[],Node
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.",event,[],[],Node
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",event,[],[],Node
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.",event,[],[],Node
options,entity,[],[],Node
Products can be added or removed from the group without editing the rules themselves.,event,[],[],Node
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",event,[],[],Node
SEP Bank Basket areas,entity,[],[],Node
To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.,event,[],[],Node
products,entity,[],[],Node
This group cannot be removed or renamed.,event,[],[],Node
"However, the product types in the group can be altered.",event,[],[],Node
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",event,[],[],Node
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",event,[],[],Node
"The image shows a green button with the text ""360T"" on it.",event,[],[],Node
"text ""360T""",entity,[],[],Node
The button has a white outline and a white background.,event,[],[],Node
button,entity,[],[],Node
white outline,entity,[],[],Node
white background,entity,[],[],Node
The text is in a stylized font.,event,[],[],Node
stylized font,entity,[],[],Node
Providers are temporarily blocked from particular request types.,event,[],[],Node
"A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.",event,[],[],Node
A Provider is removed completely and its relationship is rejected using the Counterpart Relationship Management tool.,event,[],[],Node
Please refer to the relevant user guide.,event,[],[],Node
The Provider Groups can be edited.,event,[],[],Node
Providers may be temporarily blocked from particular request types.,event,[],[],Node
Providers,entity,[],[],Node
Provider Group,entity,[],[],Node
A Blocked Provider will remain in a Provider Group.,event,[],[],Node
Blocked Provider,entity,[],[],Node
A Blocked Provider will appear with a blocked symbol.,event,[],[],Node
blocked symbol,entity,[],[],Node
A Provider should be removed completely.,event,[],[],Node
Provider,entity,[],[],Node
The relationship should be rejected using the Counterpart Relationship Management tool.,event,[],[],Node
relationship,entity,[],[],Node
Counterpart Relationship Management tool,entity,[],[],Node
Refer to the relevant user guide.,event,[],[],Node
user guide,entity,[],[],Node
