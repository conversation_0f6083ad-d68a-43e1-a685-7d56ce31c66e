Name,Type
Create Group,Entity
Bank Basket feature,Entity
creation of groups,Entity
main entity,Entity
Supersonic (SEP),Entity
Blocked Providers,Entity
create new group,Entity
toggle option,Entity
counterpart relationship(s),Entity
selected product types,Entity
"text ""360T""",Entity
providers,Entity
institution tree,Entity
each parameter,Entity
tool,Entity
Energy Asian Swaps,Entity
RFS configuration,Entity
particular user,Entity
TEX main entity,Entity
entity tab,Entity
CapFloor,Entity
"RFS, Orders or SEP",Entity
"request type (RFS, Order, SEP)",Entity
set of rules,Entity
Options,Entity
tree,Entity
ITEX entities,Entity
relevant administrative rights,Entity
user guide,Entity
'BRIDGE ADMINISTRATION',Entity
EMS application,Entity
FRA,Entity
rule management capabilities,Entity
green outline,Entity
Providers,Entity
green check mark,Entity
enhancement,Entity
buckets of currency pairs,Entity
configured rules,Entity
selected tree item,Entity
'Wizards',Entity
user interface,Entity
Deposit,Entity
Configuration Groups tab,Entity
Save,Entity
drop down list,Entity
interest rate products,Entity
save changes,Entity
single rule,Entity
currency pairs,Entity
rules,Entity
separate baskets,Entity
relationship,Entity
Bank Basket areas,Entity
new values,Entity
requests,Entity
overlapping sets of currencies,Entity
active institution,Entity
RFS Commodity Bank Baskets area,Entity
temporary blocks,Entity
FX products,Entity
new form/sheet,Entity
Add Currency Couple button,Entity
FX Spot,Entity
NDF,Entity
screen header,Entity
'Change Request',Entity
RFS REQUESTER,Entity
Bank Basket Product Groups Create Group,Entity
all product types,Entity
customer relationship manager,Entity
Bank Baskets quick link,Entity
one single rule,Entity
"""Discard all changes"" button",Entity
single TEX entity,Entity
1 WEEK,Entity
white outline,Entity
product group,Entity
homepage,Entity
currencies,Entity
configuration of separate baskets,Entity
product(s),Entity
PROPRIETARY AND CONFIDENTIAL INFORMATION,Entity
360T enhanced Bank Basket feature,Entity
minimize icon,Entity
RFS requests,Entity
RFS,Entity
ISO code,Entity
Live Audit Log,Entity
Bank Basket rules,Entity
product groups,Entity
available parameters,Entity
interface,Entity
Counterpart Relationship Management tool,Entity
rounded corners,Entity
6 MONTHS,Entity
icons,Entity
quick navigation toolbar,Entity
group of products,Entity
start values,Entity
alphanumeric value,Entity
Bridge Administration feature,Entity
FX time period groups,Entity
button,Entity
360T,Entity
FX Time Period Group,Entity
Bank Basket Configuration,Entity
Bank Basket configuration details,Entity
corresponding user rights,Entity
Default Groups,Entity
Configuration Group icons,Entity
FX Time Period Groups,Entity
name,Entity
individual Provider Groups,Entity
Individual custom rules,Entity
single arrow,Entity
Order Bank Baskets,Entity
DEAL TRACKING,Entity
This icon,Entity
<EMAIL>,Entity
Figure 2,Entity
periods,Entity
360T Bank Baskets,Entity
Users,Entity
removed or renamed,Entity
Bank Basket configuration,Entity
screen,Entity
ability to apply and remove temporary blocks,Entity
Groups,Entity
versions,Entity
SEP Bank Baskets,Entity
white background,Entity
entities,Entity
available shortcuts,Entity
Bridge application,Entity
Currencies,Entity
selected item,Entity
system,Entity
single currencies,Entity
TRADE SECRETS,Entity
1 MONTH,Entity
Deal Tracking,Entity
RFS FX Bank Baskets area,Entity
sets of rules,Entity
'RFS REQUESTER',Entity
new currency,Entity
Configuration Groups,Entity
Remove Group,Entity
text 'User Guide 360T Bank Baskets Configuration',Entity
Bridge Administration,Entity
options,Entity
DEUTSCHE BÖRSE GROUP,Entity
'Administration Start' page,Entity
product types,Entity
PRIOR WRITTEN APPROVAL,Entity
arrow icon,Entity
Currency Couple Groups,Entity
RFS Commodity Bank Baskets,Entity
T,Entity
scroll from source,Entity
This feature,Entity
NDS,Entity
desired name,Entity
company's Bank Baskets,Entity
Bank Basket Product Groups,Entity
Configuration Group area,Entity
MM Time Period Groups,Entity
Rename Group,Entity
other configuration tools,Entity
available product types,Entity
blocked symbol,Entity
Default Group,Entity
RFS MM Bank Baskets,Entity
active task,Entity
trade-on-behalf,Entity
maturities,Entity
single rule for group of currencies,Entity
rule,Entity
individual custom rules,Entity
creating rules,Entity
Provider,Entity
green rectangle,Entity
Order Spot,Entity
Select Member for 'FX Spot and Forward',Entity
users,Entity
'DEAL TRACKING',Entity
RFS Cross Currency Netting Bank Baskets,Entity
Loan,Entity
Spot orders,Entity
Bridge Administration Homepage,Entity
Product Groups,Entity
Figure 17,Entity
'Regulatory Data',Entity
tenors,Entity
customized groups,Entity
centralized management,Entity
request types,Entity
currency groups,Entity
Selected,Entity
360T Bank Baskets Configuration user guide,Entity
0,Entity
stylized font,Entity
A currency,Entity
search field,Entity
active homepage icon,Entity
Orders requests,Entity
configuration groups,Entity
Highlighting a currency,Entity
Orders,Entity
trade-on-behalf entities,Entity
individual currencies,Entity
Bullet Swaps,Entity
all products,Entity
SEP requests,Entity
edit name of existing group,Entity
default group,Entity
products,Entity
Default Currency Group,Entity
RFS FX Bank Baskets,Entity
SEP Bank Basket areas,Entity
Bank Basket Configurations,Entity
data tabs,Entity
Forward orders,Entity
"menu option ""Administration""",Entity
different configuration tools,Entity
SEP,Entity
left side of the homepage,Entity
Currency Groups,Entity
changes,Entity
default Product Group,Entity
navigation panel,Entity
section,Entity
360T platform,Entity
time period,Entity
Forwards,Entity
green button,Entity
Orders Bank Basket area,Entity
configurations,Entity
desired currency,Entity
preferences options,Entity
Provider Groups,Entity
Blocked Provider,Entity
All currencies,Entity
top of the screen,Entity
OVERNIGHT,Entity
values,Entity
many rules,Entity
selection,Entity
'Bank Baskets',Entity
'Evaluator Tools',Entity
set of icons,Entity
range of maturities,Entity
two arrows,Entity
THIRD PARTY,Entity
single-click,Entity
TEX entity,Entity
Interest Rate Swap,Entity
individual product types,Entity
base currency,Entity
request type,Entity
user manual,Entity
institutions,Entity
Swaps,Entity
this tab,Entity
group of currencies,Entity
trade-as,Entity
users with less complex bank basket setups,Entity
panel,Entity
Provider Group,Entity
trade-as entities,Entity
User Guide,Entity
rule creation for interest rate products,Entity
360 TREASURY SYSTEMS AG,Entity
Products,Entity
configuring complex bank basket rules,Entity
multiple forms/sheets,Entity
Block Trades,Entity
rule creation,Entity
parameters,Entity
removed group,Entity
quote currency,Entity
relevant products,Entity
complex bank basket rules,Entity
Figure 16,Entity
active task/sheet,Entity
institution,Entity
taskbar,Entity
Available,Entity
Bridge Administration tool,Entity
SEP trading,Entity
delete group,Entity
individuals,Entity
360T Bridge Administration tool,Entity
new group,Entity
existing values,Entity
groups,Entity
configuration tools,Entity
Currency Group name,Entity
help options,Entity
RFS Requester,Entity
360,Entity
currency group,Entity
THIS FILE,Entity
unsaved changes,Entity
currency couple,Entity
double arrows,Entity
text,Entity
currency,Entity
maturity ranges,Entity
Currency Couple Group,Entity
MM time period groups,Entity
end values,Entity
user,Entity
bank basket setups,Entity
Order,Entity
Tenors,Entity
group,Entity
screenshot,Entity
entity,Entity
various groups,Entity
image,Entity
improved rule management capabilities,Entity
logo,Entity
SEP streaming spot executions,Entity
tabs,Entity
white,Entity
"""Bank Baskets"" quick link",Entity
A user clicks Save.,Event
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.",Event
Users are reminded to save changes to configurations.,Event
Currencies can be added or removed from the group without editing the rules themselves.,Event
All Default Groups can be modified.,Event
The system does not restrict the creation of groups with overlapping sets of currencies.,Event
One single rule can be set for each group of products rather than many rules for individual product types.,Event
Providers are temporarily blocked from particular request types.,Event
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,Event
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.",Event
A Blocked Provider will remain in a Provider Group.,Event
The desired currency is typed.,Event
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Event
A user clicks Create Group again.,Event
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,Event
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Event
Clicking the single arrow moves the desired currency.,Event
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,Event
If the removed group is used in any configured rules this group is replaced by the Default Group.,Event
The selection is confirmed by clicking the green check mark.,Event
Create Group adds a new group.,Event
The toggle option allows the user to display only institutions in the navigation panel.,Event
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Event
Individual unsaved changes can be reverted by clicking on the arrow icon.,Event
The Provider Groups can be edited.,Event
The ISO code is chosen in the drop down list.,Event
The Add FX Time Period button is clicked.,Event
Configuration Groups facilitate centralized management of parameters.,Event
The image shows a user interface for configuring 360T Bank Baskets.,Event
Request types include Order.,Event
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",Event
A user clicks on the Currency Group name.,Event
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".",Event
Please refer to the relevant user guide.,Event
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.",Event
"The text ""360"" is in white with a green outline.",Event
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,Event
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",Event
THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.,Event
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,Event
Product Groups are intended to allow the classification of product types into customized groups.,Event
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",Event
The product types in the default group can be altered.,Event
It is not required to configure groups based on the above parameters.,Event
A product group can be used to simplify rule creation for all relevant product types.,Event
An FX Time Period Group can simplify rules for FX products.,Event
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Event
A user clicks Create Group.,Event
Currency Couple Groups can be removed.,Event
Configuration Groups allow users to create a group one single time and reuse it across various rules.,Event
The currencies configured for the group are viewed.,Event
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Event
Bank Basket rules for Forward or Spot orders are configured on this tab.,Event
Rename Group changes the name of a group.,Event
Products with varying maturities or tenors may be configured into maturity ranges.,Event
Highlighting a currency activates the single arrow.,Event
The default group cannot be removed or renamed.,Event
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",Event
The Default Group contains all currency pairs.,Event
The single arrow is activated.,Event
Rules for interest rate products can be simplified.,Event
The Add MM Time Period button is clicked.,Event
The same tenors may be used in various groups in order to be used for different sets of rules.,Event
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.",Event
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,Event
Remove Group deletes an individual group.,Event
Separate baskets can be configured by request type.,Event
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",Event
A user can add currency pairs within a group.,Event
The Add Currency Couple button is clicked.,Event
The Default Group will include all existing values.,Event
Remove Group cannot be used on the Default Group.,Event
This icon is deactivated when using the Bank Basket configuration.,Event
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.",Event
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,Event
Currencies configured for the group can be viewed by clicking on the Currency Group name.,Event
To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.,Event
The Currency Couple Group can be used to simplify rules.,Event
A user types the desired name.,Event
The same currency can be added to many different groups.,Event
A Provider is removed completely and its relationship is rejected using the Counterpart Relationship Management tool.,Event
Request types include RFS.,Event
This group cannot be removed or renamed.,Event
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Event
A currency is highlighted with a single-click.,Event
Bank Basket rules for SEP streaming spot executions are configured on this tab.,Event
Configuration Groups are particularly useful when configuring complex bank basket rules.,Event
They are used for different sets of rules.,Event
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,Event
"The image shows a green button with the text ""360T"" on it.",Event
"A new group is created by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Event
Temporary blocks can be removed.,Event
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,Event
This allows setting one single rule for each group of products rather than many rules for individual product types.,Event
Currency Groups are intended to allow the classification of single currencies into customized groups.,Event
THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.,Event
"The ""T"" is also in white with a green outline.",Event
Request types include SEP.,Event
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",Event
Bank Basket rules for four separate request types are configured on this tab.,Event
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",Event
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",Event
Applying temporary blocks does not affect the configured rules.,Event
A Blocked Provider will appear with a blocked symbol.,Event
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Event
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,Event
A default group exists which includes all products.,Event
The groups themselves can be edited without changing a set of rules based on those groups.,Event
The desired time period is chosen.,Event
Providers may be temporarily blocked from particular request types.,Event
Refer to the relevant user guide.,Event
Products can be added or removed from a product group without editing the rules themselves.,Event
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Event
Save is clicked.,Event
The same tenors are used in various groups.,Event
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,Event
Rename Group cannot be used on the Default Group.,Event
A Provider should be removed completely.,Event
The Bank Basket feature has been enhanced.,Event
The Save button is clicked.,Event
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",Event
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.",Event
An RFS configuration will have no impact on SEP trading.,Event
"Tenors are defined as a range of maturities, with both start and end values included.",Event
The button has a white outline and a white background.,Event
Currency Couple Groups can be renamed.,Event
A Currency Couple Group is created.,Event
Currencies may be added or removed from the default group.,Event
All currencies can be moved in either direction by using the double arrows.,Event
Currency Couple Groups can be created.,Event
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,Event
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,Event
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Event
The new currency must be selected by the user.,Event
The relationship should be rejected using the Counterpart Relationship Management tool.,Event
"A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.",Event
The selected item will be highlighted as an active task inside the taskbar.,Event
The image shows a user interface for managing currency groups in a financial application.,Event
The desired time period is selected.,Event
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).",Event
The enhancement provides improved rule management capabilities.,Event
Products can be added or removed from the group without editing the rules themselves.,Event
Individual custom rules may be preferable for some users with less complex bank basket setups.,Event
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",Event
Temporary blocks can be applied.,Event
An MM Time Period Group is created.,Event
The text is in a stylized font.,Event
It is possible to open multiple forms/sheets at a time.,Event
"Only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.",Event
"The Default Group contains all product types across RFS, Orders and SEP.",Event
Users may still set individual custom rules without utilizing the Configuration Groups.,Event
"However, the product types in the group can be altered.",Event
allow setting,Relation
cannot be,Relation
has,Relation
added or removed from,Relation
before,Relation
available to,Relation
preferable for,Relation
configures,Relation
replaced by,Relation
defined as,Relation
has options for,Relation
manages,Relation
describes,Relation
not added to,Relation
apply for,Relation
contains,Relation
are for,Relation
is of,Relation
allow classification of,Relation
is part of,Relation
set,Relation
from,Relation
cannot be used on,Relation
not included in,Relation
available on,Relation
edited without changing,Relation
enhanced to provide,Relation
because,Relation
displays text,Relation
facilitate,Relation
administer,Relation
include,Relation
modified by removing,Relation
at the same time,Relation
for,Relation
configured in,Relation
altered in,Relation
as a result,Relation
opens,Relation
reused for,Relation
useful for,Relation
titled,Relation
do not affect,Relation
based on,Relation
have,Relation
configured under,Relation
shows,Relation
accessed via,Relation
utilizes,Relation
allow creation of,Relation
simplifies,Relation
found within,Relation
is participated by,Relation
allow user to,Relation
lead to,Relation
includes,Relation
displays,Relation
can set up,Relation
located in,Relation
used in,Relation
allow blocking of,Relation
configured by,Relation
selected by,Relation
depicts,Relation
opens to,Relation
allow setting of,Relation
classify,Relation
has option for,Relation
