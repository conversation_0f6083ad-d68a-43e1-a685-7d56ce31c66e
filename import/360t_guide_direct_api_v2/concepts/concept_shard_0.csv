node,conceptualized_node,node_type
One single rule can be set for each group of products rather than many rules for individual product types.,"simplification, standardization, efficiency, consolidation, regulation",event
The Add MM Time Period button is clicked.,"interaction, configuration, data entry, addition, operation",event
Highlighting a currency activates the single arrow.,"activation, interaction, selection, trigger, display",event
A user clicks Save.,"saving, action, operation, commitment, storage",event
Separate baskets can be configured by request type.,"setup, customization, classification, organization, structuring",event
Rename Group changes the name of a group.,"modification, management, labeling, update, administration",event
"The ""T"" is also in white with a green outline.","styling, coloring, appearance, description, design",event
The groups themselves can be edited without changing a set of rules based on those groups.,"alteration, flexibility, decoupling, management, governance",event
Bank Basket rules for SEP streaming spot executions are configured on this tab.,"configuration, rule setting, system management, financial administration",event
The text is in a stylized font.,"stylization, formatting, design, appearance, presentation",event
A user clicks on the Currency Group name.,"interaction, selection, navigation, access, query",event
Request types include Order.,"classification, inclusion, definition, categorization, specification",event
The selection is confirmed by clicking the green check mark.,"confirmation, approval, validation, action, interaction, completion",event
Temporary blocks can be applied.,"restriction, suspension, control, measure, application",event
The relationship should be rejected using the Counterpart Relationship Management tool.,"rejection, termination, management, process, decision",event
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,"interaction, navigation, selection, control, functionality, usability",event
The desired currency is typed.,"input, specification, financial, transaction, data entry",event
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,"management, organization, control, systematization, administration",event
The selected item will be highlighted as an active task inside the taskbar.,"highlighting, activation, status change, visual cue, user interface",event
The Bank Basket feature has been enhanced.,"improvement, upgrade, development, update, refinement, progress",event
They are used for different sets of rules.,"differentiation, application, classification, regulation, categorization",event
Save is clicked.,"action, commit, storage, persistence, recording",event
A Blocked Provider will appear with a blocked symbol.,"blocking, status, display, restriction, indication",event
Currencies may be added or removed from the default group.,"adjustment, management, configuration, update, categorization",event
The ISO code is chosen in the drop down list.,"selection, input, choice, configuration, data entry",event
Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.,"periodization, duration, timeframe, scheduling, intervals",event
A user can add currency pairs within a group.,"addition, grouping, configuration, management, customization",event
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.","configuration, management, operation, interaction, control",event
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.","introduction, grouping, categorization, system update, definition",event
Remove Group deletes an individual group.,"deletion, removal, management, modification, operation",event
Currencies configured for the group can be viewed by clicking on the Currency Group name.,"access, viewing, display, navigation, retrieval",event
This group cannot be removed or renamed.,"restriction, immutability, permanence, fixity, constraint",event
Products with varying maturities or tenors may be configured into maturity ranges.,"configuration, structuring, grouping, categorization, organization",event
A currency is highlighted with a single-click.,"selection, interaction, designation, focus, marking",event
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.","opening, display, navigation, access, interaction, information",event
Users are reminded to save changes to configurations.,"reminder, notification, instruction, data preservation, configuration, user prompt",event
Clicking the single arrow moves the desired currency.,"interaction, manipulation, transfer, adjustment, control",event
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.","policy, update, exclusion, management, development",event
"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.","simplification, grouping, automation, management, efficiency",event
Products can be added or removed from a product group without editing the rules themselves.,"management, flexibility, configuration, grouping, adaptation",event
Currency Groups are intended to allow the classification of single currencies into customized groups.,"grouping, categorization, organization, management, arrangement",event
Request types include SEP.,"categorization, classification, definition, specification, enumeration",event
Currencies can be added or removed from the group without editing the rules themselves.,"flexibility, management, adjustment, modularity, configuration",event
The button has a white outline and a white background.,"appearance, design, styling, coloration, visibility, low contrast",event
The image shows a user interface for managing currency groups in a financial application.,"management, finance, interface, grouping, system, configuration",event
Bank Basket rules for Forward or Spot orders are configured on this tab.,"configuration, rules, finance, setup, order management",event
Configuration Groups facilitate centralized management of parameters.,"management, centralization, control, organization, administration",event
The product types in the default group can be altered.,"modification, configuration, adjustment, management, customization",event
Product Groups are intended to allow the classification of product types into customized groups.,"classification, grouping, organization, categorization, structuring",event
The image shows a user interface for configuring 360T Bank Baskets.,"configuration, interface, setup, banking, customization",event
Configuration Groups are particularly useful when configuring complex bank basket rules.,"configuration, management, structuring, rule setting, system setup",event
This icon is deactivated when using the Bank Basket configuration.,"deactivation, restriction, configuration, control, adjustment",event
The Add FX Time Period button is clicked.,"activation, operation, command, configuration, user input",event
Individual custom rules may be preferable for some users with less complex bank basket setups.,"customization, configuration, optimization, management, preference",event
An FX Time Period Group can simplify rules for FX products.,"simplification, regulation, optimization, management, standardization",event
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.","creation, procedure, setup, configuration, management",event
Remove Group cannot be used on the Default Group.,"restriction, error, policy, protection, constraint",event
A product group can be used to simplify rule creation for all relevant product types.,"simplification, optimization, management, standardization, efficiency",event
Currency Couple Groups can be renamed.,"relabeling, modification, configuration, management, organization",event
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,"configuration, filtering, customization, setting, display",event
The same tenors are used in various groups.,"sharing, allocation, deployment, pooling, common use",event
Request types include RFS.,"classification, definition, enumeration, categorization, specification",event
An RFS configuration will have no impact on SEP trading.,"non-impact, neutrality, stability, independence, decoupling",event
"The image shows a green button with the text ""360T"" on it.","visual, graphic, element, interface, display",event
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,"simplification, standardization, grouping, efficiency, consolidation",event
Temporary blocks can be removed.,"removal, clearing, unblocking, resolution, progress",event
It is possible to open multiple forms/sheets at a time.,"capability, multitasking, concurrency, access, feature",event
The desired time period is selected.,"selection, choice, decision, scheduling, planning",event
It is not required to configure groups based on the above parameters.,"declaration, optionality, discretion, waiver, flexibility",event
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,"choosing, setup, system interaction, data processing, task",event
Please refer to the relevant user guide.,"reference, guidance, information, consultation, instruction",event
A Provider should be removed completely.,"removal, termination, discontinuation, severance, dismantling",event
Providers are temporarily blocked from particular request types.,"blocking, restriction, denial, interruption, suspension",event
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,"exclusion, configuration, system update, policy, data handling",event
Users may still set individual custom rules without utilizing the Configuration Groups.,"customization, configuration, user control, autonomy, rule setting",event
A default group exists which includes all products.,"grouping, categorization, organization, inclusion, arrangement",event
"The Default Group contains all product types across RFS, Orders and SEP.","grouping, categorization, inclusion, definition, organization",event
Rename Group cannot be used on the Default Group.,"restriction, prohibition, system rule, operation failure, constraint",event
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,"search, navigation, discovery, location, query, retrieval",event
The Add Currency Couple button is clicked.,"interaction, operation, addition, configuration, input",event
The Save button is clicked.,"storage, preservation, action, commitment, persistence",event
"There are two arrows pointing towards each other in the middle of the ""0"" in ""360"".","interaction, opposition, convergence, symbolism, diagram",event
THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.,"confidentiality, secrecy, warning, protection, disclosure",event
Each entity tab has a Live Audit Log which tracks all unsaved changes.,"logging, auditing, tracking, monitoring, data capture",event
The same currency can be added to many different groups.,"distribution, allocation, integration, financial operation, resource management",event
Currency Couple Groups can be created.,"formation, grouping, setup, definition, configuration",event
Configuration Groups allow users to create a group one single time and reuse it across various rules.,"grouping, reusability, management, standardization, efficiency",event
"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.","management, configuration, access control, grouping, blocking, system feature",event
The desired time period is chosen.,"selection, decision, scheduling, planning, allocation",event
The Default Group contains all currency pairs.,"inclusion, grouping, definition, configuration, categorization",event
"A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.","restriction, status change, system update, display change, access control",event
Applying temporary blocks does not affect the configured rules.,"restriction, operation, stability, integrity, policy",event
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.","configuration, setup, management, standardization, efficiency",event
"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","application, filtering, restriction, policy, selection",event
THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.,"policy, restriction, confidentiality, authorization, security",event
The same tenors may be used in various groups in order to be used for different sets of rules.,"re-use, adaptation, application, flexibility, configuration",event
"The text ""360"" is in white with a green outline.","formatting, styling, appearance, display, graphics",event
Products can be added or removed from the group without editing the rules themselves.,"flexibility, management, configuration, adaptability, modularity",event
Refer to the relevant user guide.,"guidance, information, consultation, reference, instruction",event
A Provider is removed completely and its relationship is rejected using the Counterpart Relationship Management tool.,"termination, disassociation, management, severance, rejection",event
The single arrow is activated.,"activation, initiation, launch, trigger, deployment",event
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.","configuration, structuring, organization, design, modeling",event
"Tenors are defined as a range of maturities, with both start and end values included.","concept, definition, specification, duration, parameters, measurement",event
A user types the desired name.,"input, naming, data entry, selection, identification",event
"Only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.","system configuration, data filtering, policy enforcement, criteria definition, information governance",event
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.","grouping, organization, classification, structuring, management",event
A Currency Couple Group is created.,"creation, formation, organization, establishment, grouping",event
Create Group adds a new group.,"creation, formation, grouping, organization, structuring",event
The toggle option allows the user to display only institutions in the navigation panel.,"filtering, configuration, customization, control, setting, interaction",event
"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.","system, features, functions, tools, management",event
The new currency must be selected by the user.,"selection, choice, decision, option, configuration, input",event
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,"configuration, setup, management, system administration, customization",event
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,"click, interaction, control, ui action, hiding",event
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.","reversion, undo, control, operation, data management",event
A user clicks Create Group.,"creation, initiation, interaction, formation, setup",event
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.","activation, configuration, initialization, setup, automation, defaulting",event
The Default Group will include all existing values.,"inclusion, defaulting, setting, configuration, grouping, definition",event
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,"search, query, retrieval, input, lookup",event
Bank Basket rules for four separate request types are configured on this tab.,"configuration, system setup, policy definition, process control, parameterization",event
The Provider Groups can be edited.,"editing, modification, management, update, configuration",event
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,"interaction, transfer, selection, configuration, management",event
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.","listing, parameters, configuration, options, definition",event
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible, with options for available and selected product types.","selection, configuration, interface, management, financials, display",event
The system does not restrict the creation of groups with overlapping sets of currencies.,"permissiveness, configuration, interoperability, policy, flexibility",event
All currencies can be moved in either direction by using the double arrows.,"exchange, transaction, adjustment, control, movement",event
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.","separation, independence, configuration, system design, partitioning",event
The currencies configured for the group are viewed.,"review, access, financials, data check, system check",event
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,"documentation, instruction, explanation, guidance, reference",event
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.","simplification, financial management, rule processing, product categorization, system utility",event
"However, the product types in the group can be altered.","change, modification, flexibility, adjustment, update",event
"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.","management, configuration, organization, data control, system utility, grouping",event
An MM Time Period Group is created.,"creation, formation, grouping, establishment, organization, community",event
"A new group is created by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.","creation, setup, process, configuration, task",event
The Currency Couple Group can be used to simplify rules.,"simplification, organization, efficiency, grouping, clarity",event
If the removed group is used in any configured rules this group is replaced by the Default Group.,"replacement, defaulting, reconfiguration, policy enforcement, fallback",event
To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.,"process, creation, instructions, workflow, configuration",event
Rules for interest rate products can be simplified.,"simplification, reform, regulation, policy, improvement",event
The enhancement provides improved rule management capabilities.,"improvement, administration, upgrade, optimization, development",event
All Default Groups can be modified.,"modification, customization, configuration, management, flexibility, control",event
A Blocked Provider will remain in a Provider Group.,"retention, status management, policy enforcement, system rule, grouping",event
Currency Couple Groups can be removed.,"removal, discontinuation, cancellation, restructuring, optimization, adjustment, financial action",event
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,"configuration, management, policy, system setup, control",event
Providers may be temporarily blocked from particular request types.,"blocking, restriction, suspension, denial, limitation, access control",event
"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).","management, organization, control, systematization, arrangement",event
The default group cannot be removed or renamed.,"constraint, immutability, system rule, limitation, fixed state",event
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,"selection, display, navigation, access, information, interaction",event
"The image shows a logo for ""360T"", which is a green rectangle with rounded corners.","depiction, display, visual content, graphic element, branding",event
This allows setting one single rule for each group of products rather than many rules for individual product types.,"simplification, consolidation, efficiency, optimization, streamlining",event
A user clicks Create Group again.,"re-attempt, interaction, re-execution, re-trigger",event
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,"documentation, information, guidance, instructions, setup",event
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,"interface, control, toggling, interaction, management",event
Individual unsaved changes can be reverted by clicking on the arrow icon.,"undo, rollback, restoration, discard, management, control",event
end values,"value, number, data, concept",entity
Spot orders,"order, transaction, trade, concept, term",entity
Bridge application,"software, program, tool, utility, connector, app",entity
Deal Tracking,"management, system, process, software, function",entity
This icon,"symbol, graphic, element, visual, indicator",entity
user interface,"interface, display, control, software, component, system, design, view",entity
RFS Cross Currency Netting Bank Baskets,"system, mechanism, finance, banking, tool, product, rule set",entity
Bank Basket Configuration,"setting, feature, system, process, module",entity
'Evaluator Tools',"tool, software, feature, utility, function",entity
'Bank Baskets',"option, feature, setting, category",entity
quick navigation toolbar,"toolbar, interface, element, control",entity
Bullet Swaps,"instrument, product, derivative, agreement",entity
green outline,"border, graphic, element, feature",entity
individuals,"person, human, user, participant, being",entity
6 MONTHS,"period, duration, time, interval, measure",entity
minimize icon,"button, control, element, symbol",entity
stylized font,"font, typeface, typography, text style, design element",entity
Live Audit Log,"log, record, feature, tool",entity
individual custom rules,"rule, setting, policy, guideline, regulation, parameter",entity
Available,"state, status, property, concept",entity
Swaps,"contract, agreement, instrument, derivative, transaction, product",entity
sets of rules,"policy, system, framework, guideline, code",entity
Default Currency Group,"group, category, setting, parameter, concept",entity
Loan,"debt, credit, product, financing, transaction, agreement",entity
existing values,"values, principles, concepts, standards, ideals",entity
selected product types,"category, item, goods, service, selection",entity
icons,"symbol, graphic, image, element, visual",entity
counterpart relationship(s),"connection, pairing, association, correspondence, concept",entity
new group,"entity, collection, association, formation",entity
Bank Basket Product Groups Create Group,"function, feature, module, component, tool",entity
OVERNIGHT,"period, duration, time",entity
Product Groups,"category, classification, system",entity
Bank Basket feature,"function, component, module, tool, system",entity
Default Groups,"collection, setting, category, configuration, element",entity
"menu option ""Administration""","item, setting, control, feature",entity
Add Currency Couple button,"control, element, component, widget",entity
360T enhanced Bank Basket feature,"feature, software, product, service, tool",entity
1 MONTH,"time, duration, unit, period",entity
parameters,"concept, setting, variable, input, attribute",entity
text 'User Guide 360T Bank Baskets Configuration',"guide, document, manual, text, information",entity
image,"picture, visual, graphic, media, content",entity
Counterpart Relationship Management tool,"software, application, system, program, utility, platform",entity
create new group,"action, command, function, feature, operation",entity
arrow icon,"symbol, indicator, graphic, element",entity
Currency Couple Groups,"group, category, financial concept, trading term",entity
single rule,"policy, guideline, principle, standard, concept",entity
green check mark,"symbol, icon, indicator, confirmation, selection, mark",entity
button,"control, element, component, object",entity
Default Group,"category, classification, set, container",entity
screenshot,"image, picture, capture, file, data, visual",entity
Orders Bank Basket area,"grouping, category, system, component, segment",entity
currency pairs,"financial instrument, asset, exchange rate, investment, commodity",entity
RFS MM Bank Baskets,"rules, feature, system, product, concept",entity
Remove Group,"command, action, operation, feature, tool",entity
TEX entity,"organization, structure, component, concept",entity
individual currencies,"money, asset, value, unit, medium",entity
company's Bank Baskets,"financial product, investment, portfolio, product, offering, service",entity
enhancement,"improvement, feature, upgrade, addition, change",entity
entity tab,"ui element, interface, component, feature, section",entity
taskbar,"interface, element, component, feature, software",entity
'BRIDGE ADMINISTRATION',"department, management, organization, service, unit",entity
'Administration Start' page,"screen, interface, element, view",entity
configuration of separate baskets,"arrangement, structure, grouping, system, model",entity
trade-on-behalf entities,"agent, party, organization, unit",entity
many rules,"regulation, guideline, policy, system, framework, concept",entity
SEP requests,"inquiry, submission, document, filing, transaction",entity
TEX main entity,"entity, organization, component, unit",entity
versions,"release, edition, iteration, update, form",entity
blocked symbol,"icon, sign, indicator, mark, graphic",entity
white background,"background, color, attribute, visual",entity
group of products,"category, collection, items, goods, assortment",entity
tabs,"ui element, control, section, component, navigation",entity
one single rule,"rule, principle, guideline, policy, standard",entity
Configuration Groups tab,"element, component, interface, feature",entity
FX Time Period Groups,"group, parameter, category, concept, setting",entity
User Guide,"guide, manual, document, instructions, resource",entity
removed group,"group, collection, setting, parameter, data, entity",entity
PRIOR WRITTEN APPROVAL,"consent, permission, authorization, requirement, document",entity
Blocked Providers,"groups, entities, organizations, parties",entity
Orders,"transaction, request, instruction, process, area",entity
active task/sheet,"item, object, element, unit, entry, selection",entity
requests,"action, message, command, query, input",entity
'Wizards',"feature, tool, option, component",entity
white,"color, shade, attribute, concept",entity
white outline,"border, line, shape, visual, feature, element",entity
group of currencies,"asset class, collection, category, financial instrument, monetary system",entity
trade-as entities,"business, company, name, concept, organization",entity
logo,"symbol, mark, design, graphic, emblem",entity
configuration tools,"software, utility, program, application, component",entity
group,"collection, set, category, cluster, unit",entity
entities,"concept, thing, object, item, element",entity
range of maturities,"investment term, financial concept, time period, duration, set, concept",entity
Bridge Administration,"module, function, process, management, feature",entity
periods,"time, duration, interval, measurement, span",entity
alphanumeric value,"value, data, input, string, text",entity
entity,"concept, item, object, record, element",entity
panel,"section, group, element, component",entity
360T platform,"platform, system, software, service, exchange",entity
other configuration tools,"software, utility, program, application",entity
Figure 17,"diagram, illustration, image, visual, representation",entity
creation of groups,"process, action, formation, activity, operation",entity
particular user,"individual, person, client, entity",entity
Currencies,"money, unit, asset, system, finance",entity
single currencies,"money, asset, instrument, concept, value",entity
Order Spot,"product, transaction, instrument, trade, service",entity
FX Spot,"transaction, currency, exchange, financial product, market",entity
360T Bridge Administration tool,"tool, software, system, application",entity
maturity ranges,"range, period, classification",entity
tool,"object, device, implement, utility, instrument",entity
RFS Requester,"software, system, tool, program, component",entity
NDS,"fx product, swap, derivative, financial product, instrument, contract",entity
drop down list,"list, menu, control, element, widget, component",entity
SEP Bank Basket areas,"category, segment, domain, grouping",entity
Blocked Provider,"status, participant, entry, record, designation",entity
360T,"application, software, system, platform",entity
Interest Rate Swap,"swap, product, derivative, agreement, contract, tool",entity
user manual,"document, guide, instruction, publication, information",entity
product groups,"group, category, collection, data, structure",entity
start values,"value, data, point, initial point, boundary",entity
TRADE SECRETS,"information, property, asset, concept, data",entity
homepage,"page, webpage, interface, portal, dashboard",entity
available parameters,"settings, options, variables, attributes, elements",entity
Individual custom rules,"guidelines, policies, standards, criteria, parameters",entity
active homepage icon,"icon, symbol, graphic, element, button",entity
request types,"categories, classification, concept, information, data",entity
Options,"product, instrument, derivative, contract, security",entity
selection,"process, choice, decision, action, option",entity
CapFloor,"product, instrument, derivative, contract",entity
desired name,"word, text, input, identifier, label",entity
currency groups,"group, category, concept, classification, set",entity
groups,"collection, set, category, unit",entity
default Product Group,"group, category, setting, parameter, collection",entity
Order,"request, command, instruction, transaction, process, document",entity
new values,"concept, principle, item",entity
Currency Groups,"parameter, category, collection, setting, concept",entity
relationship,"connection, bond, link, concept, tie",entity
Highlighting a currency,"action, operation, selection, function",entity
two arrows,"symbol, graphic, shape, element",entity
Bank Basket Configurations,"configuration, setting, system, framework, arrangement",entity
This feature,"function, tool, component, capability, element",entity
Block Trades,"trade, transaction, instrument, product, deal",entity
rules,"concept, principle, guideline, regulation, standard, policy",entity
configuration groups,"group, set, arrangement, category",entity
configurations,"settings, options, parameters, data, setup",entity
Configuration Group icons,"graphic, symbol, element, component, feature",entity
Order Bank Baskets,"system, feature, configuration, tool, module",entity
bank basket setups,"system, portfolio, arrangement, rules, product",entity
Currency Couple Group,"category, concept, term, structure",entity
single-click,"action, interaction, method, input",entity
Rename Group,"function, operation, feature, utility, command",entity
Deposit,"money, payment, transaction, fund, asset",entity
options,"choices, selections, types, categories, parameters, alternatives",entity
Products,"goods, items, offerings, output, assets",entity
active institution,"institution, organization, entity, body",entity
FRA,"product, agreement, contract, derivative, instrument",entity
complex bank basket rules,"rules, regulations, policies, guidelines, criteria",entity
Configuration Group area,"section, panel, component, interface, element",entity
institutions,"organization, establishment, body, group, entity",entity
section,"part, division, segment, component, category",entity
search field,"input, control, element, component, box",entity
'Regulatory Data',"data, information, records, compliance, regulation, rules",entity
MM time period groups,"group, category, data, setting, classification",entity
360 TREASURY SYSTEMS AG,"company, organization, business, corporation, provider",entity
tenors,"maturity, duration, period, time, length, concept",entity
Supersonic (SEP),"system, service, program, platform, feature",entity
request type,"category, classification, attribute, group, parameter",entity
EMS application,"software, program, tool, system, utility",entity
maturities,"term, duration, period, time",entity
name,"label, identifier, word, attribute, concept",entity
product types,"categories, products, items, groups, classifications",entity
Forwards,"product, instrument, contract, derivative, transaction, agreement, finance",entity
base currency,"currency, money, asset, standard, unit",entity
individual Provider Groups,"groups, organizations, providers, entities",entity
product(s),"item, good, offering, merchandise, commodity",entity
product group,"category, classification, segment, collection",entity
Figure 2,"figure, diagram, illustration, image, graphic",entity
currency group,"category, financial term, economic concept, classification",entity
text,"writing, content, information, message, document",entity
configuring complex bank basket rules,"process, operation, policy, management, system",entity
system,"framework, structure, mechanism, concept, entity",entity
all product types,"category, goods, items, assortment",entity
creating rules,"regulation, policy, governance, management, formation",entity
<EMAIL>,"email, address, contact, identifier, string, data",entity
institution tree,"structure, hierarchy, model, concept",entity
preferences options,"settings, choices, configuration, feature, element",entity
available shortcuts,"links, options, controls, features, elements, access",entity
Provider Groups,"organization, entity, network, unit",entity
FX Time Period Group,"group, category, setting, parameter, definition",entity
active task,"task, activity, process, item",entity
Bridge Administration feature,"module, tool, software, component",entity
users,"people, individuals, participants, members, accounts",entity
improved rule management capabilities,"functionality, feature, ability, control, system",entity
DEUTSCHE BÖRSE GROUP,"company, corporation, exchange, institution, business, group",entity
unsaved changes,"data, edits, information, content, state",entity
selected item,"object, element, task, component, focus",entity
Bank Basket configuration,"setting, parameter, feature, module, tool",entity
single arrow,"symbol, indicator, pointer, icon",entity
desired currency,"money, value, payment, asset, financial instrument",entity
buckets of currency pairs,"collection, group, asset, instrument, category",entity
main entity,"principal, root, component, concept",entity
Bank Baskets quick link,"link, feature, element, control",entity
360T Bank Baskets,"product, instrument, feature, concept",entity
RFS FX Bank Baskets,"product, service, system, instrument, grouping",entity
RFS Commodity Bank Baskets area,"financial concept, market segment, product group, banking service, trading platform, financial instrument",entity
currencies,"money, asset, unit, system, exchange",entity
RFS FX Bank Baskets area,"category, segment, domain, concept, structure",entity
"request type (RFS, Order, SEP)","category, classification, grouping, attribute, designation",entity
RFS REQUESTER,"role, agent, system, party",entity
THIS FILE,"information, document, data, record",entity
Provider,"service, supplier, vendor, organization, entity",entity
Orders requests,"query, directive, communication, transaction",entity
rule,"regulation, guideline, principle, standard, policy",entity
tree,"structure, hierarchy, system, model",entity
Selected,"state, group, list, area, designation",entity
SEP trading,"trading, market, finance, commerce",entity
set of rules,"regulations, guidelines, principles, code, framework, system",entity
Provider Group,"organization, network, entity, association, collective",entity
corresponding user rights,"entitlements, privileges, provisions, concepts, rules",entity
green rectangle,"shape, figure, graphic, element",entity
360T Bank Baskets Configuration user guide,"guide, document, information, instructions, publication",entity
Currency Group name,"name, label, identifier, category, term",entity
set of icons,"collection, graphic, element, symbol, visual",entity
new currency,"currency, money, asset, instrument, finance",entity
SEP Bank Baskets,"financial product, investment, banking service, financial instrument, asset",entity
Providers,"supplier, vendor, participant, actor, entity",entity
double arrows,"symbol, icon, indicator, control",entity
this tab,"section, interface, component, element, area",entity
"RFS, Orders or SEP","request, document, record, item, term",entity
trade-as,"entity, concept, designation, term",entity
default group,"category, collection, setting, structure",entity
toggle option,"setting, control, feature, element",entity
various groups,"entity, collection, organization, ensemble",entity
providers,"supplier, vendor, company, organization, entity",entity
"""Bank Baskets"" quick link","link, button, element, feature, control, navigation",entity
Create Group,"feature, function, action, option, command",entity
selected tree item,"node, ui element, data item, component, object",entity
Bank Basket Product Groups,"groups, categories, products, offerings, finance",entity
trade-on-behalf,"entity, concept, arrangement, method",entity
configured rules,"setting, policy, regulation, guideline, standard",entity
customer relationship manager,"role, manager, profession, employee, person",entity
relevant products,"goods, items, offerings, selections",entity
scroll from source,"element, control, feature, function, command, option",entity
navigation panel,"panel, component, interface, element, section",entity
Configuration Groups,"group, feature, tool, concept",entity
institution,"organization, establishment, body, entity, system",entity
single rule for group of currencies,"rule, regulation, policy, guideline, standard",entity
360,"number, value, figure, code",entity
currency couple,"pair, instrument, concept, term, grouping",entity
"text ""360T""","string, label, identifier, data, code",entity
save changes,"action, command, function, operation, feature",entity
interest rate products,"product, financial product, financial instrument, derivative, asset, security",entity
quote currency,"currency, money, asset, finance, value, unit",entity
interface,"software, system, tool, component, feature",entity
'RFS REQUESTER',"role, user, system, component, category",entity
relevant administrative rights,"permission, authority, access, control, privilege, authorization",entity
rule creation,"process, development, regulation, policy, structuring",entity
All currencies,"money, finance, economy, asset, exchange medium",entity
user guide,"manual, document, instructions, information, resource",entity
Figure 16,"diagram, chart, image, graphic, representation, visual aid",entity
centralized management,"management, control, system, process, administration",entity
currency,"money, payment, value, financial instrument, exchange medium",entity
FX time period groups,"group, category, data, structure, component",entity
Bridge Administration Homepage,"page, website, interface, document, software",entity
data tabs,"tab, feature, component, element, interface",entity
rule creation for interest rate products,"process, regulation, finance, policy, procedure",entity
RFS Commodity Bank Baskets,"product, financial product, investment, asset, instrument",entity
Bridge Administration tool,"software, application, utility, system",entity
'Change Request',"request, document, form, process, item",entity
FX products,"products, financial products, currency instruments, investment tools, derivatives",entity
rounded corners,"shape, design, feature, element, attribute",entity
help options,"option, feature, element, function",entity
users with less complex bank basket setups,"people, customers, clients, individuals, account holders",entity
values,"principles, standards, ideals, attributes, data",entity
screen header,"ui element, component, interface, element, section, display",entity
T,"letter, character, symbol, mark, design",entity
each parameter,"setting, value, variable, property, data",entity
screen,"display, monitor, hardware, component",entity
different configuration tools,"software, utility, program, application, system",entity
green button,"control, element, component, object",entity
Groups,"collection, category, parameter, setting, component",entity
1 WEEK,"duration, time, period, unit",entity
edit name of existing group,"action, modification, function, feature, setting",entity
multiple forms/sheets,"document, interface, file, object",entity
RFS configuration,"configuration, setting, arrangement, system, parameter",entity
Bank Basket areas,"section, category, module, domain",entity
"""Discard all changes"" button","button, control, element, feature",entity
temporary blocks,"restriction, limitation, measure, state",entity
0,"number, digit, value, symbol, integer",entity
Bank Basket rules,"rules, regulations, policy, framework",entity
NDF,"product, instrument, financial product, derivative, swap",entity
Tenors,"term, period, concept, measure, duration",entity
THIRD PARTY,"entity, person, group, party, outsider, recipient",entity
delete group,"action, operation, function, command, feature",entity
rule management capabilities,"function, feature, system, process, tool, concept",entity
Save,"action, command, function, operation",entity
SEP,"standard, system, protocol, framework, scheme",entity
Bank Basket configuration details,"information, settings, form",entity
Energy Asian Swaps,"derivative, contract, product, instrument",entity
overlapping sets of currencies,"finance, grouping, arrangement, concept",entity
SEP streaming spot executions,"transaction, process, system, operation, activity",entity
left side of the homepage,"section, area, webpage, interface, element",entity
A currency,"money, value, exchange, payment, asset, unit",entity
MM Time Period Groups,"group, category, parameter, classification, set",entity
Forward orders,"order, transaction, agreement, instruction, financial product",entity
RFS requests,"request, communication, transaction, process",entity
all products,"goods, items, offerings, merchandise, commodities, things",entity
RFS,"request, type, term, abbreviation",entity
Users,"people, individuals, customers, participants, consumers",entity
ITEX entities,"component, unit, group, type",entity
new form/sheet,"document, page, interface, screen, element",entity
changes,"modification, alteration, adjustment, update, edit",entity
user,"person, individual, client, consumer, participant",entity
PROPRIETARY AND CONFIDENTIAL INFORMATION,"intellectual property, data, secrets, assets, knowledge",entity
Select Member for 'FX Spot and Forward',"section, control, setting, option, field",entity
top of the screen,"area, location, interface, display, section",entity
separate baskets,"category, group, container, division, segment",entity
removed or renamed,"action, change, modification, operation, alteration",entity
ISO code,"code, standard, identifier, symbol, data",entity
individual product types,"item, good, merchandise, category",entity
DEAL TRACKING,"process, function, management, system, operation",entity
time period,"duration, interval, span, segment",entity
products,"goods, items, merchandise, offerings, wares",entity
ability to apply and remove temporary blocks,"capability, functionality, feature, control, management",entity
single TEX entity,"node, element, object, item, concept",entity
customized groups,"category, collection, set, type",entity
available product types,"categories, options, varieties, classifications, groups",entity
'DEAL TRACKING',"feature, process, system, tool, management",entity
added or removed from,"change, alteration, modification, update, adjust, reconfigure",relation
before,"temporal, sequence, precedence, prior, earlier",relation
displays,"show, exhibit, present, reveal, manifest",relation
based on,"foundation, origin, derivation, source, reliance",relation
include,"contain, comprise, encompass, incorporate",relation
is of,"relation, attribute, origin, composition, characteristic",relation
have,"possess, own, contain, include, experience",relation
configures,"set up, customize, define, prepare, arrange",relation
shows,"display, exhibit, reveal, indicate, demonstrate, convey, express, make known, point out",relation
preferable for,"good for, suitable for, ideal for, beneficial for, better for",relation
cannot be used on,"restriction, incompatibility, unsuitability, prohibition, exclusion",relation
set,"place, establish, arrange, determine, create",relation
replaced by,"substituted, succeeded, supplanted, superseded, exchanged for",relation
at the same time,"simultaneous, concurrent, synchronous, together, coincident",relation
has,"possess, contain, feature, comprise, exhibit",relation
displays text,"show, convey, render, output",relation
from,"source, origin, start, derivation, separation",relation
selected by,"chosen, picked, appointed, elected, designated",relation
enhanced to provide,"upgrade, optimize, enable, facilitate",relation
simplifies,"ease, clarify, streamline, uncomplicate, facilitate",relation
used in,"application, employ, function, utility, purpose",relation
facilitate,"help, enable, support, aid, ease",relation
for,"purpose, intended for, on behalf, due to, in return",relation
classify,"categorize, sort, group, organize, label",relation
apply for,"request, seek, solicit, submit",relation
is part of,"inclusion, component of, belongs to, member of, subset of",relation
allow setting,"grant permission, permit adjustment, enable configuration, offer options",relation
reused for,"recycle, repurpose, reclaim, re-employ, reapply",relation
as a result,"outcome, consequence, effect, causation, product",relation
are for,"purpose, intent, function, support, beneficiary",relation
useful for,"purpose, utility, benefit, function, application",relation
opens,"starts, initiates, unlocks, reveals, establishes",relation
manages,"control, oversees, administers, directs, leads",relation
depicts,"show, represent, portray, illustrate, describe",relation
opens to,"reveal, lead to, provide access, allow entry, expose",relation
allow user to,"permit, enable, grant, provide, authorize",relation
not added to,"exclude, omit, separate, lack, withhold",relation
defined as,"identify, characterize, specify, explain, describe",relation
configured under,"controlled by, part of, subordinate to, managed by, contained in",relation
cannot be,"impossible, excluded, prohibited, denied, unattainable",relation
located in,"position, situated, contained, placement, resides",relation
configured in,"arrangement, setup, structure, defined as, organized into",relation
includes,"contain, comprise, encompass, incorporate, has",relation
configured by,"set by, determined by, arranged by, defined by",relation
do not affect,"no impact, be independent, remain unchanged, have no bearing, not alter",relation
lead to,"cause, result, produce, effect, generate",relation
edited without changing,"proofread, review, check, verify, examine",relation
accessed via,"method, through, means, path, channel",relation
has options for,"offers, choices, provides, allows, alternatives",relation
modified by removing,"removal, alteration, deletion, reduction, exclusion",relation
utilizes,"use, employ, apply, leverage, access",relation
describes,"explains, defines, characterizes, portrays, details",relation
is participated by,"activity, event, subject, focus, target",relation
contains,"holds, includes, has, encloses, comprises",relation
available to,"access to, for use, provided for, can obtain, open for",relation
allow setting of,"permit, enable, define, specify, configure",relation
not included in,"exclude, omit, separate from, not part, left out",relation
allow creation of,"enable, facilitate, authorize, make possible, support",relation
because,"cause, reason, explanation, grounds",relation
allow blocking of,"enable restriction, permit prevention, authorize stopping, grant control, regulate access",relation
altered in,"change, modified, transformed, varied, adjusted",relation
can set up,"establish, create, form, organize, initiate",relation
titled,"name, designate, label, identify, classify",relation
found within,"located in, contained in, present in, inside, exists in",relation
administer,"manage, govern, oversee, direct, control",relation
allow classification of,"categorizes, defines type, groups, identifies class, enables sorting",relation
has option for,"choice, alternative, availability, selection, provision",relation
available on,"located at, accessible via, obtainable from, present at, supplied by",relation
