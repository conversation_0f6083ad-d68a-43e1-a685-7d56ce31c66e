{"custom_id": "concept_0", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The image shows a logo for \"360T\", which is a green rectangle with rounded corners.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_1", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"green rectangle\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_2", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"rounded corners\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_3", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The text \"360\" is in white with a green outline.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_4", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"green outline\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_5", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The \"T\" is also in white with a green outline.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_6", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"There are two arrows pointing towards each other in the middle of the \"0\" in \"360\".\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_7", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"two arrows\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_8", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_9", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"THIS FILE\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_10", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"PROPRIETARY AND CONFIDENTIAL INFORMATION\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_11", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"TRADE SECRETS\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_12", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_13", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"THIRD PARTY\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_14", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"PRIOR WRITTEN APPROVAL\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_15", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"360 TREASURY SYSTEMS AG\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_16", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"user manual\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_17", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket feature\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_18", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"360T Bridge Administration tool\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_19", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"improved rule management capabilities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_20", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"configuration groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_21", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"currency\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_22", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"currency couple\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_23", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"time period\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_24", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"product(s)\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_25", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"configuration of separate baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_26", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"separate baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_27", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"request type (RFS, Order, SEP)\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_28", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"ability to apply and remove temporary blocks\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_29", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"temporary blocks\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_30", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"configured rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_31", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"counterpart relationship(s)\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_32", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"360T enhanced Bank Basket feature\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_33", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"entities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_34", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"EMS application\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_35", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bridge application\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_36", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"corresponding user rights\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_37", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"company's Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_38", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"<EMAIL>\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_39", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"relevant administrative rights\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_40", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"customer relationship manager\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_41", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket configuration\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_42", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bridge Administration tool\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_43", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bridge Administration\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_44", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"menu option \"Administration\"\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_45", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"screen header\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_46", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bridge Administration feature\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_47", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"homepage\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_48", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"available shortcuts\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_49", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"different configuration tools\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_50", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"particular user\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_51", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"quick navigation toolbar\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_52", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"active homepage icon\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_53", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"left side of the homepage\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_54", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"screenshot\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_55", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"360T Bank Baskets Configuration user guide\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_56", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"screen\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_57", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'Administration Start' page\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_58", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'Regulatory Data'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_59", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'Bank Baskets'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_60", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'Change Request'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_61", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'Wizards'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_62", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'Evaluator Tools'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_63", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"top of the screen\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_64", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'RFS REQUESTER'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_65", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'DEAL TRACKING'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_66", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"'BRIDGE ADMINISTRATION'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_67", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"preferences options\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_68", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"help options\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_69", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_70", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Bank Basket feature has been enhanced.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_71", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The enhancement provides improved rule management capabilities.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_72", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"enhancement\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_73", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"rule management capabilities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_74", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_75", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Separate baskets can be configured by request type.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_76", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"request type\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_77", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Request types include RFS.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_78", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"request types\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_79", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Request types include Order.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_80", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Request types include SEP.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_81", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Temporary blocks can be applied.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_82", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Temporary blocks can be removed.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_83", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Applying temporary blocks does not affect the configured rules.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_84", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Figure 2\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_85", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bridge Administration Homepage\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_86", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"\"Bank Baskets\" quick link\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_87", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"navigation panel\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_88", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"institution tree\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_89", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"single TEX entity\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_90", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"TEX main entity\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_91", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"trade-as entities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_92", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"trade-on-behalf entities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_93", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The \"Bank Baskets\" quick link opens a navigation panel which contains an institution tree.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_94", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Baskets quick link\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_95", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_96", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"TEX entity\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_97", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"trade-as\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_98", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"trade-on-behalf\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_99", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"ITEX entities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_100", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"main entity\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_101", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_102", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"institution\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_103", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"single-click\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_104", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"new form/sheet\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_105", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket configuration details\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_106", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"entity\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_107", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"It is possible to open multiple forms/sheets at a time.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_108", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"multiple forms/sheets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_109", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The selected item will be highlighted as an active task inside the taskbar.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_110", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"selected item\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_111", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"active task\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_112", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"taskbar\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_113", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_114", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"set of icons\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_115", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A search field will open and the user can type in an alphanumeric value in order to find the desired institution.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_116", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"search field\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_117", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"alphanumeric value\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_118", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_119", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"This feature\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_120", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"active task/sheet\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_121", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_122", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"selected tree item\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_123", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"active institution\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_124", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"scroll from source\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_125", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"This icon is deactivated when using the Bank Basket configuration.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_126", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"This icon\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_127", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_128", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"other configuration tools\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_129", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"toggle option\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_130", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"individuals\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_131", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Each entity tab has a Live Audit Log which tracks all unsaved changes.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_132", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Individual unsaved changes can be reverted by clicking on the arrow icon.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_133", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Clicking on the \"Discard all changes\" button will revert all unsaved changes.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_134", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_135", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_136", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Configuration Groups allow users to create a group one single time and reuse it across various rules.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_137", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The toggle option allows the user to display only institutions in the navigation panel.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_138", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"institutions\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_139", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"configuration tools\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_140", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_141", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"minimize icon\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_142", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"entity tab\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_143", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Live Audit Log\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_144", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"unsaved changes\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_145", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"arrow icon\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_146", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"\"Discard all changes\" button\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_147", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The image contains the text 'User Guide 360T Bank Baskets Configuration'.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_148", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"text 'User Guide 360T Bank Baskets Configuration'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_149", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Configuration Groups tab\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_150", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"data tabs\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_151", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_152", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Configuration Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_153", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"parameters\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_154", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Bank Basket rules for four separate request types are configured on this tab.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_155", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_156", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"this tab\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_157", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS FX Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_158", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS MM Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_159", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS Commodity Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_160", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS Cross Currency Netting Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_161", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_162", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Provider Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_163", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Blocked Providers\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_164", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Orders\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_165", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Order Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_166", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Bank Basket rules for Forward or Spot orders are configured on this tab.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_167", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Forward orders\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_168", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Spot orders\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_169", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_170", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Supersonic (SEP)\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_171", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"SEP Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_172", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Bank Basket rules for SEP streaming spot executions are configured on this tab.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_173", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"SEP streaming spot executions\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_174", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_175", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket Configurations\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_176", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS requests\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_177", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Orders requests\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_178", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"SEP requests\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_179", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"An RFS configuration will have no impact on SEP trading.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_180", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS configuration\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_181", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"SEP trading\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_182", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"centralized management\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_183", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_184", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"each parameter\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_185", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"creating rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_186", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_187", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"set of rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_188", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"available parameters\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_189", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Currency Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_190", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Currency Couple Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_191", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"FX Time Period Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_192", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"MM Time Period Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_193", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"create new group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_194", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"edit name of existing group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_195", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"delete group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_196", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"save changes\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_197", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Rename Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_198", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Default Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_199", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Remove Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_200", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"removed group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_201", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"configuring complex bank basket rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_202", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"individual custom rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_203", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Individual custom rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_204", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"users with less complex bank basket setups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_205", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket Configuration\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_206", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"existing values\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_207", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Default Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_208", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"values\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_209", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"new values\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_210", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"new currency\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_211", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Default Currency Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_212", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"single currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_213", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"single rule for group of currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_214", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_215", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"currency group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_216", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"rule creation for interest rate products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_217", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"interest rate products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_218", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Deposit\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_219", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Interest Rate Swap\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_220", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"CapFloor\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_221", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Configuration Groups facilitate centralized management of parameters.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_222", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_223", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"requests\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_224", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The groups themselves can be edited without changing a set of rules based on those groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_225", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_226", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_227", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Configuration Group area\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_228", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"changes\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_229", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Create Group adds a new group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_230", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Create Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_231", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Rename Group changes the name of a group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_232", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Rename Group cannot be used on the Default Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_233", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Remove Group deletes an individual group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_234", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Remove Group cannot be used on the Default Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_235", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"If the removed group is used in any configured rules this group is replaced by the Default Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_236", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Users are reminded to save changes to configurations.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_237", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"configurations\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_238", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Configuration Groups are particularly useful when configuring complex bank basket rules.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_239", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"complex bank basket rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_240", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"It is not required to configure groups based on the above parameters.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_241", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Users may still set individual custom rules without utilizing the Configuration Groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_242", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Individual custom rules may be preferable for some users with less complex bank basket setups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_243", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"bank basket setups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_244", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_245", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Default Group will include all existing values.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_246", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"All Default Groups can be modified.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_247", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_248", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"versions\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_249", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_250", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"360T platform\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_251", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The new currency must be selected by the user.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_252", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currency Groups are intended to allow the classification of single currencies into customized groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_253", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"customized groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_254", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"This allows setting one single rule for each group of currencies rather than many rules for individual currencies.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_255", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"group of currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_256", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"individual currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_257", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currencies can be added or removed from the group without editing the rules themselves.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_258", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The image shows a user interface for managing currency groups in a financial application.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_259", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A currency is highlighted with a single-click.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_260", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The single arrow is activated.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_261", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Clicking the single arrow moves the desired currency.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_262", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A user clicks Create Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_263", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A user types the desired name.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_264", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A user clicks Create Group again.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_265", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A user clicks Save.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_266", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A user clicks on the Currency Group name.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_267", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The currencies configured for the group are viewed.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_268", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A Currency Couple Group is created.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_269", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Currency Couple Group can be used to simplify rules.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_270", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currencies may be added or removed from the default group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_271", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"default group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_272", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"A currency\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_273", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Highlighting a currency activates the single arrow.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_274", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Highlighting a currency\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_275", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"single arrow\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_276", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_277", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"desired currency\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_278", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Available\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_279", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Selected\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_280", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"All currencies can be moved in either direction by using the double arrows.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_281", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"All currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_282", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"double arrows\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_283", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_284", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"new group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_285", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"desired name\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_286", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currencies configured for the group can be viewed by clicking on the Currency Group name.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_287", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_288", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Currency Group name\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_289", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The same currency can be added to many different groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_290", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The system does not restrict the creation of groups with overlapping sets of currencies.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_291", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"system\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_292", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"creation of groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_293", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"overlapping sets of currencies\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_294", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currency Couple Groups allow the creation of \"buckets\" of currency pairs.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_295", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"buckets of currency pairs\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_296", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_297", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Currency Couple Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_298", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"FX products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_299", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS FX Bank Baskets area\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_300", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"FX Spot\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_301", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Forwards\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_302", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Options\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_303", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Block Trades\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_304", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Energy Asian Swaps\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_305", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bullet Swaps\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_306", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS Commodity Bank Baskets area\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_307", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Order Spot\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_308", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Orders Bank Basket area\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_309", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS REQUESTER\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_310", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"DEAL TRACKING\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_311", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Default Group contains all currency pairs.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_312", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"currency pairs\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_313", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"base currency\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_314", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"quote currency\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_315", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currency Couple Groups can be created.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_316", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Configuration Group icons\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_317", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currency Couple Groups can be renamed.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_318", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Currency Couple Groups can be removed.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_319", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A user can add currency pairs within a group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_320", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Add Currency Couple button is clicked.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_321", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Add Currency Couple button\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_322", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The ISO code is chosen in the drop down list.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_323", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"ISO code\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_324", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"drop down list\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_325", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The desired currency is typed.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_326", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The selection is confirmed by clicking the green check mark.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_327", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"selection\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_328", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"green check mark\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_329", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Save is clicked.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_330", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Products with varying maturities or tenors may be configured into maturity ranges.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_331", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_332", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"maturities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_333", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"tenors\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_334", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"maturity ranges\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_335", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"An FX Time Period Group can simplify rules for FX products.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_336", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"FX Time Period Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_337", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Add FX Time Period button is clicked.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_338", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The desired time period is chosen.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_339", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Save button is clicked.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_340", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Add MM Time Period button is clicked.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_341", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The desired time period is selected.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_342", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"An MM Time Period Group is created.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_343", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Rules for interest rate products can be simplified.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_344", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The same tenors are used in various groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_345", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"They are used for different sets of rules.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_346", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Tenors\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_347", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"range of maturities\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_348", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"various groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_349", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Product Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_350", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"product types\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_351", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"single rule\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_352", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"product group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_353", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"rule creation\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_354", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"all products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_355", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"removed or renamed\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_356", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"all product types\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_357", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"relevant products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_358", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS, Orders or SEP\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_359", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"default Product Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_360", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket areas\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_361", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"individual Provider Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_362", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"providers\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_363", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"user interface\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_364", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"360T Bank Baskets\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_365", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"RFS Requester\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_366", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Deal Tracking\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_367", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"interface\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_368", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"currency groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_369", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"FX time period groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_370", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"MM time period groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_371", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"product groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_372", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"section\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_373", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Select Member for 'FX Spot and Forward'\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_374", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"available product types\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_375", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"selected product types\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_376", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Figure 16\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_377", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket Product Groups\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_378", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Figure 17\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_379", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Bank Basket Product Groups Create Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_380", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"green button\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_381", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"User Guide\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_382", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"DEUTSCHE B\u00d6RSE GROUP\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_383", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Product Groups are intended to allow the classification of product types into customized groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_384", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"One single rule can be set for each group of products rather than many rules for individual product types.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_385", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A new group is created by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_386", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A product group can be used to simplify rule creation for all relevant product types.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_387", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A default group exists which includes all products.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_388", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The default group cannot be removed or renamed.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_389", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The product types in the default group can be altered.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_390", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Default Group contains all product types across RFS, Orders and SEP.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_391", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_392", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Products can be added or removed from a product group without editing the rules themselves.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_393", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_394", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"periods\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_395", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"OVERNIGHT\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_396", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"1 WEEK\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_397", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"1 MONTH\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_398", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"6 MONTHS\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_399", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Tenors are defined as a range of maturities, with both start and end values included.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_400", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"start values\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_401", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"end values\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_402", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The same tenors may be used in various groups in order to be used for different sets of rules.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_403", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"sets of rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_404", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"This allows setting one single rule for each group of products rather than many rules for individual product types.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_405", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"one single rule\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_406", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"group of products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_407", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"many rules\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_408", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"individual product types\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_409", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The image shows a user interface for configuring 360T Bank Baskets.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_410", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"It includes options for RFS Requester, Deal Tracking, and Bridge Administration.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_411", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_412", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A section titled \"Select Member for 'FX Spot and Forward'\" is visible, with options for available and selected product types.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_413", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"options\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_414", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Products can be added or removed from the group without editing the rules themselves.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_415", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_416", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"SEP Bank Basket areas\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_417", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_418", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"products\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_419", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"This group cannot be removed or renamed.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_420", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"However, the product types in the group can be altered.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_421", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_422", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_423", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The image shows a green button with the text \"360T\" on it.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_424", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"text \"360T\"\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_425", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The button has a white outline and a white background.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_426", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"button\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_427", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"white outline\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_428", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"white background\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_429", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The text is in a stylized font.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_430", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"stylized font\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_431", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Providers are temporarily blocked from particular request types.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_432", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A Blocked Provider will remain in a Provider Group but will appear with a \"blocked\" symbol.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_433", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A Provider is removed completely and its relationship is rejected using the Counterpart Relationship Management tool.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_434", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Please refer to the relevant user guide.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_435", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The Provider Groups can be edited.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_436", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Providers may be temporarily blocked from particular request types.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_437", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Providers\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_438", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Provider Group\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_439", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A Blocked Provider will remain in a Provider Group.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_440", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Blocked Provider\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_441", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A Blocked Provider will appear with a blocked symbol.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_442", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"blocked symbol\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_443", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"A Provider should be removed completely.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_444", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Provider\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_445", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"The relationship should be rejected using the Counterpart Relationship Management tool.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_446", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"relationship\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_447", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"Counterpart Relationship Management tool\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_448", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this event: \"Refer to the relevant user guide.\"\n\nGenerate 3-5 high-level conceptual categories that this event belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the event is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the event is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n{"custom_id": "concept_449", "method": "POST", "url": "/v1alpha/models/gemini-2.5-flash:generateContent", "body": {"contents": [{"parts": [{"text": "Given this entity: \"user guide\"\n\nGenerate 3-5 high-level conceptual categories that this entity belongs to. \nFocus on abstract concepts, themes, and domains rather than specific details.\n\nExamples:\n- If the entity is about \"user clicking a button\" \u2192 concepts might be: \"interaction, interface, action, engagement\"\n- If the entity is about \"system processing data\" \u2192 concepts might be: \"computation, processing, automation, workflow\"\n\nProvide only the concepts as a comma-separated list without quotes or explanations."}]}], "generationConfig": {"maxOutputTokens": 100, "temperature": 0.3}}}\n