{"total_requests": 450, "nodes": [{"custom_id": "concept_0", "node": "The image shows a logo for \"360T\", which is a green rectangle with rounded corners.", "node_type": "event"}, {"custom_id": "concept_1", "node": "green rectangle", "node_type": "entity"}, {"custom_id": "concept_2", "node": "rounded corners", "node_type": "entity"}, {"custom_id": "concept_3", "node": "The text \"360\" is in white with a green outline.", "node_type": "event"}, {"custom_id": "concept_4", "node": "green outline", "node_type": "entity"}, {"custom_id": "concept_5", "node": "The \"T\" is also in white with a green outline.", "node_type": "event"}, {"custom_id": "concept_6", "node": "There are two arrows pointing towards each other in the middle of the \"0\" in \"360\".", "node_type": "event"}, {"custom_id": "concept_7", "node": "two arrows", "node_type": "entity"}, {"custom_id": "concept_8", "node": "THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS.", "node_type": "event"}, {"custom_id": "concept_9", "node": "THIS FILE", "node_type": "entity"}, {"custom_id": "concept_10", "node": "PROPRIETARY AND CONFIDENTIAL INFORMATION", "node_type": "entity"}, {"custom_id": "concept_11", "node": "TRADE SECRETS", "node_type": "entity"}, {"custom_id": "concept_12", "node": "THIS FILE MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG.", "node_type": "event"}, {"custom_id": "concept_13", "node": "THIRD PARTY", "node_type": "entity"}, {"custom_id": "concept_14", "node": "PRIOR WRITTEN APPROVAL", "node_type": "entity"}, {"custom_id": "concept_15", "node": "360 TREASURY SYSTEMS AG", "node_type": "entity"}, {"custom_id": "concept_16", "node": "user manual", "node_type": "entity"}, {"custom_id": "concept_17", "node": "Bank Basket feature", "node_type": "entity"}, {"custom_id": "concept_18", "node": "360T Bridge Administration tool", "node_type": "entity"}, {"custom_id": "concept_19", "node": "improved rule management capabilities", "node_type": "entity"}, {"custom_id": "concept_20", "node": "configuration groups", "node_type": "entity"}, {"custom_id": "concept_21", "node": "currency", "node_type": "entity"}, {"custom_id": "concept_22", "node": "currency couple", "node_type": "entity"}, {"custom_id": "concept_23", "node": "time period", "node_type": "entity"}, {"custom_id": "concept_24", "node": "product(s)", "node_type": "entity"}, {"custom_id": "concept_25", "node": "configuration of separate baskets", "node_type": "entity"}, {"custom_id": "concept_26", "node": "separate baskets", "node_type": "entity"}, {"custom_id": "concept_27", "node": "request type (RFS, Order, SEP)", "node_type": "entity"}, {"custom_id": "concept_28", "node": "ability to apply and remove temporary blocks", "node_type": "entity"}, {"custom_id": "concept_29", "node": "temporary blocks", "node_type": "entity"}, {"custom_id": "concept_30", "node": "configured rules", "node_type": "entity"}, {"custom_id": "concept_31", "node": "counterpart relationship(s)", "node_type": "entity"}, {"custom_id": "concept_32", "node": "360T enhanced Bank Basket feature", "node_type": "entity"}, {"custom_id": "concept_33", "node": "entities", "node_type": "entity"}, {"custom_id": "concept_34", "node": "EMS application", "node_type": "entity"}, {"custom_id": "concept_35", "node": "Bridge application", "node_type": "entity"}, {"custom_id": "concept_36", "node": "corresponding user rights", "node_type": "entity"}, {"custom_id": "concept_37", "node": "company's Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_38", "node": "<EMAIL>", "node_type": "entity"}, {"custom_id": "concept_39", "node": "relevant administrative rights", "node_type": "entity"}, {"custom_id": "concept_40", "node": "customer relationship manager", "node_type": "entity"}, {"custom_id": "concept_41", "node": "Bank Basket configuration", "node_type": "entity"}, {"custom_id": "concept_42", "node": "Bridge Administration tool", "node_type": "entity"}, {"custom_id": "concept_43", "node": "Bridge Administration", "node_type": "entity"}, {"custom_id": "concept_44", "node": "menu option \"Administration\"", "node_type": "entity"}, {"custom_id": "concept_45", "node": "screen header", "node_type": "entity"}, {"custom_id": "concept_46", "node": "Bridge Administration feature", "node_type": "entity"}, {"custom_id": "concept_47", "node": "homepage", "node_type": "entity"}, {"custom_id": "concept_48", "node": "available shortcuts", "node_type": "entity"}, {"custom_id": "concept_49", "node": "different configuration tools", "node_type": "entity"}, {"custom_id": "concept_50", "node": "particular user", "node_type": "entity"}, {"custom_id": "concept_51", "node": "quick navigation toolbar", "node_type": "entity"}, {"custom_id": "concept_52", "node": "active homepage icon", "node_type": "entity"}, {"custom_id": "concept_53", "node": "left side of the homepage", "node_type": "entity"}, {"custom_id": "concept_54", "node": "screenshot", "node_type": "entity"}, {"custom_id": "concept_55", "node": "360T Bank Baskets Configuration user guide", "node_type": "entity"}, {"custom_id": "concept_56", "node": "screen", "node_type": "entity"}, {"custom_id": "concept_57", "node": "'Administration Start' page", "node_type": "entity"}, {"custom_id": "concept_58", "node": "'Regulatory Data'", "node_type": "entity"}, {"custom_id": "concept_59", "node": "'Bank Baskets'", "node_type": "entity"}, {"custom_id": "concept_60", "node": "'Change Request'", "node_type": "entity"}, {"custom_id": "concept_61", "node": "'Wizards'", "node_type": "entity"}, {"custom_id": "concept_62", "node": "'Evaluator Tools'", "node_type": "entity"}, {"custom_id": "concept_63", "node": "top of the screen", "node_type": "entity"}, {"custom_id": "concept_64", "node": "'RFS REQUESTER'", "node_type": "entity"}, {"custom_id": "concept_65", "node": "'DEAL TRACKING'", "node_type": "entity"}, {"custom_id": "concept_66", "node": "'BRIDGE ADMINISTRATION'", "node_type": "entity"}, {"custom_id": "concept_67", "node": "preferences options", "node_type": "entity"}, {"custom_id": "concept_68", "node": "help options", "node_type": "entity"}, {"custom_id": "concept_69", "node": "This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.", "node_type": "event"}, {"custom_id": "concept_70", "node": "The Bank Basket feature has been enhanced.", "node_type": "event"}, {"custom_id": "concept_71", "node": "The enhancement provides improved rule management capabilities.", "node_type": "event"}, {"custom_id": "concept_72", "node": "enhancement", "node_type": "entity"}, {"custom_id": "concept_73", "node": "rule management capabilities", "node_type": "entity"}, {"custom_id": "concept_74", "node": "Configuration groups based on currency, currency couple, time period and product(s) have been introduced.", "node_type": "event"}, {"custom_id": "concept_75", "node": "Separate baskets can be configured by request type.", "node_type": "event"}, {"custom_id": "concept_76", "node": "request type", "node_type": "entity"}, {"custom_id": "concept_77", "node": "Request types include RFS.", "node_type": "event"}, {"custom_id": "concept_78", "node": "request types", "node_type": "entity"}, {"custom_id": "concept_79", "node": "Request types include Order.", "node_type": "event"}, {"custom_id": "concept_80", "node": "Request types include SEP.", "node_type": "event"}, {"custom_id": "concept_81", "node": "Temporary blocks can be applied.", "node_type": "event"}, {"custom_id": "concept_82", "node": "Temporary blocks can be removed.", "node_type": "event"}, {"custom_id": "concept_83", "node": "Applying temporary blocks does not affect the configured rules.", "node_type": "event"}, {"custom_id": "concept_84", "node": "Figure 2", "node_type": "entity"}, {"custom_id": "concept_85", "node": "Bridge Administration Homepage", "node_type": "entity"}, {"custom_id": "concept_86", "node": "\"Bank Baskets\" quick link", "node_type": "entity"}, {"custom_id": "concept_87", "node": "navigation panel", "node_type": "entity"}, {"custom_id": "concept_88", "node": "institution tree", "node_type": "entity"}, {"custom_id": "concept_89", "node": "single TEX entity", "node_type": "entity"}, {"custom_id": "concept_90", "node": "TEX main entity", "node_type": "entity"}, {"custom_id": "concept_91", "node": "trade-as entities", "node_type": "entity"}, {"custom_id": "concept_92", "node": "trade-on-behalf entities", "node_type": "entity"}, {"custom_id": "concept_93", "node": "The \"Bank Baskets\" quick link opens a navigation panel which contains an institution tree.", "node_type": "event"}, {"custom_id": "concept_94", "node": "Bank Baskets quick link", "node_type": "entity"}, {"custom_id": "concept_95", "node": "Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.", "node_type": "event"}, {"custom_id": "concept_96", "node": "TEX entity", "node_type": "entity"}, {"custom_id": "concept_97", "node": "trade-as", "node_type": "entity"}, {"custom_id": "concept_98", "node": "trade-on-behalf", "node_type": "entity"}, {"custom_id": "concept_99", "node": "ITEX entities", "node_type": "entity"}, {"custom_id": "concept_100", "node": "main entity", "node_type": "entity"}, {"custom_id": "concept_101", "node": "The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.", "node_type": "event"}, {"custom_id": "concept_102", "node": "institution", "node_type": "entity"}, {"custom_id": "concept_103", "node": "single-click", "node_type": "entity"}, {"custom_id": "concept_104", "node": "new form/sheet", "node_type": "entity"}, {"custom_id": "concept_105", "node": "Bank Basket configuration details", "node_type": "entity"}, {"custom_id": "concept_106", "node": "entity", "node_type": "entity"}, {"custom_id": "concept_107", "node": "It is possible to open multiple forms/sheets at a time.", "node_type": "event"}, {"custom_id": "concept_108", "node": "multiple forms/sheets", "node_type": "entity"}, {"custom_id": "concept_109", "node": "The selected item will be highlighted as an active task inside the taskbar.", "node_type": "event"}, {"custom_id": "concept_110", "node": "selected item", "node_type": "entity"}, {"custom_id": "concept_111", "node": "active task", "node_type": "entity"}, {"custom_id": "concept_112", "node": "taskbar", "node_type": "entity"}, {"custom_id": "concept_113", "node": "A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:", "node_type": "event"}, {"custom_id": "concept_114", "node": "set of icons", "node_type": "entity"}, {"custom_id": "concept_115", "node": "A search field will open and the user can type in an alphanumeric value in order to find the desired institution.", "node_type": "event"}, {"custom_id": "concept_116", "node": "search field", "node_type": "entity"}, {"custom_id": "concept_117", "node": "alphanumeric value", "node_type": "entity"}, {"custom_id": "concept_118", "node": "This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.", "node_type": "event"}, {"custom_id": "concept_119", "node": "This feature", "node_type": "entity"}, {"custom_id": "concept_120", "node": "active task/sheet", "node_type": "entity"}, {"custom_id": "concept_121", "node": "Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.", "node_type": "event"}, {"custom_id": "concept_122", "node": "selected tree item", "node_type": "entity"}, {"custom_id": "concept_123", "node": "active institution", "node_type": "entity"}, {"custom_id": "concept_124", "node": "scroll from source", "node_type": "entity"}, {"custom_id": "concept_125", "node": "This icon is deactivated when using the Bank Basket configuration.", "node_type": "event"}, {"custom_id": "concept_126", "node": "This icon", "node_type": "entity"}, {"custom_id": "concept_127", "node": "For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.", "node_type": "event"}, {"custom_id": "concept_128", "node": "other configuration tools", "node_type": "entity"}, {"custom_id": "concept_129", "node": "toggle option", "node_type": "entity"}, {"custom_id": "concept_130", "node": "individuals", "node_type": "entity"}, {"custom_id": "concept_131", "node": "Each entity tab has a Live Audit Log which tracks all unsaved changes.", "node_type": "event"}, {"custom_id": "concept_132", "node": "Individual unsaved changes can be reverted by clicking on the arrow icon.", "node_type": "event"}, {"custom_id": "concept_133", "node": "Clicking on the \"Discard all changes\" button will revert all unsaved changes.", "node_type": "event"}, {"custom_id": "concept_134", "node": "Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.", "node_type": "event"}, {"custom_id": "concept_135", "node": "Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS, Order, SEP).", "node_type": "event"}, {"custom_id": "concept_136", "node": "Configuration Groups allow users to create a group one single time and reuse it across various rules.", "node_type": "event"}, {"custom_id": "concept_137", "node": "The toggle option allows the user to display only institutions in the navigation panel.", "node_type": "event"}, {"custom_id": "concept_138", "node": "institutions", "node_type": "entity"}, {"custom_id": "concept_139", "node": "configuration tools", "node_type": "entity"}, {"custom_id": "concept_140", "node": "The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.", "node_type": "event"}, {"custom_id": "concept_141", "node": "minimize icon", "node_type": "entity"}, {"custom_id": "concept_142", "node": "entity tab", "node_type": "entity"}, {"custom_id": "concept_143", "node": "Live Audit Log", "node_type": "entity"}, {"custom_id": "concept_144", "node": "unsaved changes", "node_type": "entity"}, {"custom_id": "concept_145", "node": "arrow icon", "node_type": "entity"}, {"custom_id": "concept_146", "node": "\"Discard all changes\" button", "node_type": "entity"}, {"custom_id": "concept_147", "node": "The image contains the text 'User Guide 360T Bank Baskets Configuration'.", "node_type": "event"}, {"custom_id": "concept_148", "node": "text 'User Guide 360T Bank Baskets Configuration'", "node_type": "entity"}, {"custom_id": "concept_149", "node": "Configuration Groups tab", "node_type": "entity"}, {"custom_id": "concept_150", "node": "data tabs", "node_type": "entity"}, {"custom_id": "concept_151", "node": "Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.", "node_type": "event"}, {"custom_id": "concept_152", "node": "Configuration Groups", "node_type": "entity"}, {"custom_id": "concept_153", "node": "parameters", "node_type": "entity"}, {"custom_id": "concept_154", "node": "Bank Basket rules for four separate request types are configured on this tab.", "node_type": "event"}, {"custom_id": "concept_155", "node": "Bank Basket rules", "node_type": "entity"}, {"custom_id": "concept_156", "node": "this tab", "node_type": "entity"}, {"custom_id": "concept_157", "node": "RFS FX Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_158", "node": "RFS MM Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_159", "node": "RFS Commodity Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_160", "node": "RFS Cross Currency Netting Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_161", "node": "Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.", "node_type": "event"}, {"custom_id": "concept_162", "node": "Provider Groups", "node_type": "entity"}, {"custom_id": "concept_163", "node": "Blocked Providers", "node_type": "entity"}, {"custom_id": "concept_164", "node": "Orders", "node_type": "entity"}, {"custom_id": "concept_165", "node": "Order Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_166", "node": "Bank Basket rules for Forward or Spot orders are configured on this tab.", "node_type": "event"}, {"custom_id": "concept_167", "node": "Forward orders", "node_type": "entity"}, {"custom_id": "concept_168", "node": "Spot orders", "node_type": "entity"}, {"custom_id": "concept_169", "node": "Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).", "node_type": "event"}, {"custom_id": "concept_170", "node": "Supersonic (SEP)", "node_type": "entity"}, {"custom_id": "concept_171", "node": "SEP Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_172", "node": "Bank Basket rules for SEP streaming spot executions are configured on this tab.", "node_type": "event"}, {"custom_id": "concept_173", "node": "SEP streaming spot executions", "node_type": "entity"}, {"custom_id": "concept_174", "node": "Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.", "node_type": "event"}, {"custom_id": "concept_175", "node": "Bank Basket Configurations", "node_type": "entity"}, {"custom_id": "concept_176", "node": "RFS requests", "node_type": "entity"}, {"custom_id": "concept_177", "node": "Orders requests", "node_type": "entity"}, {"custom_id": "concept_178", "node": "SEP requests", "node_type": "entity"}, {"custom_id": "concept_179", "node": "An RFS configuration will have no impact on SEP trading.", "node_type": "event"}, {"custom_id": "concept_180", "node": "RFS configuration", "node_type": "entity"}, {"custom_id": "concept_181", "node": "SEP trading", "node_type": "entity"}, {"custom_id": "concept_182", "node": "centralized management", "node_type": "entity"}, {"custom_id": "concept_183", "node": "Groups", "node_type": "entity"}, {"custom_id": "concept_184", "node": "each parameter", "node_type": "entity"}, {"custom_id": "concept_185", "node": "creating rules", "node_type": "entity"}, {"custom_id": "concept_186", "node": "groups", "node_type": "entity"}, {"custom_id": "concept_187", "node": "set of rules", "node_type": "entity"}, {"custom_id": "concept_188", "node": "available parameters", "node_type": "entity"}, {"custom_id": "concept_189", "node": "Currency Groups", "node_type": "entity"}, {"custom_id": "concept_190", "node": "C<PERSON>rency Couple Groups", "node_type": "entity"}, {"custom_id": "concept_191", "node": "FX Time Period Groups", "node_type": "entity"}, {"custom_id": "concept_192", "node": "MM Time Period Groups", "node_type": "entity"}, {"custom_id": "concept_193", "node": "create new group", "node_type": "entity"}, {"custom_id": "concept_194", "node": "edit name of existing group", "node_type": "entity"}, {"custom_id": "concept_195", "node": "delete group", "node_type": "entity"}, {"custom_id": "concept_196", "node": "save changes", "node_type": "entity"}, {"custom_id": "concept_197", "node": "Rename Group", "node_type": "entity"}, {"custom_id": "concept_198", "node": "Default Group", "node_type": "entity"}, {"custom_id": "concept_199", "node": "Remove Group", "node_type": "entity"}, {"custom_id": "concept_200", "node": "removed group", "node_type": "entity"}, {"custom_id": "concept_201", "node": "configuring complex bank basket rules", "node_type": "entity"}, {"custom_id": "concept_202", "node": "individual custom rules", "node_type": "entity"}, {"custom_id": "concept_203", "node": "Individual custom rules", "node_type": "entity"}, {"custom_id": "concept_204", "node": "users with less complex bank basket setups", "node_type": "entity"}, {"custom_id": "concept_205", "node": "Bank Basket Configuration", "node_type": "entity"}, {"custom_id": "concept_206", "node": "existing values", "node_type": "entity"}, {"custom_id": "concept_207", "node": "Default Groups", "node_type": "entity"}, {"custom_id": "concept_208", "node": "values", "node_type": "entity"}, {"custom_id": "concept_209", "node": "new values", "node_type": "entity"}, {"custom_id": "concept_210", "node": "new currency", "node_type": "entity"}, {"custom_id": "concept_211", "node": "Default Currency Group", "node_type": "entity"}, {"custom_id": "concept_212", "node": "single currencies", "node_type": "entity"}, {"custom_id": "concept_213", "node": "single rule for group of currencies", "node_type": "entity"}, {"custom_id": "concept_214", "node": "Currencies", "node_type": "entity"}, {"custom_id": "concept_215", "node": "currency group", "node_type": "entity"}, {"custom_id": "concept_216", "node": "rule creation for interest rate products", "node_type": "entity"}, {"custom_id": "concept_217", "node": "interest rate products", "node_type": "entity"}, {"custom_id": "concept_218", "node": "<PERSON><PERSON><PERSON><PERSON>", "node_type": "entity"}, {"custom_id": "concept_219", "node": "Interest Rate Swap", "node_type": "entity"}, {"custom_id": "concept_220", "node": "CapFloor", "node_type": "entity"}, {"custom_id": "concept_221", "node": "Configuration Groups facilitate centralized management of parameters.", "node_type": "event"}, {"custom_id": "concept_222", "node": "Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.", "node_type": "event"}, {"custom_id": "concept_223", "node": "requests", "node_type": "entity"}, {"custom_id": "concept_224", "node": "The groups themselves can be edited without changing a set of rules based on those groups.", "node_type": "event"}, {"custom_id": "concept_225", "node": "The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.", "node_type": "event"}, {"custom_id": "concept_226", "node": "A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.", "node_type": "event"}, {"custom_id": "concept_227", "node": "Configuration Group area", "node_type": "entity"}, {"custom_id": "concept_228", "node": "changes", "node_type": "entity"}, {"custom_id": "concept_229", "node": "Create Group adds a new group.", "node_type": "event"}, {"custom_id": "concept_230", "node": "Create Group", "node_type": "entity"}, {"custom_id": "concept_231", "node": "Rename Group changes the name of a group.", "node_type": "event"}, {"custom_id": "concept_232", "node": "Rename Group cannot be used on the Default Group.", "node_type": "event"}, {"custom_id": "concept_233", "node": "Remove Group deletes an individual group.", "node_type": "event"}, {"custom_id": "concept_234", "node": "Remove Group cannot be used on the Default Group.", "node_type": "event"}, {"custom_id": "concept_235", "node": "If the removed group is used in any configured rules this group is replaced by the Default Group.", "node_type": "event"}, {"custom_id": "concept_236", "node": "Users are reminded to save changes to configurations.", "node_type": "event"}, {"custom_id": "concept_237", "node": "configurations", "node_type": "entity"}, {"custom_id": "concept_238", "node": "Configuration Groups are particularly useful when configuring complex bank basket rules.", "node_type": "event"}, {"custom_id": "concept_239", "node": "complex bank basket rules", "node_type": "entity"}, {"custom_id": "concept_240", "node": "It is not required to configure groups based on the above parameters.", "node_type": "event"}, {"custom_id": "concept_241", "node": "Users may still set individual custom rules without utilizing the Configuration Groups.", "node_type": "event"}, {"custom_id": "concept_242", "node": "Individual custom rules may be preferable for some users with less complex bank basket setups.", "node_type": "event"}, {"custom_id": "concept_243", "node": "bank basket setups", "node_type": "entity"}, {"custom_id": "concept_244", "node": "Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.", "node_type": "event"}, {"custom_id": "concept_245", "node": "The Default Group will include all existing values.", "node_type": "event"}, {"custom_id": "concept_246", "node": "All Default Groups can be modified.", "node_type": "event"}, {"custom_id": "concept_247", "node": "In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.", "node_type": "event"}, {"custom_id": "concept_248", "node": "versions", "node_type": "entity"}, {"custom_id": "concept_249", "node": "If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.", "node_type": "event"}, {"custom_id": "concept_250", "node": "360T platform", "node_type": "entity"}, {"custom_id": "concept_251", "node": "The new currency must be selected by the user.", "node_type": "event"}, {"custom_id": "concept_252", "node": "Currency Groups are intended to allow the classification of single currencies into customized groups.", "node_type": "event"}, {"custom_id": "concept_253", "node": "customized groups", "node_type": "entity"}, {"custom_id": "concept_254", "node": "This allows setting one single rule for each group of currencies rather than many rules for individual currencies.", "node_type": "event"}, {"custom_id": "concept_255", "node": "group of currencies", "node_type": "entity"}, {"custom_id": "concept_256", "node": "individual currencies", "node_type": "entity"}, {"custom_id": "concept_257", "node": "Currencies can be added or removed from the group without editing the rules themselves.", "node_type": "event"}, {"custom_id": "concept_258", "node": "The image shows a user interface for managing currency groups in a financial application.", "node_type": "event"}, {"custom_id": "concept_259", "node": "A currency is highlighted with a single-click.", "node_type": "event"}, {"custom_id": "concept_260", "node": "The single arrow is activated.", "node_type": "event"}, {"custom_id": "concept_261", "node": "Clicking the single arrow moves the desired currency.", "node_type": "event"}, {"custom_id": "concept_262", "node": "A user clicks Create Group.", "node_type": "event"}, {"custom_id": "concept_263", "node": "A user types the desired name.", "node_type": "event"}, {"custom_id": "concept_264", "node": "A user clicks Create Group again.", "node_type": "event"}, {"custom_id": "concept_265", "node": "A user clicks Save.", "node_type": "event"}, {"custom_id": "concept_266", "node": "A user clicks on the Currency Group name.", "node_type": "event"}, {"custom_id": "concept_267", "node": "The currencies configured for the group are viewed.", "node_type": "event"}, {"custom_id": "concept_268", "node": "A Currency Couple Group is created.", "node_type": "event"}, {"custom_id": "concept_269", "node": "The Currency Couple Group can be used to simplify rules.", "node_type": "event"}, {"custom_id": "concept_270", "node": "Currencies may be added or removed from the default group.", "node_type": "event"}, {"custom_id": "concept_271", "node": "default group", "node_type": "entity"}, {"custom_id": "concept_272", "node": "A currency", "node_type": "entity"}, {"custom_id": "concept_273", "node": "Highlighting a currency activates the single arrow.", "node_type": "event"}, {"custom_id": "concept_274", "node": "Highlighting a currency", "node_type": "entity"}, {"custom_id": "concept_275", "node": "single arrow", "node_type": "entity"}, {"custom_id": "concept_276", "node": "Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.", "node_type": "event"}, {"custom_id": "concept_277", "node": "desired currency", "node_type": "entity"}, {"custom_id": "concept_278", "node": "Available", "node_type": "entity"}, {"custom_id": "concept_279", "node": "Selected", "node_type": "entity"}, {"custom_id": "concept_280", "node": "All currencies can be moved in either direction by using the double arrows.", "node_type": "event"}, {"custom_id": "concept_281", "node": "All currencies", "node_type": "entity"}, {"custom_id": "concept_282", "node": "double arrows", "node_type": "entity"}, {"custom_id": "concept_283", "node": "A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.", "node_type": "event"}, {"custom_id": "concept_284", "node": "new group", "node_type": "entity"}, {"custom_id": "concept_285", "node": "desired name", "node_type": "entity"}, {"custom_id": "concept_286", "node": "Currencies configured for the group can be viewed by clicking on the Currency Group name.", "node_type": "event"}, {"custom_id": "concept_287", "node": "currencies", "node_type": "entity"}, {"custom_id": "concept_288", "node": "Currency Group name", "node_type": "entity"}, {"custom_id": "concept_289", "node": "The same currency can be added to many different groups.", "node_type": "event"}, {"custom_id": "concept_290", "node": "The system does not restrict the creation of groups with overlapping sets of currencies.", "node_type": "event"}, {"custom_id": "concept_291", "node": "system", "node_type": "entity"}, {"custom_id": "concept_292", "node": "creation of groups", "node_type": "entity"}, {"custom_id": "concept_293", "node": "overlapping sets of currencies", "node_type": "entity"}, {"custom_id": "concept_294", "node": "Currency Couple Groups allow the creation of \"buckets\" of currency pairs.", "node_type": "event"}, {"custom_id": "concept_295", "node": "buckets of currency pairs", "node_type": "entity"}, {"custom_id": "concept_296", "node": "A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.", "node_type": "event"}, {"custom_id": "concept_297", "node": "Currency Couple Group", "node_type": "entity"}, {"custom_id": "concept_298", "node": "FX products", "node_type": "entity"}, {"custom_id": "concept_299", "node": "RFS FX Bank Baskets area", "node_type": "entity"}, {"custom_id": "concept_300", "node": "FX Spot", "node_type": "entity"}, {"custom_id": "concept_301", "node": "Forwards", "node_type": "entity"}, {"custom_id": "concept_302", "node": "Options", "node_type": "entity"}, {"custom_id": "concept_303", "node": "Block Trades", "node_type": "entity"}, {"custom_id": "concept_304", "node": "Energy Asian Swaps", "node_type": "entity"}, {"custom_id": "concept_305", "node": "Bullet Swaps", "node_type": "entity"}, {"custom_id": "concept_306", "node": "RFS Commodity Bank Baskets area", "node_type": "entity"}, {"custom_id": "concept_307", "node": "Order Spot", "node_type": "entity"}, {"custom_id": "concept_308", "node": "Orders Bank Basket area", "node_type": "entity"}, {"custom_id": "concept_309", "node": "RFS REQUESTER", "node_type": "entity"}, {"custom_id": "concept_310", "node": "DEAL TRACKING", "node_type": "entity"}, {"custom_id": "concept_311", "node": "The Default Group contains all currency pairs.", "node_type": "event"}, {"custom_id": "concept_312", "node": "currency pairs", "node_type": "entity"}, {"custom_id": "concept_313", "node": "base currency", "node_type": "entity"}, {"custom_id": "concept_314", "node": "quote currency", "node_type": "entity"}, {"custom_id": "concept_315", "node": "Currency Couple Groups can be created.", "node_type": "event"}, {"custom_id": "concept_316", "node": "Configuration Group icons", "node_type": "entity"}, {"custom_id": "concept_317", "node": "Currency Couple Groups can be renamed.", "node_type": "event"}, {"custom_id": "concept_318", "node": "Currency Couple Groups can be removed.", "node_type": "event"}, {"custom_id": "concept_319", "node": "A user can add currency pairs within a group.", "node_type": "event"}, {"custom_id": "concept_320", "node": "The Add Currency Couple button is clicked.", "node_type": "event"}, {"custom_id": "concept_321", "node": "Add <PERSON><PERSON><PERSON><PERSON>le button", "node_type": "entity"}, {"custom_id": "concept_322", "node": "The ISO code is chosen in the drop down list.", "node_type": "event"}, {"custom_id": "concept_323", "node": "ISO code", "node_type": "entity"}, {"custom_id": "concept_324", "node": "drop down list", "node_type": "entity"}, {"custom_id": "concept_325", "node": "The desired currency is typed.", "node_type": "event"}, {"custom_id": "concept_326", "node": "The selection is confirmed by clicking the green check mark.", "node_type": "event"}, {"custom_id": "concept_327", "node": "selection", "node_type": "entity"}, {"custom_id": "concept_328", "node": "green check mark", "node_type": "entity"}, {"custom_id": "concept_329", "node": "Save is clicked.", "node_type": "event"}, {"custom_id": "concept_330", "node": "Products with varying maturities or tenors may be configured into maturity ranges.", "node_type": "event"}, {"custom_id": "concept_331", "node": "Products", "node_type": "entity"}, {"custom_id": "concept_332", "node": "maturities", "node_type": "entity"}, {"custom_id": "concept_333", "node": "tenors", "node_type": "entity"}, {"custom_id": "concept_334", "node": "maturity ranges", "node_type": "entity"}, {"custom_id": "concept_335", "node": "An FX Time Period Group can simplify rules for FX products.", "node_type": "event"}, {"custom_id": "concept_336", "node": "FX Time Period Group", "node_type": "entity"}, {"custom_id": "concept_337", "node": "The Add FX Time Period button is clicked.", "node_type": "event"}, {"custom_id": "concept_338", "node": "The desired time period is chosen.", "node_type": "event"}, {"custom_id": "concept_339", "node": "The Save button is clicked.", "node_type": "event"}, {"custom_id": "concept_340", "node": "The Add MM Time Period button is clicked.", "node_type": "event"}, {"custom_id": "concept_341", "node": "The desired time period is selected.", "node_type": "event"}, {"custom_id": "concept_342", "node": "An MM Time Period Group is created.", "node_type": "event"}, {"custom_id": "concept_343", "node": "Rules for interest rate products can be simplified.", "node_type": "event"}, {"custom_id": "concept_344", "node": "The same tenors are used in various groups.", "node_type": "event"}, {"custom_id": "concept_345", "node": "They are used for different sets of rules.", "node_type": "event"}, {"custom_id": "concept_346", "node": "Tenors", "node_type": "entity"}, {"custom_id": "concept_347", "node": "range of maturities", "node_type": "entity"}, {"custom_id": "concept_348", "node": "various groups", "node_type": "entity"}, {"custom_id": "concept_349", "node": "Product Groups", "node_type": "entity"}, {"custom_id": "concept_350", "node": "product types", "node_type": "entity"}, {"custom_id": "concept_351", "node": "single rule", "node_type": "entity"}, {"custom_id": "concept_352", "node": "product group", "node_type": "entity"}, {"custom_id": "concept_353", "node": "rule creation", "node_type": "entity"}, {"custom_id": "concept_354", "node": "all products", "node_type": "entity"}, {"custom_id": "concept_355", "node": "removed or renamed", "node_type": "entity"}, {"custom_id": "concept_356", "node": "all product types", "node_type": "entity"}, {"custom_id": "concept_357", "node": "relevant products", "node_type": "entity"}, {"custom_id": "concept_358", "node": "RFS, Orders or SEP", "node_type": "entity"}, {"custom_id": "concept_359", "node": "default Product Group", "node_type": "entity"}, {"custom_id": "concept_360", "node": "Bank Basket areas", "node_type": "entity"}, {"custom_id": "concept_361", "node": "individual Provider Groups", "node_type": "entity"}, {"custom_id": "concept_362", "node": "providers", "node_type": "entity"}, {"custom_id": "concept_363", "node": "user interface", "node_type": "entity"}, {"custom_id": "concept_364", "node": "360T Bank Baskets", "node_type": "entity"}, {"custom_id": "concept_365", "node": "RFS Requester", "node_type": "entity"}, {"custom_id": "concept_366", "node": "Deal Tracking", "node_type": "entity"}, {"custom_id": "concept_367", "node": "interface", "node_type": "entity"}, {"custom_id": "concept_368", "node": "currency groups", "node_type": "entity"}, {"custom_id": "concept_369", "node": "FX time period groups", "node_type": "entity"}, {"custom_id": "concept_370", "node": "MM time period groups", "node_type": "entity"}, {"custom_id": "concept_371", "node": "product groups", "node_type": "entity"}, {"custom_id": "concept_372", "node": "section", "node_type": "entity"}, {"custom_id": "concept_373", "node": "Select Member for 'FX Spot and Forward'", "node_type": "entity"}, {"custom_id": "concept_374", "node": "available product types", "node_type": "entity"}, {"custom_id": "concept_375", "node": "selected product types", "node_type": "entity"}, {"custom_id": "concept_376", "node": "Figure 16", "node_type": "entity"}, {"custom_id": "concept_377", "node": "Bank Basket Product Groups", "node_type": "entity"}, {"custom_id": "concept_378", "node": "Figure 17", "node_type": "entity"}, {"custom_id": "concept_379", "node": "Bank Basket Product Groups Create Group", "node_type": "entity"}, {"custom_id": "concept_380", "node": "green button", "node_type": "entity"}, {"custom_id": "concept_381", "node": "User Guide", "node_type": "entity"}, {"custom_id": "concept_382", "node": "DEUTSCHE BÖRSE GROUP", "node_type": "entity"}, {"custom_id": "concept_383", "node": "Product Groups are intended to allow the classification of product types into customized groups.", "node_type": "event"}, {"custom_id": "concept_384", "node": "One single rule can be set for each group of products rather than many rules for individual product types.", "node_type": "event"}, {"custom_id": "concept_385", "node": "A new group is created by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.", "node_type": "event"}, {"custom_id": "concept_386", "node": "A product group can be used to simplify rule creation for all relevant product types.", "node_type": "event"}, {"custom_id": "concept_387", "node": "A default group exists which includes all products.", "node_type": "event"}, {"custom_id": "concept_388", "node": "The default group cannot be removed or renamed.", "node_type": "event"}, {"custom_id": "concept_389", "node": "The product types in the default group can be altered.", "node_type": "event"}, {"custom_id": "concept_390", "node": "The Default Group contains all product types across RFS, Orders and SEP.", "node_type": "event"}, {"custom_id": "concept_391", "node": "Only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.", "node_type": "event"}, {"custom_id": "concept_392", "node": "Products can be added or removed from a product group without editing the rules themselves.", "node_type": "event"}, {"custom_id": "concept_393", "node": "Periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.", "node_type": "event"}, {"custom_id": "concept_394", "node": "periods", "node_type": "entity"}, {"custom_id": "concept_395", "node": "OVERNIGHT", "node_type": "entity"}, {"custom_id": "concept_396", "node": "1 WEEK", "node_type": "entity"}, {"custom_id": "concept_397", "node": "1 MONTH", "node_type": "entity"}, {"custom_id": "concept_398", "node": "6 MONTHS", "node_type": "entity"}, {"custom_id": "concept_399", "node": "Tenors are defined as a range of maturities, with both start and end values included.", "node_type": "event"}, {"custom_id": "concept_400", "node": "start values", "node_type": "entity"}, {"custom_id": "concept_401", "node": "end values", "node_type": "entity"}, {"custom_id": "concept_402", "node": "The same tenors may be used in various groups in order to be used for different sets of rules.", "node_type": "event"}, {"custom_id": "concept_403", "node": "sets of rules", "node_type": "entity"}, {"custom_id": "concept_404", "node": "This allows setting one single rule for each group of products rather than many rules for individual product types.", "node_type": "event"}, {"custom_id": "concept_405", "node": "one single rule", "node_type": "entity"}, {"custom_id": "concept_406", "node": "group of products", "node_type": "entity"}, {"custom_id": "concept_407", "node": "many rules", "node_type": "entity"}, {"custom_id": "concept_408", "node": "individual product types", "node_type": "entity"}, {"custom_id": "concept_409", "node": "The image shows a user interface for configuring 360T Bank Baskets.", "node_type": "event"}, {"custom_id": "concept_410", "node": "It includes options for RFS Requester, Deal Tracking, and Bridge Administration.", "node_type": "event"}, {"custom_id": "concept_411", "node": "The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups.", "node_type": "event"}, {"custom_id": "concept_412", "node": "A section titled \"Select Member for 'FX Spot and Forward'\" is visible, with options for available and selected product types.", "node_type": "event"}, {"custom_id": "concept_413", "node": "options", "node_type": "entity"}, {"custom_id": "concept_414", "node": "Products can be added or removed from the group without editing the rules themselves.", "node_type": "event"}, {"custom_id": "concept_415", "node": "Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.", "node_type": "event"}, {"custom_id": "concept_416", "node": "SEP Bank Basket areas", "node_type": "entity"}, {"custom_id": "concept_417", "node": "To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.", "node_type": "event"}, {"custom_id": "concept_418", "node": "products", "node_type": "entity"}, {"custom_id": "concept_419", "node": "This group cannot be removed or renamed.", "node_type": "event"}, {"custom_id": "concept_420", "node": "However, the product types in the group can be altered.", "node_type": "event"}, {"custom_id": "concept_421", "node": "However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.", "node_type": "event"}, {"custom_id": "concept_422", "node": "Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.", "node_type": "event"}, {"custom_id": "concept_423", "node": "The image shows a green button with the text \"360T\" on it.", "node_type": "event"}, {"custom_id": "concept_424", "node": "text \"360T\"", "node_type": "entity"}, {"custom_id": "concept_425", "node": "The button has a white outline and a white background.", "node_type": "event"}, {"custom_id": "concept_426", "node": "button", "node_type": "entity"}, {"custom_id": "concept_427", "node": "white outline", "node_type": "entity"}, {"custom_id": "concept_428", "node": "white background", "node_type": "entity"}, {"custom_id": "concept_429", "node": "The text is in a stylized font.", "node_type": "event"}, {"custom_id": "concept_430", "node": "stylized font", "node_type": "entity"}, {"custom_id": "concept_431", "node": "Providers are temporarily blocked from particular request types.", "node_type": "event"}, {"custom_id": "concept_432", "node": "A Blocked Provider will remain in a Provider Group but will appear with a \"blocked\" symbol.", "node_type": "event"}, {"custom_id": "concept_433", "node": "A Provider is removed completely and its relationship is rejected using the Counterpart Relationship Management tool.", "node_type": "event"}, {"custom_id": "concept_434", "node": "Please refer to the relevant user guide.", "node_type": "event"}, {"custom_id": "concept_435", "node": "The Provider Groups can be edited.", "node_type": "event"}, {"custom_id": "concept_436", "node": "Providers may be temporarily blocked from particular request types.", "node_type": "event"}, {"custom_id": "concept_437", "node": "Providers", "node_type": "entity"}, {"custom_id": "concept_438", "node": "Provider Group", "node_type": "entity"}, {"custom_id": "concept_439", "node": "A Blocked Provider will remain in a Provider Group.", "node_type": "event"}, {"custom_id": "concept_440", "node": "Blocked Provider", "node_type": "entity"}, {"custom_id": "concept_441", "node": "A Blocked Provider will appear with a blocked symbol.", "node_type": "event"}, {"custom_id": "concept_442", "node": "blocked symbol", "node_type": "entity"}, {"custom_id": "concept_443", "node": "A Provider should be removed completely.", "node_type": "event"}, {"custom_id": "concept_444", "node": "Provider", "node_type": "entity"}, {"custom_id": "concept_445", "node": "The relationship should be rejected using the Counterpart Relationship Management tool.", "node_type": "event"}, {"custom_id": "concept_446", "node": "relationship", "node_type": "entity"}, {"custom_id": "concept_447", "node": "Counterpart Relationship Management tool", "node_type": "entity"}, {"custom_id": "concept_448", "node": "Refer to the relevant user guide.", "node_type": "event"}, {"custom_id": "concept_449", "node": "user guide", "node_type": "entity"}], "created_at": "2025-07-29T09:00:16.132685"}