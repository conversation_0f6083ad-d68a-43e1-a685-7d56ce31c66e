{"id": "1", "metadata": {"lang": "en"}, "original_text": "## BANK BASKET CONFIGURATION (BRIDGE ADMINISTRATION) Image /page/0/Picture/1 description: { \"image\\_description\": \"The image shows a logo for \\\"360T\\\", which is a green rectangle with rounded corners. The text \\\"360\\\" is in white with a green outline, and the \\\"T\\\" is also in white with a green outline. There are two arrows pointing towards each other in the middle of the \\\"0\\\" in \\\"360\\\".\" } # TEX MULTIDEALER TRADING SYSTEM USER GUIDE 360T BRIDGE ADMINISTRATION: ENHANCED BANK BASKET CONFIGURATION © 360 TREASURY SYSTEMS AG, 2019 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG ## CONTENTS | 1 | | INTRODUCTION | 4 | |---|-------|---------------------------------------|----| | 2 | | GETTING STARTED | 4 | | 3 | | CONFIGURATION OF BANK BASKETS | 7 | | | 3.1 | SETTING UP CONFIGURATION GROUPS | 8 | | | 3.1.1 | Defining Currency Groups | 9 | | | 3.1.2 | Defining Currency Couple Groups | 11 | | | 3.1.3 | Defining FX Time Period Groups | 13 | | | 3.1.4 | Defining MM Time Period Groups | 14 | | | 3.1.5 | Defining Product Groups | 15 | | | 3.2 | PROVIDER GROUPS AND BLOCKED PROVIDERS | 16 | | | 3.3 | BANK BASKET RULES | 18 | | | 3.3.1 | Defining Bank Basket Rules | 18 | | | 3.3.2 | Order of the Rules | 19 | | | 3.4 | NOTE ON SUPERSONIC | 19 | | 4 | | BANK BASKETS EVALUATOR TOOL | 19 | | 5 | | CONTACT 360T | 22 | ## TABLE OF FIGURES | Figure 1 Header Bar | 4 | |-----------------------------------------------------------------|----| | Figure 2 Bridge Administration: Homepage | 5 | | Figure 3 Bank Basket: Start page | 5 | | Figure 4 Bank Basket: Configuration for a selected legal entity | 6 | | Figure 5 Bank Basket: Configuration: Live Audit Log | 7 | | Figure 6 Bank Basket: Configuration Data Tabs | 8 | | Figure 7 Bank Basket: Currency Groups | 9 | | Figure 8 Bank Basket: RFS MM Bank Baskets | 10 | | Figure 9 Bank Basket: Currency Groups Default Group | 10 | | Figure 10 Bank Basket: Currency Groups Create Group | 11 | | Figure 11 Bank Basket: Configured Currency Group | 11 | | Figure 12 Bank Basket: Currency Couple Groups | 12 | | Figure 13 Bank Basket: Add Currency Couple | 13 | | Figure 14 Bank Basket: Add FX Time Period | 14 | | Figure 15 Bank Basket: Add MM Time Period | 15 | | Figure 16 Bank Basket: Product Groups | 16 | | Figure 17 Bank Basket: Product Groups Create Group | 16 | | Figure 18 Bank Basket: Blocked RFS Providers | 17 | | Figure 19 Bank Basket: RFS Provider Groups | 17 | | Figure 20 Bank Basket: Add Rule 18 | | |---------------------------------------------------------|--| | Figure 21 Use Custom selection for Currency couples 19 | | | Figure 22 Bank Basket: Order of Rules 19 | | | Figure 23 Evaluator Tool Quick Link 20 | | | Figure 24 Evaluator Tool icon 20 | | | Figure 24 Evaluator Tool 21 | ", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 1010, "total_tokens": 2009, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 5.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 999, "total_tokens": 1998, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 1042, "total_tokens": 2041, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.****************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "| | Figure 26 Evaluator Tool: Highlighted Rule 21 | | ## 1 INTRODUCTION This user manual describes the Bank Basket feature of the 360T Bridge Administration tool. The Bank Basket feature has been enhanced to provide improved rule management capabilities, including the introduction of configuration groups based on currency, currency couple, time period and product(s); configuration of separate baskets by request type (RFS, Order, SEP); as well as the ability to apply and remove temporary blocks without affecting the configured rules or counterpart relationship(s). #### Please note: The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications. Only users with corresponding user rights are able to administer the company's Bank Baskets. <NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights. ## 2 GETTING STARTED The Bank Basket configuration is found within the Bridge Administration tool. Bridge Administration can be accessed via the menu option \"Administration\" in the screen header of the Bridge application. | | | | $\\vee$ Preferences<br>V Administration | $\\times$ Help<br>$\\Box$<br>A <sub>A</sub><br>$\\mathbb{X}$ | |-----------------------------------------------------------------------------|-------------------------------------------------------------------------------------|----------------------------------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------------------------| | > Bridge Administration | | | | X | | <b>SCIENCES</b> | | | <b>SCIENS</b> | | | <b>UNIT SIDEN</b><br>$^{117}890$<br><sup>⊞7</sup> 92α<br>Spot // 21.11.2017 | <b><i>STATISTICS</i></b><br><b>POST MARK</b><br>1327<br>##336<br>Spot // 21 11.2017 | <b>STATISTICS</b><br><b>START CORPORA</b><br>084<br>0.55<br>5001//21112017 | <b>Life of Life and</b><br>889184<br>0.994<br>93.<br>Spot // 2111 2017 | | | 11703<br><b>ILFOG.</b> | 132.2 d | 08900 mm<br><b>TIDO AT TELL</b> | 0.99 ft J Lune<br>0994 T | | Figure 1 Header Bar The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user. A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage. Image /page/4/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user guide. The screen displays the 'Administration Start' page with options for 'Regulatory Data', 'Bank Baskets', 'Change Request', 'Wizards', and 'Evaluator Tools'. The top of the screen includes tabs for 'RFS REQUESTER', 'DEAL TRACKING', and 'BRIDGE ADMINISTRATION', along with preferences and help options. The bottom of the screen display", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 855, "total_tokens": 1854, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 844, "total_tokens": 1843, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 887, "total_tokens": 1886, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.**************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "s system information and connection status.\" } Figure 2 Bridge Administration: Homepage The \"Bank Baskets\" quick link opens a navigation panel which contains an institution tree. Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity. | | | | | | | | | Preferences | Administration | Help | A A | |--|--|----------------------|----------------------|------------------------------|---|--|--|-------------|----------------|------|-----| | | | <b>RFS Requester</b> | <b>DEAL TRACKING</b> | <b>Bridge Administration</b> | + | | | | | | | Q 360T.ALIAS 360T.ALIAS.Company 1 360T.ALIAS.Company 2 360T.ALIAS.Company 3 360T.ALIAS.Company 4 360T.ALIAS.Company 5 360T.ALIAS.Company 6 >> No Individuals/Institutions are selected | 360TAS Treasurer 1, 360T ALIAS // INT | Mi, 25. Apr 2018, 14:42:22 GMT // Connected | |---------------------------------------|---------------------------------------------| |---------------------------------------|---------------------------------------------| Figure 3 Bank Basket: Start page The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity. It is possible to open multiple forms/sheets at a time. The selected item will be highlighted as an active task inside the taskbar. Image /page/5/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user interface. The interface includes a navigation menu on the left, a main content area in the center, and a header with user preferences and administration options. The navigation menu lists several 360T.ALIAS companies. The main content area displays currency group settings, with options to create and manage currency groups. The header includes tabs for different configuration groups, such as RFS Bank Baskets, Orders Bank Baskets, and SEP Bank Baskets.\" } Figure 4 Bank Basket: Configuration for a selected legal entity A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel: - Search : A search field will open and the user can type in an alphanumeric value in order to find the desired institution. - Scroll from source : This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel. Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source. - Show individuals view toggle : This icon is deactivated when using the Bank Basket configuration. For other configuration tools the toggle option allows the user to display only individuals in the navigation panel. - Show institutions view toggle : This icon is deactivated when using t", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 787, "total_tokens": 1786, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 776, "total_tokens": 1775, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 819, "total_tokens": 1818, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "he Bank Basket configuration. For other configuration tools the toggle option allows the user to display only institutions in the navigation panel. The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel. Each entity tab has a Live Audit Log which tracks all unsaved changes. Individual unsaved changes can be reverted by clicking on the arrow icon. Clicking on the \"Discard all changes\" button will revert all unsaved changes. Image /page/6/Picture/0 description: { \"image\\_description\": \"The image contains the text 'User Guide 360T Bank Baskets Configuration'. The text appears to be the title or heading of a document or guide.\" } | | 9 ※ 上 | | | | | | Live Audit Log | 目<br>$\\Omega$ | |-----------------------------|------------------------------------------|---------------------------------------------------------|-----------------------------------|------------------------------------------|---|------------|------------------|---------------| | $\\mathcal{L}_{\\mathcal{F}}$ | へ 盒 360T.ALIAS<br>盒 360T.ALIAS.Company 1 | <b>Currency Groups</b><br><b>Currency Couple Groups</b> | FX Time Period Groups | MM Time Period ( ) | | Target | Event Name | | | | 直 360T.ALIAS.Company 2 | | | | | 360T.ALIAS | Currency Added | $\\sqrt{2}$ | | ₿ | 盒 360T.ALIAS.Company 3 | <b>Currency Group</b> | | | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | | 盒 360T.ALIAS.Company 4 | Default Group | | a <sub>1</sub> | | 360T ALIAS | Currency Added | $\\sqrt{2}$ | | <b>tip</b> | 宜 360T.ALIAS.Company 5 | | | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | | 盒 360T.ALIAS.Company 6 | | | | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | 同 | | | | | | 360T.ALIAS | Currency Added | $\\sqrt{2}$ | | | | | | | √ | 360T.ALIAS | Currency Removed | | | $\\vec{\\Sigma}$ | | | | | | | | | | ಥೆ | | | | | | | | | | | | | | | | | | | | | | | | <b>DX</b><br>$ 1^{2}$ | | | | | | | | Available Currencies | Select Member for \"Default Group\" | <b>Selected Currencies</b> | | | | | | | | | AUD | | | | | | | | | SEK | $\\geq$<br>CAD | | | | | | | | | SGD<br>ZAR | $\\overline{\\epsilon}$<br>CHF | | | | | | | | | AED | EUR | | | | | | | | | AFN | GBP | | | | | | | | | ALL | $\\gg$<br><b>USD</b> | | | | | | | | | AMD | $\\ll$ | | | | | | | | | ANG | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Figure 5 Bank Basket: Configuration: Live Audit Log ## 3 CONFIGURATION OF BANK BASKETS Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs: - Configuration Groups: Facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS; Order; SEP). Allow users to create a group one single time and reuse it across various rules. - RFS Bank Baskets: Provider Groups and temporarily Blocked Providers ma", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 982, "total_tokens": 1981, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 971, "total_tokens": 1970, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 1014, "total_tokens": 2013, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "y be configured specifically for RFS requests. Bank Basket rules for four separate request types are configured on this tab: - o RFS FX Bank Baskets - o RFS MM Bank Baskets - o RFS Commodity Bank Baskets - o RFS Cross Currency Netting Bank Baskets - Order Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Orders. Bank Basket rules for Forward or Spot orders are configured on this tab. - SEP Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP). Bank Basket rules for SEP streaming spot executions are configured on this tab. Image /page/7/Picture/0 description: { \"image\\_description\": \"The image shows the logo of DEUTSCHE GROUP. The logo consists of two parts: a green graphic element on the left and the text \\\"DEUTSCHE GROUP\\\" on the right. The graphic element is a stylized representation of the letters \\\"360\\\" and a \\\"T\\\" inside a rounded rectangle. The text \\\"DEUTSCHE GROUP\\\" is in a sans-serif font, with \\\"DEUTSCHE\\\" stacked above \\\"GROUP\\\".\" } | RFS Requester | DEAL TRACKING | Bridge Administration | + | Preferences | Administration | Help | AA | X | |---------------|---------------|-----------------------|---|-------------|----------------|------|----|---| |---------------|---------------|-----------------------|---|-------------|----------------|------|----|---| | | | <input type=\"text\"/> | |--|--|----------------------| |--|--|----------------------| | | 360T.ALIAS | |--|----------------------| | | 360T.ALIAS.Company 1 | | | 360T.ALIAS Company 2 | | | 360T.ALIAS.Company 3 | | | 360T.ALIAS.Company 4 | | | 360T.ALIAS.Company 5 | | | 360T.ALIAS.Company 6 | | Configuration Groups | RFS Bank Baskets | Orders Bank Baskets | SEP Bank Baskets | | |----------------------|------------------------|-----------------------|-----------------------|----------------| | Currency Groups | Currency Couple Groups | FX Time Period Groups | MM Time Period Groups | Product Groups | | Currency Group | |----------------| | Default Group | | | | Create Group | |--|--|--------------| |--|--|--------------| | Create Change Request | Discard All Changes | Save | |-----------------------|---------------------|------| |-----------------------|---------------------|------| | 360T.ALIAS Company 1 X | |------------------------| |------------------------| | 360TAS Treasurer 1, 360T ALIAS // INT | Tue, 19. Jun 2018, 15:59:16 GMT // Connected [FFM] | |---------------------------------------|----------------------------------------------------| |---------------------------------------|----------------------------------------------------| Figure 6 Bank Basket: Configuration Data Tabs Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another i.e., an RFS configuration will have no impact on SEP trading, and vice v", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 810, "total_tokens": 1809, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 799, "total_tokens": 1798, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 842, "total_tokens": 1841, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "ersa. ### 3.1 Setting up Configuration Groups Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration. Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP. The groups themselves can be edited (values added or removed) without changing a set of rules based on those groups. The available parameters are: - o Currency Groups - o Currency Couple Groups - o FX Time Period Groups - o MM Time Period Groups A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes. - Create Group : To add a new group. - Rename Group : To change the name of a group. Cannot be used on the Default Group. - Remove Group : To delete an individual group. Cannot be used on the Default Group. If the removed group is used in any configured rules this group is replaced by the Default Group. - Save : Please remember to save changes to your configurations. Configuration Groups are particularly useful when configuring complex bank basket rules. - Note: It is not required to configure groups based on the above parameters. Users may still set individual custom rules without utilizing the Configuration Groups. Individual custom rules may be preferable for some users with less complex bank basket setups. - Note: Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group. The Default Group will include all existing values. All Default Groups can be modified (values may be removed). In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group. For example, if a new currency is added to the 360T platform the Default Currency Group will not include the new currency. The new currency must be selected by the user. #### 3.1.1 Defining Currency Groups Currency Groups are intended to allow the classification of single currencies into customized groups. This allows setting one single rule for each group of currencies rather than many rules for individual currencies. Currencies can be added or removed from the group without editing the rules themselves. Image /page/8/Picture/11 description: { \"image\\_description\": \"The image shows a user interface for managing currency groups in a financial application. The interface includes a list of available currencies and a list of selected currencies, allowing users to add or remove currencies from a group. The user is currently creating a currency group named G10.\" } Figure 7 Bank Basket: Currency Groups Once created, a currency group can be used to simplify rule creation for (1) interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and ", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 695, "total_tokens": 1694, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 684, "total_tokens": 1683, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 727, "total_tokens": 1726, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 5.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "Tri Party Repo found in the RFS MM Bank Baskets area and (2) Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area. Image /page/9/Picture/0 description: { \"image\\_description\": \"The image shows a green and white logo with the number 360 and a symbol that looks like a T.\" } User Guide 360T Bank Baskets Configuration | 侖 | Q ※ 上 二<br>$\\langle$ | | <b>Configuration Groups</b> | <b>RFS Bank Baskets</b> | Orders Bank Baskets | SEP Bank Baskets Copy To | $ \\mathcal{E}_\\lambda $ | | | | の位置 | |----------------------------------|------------------------------------------------------------|--------------|-----------------------------|------------------------------|-----------------------------------------|--------------------------|----------------------------|--------------|-----------------------------------------|--------------|----------| | $\\mathcal{G}$ | $\\wedge$ $\\hat{\\Xi}$ 360T.ALIAS<br>盒 360T.ALIAS.Company 1 | | RFS Provider Groups | <b>Blocked RFS Providers</b> | RFS FX Bank Baskets RFS MM Bank Baskets | -- | RFS Commodity Bank Baskets | | RFS Cross Currency Netting Bank Baskets | | | | -<br>$\\Box$ | 盒 360T.ALIAS.Company 2 | | POS Product Type | | Currency | | <b>Time Period</b> | | <b>Bank Basket</b> | | | | 凾 | 盒 360T.ALIAS.Company 3<br><sup> 360T ALIAS Company 4</sup> | $\\mathbf{1}$ | Any | $\\vee$ | Any | $\\widehat{\\phantom{a}}$ | Any | $\\checkmark$ | Default Group | $\\checkmark$ | 図自 | | | 盒 360T ALIAS Company 5<br>章 360T ALIAS Company 6 | | | | Any<br>Default Group | | | | | | Add Rule | | $\\quad \\ \\ \\, \\textcircled{F}$ | | | | | 610. | | | | | | | | | | | | | Custom | | | | | | | | ಂದ್ರಿ | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Discard All Changes | <b>Save</b> | | Figure 8 Bank Basket: RFS MM Bank Baskets A default group exists which includes all currencies. This group cannot be removed or renamed. However, the currencies in the group can be altered as described below. | | | Preferences Administration Help AA - X | | |--|----------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|------| | | RFS REQUESTER DEAL TRACKING | + BRIDGE ADMINISTRATION | | | | Q | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets Copy To... | | | | 360T ALIAS | Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product Groups | | | | 360T.", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 951, "total_tokens": 1950, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.**************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 940, "total_tokens": 1939, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 983, "total_tokens": 1982, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 5.**************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "ALIAS Company 1<br>360T.ALIAS.Company 2<br>360T.ALIAS.Company 3<br>360T.ALIAS.Company 4<br>360T.ALIAS.Company 5<br>360T.ALIAS.Company 6 | Currency Group <div>Default Group</div> <div>G10</div> <div>Create Group</div> | | | | | | | | | | Select Member for \"Default Group\" | - X | | | | Available Currencies Selected Currencies <div>AUD</div> <div>CAD</div> <div>CHF</div> <div>EUR</div> <div>GBP</div> <div>HKD</div> <div>JPY</div> <div>NOK</div> | | | | | Discard All Changes Create Change Request 360T.ALIAS X | Save | | | 360TAS.Treasurer1, 360T ALIAS // INT | Fri, 06. Jul 2018, 07:39:04 GMT // Connected [FFM] | | Figure 9 Bank Basket: Currency Groups Default Group Currencies may be added or removed from the default group. A currency is highlighted with a single-click. This activates the single arrow. Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available. All currencies can be moved in either direction by using the double arrows. To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save. Please, provide a name for a new group | | G10 | |--|-----| |--|-----| | Cancel | Create Group | |--------|--------------| |--------|--------------| Figure 10 Bank Basket: Currency Groups Create Group To view the currencies configured for the group, click on the Currency Group name. | Preferences | Administration | Help | AA | |-------------|----------------|------|----| |-------------|----------------|------|----| RFS REQUESTER DEAL TRACKING BRIDGE ADMINISTRATIONConfiguration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets Copy To...Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product GroupsCurrency Group Default Group G10 Create GroupSelect Member for \"G10\"Available Currencies HKD PLN SGD ZAR AED AFN ALLSelected Currencies AUD CAD CHF EUR GBP JPY NOK NZDCreate Change Request Discard All Changes SaveFigure 11 Bank Basket: Configured Currency Group Note: The same currency can be added to many different groups. The system does not restrict the creation of groups with overlapping sets of currencies. #### 3.1.2 Defining Currency Couple Groups Currency Couple Groups allow the creation of \"buckets\" of currency pairs. Once created, a Currency Couple Group can be used to simplify rules for (1) FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades etc; (2) Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area; and also (3) Order Spot and Forwards found in the Orders Bank Basket area. | | | | | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\Diamond$ AA $\\Box$ $\\Box$ X | |----------------------------------------------------------------|--------------------------------------------------------------|--------------------", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 839, "total_tokens": 1838, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 828, "total_tokens": 1827, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 871, "total_tokens": 1870, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "-----------------------------|----------------------------|-------------------------------------------------------------------------------------------| | RFS REQUESTER | <b>DEAL TRACKING</b><br><b>BRIDGE ADMINISTRATION</b> | $+$ | | | | | | | | | | Q ※ 上 三<br>侖 | <b>Configuration Groups</b><br>RFS Bank Baskets<br>$\\langle$ | Orders Bank Baskets SEP Bank Baskets<br>Copy To | $\\delta$ | $\\mathcal{A} \\cap \\mathcal{A} \\equiv \\mathcal{A}$ | | △ 盒 360T.ALIAS | Currency Groups<br><b>Currency Couple Groups</b> | FX Time Period Groups<br>MM Time Period Groups | Product Groups | | | €<br><sup> 360T.ALIAS.Company 1</sup> | | | | | | 查 360T.ALIAS.Company 2 | | <b>Currency Couple Group</b> | | | | ₿<br>盒 360T.ALIAS.Company 3 | | Default Group | | al前 | | 盒 360T ALIAS Company 4<br>$\\sqrt{2}$<br>盒 360T.ALIAS.Company 5 | <b>USD v G10</b> | | | al fi | | 盒 360T.ALIAS.Company 6 | | | | Create Group | | ₹ | | | | | | $\\overline{\\mathcal{L}}$ | | | | | | | | | | | | 呢 | | | | | | | | | | <b>DX</b><br>$ +$ | | | | Select Member for \"USD v G10\" | | | | | | <b>Base Currency</b><br><b>Quote Currency</b> | | | | | <b>USD</b> | <b>EUR</b> | | <b>シ</b> 童 | | | USD | JPY | | ジョ | | | USD | GBP | | ショ | | | <b>USD</b> | CHF | | ショ | | | USD | <b>AUD</b> | | $\\bar{z}$<br>市 | | | <b>USD</b> | <b>NZD</b> | | 3/ 自 | | | USD | CAD | | 5/ B | | | <b>USD</b> | SEK | | v)<br>'n | | | | | <b>Add Currency Couple</b> | | | | | | | | | | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | | | Discard All Changes<br>Save | | $\\frac{1}{\\alpha}$ | 360T ALIAS X | | | | | 360TAS.Treasurer1, 360T.ALIAS // INT | | <b>ELECTE</b> | | Fri, 06: Jul 2018, 08:27:08 GMT // Connected [FFM] . | Figure 12 Bank Basket: Currency Couple Groups The Default Group contains all currency pairs denoted as \\*\\*\\* / \\*\\*\\* for the base and quote currency, respectively. Currency Couple Groups can be created, renamed and removed using the standard Configuration Group icons. Within a group the user can add currency pairs: Click the Add Currency Couple button. Choosing the ISO code in the drop down list or type the desired currency. Confirm the selection by clicking the green check mark . Click Save. Image /page/12/Picture/0 description: { \"image\\_description\": \"The image contains text that reads 'User Guide 360T Bank Baskets Configuration'.\" } Image /page/12/Picture/1 description: { \"image\\_description\": \"Here are the bounding box detections: [{\\\"box\\_2d\\\": [856, 881, 890, 957], \\\"label\\\": \\\"Save\\\"}, {\\\"box\\_2d\\\": [766, 392, 957, 516], \\\"label\\\": \\\"dropdown\\\"}]\" } Figure 13 Bank Basket: Add Currency Couple #### 3.1.3 Defining FX Time Period Groups Products with varying maturities or tenors may be configured into maturity ranges using the FX Time Period Groups. Once created, an FX Time Period Group can be used to simplify rules for (1) FX products found in the RFS FX Bank Baskets area including: Forwards, Swaps, ", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 1062, "total_tokens": 2061, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 1051, "total_tokens": 2050, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.****************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 1094, "total_tokens": 2093, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 5.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "NDF, NDS, Options and Block Trades etc. and (2) Order Spot and Forwards found in the Orders Bank Basket area. Note: These groups are not relevant for FX Spot and Multi-leg Swaps. The Default Group contains all possible maturities denoted as TODAY / UNLIMITED. The values of the Default Group can be modified, but the group cannot be deleted or renamed. FX Time Period Groups can be created, renamed and removed using the standard Configuration Group icons. FX Time Periods may be added to each group: Click the Add FX Time Period button. Choose the desired time period. Confirm the selection by clicking the green check mark . Click Save. Image /page/13/Picture/0 description: { \"image\\_description\": \"The image contains a user interface for configuring 360T Bank Baskets. It shows options for setting up FX Time Period Groups, including selecting time periods from 'Today' to '3 Months'. The interface includes buttons for creating groups, adding time periods, discarding changes, and saving settings.\" } Figure 14 Bank Basket: Add FX Time Period The ability to add discontinuous tenor ranges is possible. For example, two separate periods may be added for TODAY / 1 WEEK and 1 MONTH / 6 MONTHS. Tenors are defined as a range of maturities, with both start and end values included. The same tenors may be used in various groups in order to be used for different sets of rules. #### 3.1.4 Defining MM Time Period Groups MM products with varying maturities or tenors may be configured into maturity ranges using the MM Time Period Groups. Once created, an MM Time Period Group can be used to simplify rules for interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and Tri Party Repo found in the RFS MM Bank Baskets area. The Default Group contains all possible maturities denoted as OVERNIGHT / UNLIMITED. The values of the Default Group can be modified, but the group cannot be deleted or renamed. MM Time Period Groups can be created, renamed and removed using the standard Configuration Group icons. MM Time Periods may be added to each group: Clicking the Add MM Time Period button. Select the desired time period. Confirm the selection by clicking the green check mark . Click Save. Image /page/14/Picture/0 description: { \"image\\_description\": \"The image shows a user interface for configuring bank baskets in 360T, a Deutsche Börse Group platform. The interface includes options for managing MM Time Period Groups, selecting members for a default group, and adding MM Time Periods. The user is currently selecting a time period from the 'From' dropdown menu, with 'OVERNIGHT' highlighted. The 'To' field is set to 'UNLIMITED'. There are also options to create change requests, discard changes, and save the configuration.\" } Figure 15 Bank Basket: Add MM Time Period The ability to add discontinuous tenor ranges is possible. For example, two separate p", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 751, "total_tokens": 1750, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 740, "total_tokens": 1739, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.**************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 783, "total_tokens": 1782, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "eriods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS. Tenors are defined as a range of maturities, with both start and end values included. The same tenors may be used in various groups in order to be used for different sets of rules. #### 3.1.5 Defining Product Groups Product Groups are intended to allow the classification of product types into customized groups. This allows setting one single rule for each group of products rather than many rules for individual product types. Image /page/15/Picture/0 description: { \"image\\_description\": \"The image shows a user interface for configuring 360T Bank Baskets. It includes options for RFS Requester, Deal Tracking, and Bridge Administration. The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups. A section titled \\\"Select Member for 'FX Spot and Forward'\\\" is visible, with options for available and selected product types.\" } Figure 16 Bank Basket: Product Groups Products can be added or removed from the group without editing the rules themselves. Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas. To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save. Please, provide a name for a new group | | MM products | |--|-------------| |--|-------------| | | Cancel | Create Group | |--|--------|--------------| |--|--------|--------------| Figure 17 Bank Basket: Product Groups Create Group A default group exists which includes all products. This group cannot be removed or renamed. However, the product types in the group can be altered. Note: The Default Group contains all product types across RFS, Orders and SEP. However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group. ## 3.2 Provider Groups and Blocked Providers Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers. Image /page/16/Picture/0 description: { \"image\\_description\": \"The image shows a green button with the text \\\"360T\\\" on it. The button has a white outline and a white background. The text is in a stylized font.\" } DEUTSCHE BÖRSE GROUP User Guide 360T Bank Baskets Configuration | RFS REQUESTER | DEAL TRACKING | BRIDGE ADMINISTRATION | + | |---------------|---------------|-----------------------|---| |---------------|---------------|-----------------------|---| | Preferences | Administration | Help | A A | X | |-------------|----------------|------|-----|---| |-------------|----------------|------|-----|---| | | Q | | | |--|----------------------|--|--| | | 360T ALIAS | | | | | 360T ALIAS Company 1 | | | | | 360T ALIAS Company 2 | | | | | 360T ALIAS Company 3 ", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 795, "total_tokens": 1794, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 784, "total_tokens": 1783, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 827, "total_tokens": 1826, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "| | | | | 360T ALIAS Company 4 | | | | | 360T ALIAS Company 5 | | | | | 360T.ALIAS.Company 6 | | | | Configuration Groups | RFS Bank Baskets | Orders Bank Baskets | SEP Bank Baskets | |----------------------|------------------|---------------------|------------------| |----------------------|------------------|---------------------|------------------| | RFS Provider Groups | Blocked RFS Providers | RFS FX Bank Baskets | RFS MM Bank Baskets | RFS Commodity Bank Baskets | RFS Cross Currency Netting Bank Baskets | |---------------------|-----------------------|---------------------|---------------------|----------------------------|-----------------------------------------| |---------------------|-----------------------|---------------------|---------------------|----------------------------|-----------------------------------------| | Available Providers | Blocked Providers | |---------------------|-------------------| | Barclays BARX.DEMO | 360TBANK.TEST | | BOAL DEMO | | | CITIBANK DEMO | | | COBA DEMO | | | RBC DEMO | | | RBS.LND DEMO | | | SEB FRA DEMO | | | SOCGEN.LND.DEMO | | | Create Change Request | Discard All Changes | Save | |-----------------------|---------------------|------| |-----------------------|---------------------|------| | 360T ALIAS * X | 360T ALIAS Company 1 * X | |----------------|--------------------------| |----------------|--------------------------| | 360TAS Treasurer1, 360T ALIAS // INT | Fri, 06. Jul 2018, 11:03:01 GMT // Connected [FFM] | |--------------------------------------|----------------------------------------------------| |--------------------------------------|----------------------------------------------------| Figure 18 Bank Basket: Blocked RFS Providers The Provider Groups themselves can be edited (values added or removed) without changing a set of rules based on those groups. Providers may also be temporarily blocked from particular request types without affecting the configured Provider Group. A Blocked Provider will remain in a Provider Group but will appear with a \"blocked\" symbol. If a Provider should be removed completely, the relationship should be rejected using the Counterpart Relationship Management tool. In this case, please refer to the relevant user guide. | | | | Preferences Administration Help AA X | |--|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 691, "total_tokens": 1690, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.8092429637908936}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 680, "total_tokens": 1679, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.534933805465698}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 723, "total_tokens": 1722, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "----------------|-----------------------------------------------------------------------------------------------------------------------| | | RFS REQUESTER DEAL TRACKING | + BRIDGE ADMINISTRATION | | | | Q <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> < 360T.ALIAS | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets <img alt=\"icon\" src=\"icon_image\"/> RFS Provider Groups Blocked RFS Providers RFS FX Bank Baskets RFS MM Bank Baskets RFS Commodity Bank Baskets RFS Cross Currency Netting Bank Baskets | | | | 360T.ALIAS.Company 1 360T.ALIAS.Company 2 360T.ALIAS.Company 3 360T.ALIAS.Company 4 360T.ALIAS.Company 5 360T.ALIAS.Company 6 | Provider Group Default Group Nordic Banks AUD Banks | <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> Create Group | | | | Select Member for \"Nordic Banks\" Available Providers Selected Providers Barclays BARX.DEMO BOAL DEMO CITIBANK DEMO COBA DEMO RBS.LND.DEMO SOCGEN.LND DEMO TB-HSBC DEMO | - x 360TBANK.TEST RBC.DEMO SEB FRA.DEMO | | | 360TAS Treasurer1, 360T ALIAS // INT | Create Change Request 360T ALIAS x 360T.ALIAS Company 1 x | Discard All Changes Save Fri, 06. Jul 2018, 11:06:37 GMT // Connected [FFM] · | Figure 19 Bank Basket: RFS Provider Groups With the initial activation of the Bank Basket configuration, the Default Group will include all active-relationship providers (where the Counterpart Relationship is \"green-green\"). Note: New Counterparties (newly accepted relationships) will be added to the Default Group. To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save. Adding or removing providers is done by moving certain banks from \"Available Providers\" to \"Selected Providers\" using the arrow buttons. ## 3.3 Bank Basket Rules #### 3.3.1 Defining Bank Basket Rules Once all configuration groups are in place, individual rules may be defined using the relevant groups for each Bank Basket area (RFS, Orders and SEP). The workflow to define bank basket rules is very similar across all Bank Basket areas. The relevant Configuration Groups may vary. With the initial activation, all Bank Baskets will contain one single rule denoted with a combination of Any / Default Group for each parameter. To add a new rule: Click Add Rule. Select an option from each of the configuration groups using the drop down menu. Confirm the selection by clicking the green check mark . Click Save. | | | | | | v Preferences v Administration v Help G AA - C X | | |-----------------------------|--------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------|-------------------------------|------------------------------------------------------|-------------------| | | <b>RFS REQUESTER<", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 838, "total_tokens": 1837, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.959823131561279}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 827, "total_tokens": 1826, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 5.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 870, "total_tokens": 1869, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.****************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "/b><br><b>DEAL TRACKING</b> | BRIDGE ADMINISTRATION<br>$+$ | | | | | | 合 | Q 卷 1<br>△ 盒 360T.ALIAS | Configuration Groups<br>RFS Bank Baskets | Orders Bank Baskets<br>SEP Bank Baskets | $\\partial \\mathcal{E}_k$ | | $\\Omega \\cap \\Xi$ | | $\\mathcal{L}_{\\mathcal{F}}$ | 盒 360T.ALIAS.Company 1 | Orders Provider Groups<br><b>Blocked Orders Providers</b> | Orders FX Bank Baskets | | | | | ₿ | 盒 360T.ALIAS.Company 2<br>意 360T ALIAS Company 3 | POS Product Type | Currency Couple | <b>Time Period</b> | <b>Bank Basket</b> | | | | 盒 360T.ALIAS.Company 4 | Default Group<br>$21 - 1$ | M/m | Default Group | Preferred Limit Order Providers | M E | | 凾 | 盒 360T.ALIAS.Company 5 | Default Group<br>$-2$ | AUD/*** | Default Group | Preferred Limit Order Providers | | | - | 盒 360T.ALIAS.Company 6 | 3<br>Any | Any | Any | Default Group | | | 同 | | Default Group<br>$\\hat{\\phantom{a}}$ | Default Group<br>$\\checkmark$ | Default Group<br>$\\checkmark$ | Default Group<br>$\\checkmark$ | Mi | | $\\frac{1}{\\sqrt{2}}$ | | Any | | | | Add Rule | | ශී | | Default Group<br>Spot. | | | | | | | | Options<br>Non deliverable<br>Streaming spot<br>Money market<br>Custom | | | | | | $Q$ D $Q$ | | Create Change Request | | | Discard All Changes<br><b>Save</b> | | | | | 360T.ALIAS.Company 1 ' X<br>360T.ALIAS * X | | | | | | | 360TAS.Treasurer1, 360TALIAS: // INT | | <b>SECT</b> | | Fri, 06. Jul 2018, 11:28:21 GMT // Connected [FFM] @ | | Figure 20 Bank Basket: Add Rule For each Configuration Group, the user can either explicitly select one of the previously saved groups, \"Any\" or \"Custom\". The selection of \"Custom\" in each rule parameter allows to define a criteria in that moment. A predefined Configuration group is not needed. For example, if no Currency Couple Group was defined for AUD, but a specific bank basket should be used for all AUD requests, then the rules shown in the figure below can be defined. Image /page/18/Picture/0 description: { \"image\\_description\": \"The image contains the text '360T' in a green, stylized font on a white background. The text appears to be a logo or brand name.\" } User Guide 360T Bank Baskets Configuration | | <b>DEAL TRACKING</b><br><b>RFS Requester</b> | | $+$<br><b>Bridge Administration</b> | | | | | |-----------------------------|--------------------------------------------------------------------|---------------|-------------------------------------------------|------------------------------------------------------------|---------------------|-----------------------------------|--------------| | 合 | 激<br>Q<br>$\\mathbb{Z}$ | | Configuration Groups<br><b>RFS Bank Baskets</b> | Orders Bank Baskets | SEP Bank Baskets | | の心量 | | | △ 盒 360T.ALIAS | | <b>RFS Provider Groups</b> | <b>Blocked RFS Providers</b><br><b>RFS Fx Bank Baskets</b> | RFS Mm Bank Baskets | <b>RFS Commodity Bank Baskets</b> | RFS Cross Cr | | $\\mathcal{L}_{\\mathcal{F}}$ | <", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 1017, "total_tokens": 2016, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.**************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 1006, "total_tokens": 2005, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 1049, "total_tokens": 2048, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "sup> 360T.ALIAS.Company 1</sup> | | | | | | | | $\\sim$ | 360T.ALIAS.Company 2 | POS | Product Type | <b>Currency Couple</b> | <b>Time Period</b> | <b>Bank Basket</b> | | | P | <b>童 360T.ALIAS.Company 3</b><br><sup>1</sup> 360T.ALIAS.Company 4 | ÷ | Forwards and Swaps | Scandies | Default Group | Nordic Banks | ショ | | $\\frac{1}{\\sqrt{2}}$ | <sup> 360T.ALIAS.Company 5</sup> | $\\frac{1}{2}$ | Any | $M+++$ | Any | AUD Banks | ショ | | $-$ | | ÷<br>3 | Any | AUD/*** | Any | AUD Banks | √ □ | | ę | <b>血 360T.ALIAS.Company 6</b> | ÷<br>4 | Any | Any | Any | Default Group | シー | Figure 21 Use Custom selection for Currency couples In the example above, all RFS requests with base currency AUD (Rule 2) or terms currency AUD (Rule 3) will use the AUD-Group bank basket. The tenor can be used in a similar way to determine the banks to which a request will be sent. #### 3.3.2 Order of the Rules The POS (position) column indicates which rules take precedent. For each individual request, the Bank Basket will be chosen based on the rule order 1 … n, where 1 is first. If the request meets the criteria found in Rule 1, this rule will be used to define the Bank Basket. Therefore it is advisable to sort the rules with the most restrictive definition on top. In order to move an existing rule up or down in the list, please select the relevant line with the mouse. Drag and drop the rule into the desired position. | | <b>Configuration Groups</b> | <b>RFS Bank Baskets</b> | <b>Orders Bank Baskets</b> | | SEP Bi | |----------------|-------------------------------|---------------------------------|----------------------------|------------------------|--------| | | <b>Orders Provider Groups</b> | <b>Blocked Orders Providers</b> | | <b>Orders Fx Bank</b> | | | | | | | | | | POS | Product Type | | | <b>Currency Couple</b> | | | 1 | Default Group | | | ***/AUD | | | $\\overline{2}$ | Default Group | | | AUD/*** | | Figure 22 Bank Basket: Order of Rules ## 3.4 Note on Supersonic The possibility to configure currency Bank Baskets for Supersonic is not available yet. Bank Basket selection is configured directly in the Supersonic interface by defining SEP Contributors for each spot currency pair. Therefore, the SEP Bank Baskets are currently only used to block SEP providers if needed. ## 4 BANK BASKETS EVALUATOR TOOL The Bank Baskets Evaluator Tool may assist the user in identifying which rule applies to a particular type of request. The tool can be accessed from the Bridge Administration Homepage. Image /page/19/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user interface. The main area of the screen displays the 'Administration Start' page, which includes sections for 'Configurations' and 'Actions'. Under 'Configurations', there are icons for 'Regulatory Data' and 'Bank Baskets'. Under 'Actions', ther", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 879, "total_tokens": 1878, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 868, "total_tokens": 1867, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 911, "total_tokens": 1910, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "e are icons for 'Change Request', 'Wizards', and 'Evaluator Tools'. The 'Evaluator Tools' icon is highlighted with a red dashed border. The top of the screen includes tabs for 'RFS REQUESTER', 'DEAL TRACKING', and 'BRIDGE ADMINISTRATION', along with options for 'Preferences', 'Administration', and 'Help'. The bottom of the screen displays information about the user's connection and the date and time.\" } Figure 23 Evaluator Tool Quick Link The tool may also be accessed using the Bank Basket Evaluator Tool icon located next to the Configuration Data Tabs. | | | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b> | <b>BRIDGE ADMINISTRATION</b> | Preferences Administration Help AA - X | | |--|--|--------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|------------------------------------------------------| | | | Q 360T.ALIAS<br>360T ALIAS Company 1 | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets | | | | | | 360T.ALIAS Company 2<br>360T.ALIAS Company 3 | Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product Groups | | | | | | 360T.ALIAS Company 4<br>360T.ALIAS Company 5 | Currency Group Default Group | | | | | | 360T ALIAS Company 6 | | Create Group | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Create Change Request | Discard All Changes | Save | | | | | 360T ALIAS * X 360T ALIAS Company 1 * X | | | | | | 360TAS Treasurer1, 360T ALIAS // INT | | | Fri, 06. Jul 2018, 12:04:09 GMT // Connected [FFM] . | Figure 24 Evaluator Tool icon Both methods will require the user to identify the desired company to evaluate that company's Bank Basket. If using the icon, the system will take the company Bank Basket based on the current active tab. Enter the desired parameters for a particular type of request. Click \"Find Bank Baskets Rule\". Please enter product details | Product Type: | Fx Spot | |------------------|-----------| | Currency Couple: | EUR / USD | Find Bank Baskets RuleFigure 25 Evaluator Tool The system will jump to and highlight the relevant rule. | | <b>DEAL TRACKING</b><br><b>RFS REQUESTER</b> | $+$<br><b>BRIDGE ADMINISTRATION</b> | | | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ AA $ \\Box$ X | | |----------------------------------------------------------------|-------------------------------------------------------|-----------------------------------------------------------------|--------------------------------------------|----------------------------|-----------------------------------------------------------------------------|----------| | 合 | Q 豪<br>12<br>!!!!!!!!!!!", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 837, "total_tokens": 1836, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 826, "total_tokens": 1825, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.****************}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 869, "total_tokens": 1868, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.***************}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | <b>Configuration Groups</b><br><b>RFS Bank Baskets</b> | Orders Bank Baskets<br>SEP Bank Baskets | $\\sigma$ | | のの言 | | | △ 盒 360T.ALIAS | RFS Provider Groups<br><b>Blocked RFS Providers</b> | RFS FX Bank Baskets<br>RFS MM Bank Baskets | RFS Commodity Bank Baskets | RFS Cross Currency Netting Bank Baskets | | | $\\mathcal{L}$ | 盒 360T.ALIAS.Company 1 | | | | | | | $\\qquad \\qquad \\boxdot \\qquad$ | 盒 360T.ALIAS.Company 2<br>盒 360T.ALIAS.Company 3 | POS Product Type | Currency Couple | <b>Time Period</b> | <b>Bank Basket</b> | | | $\\frac{d}{d}$ | 盒 360T.ALIAS.Company 4 | : 1 Forwards and Swaps | Scandies | Default Group | Nordic Banks | ショ | | | 盒 360T ALIAS.Company 5 | $2$ Any | ***/AUD | Any | AUD Banks | ジョ | | - | 盒 360T.ALIAS.Company 6 | $\\frac{1}{2}$ 3 Any | AUD/*** | Any | AUD Banks | ジョ | | 厚 | | 4 Any | Any | Any | Default Group | $ V $ ( | | $\\frac{1}{\\sqrt{2}}$ | | | | | | Add Rule | | | | | | | | | | $\\begin{array}{c}\\n\\bullet \\\\ \\bullet \\\\ \\bullet\\n\\end{array}$ | 360TAS.Treasurer1, 360T.ALIAS // INT | Create Change Request<br>360T ALIAS Company 1 X<br>360T.ALIAS X | <b>EECT</b> | | Discard All Changes<br>Fri, 06. Jul 2018, 12:10:31 GMT // Connected [FFM] . | Save | Figure 26 Evaluator Tool: Highlighted Rule ## 5 CONTACT 360T Image /page/21/Picture/2 description: { \"image\\_description\": \"The image shows a logo with the number '360' in a stylized font, followed by a symbol resembling two arrows pointing towards each other within a square, and then the letter 'T'. The logo is primarily green with white accents and is set against a white background.\" } #### Global Support Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL> #### Germany 360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main Phone: +49 69 900289-0 ### Middle East Asia Pacific ## United Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134 ### EMEA Americas ## USA 360 Trading Networks Inc. 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 ## Singapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "prompt_tokens": 978, "total_tokens": 1977, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.672722816467285}, "usage_stage_two": {"completion_tokens": 0, "prompt_tokens": 967, "total_tokens": 1966, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.485085964202881}, "usage_stage_three": {"completion_tokens": 0, "prompt_tokens": 1010, "total_tokens": 2009, "completion_tokens_details": null, "prompt_tokens_details": null, "time": 4.998520135879517}}
