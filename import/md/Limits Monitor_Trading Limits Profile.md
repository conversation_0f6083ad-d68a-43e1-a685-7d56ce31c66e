# **USER GUIDE**

![](_page_0_Picture_1.jpeg)

# **LIMITS MONITOR**

*FOR TRADING LIMITS*

© 360 TREASURY SYSTEMS AG, 2022

THIS FILE CONTAINS PROPRIETARY AND CONFID<PERSON><PERSON><PERSON> INFORMATION

INCLUDING <PERSON>RA<PERSON> SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR

| 1 |              | INTRODUCTION3                                      |          |
|---|--------------|----------------------------------------------------|----------|
| 2 |              | GETTING STARTED5                                   |          |
| 3 |              | DEFINING RISK PORTFOLIO GROUPS7                    |          |
| 4 |              | DEFINING RISK PORTFOLIO RULES<br>9                 |          |
|   | 4.1          | RULE ID                                            | 10       |
|   | 4.2          | COUNTERPART                                        | 10       |
|   | 4.3          | PRODUCT<br>                                        | 11       |
|   | 4.4          | LEGAL ENTITY GROUP<br>                             | 11       |
|   | 4.5          | DEALER                                             | 12       |
|   | 4.6          | ALGORITHMS<br>                                     | 13       |
|   | 4.6.1        | Daily Gross Trading Limit<br>                      | 13       |
|   | 4.6.2        | Per Deal Limit                                     | 14       |
|   | 4.7          | LIMIT<br>                                          | 14       |
|   | 4.8          | EDIT RULE<br>                                      | 14       |
|   | 4.9          | ACTIVATE/DEACTIVATE RULE<br>                       | 14       |
|   | 4.10<br>4.11 | DELETE RULE<br><br>BULK UPLOAD/DOWNLOAD VIA CSV    | 15<br>15 |
| 5 |              | ACTIVE RULES<br>18                                 |          |
|   | 5.1          | APPLYING RULES AND LIMITS TO TRADES                | 20       |
|   | 5.1.1        | Reallocating Trades After End of Day Rollover:<br> | 20       |
|   | 5.2          | CALCULATING UTILIZATION<br>                        | 20       |
|   | 5.2.1        | Daily Gross Trading Limit<br>                      | 23       |
|   | 5.2.2        | Per Deal Limit                                     | 23       |
|   | 5.3          | UTILIZATION RESET                                  | 24       |
|   | 5.4          | VISUALIZATION<br>                                  | 24       |
|   | 5.4.1        | Risk Entries                                       | 24       |
|   | 5.5          | ALERT EMAILS                                       | 25       |
|   | 5.6          | SNAPSHOT REPORTS<br>                               | 26       |
| 6 |              | LIMIT CHECK FLOW27                                 |          |
| 7 |              | AUDIT LOG<br>31                                    |          |
| 8 |              | CONTACT 360T<br>32                                 |          |

| Figure 1 Header Bar5                     |  |
|------------------------------------------|--|
| Figure 2 Bridge Administration Homepage5 |  |
| Figure 3 Risk Portfolio Homepage6        |  |
| Figure 4 HTML Login6                     |  |
| Figure 5 HTML Access to Limits Monitor7  |  |
| Figure 6 Risk Portfolio Groups<br>8      |  |

| Figure 7 Counterpart Groups9                                                           |  |
|----------------------------------------------------------------------------------------|--|
| Figure 8 Risk Portfolio Rules<br>9                                                     |  |
| Figure 9 Defining Rule ID<br>10                                                        |  |
| Figure 10 Defining counterpart for risk portfolio rules<br>10                          |  |
| Figure 11 Defining product for risk portfolio rules<br>11                              |  |
| Figure 12 Defining legal entity for risk portfolio rules<br>12                         |  |
| Figure 13 Defining dealer for risk portfolio rules<br>12                               |  |
| Figure 14 Setting algorithm for risk portfolio rules13                                 |  |
| Figure 15 Defining limit for risk portfolio rules14                                    |  |
| Figure 16 Risk Portfolio Rules Upload/Download<br>15                                   |  |
| Figure 17 Upload Result File<br>16                                                     |  |
| Figure 18 CSV Column separator setting<br>16                                           |  |
| Figure 19 Updating the limit for current trading date18                                |  |
| Figure 20 Filtering the rules by using search function19                               |  |
| Figure 21<br>Monitoring the updated utilization amounts within Active Rules tab.<br>21 |  |
| Figure 22 Reset Utilization<br>24                                                      |  |
| Figure 23 Risk Entries<br>24                                                           |  |
| Figure 24 Alert emails<br>25                                                           |  |
| Figure 25 Limit Breach Details in TWS28                                                |  |
| Figure 26 Limit Breach Details in TWS for Orders<br>29                                 |  |
| Figure 27 Audit Log31                                                                  |  |

| Table 1 Field Definition for csv risk portfolio rule upload18                                |  |
|----------------------------------------------------------------------------------------------|--|
| Table 2: Field definition for csv active limit upload20                                      |  |
| Table 3: Sample trades<br>22                                                                 |  |
| Table 4: 360T EOD rates to convert Cashflow/Notional Ledgers into USD (credit<br>currency)22 |  |

# <span id="page-2-0"></span>**1 INTRODUCTION**

The purpose of 360T Limits Monitor is to provide 360T clients a rule-based and parametrized limit monitoring and pre-trade limit check system which ensures that the risks resulting from permissioned trading relationships do not exceed specified limits.

Limits Monitor has two different profiles which differentiates the functions made available to the users on entity level:

"Trading Limits" profile grants access to part of the configuration parameters such as product, counterpart, dealer as well as groups of these fields and daily gross trading as well as per deal limit types.

"Full Profile" grants full access to the tool which grants access to the additional parameters such as currency pair, execution method and time period as well as daily and aggregate settlement limits both net and gross and Potential Future Exposure. Please contact [<EMAIL>](mailto:<EMAIL>) or your customer relationship manager if you are interested in getting onboarded with full access.

This user guide describes the functionalities provided for Trading Limits profile and how it can be administrated via Risk Portfolio in Bridge Administration panel.

Trading limits can be defined for many parameters such as counterpart, legal entity, dealer and product type.

In order to set limits on specific segments of your trading portfolio that are important with respect to the types of risk you face, you can define Risk Portfolios based on individual or groups of related counterparties or internal accounts/entities, dealers and products. The criteria and steps to define these risk portfolios are described in detail in this user guide.

Defined *risk portfolio*s can overlap and do not necessarily have to be mutually exclusive. The exposure resulted from the allocated trades is calculated based on the defined algorithm for that specific risk portfolio.

360T`s new limit functionality currently captures all *FX Spot, FX Forward, FX Swap, NDF, NDS, Block FX Forward and Block NDF, FX Time Option* trades, negotiated as RFS, Streaming, MidMatch or Order and initiated through different 360T applications such as *Bridge* , *SST* or *EMS, as well as taker API interfaces.* 

The allocation of trades to the risk portfolios is done based on the defined parameters of Risk Portfolio rules which is explained in Section 3 and Section 4 in detail.

The tool allows clients to define whether a trade which does not fall into any of the defined Risk Portfolios should basically be allowed or not, by a generic configuration in the initial onboarding. The trade intentions / orders which cannot be allocated to any of the clusters can be either

- Allowed (Risk Portfolios to be configured as *Constraints*)
- Or disallowed (Risk Portfolios to be configured as *Permissions*).

All limits defined for the Risk Portfolios are in credit currency<sup>1</sup> . The conversion of trade amounts is done using the end-of-day rates of the 360T Essential Data Feed.

It is important to note that only users with corresponding user rights are able to administer the Risk Portfolio within Bridge. Please contact [<EMAIL>](mailto:<EMAIL>) or your customer relationship manager for setting up the administrator rights.

<sup>1</sup> Credit currency (also referred to as company currency or home currency) is a single currency defined for 360T entity accounts in the initial onboarding stage. This parameter can be changed by CAS teams upon request.

# <span id="page-4-0"></span>**2 GETTING STARTED**

Limits Monitor can be administrated manually via Risk Portfolio module within the Bridge Administration tool as well as automatically via API. Bridge Administration can be accessed via the menu option "Administration" in the screen header of the Bridge application.

|                              | $\vee$ Preferences | A Administration | $\vee$ Help $\bigcup$ $\bigcirc$ $\bigcirc$ AA - $\Box$ X |  |  |
|------------------------------|--------------------|------------------|-----------------------------------------------------------|--|--|
|                              |                    |                  |                                                           |  |  |
| Change Password              |                    |                  |                                                           |  |  |
| <b>Bridge Administration</b> |                    |                  |                                                           |  |  |
|                              |                    |                  |                                                           |  |  |
|                              |                    |                  |                                                           |  |  |
|                              |                    |                  |                                                           |  |  |
|                              |                    |                  |                                                           |  |  |
|                              |                    |                  |                                                           |  |  |

<span id="page-4-1"></span>Figure 1 Header Bar

The Bridge Administration feature opens to a homepage with shortcuts to several different administration tools and actions for the particular user.

A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.

The Risk Portfolio administration panel is opened using the Risk Portfolio button on the Bridge Administration homepage.

![](_page_4_Picture_9.jpeg)

Figure 2 Bridge Administration Homepage

<span id="page-4-2"></span>The "Risk Portfolio" icon opens a navigation panel which shows the institution tree. Depending on the setup, the tree may include a single entity or several entities if the user`s entity has trade-as, trade-on-behalf or other ITEX entities configured under the main entity.

![](_page_5_Picture_2.jpeg)

Figure 3 Risk Portfolio Homepage

<span id="page-5-0"></span>A single-click on the entity name opens the Risk Portfolio panel.

For users who wants to access the tool via their internet browser, there is also an HTML SSO login available (For activation, please contact Client Advisory Services Team).

After multi-factor authentication, user will be able to see all trading applications as well as Self-Service portal, which is the administration panel on HTML.

![](_page_5_Picture_7.jpeg)

Figure 4 HTML Login

<span id="page-5-1"></span>Clicking in Self-Service Portal icon will direct user to the 360T Self Service Portal Start Page where all administration panels user has access is displayed. Clicking on LIMMO icon will launch the Limits Monitor administration panel.

![](_page_6_Picture_2.jpeg)

Figure 5 HTML Access to Limits Monitor

<span id="page-6-1"></span>IMPORTANT: Please note that clients with several legal or sub-entities within 360T system will see all of the related entities on the left-hand side menu. However, risk portfolio rules should be created for the credit entity which needs to assign the credit lines to its counterparts and be part of the trades (i.e. main entity).

# <span id="page-6-0"></span>**3 DEFINING RISK PORTFOLIO GROUPS**

Risk Portfolio Groups facilitate the management of parameters that can be used in risk portfolio rule definition. This allows admin users to have a clear overview of the risk categories they wish to create and makes the limit definition process more efficient by giving users the ability to combine several values into one field. It also simplifies the management of parameters as any change to a risk portfolio group affects each associated risk portfolio rule.

| Risk Portfolio Groups Risk Portfolio Rules Active Rules = |                                                                     |            |               |  |                                      |  |  |  |
|-----------------------------------------------------------|---------------------------------------------------------------------|------------|---------------|--|--------------------------------------|--|--|--|
|                                                           | Product Groups Legal Entity Groups Dealer Groups Counterpart Groups |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           | $\alpha$                                                            |            | $\rightarrow$ |  |                                      |  |  |  |
| Group Name                                                |                                                                     |            |               |  |                                      |  |  |  |
|                                                           | Deliverables                                                        |            | $V$ B         |  |                                      |  |  |  |
|                                                           | NonDeliverables                                                     |            | <b>ze</b>     |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     | $\! +$     |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           | Available (5)                                                       |            | Selected (2)  |  |                                      |  |  |  |
|                                                           | <b>Fx Spot</b>                                                      | <b>NDS</b> |               |  |                                      |  |  |  |
|                                                           | <b>Px Forward</b>                                                   | <b>NDF</b> |               |  |                                      |  |  |  |
|                                                           | <b>Fx Swap</b><br><b>Block-Trade</b>                                |            |               |  |                                      |  |  |  |
|                                                           | <b>Fx Time Option</b>                                               |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     | $\,$       |               |  |                                      |  |  |  |
|                                                           |                                                                     | $\epsilon$ |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     | $\,$       |               |  |                                      |  |  |  |
|                                                           |                                                                     | $\ll$      |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  | Discard all Changes Contract Changes |  |  |  |
| CAGLARCOMP1.TEST X                                        |                                                                     |            |               |  |                                      |  |  |  |
|                                                           |                                                                     |            |               |  |                                      |  |  |  |

<span id="page-7-0"></span>Figure 6 Risk Portfolio Groups

Risk Portfolio Groups consist of the following parameters:

- *Product Groups*: Includes FX Spot, FX Forward, FX Swap, NDF, NDS, FX Time Option and Block-Trades.
- *Legal Entity Groups*: An entity or entities (i.e. in case trades are executed on behalf of several legal entities) can be defined within *Legal Entity Groups.*
- *Dealer Groups:* Single or multiple dealers of an entity can be grouped together via Dealer Groups. All active users of an entity who is allowed to trade will appear in the list.
- *Counterpart Groups*: Where admin users would like to set a single credit line for several counterparts (either based on credit rating, country risk or related entities etc.), they can create a counterpart group and add multiple counterparts into that group. The available members are determined based on the permissioned trading relationship.

![](_page_8_Picture_2.jpeg)

Figure 7 Counterpart Groups

# <span id="page-8-1"></span><span id="page-8-0"></span>**4 DEFINING RISK PORTFOLIO RULES**

By combining different risk parameters such as counterparty, legal entity, dealer and product within a Risk Portfolio, limits can be defined for many different types of exposure.

With a single-click on button, a new rule will be added as a row with nine parameters shown as separate columns, to be defined by the admin user.

IMPORTANT: Please note that adding, removing or amending a rule will be effective immediately *after end of day rollover*. This occurs at 5:00 PM New York Time. Limits can be updated intraday with an immediate effect within the Active Rules tab.

The switch button on the left of each rule is used to enable or disable the rule, mitigating the need to remove rules and the requirement to redefine should they be needed again.

The other parameters of the risk portfolio rules will be described in the following subsections in detail.

| Risk Portfolio Groups (Risk Portfolio Rules ) Active Rules = |                 |                      |                    |                                      |                   |                   |   |                                                 |                  |                     |   | $\begin{array}{c} \pm \\ \pm \end{array}$ |
|--------------------------------------------------------------|-----------------|----------------------|--------------------|--------------------------------------|-------------------|-------------------|---|-------------------------------------------------|------------------|---------------------|---|-------------------------------------------|
|                                                              | $^{\circ}$      |                      |                    | $\rightarrow$                        |                   |                   |   |                                                 | Constraints      | $\vee$ Rules Switch |   |                                           |
|                                                              | Enabled Rule Id |                      | $\vee$ Counterpart | Product                              | Legal Entity      | Dealer            |   | Algorithm                                       | Limit            |                     |   |                                           |
|                                                              |                 | FatFinger            | Any                | $V$ Any                              | $\frac{1}{2}$ Any | $\frac{1}{2}$ Any |   | / Per Deal Limit                                | $7/$ (25,000,000 |                     |   |                                           |
|                                                              | ∞               | 360TBank-ND          |                    | 360TBANKTEST 5 NonDeliverables 5 Any |                   | $Z$ Any           |   | V Daily Gross Trading Limit V   (50,000,000     |                  |                     | × |                                           |
|                                                              | O               | 360TBank-Deliverable |                    | 360TBANKTEST / Deliverables / Any    |                   |                   |   | Many / Daily Gross Trading Limit / (100,000,000 |                  |                     |   |                                           |
|                                                              |                 |                      |                    |                                      |                   |                   | - |                                                 |                  |                     |   |                                           |

<span id="page-8-2"></span>Figure 8 Risk Portfolio Rules

# <span id="page-9-0"></span>**4.1 Rule ID**

Rule ID field is a text-box in which users can define the name/ID of their rules. The field helps users to identify their rules and allows an easier matching between several limit systems they are administrating.

| <b>Risk Portfolio Groups</b>             | <b>Risk Portfolio Rules</b> | <b>Active Rules</b>      | Ξ<br><b>Risk Portfolio PFF</b>             |                     |                         |                                    |                                                           |                        | 土土 |
|------------------------------------------|-----------------------------|--------------------------|--------------------------------------------|---------------------|-------------------------|------------------------------------|-----------------------------------------------------------|------------------------|----|
| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                             | $\rightarrow$            |                                            |                     |                         |                                    | Constraints                                               | <b>VO</b> Rules Switch |    |
| Enabled                                  | Rule Id                     | $\wedge$ Counterpart     | Portfolio                                  | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Ex Time Period</b>              | Algorithm                                                 | Limit                  |    |
| <b>CO</b>                                | 360T Group Risk             | 360T Group Related / Any |                                            | $\mathbb{Z}$ Any    | $\mathbb{Z}$ Any        | $\mathbb{Z}$ Any<br>$\overline{z}$ | Gross Daily Settlement Limit / 0                          |                        |    |
| $\sigma$                                 | 360T.MMC PFE                | 360T.MMC                 | $\frac{3}{2}$ Any vs G10 $\frac{3}{2}$ Any |                     | $\frac{1}{2}$ Any       |                                    | ジ TODAY-1 MONTH ジ Potential Future Exposure<br>$\bar{z}$  | 45,500,000             |    |
| Ø                                        | 360T.MMC Settlement         | 360T.MMC                 | $\frac{3}{2}$ Any                          | $\frac{3}{2}$ Any   | $\frac{3}{2}$ Any       |                                    | TODAY-3 MONTHS 5 Net Daily Settlement Limit 5 (51,000,000 |                        |    |

<span id="page-9-2"></span>Figure 9 Defining Rule ID

The Rule ID value also serves to match the rules in Risk Portfolio Rules and Active Rules tab.

# <span id="page-9-1"></span>**4.2 Counterpart**

The *Counterpart* field defines the single trading counterparty or group of counterparties for which limits will be defined. As described in Section 3, admin users can group several counterparties and set generic rules and limits for the group. Once a rule has been added, users will be able to select the desired counterpart from a list which includes permissioned counterparts and pre-defined counterpart groups. Please note that the group values appear on top of the list.

| (a)                  |                          |                           | Please select: | $\times$      | Constraints                                                            | <b>O</b> Rules Switch<br>$\vee$ |  |
|----------------------|--------------------------|---------------------------|----------------|---------------|------------------------------------------------------------------------|---------------------------------|--|
| Enabled              | Rule Id                  |                           |                |               | Algorithm                                                              | Limit                           |  |
| ♡●                   | 360T Group               | Q                         |                | $\rightarrow$ | $\mathbb{Z}^2$ Gross Daily Settlement Limit $\mathbb{Z}^2$ (0)         |                                 |  |
| VO                   | 360T.MMC PFE             |                           | Available (13) |               | TH $\frac{1}{2}$ Potential Future Exposure $\frac{1}{2}$ (45,500,000   |                                 |  |
| $\sqrt{\phantom{a}}$ | 360T.MMC Settlement      | Rating C+                 |                |               | THS $\mathbb{R}$ Net Daily Settlement Limit $\mathbb{R}$   (51,000,000 |                                 |  |
| $\sqrt{\phantom{a}}$ | 360TBANK PFE             | 360T Group Related        |                |               | TH J Potential Future Exposure J 67,500,000                            |                                 |  |
| V O                  | 360TBANK Settlement      | 360T.MMC<br>360TBANK.TEST |                |               | THS $5$ Net Daily Settlement Limit $5$ (73,000,000                     |                                 |  |
| $\sigma$             | <b>COBA PFE</b>          | BNPP, PAR, DEMO           |                |               | TH J Potential Future Exposure J (23,500,000                           |                                 |  |
| $\sqrt{\phantom{a}}$ | <b>COBA Settlement</b>   | BOAL.DEMO                 |                |               | THS $\frac{1}{2}$ Net Daily Settlement Limit $\frac{1}{2}$ (29,000,000 |                                 |  |
| V O                  | PEBANK PFE               | CAGLAR.TEST               |                |               | TH = Potential Future Exposure = = 7 (34,500,000                       |                                 |  |
| $\sqrt{\phantom{a}}$ | <b>PEBANK Settlement</b> | CAGLARMTF.TEST            |                |               | THS F Net Daily Settlement Limit F 40,000,000                          |                                 |  |
| $\sqrt{\phantom{a}}$ | <b>RBS PFE</b>           | COBA.DEMO<br>MOCK.TEST    |                |               | TH 5 Potential Future Exposure 5 2 2.000.000                           |                                 |  |
| $\sqrt{\phantom{a}}$ | <b>RBS Settlement</b>    | PEBANK_APAC.TEST          |                |               | THS 3 Net Daily Settlement Limit 3 (24,000,000                         |                                 |  |
| VO                   | SEB PFE                  | RBS.LND.DEMO              |                |               | TH $\mathbb{R}$ Potential Future Exposure $\mathbb{R}$ (56,500,000     |                                 |  |
| VO                   | <b>SEB Settlement</b>    | SEB.FRA.DEMO              |                |               | THS $5$ Net Daily Settlement Limit $5$ 62,000,000                      |                                 |  |
|                      | <b>Total Net Limit</b>   |                           |                |               | Aggregate Net Settlement L. 3/ (2,000,000,000                          |                                 |  |
|                      |                          |                           |                |               |                                                                        |                                 |  |

<span id="page-9-3"></span>Figure 10 Defining counterpart for risk portfolio rules

It is also possible to make the rule applicable to all permissioned counterparties by clicking on *Apply `Any`*.

# <span id="page-10-0"></span>**4.3 Product**

The *Product* field defines the group of products to which the limits will be applied. As described i[n Section 3,](#page-6-0) admin users can create *Product Groups* from the *Risk Portfolio Groups* tab. The pre-configured product groups will then be available in the *Product*  field of the added rule.

In addition to define pre-configured product groups in risk portfolio rule, it is also possible to *Apply `Any`* which makes the rule applicable for all portfolios, thus for every currency pair and product traded.

![](_page_10_Picture_5.jpeg)

Figure 11 Defining product for risk portfolio rules

# <span id="page-10-2"></span><span id="page-10-1"></span>**4.4 Legal Entity Group**

The *Legal Entity Group* field defines the group of related entities for which the risk limit will be defined. As described in Section 3, admin users can group several legal entities in order to set generic rules and limits for them. Once a rule has been added, users will be able to select the desired legal entity or pre-defined legal entity group by by selecting it and then clicking on *Apply Selected*. It is also possible to select `Any` which would make the rule applicable to all the legal entities involved in trading from the client's perspective.

*Legal Entity Group* parameter can be especially useful for clients with multiple legal entities who need to manage risks centrally.

| $\alpha$             |                          |               |                       | $\times$              |                                                                        | Constraints $\vee$ | <b>O</b> Rules Switch |  |
|----------------------|--------------------------|---------------|-----------------------|-----------------------|------------------------------------------------------------------------|--------------------|-----------------------|--|
| Enabled Rule Id      |                          | (Q            | Please select:        | $\rightarrow$         | Algorithm                                                              | Limit              |                       |  |
| $\circledcirc$       | 360T Group Risk          |               | Available (1)         |                       | $\mathbb{Z}^2$ Gross Daily Settlement Limit $\mathbb{Z}^2$   (0)       |                    | 而                     |  |
| √●                   | 360T.MMC PFE             | 360T.RMS.TAS1 |                       |                       | TH 5 Potential Future Exposure 5 45,500,000                            |                    | 亩                     |  |
| QO.                  | 360T.MMC Settlement      |               |                       |                       | THS F Net Daily Settlement Limit F 51,000,000                          |                    | 前                     |  |
| $\sigma$             | 360TBANK PFE             |               |                       |                       | TH I Potential Future Exposure I 67,500,000                            |                    | 面                     |  |
| $\bullet$            | 360TBANK Settlement      |               |                       |                       | THS $\frac{1}{2}$ Net Daily Settlement Limit $\frac{1}{2}$ (73,000,000 |                    | 面                     |  |
| $\sigma$             | COBA PFE                 |               |                       |                       | TH = Potential Future Exposure = = 23,500,000                          |                    | 前                     |  |
| $\sigma$             | <b>COBA Settlement</b>   |               |                       |                       | THS $\frac{1}{2}$ Net Daily Settlement Limit $\frac{1}{2}$ (29,000,000 |                    | 育                     |  |
| $\sigma$             | PEBANK PFE               |               |                       |                       | TH / Potential Future Exposure / 34,500,000                            |                    | iîi                   |  |
| $\sigma$             | <b>PEBANK Settlement</b> |               |                       |                       | THS $\mathbb{R}$ Net Daily Settlement Limit $\mathbb{R}$ (40,000,000   |                    | 面                     |  |
| $\sqrt{2}$           | <b>RBS PFE</b>           |               |                       |                       | TH = Potential Future Exposure = = 2,000,000                           |                    | 前                     |  |
| $\sigma$             | <b>RBS Settlement</b>    |               |                       |                       | THS F Net Daily Settlement Limit F 24,000,000                          |                    | 育                     |  |
| $\sqrt{\phantom{a}}$ | SEB PFE                  |               |                       |                       | TH IV Potential Future Exposure IV 56,500,000                          |                    | 面                     |  |
| (VO)                 | <b>SEB Settlement</b>    |               |                       |                       | THS $\mathbb{R}$ Net Daily Settlement Limit $\mathbb{R}$   62,000,000  |                    | 面                     |  |
| rv o                 | <b>Total Net Limit</b>   |               |                       |                       | 5 Aggregate Net Settlement L. 5 (2,000,000,000                         |                    | 前                     |  |
|                      |                          |               | Apply "Any"<br>Cancel | <b>Apply Selected</b> |                                                                        |                    |                       |  |

<span id="page-11-1"></span>Figure 12 Defining legal entity for risk portfolio rules

# <span id="page-11-0"></span>**4.5 Dealer**

*Dealer* parameter defines the single or group of individuals who executed the transaction on behalf of the credit entity. As described in *Section 3,* it is possible to group the dealers in order to assign one single limit for a group of them. As well as created groups, all dealers who can trade (Trader, Treasurer, Hybrid and API types of users) and are active will appear as available value for this parameter.

| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> |                                          | Active Rules Risk Portfolio PFE    | $=$                   |                       |                       |  |                                                         |              |                             | 出土  |
|------------------------------|-----------------------------|------------------------------------------|------------------------------------|-----------------------|-----------------------|-----------------------|--|---------------------------------------------------------|--------------|-----------------------------|-----|
| $\sqrt{a}$                   |                             |                                          | $\rightarrow$                      |                       |                       |                       |  | Permissions                                             | $\checkmark$ | <b>O</b> Rules Switch       |     |
| Enabled Rule Id              |                             | $\vee$ Cour                              |                                    | Please select:        | $\times$              | <b>Fx Time Period</b> |  | Algorithm                                               |              | Limit                       |     |
| ☞                            | RCM                         | Any                                      |                                    |                       |                       | Any                   |  | IV Daily Net Trading Limit                              |              | $\mathbb{Z}$ (2,000,000,000 | I û |
| ▽●                           | RCL-GBP                     | Any                                      | Q                                  |                       | $\rightarrow$         |                       |  | TODAY-1 MONTH   Maily Net Trading Limit                 |              | $\frac{3}{2}$ (500,000,000) | 亩   |
| ▽●                           | <b>RCL-CHF</b>              | Any                                      |                                    | Available (5)         |                       |                       |  | TODAY-1 MONTH $\mathbb{R}$ Daily Net Trading Limit      |              | $7/$ 350,000,000            | 亩   |
| ∞                            | 97047993-ER                 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Swap Dealers<br>360TMMC.AutoDealer |                       |                       | Any                   |  | T/ Gross Daily Settlement Li. T/ 20,000,000             |              |                             | 面   |
| $\sim$ 0                     | 22175403-WER                | 221                                      | 360TMMC.Trader1                    |                       |                       |                       |  | SPOTNEXT-1 YEAR / Potential Future Exposure / 2,400,000 |              |                             | 盲   |
| $\sigma$                     | 22175403-ER                 | 221                                      | 360TMMC.API                        |                       |                       | Any<br>Any            |  | Gross Daily Settlement Li.   20,000,000                 |              |                             | 面   |
| $\circ$                      | 22175394-ER                 | 221                                      | 360TMMC.Trader2                    |                       |                       |                       |  | Cross Daily Settlement Li. 7 7,500,000                  |              |                             | 前   |
|                              |                             |                                          |                                    |                       |                       |                       |  |                                                         |              |                             |     |
|                              |                             |                                          |                                    |                       |                       |                       |  |                                                         |              |                             |     |
|                              |                             |                                          |                                    | Apply "Any"<br>Cancel | <b>Apply Selected</b> |                       |  |                                                         |              |                             |     |

<span id="page-11-2"></span>Figure 13 Defining dealer for risk portfolio rules

# <span id="page-12-0"></span>**4.6 Algorithms**

The creation of risk portfolios provides a solution to address different types of risk exposures through different combinations of trade parameters, and this is further enhanced by the ability to assign different risk exposure calculation methodologies.

| <b>MISK PORTIONO GROOPS ANSAFARITIONS MARKS ACTIVE NUMBER</b> |                      |                          |          |                                  |                |                                                                                                                                                             |                                     | $\frac{1}{2}$ |
|---------------------------------------------------------------|----------------------|--------------------------|----------|----------------------------------|----------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------|---------------|
|                                                               | $\sqrt{a}$           |                          |          | $\rightarrow$                    |                |                                                                                                                                                             | Constraints v <b>D</b> Rules Switch |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      | Enabled Rule Id          |          |                                  |                | v Counterpart Product Legal Entity Dealer Algorithm National Limit                                                                                          |                                     |               |
|                                                               | $\sigma$             | FatFinger<br>360TBank-ND |          |                                  |                | Any of Any of Any of Any of Per Deal Limit of (25,000,000)<br>360TBANKTEST V   NonDeliverables V   Any V   Any V   Daily Gross Trading Limit V   50,000,000 | ٠                                   |               |
|                                                               | $\overline{(\cdot)}$ |                          |          |                                  |                | 360TBank-Deliverable 360TBANKTEST & Deliverables & Any by Any 19 Daily Cross Trading Limit 8 (100,000,000                                                   | ٠                                   |               |
|                                                               | $\circ$              |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  | Please Select: | $\times$                                                                                                                                                    |                                     |               |
|                                                               |                      |                          | $\alpha$ |                                  |                | $\rightarrow$                                                                                                                                               |                                     |               |
|                                                               |                      |                          |          |                                  | Available (2)  |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          | Per Deal Limit                   |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          | <b>Daily Gross Trading Limit</b> |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                | Cancel Apply                                                                                                                                                |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |
|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |

<span id="page-12-2"></span>Figure 14 Setting algorithm for risk portfolio rules.

The *Algorithm* field of a risk portfolio rule defines what the user is limiting.

Two different algorithms are available for Trading Limits Profile:

- **Daily Gross Trading Limit**
- **Per Deal Limit**

Users who has full access to the tool can also assign one of the below limit types to their risk portfolios:

- **Net Daily Settlement Limit,**
- **Gross Daily Settlement Limit,**
- **Aggregate Net Settlement Limit,**
- **Aggregate Gross Settlement Limit**
- **Potential Future Exposure and**
- **Daily Net Trading Limit**

Please contact your Account Manager to receive more information on the other methodologies.

### <span id="page-12-1"></span>**4.6.1 Daily Gross Trading Limit**

Daily Gross Trading Limit algorithm can be used to limit the total intraday trading volume for the current trading day.

*For example: If Credit Entity* A sets a 10 million Euro trading limit for a specific risk portfolio rule, the company currency equivalent of total transaction done for the current trading day cannot exceed 10 million Euro for that specific risk portfolio. After end of day rollover, the utilization is reset to 0 and another 10 million Euro limit is available.

#### <span id="page-13-0"></span>**4.6.2 Per Deal Limit**

Also known as Fat-Finger limit, Per Deal Limit aims to restrict the notional amount of one single deal. This limit type is mostly used to address operation risk and associated with dealers.

## <span id="page-13-1"></span>**4.7 Limit**

The last parameter defined for each portfolio is the amount of the limit. The limit is defined in credit currency of the entity.

**IMPORTANT: Please note that a generic limit set on a risk portfolio rule can be amended within that rule. As with any parameter change in Risk Portfolio Rules, a generic limit change will only be valid after end of day rollover. Should an immediate change to a limit be required, this should be done within the 'Active Rules' tab; such a change will be valid immediately for the duration of that day, and overwritten by the limit set under Risk Portfolio Rules at end of day rollover.**

![](_page_13_Figure_8.jpeg)

<span id="page-13-4"></span>Figure 15 Defining limit for risk portfolio rules.

# <span id="page-13-2"></span>**4.8 Edit Rule**

Parameters of a risk portfolio rule can be changed by clicking on icon next to the relevant parameter. *Rule Id* and *Limit* parameters can be amended by single-click on the text-field. Once a change is done on the rule, it is applied by click on `Save`.

![](_page_13_Figure_12.jpeg)

## <span id="page-13-3"></span>**4.9 Activate/Deactivate Rule**

For users who want to remove a Risk Portfolio rule temporarily, the Risk Portfolio panel provides an option to deactivate the rules instead of completely deleting them. By using the toggle button ( ), it is possible to enable or disable the relevant rule.

IMPORTANT: Please note that, consistent with all actions in Risk Portfolio Rules, this action will only take effect with *end of day rollover*.

# <span id="page-14-0"></span>**4.10Delete Rule**

Risk portfolio rules can be completely removed with the `Delete Rule` function. Clicking on the icon and then saving removes the rule from the Risk Portfolio rules.

IMPORTANT: Please note that a deleted rule is still active until *end of day rollover*. (Active Rules can be monitored under `Active Rules` tab. The function of this tab is explained in [Section 5](file://///360t.com/shares/Projects/Risk%20limits/User%20Manual/360T%20User%20Guide%20Global%20Risk%20Management%20System.docx) in more detail.)

# <span id="page-14-1"></span>**4.11Bulk Upload/Download via CSV**

360T`s Limits Monitor provides a csv upload/download functionality to allow bulk upload/update of risk portfolio rules. The functionality facilitates the manual administration of rule and limit settings by allowing,

- Creation of new risk portfolio rules
- Update of any parameter of an existing risk portfolio rule
- Deletion/deactivation of an existing risk portfolio rule.

| Q         |                            |                                 | $\rightarrow$ |                                          |                   |                     |                   |                         |                       | Constraints                       |           | <b>KO</b><br><b>Rules Switch</b><br>$\checkmark$ |   |
|-----------|----------------------------|---------------------------------|---------------|------------------------------------------|-------------------|---------------------|-------------------|-------------------------|-----------------------|-----------------------------------|-----------|--------------------------------------------------|---|
| Enabled   | Rule Id                    | $\vee$ Counterpart              |               | Portfolio                                |                   | <b>Legal Entity</b> |                   | <b>Execution Method</b> | <b>Fx Time Period</b> | <b>Algorithm</b>                  |           | Limit                                            |   |
| ∞         | <b>Total Net Limit</b>     | Any                             |               | - V  <br>$\frac{y}{x}$ Any               | Any               |                     | $\frac{1}{2}$ Any |                         | $\mathbb{Z}$ Any<br>₹ | Aggregate Net Settlement Limit 3  |           | 2,000,000,000                                    | û |
| ØO        | <b>SEB Settlement</b>      | <b>SEB FRA DEMO</b>             |               | $\mathcal{V}$ Any                        | $\mathcal{V}$ Any |                     | $\mathbb{Z}$ Any  |                         | F/ TODAY-3 MONTHS F/  | Net Daily Settlement Limit        | ∌∕        | 62.000.000                                       | û |
| $\bullet$ | SEB PFE                    | SEB.FRA.DEMO                    |               | $\frac{3}{2}$ Any vs G10 $\frac{3}{2}$   | Anv               |                     | $\frac{3}{2}$ Any |                         | 5 TODAY-1 MONTH 5     | Potential Future Exposure         |           | $5/$ 56,500,000                                  | Û |
| Ø         | <b>RBS Settlement</b>      | RBS.LND.DEMO                    |               | $\mathbb{Z}$ Any<br>₩                    | Any               | $\frac{1}{2}$       | Any               | ৶                       | TODAY-3 MONTHS =      | Net Daily Settlement Limit        | Ξ/        | 24,000,000                                       | û |
| $\bullet$ | <b>RBS PFE</b>             | RBS.LND.DEMO                    |               | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Any               |                     | $\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         | $\bar{z}$ | 2,000,000                                        | û |
| Ø         | <b>PEBANK Settlement</b>   | PEBANK_APAC.TEST                |               | W.<br>Any                                | Anv               | $\overline{z}$      | Any               |                         | TODAY-3 MONTHS        | Net Daily Settlement Limit        | ₹∕        | 40,000,000                                       | û |
| $\bullet$ | PEBANK PFE                 | PEBANK_APAC.TEST / Any vs G10 / |               |                                          | Anv               |                     | $\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         |           | $7/$ (34,500,000                                 | Û |
| ØO        | <b>COBA Settlement</b>     | COBA.DEMO                       |               | $\frac{1}{2}$ Any<br>5/                  | Anv               | ₹                   | Any               | ₩                       | TODAY-3 MONTHS E      | Net Daily Settlement Limit        | ₹∕        | 29,000,000                                       | û |
| $\bullet$ | <b>COBA PFE</b>            | COBA.DEMO                       |               | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Anv               |                     | $\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         |           | $\frac{7}{2}$ (23.500.000)                       | û |
| Ø         | <b>360TBANK Settlement</b> | <b>360TBANKTEST</b>             |               | $\mathbb{Z}$ Any<br>$\overline{z}$       | Any               |                     | $\mathbb{Z}$ Any  | ₩                       | TODAY-3 MONTHS EZ     | Net Daily Settlement Limit        | ₩         | 73,000,000                                       | û |
| $\bullet$ | <b>360TBANK PFE</b>        | 360TBANK.TEST                   |               | $\mathbb{Z}$ Any vs G10 $\mathbb{Z}$     | Anv               |                     | $\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         |           | 7/67.500.000                                     | û |
| Ø         | 360T.MMC Settlement        | 360T.MMC                        |               | $\mathbb{Z}$ Any<br>$\bar{z}$            | Anv               |                     | $\mathbb{Z}$ Any  | ₩                       | TODAY-3 MONTHS E      | Net Daily Settlement Limit        | ₩         | 51,000,000                                       | ŵ |
| $\bullet$ | 360T.MMC PFE               | 360T.MMC                        |               | $V$ Any vs G10 $V$                       | Any               |                     | $\frac{3}{2}$ Any | ₩                       | TODAY-1 MONTH         | Potential Future Exposure         |           | $7/$ (45.500.000                                 | û |
| Ø         | 360T Group Risk            | 360T Group Related %            |               | ₩<br>Any                                 | Any               |                     | $\frac{3}{2}$ Any | ₩                       | TODAY-3 MONTHS E      | Aggregate Gross Settlement Li., E |           | 50,000,000                                       | û |

<span id="page-14-2"></span>Figure 16 Risk Portfolio Rules Upload/Download

By clicking on icon on top right of the *Risk Portfolio Rules* view, user can download the snapshot of their current risk portfolio rules as csv file. After making the necessary

changes in the downloaded file, user can upload the new rules by clicking on icon and then selecting the file.

Once a rule is uploaded, 360T`s Limit Monitor will create a result file to display the status of the changes. Admin user can save the result file, review it and then save or discard the changes.

#### User Guide Limits Monitor

| Save As                            |                                                                              |                 |                |                                      |          |      |                                        |      |               | $\times$         |               |                          |                       |                                                         |                                          |                            |                        |      |
|------------------------------------|------------------------------------------------------------------------------|-----------------|----------------|--------------------------------------|----------|------|----------------------------------------|------|---------------|------------------|---------------|--------------------------|-----------------------|---------------------------------------------------------|------------------------------------------|----------------------------|------------------------|------|
| $\leftarrow$ $\rightarrow$<br>个    | > This PC > Desktop > 360T Limits Monitor                                    |                 |                |                                      | $\vee$ 0 |      | C Search 360T Limits Monitor           |      |               |                  |               |                          |                       |                                                         |                                          |                            |                        | 土土   |
| New folder<br>Organize -           |                                                                              |                 |                |                                      |          |      |                                        |      | 日十            | $\bullet$        |               |                          |                       | Constraints                                             |                                          | $\checkmark$               | <b>Nules</b> Switch    |      |
| <sup>19</sup> VisualVM             | Name                                                                         |                 | File ownership | Date modified                        |          | Type |                                        | Size |               |                  |               |                          |                       |                                                         |                                          |                            |                        |      |
| <sup>1</sup> WINDOWS               | <b>D</b> 360T active limmo rules                                             |                 |                | 10/05/2021 15:18                     |          |      | Microsoft Excel C                      |      | $2$ KB        |                  | <b>Method</b> |                          | <b>Fx Time Period</b> | Algorithm                                               |                                          | Limit                      |                        |      |
| <b>This PC</b>                     | <b>Di</b> 360T active limmo rules result<br>360T limmo_curreny_couple_groups |                 |                | 10/05/2021 15:19<br>30/04/2021 17:32 |          |      | Microsoft Excel C<br>Microsoft Excel C |      | 2 K B<br>1 KB |                  |               |                          | $\frac{1}{2}$ Any     | ジ Aggregate Net Settlement Limit ジ                      |                                          | 2.000.000.000              |                        |      |
| 3D Objects<br>Desktop              | 360T limmo_curreny_couple_groups_result                                      |                 |                | 30/04/2021 17:21                     |          |      | Microsoft Excel C                      |      | 3 KB          |                  |               |                          |                       | TODAY-3 MONTHS I Net Daily Settlement Limit             | ৶                                        | 62,000,000                 | 肯                      |      |
| <b>Pall</b> Documents              | 360T limmo_rules                                                             |                 |                | 16/05/2021 13:40                     |          |      | Microsoft Excel C                      |      | 2 KB          |                  |               |                          |                       | ■ TODAY-1 MONTH ■ Potential Future Exposure             | U.                                       | 56,500,000                 |                        |      |
| <b>L</b> Downloads                 |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | 0                        |                       | TODAY-3 MONTHS $\mathcal{V}$ Net Daily Settlement Limit | ¥.                                       | 24,000,000                 | ŵ                      |      |
| h Music                            |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\overline{z}$           |                       | TODAY-1 MONTH   Potential Future Exposure               | $\bar{z}$                                | 2,000,000                  | 亩                      |      |
| Pictures                           |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\bar{z}$                |                       | TODAY-3 MONTHS 5/ Net Daily Settlement Limit            | W.                                       | 40,000,000                 | ŝ                      |      |
| Videos<br>Local Disk (C)           |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\overline{\mathscr{L}}$ |                       | TODAY-1 MONTH IV Potential Future Exposure              | ∛                                        | 34,500,000                 |                        |      |
| shares (\\office-                  |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | V                        |                       | TODAY-3 MONTHS E Net Daily Settlement Limit             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 29.000.000                 | ŵ                      |      |
|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               |                          |                       | TODAY-1 MONTH 5 Potential Future Exposure               | $\bar{z}$                                | 23,500,000                 | 旨                      |      |
| File name: 360T limmo rules result |                                                                              |                 |                |                                      |          |      |                                        |      |               | $\gamma_{\rm c}$ |               | $\bar{z}$ /              |                       | TODAY-3 MONTHS $5/$ Net Daily Settlement Limit          | $\bar{z}$ /                              | 73,000,000                 | û                      |      |
| Save as type: CSV                  |                                                                              |                 |                |                                      |          |      |                                        |      |               | $\sim$           |               | $\mathbb{Z}^{\ell}$      |                       | TODAY-1 MONTH   Potential Future Exposure               | び                                        | 67,500,000                 | 请                      |      |
| $\land$ Hide Folders               |                                                                              |                 |                |                                      |          |      | Save                                   |      | Cancel        |                  |               | V                        |                       | TODAY-3 MONTHS   Net Daily Settlement Limit             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 51,000,000                 | ŵ                      |      |
|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\overline{z}$           |                       | TODAY-1 MONTH = Potential Future Exposure               | $\bar{z}$                                | 45,500,000                 |                        |      |
|                                    | $\sigma$                                                                     | 360T Group Risk |                | 360T Group Related 5 Any             |          |      | $V$ Any                                |      | $V$ Any       |                  |               | $\mathbb{Z}^d$           |                       | TODAY-3 MONTHS   Aggregate Gross Settlement Li.         |                                          | 50,000,000                 | $\widehat{\mathbf{B}}$ |      |
|                                    | cσ                                                                           | 218631          |                | 360T Group Related 5 Any             |          |      | $5/$ Any                               |      | $5/$ Any      |                  |               |                          |                       | 3 MONTHS-6 MO., 5 Aggregate Gross Settlement LL, 5      |                                          | (20,000,000)               | 亩                      |      |
|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  | $+$           |                          |                       |                                                         |                                          |                            |                        |      |
|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               |                          |                       |                                                         |                                          | <b>Discard all Changes</b> |                        | Save |

<span id="page-15-0"></span>Figure 17 Upload Result File

For successful operation, csv should contain below columns:

Three60tID,RuleId,Active,Counterpart,Portfolio,LegalEntity,Dealer,ExecutionMethod,TimePeriod,AlgorithmType,Limit

Column separator can be selected as "," or ";" by using Preferences > Shared Settings in Bridge.

|                     |                                                   |                                           | $\land$ Preferences $\lor$ Administration $\lor$ Help $\parallel$ |           | $\bullet$ AA $-$ D $\times$ |
|---------------------|---------------------------------------------------|-------------------------------------------|-------------------------------------------------------------------|-----------|-----------------------------|
|                     |                                                   |                                           |                                                                   |           | $\times$                    |
| Design Theme        | Other Settings                                    |                                           |                                                                   |           |                             |
| <b>Display Size</b> |                                                   |                                           |                                                                   |           |                             |
| Acknowledgement     | 0 0 Enable sounds                                 |                                           |                                                                   |           |                             |
| Shared Settings     | Enable Auto Dealer ticket sounds                  |                                           |                                                                   |           |                             |
|                     | 00 Use comma as decimal separator                 |                                           |                                                                   |           |                             |
|                     | Use semicolon as CSV separator                    |                                           |                                                                   |           |                             |
|                     | 00 Open the TWS pricing panels in new windows     |                                           |                                                                   |           |                             |
|                     | 00 Disable animations                             |                                           |                                                                   |           |                             |
|                     |                                                   |                                           |                                                                   |           |                             |
|                     |                                                   |                                           |                                                                   |           |                             |
| <b>RBS PFE</b>      | $V$ Any vs G10 $V$ Any<br>$V$ Any<br>RBS.LND.DEMO | TODAY-1 MONTH   Potential Future Exposure |                                                                   | 2.000.000 |                             |

<span id="page-15-1"></span>Figure 18 CSV Column separator setting

Please note that, **upload functionality works with snapshot strategy**. This means, the validated rules that are uploaded in the last batch becomes valid as a whole, once the changes are saved. Another way to say it, **if an existing rule is not uploaded in the new batch, then the relevant rule would be removed**.

Therefore, when users

- **a)** *update an existing rule(s)* (for example, changing the limit of a specific counterparty),
- **b) add a new rule(s)** while keeping the existing configuration,

**it is recommended to download the snapshot of existing configuration via download functionality, do the changes (i.e. change the limit) on the downloaded file and then upload the file again.**

Please also note that, while creating a new rule, *Three60tID* field should come with null value, whereas while updating an existing rule, *Three60tID* value of an existing rule shouldn`t be changed.

| Field Name      | Type    | Possible Values                                                                 | Description                                                                          |
|-----------------|---------|---------------------------------------------------------------------------------|--------------------------------------------------------------------------------------|
| Three60tID      | String  | Determined by 360T.                                                             | Indicates the unique rule                                                            |
|                 |         | It must be null to create a<br>new rule.                                        | ID assigned automatically<br>by 360T.                                                |
|                 |         | The current value must be<br>provided<br>to<br>update<br>the<br>existing rule.  |                                                                                      |
| RuleId          | String  | All characters are allowed.                                                     | Defines<br>the<br>rule<br>ID                                                         |
|                 |         | When<br>left<br>empty,<br>Three60tID<br>value of the<br>rule will be populated. | determined by client.                                                                |
| Active          | Boolean | TRUE, FALSE                                                                     | Defines whether the tule<br>is set to enabled (TRUE)<br>or disabled (FALSE).         |
| Counterpart     | String  | Any or                                                                          | Defines the counterpart or                                                           |
|                 |         | 360T System name of a<br>permissioned counterpart<br>or                         | group of counterparts for<br>which the limit will be<br>applied for.                 |
|                 |         | Name<br>of<br>a<br>pre-defined<br>counterpart group.                            |                                                                                      |
| Portfolio       | String  | Any or                                                                          | Defines the product the                                                              |
|                 |         | Pre-Defined product group<br>name.                                              | limit will be applied for.                                                           |
| LegalEntity     | String  | Any or                                                                          | Defines the legal entity or                                                          |
|                 |         | 360T System name of a<br>legal entity or                                        | group of legal entities for<br>which the limit will be<br>applied for.               |
|                 |         | a pre-defined legal entity<br>group.                                            |                                                                                      |
| Dealer          | String  | Any or                                                                          | Defines<br>the<br>dealer<br>or                                                       |
|                 |         | 360T<br>System<br>name<br>of<br>credit entity`s user who<br>can trade or        | group of dealers for which<br>the limit will be applied for.                         |
|                 |         | a pre-defined dealer group                                                      |                                                                                      |
| ExecutionMethod | String  | Any (Not applicable for<br>Trading Limits)                                      | Defines<br>the<br>execution<br>method(s) for which the<br>limit will be applied for. |
| TimePeriod      | String  | Any (Not applicable for<br>Trading Limits)                                      | Defines for which value<br>dates the limit will be<br>applied for.                   |
| AlgorithmType   | String  | Daily Gross Trading Limit,<br>Per Deal Limit                                    | Defines the method to be<br>used for limit check prior<br>to executon trade and to   |

|       |         |                                     | calculate<br>the<br>utilization<br>post execution.                                                                     |
|-------|---------|-------------------------------------|------------------------------------------------------------------------------------------------------------------------|
| Limit | Decimal | Positive values up to 13<br>digits. | Defines<br>the<br>maximum<br>amount<br>that<br>can<br>be<br>utilized as per defined<br>parameters<br>and<br>algorithm. |

<span id="page-17-2"></span>Table 1 Field Definition for csv risk portfolio rule upload

# <span id="page-17-0"></span>**5 ACTIVE RULES**

The third tab of Risk Portfolio administration panel, called `Active Rules`, is a dashboard which shows currently active rules and the corresponding `Utilization` amount.

Admin users can do following operations in *Active Rules*:

**1) Edit Active Limit**: With single-click on the *Limit* free-text area, it is possible to update the active limit amount of a risk portfolio rule. The change done in the *Limit* field is **effective immediately** and is only valid until day rollover: For the new limit to be extended permanently, it must be changed in the relevant `Risk Portfolio Rule`.

| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> | <b>Active Rules</b> | <b>Risk Portfolio PFE</b> | Ξ                   |                         |                       |                                |                          |             |                    | 土工 |
|------------------------------|-----------------------------|---------------------|---------------------------|---------------------|-------------------------|-----------------------|--------------------------------|--------------------------|-------------|--------------------|----|
|                              |                             |                     | →                         |                     |                         |                       |                                |                          |             | <b>Refresh All</b> |    |
| <b>Rule Id</b>               |                             | $\vee$ Counterpart  | Portfolio                 | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                      | Limit                    | Utilization |                    |    |
| Total Net Limit              |                             | <b>Any</b>          | Any                       | Any                 | Any                     | Any                   | Aggregate Net Settlement Limit | $3 000,000,000 $ 803,806 |             | <b>‴</b> ⊙∣�       |    |

<span id="page-17-1"></span>Figure 19 Updating the limit for current trading date

- **2) Visualize the breakdown of the utilization**: Clicking on icon opens a new window as a pop-up in which the user can see the cashflows that is accounted to calculate utilization.
- **3) Refresh the rule(s):** Clicking on icon next to an active rule brings the latest updated information to the display for the corresponding rule. Although 360T`s Limits Monitor has a real-time update of utilization calculations, it is required to refresh the view to be able to visualize the latest updates.

Please note that `Refresh All` button on top right of the rule dashboard triggers refresh for all active rules.

- **4) Jump to the Rule:** By clicking on the 'Jump to the Rule' button , admin users can navigate to the corresponding rule under the `Risk Portfolio Rules` panel. This makes it easier to locate the rule that admin user wants to review, edit, or deactivate.
- **5)** *Search within Active Rules*: The Active Rules tab has a search field where admin users can enter text to filter the rules they are looking for. This makes it easier for them to locate the active rule for which they are searching.

Limit

| $Q$ 360T            |                    | $\rightarrow$ |                     |                         |                       |                                  |            |                    | <b>Refresh All</b> |
|---------------------|--------------------|---------------|---------------------|-------------------------|-----------------------|----------------------------------|------------|--------------------|--------------------|
| Rule Id             | $\vee$ Counterpart | Portfolio     | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                        | Limit      | <b>Utilization</b> |                    |
| 360TBANK Settlement | 360TBANK.TEST      | Anv           | Any                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit       | 73,000,000 | $\mathbf{0}$       | $m \odot \phi$     |
| <b>360TBANK PFE</b> | 360TBANKTEST       | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure        | 67,500,000 | $\circ$            | 面のゆ                |
| 360T.MMC Settlement | 360T.MMC           | Anv           | Any                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit       | 51,000,000 | $\mathbf{0}$       | 而のめ                |
| 360T.MMC PFE        | 360T.MMC           | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure        | 45,500,000 | $\circ$            | 面のめ                |
| 360T Group Risk     | 360T Group Related | Any           | Any                 | Anv                     | TODAY-3 MONTHS        | Aggregate Gross Settlement Limit | 50,000,000 | $\circ$            | 而らめ                |

<span id="page-18-0"></span>Figure 20 Filtering the rules by using search function

- **6) Download EOD (end-of-day) rates as csv file**: By clicking on , user can download the EOD rates which are used as reference rates to convert the risk exposures into company`s home currency.
- **7) Download active rules as csv file:** By clicking on , user can download the active rules as csv file into their PC. The downloaded file will have below columns:

Three60tID;RuleId;Active;Counterpart;Portfolio;LegalEntity;Dealer;ExecutionMethod;TimePeriod;AlgorithmType;

**8) Bulk update active limits via csv upload:** Users can update the active limit amount of multiple rules by using csv upload functionality within Active Rules tab. After changing the limit in the downloaded csv file, user can upload the new rules

by clicking on icon and then selecting the file.

For a successful operation, users can provide all below columns. Although all these columns are required, system will only validate *Three60tID and Limit values.* This means, any changes in other values will be disregarded.

Three60tID;RuleId;Active;Counterpart;Portfolio;LegalEntity;ExecutionMethod;TimePeriod;AlgorithmType;Limit

After the file is uploaded, *Limits Monitor* will create a result file to provide feedback to the user.

| Field Name                                                                                                         | Type    | Possible Values                                                                                            | Description                                                                                                                                                                                                                              |
|--------------------------------------------------------------------------------------------------------------------|---------|------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Three60tID                                                                                                         | String  | Determined by 360T.<br>The<br>current<br>value<br>must be provided to<br>update<br>the<br>active<br>limit. | Indicates<br>the<br>unique<br>rule<br>ID<br>assigned<br>automatically by 360T.                                                                                                                                                           |
| RuleId,<br>Active, Counterpart,<br>Portfolio. LegalEntity, Dealer<br>ExecutionMethod,<br>TimePeriod, AlgorithmType | Any     | Can be any value.<br>System<br>will<br>ignore<br>these<br>fields<br>to<br>operate limit update.            | Please<br>see<br>….<br>For<br>detailed description of<br>the field. Since these<br>fields are only editable<br>via Risk Portfolio rule<br>configuration, provided<br>values in Active Rules<br>upload<br>will<br>not<br>be<br>validated. |
| Limit                                                                                                              | Decimal | Positive values up to<br>13 digits.                                                                        | Defines the maximum<br>amount that can be<br>utilized as per defined<br>parameters<br>and<br>algorithm.                                                                                                                                  |

<span id="page-19-3"></span>Table 2: Field definition for csv active limit upload

### <span id="page-19-0"></span>**5.1 Applying rules and limits to trades**

Once a deal is negotiated or order is placed, the system determines which rule(s) match(es) and carries out a credit check against the limit within that rule(s). If there are many rules that matches the trade, all of them will be checked and request/order will be allowed if and only if all of the checks passes.

#### <span id="page-19-1"></span>**5.1.1 Reallocating Trades After End of Day Rollover:**

Limits Monitor validates any changes made to Risk Portfolio Rules at New York 5 PM. It then rematches the request/orders with the rules again.

IMPORTANT: Please note as a result of change in risk portfolio rule or change in trades that match with a specific risk portfolio rule due to change in value dates, the utilization amount may exceed the active limit after end of day rollover. For example, if user extends the end of Fx Time Period of a rule from 1 Week to 2 Weeks, GTC orders with tenors between 1 to 2 weeks will also be utilized during the rollover phase and this may result in a higher utilization which may exceed the limit for that specific risk portfolio rule.

### <span id="page-19-2"></span>**5.2 Calculating Utilization**

Utilization shows the amount of the exposure calculated per active rule, based on the captured cashflow (trades) and the assigned algorithm. While filtering parameters (Counterpart, Product, Dealer and Legal Entity) determine which trades should be captured and checked, the algorithm assigned to the risk portfolio is the function which calculates the exposure based on the captured risk events (cashflow derived by a trade). For Trading Limit profile, only the gross notional amount for the current date trades are accounted where as for Per Deal Limit, no utilization calculation is done as the limit is set for each and every single request/orders.

For Swap and NDS transactions, the notional of swap is calculated and not each leg separately. In case of an uneven swap, the larger notional is taken into account.

For Block-Trades, net spot amount of all legs is taken into account.

Utilization is updated on the system whenever a trade is booked or an order is placed. In case of placing an order into an Order Book such as 360T SUN, utilization is only updated when there is a match and match is allowed only if the counterpart has been allocated for sufficient limit.

In order to see the most up-to-date utilization values on the UI, users should click the `Refresh All` button on top of the rule dashboard.

|                            |                    | $\rightarrow$ |                     |                         |                       |                                       |               |                                          | <b>Refresh All</b> |
|----------------------------|--------------------|---------------|---------------------|-------------------------|-----------------------|---------------------------------------|---------------|------------------------------------------|--------------------|
| $\mathbb Q$                |                    |               |                     |                         |                       |                                       |               |                                          |                    |
| Rule Id                    | $\vee$ Counterpart | Portfolio     | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                             | Limit         | <b>Utilization</b>                       |                    |
| <b>Total Net Limit</b>     | Any                | Any           | Any                 | Any                     | Any                   | <b>Aggregate Net Settlement Limit</b> | 2,000,000,000 | 876,867                                  | 而のめ                |
| <b>SEB Settlement</b>      | SEB.FRA.DEMO       | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 62,000,000    | 5,618                                    | 面のめ                |
| SEB PFE                    | SEB, FRA, DEMO     | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 56,500,000    | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 面のめ                |
| <b>RBS Settlement</b>      | RBS.LND.DEMO       | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 24,000,000    | 67,442                                   | 面のめ                |
| <b>RBS PFE</b>             | RBS.LND.DEMO       | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 2,000,000     | 36,000                                   | 面のめ                |
| <b>PEBANK Settlement</b>   | PEBANK_APAC.TEST   | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 40,000,000    | $\circ$                                  | 面のめ                |
| PEBANK PFE                 | PEBANK APACTEST    | Any vs G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 34,500,000    | $\circ$                                  | 而のめ                |
| <b>COBA Settlement</b>     | COBA.DEMO          | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 29.000.000    | $\circ$                                  | 面のゆ                |
| <b>COBA PFE</b>            | COBA.DEMO          | Any vs G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 23.500.000    | $\bullet$                                | 面のめ                |
| <b>360TBANK Settlement</b> | 360TBANK.TEST      | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 73,000,000    | $\circ$                                  | 面のめ                |
|                            | 360TBANK.TEST      | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 67.500.000    | $\circ$                                  | 面のめ                |
| <b>360TBANK PFE</b>        |                    |               |                     |                         |                       |                                       |               |                                          |                    |

<span id="page-20-0"></span>Figure 21 Monitoring the updated utilization amounts within Active Rules tab.

*Example:*

*Bank A* has defined some risk portfolio rules to limit its counterparty risk for *Counterparty A*, as well as to limit the trading activities of its own dealers.

Bank A has executed the below trades with Counterparty A.

| ID | Trade Date | Value<br>Date | Currency Pair | Currency1 | Currency2 | Action | Notional<br>Currency | Notional<br>Amount | Executed<br>Rate |
|----|------------|---------------|---------------|-----------|-----------|--------|----------------------|--------------------|------------------|
| 1  | 04.10.2019 | 04.10.2019    | USD/TRY       | USD       | TRY       | Sell   | USD                  | 1,000,000          | 5.6545           |
| 2  | 04.10.2019 | 04.10.2019    | EUR/USD       | EUR       | USD       | Buy    | EUR                  | 2,000,000          | 1.1105           |
| 3  | 03.10.2019 | 07.10.2019    | EUR/USD       | EUR       | USD       | Buy    | EUR                  | 2,000,000          | 1.1118           |
| 4  | 03.10.2019 | 07.10.2019    | CHF/EUR       | EUR       | CHF       | Sell   | EUR                  | 1,000,000          | 1.0911           |
| 5  | 04.10.2019 | 11.10.2019    | EUR/GBP       | EUR       | GBP       | Buy    | GBP                  | 2,000,000          | 0.8948           |
| 6  | 04.10.2019 | 11.10.2019    | EUR/USD       | EUR       | USD       | Sell   | USD                  | 3,000,000          | 1.1158           |
| 7  | 04.10.2019 | 11.10.2019    | GBP/TRY       | GBP       | TRY       | Sell   | GBP                  | 4,000,000          | 7.0258           |

Table 3: Sample trades

Below 360T EOD Rates are used as reference rate to convert different currency exposure into one single currency (in our example, it is USD).

| Currency Pair | EOD Rates |
|---------------|-----------|
| USD/TRY       | 5.6520    |
| EUR/USD       | 1.1050    |
| GBP/USD       | 1.2525    |
| USD/CHF       | 0.9995    |
| USD/USD       | 1         |

<span id="page-21-1"></span><span id="page-21-0"></span>Table 4: 360T EOD rates to convert Cashflow/Notional Ledgers into USD (credit currency).

### <span id="page-22-0"></span>**5.2.1 Daily Gross Trading Limit**

Daily Gross Trading Limit algorithm limits the total gross volume of trades done on a single trading day. Daily Gross Trading Limit sums the notional amount converted to company currency for all value dates across the credit horizon for current trading date.

**Important Note**: For Swap/NDS, Daily Gross Trading Algorithm only considers the larger amount of both of the legs. For Block-Trades, net amount the legs are considered for calculation.

For example; Bank A has set below Risk Portfolio Rule for Counterparty A.

| Counterpart | Product       | Dealer       | Legal<br>Entity<br>Group | Algorithm           | Limit      |
|-------------|---------------|--------------|--------------------------|---------------------|------------|
| Any         | Spot/Outright | BankA.Trader | Any                      | Daily Gross Trading | 50,000,000 |

*Under the assumption of current date = 04.10.2019 and all trades are Spot/Outright,* the rule would apply to 5 of the trades in Table 1 (#1,2, 5, 6 and 7) and the algorithm calculates the utilization amount as follows:

1,000,000 USD (from *Trade Id1)* + 2,210,000 USD (from *Trade ID2)* + 2,505,000 USD (from *Trade Id5)* + 3,000,000 USD (from *Trade Id6) +* 5,010,000 USD (from *Trade Id7) = 13,725,000 USD*

**Utilization:** *13,725,000 USD* 

#### <span id="page-22-1"></span>**5.2.2 Per Deal Limit**

Per Deal Limit only limits the notional of a deal that can be traded and therefore, there is no utilization calculation is relevant. The amount defined as limit does a limit check for each trade based and if notional of the deal > per deal limit, limit check fails.

For example, if Bank A defined 100m USD per deal limit for its traders, any trade with notional higher than 100 million USD equivalent will fail.

For a swap and NDS trades, only one leg (in case of uneven swap, the larger amount) is considered. For example, USDJPY Spot – 1 Week swap with 60 million USD amount passes the check for any per deal limit equal or higher than 60 million.

For Block Trades, net amount of all legs is considered.

**Important Note**: For Swap/NDS, Daily Gross Trading Algorithm only considers the largest amount of both of the legs. For Block-Trades, net amount the legs are considered for calculation.

# <span id="page-23-0"></span>**5.3 Utilization Reset**

360T`s Limits Monitor allows authorized users to reset the limit usage calculation for Daily Net and Gross Trading algorithms. Once a user is enabled for the feature, he/she will be able to click on and restart the calculation rather than waiting for day roll.

| Algorithm                 |             | <b>Utilization</b> |  |  |  |
|---------------------------|-------------|--------------------|--|--|--|
| Daily Gross Trading Limit | 100,000,000 | o                  |  |  |  |

<span id="page-23-3"></span>Figure 22 Reset Utilization

Once the functionality used, all admin users will receive an email which informs them about the action.

To be enabled for the functionality, please contact 360T CAS.

# <span id="page-23-1"></span>**5.4 Visualization**

#### <span id="page-23-2"></span>**5.4.1 Risk Entries**

Risk Entries tab provides the details of the cashflow (trade legs) matches with a specific risk portfolio rule. Information provided in this tab helps admin users to verify the utilization calculation as it includes necessary information.

Table can be exported as csv by clicking on icon.

|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        | $\times$ |
|-------------------|-------------------|-------------|-------------------------|------------------|-------------------------|------------------|-------------------------------|------------|---------------------|----------------------------|---------|--------|----------|
|                   |                   |             | Daily Limit Usage Table |                  | Daily Limit Usage Graph |                  | <b>Limit Used By Currency</b> |            | <b>Risk Entries</b> | 一支                         |         |        |          |
|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |
| <b>Value Date</b> | <b>Trade Date</b> | Product     | <b>Legal Entity</b>     | Counterpart      | Base CCY                | <b>Ouote CCY</b> | Notional CCY Receive CCY      |            | Pay CCY             | Notional Amount Trade Rate |         | Revert |          |
| 16.10.2020        | 15.10.2020        | <b>SPOT</b> | 360T.RMS                | RBS.LND.DEMO     | <b>USD</b>              | <b>TRY</b>       | <b>USD</b>                    | <b>TRY</b> | <b>USD</b>          | 100.000.00                 | 7.93256 | false  |          |
| 16 10 2020        | 15 10 2020        | <b>SPOT</b> | 360T.RMS                | PEBANK APAC.TEST | <b>USD</b>              | <b>TRY</b>       | <b>USD</b>                    | <b>TRY</b> | <b>USD</b>          | 123,000.00                 | 7.93667 | false  |          |
| 19.10.2020        | 15.10.2020        | <b>SPOT</b> | 360T.RMS                | PEBANK_APAC.TEST | <b>EUR</b>              | <b>TRY</b>       | <b>EUR</b>                    | <b>EUR</b> | <b>TRY</b>          | 58,100.00                  | 9.29767 | false  |          |
|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |
|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |
|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |

<span id="page-23-4"></span>Figure 23 Risk Entries

Below you can see the description of the information provided in the risk entries table:

- *i. Value Date:* Displays the effective/settlement date of the cashflow. Table is by default ranked ascending by *Value Date* parameter.
- *ii. Trade Date:* Displays the date that trade which causes the cashflow is executed.
- *iii. Product:* Indicates the product type. Please note that, it shows the type of the product that is originally executed i.e. Spot will be displayed even though value date doesn`t indicate a spot date for that specific currency pair any longer.
- *iv. Legal Entity:* Shows the name of the legal entity for which the credit entity made the transaction for.
- *v. Counterpart:* Displays 360T system name of the counterparty against which the credit entity dealt.

- *vi. Base CCY:* Displays the base currency for a specific trade/trade intention.
- *vii. Quote CCY:* Displays the quote currency for a specific trade/trade intention.
- *viii. Notional CCY:* Displays the currency which specify the amount to be exchanged via the corresponding trade/trade intention.
- *ix. Receive CCY:* Indicates the currency which credit entity will receive at value date.
- *x. Pay CCY:* Indicates the currency which credit entity agreed to pay at value date.
- *xi. Notional Amount:* Shows the notional amount of transaction.
- *xii. Trade Rate:* Shows the agreed execution rate or limit/stop rate. This information is used to calculate opposite amount of cashflow for net algorithms.
- **xiii.** *Revert:* Indicates whether the created risk entry is reverted (True) due to nonexecution or a final entry (false). In case of an order which is not fulfilled, Limits Monitor would allocate the exposed amount at the moment of placing or accepting order. This creates a risk entry in the system. However, when this order is withdrawn for ex., negation of the record would also be created by creating the same entry with `Revert = True`.

### <span id="page-24-0"></span>**5.5 Alert Emails**

360T`s Limits Monitor can notify the users who has access to the tool when the utilization of a limit for a certain rule has reached to a certain threshold value.

The automatically triggered email contains all the parameters of an active rule and aims to help client admins to notice the level of limit usage so that they can closely monitor the limits for risk portfolio rule and when necessary, make an action accordingly.

| Dear Test Admin,                                                                                                                                                                                                                                                  |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| The 75% threshold defined for 164715 has been reached as of 2021-06-28 16:35:32 GMT. In case, you need to take any action, please go to Risk Portfolio/Bridge Administration panel.                                                                               |
| Rule $ID = 164715$<br>Counterpart = MTBankA<br>$Portfolio = Any$<br>Legal Entity Group = $Any$<br>Execution Method = Any<br>$FX$ Time Period = TODAY - SPOT<br>Algorithm = Net Daily Settlement Limit<br>$Limit = 50,000,000,00$<br>Utilization = $38,359,709.00$ |
| Note: Please note that, limits and utilizations may have been updated with any further activity after this email is triggered.                                                                                                                                    |
| For any further questions, please contact 360T Client Advisory Services using the details below.                                                                                                                                                                  |
| Thank you.                                                                                                                                                                                                                                                        |
| Client Advisory Services<br>Global email: <EMAIL><br>Global fax: +49 69 900 289 59                                                                                                                                                                           |
| <b>EMEA</b><br>Business: +49 69 900 289 73                                                                                                                                                                                                                        |
| AMERICAS<br>Tel: ****** 776 2920                                                                                                                                                                                                                                  |
| APAC<br>Tel: +65 6325 9973                                                                                                                                                                                                                                        |
| Grüneburgweg 16-18 / Westend Carrée<br>60322 Frankfurt am Main<br>affiliate support on the state.                                                                                                                                                                 |

<span id="page-24-1"></span>Figure 24 Alert emails

By default, there are three levels of thresholds for 75, 85 and 95%, although it is possible to set different threshold values. In case you would like to start receiving the alert emails, please contact Client Advisory Services team at [<EMAIL>.](mailto:<EMAIL>)

# <span id="page-25-0"></span>**5.6 Snapshot Reports**

360T`s Limits Monitor can provide the snapshot of the Active Rules as a file in .csv format via the email. The reports are generated twice a day -end of day and beginning of day- and sent only to the admin users who are activated for the reports.

End of day reports are generated slightly before day rollover at NY 5 PM and displays the active risk portfolio rules as well as the corresponding limit and utilization values. Beginning of day reports are generated slightly after day rollover is completed after NY 5 PM.

The reports help clients to

- 1) Monitor if any existing active rule has not available limit mainly as a result of changes due to day rollover.
- 2) Monitor the limit usage across time by comparing snapshots of different dates.

Please contact our Client Advisory Services team [\(<EMAIL>\)](mailto:<EMAIL>) to start receiving the reports.

# <span id="page-26-0"></span>**6 LIMIT CHECK FLOW**

As discussed in previous sections, Limits Monitor is able to conduct limit check for all negotiation types for supported products. Due to difference in the flow of different negotiations, at which stage limit check and utilization update is conducted also change. This section will provide more details on the nuances of limit check:

### **RFS:**

Limits Monitor conducts limit check at two stages for RFS negotiation. First limit check is conducted when the negotiation is initiated. At this stage;

- If a limit assigned to a quote provider is not sufficient (i.e. in case of limit check failure) from requester`s point of view, that particular provider is taken out of the bank basket and requests won`t be delivered to them.
- From provider point of view, if limit against a requester is not sufficient, there can be two different consequence depending on the provider`s preference:
  - o Request can be routed to dealer intervention with a notification that shows the details of limit breach(es). This will give trader the option to price request manually despite the limit check failure. Please contact [<EMAIL>](mailto:<EMAIL>) for activation.

![](_page_27_Picture_2.jpeg)

<span id="page-27-0"></span>Figure 25 Limit Breach Details in TWS

o Provider is taken out of bank basket which means provider cannot receive the request (no option to override limit).

After the initial limit check, Limits Monitor conducts another check upon execution attempt initiated by the requester. This ensures that all recent updates during RFS negotiation. In case this last limit check fails, execution will not be allowed and rejected. If limit check passes, trade will be booked and utilization will be updated immediately.

#### **Order:**

For private book orders, Limits Monitor conducts limit check at order entry stage and reserve utilization immediately as soon as limit check passes and order is accepted by the providers. At this stage;

- If a limit assigned to an order provider is not sufficient (i.e. in case of limit check failure) from requester`s point of view, then order cannot be delivered to the provider and stays in Initialized stage.
- From provider point of view, if limit against a requester is not sufficient, there can be two different consequence depending on the provider`s preference:
  - o Order can be routed to dealer intervention with a notification that shows the details of limit breach(es). This will give trader the option to accept order manually despite the limit check failure. Please contact [<EMAIL>](mailto:<EMAIL>) for activation.

|                                                                                                                                         | O<br><b>• Order View Execution</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
|-----------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                         | Limit Order<br><b>O</b> GTC                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| is 120,200.<br>is 120,200.                                                                                                              | Manual Intervention required due to Limit breach:<br>. Rule 22175394-ER: Available limit -22,500,000 is insufficient for<br>25.11.2022 for counterpart 360T.RMS. Required free limit for the request<br>· Rule 97047993-ER: Available limit -10,000,000 is insufficient for<br>25.11.2022 for counterpart 360T.RMS. Required free limit for the request<br>. Rule 270504: Available limit 0 is insufficient for 25.11.2022 for<br>counterpart 360T.RMS. Required free limit for the request is 120,200. |
| <b>Order Details</b>                                                                                                                    | Order Changes                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| <b>Listed Product</b><br>Requester Action<br>Notional Amount<br>Limit Spot Rate<br><b>Effective Date</b><br>Market Rate<br>Counterparts | FX Spot<br>Client Buys RUB / Sells TRY<br>10,000,000.00 RUB<br>0.30880<br>Spot // Fri, 25, Nov 2022<br>0.30860<br>Request Changes                                                                                                                                                                                                                                                                                                                                                                       |
| Requester Company                                                                                                                       | 360T.RMS                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Requester Individual                                                                                                                    | 360TRMS.RiskManager                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| Expiry Date<br>Expiry Date - Local<br>Reference #                                                                                       | <b>GTC</b><br>PO-1800144-627                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|                                                                                                                                         | <b>Override Limit</b><br>Reject                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |

<span id="page-28-0"></span>Figure 26 Limit Breach Details in TWS for Orders

o Provider is taken out of bank basket which means provider cannot receive the order (no option to override limit).

#### **Streaming:**

In Streaming flow, limit check conducted continuously and any quote beyond the limit is filtered out/made unavailable for execution by Limits Monitor. Even though, liquidity is monitored and filtered accordingly, another limit check is conducted when order is placed against a quote and in case of failure, execution attempt is rejected.

### **Order Book (M|M, COB):**

For central order books, Limits Monitor conducts limit check on order book level to ensure that pending orders can only be aggressed by counterparties that is eligible as per either their own limits or their counterpart`s limits. In M|M, when there is no sufficient limit for a certain order in the book, traders can see the liquidity but it would be unavailable for them to trade against.

# <span id="page-30-0"></span>**7 AUDIT LOG**

360T`s Limits Monitor has a live audit log view which records every change done on the configurations by admin users.

Audit Log view can be accessed by clicking on icon next to the Risk Portfolio PFE tab.

The view has three columns:

- Log Date Time = Timestamp of the change (in GMT).
- User = The user who has performed the change.
- Description = The content of the change.

Similar to other tabs, Audit Log has a search function, by which users can filter the audit log entries and find the changes they want to see easily.

| $Q_1$                   |                     | $\rightarrow$                                                                                        |
|-------------------------|---------------------|------------------------------------------------------------------------------------------------------|
| <b>Log Date Time</b>    | <b>User</b>         | <b>Description</b>                                                                                   |
| May 20, 2021 6:31:48 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:100000000, Institution:360T.RMS), Active LimmoRule(Id:205870) |
| May 20, 2021 9:32:59 AM | ********            | Active Limmo rule limit updated (Limit:1000000, Institution:360T.RMS), Active LimmoRule(Id:205870)   |
| May 16, 2021 1:17:41 PM | 360TRMS.RiskManager | TimePeriodGroup updated (TimePeriodGroup:-1, TimePeriodGroupName:Any), LimmoRule(Id:205869)          |
| May 16, 2021 1:17:41 PM | 360TRMS.RiskManager | Time period updated (From:TODAY, To:TODAY), LimmoRule(Id:205869).                                    |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:217293)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205866)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205867)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205868)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205869)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205870)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205864)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205865)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205863)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205862)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205861)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205858)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205859)         |
| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205860)         |
| May 9, 2021 7:56:29 PM  | 360TRMS.RiskManager | Limit updated (Limit:50000000), LimmoRule(Id:217293)                                                 |
| May 9, 2021 7:56:20 PM  | 360TRMS.RiskManager | Algorithm updated (AlgorithmType:Aggregate Gross Settlement Limit), LimmoRule(Id:217293)             |

<span id="page-30-1"></span>Figure 27 Audit Log

# <span id="page-31-0"></span>**8 CONTACT 360T**

#### **Germany**

*360 Treasury Systems AG* Grüneburgweg 16-18 60322 Frankfurt am Main Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **Singapore**

*360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756

#### **Middle East**

#### **United Arab Emirates**

*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

#### **EMEA Americas**

#### **USA**

*360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902

#### **Asia Pacific South Asia**

### **India**

*ThreeSixty Trading Networks (India) Pvt Ltd* Level 8, Vibgyor Towers, G Block, C-62, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4090 7165 Fax: +91 22 4090 7070