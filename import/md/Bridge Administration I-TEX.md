# **I-TEX (BRIDGE ADMINISTRATION)**

![](_page_0_Picture_1.jpeg)

# **TEX MULTIDEALER TRADING SYSTEM**

USER GUIDE 360T BRIDGE ADMINISTRATION

I-TEX: INSTITUTION ADMINISTRATION

© 360 TREASURY SYSTEMS AG, 2020

THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG

| 2<br>GETTING STARTED<br><br>3<br>I-TEX ENTITY CREATION<br>4<br>I-TEX USER CREATION<br>5<br>PASSWORD AND PIN CREATION<br><br>5.1<br>CREATE PASSWORD<br>5.2<br>CREATE PIN<br><br>6<br>PASSWORD AND PIN ENTRY<br>7<br>I-TEX ENTITY REMOVAL<br>8<br>I-TEX USER REMOVAL<br><br>9<br>TECHNICAL INFORMATION<br>10<br>CONTACTING 360T | 1 | INTRODUCTION | 4        |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---|--------------|----------|
|                                                                                                                                                                                                                                                                                                                               |   |              | 4        |
|                                                                                                                                                                                                                                                                                                                               |   |              | 7        |
|                                                                                                                                                                                                                                                                                                                               |   |              | 7        |
|                                                                                                                                                                                                                                                                                                                               |   |              | 13       |
|                                                                                                                                                                                                                                                                                                                               |   |              | 13<br>14 |
|                                                                                                                                                                                                                                                                                                                               |   |              | 15       |
|                                                                                                                                                                                                                                                                                                                               |   |              | 17       |
|                                                                                                                                                                                                                                                                                                                               |   |              | 19       |
|                                                                                                                                                                                                                                                                                                                               |   |              | 20       |
|                                                                                                                                                                                                                                                                                                                               |   |              | 21       |

## **TABLE OF FIGURES**

| Figure 1<br>Header Bar4                                                 |  |
|-------------------------------------------------------------------------|--|
| Figure 2 Bridge Administration: Homepage<br>5                           |  |
| Figure 3 Bridge Administration: Institution tree<br>6                   |  |
| Figure 4<br>Bridge Administration: Expanded tree<br>7                   |  |
| Figure 5 Bridge Administration: Users tab<br>8                          |  |
| Figure 6 Bridge Administration: User Creation Wizard (1)<br>8           |  |
| Figure 7 Bridge Administration: User Creation Wizard (2)<br>9           |  |
| Figure 8 Bridge Administration: User Creation Wizard (3)<br>10          |  |
| Figure 9 Bridge Administration: User Creation Wizard (4)<br>10          |  |
| Figure 10 Bridge Administration: User Creation Wizard (6)<br>11         |  |
| Figure 11 Bridge Administration: User creation change request comment11 |  |
| Figure 12 Bridge Administration: Change Requests Action icon.<br>12     |  |
| Figure 13 Bridge Administration: Change Request Management.<br>12       |  |
| Figure 14 Bridge Administration: Users<br>tab<br>13                     |  |
| Figure 15 Institution: Password confirmation popup<br>14                |  |
| Figure 16 Institution: Password popup14                                 |  |
| Figure 17 Institution: PIN confirmation popup15                         |  |
| Figure 18 Institution: PIN popup<br>15                                  |  |
| Figure 19 Institution: Password entry16                                 |  |
| Figure 20 Institution: Change password popup16                          |  |
| Figure 21 Institution: Please add PIN popup17                           |  |
| Figure 22 Company Details: Institution Status<br>18                     |  |
| Figure 23 Company Details: Institution deactivation change request18    |  |
| Figure 24 List of users19                                               |  |
|                                                                         |  |

## <span id="page-3-0"></span>**1 INTRODUCTION**

This user manual describes the handling of I-TEX entities, the I-TEX user creation and removal process as well as the password and PIN functionality, captured within the 360T Bridge Administration – Institution feature.

Only users with corresponding administrative rights are able to access the required I-TEX features. These rights can be separately assigned (remove I-TEX entities; create and delete I-TEX users; administrate I-TEX Password, administrate I-TEX PIN or both). <NAME_EMAIL> or your customer relationship manager for setting up the relevant administrative rights.

## <span id="page-3-1"></span>**2 GETTING STARTED**

To access the I-TEX administration functionalities, open the 360T Bridge Administration – Institution feature.

The Institution feature is located within the Bridge Administration tool. Bridge Administration can be accessed either via menu option "Administration" from the screen header of the Bridge application, or as a standalone feature from your starter applet.

|                                                                  |                                                          |                                                    | $\vee$ Preferences<br>■ Administration                        | $\Box$ $\times$<br>$\vee$ Help<br>$AA -$ |
|------------------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------|---------------------------------------------------------------|------------------------------------------|
| > Bridge Administration                                          |                                                          |                                                    |                                                               | ×                                        |
|                                                                  |                                                          |                                                    |                                                               |                                          |
| <b>JOH LUIV</b><br><b>DOA FAM</b>                                | <b>DEIL ADI</b><br>ana nni                               | <b>Jeil Luiv</b><br><b>DOA FAIL</b>                | JEIL AND<br><b>DOA APR</b>                                    |                                          |
| 1.17890<br>117 <sub>O</sub><br><b>ブノ</b> の<br>Spot // 21.11.2017 | 13237<br>$1.32$ <sup>3</sup><br>36<br>Spot // 21.11.2017 | $0.89 - 1$<br>0.89084<br>- 4<br>Spot // 21.11.2017 | 0.99184<br>$0.99 - 1$<br>9 <sub>3</sub><br>Spot // 21.11.2017 |                                          |

<span id="page-3-2"></span>Figure 1 Header Bar

Upon opening Bridge Administration, shortcuts to different categories of configuration tools and actions for the particular user will be on display.

A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.

![](_page_4_Picture_2.jpeg)

Figure 2 Bridge Administration: Homepage

<span id="page-4-0"></span>Upon clicking on the Institution quick links, the navigation panel which showcases various icons and an institution tree will become visible. The Institution tree includes the full list of users and entities configured under the main entity. Users belonging to the Main entity will be displayed by default.

|                                                                                                                                                                                                                                                                                                                                                                                             | $\vee$ Preferences           | $\vee$ Administration                    | $\vee$ Help                                          | $\bullet$ | $AA - CD \times$ |             |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|------------------------------------------|------------------------------------------------------|-----------|------------------|-------------|
| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                                                                             | <b>BRIDGE ADMINISTRATION</b> | $+$                                      |                                                      |           |                  |             |
|                                                                                                                                                                                                                                                                                                                                                                                             |                              |                                          |                                                      |           |                  |             |
| 亘<br>Q<br>$\overline{\left\langle \right\rangle }$<br>合<br>$\underline{\widehat{\mathbf{m}}}$ GroupE<br>$\mathcal{L}$<br>$\vee$ $\frac{1}{111}$ SubsidiaryE<br>CroupE.AutoDealer<br>CroupE.TraderA<br>工<br>CroupE.TraderB<br>$\overline{\text{un}}$<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB<br>CroupE.TreasurerC<br>☆<br>$\mathbb C$<br>$\bigcirc$<br>$\leftrightarrow$ |                              | No Individuals/Institutions are selected |                                                      |           |                  |             |
| C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                                                                                                         | <b>EECT</b>                  |                                          | Tue, 03. Nov 2020, 18:46:50 GMT // Connected [FFM] · |           |                  | <b>BETA</b> |

<span id="page-5-0"></span>![](_page_5_Figure_3.jpeg)

The full content of the Institution tree wil be visible by clicking at the expansion icon .

|                                                                                                                                                                                                                                                                                                                                                                       | $\vee$ Preferences                     | $\vee$ Administration                    | $\vee$ Help                                          | $\bullet$ AA - $\bullet$ X |             |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|------------------------------------------|------------------------------------------------------|----------------------------|-------------|
| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                                                       | $^{+}$<br><b>BRIDGE ADMINISTRATION</b> |                                          |                                                      |                            |             |
| Q ※<br>上血                                                                                                                                                                                                                                                                                                                                                             |                                        |                                          |                                                      |                            |             |
| $\langle$<br>合<br>$\wedge \hat{\mathbf{\underline{m}}}$ GroupE<br>÷<br>$\mathcal{G}$<br>I ∧ <u>Im</u> SubsidiaryE<br>u Li In<br>SubsidiaryE.TreasurerA  <br>1<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC  <br>WILL<br>CroupE.AutoDealer<br>CroupE.TraderA<br>CroupE.TraderB<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB<br>CroupE.TreasurerC |                                        | No Individuals/Institutions are selected |                                                      |                            |             |
| ☆<br>$\cup$<br>$\bigcirc$                                                                                                                                                                                                                                                                                                                                             |                                        |                                          |                                                      |                            |             |
| $\ominus$                                                                                                                                                                                                                                                                                                                                                             |                                        |                                          |                                                      |                            |             |
| C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                                                                                   | <b>EECT</b>                            |                                          | Tue, 03. Nov 2020, 18:48:30 GMT // Connected [FFM] · |                            | <b>BETA</b> |

<span id="page-6-2"></span>Figure 4 Bridge Administration: Expanded tree

## <span id="page-6-0"></span>**3 I-TEX ENTITY CREATION**

I-TEX entity creation is not currently supported by 360T Bridge Administration – Institution. In case you would like to create a new I-TEX entity, please contact the 360T CAS team via the contact details provided under chapter [10](#page-20-0) Contacting 360T.

## <span id="page-6-1"></span>**4 I-TEX USER CREATION**

Creation of Change Request and approval by a Supervisor (different user) are mandatory steps for ITEX user creation.

### **Administrator:**

A new user creation change request process is intitiated by the Administrator. The corresponding I-TEX entity must be first selected from the Institution tree. This opens a Company Details tab containing the available details of the entity. The "Create Internal User" button is available from the Users tab.

|                             |                                                         |                              |                                              | $\vee$ Preferences     | $\vee$ Administration | $\vee$ Help                 | $A$ $A$ - $B$ $X$<br>$\odot$                                                                        |             |
|-----------------------------|---------------------------------------------------------|------------------------------|----------------------------------------------|------------------------|-----------------------|-----------------------------|-----------------------------------------------------------------------------------------------------|-------------|
|                             | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>         |                              | $+$<br><b>BRIDGE ADMINISTRATION</b>          |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
| 合                           | Q ※<br>上 血<br>$\langle$                                 | <b>Company Details</b>       | <b>Deal Tracking Groups (1)</b><br>Users (3) |                        |                       |                             | $\textcolor{black}{\curvearrowleft} \hspace{0.1cm} \mathbb{R} \hspace{0.1cm} \equiv \hspace{0.1cm}$ |             |
|                             | へ 血 GroupE                                              | 自立也                          |                                              |                        |                       |                             |                                                                                                     |             |
| $\mathcal{L}_{\mathcal{F}}$ | $\land \overline{\underline{\mathfrak{m}}}$ SubsidiaryE |                              | Users (3)                                    |                        |                       | <b>Status</b>               |                                                                                                     |             |
|                             | SubsidiaryE.TreasurerA                                  |                              | SubsidiaryE.TreasurerA                       | <b>Create Password</b> | <b>Create PIN</b>     | $\bullet$<br>Active         | 一面                                                                                                  |             |
| $\overline{\mathbb{F}_Q}$   | SubsidiaryE.TreasurerB                                  |                              | SubsidiaryE.TreasurerB                       | <b>Create Password</b> | <b>Create PIN</b>     | $\bullet$<br>Active         | û                                                                                                   |             |
| villy                       | SubsidiaryE.TreasurerC                                  |                              | SubsidiaryE.TreasurerC                       | <b>Create Password</b> | <b>Create PIN</b>     | <b>1 1 1</b><br>Active      |                                                                                                     |             |
|                             | CroupE.AutoDealer                                       |                              |                                              |                        |                       | <b>Create Internal User</b> |                                                                                                     |             |
|                             | CroupE.TraderA                                          |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             | CroupE.TraderB                                          |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             | CroupE.TraderC                                          |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             | CroupE.TreasurerA                                       |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             | GroupE.TreasurerB                                       |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             | CroupE.TreasurerC                                       |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
| ☆                           |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
| D                           |                                                         | <b>Create Change Request</b> |                                              |                        |                       |                             | Discard All Changes<br>Save                                                                         |             |
| $\bigcirc$                  |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |
| $\ominus$                   |                                                         | SubsidiaryE $\times$         |                                              |                        |                       |                             |                                                                                                     |             |
|                             | C GroupE.TreasurerC, GroupE // BETA                     |                              | FEET                                         |                        |                       |                             | Tue, 03. Nov 2020, 18:05:45 GMT // Connected [FFM] ·                                                | <b>BETA</b> |

<span id="page-7-0"></span>Figure 5 Bridge Administration: Users tab

A Help Wizard with the following mandatory fields will be populated initially:

- a. Login Name: User's name or name synonym
- b. Postion: Front or Back Office user

Please note: The email address and the phone number, which must both reference the main entity and without disclosing any personal details of the ITEX user, are predefined by the 360T CAS team when the I-TEX entity is initally set up.

| $\left.\right\rangle$ 4 $\left.\right\rangle$ 5 $\left.\right\rangle$<br>3 <sup>1</sup> | $\checkmark$             | Create an Individual                                                  |              |                |
|-----------------------------------------------------------------------------------------|--------------------------|-----------------------------------------------------------------------|--------------|----------------|
|                                                                                         |                          | Please fill in the Individual details                                 |              |                |
|                                                                                         | Login Name*              | SubsidiaryE<br>$\checkmark$<br>Name does not match the correct format |              |                |
|                                                                                         | Description              |                                                                       |              |                |
|                                                                                         | Email *                  | <EMAIL>                                         | $\checkmark$ |                |
|                                                                                         | Phone Number *           | +49 123 456 789                                                       | $\checkmark$ |                |
|                                                                                         | Position *               | <b>O</b> Front Office                                                 |              |                |
|                                                                                         |                          | <b>Back Office</b>                                                    |              |                |
|                                                                                         | Expose data to providers | Disabled<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                  |              |                |
|                                                                                         |                          |                                                                       | * Mandatory  |                |
| Previous                                                                                |                          |                                                                       |              | Next<br>Cancel |

<span id="page-7-1"></span>Figure 6 Bridge Administration: User Creation Wizard (1)

The second wizard tab (2) will appear after clicking on the icon: . The field "Trading Type" allows the specification of one of three available trading permissions:

- Plain TEX User: should be selected when user trades for the I-TEX entity-only
- Trade-as User: should be selected when requests are sent by the user in the name of related entities via 360T's Trade-As functionality
- Trade-on-behalf User: should be selected when requests are sent by the user in the name of related entities via 360T's Trade-on-Behalf functionality

| Trading Type  | Trade-as User        |  |
|---------------|----------------------|--|
|               | Trade-as User        |  |
| Available     | Trade-on-behalf User |  |
| TradeAsE TAS  | Plain TEX User       |  |
| TradeAsE.TAS2 |                      |  |
|               | ĸ                    |  |
|               | ---                  |  |
|               | $\gg$                |  |
|               | $\propto$            |  |
|               |                      |  |

<span id="page-8-0"></span>Figure 7 Bridge Administration: User Creation Wizard (2)

Please note: The selection of the Trading Type depends on the particular set-up, however "Plain TEX User" is the default option. In case trade-as or trade-on-behalf rights need to be assigned, the legal entities can be assigned immediately to the user, by transferring a Legal Entity Group from Available to Selected as seen above.

In Step No. 3), the user can be added as a Member of a Deal Tracking group by transferring the group from Available to Selected.

Please note: Viewers of a Deal Tracking group (see tab (4)) can see the Members' executed trades. Back Office users do not need to be added as "Members" to a Deal Tracking group as they do not trade.

| $\left(\begin{array}{c}1\end{array}\right)$ $\left(\begin{array}{c}2\end{array}\right)$ $\left(\begin{array}{c}3\end{array}\right)$ $\left(\begin{array}{c}4\end{array}\right)$ $\left(\begin{array}{c}5\end{array}\right)$ $\sqrt{2}$ |                              |                                                   | Create an Individual                                                                                                                                                                                                                                                                                                                                         |          |                       |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|---------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|-----------------------|
|                                                                                                                                                                                                                                        |                              | Configure Deal Tracking membership and viewership |                                                                                                                                                                                                                                                                                                                                                              |          |                       |
|                                                                                                                                                                                                                                        | <b>Deal Tracking Members</b> |                                                   |                                                                                                                                                                                                                                                                                                                                                              |          |                       |
|                                                                                                                                                                                                                                        | SubsidiaryE                  | Available                                         | $\gg$<br>$\begin{picture}(20,20) \put(0,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1,0){15}} \put(15,0){\line(1$ | Selected |                       |
| Previous                                                                                                                                                                                                                               |                              |                                                   |                                                                                                                                                                                                                                                                                                                                                              |          | <b>Next</b><br>Cancel |

<span id="page-9-0"></span>Figure 8 Bridge Administration: User Creation Wizard (3)

User Guide Bridge Administration: I-TEX Insitution Administration

In the next tab (4), the user can be added as a Viewer of a Deal Tracking group by transferring the group from Available to Selected.

| $4 \rightarrow 5$<br>$\checkmark$ |           | Create an Individual       |                                                   |                       |
|-----------------------------------|-----------|----------------------------|---------------------------------------------------|-----------------------|
|                                   |           |                            | Configure Deal Tracking membership and viewership |                       |
| <b>Deal Tracking Viewers</b>      |           |                            |                                                   |                       |
| SubsidiaryE                       | Available | $\gg$<br>$\langle \langle$ | Selected                                          |                       |
| Previous                          |           |                            |                                                   | <b>Next</b><br>Cancel |

<span id="page-9-1"></span>Figure 9 Bridge Administration: User Creation Wizard (4)

Please note: Viewers of a Deal Tracking group have view-only access to the executed trades of Members of the same Deal Tracking Group.

The last tab (5) of the Wizard presents an overview of all pending changes. The Administrator has the possibility to review and modify if necessary by reverting to previous tabs.

|          |                             | Create an Individual                 |                  |
|----------|-----------------------------|--------------------------------------|------------------|
|          |                             | Overview: Please review your changes |                  |
|          | Individual Name             | SubsidiaryE.TreasurerD               |                  |
|          | Description                 |                                      |                  |
|          | Email                       | <EMAIL>        |                  |
|          | Phone Number                | +49 123 456 789                      |                  |
|          | Fax Number                  |                                      |                  |
|          | Position                    | Treasurer                            |                  |
|          |                             |                                      |                  |
|          | Trading Type                | Plain TEX User                       |                  |
|          | TAS/TOB Groups              |                                      |                  |
|          | Deal Tracking Member        | SubsidiaryE                          |                  |
|          | <b>Deal Tracking Viewer</b> | SubsidiaryE                          |                  |
|          |                             |                                      |                  |
|          |                             |                                      |                  |
| Previous |                             |                                      | Create<br>Cancel |

<span id="page-10-0"></span>Figure 10 Bridge Administration: User Creation Wizard (6)

Upon clicking at "Create" entries will be saved and the Change Request process is now initiated, with the requirement to provide a comment. This comment will be available to the Supervisor while the change request is being approved.

| Your text here |  |  |
|----------------|--|--|
|                |  |  |
|                |  |  |
|                |  |  |

<span id="page-10-1"></span>Figure 11 Bridge Administration: User creation change request comment.

#### **Supervisor (Change Request Approval):**

The user with Supervisor permissions is able to approve the user creation change request submitted by the Administrator.

The Change Request can be accessed via the Bridge Administration Homepage.

| Configurations        | <b>Administration Start</b> |                        |
|-----------------------|-----------------------------|------------------------|
| Institution           | <b>Bank Baskets</b>         |                        |
| Actions               |                             |                        |
| <b>Change Request</b> | Wizards                     | <b>Evaluator Tools</b> |

<span id="page-11-0"></span>Figure 12 Bridge Administration: Change Requests Action icon.

By turning on the icon populates all pending Change Requests, which can be individually reviewed, approved or rejected using the action icons:

|                                                      |                              |                              | Change Requests (1/1) |                   |               | $\bullet$                                                   | Show all          |
|------------------------------------------------------|------------------------------|------------------------------|-----------------------|-------------------|---------------|-------------------------------------------------------------|-------------------|
| Select Category<br>Select Company<br>Filter by CR id | $\checkmark$<br>$\checkmark$ | 000.CRM.STRESS.TEST $\times$ |                       |                   |               |                                                             | $\trianglelefteq$ |
| Id                                                   | Institution                  | <b>Comment</b>               | Submitted at          | Submitted by      | <b>Status</b> |                                                             |                   |
| CR-2681                                              | 000.CRM.STRESS.TEST          | test                         | Oct 30, 2020 4:10:2   | CRMST.SuperAdmin1 | $\bullet$ 0/1 | $\circledcirc \mathbb{E} \vee \times \triangleright \equiv$ |                   |
|                                                      |                              |                              |                       |                   |               |                                                             |                   |
|                                                      |                              |                              |                       |                   |               |                                                             |                   |
|                                                      |                              |                              |                       |                   |               |                                                             |                   |
|                                                      |                              |                              |                       |                   |               |                                                             |                   |
|                                                      |                              |                              |                       |                   |               |                                                             |                   |

<span id="page-11-1"></span>Figure 13 Bridge Administration: Change Request Management.

## <span id="page-12-0"></span>**5 PASSWORD AND PIN CREATION**

The Password and PIN feature is available in the "Users" tab of the selected entity from the Insitution tree:

|           |                           |                                                                                                                          |                       |                                     |                          | $\vee$ Preferences     | $\vee$ Administration | $\vee$ Help             |                                                      |                         | $\mathbb{D}^1$ O AA - $\mathbb{D}$ X |             |
|-----------|---------------------------|--------------------------------------------------------------------------------------------------------------------------|-----------------------|-------------------------------------|--------------------------|------------------------|-----------------------|-------------------------|------------------------------------------------------|-------------------------|--------------------------------------|-------------|
|           |                           | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                          |                       | <b>BRIDGE ADMINISTRATION</b>        | $^{+}$                   |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           | 合                         | Q 张   1 童<br>$\langle$                                                                                                   | Company Details       | Users (3)<br>ı                      | Deal Tracking Groups (1) |                        |                       |                         |                                                      |                         | の 心 重                                |             |
|           | $\mathcal{L}$             | $\land \overline{\hat{\underline{\bf m}}}$ GroupE<br>$\land \overline{\text{m}}$ SubsidiaryE<br>& SubsidiaryE.TreasurerA | 自立出                   | Users (3)<br>SubsidiaryE.TreasurerA |                          | <b>Create Password</b> | <b>Create PIN</b>     | <b>Status</b><br>Active | $\bullet$                                            | 一面                      |                                      |             |
|           |                           | & SubsidiaryE.TreasurerB                                                                                                 |                       | SubsidiaryE.TreasurerB              |                          | <b>Create Password</b> | <b>Create PIN</b>     | Active                  | $\sigma$                                             | ŵ                       |                                      |             |
|           | $\mathbb{F}_{\mathbb{Q}}$ | SubsidiaryE.TreasurerC                                                                                                   |                       | SubsidiaryE.TreasurerC              |                          | <b>Create Password</b> | <b>Create PIN</b>     | Active                  | $\bullet$                                            | $\widehat{\mathbb{m}}$  |                                      |             |
|           | $\overline{\text{unif}}$  | CroupE.AutoDealer                                                                                                        |                       |                                     |                          |                        |                       |                         | <b>Create Internal User</b>                          |                         |                                      |             |
|           |                           | CroupE.TraderA                                                                                                           |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           | CroupE.TraderB                                                                                                           |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           | CroupE.TraderC                                                                                                           |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           | CroupE.TreasurerA                                                                                                        |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           | CroupE.TreasurerB                                                                                                        |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           | CroupE.TreasurerC                                                                                                        |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
| ₿         |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
| Ď         |                           |                                                                                                                          | Create Change Request |                                     |                          |                        |                       |                         |                                                      | Discard All Changes ) ( | Save                                 |             |
| $\circ$   |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
| $\ominus$ |                           |                                                                                                                          | SubsidiaryE $\times$  |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |
|           |                           | C GroupE.TreasurerC, GroupE // BETA                                                                                      |                       |                                     | <b>EEUT</b>              |                        |                       |                         | Tue, 03. Nov 2020, 18:05:45 GMT // Connected [FFM] . |                         |                                      | <b>BETA</b> |

<span id="page-12-2"></span>Figure 14 Bridge Administration: Users tab

User may require a password, PIN, or both, depending on several scenarios:

- A new 360T user would like to login for the first time: both Password and PIN are required
- An existing user has forgotten their passowrd: Password only
- The JKS certificate of a user has expired: PIN only
- An existing user has a new PC: PIN only

## <span id="page-12-1"></span>**5.1 Create Password**

The "Create Password" button is available next to the individual user name. When selected, a window will pop up requesting the confirmation of the password creation action:

| Please confirm:                                          |              |
|----------------------------------------------------------|--------------|
| Create / Reset password for user SubsidiaryE.TreasurerA. |              |
|                                                          | Cancel<br>OK |

<span id="page-13-1"></span>Figure 15 Institution: Password confirmation popup

After clicking "OK", a second pop-up window will appear with the user's new password.

| SubsidiaryE.TreasurerA Password |    |
|---------------------------------|----|
| J7Yw8kdomO                      |    |
|                                 |    |
|                                 |    |
|                                 | OK |

<span id="page-13-2"></span>Figure 16 Institution: Password popup

The created text must be copied and sent to the user e.g. via email. Please click at "OK" after the text has been copied to close the window.

Please note: Creating a new password will reset the user's existing password.

### <span id="page-13-0"></span>**5.2 Create PIN**

Clicking on "Create PIN" button adjacent to the affected user triggers the pop-up of PIN creation window:

| Please confirm:                                     |
|-----------------------------------------------------|
| Create / Reset PIN for user SubsidiaryE.TreasurerA. |
|                                                     |
|                                                     |
| Cancel<br>OK                                        |

<span id="page-14-1"></span>Figure 17 Institution: PIN confirmation popup

After clicking "OK", a second pop-up window will appear with the user's new PIN.

| SubsidiaryE.TreasurerA PIN |        |    |
|----------------------------|--------|----|
|                            | 553711 |    |
|                            |        |    |
|                            |        |    |
|                            |        | OK |

<span id="page-14-2"></span>Figure 18 Institution: PIN popup

The created text must be copied and sent to the user e.g. via email. Please click at "OK" once the text has been copied to close the window.

Please note: Creating a new PIN will reset the user's existing security certificate. The new PIN needs to be entered upon the user's next login.

## <span id="page-14-0"></span>**6 PASSWORD AND PIN ENTRY**

Users may be prompted to enter a password, PIN, or both, if these have been reset, or created for the first time.

A first/reset password should be entered in the starter applet:

|             | <b>DEUTSCHE BÖRSE</b><br><b>EEMT</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                |
|-------------|----------------------------------------------------------------------------------|----------------|
| <b>BETA</b> |                                                                                  | Version: 4.4.1 |
|             | SubsidiaryE.TreasurerA<br>$\checkmark$<br>Password                               |                |
|             | Login                                                                            |                |
| Help        | <b>Change Password</b>                                                           |                |

<span id="page-15-0"></span>Figure 19 Institution: Password entry

A user will be prompted to set a new password in the Change Password pop-up window.

| <b>Change Password</b>                                                           |                                                |  |
|----------------------------------------------------------------------------------|------------------------------------------------|--|
|                                                                                  |                                                |  |
| New Password                                                                     |                                                |  |
| Repeat Password                                                                  |                                                |  |
| Minimum numeric characters<br>Minimum password length<br>Maximum password length | คิ<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |  |
| Cancel<br>OK                                                                     |                                                |  |

<span id="page-15-1"></span>Figure 20 Institution: Change password popup

A user will be prompted to add their PIN in a separate pop-up window.

![](_page_16_Picture_2.jpeg)

Figure 21 Institution: Please add PIN popup

<span id="page-16-1"></span>After the Password, PIN, or both have been correctly inserted the user will be logged in to the 360T platform.

## <span id="page-16-0"></span>**7 I-TEX ENTITY REMOVAL**

Removing an I-TEX entity requires a Change Request to be created by an Administrator, and thus approved by a Supervisor (different user).

Please note: An I-TEX entity can only be removed if all the related users are either in a deactivated or a deleted status.

Administrator can submit a change request to remove an I-TEX entity by changing the status of the entity from "Institution active" to "Institution inactive" in the "Company Details" of the ITEX entity.

#### User Guide Bridge Administration: I-TEX Insitution Administration

|                                                                                                                                                                                                                                                                                                                                                                                                                          | $\vee$ Preferences                                                                                                                                                                                                                                                                                                                                                 | $AA - CD X$<br>$\odot$<br>$\vee$ Administration<br>ぺ<br>$\vee$ Help                                                                                                                                                |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                                                                                                          | <b>BRIDGE ADMINISTRATION</b><br>$^{+}$                                                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                    |
| 撥<br>$\alpha$<br>上 血<br>$\langle$<br>合<br>$\wedge \overline{m}$ GroupE<br>$\mathcal{L}$<br>$\gamma$ $\hat{m}$ SubsidiaryE $\blacksquare$<br>SubsidiaryE.TreasurerA<br><b>T</b> O<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC<br>$\sqrt{117}$<br>SubsidiaryE.TreasurerD<br>CroupE.AutoDealer<br>CroupE.TraderA<br>CroupE.TraderB<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB<br>CroupE.TreasurerC | <b>Company Details</b><br>Users (4)<br><b>Deal Tracking Groups (1)</b><br><b>Overview</b><br><b>Prefixes</b><br>Internal trades only<br>Company Name *<br>Description<br>Phone Number<br>Fax Number<br>Country *<br>US Person *<br><b>EUR</b><br>Currency<br>LEI<br>Status *<br>Provider Role<br>Requestor Role<br>$\vee$<br>Prime Broker<br>High Frequency Trader | $\Omega \quad \Omega \quad \equiv$<br>SubsidiaryE<br>Germany<br>$\checkmark$<br>O 0 Disabled<br>$\checkmark$<br><b>Institution active</b><br>$\checkmark$<br>O 0 Disabled<br>Enabled<br>○ Disabled<br>O 0 Disabled |
| ⇔□○<br>$\bigoplus$<br>C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                                                                                                                | Current ITEX Group Treasury<br>Create Change Request<br>SubsidiaryE $\times$<br><b>Geor</b>                                                                                                                                                                                                                                                                        | Discard All Changes ) (<br>Save<br>Tue, 03. Nov 2020, 19:03:07 GMT // Connected [FFM] ·<br><b>BETA</b>                                                                                                             |

<span id="page-17-0"></span>Figure 22 Company Details: Institution Status

Once the Status is updated, the Administrator can submit the deactivation request by clicking at the "Created Change Request" icon:

|                                                                                                                    |                                                           | $\vee$ Preferences                                                     | $\nabla^e$<br>$\odot$<br>$AA - CD \times$<br>$\vee$ Administration<br>$\vee$ Help |
|--------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------------------------------------------------|
|                                                                                                                    | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>           | $+$<br><b>BRIDGE ADMINISTRATION</b>                                    |                                                                                   |
|                                                                                                                    |                                                           |                                                                        |                                                                                   |
| 侖                                                                                                                  | Q<br>幾<br>上 血<br>$\overline{\left\langle \right\rangle }$ | <b>Company Details</b><br>Users (4)<br><b>Deal Tracking Groups (1)</b> | の № 亖                                                                             |
| $\mathcal{L}_{\mathcal{F}}$                                                                                        | $\wedge \hat{\mathbb{m}}$ GroupE                          | <b>Overview</b><br><b>Prefixes</b><br>Internal trades only             |                                                                                   |
|                                                                                                                    | ∧ 血 SubsidiaryE<br>SubsidiaryE.TreasurerA                 |                                                                        |                                                                                   |
| <b>武</b>                                                                                                           | SubsidiaryE.TreasurerB                                    | Company Name*                                                          | SubsidiaryE                                                                       |
|                                                                                                                    | SubsidiaryE.TreasurerC                                    | Description                                                            |                                                                                   |
| viiy                                                                                                               | SubsidiaryE.TreasurerD                                    | Phone Number                                                           |                                                                                   |
|                                                                                                                    | CroupE.AutoDealer                                         | Fax Number                                                             |                                                                                   |
|                                                                                                                    | CroupE.TraderA                                            | Country *                                                              | Germany                                                                           |
|                                                                                                                    | CroupE.TraderB                                            | US Person *                                                            | O 0 Disabled                                                                      |
|                                                                                                                    | CroupE.TraderC                                            | Currency                                                               | <b>EUR</b><br>$\checkmark$                                                        |
|                                                                                                                    | CroupE.TreasurerA                                         |                                                                        |                                                                                   |
|                                                                                                                    | CroupE.TreasurerB<br>CroupE.TreasurerC                    | LEI                                                                    |                                                                                   |
|                                                                                                                    |                                                           | Status *                                                               | Institution inact                                                                 |
|                                                                                                                    |                                                           | Provider Role                                                          | ○ 0 Disabled                                                                      |
|                                                                                                                    |                                                           | Requestor Role                                                         | <b>VO</b> Enabled                                                                 |
|                                                                                                                    |                                                           | Prime Broker                                                           | O 0 Disabled                                                                      |
|                                                                                                                    |                                                           | High Frequency Trader                                                  | O 0 Disabled                                                                      |
|                                                                                                                    |                                                           |                                                                        |                                                                                   |
| ☆                                                                                                                  |                                                           |                                                                        |                                                                                   |
| D                                                                                                                  |                                                           | <b>Create Change Request</b>                                           | <b>Discard All Changes</b><br>Save                                                |
| $\circ$                                                                                                            |                                                           |                                                                        |                                                                                   |
| $\ominus$                                                                                                          |                                                           | SubsidiaryE * X                                                        |                                                                                   |
| C GroupE.TreasurerC, GroupE // BETA<br>Fech<br>Tue, 03. Nov 2020, 19:04:50 GMT // Connected [FFM] ●<br><b>BETA</b> |                                                           |                                                                        |                                                                                   |

<span id="page-17-1"></span>Figure 23 Company Details: Institution deactivation change request.

By selecting "Create Change Request", changes will be saved and the Change Request process is initiated, a comment is now required to be added. This comment will be visible to the Supervisor upon approving the change request.

#### **Supervisor**

Similar to the approval of the Change Request for user creation (see Chapter 4), approval of the Change Request to remove the I-TEX entity is accessed via the Bridge Administration Homepage, from the Change Request window.

Please note: By removing the I-TEX entity will also remove the 360T Counterparty Relationship Management (CRM) tool relationship between the TEX and I-TEX entity.

## <span id="page-18-0"></span>**8 I-TEX USER REMOVAL**

Removing an I-TEX user requires a Change Request to be created by an Administrator, and thus approved by a Supervisor (different user).

#### **Administrator:**

The process of the user removal change request is intitiated by the Administrator. The corresponding I-TEX entity must be first selected from the Institution tree, which leads to the opening of the Company Details tab containing the available details of the entity. The full list of Users with their status is shown in the "Users" tab.

|                                       |                                                                                                                                                                                                                                                                                                                   | $\vee$ Preferences                                                                                                           | $\vee$ Administration                                                                                | $\vee$ Help                                                                                                                               | D <sub>e</sub><br>$\odot$<br>$AA - CD X$                                                                        |             |
|---------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------|-------------|
|                                       | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                   | <b>BRIDGE ADMINISTRATION</b>                                                                                                 | $\pm$                                                                                                |                                                                                                                                           |                                                                                                                 |             |
| 合                                     | Q<br>邀<br>上血<br>$\overline{\left( \right. }%$                                                                                                                                                                                                                                                                     | Company Details   Users (4)                                                                                                  | <b>Deal Tracking Groups (1)</b>                                                                      |                                                                                                                                           | $\Omega \quad \Omega \quad \equiv$                                                                              |             |
| $\mathcal{L}_{\mathcal{F}}$<br>4<br>W | $\wedge \hat{\mathbb{m}}$ GroupE<br>$\land \frac{\hat{\pi}}{\mathbf{u}}$ SubsidiaryE<br>SubsidiaryE.TreasurerA<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC<br>SubsidiaryE.TreasurerD<br>CroupE.AutoDealer<br>CroupE.TraderA<br>CroupE.TraderB<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB | Ê<br>土土<br>Users (4)<br>SubsidiaryE.TreasurerA<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC<br>SubsidiaryE.TreasurerD | <b>Create Password</b><br><b>Create Password</b><br><b>Create Password</b><br><b>Create Password</b> | <b>Status</b><br><b>Create PIN</b><br>Active<br><b>Create PIN</b><br>Active<br><b>Create PIN</b><br>Active<br><b>Create PIN</b><br>Active | û<br>$\bullet$<br>û<br>Œ<br>$\widehat{\mathbb{U}}$<br>$\bullet$<br>û<br>$\sigma$<br><b>Create Internal User</b> |             |
| ☆<br>D<br>$\bigcirc$<br>$\ominus$     | CroupE.TreasurerC                                                                                                                                                                                                                                                                                                 | Create Change Request<br>SubsidiaryE $\times$                                                                                |                                                                                                      |                                                                                                                                           | Discard All Changes ) (<br>Save                                                                                 |             |
|                                       | C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                               | <b>new T</b>                                                                                                                 |                                                                                                      | Tue, 03. Nov 2020, 18:54:08 GMT // Connected [FFM] ●                                                                                      |                                                                                                                 | <b>BETA</b> |

<span id="page-18-1"></span>Figure 24 List of users.

The user can be removed either:

- permanently (new User status: Deleted) or
- temporarily (new User status: Inactive)

In both cases the user will be removed and log in will be prohibited. Deleting a user removes the view access to the deal tracking groupand TAS/TOB rights, while making an user inactive retains these settings.

#### **Supervisor**

The Change Request to remove the I-TEX user can be accessed by the Supervisor via the Bridge Administration Homepage, similar to the approval of the user creation change request (see Chapter [4\)](#page-6-1).

## <span id="page-19-0"></span>**9 TECHNICAL INFORMATION**

360T requires a private certificate (.JKS file) for a user to login to the platform. With the entry of the password and PIN or PIN only (in case of an existing, valid password) an automatic download of the JKS file is triggered.

Some local PC settings may prevent the automatic download. In case of any issues, please download the folowing .pdf file containing technical requirements for the use of the system:

[https://download.360t.com/documents/Technical\\_Requirements\\_TEX.pdf](https://download.360t.com/documents/Technical_Requirements_TEX.pdf)

If necessary, this should be reviewed by your system administrator regarding your proxy and firewall settings.

In case of any technical questions, please contact the Client Advisory Services <NAME_EMAIL> or +49 69 900289 19.

## <span id="page-20-0"></span>**10 CONTACTING 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

### **Middle East Asia Pacific**

#### **United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

### **EMEA Americas**

#### **USA** *360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900

## **Singapore**

*360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756