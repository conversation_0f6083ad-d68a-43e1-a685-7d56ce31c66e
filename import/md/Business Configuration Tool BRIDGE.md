# Business Configuration Tool BRIDGE

*Processed page-by-page on 2025-07-30 11:58:38*
*Success rate: 100.0% (23/23 pages)*

---



<!-- PAGE 0 -->

## **BUSINESS CONFIGURATION TOOL**

![](_page_0_Picture_1.jpeg)

## **TEX MULTIDEALER TRADING SYSTEM**

USER GUIDE 360T BRIDGE:

BUSINESS CONFIGURATION TOOL: MARKET TAKER

© 360 TREASURY SYSTEMS AG, 2017 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG


<!-- PAGE 1 -->

## **CONTENTS**

| 1                |                                            | INTRODUCTION                                                                                                                                                          |  |
|------------------|--------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| $\mathbf{2}$     |                                            | GENERAL ………………………………………………………………………………………………                                                                                                                          |  |
| 3                |                                            | COUNTERPART RELATIONSHIPS                                                                                                                                             |  |
|                  | 31<br>3.2<br>3.3<br>3.4                    | INITIAL SET-UP/OVERVIEW<br>REQUESTING A RELATIONSHIP WITH A MARKET MAKER<br>NOTIFICATION ABOUT THE MARKET MAKER'S DECISION<br>COPYING THE SETUP TO THE CLIPBOARD      |  |
| $\boldsymbol{4}$ | <b>BANK BASKETS</b>                        |                                                                                                                                                                       |  |
|                  | 41<br>4.2<br>4.3<br>4.4                    | CONFIGURATION OF BANK BASKETS<br>SETTING UP BANK BASKET GROUPS<br>DEFINING BANK BASKETS PER PRODUCT AND TENOR<br>APPLYING BANK BASKETS PER CURRENCY COUPLE            |  |
| 5                |                                            | TRADING LIMITS                                                                                                                                                        |  |
|                  | 5 1<br>5.2<br>5.3<br>5.4<br>5.4.1<br>5.4.2 | SETTING LIMIT RATES/COMPANY EXCHANGE RATES<br>TRADING LIMITS CONFIGURATION<br>PRODUCT GROUPS<br>DEFINING TRADING LIMITS<br>Treasurer Limits<br><i>Provider Limits</i> |  |
| 6                |                                            | CONTACTING 360T                                                                                                                                                       |  |


<!-- PAGE 2 -->

## **TABLE OF FIGURES**

| Figure 1 Accessing Business Configuration Tool through Administration button4 |  |
|-------------------------------------------------------------------------------|--|
| Figure 2 Business Configuration Tool: Sample View.<br>5                       |  |
| Figure 3 Counterpart Relationships tab.<br>6                                  |  |
| Figure 3 All Providers<br>7                                                   |  |
| Figure 4 Requesting relationships with providers8                             |  |
| Figure 5 Pop-up notification about changes in counterpart relationships<br>8  |  |
| Figure 7 Examples of newly accepted relationships.<br>9                       |  |
| Figure 9 Copy table content to Clipboard<br>10                                |  |
| Figure 10 BCT: Bank Baskets tab.<br>11                                        |  |
| Figure 11 Adding a new Bank Basket Group12                                    |  |
| Figure 12 Creating a new Bank Basket Group12                                  |  |
| Figure 13 Adding banks to Bank Basket Groups<br>13                            |  |
| Figure 14 Configuration of Bank Baskets by Product14                          |  |
| Figure 15 Individual user settings per product and currency couple<br>15      |  |
| Figure 16 General tab: Company Exchange Rates16                               |  |
| Figure 17 Limit Currency Exchange Rates<br>16                                 |  |
| Figure 18 Trading Limits tab17                                                |  |
| Figure 19: Product Groups: example18                                          |  |
| Figure 20 Adding a new product group18                                        |  |
| Figure 21 Selecting products19                                                |  |
| Figure 22 Treasurer trading limits configuration20                            |  |
| Figure 24 Defining provider trading limits per product group21                |  |
| Figure 25 Defining Provider trading limits by legal entity22                  |  |


<!-- PAGE 3 -->

## **1 INTRODUCTION**

This user manual describes the functionality of the 360T Bridge Business Configuration Tool BCT enables administrator users to change the configuration for one or multiple entities at one time. The tool is available only to users who have business administrator rights. <NAME_EMAIL> or your customer relationship manager for setting up the administrator rights.

After entering the application over the starter application button called Administration, the initial standard Business Configuration Tool window will open up.

| <b>ENSING ROUP</b> BORSE                               |
|--------------------------------------------------------|
| Version: 3.23-SNAPSHOT                                 |
| 360T, Roman<br>$\checkmark$<br><b><i>BOSCOBODE</i></b> |
| Logout                                                 |
| <b>Change Password</b><br>Administration<br>Help       |

Figure 1 Accessing Business Configuration Tool through Administration button.

Depending on the user permissions, a variable number of tabs will be displayed, for example:

- General
- Counterpart Relationships
- Bank Baskets
- Trading Limits


<!-- PAGE 4 -->

| BCT Business Configuration Tool - DEMO                                                     | System Administration Help $ \Box$ $\times$                                      |  |
|--------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------|--|
| Bank Baskets   Trading Limits<br><b>Counterpart Relationships</b><br>General               |                                                                                  |  |
| Save Reload Discard Changes                                                                |                                                                                  |  |
| General<br><sup>7</sup> Company Exchange Rates<br>o.<br><b>7</b> Identification Codes<br>o |                                                                                  |  |
| 17/05/17 07:55:09 GMT                                                                      | Login as - 360T. Roman as User: CompanyBZ. TreasurerA, Company: CompanyBZ [1111] |  |

Figure 2 Business Configuration Tool: Sample View.

## **2 GENERAL**

The tab General allows administrator to:

- Set the Company Exchange Rates used for instance in the Limit Management functionality to convert all notional amounts into the company currency (see more details Chapter 5)
- Add/modify the BIC identification code or Participant Short Code used for instance for the Money market funds trading.

## **3 COUNTERPART RELATIONSHIPS**

The Counterpart Relationships functionality allows to manage relationships with liquidity providers and is available only to users who have correspondent administrator rights. <NAME_EMAIL> or your 360T relationship manager for setting up the administrator rights.

## **3.1 Initial set-up/ Overview**

The administrator user can access the counterpart relationship configuration through the BCT tab named Counterpart Relationships.


<!-- PAGE 5 -->

| <b>Business Configuration Tool - DEMO</b><br><b>Administration Help</b><br>$\Box$ $\times$<br><b>System</b><br>BCT |                      |                     |                    |             |                     |              |               |                   |                       |
|--------------------------------------------------------------------------------------------------------------------|----------------------|---------------------|--------------------|-------------|---------------------|--------------|---------------|-------------------|-----------------------|
| <b>Bank Baskets</b><br><b>Counterpart Relationships</b><br><b>Trading Limits</b><br><b>General</b>                 |                      |                     |                    |             |                     |              |               |                   |                       |
| <b>Reload Discard Changes</b><br><b>Save</b>                                                                       |                      |                     |                    |             |                     |              |               |                   |                       |
| ۵<br>CompanyBZ<br>$\circ$<br>All Providers<br>$\equiv$<br>o                                                        | Changed              | <b>Legal Entity</b> | <b>Provider</b>    | <b>RFS</b>  |                     | Order        |               | <b>Supersonic</b> |                       |
|                                                                                                                    |                      |                     |                    | Is RFS Req. | <b>Is RFS Prov.</b> | Is Ord. Req. | Is Ord. Prov. | Is SEP Req.       | <b>Is SE</b>          |
|                                                                                                                    |                      | CompanyBZ           | 360TBANK, TEST     | v           | ✔                   | v            | ✔             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | Barclays BARX.DEMO | v           | ✔                   | ✔            | Ψ             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | <b>BOAL, DEMO</b>  | ✔           | ✔                   | ✔            | ✔             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | CITIBANK.DEMO      | ✔           | ✔                   | ✔            | Ψ             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | COBA, DEMO         | v           | v                   | ✔            | Ψ             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | COBA.FRA DRESDN    | v           | ✔                   | v            | Ψ             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | Credit Suisse.DEMO | v           | ✔                   | v            | ✔             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | HSBC.DEMO          | v           | ✔                   | v            | Ψ             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | RBS.LND.DEMO       | v           | ✔                   | v            | Ψ             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | SEB.DEMO           | v           | ✔                   | v            | ✔             |                   |                       |
|                                                                                                                    |                      | CompanyBZ           | Unicredit.DEMO     | v           | ✔                   | v            | Ψ             |                   |                       |
|                                                                                                                    |                      |                     |                    |             |                     |              |               |                   |                       |
|                                                                                                                    | $\blacktriangleleft$ |                     |                    |             |                     |              |               |                   | $\blacktriangleright$ |
| 17/05/17 07:55:09 GMT<br>Login as - 360T. Roman as User: CompanyBZ. TreasurerA, Company: CompanyBZ                 |                      |                     |                    |             |                     |              |               |                   |                       |

Figure 3 Counterpart Relationships tab.

As an initial set-up a new market taker will see a list of all available providers.

Relationships with a provider can be established for the different trading methods e.g. RFS, Orders, Supersonic etc. For each of these products there is a different section on the screen.

Each of these sections consists of two columns in which the status of the relationship from the perspective of both counterparties involved is being displayed, e.g. for RFS the column "Is RFS Req." shows the relationship status from the point of view of the requester while the column "Is RFS Prov." displays the relationship status from the provider's perspective.

For counterpart relationships the following statuses exist:

Initial status undefined displayed as

Status accepted displayed as

Status rejected displayed as

The initial status of a relationship is undefined which is represented by a blue coloured field with white question mark inside.


<!-- PAGE 6 -->

| BOT Business Configuration Tool - DEMO<br>System Administration Help<br>$\Box$ $\times$  |                        |                     |                           |             |                     |                      |                                                                             |                   |                     |                       |
|------------------------------------------------------------------------------------------|------------------------|---------------------|---------------------------|-------------|---------------------|----------------------|-----------------------------------------------------------------------------|-------------------|---------------------|-----------------------|
| <b>Bank Baskets</b> Trading Limits<br><b>Counterpart Relationships</b><br><b>General</b> |                        |                     |                           |             |                     |                      |                                                                             |                   |                     |                       |
| Save Reload Discard Changes                                                              |                        |                     |                           |             |                     |                      |                                                                             |                   |                     |                       |
| <b>CompanyBZ</b><br>a.<br>$\bullet$<br>$\Box$ / Changes                                  | Changed                | <b>Legal Entity</b> | <b>Provider</b>           | <b>RFS</b>  |                     | Order                |                                                                             | <b>Supersonic</b> |                     | <b>MM Fund</b>        |
| <b>E</b> Providers                                                                       |                        |                     |                           | Is RFS Req. | <b>Is RFS Prov.</b> | Is Ord. Req.         | Is Ord. Prov.                                                               | Is SEP Req.       | <b>Is SEP Prov.</b> | <b>Is MMFund Re</b>   |
| o<br>Е                                                                                   | v                      | <b>CompanyBZ</b>    | <b>360TBANK EMEA.TEST</b> |             |                     |                      |                                                                             |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | 360TBANK.TEST             | ✔           | $\sqrt{2}$          | v                    | v                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | Bardays BARX.DEMO         | ✔           | ✔                   | ✔                    | ✔                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | <b>BOAL, DEMO</b>         | ✔           | ✔                   | ✔                    | ✔                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | CITIBANK.DEMO             | ✔           | v                   |                      | v                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | COBA.DEMO                 | v           | v                   | v                    | $\checkmark$                                                                |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | COBA.FRA DRESDNER         | V           | ✔                   | ✔                    | v                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | Credit Suisse.DEMO        | ✔           | $\checkmark$        | $\blacktriangledown$ | $\blacktriangledown$                                                        |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | HSBC.DEMO                 | ✔           | ✔                   | v                    | v                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | RBS.LND.DEMO              | ✔           | v                   | v                    | v                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | SEB.DEMO                  | ✔           | ✔                   | M                    | v                                                                           |                   |                     |                       |
|                                                                                          |                        | CompanyBZ           | Unicredit.DEMO            | ✔           | ✔                   |                      | ✔                                                                           |                   |                     |                       |
|                                                                                          |                        |                     |                           |             |                     |                      |                                                                             |                   |                     |                       |
|                                                                                          | $\left  \cdot \right $ |                     |                           |             |                     |                      |                                                                             |                   |                     | $\blacktriangleright$ |
| 22/05/17 11:35:06 GMT                                                                    |                        |                     |                           |             |                     |                      | Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ ETT |                   |                     |                       |

Figure 4 All Providers

## **3.2 Requesting a relationship with a market maker**

If a market taker company wants to do trades with one or more available market makers the administrator can request a relationship with the providers in question. For example a relationship for RFS can be requested by checking the check-box in the corresponding "Is RFS Req." field. The changes must be saved in order to be applied.


<!-- PAGE 7 -->

## User Guide 360T Business Configuration Tool (BRIDGE)

| BOT Business Configuration Tool - DEMO<br>System Administration Help<br>$\Box$ $\times$                                                           |                        |                     |                           |            |             |                     |    |                                   |  |                   |                     |                       |
|---------------------------------------------------------------------------------------------------------------------------------------------------|------------------------|---------------------|---------------------------|------------|-------------|---------------------|----|-----------------------------------|--|-------------------|---------------------|-----------------------|
| <b>Counterpart Relationships*</b><br>Bank Baskets   Trading Limits<br><b>General</b>                                                              |                        |                     |                           |            |             |                     |    |                                   |  |                   |                     |                       |
| Save Reload Discard Changes                                                                                                                       |                        |                     |                           |            |             |                     |    |                                   |  |                   |                     |                       |
| $\overline{u}$<br>$\bullet$<br><b>CompanyBZ</b>                                                                                                   |                        | <b>Legal Entity</b> | <b>Provider</b>           | <b>RFS</b> |             |                     |    | <b>Order</b>                      |  | <b>Supersonic</b> |                     | <b>MM Fund</b>        |
| / Changes<br>$\Box$<br><b>Providers</b>                                                                                                           | Changed                |                     |                           |            | Is RFS Req. | <b>Is RFS Prov.</b> | Is | This is the Configuration Toolbar |  | <b>SEP Req.</b>   | <b>Is SEP Prov.</b> | <b>Is MMFund Re</b>   |
| ۰<br>Е                                                                                                                                            | v                      | <b>CompanyBZ</b>    | <b>360TBANK EMEA.TEST</b> |            |             |                     |    |                                   |  |                   |                     |                       |
| ■ <del>●</del> Fully Accepted                                                                                                                     |                        | CompanyBZ           | 360TBANK, TEST            |            |             | v                   |    | ✔                                 |  |                   |                     |                       |
| (Partly) Accepted                                                                                                                                 |                        | CompanyBZ           | Barclays BARX.DEMO        |            |             | ✔                   |    | ✔                                 |  |                   |                     |                       |
| / (Partly) Undefined<br>$\Box$                                                                                                                    |                        | CompanyBZ           | <b>BOAL.DEMO</b>          | ✔          |             | ✔                   |    | v                                 |  |                   |                     |                       |
| <b>D</b> (Partly) Rejected                                                                                                                        |                        | CompanyBZ           | CITIBANK.DEMO             | v          |             | v                   |    | v                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        | CompanyBZ           | COBA.DEMO                 | v          |             | v                   | v  | v                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        | CompanyBZ           | COBA.FRA DRESDNER         |            |             | ✔                   | ✔  | ✔                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        | CompanyBZ           | Credit Suisse.DEMO        | V          |             | ✔                   | ✔  | ✔                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        | CompanyBZ           | HSBC.DEMO                 | v          |             | v                   | v  | v                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        | CompanyBZ           | RBS.LND.DEMO              | v          |             | $\checkmark$        | v  | v                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        | CompanyBZ           | SEB.DEMO                  | V          |             | $\checkmark$        | v  | v                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        | CompanyBZ           | Unicredit.DEMO            | v          |             | $\sqrt{}$           | M  | ✔                                 |  |                   |                     |                       |
|                                                                                                                                                   |                        |                     |                           |            |             |                     |    |                                   |  |                   |                     |                       |
|                                                                                                                                                   | $\left  \cdot \right $ |                     |                           |            |             |                     |    |                                   |  |                   |                     | $\blacktriangleright$ |
| Login as - 360T. Roman as User: CompanyBZ. Treasurer A, Company: CompanyBZ ESSEREE ESSEREE<br>configuration saved 3936 ms - 22/05/17 12:00:05 GMT |                        |                     |                           |            |             |                     |    |                                   |  |                   |                     |                       |

Figure 5 Requesting relationships with providers

After requesting a relationship for RFS with a certain provider the market taker is immediately able to send a RFS request to this provider. Orders and SEP deals can only be done after the provider has explicitly accepted the relationship.

Please note that users have to add the newly added provider to their provider list on their RFS Live Pricing screen upon sending a RFS request.

## **3.3 Notification about the market maker's decision**

It is now up to the provider to decide whether he wants to accept or reject the requested relationship. He can differentiate between support for RFS and other products.

The counterpart relationship administrator will be notified about the decision of the provider. If the administrator is logged in with an open counterpart relationship management tool at the time of a provider side change a pop-up window will appear asking him to reload the configuration.

![](_page_0_Picture_9.jpeg)

Figure 6 Pop-up notification about changes in counterpart relationships


<!-- PAGE 8 -->

The items changed by a provider are marked in blue writing and can be found in the node Changes/News in the tree.

| <b>Business Configuration Tool - DEMO</b><br><b>Administration Help</b><br>$ \Box$ $\times$<br>BCT<br>System<br>Bank Baskets   Trading Limits<br><b>Counterpart Relationships</b><br>General |                                                                                                                          |                |                     |                           |             |                     |              |               |                   |                     |                     |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------|----------------|---------------------|---------------------------|-------------|---------------------|--------------|---------------|-------------------|---------------------|---------------------|
| <b>Save Reload Discard Changes</b>                                                                                                                                                           |                                                                                                                          |                |                     |                           |             |                     |              |               |                   |                     |                     |
|                                                                                                                                                                                              | □ © CompanyBZ<br>$\Box$ / Changes<br>roviders<br>日<br>$\Box \equiv$ All Providers                                        | <b>Changed</b> | <b>Legal Entity</b> | <b>Provider</b>           | <b>RFS</b>  |                     | Order        |               | <b>Supersonic</b> |                     | <b>MM Fund</b>      |
|                                                                                                                                                                                              |                                                                                                                          |                |                     |                           | Is RFS Req. | <b>Is RFS Prov.</b> | Is Ord. Req. | Is Ord. Prov. | Is SEP Req.       | <b>Is SEP Prov.</b> | <b>Is MMFund Re</b> |
|                                                                                                                                                                                              |                                                                                                                          | v.             | <b>CompanyBZ</b>    | <b>360TBANK EMEA.TEST</b> |             |                     |              |               |                   |                     |                     |
|                                                                                                                                                                                              | <b>■</b> <del>V</del> Fully Accepted<br>(Partly) Accepted<br><b>D</b> / (Partly) Undefined<br><b>D</b> (Partly) Rejected |                |                     |                           |             |                     |              |               |                   |                     |                     |

Figure 7 Examples of newly accepted relationships.

All relationships can be viewed by clicking on the node "All Providers" in the tree. Apart from the overview the list of providers is broken down into different views for fully accepted, (partly) accepted, (partly) undefined and (partly) rejected relationships.

In case a requester company does not want to send requests to a certain provider – with whom a relationship exists – anymore, the counterpart relationship administrator can simply deselect the relevant check-box e.g. "Is RFS Req." for RFS. After saving the configuration the requester company's users will no longer be able to send orders to the affected provider.

## **3.4 Copying the setup to the clipboard**

The setup can be exported to the clipboard in order to be imported in another system, for instance in a spreadsheet software.

To copy the setup, please select the concerned view (Changes, All providers, Fully accepted, Partly accepted etc.), then click the right mouse button to open the context menu in the table and select the command Copy to Clipboard.


<!-- PAGE 9 -->

| Business Configuration Tool - DEMO<br>System Administration Help<br>$ \Box$ $\times$     |                        |                        |                                 |             |                          |              |                                                                         |                   |                     |                       |
|------------------------------------------------------------------------------------------|------------------------|------------------------|---------------------------------|-------------|--------------------------|--------------|-------------------------------------------------------------------------|-------------------|---------------------|-----------------------|
| <b>Bank Baskets</b> Trading Limits<br><b>Counterpart Relationships</b><br><b>General</b> |                        |                        |                                 |             |                          |              |                                                                         |                   |                     |                       |
| Save Reload Discard Changes                                                              |                        |                        |                                 |             |                          |              |                                                                         |                   |                     |                       |
| $\overline{a}$<br><b>CompanyBZ</b><br>$\bullet$<br>/ Changes<br>۰<br><b>E</b> Providers  | Changed                | <b>Legal Entity</b>    | <b>Provider</b>                 | <b>RFS</b>  |                          | Order        |                                                                         | <b>Supersonic</b> |                     | <b>MM Fund</b>        |
|                                                                                          |                        |                        |                                 | Is RFS Req. | Is RFS Prov.             | Is Ord. Req. | Is Ord. Prov.                                                           | Is SEP Req.       | <b>Is SEP Prov.</b> | <b>Is MMFund Re</b>   |
| ۰<br>旧                                                                                   | v                      | <b>CompanyBZ</b>       | <b>360TBANK EMEA.TEST</b>       |             |                          |              |                                                                         |                   |                     |                       |
| <b>■</b> <del>⁄</del> Fully Accepted                                                     |                        | CompanyBZ              | 360TBANK, TEST                  | v           |                          |              |                                                                         |                   |                     |                       |
| Partly) Accepted<br>o                                                                    |                        | CompanyBZ              | Bardays BARX.DEMO               | V           | <b>Copy to Clipboard</b> |              | v                                                                       |                   |                     |                       |
| / (Partly) Undefined<br>o                                                                |                        | CompanyBZ              | BOAL.DEMO                       | V           |                          |              |                                                                         |                   |                     |                       |
| <b>D</b> (Partly) Rejected                                                               |                        | CompanyBZ              | CITIBANK.DEMO                   | v           | v                        |              | $\checkmark$                                                            |                   |                     |                       |
|                                                                                          |                        | CompanyBZ              | COBA.DEMO                       | v           | v                        | v            | $\checkmark$                                                            |                   |                     |                       |
|                                                                                          |                        | CompanyBZ              | COBA.FRA DRESDNER               | ✔           | ✔                        | ✔            | v                                                                       |                   |                     |                       |
|                                                                                          |                        | CompanyBZ              | Credit Suisse.DEMO<br>HSBC.DEMO | ✔<br>✔      | ✔<br>v                   | ✔<br>v       | ✔<br>v                                                                  |                   |                     |                       |
|                                                                                          |                        | CompanyBZ<br>CompanyBZ | RBS.LND.DEMO                    | v           | v                        | v            | v                                                                       |                   |                     |                       |
|                                                                                          |                        | CompanyBZ              | SEB.DEMO                        | ✔           | ✔                        | v            | v                                                                       |                   |                     |                       |
|                                                                                          |                        | CompanyBZ              | Unicredit.DEMO                  | ✔           | $\sqrt{}$                | v            | ✔                                                                       |                   |                     |                       |
|                                                                                          |                        |                        |                                 |             |                          |              |                                                                         |                   |                     |                       |
|                                                                                          | $\left  \cdot \right $ |                        |                                 |             |                          |              |                                                                         |                   |                     | $\blacktriangleright$ |
| discarding changes 30 ms - 22/05/17 11:41:17 GMT                                         |                        |                        |                                 |             |                          |              | Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ |                   |                     |                       |

Figure 8 Copy table content to Clipboard

Once copied to the clipboard, the content can then be pasted in another system for further processing using the paste command available in such a system, generally Ctrl+V.

## **4 BANK BASKETS**

Once a counterpart relationship is agreed with the market maker, the business administrator can configure bank baskets depending on the product or product group and currencies.

Please note that only users with corresponding administration rights are able to manage the company's Bank Baskets. <NAME_EMAIL> or your customer relationship manager for setting up the administrator rights.

## **4.1 Configuration of Bank Baskets**

The Bank Basket configuration tool can be accessed through the tab Bank Baskets in the Business Configuration Tool.


<!-- PAGE 10 -->

| Business Configuration Tool - DEMO                                                                                                                                                            |                     |                       |                                                                         | System Administration Help | $\Box$ $\times$<br>$\equiv$ |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|-----------------------|-------------------------------------------------------------------------|----------------------------|-----------------------------|
| General   Counterpart Relationships                                                                                                                                                           | <b>Bank Baskets</b> | <b>Trading Limits</b> |                                                                         |                            |                             |
| Save Reload Discard Changes                                                                                                                                                                   |                     |                       |                                                                         |                            |                             |
| CompanyBZ - Bank Baskets<br>$\overline{\mathbf{u}}$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br><b>B</b> & Bank Basket Groups<br><b>D</b> O Standard Group<br><b>O</b> Product Bank Basket |                     |                       |                                                                         |                            |                             |
|                                                                                                                                                                                               |                     |                       |                                                                         |                            |                             |
| 22/05/17 11:35:06 GMT                                                                                                                                                                         |                     |                       | Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ |                            |                             |

Figure 9 BCT: Bank Baskets tab.

Depending on the entity setup, a single entity or multiple entities are displayed, each of them having its own bank basket configuration.

## **4.2 Setting up Bank Basket Groups**

First of all the administrator may set up Bank Basket Groups. Per default there is a Standard Group provided which includes all providers the requester company has a relationship with.

The administrator can set up additional Bank Basket Groups using the "Add" button located in the panel on the right hand side of the window.


<!-- PAGE 11 -->

| BOT Business Configuration Tool - DEMO                                                                                                |                                                            |  | System Administration Help |                                   | $ \Box$ $\times$ |  |  |
|---------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------|--|----------------------------|-----------------------------------|------------------|--|--|
| General   Counterpart Relationships                                                                                                   | <b>Bank Baskets*</b><br><b>Trading Limits</b>              |  |                            |                                   |                  |  |  |
| Save Reload Discard Changes                                                                                                           |                                                            |  |                            |                                   |                  |  |  |
| CompanyBZ - Bank Baskets<br>3 & Bank Basket Groups<br>$\Box$ $\Box$ Spot<br><b>D</b> C Standard Group<br><b>e</b> Product Bank Basket | <b>Bank Basket Groups</b><br>Spot<br><b>Standard Group</b> |  |                            | Add<br>Rename<br>Remove<br>Select |                  |  |  |
| Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ $\Box$<br>22/05/17 11:35:06 GMT                               |                                                            |  |                            |                                   |                  |  |  |

Figure 10 Adding a new Bank Basket Group

After pressing the "Add" button a window will open up where the administrator can enter the name of the new Bank Basket Group.

| Create new requester group             |  |
|----------------------------------------|--|
| Please specify group name<br>lew Group |  |
| Cancel<br><b>COLL</b>                  |  |

Figure 11 Creating a new Bank Basket Group

It is also possible to rename or remove existing groups using the "Rename" and "Remove" buttons. By pressing the "Select" button the administrator can directly jump into the configuration of a particular group.

If the administrator now clicks on this newly created group within the Bank Baskets Group tree he is able to select certain banks out of the full list of banks for this particular Bank Basket.

The selection is done by moving certain banks from the "available members" area on the right side to the "selected members" area on the left side using the arrow buttons.


<!-- PAGE 12 -->

| BOT Business Configuration Tool - DEMO                                                                                                               |                                               |                               |                                                                                                                                                                                                   |  | System Administration Help |  | $-\Box \times$ |
|------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------|-------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|----------------------------|--|----------------|
| General   Counterpart Relationships                                                                                                                  | <b>Bank Baskets*</b><br><b>Trading Limits</b> |                               |                                                                                                                                                                                                   |  |                            |  |                |
| Save Reload Discard Changes                                                                                                                          |                                               |                               |                                                                                                                                                                                                   |  |                            |  |                |
| C & CompanyBZ - Bank Baskets<br><b>B</b> & Bank Basket Groups<br>$\Box$ $\bigcirc$ Spot<br><b>D</b> O Standard Group<br><b>O</b> Product Bank Basket | selected members<br>BOAL.DEMO                 | $\rightarrow$<br>$\prec\prec$ | available members<br>360TBANK.TEST<br>Barclays BARX.DEMO<br>CITIBANK.DEMO<br>COBA.DEMO<br>COBA.FRA DRESDNER.DEMO<br>Credit Suisse.DEMO<br>HSBC.DEMO<br>RBS.LND.DEMO<br>SEB.DEMO<br>Unicredit.DEMO |  |                            |  |                |
| Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ ETT<br>22/05/17 11:35:06 GMT                                                 |                                               |                               |                                                                                                                                                                                                   |  |                            |  |                |

Figure 12 Adding banks to Bank Basket Groups

Please note that if a new relationship with a provider is being established this provider is automatically added to the Standard Group.

## **4.3 Defining Bank Baskets per product and tenor**

After having added providers to groups, the administrator can decide which Bank Basket group to use for which product and tenor. This can be done in the node Product Bank Basket.


<!-- PAGE 13 -->

| Save Reload Discard Changes<br>CompanyBZ - Bank Baskets |                                       |                     |                                                |        |
|---------------------------------------------------------|---------------------------------------|---------------------|------------------------------------------------|--------|
| <b>B</b> & Bank Basket Groups                           | <b>Product Type</b>                   | <b>Up To Period</b> | <b>Bank Basket</b>                             | Add    |
| $\Box$ $\bigcirc$ Spot                                  | <b>Block-Trade</b>                    | <b>UNLIMITED</b>    | <b>Standard Group</b>                          | Rename |
| <b>D</b> C Standard Group                               | CapFloor                              | <b>UNLIMITED</b>    | <b>Standard Group</b>                          | Remove |
| <b>Product Bank Basket</b>                              | <b>FRA</b>                            | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | FX Early Settlement                   | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>Fx Forward</b>                     | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>Fx Option</b>                      | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>FX Prolongation</b>                | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>Fx Spot</b>                        | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | Fx Swap                               | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>IRS</b>                            | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | LoanDeposit                           | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>MFT</b>                            | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | Multi-Leg-Swap<br><b>NDF</b>          | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         |                                       | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | Prolongation                          | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>Fx Time Option</b><br><b>NDS</b>   | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | Rfs Boost Forward                     | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         |                                       | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | Rfs Boost NDF<br><b>Rfs Boost NDS</b> | <b>UNLIMITED</b>    | <b>Standard Group</b><br><b>Standard Group</b> |        |
|                                                         |                                       | <b>UNLIMITED</b>    |                                                |        |
|                                                         | <b>Rfs Boost Spot</b>                 | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | Rfs Boost Swap<br><b>SEP Forward</b>  | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>SEP NDF</b>                        | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         |                                       | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>SEP NDS</b>                        | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>SEP Spot</b>                       | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>SEP Swap</b>                       | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |
|                                                         | <b>TriParty Repo</b>                  | <b>UNLIMITED</b>    | <b>Standard Group</b>                          |        |

Figure 13 Configuration of Bank Baskets by Product

The administrator gets an overview of the existing products and can then assign one of the previously defined Bank Basket Groups per product by selecting the desired Bank Basket from the drop-down list in the column Bank Basket.

In addition, the tenor can also be used to determine the banks to which a request up to a certain maturity period will be sent.

The definitions done in the Bank Basket configuration tool are available for all users of the concerned company.

If a requester company uses the Trade As functionality the Trade As entities are also listed in the Bank Basket configuration and it is possible to define different Bank Baskets for different legal entities.

## **4.4 Applying Bank Baskets per currency couple**

After the Bank Baskets have been configured on company level, each individual user can adapt the company's Bank Baskets based on additional user specific criteria. For instance, different settings can be defined for individual currency couples in the RFS Live Pricing Product Definition.

When you open the Product Definition window for a certain product and selects the effective date of the request, the Bank Basket settings that have been done on company level for the concerned product and tenor will be retrieved and the provider list displayed accordingly. You can directly select/unselect individual providers from the list or select all/unselect all providers from the Provider List. After clicking on Customizer icon in the upper right corner of the Provider List tab, the tab will turn and you can add or remove providers to/from their individual My Providers list, so that they are not visible any more in the Product Definition Provider List.


<!-- PAGE 14 -->

|                               | $EUR \vee$<br><b>Buy</b><br>۰                                                          | Notional                   |               |
|-------------------------------|----------------------------------------------------------------------------------------|----------------------------|---------------|
|                               | $\rightleftharpoons$<br>Sell<br>$USD \vee$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                            |               |
|                               | Date                                                                                   |                            |               |
|                               | 1 Week<br>Do, 01.06.2017<br>$\checkmark$                                               | 臝                          |               |
|                               |                                                                                        |                            |               |
|                               |                                                                                        |                            |               |
|                               |                                                                                        |                            |               |
|                               |                                                                                        |                            |               |
| Provider List<br>Transactions | Comments                                                                               |                            |               |
| 360TBANK.TEST                 | M BNPP.DEMO                                                                            | BOAL.DEMO                  | $\frac{1}{2}$ |
| <b>Barclays BARX.DEMO</b>     | <b>CITIBANK.DEMO</b><br>M                                                              | COBA.DEMO                  |               |
| <b>Credit Suisse.DEMO</b>     | <b>DB.DEMO</b><br>✓                                                                    | HSBC.DEMO                  |               |
| JPMORGAN.DEMO                 | LLOYDS.DEMO                                                                            | <b>Macquarie Bank.TEST</b> |               |
| PEBANKAPAC.TEST               | PEBANKEMEA1.TEST<br>M                                                                  | RBS.LND.DEMO               |               |
| <b>SEB.DEMO</b>               | Unicredit.DEMO                                                                         |                            |               |
| Select All Unselect All       |                                                                                        |                            |               |

Figure 14 Individual user settings per product and currency couple

The selection of providers will automatically be saved in the user's settings for the selected currency pair when you close the Product Definition window and will be re-used for future requests of the same product, tenor and currency couple.

This way you can define individual Bank Baskets for different products and currency couples.

## **5 TRADING LIMITS**

Trading limits can be defined to restrict trading. Limits can be defined both as intraday trading limits and as settlement limits. The intraday limit configuration is possible per user, i.e. total notional which a user is allowed to trade by trading day and in a single deal. Moreover, the intraday limits can be defined per counterparty, i.e. total notional which the requester entity can trade against its market makers. The settlement limits can be defined by counterparty and day for a maximum of 365 days. A settlement limit is the maximum receivable amount from a counterparty (provider or requestor) for one specific value date. All limits are notional based and can be set in the company currency of the requester entity. Every new request is checked against the remaining limits and when the new transaction does not exceed the remaining limit, the request will be sent out to the selected providers. After execution of the trade, the remaining limit is reduced accordingly.

It is important to note that only users with corresponding user rights are able to administer limits. <NAME_EMAIL> or your customer relationship manager for setting up the administrator rights. The following chapters describe the management of Treasurer and Provider trading limits.

## **5.1 Setting Limit Rates /Company Exchange Rates**

The configuration of Limit Rates is the basis to set up Trading and Settlement Limits. Without them the configuration of Limits is not allowed. An adequate error message is shown if the Limit Rates have not been configured.

Limit Rates can be configured via the folder Company Exchange Rates in the General tab.


<!-- PAGE 15 -->

| <b>Business Configuration Tool - DEMO</b><br>BCT                                          |                                      |                                                                         | System | <b>Administration Help</b> | $\Box$ $\times$<br>- |
|-------------------------------------------------------------------------------------------|--------------------------------------|-------------------------------------------------------------------------|--------|----------------------------|----------------------|
| <b>Counterpart Relationships</b><br><b>General</b>                                        | <b>Bank Baskets   Trading Limits</b> |                                                                         |        |                            |                      |
| Save Reload Discard Changes                                                               |                                      |                                                                         |        |                            |                      |
| General<br><sup>7</sup> Company Exchange Rates<br>o<br><b>7</b> Identification Codes<br>D |                                      |                                                                         |        |                            |                      |
|                                                                                           |                                      |                                                                         |        |                            |                      |
| 17/05/17 07:55:09 GMT                                                                     |                                      | Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ |        |                            | ┱                    |

Figure 15 General tab: Company Exchange Rates

In order to include all notional amounts requested for other currencies than the company currency of the requesting entity, FX rates can be set in the Company Exchange Rates table.

| <b>Business Configuration Tool - DEMO</b><br>BCT                                | <b>Administration Help</b><br>System         | $-$ 0 $\times$                                                          |                     |
|---------------------------------------------------------------------------------|----------------------------------------------|-------------------------------------------------------------------------|---------------------|
|                                                                                 |                                              |                                                                         |                     |
| <b>Counterpart Relationships</b><br><b>General</b>                              | <b>Bank Baskets</b><br><b>Trading Limits</b> |                                                                         |                     |
|                                                                                 |                                              |                                                                         |                     |
| Save Reload Discard Changes                                                     |                                              |                                                                         |                     |
| General<br>Company Exchange Rates<br>Ð<br><b>Identification Codes</b><br>Y<br>a | Auto Update Rates Daily V                    |                                                                         | <b>Update Rates</b> |
|                                                                                 | <b>Currency</b>                              | <b>Exchange Rate</b>                                                    |                     |
|                                                                                 | EUR/AED                                      | 4.07104                                                                 |                     |
|                                                                                 | EUR/AFN                                      | 75,43420                                                                |                     |
|                                                                                 | EUR/ALL                                      | 134.231                                                                 |                     |
|                                                                                 | EUR/AMD                                      | 536,502                                                                 |                     |
|                                                                                 | EUR/ANG                                      | 1.92113                                                                 |                     |
|                                                                                 | EUR/AOA                                      | 182,98849                                                               |                     |
|                                                                                 | EUR/ARS                                      | 17,25826                                                                |                     |
|                                                                                 | EUR/AUD                                      | 1,49269                                                                 |                     |
|                                                                                 | EUR/AWG                                      | 1.98409                                                                 |                     |
|                                                                                 | EUR/AZN                                      | 1.85108                                                                 |                     |
|                                                                                 | EUR/BAM                                      | 1.95782                                                                 |                     |
|                                                                                 | EUR/BBD                                      | 2.16665                                                                 |                     |
|                                                                                 | EUR/BDT                                      | 89.26187                                                                |                     |
|                                                                                 | <b>EUR/BGN</b>                               | 1.94552                                                                 |                     |
|                                                                                 | EUR/BHD                                      | 0.414819                                                                |                     |
|                                                                                 | EUR/BIF                                      | 1,874.355                                                               |                     |
|                                                                                 | EUR/BMD                                      | 1.10843                                                                 |                     |
|                                                                                 | EUR/BND                                      | 1,54648                                                                 |                     |
|                                                                                 | EUR/BOB                                      | 7.60383                                                                 |                     |
|                                                                                 | EUR/BRL                                      | 3.43103                                                                 |                     |
|                                                                                 | EUR/BSD                                      | 1,10239                                                                 |                     |
|                                                                                 | EUR/BWP                                      | 11.39300                                                                |                     |
|                                                                                 | EUR/BYN                                      | 2.04827                                                                 |                     |
|                                                                                 | EUR/BYR                                      | 2.231                                                                   |                     |
|                                                                                 | EUR/BZD                                      | 2.17596                                                                 |                     |
|                                                                                 | <b>CUDICAN</b>                               | $\cdot$<br>$+$ Entron                                                   |                     |
| 17/05/17 07:55:09 GMT                                                           |                                              | Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ |                     |

Figure 16 Limit Currency Exchange Rates

The rates can be entered manually by double-clicking in the corresponding cell.


<!-- PAGE 16 -->

In case the conversion should be based on market rates, they can be uploaded by clicking on the button "Update Rates". The current spot rates from 360T market data provider will be pasted in the corresponding fields. If no rate is available, a rate of 1.0000 will be displayed by default.

Additionally, the administrator can check the option "Auto Update Rates Daily" in order to automatically update the FX rates by using 360T's independent market data source.

## **5.2 Trading Limits Configuration**

The limit configuration functionality is available through the tab Trading Limits. Depending on the permissions granted, the administrator is able to set trading or settlement limits or both of them. Within the two limit categories, depending on the company's setup, he will be able to set provider, treasurer or requester limits or all of them. Therefore the available options shown in the following screenshots might look different for an administrator who does not have permission to configure both limits.

![](_page_0_Picture_6.jpeg)

Figure 17 Trading Limits tab

## **5.3 Product Groups**

Limits are in general defined for certain product groups. Once defined, these product groups are available for all different types of limits, meaning Trading Limits, Settlement Limits and also for Autodealer Intraday Notional Limits if the company has an Autodealer.

The administrator should consider this when creating a new product group and also when changing the configuration of existing product groups or even deleting a product group.

There are some predefined product groups available when the administrator opens the tool for the first time.

It is possible to add new groups or remove existing ones using the buttons on the upper right side of the tool.


<!-- PAGE 17 -->

| <b>Business Configuration Tool - DEMO</b><br>BCT                                                                                                                                                                              |                                              |                                                                                  |  | System Administration Help |  | $-$ 0 $\times$                                   |  |  |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------|----------------------------------------------------------------------------------|--|----------------------------|--|--------------------------------------------------|--|--|
| Counterpart Relationships  <br>General                                                                                                                                                                                        | <b>Bank Baskets</b><br><b>Trading Limits</b> |                                                                                  |  |                            |  |                                                  |  |  |
| Save Reload Discard Changes                                                                                                                                                                                                   |                                              |                                                                                  |  |                            |  |                                                  |  |  |
| $\Box$ Limits<br>酮<br><b>Trading Limits</b><br>۵<br>۵<br><b>T</b> Product Groups<br>$\Box$ $\oplus$ FS Swaps<br><b>D H</b> FX Spot<br><b>7</b> Requester Limits<br>D<br><b>7</b> Provider Limits<br><b>7</b> Treasurer Limits | <b>Product Groups</b>                        | <b>FS Swaps</b><br><b>FX Spot</b>                                                |  |                            |  | Add<br>Rename<br>Copy<br>Remove<br><b>Select</b> |  |  |
| configuration saved 2358 ms - 17/05/17 08:44:59 GMT                                                                                                                                                                           |                                              | Login as - 360T. Roman as User: CompanyBZ. TreasurerA, Company: CompanyBZ $\Box$ |  |                            |  |                                                  |  |  |

Figure 18: Product Groups: example.

If the administrator clicks on the "Add" button in order to create a new product group the following window will pop-up where a user can enter the name of the new group.

| <b>Create new Product Group</b>                |  |
|------------------------------------------------|--|
| Please specify group name<br>New Product Group |  |
| Cancel<br>nĸ                                   |  |

Figure 19 Adding a new product group

After clicking OK the newly defined group will be available for further configuration.

The initially provided product groups and also all newly created product groups do at first not contain any products.

Therefore as a next step the administrator needs to add products to the groups according to his business needs.

If a user clicks on a product group the following view appears:


<!-- PAGE 18 -->

![](_page_0_Picture_2.jpeg)

Figure 20 Selecting products

The administrator can now select one or more products that he wants to add to this particular group by using the arrow buttons to move products from "available products" to "selected products".

Please note that for Trading Limits a product can be a member of several groups. If inconsistent limits for a certain product have been defined the system will assume the most stringent definition as the relevant one.

Please also note that in case a product is not selected in any product group then no limits are set-up for that product. That means that all users are allowed to trade this product without any limitation.


<!-- PAGE 19 -->

## **5.4 Defining Trading Limits**

## **5.4.1 Treasurer Limits**

The limits can be defined for each product group and each individual user. If no limit is defined the default setting is "unlimited".

In order to edit the limits the administrator can simply click on a certain line and edit the limit amounts.

The first row: \*TOTAL LIMIT ALL INDIVIDUALS\* refers to the OVERALL limit for all users of the company.

Example: Setting a daily trading limit of 10,000,000 under "TOTAL LIMIT ALL INDIVIDUALS" means that all users **together** cannot execute more than 10,000,000 as a group on a given day.

To prevent a user from dealing a specific product, please insert "0".

Within a certain product group a daily trading limit can be defined as well as a deal limit to allow a maximum notional amount per deal for a particular user.

| <b>Business Configuration Tool - DEMO</b><br><b>BCT</b>                                                                                                                                 |                                              |                                      |                                      | System                 | <b>Administration</b><br>Help | $\Box$ $\times$          |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------|--------------------------------------|--------------------------------------|------------------------|-------------------------------|--------------------------|
| General   Counterpart Relationships  <br><b>Trading Limits</b><br><b>Bank Baskets</b><br><b>Reload Discard Changes</b><br>Save                                                          |                                              |                                      |                                      |                        |                               |                          |
| <b>E</b> Limits<br>龍<br>۵<br>Trad<br>Reloads Configuration from the server<br>۰<br><b>GROSS OF ORDER</b><br>o<br><b>ED</b> FS Swaps<br><b>EX Spot</b><br>o<br><b>7</b> Requester Limits |                                              | <b>Daily Trading Limit</b>           | <b>Deal Limit</b>                    | <b>Executed Trades</b> | <b>Pending Orders</b>         | <b>Used Amount</b>       |
|                                                                                                                                                                                         | T ALL INDIVIDUALS *<br>CompanyBZ.TreasurerA  | <b>UNLIMITED</b><br><b>UNLIMITED</b> | <b>UNLIMITED</b><br><b>UNLIMITED</b> | 0.00<br>0.00           | 0.00<br>0.00                  | $\Omega$<br>$\mathbf{0}$ |
|                                                                                                                                                                                         | CompanyBZ.TreasurerB<br>CompanyBZ.TreasurerC | <b>UNLIMITED</b><br>UNLIMITED -      | <b>UNLIMITED</b><br><b>UNLIMITED</b> | 0.00<br>0.00           | 0.00<br>0.00                  | $\Omega$<br>$\bf{0}$     |
| <b>7</b> Provider Limits<br>Ð<br><b>Treasurer Limits</b><br>۰<br><b>7 FS Swaps</b><br>а                                                                                                 |                                              |                                      |                                      |                        |                               |                          |
| <b>T</b> FX Spot<br>o                                                                                                                                                                   |                                              |                                      |                                      |                        |                               |                          |
|                                                                                                                                                                                         |                                              |                                      |                                      |                        |                               |                          |
|                                                                                                                                                                                         |                                              |                                      |                                      |                        |                               |                          |

Figure 21 Treasurer trading limits configuration

In case the notional amount of a new request exceeds the remaining limit and/or the deal limit, a message is displayed and the user is prevented from sending this request.

The daily limit can be increased or modified at any time by the limit administrator, e.g. in case a trade should exceptionally be done although the individual daily trading limit is already reached.

To allow the execution of a transaction, both the individual Daily Trading Limit and the total limit of \* TOTAL LIMIT ALL INDIVIDUALS \* are checked.

The Used Amount shows the current consumption of the user's daily trading limit. The Used Amount is automatically reset to zero at 00:00:00 UTC every day.

The Used Amount column gets updated on one hand with every executed deal and on the other hand with each placed order. The consumption related to executed trades and to pending orders is displayed each as a distinct value.

Executed trades are only taken into account on the day of their execution. Orders are taken into account every day anew, unless they are cancelled, withdrawn or expired. Once executed, an order is not pending anymore and therefore accounted in the Executed Trades amount.


<!-- PAGE 20 -->

## **5.4.2 Provider Limits**

Provider limits define the maximum daily transaction exposure to a specific provider counterparty.

Provider Limits can be defined for each individual Product Group. For customers with a "tradeas" setup, provider limits can be defined distinctly for each legal entity. The trading limit default setting is "unlimited".

In order to edit the limits the administrator can simply click on a certain line and edit the limit amounts.

When setting an amount under "ALL COMPANIES", a maximum daily trading limit is set as **total** for all providers for this Product Groups.

| BCT Business Configuration Tool - DEMO                                                                                                                                                                                                                                                                                                                       |                                               |                    |                            |                        | System Administration Help | $\Box$ $\times$<br>-  |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------|--------------------|----------------------------|------------------------|----------------------------|-----------------------|
| Counterpart Relationships  <br>General                                                                                                                                                                                                                                                                                                                       | <b>Bank Baskets</b><br><b>Trading Limits*</b> |                    |                            |                        |                            |                       |
| <b>Save Reload Discard Changes</b>                                                                                                                                                                                                                                                                                                                           |                                               |                    |                            |                        |                            |                       |
| $\Box$ Limits<br>□ 田<br><b>Trading Limits</b><br><b>ED</b> Product Groups<br>۰<br>$\Box$ $\Box$ FS Swaps<br><b>EX Spot</b><br>Đ.<br><b>7</b> Requester Limits<br><b>7 FS Swaps</b><br><b>7 FX Spot</b><br><b>Provider Limits</b><br><b>7 FS Swaps</b><br><b>FX Spot</b><br>7<br><b>7</b> Treasurer Limits<br><b>7 FS Swaps</b><br>в<br><b>7 FX Spot</b><br>n | <b>Provider</b>                               | Requester          | <b>Daily Trading Limit</b> | <b>Executed Trades</b> | <b>Pending Orders</b>      | <b>Used Amount</b>    |
|                                                                                                                                                                                                                                                                                                                                                              | * TOTAL LIMIT ALL COMPANI                     | <b>All Request</b> | $1,000,000 -$              | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>&amp; 360TBANK, TEST</b>                   | CompanyBZ          | 10.000                     | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>&amp; Barclays BARX.DEMO</b>               | CompanyBZ          | 100,000                    | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>BOAL.DEMO</b>                              | CompanyBZ          | 1,000,000                  | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>E CITIBANK.DEMO</b>                        | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>E COBA.DEMO</b>                            | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>E COBA.FRA DRESDNER.DEMO</b>               | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | Credit Suisse, DEMO                           | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>E HSBC.DEMO</b>                            | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b># RBS.LND.DEMO</b>                         | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>E</b> SEB.DEMO                             | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              | <b>&amp; Unicredit.DEMO</b>                   | CompanyBZ          | <b>UNLIMITED</b>           | 0.00                   | 0.00                       | 0.00                  |
|                                                                                                                                                                                                                                                                                                                                                              |                                               |                    |                            |                        |                            |                       |
|                                                                                                                                                                                                                                                                                                                                                              | $\blacktriangleleft$                          |                    |                            |                        |                            | $\blacktriangleright$ |
| discarding changes 29 ms - 17/05/17 08:47:37 GMT<br>Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ                                                                                                                                                                                                                                  |                                               |                    |                            |                        |                            |                       |

Figure 22 Defining provider trading limits per product group

The column Used Amount shows the current consumption of the company's daily trading limits. It is reset at the beginning of each trading day. The Used Amount is automatically reset to zero at 00:00:00 UTC every day.

The Used Amount column is updated on one hand with every executed deal and on the other hand with each placed order. The consumption related to executed trades and to pending orders is displayed each as a distinct value.

Executed trades are only taken into account on the day of their execution. Orders are taken into account every day anew, unless they are cancelled, withdrawn or expired. Once executed, an order is not pending anymore and therefore accounted in the Executed Trades amount.

If the value of a requested transaction would exceed a remaining limit with a given provider, then the system automatically excludes this provider from the request.

For customers with a "trade-as" setup, i.e. trading on behalf of different legal entities, the limits can be set up by provider for the main entity and/or for each legal entity separately. A tree view


<!-- PAGE 21 -->

opened from the node in front of each provider will automatically display all legal entities enabled for that particular bank in the customer relationship management functionality.

| BCT Business Configuration Tool - DEMO<br><b>Administration Help</b><br>System                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                          |                                                                                                                                                                                   |                                                                                                                                                                                                                                                                   |                                                                                                                        |                                                                                                                       | $\Box$ $\times$                                                                                                    |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------|
| <b>Counterpart Relationships</b><br><b>General</b>                                                                                                                                                                                                                                                                                                         | <b>Bank Baskets</b><br><b>Trading Limits*</b>                                                                                                                                                                                                                                                                                                            |                                                                                                                                                                                   |                                                                                                                                                                                                                                                                   |                                                                                                                        |                                                                                                                       |                                                                                                                    |
| <b>Save Reload Discard Changes</b>                                                                                                                                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                                                                                          |                                                                                                                                                                                   |                                                                                                                                                                                                                                                                   |                                                                                                                        |                                                                                                                       |                                                                                                                    |
| $\Box$ Limits<br>阳<br><b>Trading Limits</b><br>۰<br><b>ED</b> Product Groups<br>۵<br>$\Box$ $\Box$ FS Swaps<br><b>EX Spot</b><br><b>7</b> Requester Limits<br><b>7 FS Swaps</b><br>₹ FX Spot<br><b>7</b> Provider Limits<br><b>7 FS Swaps</b><br><b>FX Spot</b><br>7<br>a<br><b>7</b> Treasurer Limits<br><b>7 FS Swaps</b><br>D<br><b>7 FX Spot</b><br>в. | Provider<br><b>E</b> * TOTAL LIMIT ALL COMPANIES *<br><b>&amp; 360TBANK, TEST</b><br><b>Barclays BARX.DEMO</b><br><b>&amp; BOAL, DEMO</b><br><b>E CITIBANK, DEMO</b><br><b>&amp; COBA, DEMO</b><br><b>E COBA.FRA DRESDNER.DEMO</b><br>Credit Suisse.DEMO<br><b>&amp; HSBC.DEMO</b><br>● RBS.LND.DEMO<br><b>E</b> SEB.DEMO<br><b>&amp; Unicredit.DEMO</b> | Requester<br>All Requesters<br>CompanyBZ<br><b>CompanyBZ</b><br>CompanyBZ<br>CompanyBZ<br>CompanyBZ<br>CompanyBZ<br>CompanyBZ<br>CompanyBZ<br>CompanyBZ<br>CompanyBZ<br>CompanyBZ | <b>Daily Trading Limit</b><br>1,000,000.00<br><b>UNLIMITED</b><br>UNLIMITED -<br><b>UNLIMITED</b><br><b>UNLIMITED</b><br><b>UNLIMITED</b><br><b>UNLIMITED</b><br><b>UNLIMITED</b><br><b>UNLIMITED</b><br><b>UNLIMITED</b><br><b>UNLIMITED</b><br><b>UNLIMITED</b> | <b>Executed Trades</b><br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00 | <b>Pending Orders</b><br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00 | <b>Used Amount</b><br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00<br>0.00 |
|                                                                                                                                                                                                                                                                                                                                                            | $\blacksquare$                                                                                                                                                                                                                                                                                                                                           |                                                                                                                                                                                   |                                                                                                                                                                                                                                                                   |                                                                                                                        |                                                                                                                       | $\blacktriangleright$                                                                                              |
| discarding changes 29 ms - 17/05/17 08:47:37 GMT<br>Login as - 360T.Roman as User: CompanyBZ.TreasurerA, Company: CompanyBZ                                                                                                                                                                                                                                |                                                                                                                                                                                                                                                                                                                                                          |                                                                                                                                                                                   |                                                                                                                                                                                                                                                                   |                                                                                                                        |                                                                                                                       |                                                                                                                    |

Figure 23 Defining Provider trading limits by legal entity


<!-- PAGE 22 -->

# **6 CONTACTING 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

### **Middle East Asia Pacific**

# **United Arab Emirates**

*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

#### **EMEA Americas**

# **USA**

*360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756
