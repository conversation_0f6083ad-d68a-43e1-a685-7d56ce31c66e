# **USER GUIDE BRIDGE ADMINISTRATION**

![](_page_0_Picture_1.jpeg)

# **TEX MULTIDEALER TRADING SYSTEM**

USER GUIDE

BRIDGE ADMINISTRATION: REGULATORY DATA

© 360 TREASURY SYSTEMS AG, 2023 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION

| 1 |       | INTRODUCTION5                                                  |  |
|---|-------|----------------------------------------------------------------|--|
| 2 |       | GETTING STARTED<br>5                                           |  |
| 3 |       | COMPANY ADMIN CONTROL7                                         |  |
|   | 3.1   | COMPANY DETAILS<br>7                                           |  |
|   | 3.1.1 | Company Overview7                                              |  |
|   | 3.1.2 | Company MTF Details8                                           |  |
|   | 3.1.3 | Common Entity Details10                                        |  |
|   | 3.1.4 | Adding or Modifying LEI Details<br>11                          |  |
|   | 3.1.5 | LEI for Legal Entities12                                       |  |
|   | 3.2   | USER DETAILS<br>13                                             |  |
|   | 3.2.1 | Overview<br>13                                                 |  |
|   | 3.2.2 | Trader MTF Details13                                           |  |
|   | 3.2.3 | User Download and Upload Functionality<br>15                   |  |
|   | 3.2.4 | Download MTF Trader Details for EU MTF and Upload for UK MTF16 |  |
|   | 3.3   | AUTODEALER USER<br>18                                          |  |
|   | 3.4   | SECURITY AND DATA PROTECTION20                                 |  |
|   | 3.5   | DATA RETENTION PERIOD<br>20                                    |  |
| 4 |       | EXTERNAL MAPPING20                                             |  |
| 5 |       | CREATE AN EXTERNAL INDIVIDUAL<br>24                            |  |
| 6 |       | ANNEX30                                                        |  |
|   | 6.1   | NATIONAL CLIENT ID FOR UK<br>MTF30                             |  |
|   | 6.2   | NATIONAL CLIENT ID FOR EU<br>MTF32                             |  |
|   | 6.3   | CONCAT<br>FORMAT 34                                            |  |
| 7 |       | CONTACTING 360T35                                              |  |

## **TABLE OF FIGURES**

| Figure 1 Header Bar6                                                                    |    |
|-----------------------------------------------------------------------------------------|----|
| Figure 2 Bridge Administration: Homepage.<br>6                                          |    |
| Figure 3 Regulatory Data Administration: Start page7                                    |    |
| Figure 4 Regulatory section: Company Overview.<br>8                                     |    |
| Figure 5: Company MTF Details section9                                                  |    |
| Figure 6: Trading Capacity and Investment Decision10                                    |    |
| Figure 7 Regulatory Data Administration: Common tab<br>11                               |    |
| Figure 8 Bridge Institution Category: Company Details with LEI field11                  |    |
| Figure 9 Bridge Institution Category: Submission of a LEI Change Request12              |    |
| Figure 10 Bridge Institution Category: Approval of a LEI Change Request<br>12           |    |
| Figure 11: Trader Overview13                                                            |    |
| Figure 12 Regulatory Data Administration: MTF User Details14                            |    |
| Figure 13: Trader Overview Download/Upload feature<br>15                                |    |
| Figure 14: Trader Overview for EU MTF and UK MTF17                                      |    |
| Figure 15: Selection of venue<br>17                                                     |    |
| Figure 16: AutoDealer user18                                                            |    |
| Figure 17: Back2back Trading Capacities<br>19                                           |    |
| Figure 18: Algo IDs20                                                                   |    |
| Figure 19 360T MTF Identification of users behind API<br>21                             |    |
| Figure 20 Bridge Administration Homepage: External Mappings feature<br>21               |    |
| Figure 21 External Mapping: Create new configuration.<br>22                             |    |
| Figure 22 External Mapping: Adding external code<br>22                                  |    |
| Figure 23 External Mapping: Upload of external codes23                                  |    |
| Figure 24 External Mapping: Modification of external codes<br>24                        |    |
| Figure 25 Bridge Administration: Help Wizard.<br>25                                     |    |
| Figure 26 Bridge Administration: Help Wizard Step 1 -<br>Select an Institution.<br>26   |    |
| Figure 27 Bridge Administration: Help Wizard Step 2 –<br>Individual details.<br>27      |    |
| Figure 28 Bridge Administration: Help Wizard Step 2 –<br>Individual details completed28 |    |
| Figure 29 Bridge Administration: Help Wizard Step 3 –<br>Individual details overview.   | 29 |

## **TABLE OF TABLES**

| Table 1: Field description of entity details<br>10          |  |
|-------------------------------------------------------------|--|
| Table 2 Field description of user details15                 |  |
| Table 3: PII differences between EU and UK MTF<br>18        |  |
| Table 4: Field description of AutoDealer user details<br>19 |  |
| Table 5: Field description of External user details28       |  |

## <span id="page-4-0"></span>**1 INTRODUCTION**

360T operates two separate Multi-Lateral Trading Facilities (MTFs): "UK MTF" (MIC: G360) and "EU MTF" (MIC: 360T). The MTF activation process for new MTF participants is identical for both MTFs. Therefore, the term "360T MTF" is used throughout this document to refer to both MTFs interchangeably.

An MTF participant must enter certain Personally Identifiable Information (PII) of its users alongside static data into 360T's systems before being able to start trading on 360T MTF. 360T offers the tools "Company Admin Control", "External Mapping" and "Create External Individual" accessible via the Bridge Administration application for this purpose.

This manual outlines the how-to steps to enter PII and static data and describes how confidential data is guaranteed.

Please note: An operator of an MTF must collect the above-mentioned information to fulfill MiFID II reporting requirements. Therefore, trading on 360T MTF is not possible until the necessary data is entered into 360T's systems.

The Bridge Administration tool is available only to users who have regulatory administrator rights. As part of the onboarding process, a sub-set of administrator users to whom the tool should be accessible must be defined. These users are responsible for entering the information as described in the following chapters. Please contact *<EMAIL>* or your customer relationship manager to request permissions for the relevant administrator users.

MTF participants are enabled for 360T MTF by the 360T CAS team as part of the 360T MTF Onboarding process. The initial activation of individual users for 360T MTF can only be done by the regulatory data administrator of the institution as 360T has no access to the confidential personal data of individual users which is required for MTF activation. Per default, all entities and users are OTC enabled (off-MTF) for MiFID II relevant products: Forwards, Block, Swap, non-SEF NDF, non-SEF NDS and non-SEF Options.

## <span id="page-4-1"></span>**2 GETTING STARTED**

The Bridge Administration tool can be accessed via the menu option "Administration" in the screen header of the Bridge application. Several features may be visible in the left menu depending on user permissions.

|                                                                                                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                            | $\vee$ Preferences<br>$\vee$ Help<br>△ Administration                                                                                                                                                                                                                                                                    | $\bullet$ AA $-\bullet$ X |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|
| Change Password<br><b>Bridge Administration</b>                                                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                          | $\times$                  |
| Sell EUR<br><b>Buy EUR</b><br>$1.06$ 246<br>$1.06$ 252<br>Spot // 16.10.2023<br>1.06 27 920<br>1.06 28 170<br>2.920<br>2.970<br><b>1 Week</b> // 23.10.2023<br>1.06 37 963<br>1.06 38715<br>13363<br>13515<br>$\frac{1 \text{ Month}}{16.11.2023}$<br>1.0716100<br>1.0717300<br>91.500<br>92.000<br>6 Months // 16.04.2024 | Sell GBP<br><b>Buy GBP</b><br>1.23<br>1.23078<br>Spot // 16.10.2023<br>1.2308510<br>1.2307670<br>0.470<br>0.510<br>1 Week // 23.10.2023<br>1.2308849<br>$1.23$ 10 495<br>1.649<br>2.495<br>1 Month // 16.11.2023<br>1.23 2 1 500<br>1.2322800<br>14.400<br>15,000<br>6 Months // 16.04.2024 | Sell EUR<br><b>BUY EUR</b><br>$0.86$ 323<br>$0.86\overline{334}$<br>Spot // 16.10.2023<br>$0.86$ 35 280<br>0.8634110<br>2.010<br>2.080<br>1 Week // 23.10.2023<br>0.8641559<br>0.8643172<br>9.159<br>9.772<br>$\frac{1 \text{ Month}}{16.11.2023}$<br>0.8696100<br>0.8698000<br>63.800<br>64.600<br>6 Months // 16.04.2024 | Sell USD<br><b>Buy USD</b><br>0.90064<br>0.90078<br>Spot // 16.10.2023<br>0.8999820<br>0.9001270<br>$-6.580$<br>$-6.530$<br>1 Week // 23.10.2023<br>0.8976918<br>0.8978705<br>$-29.482$<br>$-29.095$<br>$\boxed{1$ Month // $16.11.2023$<br>0.8825200<br>0.8826500<br>$-181.500$<br>$-180.700$<br>6 Months // 16.04.2024 |                           |
| All (0)<br>Executed (0)<br>Cancelled (0)<br>Type<br>Product<br>Reference #<br>*<br>$\begin{array}{c c} \hline \textbf{0} & \textbf{0} & \textbf{0} \end{array}$                                                                                                                                                            | Rejected (0)<br>Done (0)<br>Expired (0)<br>Requester C Provider Co Requester A Notional A                                                                                                                                                                                                   | Q<br>∣,↓,<br>Quote                                                                                                                                                                                                                                                                                                         | Base Curren Quote Curre Effective Date Effective Pe Maturity Date Maturit                                                                                                                                                                                                                                                |                           |
| 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2                                                                                                                                                                                                                                                                                |                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                            | [EIII T] 08:20:45 GMT (Do, 12. Okt 2023, 10:20 MESZ) // Connected [FFM] ● // Mem: 25.0% of 580 MB GC:0.0%                                                                                                                                                                                                                |                           |

<span id="page-5-0"></span>Figure 1 Header Bar

The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools and actions for the user.

A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.

| <b>RFS REQUESTER</b>                              | <b>BRIDGE ADMINISTRATION</b><br>$+$ |                        | $\vee$ Administration<br>$\vee$ Preferences                                                               | $\vee$ Help                  | $\bullet$ AA $ \bullet$ X |  |  |  |
|---------------------------------------------------|-------------------------------------|------------------------|-----------------------------------------------------------------------------------------------------------|------------------------------|---------------------------|--|--|--|
| 合                                                 | Configurations                      |                        | Administration Start                                                                                      |                              |                           |  |  |  |
|                                                   |                                     |                        |                                                                                                           |                              |                           |  |  |  |
|                                                   | Institution<br><b>Actions</b>       | <b>Regulatory Data</b> | <b>External Mapping</b>                                                                                   | <b>Company Admin Control</b> |                           |  |  |  |
|                                                   |                                     |                        |                                                                                                           |                              |                           |  |  |  |
|                                                   | <b>Change Request</b>               | Wizards                |                                                                                                           |                              |                           |  |  |  |
|                                                   |                                     |                        |                                                                                                           |                              |                           |  |  |  |
| $\vec{\Omega}$<br>$\frac{1}{\sigma}$<br>$\bullet$ |                                     |                        |                                                                                                           |                              |                           |  |  |  |
| 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2       |                                     |                        | [EEECD T 08:23:09 GMT (Do, 12. Okt 2023, 10:23 MESZ) // Connected [FFM] ● // Mem: 42.0% of 580 MB GC:0.0% |                              |                           |  |  |  |

<span id="page-5-1"></span>Figure 2 Bridge Administration: Homepage.

## <span id="page-6-0"></span>**3 COMPANY ADMIN CONTROL**

The "Company Admin Control" quick link opens a navigation panel which contains an institution tree. The tree includes a list of trade-as, trade-on-behalf or I-TEX entities configured under the main entity.

The selection of the institutions is done by single-click within the institution tree which opens a new form/sheet with the available details of that entity. The selected item is shown next to the home icon.

|                                                        |                                                                                                                       |                                                                                                                                    |                              |     |  |  | $\vee$ Preferences $\vee$ Administration $\vee$ Help                                                    | $\parallel$ $\triangleright^1$ 0 AA - 0 X |  |  |
|--------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------|------------------------------|-----|--|--|---------------------------------------------------------------------------------------------------------|-------------------------------------------|--|--|
|                                                        |                                                                                                                       | <b>RFS REQUESTER</b>                                                                                                               | <b>BRIDGE ADMINISTRATION</b> | $+$ |  |  |                                                                                                         |                                           |  |  |
|                                                        | 合<br>$\mathcal{G}$<br>$\underline{\mathbb{F}}_{\mathbb{Q}}$<br>$\qquad \qquad \textcolor{red}{\textbf{a}}$<br>$\circ$ | Q<br>⋒<br>$\triangleright$ TradeAsG<br>TradeAsG.TAS.B1<br>TradeAsG.TAS.B2<br>$\frac{1}{11}$ TradeAsG.TAS.B3<br>m TradeAsG.TradeAsG | $\rightarrow$<br>k           |     |  |  |                                                                                                         |                                           |  |  |
| $\begin{array}{c c c c c c c c c c c c c c c c c c c $ |                                                                                                                       |                                                                                                                                    |                              |     |  |  |                                                                                                         |                                           |  |  |
|                                                        |                                                                                                                       | 1/ TradeAsG.TreasurerA, TradeAsG // QA2                                                                                            |                              |     |  |  | [EECD T08:37:13 GMT (Do, 12. Okt 2023, 10:37 MESZ) // Connected [FFM] ● // Mem: 28.0% of 580 MB GC:0.0% |                                           |  |  |

<span id="page-6-3"></span>Figure 3 Regulatory Data Administration: Start page.

In the search field , the user can type in an alphanumeric value to find the desired institution.

The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.

## <span id="page-6-1"></span>**3.1 Company Details**

#### <span id="page-6-2"></span>**3.1.1 Company Overview**

Selecting the main entity from the institution tree exposes several tabs including a "Regulatory" section.

| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                               | $+$                                                                   |                                                                             | $\vee$ Preferences             | $\vee$ Administration | $\sim$ Help           | ⊵'                              | $0$ AA - $0 \times$ |
|------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------|-----------------------------------------------------------------------------|--------------------------------|-----------------------|-----------------------|---------------------------------|---------------------|
| $\rightarrow$ )<br>$\overline{\left( $<br>Q<br>合<br>$\mathbf{\hat{m}}$ > TradeAsG                                                  | <b>Company Details</b><br><b>Users</b><br><b>UK MTF</b><br><b>OTC</b> | Regulatory<br>Daughter<br><b>Company Overview</b><br><b>Trader Overview</b> | <b>Order Management Groups</b> | F                     |                       |                                 | とむ                  |
| $\mathcal{G}$<br>TradeAsG.TAS.B1<br>$\underline{\hat{\mathbf{m}}}$ TradeAsG.TAS.B2<br>嘔<br>TradeAsG.TAS.B3<br>ft TradeAsG.TradeAsG | Name                                                                  | ΓQ                                                                          |                                | $\rightarrow$         | <b>UK MTF Enabled</b> |                                 |                     |
| $\Box$                                                                                                                             |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
| $\circ$                                                                                                                            |                                                                       | <b>TradeAsG</b><br>TradeAsG.TAS.B1                                          |                                |                       | true                  | $\approx$<br>$\bar{\hat{\phi}}$ |                     |
|                                                                                                                                    |                                                                       | TradeAsG.TAS.B2                                                             |                                |                       | true<br>true          | $\approx$                       |                     |
|                                                                                                                                    |                                                                       | TradeAsG.TAS.B3                                                             |                                |                       | true                  | $\vec{\sim}$                    |                     |
|                                                                                                                                    |                                                                       | TradeAsG.TradeAsG                                                           |                                |                       | false                 | $\phi$                          |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
| $\frac{\phi}{D}$                                                                                                                   |                                                                       |                                                                             |                                |                       |                       |                                 |                     |
|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       | <b>Discard all Changes</b>      | Save                |
| $\bigcirc$<br>$\bigoplus$                                                                                                          | TradeAsG $\times$                                                     |                                                                             |                                |                       |                       |                                 |                     |
| 1/ QA2 TradeAsG.TreasurerA, TradeAsG // QA2                                                                                        |                                                                       | Do, 1 ===================================                                   |                                |                       |                       |                                 |                     |

<span id="page-7-1"></span>Figure 4 Regulatory section: Company Overview.

In the "Company Overview" area within the "Regulatory" section, a list of trade-as and tradeon-behalf institutions are shown, alongside the information whether they are MTF enabled or not. In the example of [Figure 4,](#page-7-1) the contractual entity "TradeAsG" is a participant of UK MTF. Therefore, its admin users can only view the details of the UK MTF. Participants of EU MTF or SEF see the details related to EU MTF or SEF, respectively.

For record keeping purposes, the admin user can download this list of entities and their corresponding "MTF Enabled" status as a CSV file via the button.

**Important:** Static data **must** be configured for the *main entity*. Click on the arrow button of the main entity to reach the section "Company MTF Details" where the necessary configuration must be done, refer to next chapter.

#### <span id="page-7-0"></span>**3.1.2 Company MTF Details**

In the "Company MTF Details" section, the "Trading Capacity" as well as the "Investment decision within firm" must be set.

|                                                                                                                                                                                                                                                         | $\odot$<br>AA<br>$-$ 0 $\times$<br>$\vee$ Preferences<br>$\vee$ Administration<br>$\vee$ Help<br>⊳                                                                                                                                                                                                                                                                                                                    |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                                                                                                                                                    | $+$                                                                                                                                                                                                                                                                                                                                                                                                                   |
| $\rightarrow$<br>Q<br>$\overline{\left(}$<br>合<br>$\hat{\mathbf{m}}$ > TradeAsG<br>$\mathcal{G}$<br>TradeAsG.TAS.B1<br>$\underline{\hat{\mathbf{m}}}$ TradeAsG.TAS.B2<br>甄<br>血 TradeAsG.TAS.B3<br>fft TradeAsG.TradeAsG<br>$\mathbf \Theta$<br>$\circ$ | <b>Company Details</b><br>Regulatory<br><b>Order Management Groups</b><br><b>Users</b><br><b>Daughter</b><br>₽<br><b>UK MTF</b><br><b>Company Overview</b><br><b>Trader Overview</b><br><b>OTC</b><br><b>Company MTF Details</b><br><b>Trader MTF Details</b><br><b>Common</b><br>TradeAsG<br><b>Company Name</b><br>$\sqrt{\bullet}$<br><b>MTF Enabled</b><br><b>AOTC</b><br><b>Trading Capacity</b><br>$\checkmark$ |
| 0 <br>Q<br>$\ominus$                                                                                                                                                                                                                                    | <b>Investment Decision</b><br><b>Invest decision within firm</b><br><b>NONE</b><br>$\checkmark$<br><b>Defined Individual</b><br>$\checkmark$<br><b>Discard all Changes</b><br>Save<br>TradeAsG $\times$                                                                                                                                                                                                               |
| 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2                                                                                                                                                                                                             | Do, 1 <b>ELECT DE 55 GMT</b> (Do, 12. Okt 2023, 14:33 MESZ) // Connected [FFM] ● // Mem: 29.0% of 580 MB GC:0.0%                                                                                                                                                                                                                                                                                                      |
|                                                                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                                                                                                                                                       |

<span id="page-8-0"></span>Figure 5: Company MTF Details section

The table below explains the meaning of the different drop-down fields.

| No. | Field Name                            | Reference                                      | Details                                                                                                                                                                                                                                      |  |  |  |  |
|-----|---------------------------------------|------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|
| 1   | Trading<br>Capacity                   | RTS 22, Annex                                  | Drop-down menu consists of the following three values:                                                                                                                                                                                       |  |  |  |  |
|     |                                       | I,<br>Table<br>2,<br>Field 28                  | - 'DEAL' – dealing on own account                                                                                                                                                                                                            |  |  |  |  |
|     |                                       |                                                | - 'MTCH' – matched principal trading: a transaction where three<br>conditions must be met:                                                                                                                                                   |  |  |  |  |
|     |                                       | RTS<br>24,<br>Annex,<br>Table<br>2, Field 7    | - The facilitator interposes between buyer and seller to the<br>transaction in such a way that it is never exposed to market<br>risk                                                                                                         |  |  |  |  |
|     |                                       |                                                | – Both sides are executed simultaneously (timing)                                                                                                                                                                                            |  |  |  |  |
|     |                                       |                                                | – The transaction is executed at a price where the facilitator<br>makes no profit or loss, other than a previously disclosed<br>commission, fee or charge of the transaction.                                                                |  |  |  |  |
|     |                                       |                                                | - 'AOTC' – any other capacity                                                                                                                                                                                                                |  |  |  |  |
| 2   | Investment<br>decision within<br>firm | RTS 22, Annex<br>I,<br>Table<br>2,<br>Field 57 | This field is only applicable if the Trading Capacity "DEAL" is used.                                                                                                                                                                        |  |  |  |  |
|     |                                       |                                                | It offers two different options:                                                                                                                                                                                                             |  |  |  |  |
|     |                                       |                                                | -<br>TRADER: The Investment Decision Maker is always the<br>trader executing the transaction<br>-<br>DEFINED_USER: The Investment Decision Maker is a<br>defined user which can be selected from the "Defined<br>individual" drop down menu. |  |  |  |  |
|     |                                       |                                                | When the Trading Capacity "AOTC" or "MTCH" is used, this field can<br>be set to NONE.                                                                                                                                                        |  |  |  |  |
| 3   | Defined<br>individual                 | RTS 22, Annex<br>I,<br>Table<br>2,<br>Field 57 | This field is only applicable if the option "DEFINED_USER" was<br>selected from the field "Investment decision within firm". Select a<br>defined physical person who is considered the Investment Decision<br>Maker for 360T MTF trades.     |  |  |  |  |

<span id="page-9-2"></span>Table 1: Field description of entity details

The configured fields are shown as default values in the MiFID tab of the different 360T trading applications, refer to [Figure 6.](#page-9-1) It is possible for the trader to overwrite the default values.

|                                          | • Product Definition Competitive Bidding                                                                                                                                                                                                    | ×                                        |
|------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------|
|                                          | <b>FX Forward</b>                                                                                                                                                                                                                           |                                          |
|                                          | <b>Trade As</b><br>TradeAsG.TAS.B1<br>Sell<br>$EUR \vee$<br>Notional<br>٠<br>⊜<br>USD $\vee$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br><b>Buy</b><br><b>Effective Date</b><br>雦<br>1 Week<br>Tue, 24.10.2023<br>$\checkmark$<br>7 Days |                                          |
| Provider List Transaction                | Comments<br>MIFID                                                                                                                                                                                                                           |                                          |
| <b>Trading Venue</b>                     | <b>UK_MTF</b>                                                                                                                                                                                                                               |                                          |
| <b>Trading Capacity</b>                  | <b>DEAL</b>                                                                                                                                                                                                                                 |                                          |
| <b>Investment Decision</b>               | TradeAsG.TreasurerA                                                                                                                                                                                                                         |                                          |
| <b>Indicative Value</b><br>1.05<br>L 420 | <b>Request Timeout</b><br>$\bigodot$ 01:00 $\bigodot$                                                                                                                                                                                       | Regulatory Disclosures<br>Send<br>Cancel |

<span id="page-9-1"></span>Figure 6: Trading Capacity and Investment Decision

#### <span id="page-9-0"></span>**3.1.3 Common Entity Details**

The Common tab shows two read-only fields: Investment Firm and LEI.

The Investment Firm flag indicates whether the entity is an investment firm covered by MIFID II Directive (RTS 22, Annex I, Table 2, Field 6). 360T MTF has a transaction reporting obligation if the MTF participant is not an investment firm (e.g., a corporation). 360T uses this field to determine whether it has a transaction reporting obligation in relation to its capacity as an MTF.

The Legal Entity Identifier (LEI) is a 20-character, alpha-numeric code based on the ISO 17442 standard developed by the International Organization for Standardization (ISO). It relates to key reference information that enables clear and unique identification of legal entities participating in financial transactions. The LEI must be provided for each entity which should be enabled for MTF. The following chapter describes how a new LEI can be added to the configuration.

|                                                                      |                                                                             | <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b>                                                                                                                                    | $+$                                                                        |                                                                                                             |                                                                                                                   |                       |                      | $\vee$ Preferences $\vee$ Administration $\vee$ Help                                                          | ∾                          | $\bullet$ AA - $\bullet$ X |
|----------------------------------------------------------------------|-----------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------|-----------------------|----------------------|---------------------------------------------------------------------------------------------------------------|----------------------------|----------------------------|
| $\begin{array}{c c} \circ & \circ \\ \circ & \circ \end{array}$<br>ø | 合<br>$\mathcal{G}$<br>嘔<br>$\qquad \qquad \text{ } \blacksquare$<br>$\circ$ | $\rightarrow$ $\mid$ <<br>ŗα,<br>$\mathbf{\hat{m}}$ > TradeAsG<br>fft TradeAsG.TAS.B1<br>$\underline{\widehat{\mathbf{m}}}$ TradeAsG.TAS.B2<br>TradeAsG.TAS.B3<br>fft TradeAsG.TradeAsG | <b>Company Details</b><br><b>OTC</b><br><b>UK MTF</b><br>TradeAsG $\times$ | <b>Users</b><br><b>Daughter</b><br><b>Company Overview</b><br>Company MTF Details Trader MTF Details Common | <b>Regulatory</b><br><b>Order Management Groups</b><br><b>Trader Overview</b><br><b>UK Investment Firm</b><br>LEI | $\equiv$<br>$\bullet$ | 22345678901234567871 |                                                                                                               | <b>Discard all Changes</b> | <b>Save</b>                |
|                                                                      |                                                                             | TradeAsG.TreasurerA, TradeAsG // QA2                                                                                                                                                    |                                                                            |                                                                                                             |                                                                                                                   |                       |                      | 3. Okt 2023, 12:42:27 GMT (Fr, 13. Okt 2023, 14:42 MESZ) // Connected [FFM] . // Mem: 52.0% of 580 MB GC:0.0% |                            |                            |

<span id="page-10-1"></span>Figure 7 Regulatory Data Administration: Common tab

#### <span id="page-10-0"></span>**3.1.4 Adding or Modifying LEI Details**

A LEI (Legal Entity Identifier) must be added to Company Details tab under the Institution category in Bridge Administration. The respective field "LEI" can be found under Company Details in the Overview tab.

| <b>Company Details</b> |                 | Users (3) | <b>Deal Tracking Groups (1)</b> | Daughter (3) | <b>Legal Entities</b> | <b>Entity Groups (1)</b> | <b>TAS/TOB Groups</b>              |  |
|------------------------|-----------------|-----------|---------------------------------|--------------|-----------------------|--------------------------|------------------------------------|--|
|                        |                 |           |                                 |              |                       |                          |                                    |  |
| <b>Overview</b>        | <b>Prefixes</b> |           | Internal trades only            |              |                       |                          |                                    |  |
|                        |                 |           |                                 |              |                       |                          |                                    |  |
|                        |                 |           |                                 |              | Company Name *        |                          | TradeForE                          |  |
|                        |                 |           |                                 |              | Description           |                          |                                    |  |
|                        |                 |           |                                 |              | Phone Number          |                          |                                    |  |
|                        |                 |           |                                 |              | Fax Number            |                          |                                    |  |
|                        |                 |           |                                 |              | Country *             |                          | Germany<br>$\checkmark$            |  |
|                        |                 |           |                                 |              | US Person *           |                          | O Disabled                         |  |
|                        |                 |           |                                 |              | Currency              |                          | EUR<br>$\checkmark$                |  |
|                        |                 |           |                                 |              |                       |                          |                                    |  |
|                        |                 |           |                                 | LEI          |                       |                          |                                    |  |
|                        |                 |           |                                 |              | Status *              |                          | Institution active<br>$\checkmark$ |  |
|                        |                 |           |                                 |              | Provider Role         |                          | O Disabled                         |  |
|                        |                 |           |                                 |              | <b>Requestor Role</b> |                          | $\vee$ Enabled                     |  |
|                        |                 |           |                                 |              |                       |                          |                                    |  |
|                        |                 |           |                                 |              | Prime Broker          |                          | Disabled<br>$\bullet$              |  |
|                        |                 |           |                                 |              | High Frequency Trader |                          | Disabled<br>$\bullet$              |  |
|                        |                 |           |                                 |              |                       |                          |                                    |  |
|                        |                 |           |                                 |              |                       |                          |                                    |  |
|                        |                 |           |                                 |              |                       |                          |                                    |  |

<span id="page-10-2"></span>Figure 8 Bridge Institution Category: Company Details with LEI field

Entering or modifying a LEI requires submission of a change request (button "Create Change Request").

| Company Details<br>Deal Tracking Groups (1)<br>Users (3)<br>Daughter (3) | <b>Legal Entities</b><br>Entity Groups (1) | <b>TAS/TOB Groups</b>          | $9 \circ \equiv$            |
|--------------------------------------------------------------------------|--------------------------------------------|--------------------------------|-----------------------------|
| Overview<br>Prefixes<br>Internal trades only                             |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          | Company Name *                             | TradeForE                      |                             |
|                                                                          | Description                                |                                |                             |
|                                                                          | Phone Number                               |                                |                             |
|                                                                          | Fax Number                                 | ٠                              |                             |
|                                                                          | Country *                                  | $\bigtriangledown$<br>Cermany  |                             |
|                                                                          | US Person *                                | $\bullet$ 0 Disabled           |                             |
|                                                                          | Currency                                   | EUR<br>$\vee$                  |                             |
|                                                                          | LEI                                        | 529900P0204W9HA8JP36           |                             |
|                                                                          | Status *                                   | Institution active<br>$\vee$ ) |                             |
|                                                                          | <b>Provider Role</b>                       | O Disabled                     |                             |
|                                                                          | <b>Requestor Role</b>                      | V Enabled                      |                             |
|                                                                          | Prime Broker                               | O Disabled                     |                             |
|                                                                          | High Frequency Trader                      | · Disabled                     |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
|                                                                          |                                            |                                |                             |
| Create Change Request                                                    |                                            |                                | Discard All Changes<br>save |
|                                                                          |                                            |                                |                             |

<span id="page-11-1"></span>Figure 9 Bridge Institution Category: Submission of a LEI Change Request

Once the Change Request has been submitted, it must be approved by a Supervisor or Super Administrator in the Change Request tool in Bridge Administration.

| <b>Change Requests</b>            | <b>HTML Change Requests</b>  |                | Change Requests (1/1) |                     |                                      |               | <b>O</b> Show all                   |
|-----------------------------------|------------------------------|----------------|-----------------------|---------------------|--------------------------------------|---------------|-------------------------------------|
| Select Category<br>Solott Company | $\checkmark$<br>$\checkmark$ |                |                       |                     |                                      |               | $\qquad \qquad \Longleftrightarrow$ |
| Filter by CR id                   | Institution                  | <b>Comment</b> |                       | Submitted at        |                                      | <b>Status</b> |                                     |
| M<br>$CR - 7327$                  | TradeForE                    |                |                       | 05.12.2023 14:25:51 | Submitted by<br>TradeForE.TreasurerA | 0/1           | $\circ \circ (\vee) \circ \vDash$   |
|                                   |                              |                |                       |                     |                                      |               |                                     |

<span id="page-11-2"></span>Figure 10 Bridge Institution Category: Approval of a LEI Change Request

This approved change request will be automatically submitted with 360T CAS for final review and approval. 360T will confirm the application of the change in writing. The LEI details appear under the Company Details.

#### <span id="page-11-0"></span>**3.1.5 LEI for Legal Entities**

Depending on the setup, the Legal Entities tab may or may not be visible. To add an LEI for a Legal Entity, please complete the above process for each individual entity.

For updates on multiple entities via bulk upload, please contact the CAS team at [<EMAIL>](mailto:<EMAIL>)

## <span id="page-12-0"></span>**3.2 User details**

#### <span id="page-12-1"></span>**3.2.1 Overview**

Similarly to "Company Overview" tab, "Trader Overview" shows which users are enabled for a regulated trading venue (e.g. UK MTF).

|                         |                                                                         |                                           |                        |                                 | $\vee$ Preferences<br>$\vee$ Administration                                                                                                                                                                | $\mathcal{D}^1$<br>$\vee$ Help | $\bullet$ AA - $\bullet$ X |
|-------------------------|-------------------------------------------------------------------------|-------------------------------------------|------------------------|---------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|----------------------------|
|                         | <b>RFS REQUESTER</b>                                                    | <b>BRIDGE ADMINISTRATION</b>              | $+$                    |                                 |                                                                                                                                                                                                            |                                |                            |
|                         | (Q                                                                      | $\rightarrow$<br>$\overline{\phantom{a}}$ | <b>Company Details</b> | <b>Daughter</b><br><b>Users</b> | <b>Order Management Groups</b><br><b>Regulatory</b>                                                                                                                                                        | $\equiv$                       | 土工                         |
| 合                       | $\hat{\mathbf{m}}$ > TradeAsG                                           | <b>OTC</b>                                | <b>UK MTF</b>          | <b>Company Overview</b>         | <b>Trader Overview</b>                                                                                                                                                                                     |                                |                            |
| $\mathcal{G}$           | $\underline{\widehat{\mathbf{m}}}$ TradeAsG.TAS.B1                      |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
| 甄                       | m TradeAsG.TAS.B2<br>$\underline{\widehat{\mathbf{m}}}$ TradeAsG.TAS.B3 |                                           |                        |                                 | $\Omega$                                                                                                                                                                                                   | $\rightarrow$                  |                            |
|                         | m TradeAsG.TradeAsG                                                     |                                           |                        | <b>Name</b>                     |                                                                                                                                                                                                            | <b>UK MTF Enabled</b>          |                            |
| $\ominus$               |                                                                         |                                           |                        | TradeAsG.TreasurerA             |                                                                                                                                                                                                            | $\beta$<br>false               |                            |
| $\circ$                 |                                                                         |                                           |                        | TradeAsG.TreasurerB             |                                                                                                                                                                                                            | $\beta$<br>false               |                            |
|                         |                                                                         |                                           |                        | TradeAsG.TreasurerC             |                                                                                                                                                                                                            | $\beta$<br>false               |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |
| $\frac{1}{2}$           |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            | <b>Discard all Changes</b>     | Save                       |
| $\bigcirc$<br>$\ominus$ |                                                                         | TradeAsG $\times$                         |                        |                                 |                                                                                                                                                                                                            |                                |                            |
|                         | 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2                             |                                           |                        |                                 | Fr, 13. Okt <a>E<br/> T<br/> MT (Fr, 13. Okt 2023, 06:12 MESZ) // Connected [FFM] <a> (FM] <a> (Mem: 30.0% of 580 MB GC:0.0%</a> (FC:0.0%</a> (FC:0.0%</a> (FC:0.0% (FC:0.0% (FC:0.0% (FC:0.0% (FC:0.0% (F |                                |                            |

<span id="page-12-3"></span>Figure 11: Trader Overview

To enable a user for MTF trading, click the arrow button . This will open the "Trader MTF Details" for the chosen user. Please note this process must be completed for all individual and AutoDealer/API users who require MTF trading.

### <span id="page-12-2"></span>**3.2.2 Trader MTF Details**

Admin users of 360T MTF participants are required to activate the individual users for 360T MTF and enter their personal details. Please refer to [Figure 12](#page-13-0) and the corresponding field descriptions in [Table 2](#page-14-2) for configuration of a physical user.

| <b>Company Details</b><br><b>Daughter</b><br><b>Regulatory</b><br>$\equiv$<br><b>Users</b><br>$\rightarrow$ $\kappa$<br>Q<br>合<br><b>UK MTF</b><br><b>Company Overview</b><br><b>OTC</b><br><b>Trader Overview</b><br>$\mathbf{\hat{m}}$ > TradeAsG<br>$\mathcal{G}$<br>fil TradeAsG.TAS.B1<br><b>Company MTF Details</b><br><b>Trader MTF Details</b><br>$\hat{\mathbb{m}}$ TradeAsG.TAS.B2<br><b>Common</b><br><b>ID</b><br>$\underline{\widehat{\mathbf{m}}}$ TradeAsG.TAS.B3<br><b>Trader MTF Overview</b><br>fil TradeAsG.TradeAsG<br>$\Box$<br>TradeAsG.TreasurerA<br><b>User Name</b><br>$\circledcirc$<br>$\sqrt{\bullet}$<br><b>MTF Enabled</b><br>First Name*<br>Mack<br>Last Name*<br>John<br>Nationality *<br><b>Czech Republic</b><br><b>Country of Branch</b><br>Germany<br><b>National Client ID</b><br>7360285163<br><b>National Identification Number</b><br><b>Passport Number</b><br><b>CONCAT</b><br>$\ddot{\mathbf{r}}$<br>D<br><b>Discard all Changes</b><br>Save | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b> | $\odot$<br>$AA - CD X$<br>$\vee$ Preferences<br>$\vee$ Administration<br>$\vee$ Help<br>$+$ |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|---------------------------------------------------------------------------------------------|
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                                      |                                                                                             |
| TradeAsG $\times$<br>$\ominus$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | $\bigcirc$                                           |                                                                                             |

<span id="page-13-0"></span>Figure 12 Regulatory Data Administration: MTF User Details

| No. | Field Name               | Reference                                                                                     | Details                                                                                                                                                                                                                                                                                                               |
|-----|--------------------------|-----------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1   | First Name               |                                                                                               | The first name is set up as part of the user on-boarding process by<br>360T CAS. This value is NOT editable and shown for reference<br>purposes only.                                                                                                                                                                 |
| 2   | Last Name                |                                                                                               | The last name is set up as part of the user on-boarding process by<br>360T CAS. This value is NOT editable and shown for reference<br>purposes only.                                                                                                                                                                  |
| 3   | Nationality              | RTS 22,<br>Annex I, Table<br>2, Field 57 &<br>59<br>RTS 24,<br>Annex, Table<br>2, Field 4 & 5 | Nationality of the user                                                                                                                                                                                                                                                                                               |
| 4   | Country of the<br>Branch | RTS 22,<br>Annex I, Table<br>2, Field 60                                                      | Field used to identify the country of the branch for the user. If a user<br>is not supervised by a branch, this field should be populated with the<br>country code of the home Member State of the firm OR the country<br>code of the country where the firm has established its head office or<br>registered office. |
| 5   | National Client<br>ID    | RTS 22,<br>Annex I, Table<br>2, Field 57 &<br>59                                              | 360T, as a trading platform, is required to collect the National Client<br>ID of users.<br>The fields shown in the configuration tool are derived from the<br>nationality of the user as defined in the Annex. The identifier must be<br>assigned in accordance with the priority levels in the Annex. When           |

|  | RTS 24,<br>Annex, Table<br>2, Field 4 & 5 | the configuration tool offers several entry fields for the National Client<br>ID then the first field must be used to define the identifier. If the person<br>does not have the first priority identifier, the second priority identifier<br>should be used, and so forth. |
|--|-------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|  |                                           | E.g. 1: idm A is a citizen of Denmark with a personal identity code<br>"0707781234", where the first 6 numbers represent the date of birth<br>in "DDMMYY" format and the last 4 numbers a sequence number.                                                                 |
|  |                                           | In this case, idm A would need to enter 0707781234 in the<br>configuration as the first priority identifier.                                                                                                                                                               |
|  |                                           | E.g. 2: idm B is a citizen of the United Kingdom but does not have a<br>UK National Insurance number.                                                                                                                                                                      |
|  |                                           | In this case, idm B would need to provide the concatenation<br>(CONCAT) of the birthday, first name and second name in the format<br>as specified in RTS 22, Article 6. The length of the CONCAT string<br>must be 18 characters, refer to 0.                              |
|  |                                           | For example, 19800512JOHN#KING#                                                                                                                                                                                                                                            |
|  |                                           | Note: There are slight differences between the National Client IDs<br>that are collected for the EU MTF and UK MTF as outlined in the<br>Annex or Table 3.                                                                                                                 |

<span id="page-14-2"></span>Table 2 Field description of user details

#### <span id="page-14-0"></span>**3.2.3 User Download and Upload Functionality**

Enabling each individual physical user one by one for 360T MTF as described in the previous chapters may be time consuming. The "Trader Overview" section offers a download and upload feature to facilitate a simplified user activation process for setups with many users.

|                           |                                       |                                            |                              |                        |                     | $\vee$ Preferences |                         | $\vee$ Administration                                           | $\vee$ Help | $\mathcal{D}^1$            | $\odot$   | $AA - Q \times$ |
|---------------------------|---------------------------------------|--------------------------------------------|------------------------------|------------------------|---------------------|--------------------|-------------------------|-----------------------------------------------------------------|-------------|----------------------------|-----------|-----------------|
|                           |                                       | <b>RFS REQUESTER</b>                       | <b>BRIDGE ADMINISTRATION</b> |                        | $+$                 |                    |                         |                                                                 |             |                            |           |                 |
|                           |                                       | Q                                          | $\rightarrow$ )              | <b>Company Details</b> |                     | <b>Users</b>       | <b>Daughter</b>         | <b>Regulatory</b>                                               | $\gg$ =     |                            |           | とむ              |
|                           | 合                                     | $\hat{\mathbf{a}}$ > TradeAsG              |                              | <b>OTC</b>             | <b>UK MTF</b>       |                    | <b>Company Overview</b> | <b>Trader Overview</b>                                          |             |                            |           |                 |
|                           | $\mathcal{G}$                         | m TradeAsG.TAS.B1                          |                              |                        |                     |                    |                         |                                                                 |             |                            |           |                 |
|                           | 蚽                                     | fft TradeAsG.TAS.B2<br>fil TradeAsG.TAS.B3 |                              |                        |                     | Q                  |                         |                                                                 |             | $\rightarrow$              |           |                 |
|                           | film TradeAsG.TradeAsG<br>$\bigoplus$ |                                            |                              | <b>Name</b>            |                     |                    |                         |                                                                 |             | <b>UK MTF Enabled</b>      |           |                 |
|                           |                                       |                                            |                              |                        | TradeAsG.TreasurerA |                    |                         |                                                                 |             |                            | true      | $\beta$         |
|                           | $\circ$                               |                                            |                              |                        | TradeAsG.TreasurerB |                    |                         |                                                                 |             | false                      | $\approx$ |                 |
|                           |                                       |                                            |                              |                        | TradeAsG.TreasurerC |                    |                         |                                                                 |             |                            | false     | $\beta$         |
| ☆                         |                                       |                                            |                              |                        |                     |                    |                         |                                                                 |             |                            |           |                 |
| $\frac{1}{\overline{10}}$ |                                       |                                            |                              |                        |                     |                    |                         |                                                                 |             | <b>Discard all Changes</b> |           | Save            |
| $\ominus$                 |                                       |                                            |                              | TradeAsG $\times$      |                     |                    |                         |                                                                 |             |                            |           |                 |
| $^{\circ}$                |                                       | TradeAsG.TreasurerA, TradeAsG // QA2       |                              |                        |                     |                    |                         | Sa, 14. Okt 2023, 04:38:17 ==================================== |             |                            |           |                 |

<span id="page-14-1"></span>Figure 13: Trader Overview Download/Upload feature

The list of users can be downloaded as a CSV file via the download button. It consists of following columns:

- Trader Name
- First Name
- Last Name
- Nationality Country of Branch
- National Client ID 1

- National Client ID2
- National Client ID 3
- User Role
- MTF Enabled

Example of the content of the downloaded file:

Trader Name,First Name,Last Name,Nationality,Country of Branch,National Client ID 1,National Client ID 2,National Client ID 3,User Role,MTF Enabled

TradeAsG.TreasurerA,John,Mack,CZ,DE,7360285163,,,Treasurer,true

TradeAsG.TreasurerB,Silvia,Jones,,,,,,Treasurer,false

TradeAsG.TreasurerC,Eduardo,Rodriges,,,,,,Treasurer,false

Following information must be adjusted in the downloaded CSV file before uploading it again for 360T MTF user activation:

- Nationality Country of Branch
- National Client ID 1 (if not existent National Client ID 2 etc.)
- MTF Enabled = true

Example:

Trader Name,First Name,Last Name,Nationality,Country of Branch,National Client ID 1,National Client ID 2,National Client ID 3,User Role,MTF Enabled

TradeAsG.TreasurerB,Silvia,Jones,GB,GB,AB123456D,,,Treasurer,false

TradeAsG.TreasurerC,Eduardo,Rodriges,ES,GB, X2482300W,,,Treasurer,false

Press the upload button and then select the adjusted CSV to activate the given users. Subsequently, a response file can be stored on the local machine which shows whether all records were uploaded successfully.

#### <span id="page-15-0"></span>**3.2.4 Download MTF Trader Details for EU MTF and Upload for UK MTF**

EU MTF Participants who must migrate to UK MTF have to *re-enter* PII trader details for UK MTF. To make this process easier, it is possible to download the PII data of the existing EU MTF users as CSV file and make the necessary adjustments before uploading the file for UK MTF. This process is described further in this sub-chapter.

Admin users who have access to EU MTF and UK MTF can see in the "Trader Overview" section the MTF Enabled status of users for EU MTF and UK MTF.

|                   |               |                                                      |                                                                                                                      |             | $\vee$ Preferences     |               | $\vee$ Administration   | $\vee$ Help | $\mathcal{D}^1$            | $\odot$           | $AA - CD X$           |                   |
|-------------------|---------------|------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------|-------------|------------------------|---------------|-------------------------|-------------|----------------------------|-------------------|-----------------------|-------------------|
|                   |               | <b>RFS REQUESTER</b>                                 | <b>BRIDGE ADMINISTRATION</b>                                                                                         |             | $+$                    |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   | 合             | Q                                                    | $\rightarrow$<br>$\overline{\left(}$                                                                                 |             | <b>Company Details</b> | <b>Users</b>  | <b>Daughter</b>         | Regulatory  | $\gg$ =                    |                   |                       | とむ                |
|                   |               | $\hat{\mathbf{a}}$ > TradeAsG                        |                                                                                                                      | <b>OTC</b>  | <b>EU MTF</b>          | <b>UK MTF</b> | <b>Company Overview</b> |             | <b>Trader Overview</b>     |                   |                       |                   |
|                   | $\mathcal{G}$ | $\underline{\widehat{\mathbf{m}}}$ TradeAsG.TAS.B1   |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               | fil TradeAsG.TAS.B2                                  |                                                                                                                      |             |                        | Q             |                         |             | $\rightarrow$              |                   |                       |                   |
|                   | 鹀             | fil TradeAsG.TAS.B3                                  |                                                                                                                      | <b>Name</b> |                        |               |                         |             | <b>EU MTF Enabled</b>      |                   | <b>UK MTF Enabled</b> |                   |
|                   | $\mathbf{r}$  | $\underline{\widehat{\mathbf{m}}}$ TradeAsG.TradeAsG |                                                                                                                      |             | TradeAsG.TreasurerA    |               |                         |             |                            | $\leftrightarrow$ | false                 | $\leftrightarrow$ |
|                   | $\circ$       |                                                      |                                                                                                                      |             | TradeAsG.TreasurerB    |               |                         |             | true<br>true               | $\leftrightarrow$ | false                 | $\beta$           |
|                   |               |                                                      |                                                                                                                      |             | TradeAsG.TreasurerC    |               |                         | true        | $\triangleright$           | false             | Þ,                    |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
| ☆<br>D            |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |
| $\bigcirc$        |               |                                                      |                                                                                                                      |             |                        |               |                         |             | <b>Discard all Changes</b> |                   |                       | Save              |
| $\leftrightarrow$ |               |                                                      |                                                                                                                      |             | TradeAsG $\times$      |               |                         |             |                            |                   |                       |                   |
|                   |               |                                                      | 16:10 MES2) // Connected [FFM] ● // Mem: 53.0% of 580 MB GC:0.0% // Connected [FM] ● // Mem: 53.0% of 580 MB GC:0.0% |             |                        |               |                         |             |                            |                   |                       |                   |

<span id="page-16-0"></span>Figure 14: Trader Overview for EU MTF and UK MTF

When pressing on the download button, a popup appears and requests to select the desired venue:

| <b>Export CSV</b>                                      |
|--------------------------------------------------------|
| Please select venue to export                          |
|                                                        |
| <b>EU MTF</b><br><b>UK MTF</b><br>Cancel<br><b>SEF</b> |

<span id="page-16-1"></span>![](_page_16_Figure_6.jpeg)

The "EU MTF" option must be selected to download the EU MTF related PII data of users.

Note: There are differences between the PII data that are collected for EU MTF and UK MTF, please refer to the table below:

| Nationality   | National Client Identifiers for<br>EU MTF                                                      | National Client Identifiers for UK<br>MTF          |  |  |  |  |
|---------------|------------------------------------------------------------------------------------------------|----------------------------------------------------|--|--|--|--|
| UK            | 1.<br>Passport Number<br>2.<br>CONCAT                                                          | 1.<br>UK National Insurance Number<br>2.<br>CONCAT |  |  |  |  |
| Liechtenstein | 1.<br>National Passport Number<br>2.<br>National<br>Identity<br>Card<br>Number<br>3.<br>CONCAT | 1.<br>CONCAT                                       |  |  |  |  |

<span id="page-17-2"></span>Table 3: PII differences between EU and UK MTF

Therefore, National Client Identifiers for users with the nationality "UK" or "Liechtenstein" must be adjusted in the downloaded file.

Once done, press the upload button, select "UK MTF" and select the file to be uploaded.

### <span id="page-17-0"></span>**3.3 AutoDealer User**

In case an AutoDealer is used for MTF trading, the AutoDealer user must be MTF-enabled. Refer to [Figure 16,](#page-17-1) [Figure 17](#page-18-0) and the corresponding field descriptions in [Table 4.](#page-18-1)

|  |  |  | 1 |  |
|--|--|--|---|--|
|  |  |  |   |  |
|  |  |  |   |  |
|  |  |  |   |  |
|  |  |  |   |  |
|  |  |  |   |  |
|  |  |  |   |  |
|  |  |  |   |  |
|  |  |  |   |  |
|  |  |  |   |  |

<span id="page-17-1"></span>Figure 16: AutoDealer user

|  |  |  |  |  |  |  | 2 |  |
|--|--|--|--|--|--|--|---|--|
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  | 3 |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |
|  |  |  |  |  |  |  |   |  |

<span id="page-18-0"></span>Figure 17: Back2back Trading Capacities

| No. | Field Name                                 | Details                                                                                                                                                                            |
|-----|--------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1   | Trading Capacity PS                        | This value is non-editable and always set to "DEAL".                                                                                                                               |
|     |                                            | If a bank does not provide the Trading Capacity via the Market Maker<br>API then the Trading Capacity of an<br>MTF transaction will<br>automatically default to the value "DEAL".  |
| 2   | Trading Capacity B2B                       | Trading Capacity used for Back2Back trading. This value is only<br>relevant for B2B scenarios where ITEX or OTC trades are hedged on<br>360T MTF.                                  |
|     |                                            | E.g.: Trading Capacity B2B is set to "DEAL".                                                                                                                                       |
|     |                                            | If an ITEX or OTC transaction is hedged on MTF, 360T will record the<br>Trading Capacity of the MTF transaction as "DEAL".                                                         |
|     |                                            | Note: If the Trading Capacity was set to "AOTC" or "MTCH", then the<br>client must have a valid LEI configured on our systems. Otherwise,<br>B2B hedging on 360T MTF cannot occur. |
| 3   | Client-specific<br>Trading<br>Capacity B2B | It is feasible to set a different Trading Capacity B2B for a given client<br>(e.g., when the client has no LEI and the Trading Capacity AOTC or<br>MTCH cannot be used).           |

<span id="page-18-1"></span>Table 4: Field description of AutoDealer user details

Please note that the autodealer routing must be reconfigured for MTF trading in the 360T Auto Dealing Suite (ADS). The necessary configurations of the ADS are not in scope of this document.

In the area "Algo Ids", a list of whitelisted Algo IDs can be viewed.

|                                                                                                                         |                                                                                    |                 |                                                                                                                                                                                | $\vee$ Preferences                                                                              | $\vee$ Administration<br>$\vee$ Help                                                              | $\overline{0}$ AA $ \overline{0}$ X<br>$\mathcal{D}^1$ |  |
|-------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------|-----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------|--------------------------------------------------------|--|
|                                                                                                                         | <b>TRADER WORKSHEET</b>                                                            | <b>NEW VIEW</b> | <b>BRIDGE ADMINISTRATION</b>                                                                                                                                                   | $+$                                                                                             |                                                                                                   |                                                        |  |
| 合<br>$\mathcal{G}$<br>甄<br>$\bigoplus$                                                                                  | Q<br>$\hat{\mathbf{a}}$ > BankA<br><b>II</b> BankClientA<br><b>III</b> BankClientB | $\rightarrow$   | <b>Company Details</b><br><b>Users</b><br><b>Daughter</b><br><b>Company Overview</b><br><b>UK MTF</b><br><b>OTC</b><br><b>Company MTF Details</b><br><b>Trader MTF Details</b> | <b>Regulatory</b><br><b>Trader Overview</b><br><b>Common</b><br><b>Trader MTF Overview</b><br>Q | <b>Order Management Groups</b><br>F<br>Algo IDS<br><b>B2B Trading Capacities</b><br>$\rightarrow$ |                                                        |  |
|                                                                                                                         |                                                                                    |                 | <b>Algo IDS</b>                                                                                                                                                                | <b>Description</b><br>Type                                                                      |                                                                                                   | <b>Default</b>                                         |  |
|                                                                                                                         |                                                                                    |                 | MyAlgo                                                                                                                                                                         | desc<br><b>RFS</b>                                                                              |                                                                                                   | Set as default                                         |  |
|                                                                                                                         |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 | $\! + \!\!\!\!$                                                                                   |                                                        |  |
|                                                                                                                         |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 |                                                                                                   |                                                        |  |
| $\frac{1}{2}$                                                                                                           |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 |                                                                                                   | <b>Discard all Changes</b><br>Save                     |  |
| $\ominus$                                                                                                               |                                                                                    |                 | BankA $\times$                                                                                                                                                                 |                                                                                                 |                                                                                                   |                                                        |  |
| <sup>1</sup> / BankA.TraderA, BankA // QA2<br>Sa, 14. Okt <a>&gt;<a>&gt;<a><a><a><a><a></a></a></a></a>&lt;</a></a></a> |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 |                                                                                                   |                                                        |  |

<span id="page-19-3"></span>Figure 18: Algo IDs

An Algorithmic (algo) trading strategy, identified by its algo ID, is required to undergo conformance tests prior to being utilized on 360T MTF. Algos which pass the relevant conformance tests are added by 360T to the Algo ID white-list and are then tradable on 360T MTF.

The Algo IDs are only applicable for Market Taker APIs or Market Maker APIs.

## <span id="page-19-0"></span>**3.4 Security and data protection**

Individual confidential data, including Nationality and National Client ID will be recorded and maintained within the 360T datacenters located in Frankfurt, in an encrypted format. As part of the MTF solution, 360T will roll out infrastructure which allows only dedicated internal reporting services to decrypt MTF data. An example of a decryption service would be an "Extract, Transform, Load" (ETL) process, responsible for the submission of transaction reports to an "approved reporting mechanism" (ARM). Reports will not be saved to permanent storage and will not be backed up.

Please note that the 360T Client Advisory Services (CAS) team has NO access to the confidential data of MTF customers. The responsibility lies with the MTF participant to ensure that all data entered in the configuration tool is valid.

### <span id="page-19-1"></span>**3.5 Data retention period**

Data is stored according to regulatory and legal requirements, including requirements of the EU General Data Protection Regulation. They will be deleted once they are no longer necessary in relation to the purposes for which they were collected or otherwise processed and relevant reporting and archiving periods have elapsed.

## <span id="page-19-2"></span>**4 EXTERNAL MAPPING**

Every configured user has a 360T specific short code. This short code will be used during the trading workflow to identify the investment decision maker and execution decision maker.

Individual user details of manual traders who are using a proprietary trading system connected to 360T MTF via an API are also required to be captured in the tool. Such manual traders are then identified by their 360T specific short code via the API, as shown in [Figure 19.](#page-20-0) It is mandatory that user details are provided prior to the usage of the corresponding short codes. In case a short code is received in the execution report with unknown user details, 360T would reject the execution.

![](_page_20_Figure_3.jpeg)

<span id="page-20-0"></span>Figure 19 360T MTF Identification of users behind API

The External Mapping feature allows to map the above-mentioned external codes with 360T short codes. Additionally, it provides mapping functionality for those liquidity providers who do not intend to maintain 360T specific short codes.

|                                                      |                                     | <i><b>BEL NUICE</b></i> | $\vee$ Preferences $\vee$ Administration $\vee$ Help<br>$AA - B \times$ |
|------------------------------------------------------|-------------------------------------|-------------------------|-------------------------------------------------------------------------|
| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b> | $\boldsymbol{+}$                    |                         |                                                                         |
| 合                                                    | Administration Start                |                         |                                                                         |
|                                                      | Configurations                      |                         |                                                                         |
|                                                      |                                     | $\bigodot$              |                                                                         |
|                                                      | <b>Regulatory Data</b>              | <b>External Mapping</b> |                                                                         |
|                                                      |                                     |                         |                                                                         |
|                                                      |                                     |                         |                                                                         |
|                                                      |                                     |                         |                                                                         |
|                                                      |                                     |                         |                                                                         |
|                                                      |                                     |                         |                                                                         |
|                                                      |                                     |                         |                                                                         |
| $\vec{\zeta}$                                        |                                     |                         |                                                                         |
| $\overline{\mathbf{C}}$                              |                                     |                         |                                                                         |
| $\bigcirc$<br>TradeAsG.TreasurerA, TradeAsG // DEMO  | $\equiv$ $\equiv$ $\equiv$ $\equiv$ |                         | Fr, 17. Nov 2017, 10:47:24 GMT // Connected ·                           |

<span id="page-20-1"></span>Figure 20 Bridge Administration Homepage: External Mappings feature

The External Mapping configuration can be accessed via the Bridge Administration homepage. Clicking on the shortcut will open the initial view with "Create New Configuration" button.

| <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b>                                                                                                                                                                                                                                     | $\pm$                   |                                                               | $AA - B \times$<br>$\vee$ Preferences $\vee$ Administration $\vee$ Help                                          |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|---------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------|
| Q ※<br>$\overline{\left( \right. }%$<br>上三<br><mark>⋒</mark><br>$\land \triangleq$ TradeAsG<br>∽<br><sup> TradeAsG.TAS.B1</sup><br><sup> TradeAsG.TAS.B2</sup><br><sup> TradeAsG.TAS.B3</sup><br>$\qquad \qquad \textcircled{\scriptsize{1}}$<br><sup> medeAsG.TradeAsG</sup><br>$\circ$ | <b>External Mapping</b> | No configuration available<br><b>Create New Configuration</b> | $\textcolor{black}{\textcolor{black}{\textcircled{\small\tt\odot}}\hspace{0.1cm}}\mathop{\mathbb{R}}\nolimits\;$ |
| ☆<br>$\mathbb C$<br>$\bigcirc$<br>TradeAsG.TreasurerA, TradeAsG // DEMO                                                                                                                                                                                                                  | TradeAsG $\times$       | <b>esun</b>                                                   | Discard all changes<br>Save<br>Fr, 17. Nov 2017, 10:49:05 GMT // Connected ·                                     |

<span id="page-21-0"></span>Figure 21 External Mapping: Create new configuration.

The admin user can either add external codes individually or mass upload multiple codes using a CSV template.

To add a code, the user must select the "Add Code" icon, enter the external code and select the corresponding 360T short code of the individual.

|                                              |                                                      |                         |                      |                        | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\parallel$ AA - $\Box$ X |
|----------------------------------------------|------------------------------------------------------|-------------------------|----------------------|------------------------|--------------------------------------------------------------------------------|
|                                              | <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b> | $\boldsymbol{+}$        |                      |                        |                                                                                |
|                                              | Q<br>$\overline{\left( \right. }%$                   | <b>External Mapping</b> |                      |                        | $\mathfrak{O} \curvearrowright \equiv$                                         |
| 合                                            | $\wedge \triangleq$ TradeAsG                         |                         |                      |                        |                                                                                |
| $\mathcal{G}$                                | <sup> TradeAsG.TAS.B1</sup>                          | <u>nazi</u>             |                      |                        |                                                                                |
|                                              | <sup> madeAsG.TAS.B2</sup>                           |                         | <b>External Code</b> | <b>Individual Name</b> |                                                                                |
| $\qquad \qquad \textcircled{\scriptsize{1}}$ | <sup> madeAsG.TAS.B3</sup>                           |                         |                      |                        |                                                                                |
|                                              | <sup> medeAsG.TradeAsG</sup>                         |                         |                      |                        |                                                                                |
| $\overline{\omega}^*$                        |                                                      |                         |                      |                        | <b>Add Code</b>                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
| $\stackrel{\leftrightarrow}{\sim}$           |                                                      |                         |                      |                        |                                                                                |
| $\mathbb C$                                  |                                                      |                         |                      |                        | <b>Discard all changes</b><br>Save                                             |
| $\bigcirc$                                   |                                                      | $TradeASG \times$       |                      |                        |                                                                                |
|                                              |                                                      |                         |                      |                        |                                                                                |
|                                              | TradeAsG.TreasurerA, TradeAsG // DEMO                |                         | <b>escript</b>       |                        | Fr, 17. Nov 2017, 10:50:49 GMT // Connected ·                                  |

<span id="page-21-1"></span>Figure 22 External Mapping: Adding external code

To mass-upload the external short codes, the admin user must download a CSV template including a list of all available users and add the codes to this file. The file can then be uploaded, and the list of mapped users will be immediately visible in the External Mapping tab. The uploaded mappings can be discarded or saved.

By default, only one mapping table can be created. In case several mapping tables for different interfaces are required, please contact [<EMAIL>](mailto:<EMAIL>) who can create additional tables.

![](_page_22_Picture_4.jpeg)

<span id="page-22-0"></span>Figure 23 External Mapping: Upload of external codes

Each manually entered or uploaded mapping can be modified or deleted.

|                                              |                                                                                                                                      |                         |                      |                        | $\vee$ Preferences $\vee$ Administration $\vee$ Help | $AA - B \times$                                   |
|----------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|-------------------------|----------------------|------------------------|------------------------------------------------------|---------------------------------------------------|
|                                              | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                                 | $\pm$                   |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
| 合                                            | Q 豢<br>$\frac{1}{2}$ $\frac{1}{2}$<br>$\overline{\left\langle \right\rangle }$                                                       | <b>External Mapping</b> |                      |                        |                                                      | $\mathcal{A} \curvearrowright \mathcal{R} \equiv$ |
| $\mathcal{G}$                                | $\land \triangleq$ TradeAsG<br>fradeAsG.TAS.B1                                                                                       | 自工业                     |                      |                        |                                                      |                                                   |
|                                              | TradeAsG.TAS.B2                                                                                                                      |                         | <b>External Code</b> | <b>Individual Name</b> |                                                      |                                                   |
| $\qquad \qquad \textcircled{\scriptsize{1}}$ | <sup> TradeAsG.TAS.B3</sup>                                                                                                          |                         | TradeAsG.Smith       | TradeAsG.TreasurerB    | $ \mathcal{V} $ O                                    |                                                   |
| $\bullet$                                    | <sup> TradeAsG.TradeAsG</sup>                                                                                                        |                         | TradeAsG.Mueller     | TradeAsG.TreasurerA    | y û                                                  |                                                   |
|                                              |                                                                                                                                      |                         | TradeAsG.Kowalski    | TradeAsG.TreasurerC    | √面<br>$\vee$                                         |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        | Add Code                                             |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
| ☆                                            |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |
| O                                            |                                                                                                                                      |                         |                      |                        | Discard all changes                                  | Save                                              |
| $\bigcirc$                                   |                                                                                                                                      | TradeAsG $\times$       |                      |                        |                                                      |                                                   |
|                                              | 1/DEMO TradeAsG.TreasurerB, TradeAsG // DEMO<br>$\equiv$ $\equiv$ $\equiv$ $\equiv$<br>Fr, 17. Nov 2017, 11:45:22 GMT // Connected · |                         |                      |                        |                                                      |                                                   |

<span id="page-23-1"></span>Figure 24 External Mapping: Modification of external codes

It is mandatory that user details are provided prior to the usage of the corresponding short codes.

## <span id="page-23-0"></span>**5 CREATE AN EXTERNAL INDIVIDUAL**

Section 3.2 describes that admin users of 360T MTF participants are required to enter the personal details of individual users within the Bridge administration tool.

To capture user-static details each individual acting as either an execution decision maker (EDM) or investment decision maker (IDM) on 360T MTF must have a unique user ID.

A unique user ID is created for an individual depending on the user type:

- (1) **Platform users**: These individuals log in directly to the 360T GUI to access the MTF venue. Platform users receive a unique user ID from 360Ts Client Advisory Services team when access is requested for them via the New User Creation process.
- (2) **External users**: These individuals are not direct users and do not log in to the 360T GUI. They may still be identified as an EDM or IDM on the 360T MTF venue and/or access the venue via an API. Unique user IDs for these individuals are created by 360T MTF admin users via the "Create an External Individual" wizard.

The 'Create an External Individual' wizard can be accessed via the "Wizards" quick link from the Bridge Administration homepage. The Wizards homepage may contain several types of Help Wizards to assist in performing quick actions.

![](_page_24_Picture_2.jpeg)

Figure 25 Bridge Administration: Help Wizard.

<span id="page-24-0"></span>Click 'Create an External Individual' to open the Help Wizard. Step 1 - Please select an Institution. In the event your setup contains multiple entities the main TEX entity will appear first in the Institution Tree. Related entities (trade as, trade-on-behalf, ITEX etc.) will appear below the main TEX entity.

The user may be configured as an Execution Decision Maker (EDM) or Investment Decision Maker (IDM) of the selected institution. Therefore, care should be taken in selecting the correct entity.

Highlight the Institution and click Next.

|               |                                              |                                                                                                                    |                      |                            |                              |                                     | $\vee$ Preferences           | $\vee$ Administration                                | $\vee$ Help | $A$ $A$ $ B$ $\times$ |  |
|---------------|----------------------------------------------|--------------------------------------------------------------------------------------------------------------------|----------------------|----------------------------|------------------------------|-------------------------------------|------------------------------|------------------------------------------------------|-------------|-----------------------|--|
|               |                                              | <b>RFS REQUESTER</b>                                                                                               | <b>DEAL TRACKING</b> |                            | <b>BRIDGE ADMINISTRATION</b> | $+$                                 |                              |                                                      |             |                       |  |
|               |                                              | $\left  \begin{array}{cc} 1 & 2 \end{array} \right\rangle$ 3 $\left  \begin{array}{c} 1 \end{array} \right\rangle$ |                      |                            |                              | Create Individual                   |                              |                                                      |             |                       |  |
|               | <mark>⋒</mark>                               |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               | $\mathcal{G}$                                |                                                                                                                    |                      |                            |                              |                                     | Please select an Institution |                                                      |             |                       |  |
|               | $\qquad \qquad \textcircled{\scriptsize{1}}$ |                                                                                                                    |                      |                            | Q Search Institution         |                                     |                              |                                                      |             |                       |  |
|               | $\bullet$                                    |                                                                                                                    |                      | $\vee \mathbf{m}$ TradeAsG |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               | 厚                                            |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               | $\mathcal{L}_{\mathcal{N}}^{k}$              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
| $\frac{1}{2}$ |                                              | Previous                                                                                                           |                      |                            |                              |                                     |                              |                                                      | Cancel      | <b>Next</b>           |  |
| $\bigcirc$    |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |
|               |                                              | 1 TradeAsG.TreasurerA, TradeAsG // DEMO                                                                            |                      |                            |                              | $\equiv$ $\equiv$ $\equiv$ $\equiv$ |                              | Wed, 18. Jul 2018, 08:54:14 GMT // Connected [FFM] · |             |                       |  |

<span id="page-25-0"></span>Figure 26 Bridge Administration: Help Wizard Step 1 - Select an Institution.

Pursuant to RTS 24, 360T must record trade information enriched with user details related to the execution decision maker and investment decision maker. Most personal details and default values for trader-related dynamic fields are entered within the Regulatory Category of the Bridge administration tool.

As a first step, some basic information is required in order to create the user. Please refer to [Figure 27](#page-26-0) and the corresponding field descriptions in [Table 5.](#page-27-1)

|                                  |                     | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b>                                                              | <b>BRIDGE ADMINISTRATION</b>   | $\vee$ Preferences<br>$+$             | $\vee$ Administration $\vee$ Help | $AA -$<br>Δ                                          | $Q \times$  |  |  |
|----------------------------------|---------------------|-----------------------------------------------------------------------------------------------------------|--------------------------------|---------------------------------------|-----------------------------------|------------------------------------------------------|-------------|--|--|
|                                  | 合                   | $\left  \begin{array}{c} 1 \\ 2 \end{array} \right $ $\left  \begin{array}{c} 3 \\ 7 \end{array} \right $ |                                | Create Individual                     |                                   |                                                      |             |  |  |
|                                  | $\mathcal{G}$       |                                                                                                           |                                | Please fill in the Individual details |                                   |                                                      |             |  |  |
|                                  | $\bigoplus$         |                                                                                                           | Login Name *                   | TradeAsG<br>$\vee$ )                  |                                   |                                                      |             |  |  |
|                                  | $\circ$             |                                                                                                           | Last Name*                     | Last Name should be specified         |                                   |                                                      |             |  |  |
|                                  | 厚                   | First Name*                                                                                               | First Name should be specified |                                       |                                   |                                                      |             |  |  |
|                                  | $\mathbf{z}_\infty$ |                                                                                                           | Description                    |                                       |                                   |                                                      |             |  |  |
|                                  |                     |                                                                                                           | Email *                        |                                       |                                   |                                                      |             |  |  |
|                                  |                     |                                                                                                           |                                | Email should be specified             |                                   |                                                      |             |  |  |
|                                  |                     |                                                                                                           | Phone Number*                  | Phone Number should be specified      |                                   |                                                      |             |  |  |
|                                  |                     |                                                                                                           | Fax Number                     |                                       |                                   |                                                      |             |  |  |
|                                  |                     |                                                                                                           | Salutation *                   | $MR \vee$                             |                                   |                                                      |             |  |  |
|                                  |                     |                                                                                                           | Country *                      | Germany                               | $\checkmark$                      |                                                      |             |  |  |
|                                  |                     |                                                                                                           |                                |                                       | * Mandatory                       |                                                      |             |  |  |
|                                  |                     |                                                                                                           |                                |                                       |                                   |                                                      |             |  |  |
|                                  |                     |                                                                                                           |                                |                                       |                                   |                                                      |             |  |  |
| $\frac{\alpha}{D}$<br>$\bigcirc$ |                     | <b>Previous</b>                                                                                           |                                |                                       |                                   | Cancel                                               | <b>Next</b> |  |  |
|                                  |                     | 1/ TradeAsG.TreasurerA, TradeAsG // DEMO                                                                  |                                | $\equiv$ $\equiv$ $\equiv$ $\equiv$   |                                   | Wed, 18. Jul 2018, 08:58:20 GMT // Connected [FFM] · |             |  |  |

<span id="page-26-0"></span>Figure 27 Bridge Administration: Help Wizard Step 2 – Individual details.

| No.                                             | Field Name  | Details                                                                                                                                                                                                                                                                                       |  |  |
|-------------------------------------------------|-------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
| 1                                               | Login Name  | Every<br>configured<br>user<br>has<br>a<br>360T<br>specific<br>short<br>code<br>(PREFIX.Lastname). This short code will be used during the trading<br>workflow in order to identify the investment decision maker (IDM) and<br>execution decision maker (EDM).                                |  |  |
|                                                 |             | The PREFIX can be selected via the dropdown menu if more than<br>one prefix is configured for your entity. The Login Name will be<br>autogenerated based on the user's last name.                                                                                                             |  |  |
| 2                                               | Last Name   | This field is mandatory and will be included in the reporting data sent<br>to the appropriate Regulatory Authority.                                                                                                                                                                           |  |  |
| 3                                               | First Name  | This field is mandatory and will be included in the reporting data sent<br>to the appropriate Regulatory Authority.                                                                                                                                                                           |  |  |
| 4                                               | Description | This field is not mandatory. It is a free text field.                                                                                                                                                                                                                                         |  |  |
| 5<br>Email                                      |             | This field is mandatory and must contain "@".                                                                                                                                                                                                                                                 |  |  |
|                                                 |             | It is recommended, but not required, that the field contains the user's<br>professional email address. If the user is converted to a platform user<br>360T requires a valid value prior to release of access credentials.                                                                     |  |  |
| 6<br>Phone Number                               |             | This field is not mandatory.                                                                                                                                                                                                                                                                  |  |  |
|                                                 |             | Any value entered must begin with "+" followed by a country code. It<br>is recommended, but not required, that the field contains the user's<br>professional phone number. If the user is converted to a platform user<br>360T requires a valid value prior to release of access credentials. |  |  |
| 7<br>Fax Number<br>This field is not mandatory. |             |                                                                                                                                                                                                                                                                                               |  |  |
|                                                 |             | Any value entered must begin with "+" followed by a country code.                                                                                                                                                                                                                             |  |  |
| 8                                               | Salutation  | This field is mandatory. The field contains three possible values:<br>"MR", "MS" or "DR". "MR" is populated by default.                                                                                                                                                                       |  |  |

| 9 | Country | This field is mandatory. The country of the company is populated by<br>default.                                                                                                                                                                                                              |  |  |
|---|---------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
|   |         | This field is for administrative purposes only and does not correspond<br>to the field "Country of the Branch" (see Regulatory Category). A<br>helpful warning will be provided if the Country and Phone Number<br>country code do not match. The warning will not prevent user<br>creation. |  |  |

<span id="page-27-1"></span>Table 5: Field description of External user details

| Add values to the mandatory fields and | click Next. |
|----------------------------------------|-------------|
|----------------------------------------|-------------|

|                                              |                                                                                                                                                                                                                                                                                |                              | $\vee$ Preferences                    | $\vee$ Administration $\vee$ Help | $\vert$ $\Diamond$ AA $-$ O $\times$                 |
|----------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|---------------------------------------|-----------------------------------|------------------------------------------------------|
|                                              | RFS REQUESTER<br><b>DEAL TRACKING</b>                                                                                                                                                                                                                                          | <b>BRIDGE ADMINISTRATION</b> | $^{+}$                                |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |
| 合                                            | $\left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \right  \right  \right  \right  \right  \right  \right  \right  \right  \right  \geq \left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \left  \$ |                              | Create Individual                     |                                   |                                                      |
| $\mathcal{G}$                                |                                                                                                                                                                                                                                                                                |                              | Please fill in the Individual details |                                   |                                                      |
| $\qquad \qquad \textcircled{\scriptsize{1}}$ |                                                                                                                                                                                                                                                                                | Login Name *                 | $\sqrt{a}$<br>TradeAsG<br>$\vee$ 1    |                                   |                                                      |
| $\bullet$                                    |                                                                                                                                                                                                                                                                                | Last Name *                  | Doe                                   |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                | First Name *                 | John                                  |                                   |                                                      |
| 厚                                            |                                                                                                                                                                                                                                                                                | Description                  |                                       |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                | Email *                      | <EMAIL>                    |                                   |                                                      |
| $\mathcal{R}_{\chi}$                         |                                                                                                                                                                                                                                                                                | <b>Phone Number*</b>         |                                       |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              | Phone Number should be specified      |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                | <b>Fax Number</b>            |                                       |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                | Salutation *<br>Country *    | $MR \vee$                             |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              | Germany                               | $\checkmark$                      |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              |                                       | * Mandatory                       |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |
|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |
| $\frac{\phi}{D}$                             |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |
| $\bigcirc$                                   | <b>Previous</b>                                                                                                                                                                                                                                                                |                              |                                       |                                   | <b>Next</b><br>Cancel                                |
|                                              | 1 TradeAsG.TreasurerA, TradeAsG // DEMO                                                                                                                                                                                                                                        |                              | <b>EECT</b>                           |                                   | Wed, 18. Jul 2018, 11:18:45 GMT // Connected [FFM] · |

<span id="page-27-0"></span>Figure 28 Bridge Administration: Help Wizard Step 2 – Individual details completed.

Before creating the user, carefully review the individual details for correctness. Click Create.

|               |                             |                                                           |                                          | $\vee$ Preferences                   | $\vee$ Administration                                | $\vee$ Help | $\vert$ $\uparrow$ $\uparrow$ AA $-$ O $\times$ |  |
|---------------|-----------------------------|-----------------------------------------------------------|------------------------------------------|--------------------------------------|------------------------------------------------------|-------------|-------------------------------------------------|--|
|               |                             | RFS REQUESTER $\vee$<br><b>DEAL TRACKING</b>              | <b>BRIDGE ADMINISTRATION</b>             | $^{+}$                               |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          | Create Individual                    |                                                      |             |                                                 |  |
|               | ⋒                           | $\left  \frac{1}{2} \right $ $\left  \frac{3}{2} \right $ |                                          |                                      |                                                      |             |                                                 |  |
|               | $\mathcal{G}$               |                                                           |                                          | Overview: Please review your changes |                                                      |             |                                                 |  |
|               |                             |                                                           | <b>Individual Name</b>                   | TradeAsG.Doe                         |                                                      |             |                                                 |  |
|               | $\qquad \qquad \textbf{a}$  |                                                           | <b>Last Name</b>                         | <b>Doe</b>                           |                                                      |             |                                                 |  |
|               | $\bullet$                   |                                                           | First Name                               | John                                 |                                                      |             |                                                 |  |
|               |                             |                                                           | <b>Description</b>                       |                                      |                                                      |             |                                                 |  |
|               | 厚                           |                                                           | Email                                    | <EMAIL>                   |                                                      |             |                                                 |  |
|               | $\mathcal{L}_{\mathcal{K}}$ |                                                           | <b>Phone Number</b><br><b>Fax Number</b> |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           | Salutation                               | Mr.                                  |                                                      |             |                                                 |  |
|               |                             |                                                           | Position                                 | External                             |                                                      |             |                                                 |  |
|               |                             |                                                           | Country                                  | Germany                              |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
| $\frac{1}{2}$ |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |
| $\bigcirc$    |                             | <b>Previous</b>                                           |                                          |                                      |                                                      | Cancel      | Create                                          |  |
|               |                             | TradeAsG.TreasurerA, TradeAsG // DEMO                     |                                          | <b>ascript</b>                       | Wed, 18. Jul 2018, 11:22:53 GMT // Connected [FFM] · |             |                                                 |  |

<span id="page-28-0"></span>Figure 29 Bridge Administration: Help Wizard Step 3 – Individual details overview.

## <span id="page-29-0"></span>**6 ANNEX**

## <span id="page-29-1"></span>**6.1 National Client Id for UK MTF**

| ISO       | Country<br>Name | st priority identifier<br>1                            | nd<br>2<br>priority<br>identifier | rd<br>3<br>priority |
|-----------|-----------------|--------------------------------------------------------|-----------------------------------|---------------------|
| 3166<br>– |                 |                                                        |                                   | identifier          |
| 1         |                 |                                                        |                                   |                     |
| Alpha 2   |                 |                                                        |                                   |                     |
|           |                 |                                                        |                                   |                     |
| AT        | Austria         | CONCAT                                                 |                                   |                     |
| BE        | Belgium         | Belgian National Number                                | CONCAT                            |                     |
|           |                 | (Numéro de registre national —<br>Rijksregisternummer) |                                   |                     |
| BG        | Bulgaria        | Bulgarian Personal Number                              | CONCAT                            |                     |
| CY        | Cyprus          | National Passport Number                               | CONCAT                            |                     |
| CZ        | Czech Republic  | National identification number                         | Passport Number                   | CONCAT              |
|           |                 | (Rodné číslo)                                          |                                   |                     |
| DE        | Germany         | CONCAT                                                 |                                   |                     |
| DK        | Denmark         | Personal identity code                                 | CONCAT                            |                     |
|           |                 | 10 digits alphanumerical:<br>DDMMYYXXXX                |                                   |                     |
| EE        | Estonia         | Estonian Personal Identification<br>Code               |                                   |                     |
|           |                 | (Isikukood)                                            |                                   |                     |
| ES        | Spain           | Tax identification number                              |                                   |                     |
|           |                 | (Código de identificación fiscal)                      |                                   |                     |
| FI        | Finland         | Personal identity code                                 | CONCAT                            |                     |
| FR        | France          | CONCAT                                                 |                                   |                     |
| GB        | United Kingdom  | UK National Insurance number                           | CONCAT                            |                     |
| GR        | Greece          | 10 DSS digit investor share                            | CONCAT                            |                     |
| HR        | Croatia         | Personal Identification Number                         | CONCAT                            |                     |
|           |                 | (OIB — Osobni identifikacijski<br>broj)                |                                   |                     |
| HU        | Hungary         | CONCAT                                                 |                                   |                     |
| IE        | Ireland         | CONCAT                                                 |                                   |                     |
| IS        | Iceland         | Personal Identity Code (Kennitala)                     |                                   |                     |
| IT        | Italy           | Fiscal code                                            |                                   |                     |

|                        |                             | (Codice fiscale)                          |                                     |        |
|------------------------|-----------------------------|-------------------------------------------|-------------------------------------|--------|
| LI                     | Liechtenstein               | CONCAT                                    |                                     |        |
| LT                     | Lithuania                   | Personal code                             | National Passport<br>Number         | CONCAT |
|                        |                             | (Asmens kodas)                            |                                     |        |
| LU                     | Luxembourg                  | CONCAT                                    |                                     |        |
| LV                     | Latvia                      | Personal code                             | CONCAT                              |        |
|                        |                             | (Personas kods)                           |                                     |        |
| MT                     | Malta                       | National Identification Number            | National Passport<br>Number         |        |
| NL                     | Netherlands                 | National Passport Number                  | National identity<br>card number    | CONCAT |
| NO                     | Norway                      | 11-digit personal id                      | CONCAT                              |        |
|                        |                             | (Foedselsnummer)                          |                                     |        |
| PL                     | Poland                      | National Identification Number            | Tax Number                          |        |
|                        |                             | (PESEL)                                   | (Numer identyfikacji<br>podatkowej) |        |
| PT                     | Portugal                    | Tax number                                | National Passport<br>Number         | CONCAT |
|                        |                             | (Número de Identificação Fiscal)          |                                     |        |
| RO                     | Romania                     | National Identification Number            | National Passport<br>Number         | CONCAT |
|                        |                             | (Cod Numeric Personal)                    |                                     |        |
| SE                     | Sweden                      | Personal identity number                  | CONCAT                              |        |
| SI                     | Slovenia                    | Personal Identification Number            | CONCAT                              |        |
|                        |                             | (EMŠO: Enotna Matična Številka<br>Občana) |                                     |        |
| SK                     | Slovakia                    | Personal number                           | National Passport<br>Number         | CONCAT |
|                        |                             | (Rodné číslo)                             |                                     |        |
| All other<br>countries | National Passport<br>Number | CONCAT                                    |                                     |        |
|                        |                             |                                           |                                     |        |

## <span id="page-31-0"></span>**6.2 National Client Id for EU MTF**

| ISO<br>3166<br>–<br>1<br>Alpha 2 | Country<br>Name | st priority identifier<br>1                                                       | nd<br>2<br>priority<br>identifier | rd<br>3<br>priority<br>identifier |
|----------------------------------|-----------------|-----------------------------------------------------------------------------------|-----------------------------------|-----------------------------------|
| AT                               | Austria         | CONCAT                                                                            |                                   |                                   |
| BE                               | Belgium         | Belgian National Number<br>(Numéro de registre national —<br>Rijksregisternummer) | CONCAT                            |                                   |
| BG                               | Bulgaria        | Bulgarian Personal Number                                                         | CONCAT                            |                                   |
| CY                               | Cyprus          | National Passport Number                                                          | CONCAT                            |                                   |
| CZ                               | Czech Republic  | National identification number<br>(Rodné číslo)                                   | Passport Number                   | CONCAT                            |
| DE                               | Germany         | CONCAT                                                                            |                                   |                                   |
| DK                               | Denmark         | Personal identity code<br>10 digits alphanumerical:<br>DDMMYYXXXX                 | CONCAT                            |                                   |
| EE                               | Estonia         | Estonian Personal Identification<br>Code<br>(Isikukood)                           |                                   |                                   |
| ES                               | Spain           | Tax identification number<br>(Código de identificación fiscal)                    |                                   |                                   |
| FI                               | Finland         | Personal identity code                                                            | CONCAT                            |                                   |
| FR                               | France          | CONCAT                                                                            |                                   |                                   |
| GR                               | Greece          | 10 DSS digit investor share                                                       | CONCAT                            |                                   |
| HR                               | Croatia         | Personal Identification Number<br>(OIB — Osobni identifikacijski<br>broj)         | CONCAT                            |                                   |
| HU                               | Hungary         | CONCAT                                                                            |                                   |                                   |
| IE                               | Ireland         | CONCAT                                                                            |                                   |                                   |
| IS                               | Iceland         | Personal Identity Code (Kennitala)                                                |                                   |                                   |
| IT                               | Italy           | Fiscal code<br>(Codice fiscale)                                                   |                                   |                                   |

<span id="page-32-0"></span>

| LI                     | Liechtenstein               | National Passport Number                                                    | National Identity<br>Card Number                  | CONCAT |
|------------------------|-----------------------------|-----------------------------------------------------------------------------|---------------------------------------------------|--------|
| LT<br>Lithuania        |                             | Personal code<br>(Asmens kodas)                                             | National Passport<br>Number                       | CONCAT |
| LU                     | Luxembourg                  | CONCAT                                                                      |                                                   |        |
| LV                     | Latvia                      | Personal code<br>(Personas kods)                                            | CONCAT                                            |        |
| MT                     | Malta                       | National Identification Number                                              | National Passport<br>Number                       |        |
| NL                     | Netherlands                 | National Passport Number                                                    | National identity<br>card number                  | CONCAT |
| NO                     | Norway                      | 11-digit personal id<br>(Foedselsnummer)                                    | CONCAT                                            |        |
| PL                     | Poland                      | National Identification Number<br>(PESEL)                                   | Tax Number<br>(Numer identyfikacji<br>podatkowej) |        |
| PT                     | Portugal                    | Tax number<br>(Número de Identificação Fiscal)                              | National Passport<br>Number                       | CONCAT |
| RO                     | Romania                     | National Identification Number<br>(Cod Numeric Personal)                    | National Passport<br>Number                       | CONCAT |
| SE                     | Sweden                      | Personal identity number                                                    | CONCAT                                            |        |
| SI                     | Slovenia                    | Personal Identification Number<br>(EMŠO: Enotna Matična Številka<br>Občana) | CONCAT                                            |        |
| SK                     | Slovakia                    | Personal number<br>(Rodné číslo)                                            | National Passport<br>Number                       | CONCAT |
| All other<br>countries | National Passport<br>Number | CONCAT                                                                      |                                                   |        |

## <span id="page-33-0"></span>**6.3 CONCAT format**

The CONCAT ID has a length of 18 alpha-numerical characters and consists of the following elements in the following order:

- o the date of birth of the person in the format YYYYMMDD;
- o the first five characters of the first name;
- o the first five characters of the surname.

To obtain the first name and surname, following method should be applied:

- Any prefixes to the names that denote titles, position, profession or academic qualifications, are to be removed (e.g. Dr.)
- Prefixes to surnames that are not included in the below list, or prefixes attached to names, i.e. McDonald, MacChrystal, O'Brian, O'Neal, should not be removed; but note that the apostrophes will be removed in the next step. The below list is not case sensitive:

am, auf, auf dem, aus der, d, da, de, de l', del, de la, de le, di, do, dos, du, im, la, le, mac, mc, mhac, mhíc, mhic giolla, mic, ni, ní, níc, o, ó, ua, ui, uí, van, van de, van den, van der, vom, von, von dem, von den, von der

- Transliteration of special characters:
  - o diacritics, apostrophes, hyphens, punctuation marks and spaces must be removed.
  - o special characters such as German "Umlauts" or accented characters are translated according to a table into a characters from the range A..Z, e.g. Ä becomes A, ß becomes S
- Lower case letters must be transformed to capital letters.

| First<br>name(s) | Family<br>name/Surname(s) | Date<br>of<br>birth         | CONCAT             | Comment                                                              |
|------------------|---------------------------|-----------------------------|--------------------|----------------------------------------------------------------------|
| George           | O'Brian                   | 17th of June<br>1987        | 19870617GEORGOBRIA | O' is attached to name,<br>not converted. Removed<br>apostrophe.     |
| Sung-mi          | Ødegård                   | 14th of<br>November<br>1999 | 19991114SUNGMODEGA | Removed hyphen from<br>first name. Converted Ø<br>to O, and å to A   |
| Anna             | Von der Früht             | 6th of<br>January<br>1977   | 10770106ANNA#FRUHT | Padded Anna to 5<br>characters: Removed<br>prefix. Converted ü to U: |

## <span id="page-34-0"></span>**7 CONTACTING 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

**Germany** *360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **Middle East Asia Pacific**

**United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

#### **EMEA Americas**

**USA** *360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756