![](_page_0_Picture_0.jpeg)

# 360T Market Maker Order API

### FIX Rules of Engagement

API Version: 7.0

Implements FIX Protocol Version FIX.4.4

360 Treasury Systems AG Grüneburgweg 16-18 / Westend Carrée D-60322 Frankfurt am Main Tel: +49 69 900289 0 Fax: +49 69 900289 29 Email: <EMAIL> Commercial Register Frankfurt, No. HRB 49874 Executive Board: <PERSON>, <PERSON>, <PERSON> Supervisory Board: <PERSON>

### <span id="page-1-0"></span>**1 Overview**

The purpose of this document is to provide an overview of the FIX Market Maker Order API offered by 360 Treasury Systems. It focuses on the technical aspects and is intended to provide clients with a clear understanding of how a successful connection could be made.

It contains an overview of the general workflow, as well as detailed specifications of the utilized FIX messages. The API is implemented to meet FIX 4.4 standards.

### **Table of Contents**

| 1 | Overview                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 2                                                                       |
|---|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------|
| 2 | Product Offering<br>2.1<br>Products currently supported by API                                                                                                                                                                                                                                                                                                                                                                                                            | 4<br>4                                                                  |
| 3 | Connecting to 360T<br>3.1<br>Network Connectivity<br>3.1.1<br>Radianz<br>3.1.2<br>Stunnel<br>.<br>3.1.3<br>Cross Connect<br>.<br>3.1.4<br>N7<br>3.1.5<br>Plain Internet<br>3.2<br>Connection and Firewall Configuration<br>3.3<br>FIX Engine Compatibility Testing<br>.<br>3.4<br>FIX Protocol Levels<br>3.4.1<br>User Credentials<br>.<br>3.4.2<br>FIX Session Reset<br>.<br>3.5<br>Availability<br>.<br>3.6<br>Connection information<br>3.7<br>Order Timeout Procedure | 5<br>5<br>5<br>5<br>5<br>5<br>5<br>5<br>6<br>6<br>6<br>6<br>6<br>7<br>7 |
| 4 | Utilization of the FIX Protocol<br>4.1<br>System Messages<br>.<br>4.2<br>Business Messages<br>.                                                                                                                                                                                                                                                                                                                                                                           | 8<br>8<br>8                                                             |
| 5 | Workflow                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 9                                                                       |
| 6 | FIX Business Messages<br>6.1<br>Notes on important details<br>.<br>6.2<br>Message Header<br>6.3<br>Message Footer<br>.<br>6.4<br>Logon [A]<br>6.5<br>Heartbeat [0]<br>.<br>6.6<br>TestRequest [1]<br>.<br>6.7<br>ResendRequest [2]<br>.<br>6.8<br>Reject [3]<br>.<br>6.9<br>SequenceReset [4]<br>.<br>6.10 Logout [5]                                                                                                                                                     | 11<br>11<br>11<br>11<br>11<br>12<br>12<br>13<br>13<br>13<br>14          |
| 7 | Business messages<br>7.1<br>Notes on important details<br>.<br>7.2<br>New Order Single [D]<br>7.2.1<br>Examples<br>7.2.1.1<br>FX Spot<br>.<br>7.2.1.2<br>FX Forward<br>.                                                                                                                                                                                                                                                                                                  | 15<br>15<br>15<br>19<br>19<br>20                                        |

|   | 7.3 | New Order List [E]<br>.          | 21 |
|---|-----|----------------------------------|----|
|   | 7.4 | Cancel Order [F]<br>.            | 23 |
|   | 7.5 | Execution Report [8]<br>.        | 24 |
|   |     | 7.5.1<br>Examples                | 29 |
|   |     | 7.5.1.1<br>FX Spot<br>.          | 29 |
|   |     | 7.5.1.2<br>FX Forward<br>.       | 29 |
|   | 7.6 | Order Cancel Reject [9]          | 31 |
|   | 7.7 | Order Status [H]                 | 31 |
|   | 7.8 | Don't Know Trade [Q]<br>.        | 32 |
|   |     |                                  |    |
|   | 7.9 | Business Message Reject [j]<br>. | 33 |
| 8 |     | Migration from older versions    | 34 |
|   | 8.1 | From version 1 to Version 2      | 34 |
|   | 8.2 | From version 2 to Version 3      | 34 |
|   | 8.3 | From version 3 to Version 4      | 34 |
|   | 8.4 | From version 4 to Version 5      | 34 |
|   |     |                                  |    |
|   |     |                                  |    |
| 9 |     | Firewall configuration           | 35 |
|   |     |                                  |    |
|   |     | 10 FIX Session Reset             | 36 |

## <span id="page-4-0"></span>**2 Product Offering**

#### <span id="page-4-1"></span>**2.1 Products currently supported by API**

- 1. Limit orders
- 2. Stop orders
- 3. Market orders
- 4. OCO orders
- 5. IfDone orders

Orders can be placed with instruction to fill the entire amount in one go producing one ticket (Fill-or-Kill) or to allow several partial fills resulting in a matching number of tickets being booked.

## <span id="page-5-0"></span>**3 Connecting to 360T**

Once interest has been established in connecting a client to 360T's FIX services, a project manager will be assigned. The project manager will manage the process of connecting the FIX engines of the two companies, performing FIX engine tests, and bringing the connection live.

### <span id="page-5-1"></span>**3.1 Network Connectivity**

Only incoming connections are supported, i.e. the customer always initiates connections to 360T. Clients may connect to 360T's electronic FX trading system using one of the following options:

#### <span id="page-5-2"></span>**3.1.1 Radianz**

If a client is already connected to Radianz, lead-time to establish connectivity between the client and 360T is ten days, taking into account change control procedures. If the client is not already connected, the lead-time will be advised at the time the order is placed. Radianz will quote a low network latency and will offer a highlevel of encryption for traffic passing through its network. This is the preferred connectivity option. For more information see [http://www.radianz.com](http://www.radianz.com/)

#### <span id="page-5-3"></span>**3.1.2 Stunnel**

Connections via Internet should be secured by establishing a Stunnel connection to the 360T data center. Certificates have to be negotiated between the network teams of the client and 360T.

#### <span id="page-5-4"></span>**3.1.3 Cross Connect**

Cross-connections inside the Equinix London LD4/LD5, Equinix Tokyo TY3 and Equinix New York NY4 data centers are available for enhanced network performance. In London we recommend to order cross-connects to both sites in order to have redundancy in case of failure on one site.

#### <span id="page-5-5"></span>**3.1.4 N7**

Connectivity to 360T is available from Deutsche Börse's N7 global exchange network. N7 provides thousands of connections in 32 countries across Europe, North America and Asia. It is built to deliver speed, reliability and performance.

#### <span id="page-5-6"></span>**3.1.5 Plain Internet**

A plain internet connection is only available on our testing environments.

### <span id="page-5-7"></span>**3.2 Connection and Firewall Configuration**

See appendix [9](#page-35-0) [Firewall configuration](#page-35-0) for the necessary firewall rules to access this 360T service.

#### <span id="page-6-0"></span>**3.3 FIX Engine Compatibility Testing**

We offer clients the ability to test against our FIX engine by connecting over the Internet. This allows compatibility issues to be discovered early in the process. The process for going live has three stages:

#### 1. Initial Development & Testing

During this stage the client connection will be linked to the 360T development system.

#### 2. Conformance Testing

Once a client is ready to go live, 360T will conduct a series of conformance tests with the client to certify the connection is compatible with the production environment. Conformance testing takes place over the test network connection.

#### 3. Switch to Live System

Due to the need to make firewall changes and changes to the proxy servers the switch to the live system will take at least a week. This is because changes to these services can only be performed out of business hours, i.e., at the weekend.

#### <span id="page-6-1"></span>**3.4 FIX Protocol Levels**

FIX is the communications protocol 360T and its clients will both have to support to fully communicate with each other. 360T systems are compliant with FIX version 4.4. In addition, some systems support transport layer FIXT.1.1 and application layer FIX.5.0.

Only the messages and fields relevant for the communication between 360T and its clients are included in this document. Each message described here lists 360T required fields and their supported values plus any fields that result in rejection. Additional fields will be ignored and unsupported field values will be rejected. The official FIX specification, available on [http://www.fixprotocol.org,](http://www.fixprotocol.org) should be consulted for in-depth descriptions.

#### <span id="page-6-2"></span>**3.4.1 User Credentials**

360T will provide the values for the fields SenderCompID[49] and TargetCompID[56].[1](#page-6-5) If required by the API, 360T will also provide a password that must be provided in the Logon<A> message for the client to be authenticated.[2](#page-6-6)

#### <span id="page-6-3"></span>**3.4.2 FIX Session Reset**

For established connections, a FIX session reset is performed according to the schedule detailed in appendix [10](#page-36-0) [FIX Session Reset.](#page-36-0)

#### <span id="page-6-4"></span>**3.5 Availability**

- The 360T Production environment is available between Sunday 6 pm America/New York and Friday 5 pm America/New York.
- Every day during the week the environment has a maintenance window between 5 pm and 6 pm America/New York during which the application may not be available.

<span id="page-6-5"></span><sup>1</sup>Sender/TargetSubID[50/57] or Sender/TargetLocationID[142/143] will be defined if needed.

<span id="page-6-6"></span><sup>2</sup>A user name / identifier will also be provided, if required for the Logon<A> message.

• The non-availability of the 360T platform does not mean that the client will be disconnected from their FIX session(s) for the entire duration of the maintenance window. If the client remains connected during the maintenance window, the functionality of the API will not be available.

#### <span id="page-7-0"></span>**3.6 Connection information**

The target IP and port to connect to will be provided by 360T. Fix session details like start time, end time, reset rules, ping intervals, etc. should be agreed upon during interface development.

#### <span id="page-7-1"></span>**3.7 Order Timeout Procedure**

In order to minimize the risk of asymmetric positions for either counterpart of an order, we standardize the process of taking no action when the result of an action is unknown. Thus, both parties can act immediately without time passing to clarify the situation.

This means that orders routed into a connected Order Management System (OMS) on the liquidity provider market maker side are subject to a strict acknowledgement process.

If the provider's OMS does not reply within 10 seconds on a New Order message, the order will automatically change to the status REJECTED in the requestor's Order Management Tool (OMT). We additionally generate a cancel message from the system to cancel the order automatically in the provider's OMS to reflect the state in 360T's system. In parallel, the provider is still notified by email (code 7006 in the subject) and will be contacted by our Client Advisory Services (CAS) team. The provider will have to confirm that the order either did not reach the provider's OMS or that the order was cancelled in the provider's OMS.

In case of no reply to an Order Cancel message within 10 seconds, the order will automatically change to the status CANCELLED in the requester's OMT. In parallel, the provider is still notified by email (code 7002 in the subject) and will be contacted by our CAS team. The provider will have to confirm that the order was cancelled in the provider's OMS.

## <span id="page-8-0"></span>**4 Utilization of the FIX Protocol**

### <span id="page-8-1"></span>**4.1 System Messages**

The following administrative system messages are supported as prescribed by the FIX standard. The supported message types are:

- Logon [A]
- Heartbeat [0] (interval configured to 30 seconds on 360T side)
- Test Request [1]
- Resend Request [2]
- Reject [3]
- Sequence Reset [4]
- Logout [5]

#### <span id="page-8-2"></span>**4.2 Business Messages**

- New Order Single [D] Sent by 360T to the bank
- Cancel Request [F] Sent by 360T to the bank
- Execution Report [8] Sent by the bank to 360T
- Order Cancel Reject [9] Sent by the bank to 360T
- Order Status [H] Sent by 360T to the bank

### <span id="page-9-0"></span>**5 Workflow**

This chapter presents the most common scenarios.

![](_page_9_Figure_3.jpeg)

(c) Order filled with 2 partial fills and canceled (d) Order filled with 1 partial fill and expired

![](_page_10_Figure_1.jpeg)

5.0

(e) Order Status execution report (f) Order Status execution report

### <span id="page-11-0"></span>**6 FIX Business Messages**

### <span id="page-11-1"></span>**6.1 Notes on important details**

- The customer connects to 360T.
- All timestamps used use time in UTC.

### <span id="page-11-2"></span>**6.2 Message Header**

Each message is preceded by a standard header. It identifies the message type, length, destination, sequence number, origination point and time.

| Tag | Name         | Type         | Req. | Description                                                                                    |
|-----|--------------|--------------|------|------------------------------------------------------------------------------------------------|
| 8   | BeginString  | String       | Y    | FIX.4.4 (must be first field in message)                                                       |
| 9   | BodyLength   | Length       | Y    | must be second field in message                                                                |
| 35  | MsgType      | String       | Y    | Shown in individual message specifications in this<br>document, must be third field in message |
| 56  | TargetCompID | String       | Y    | Set to an agreed value                                                                         |
| 49  | SenderCompID | String       | Y    | Set to an agreed value                                                                         |
| 34  | MsgSeqNum    | SeqNum       | Y    | Message Sequence Number (incremented on 360T<br>side)                                          |
| 52  | SendingTime  | UTCTimestamp | Y    | Time of message transmission                                                                   |

Table 6.1: Standard Header for FIX Messages

#### <span id="page-11-3"></span>**6.3 Message Footer**

This footer will be used in all messages sent between 360T and the customer system.

| Tag | Name     | Type   | Req. | Description                                                                                                               |
|-----|----------|--------|------|---------------------------------------------------------------------------------------------------------------------------|
| 10  | CheckSum | String | Y    | Standard FIX value. Always defined as three charac<br>ters (unencrypted). Must be the last field in every FIX<br>message. |

Table 6.2: Standard Footer for FIX Messages

### <span id="page-11-4"></span>**6.4 Logon [A]**

The customer starts the communication by sending a Login message. 360T checks the supplied credentials and answers with a Logon reply if successful.

| Tag | Name                            | Type    | Req. | Description                                                                                              |
|-----|---------------------------------|---------|------|----------------------------------------------------------------------------------------------------------|
|     | <messageheader></messageheader> |         | Y    | MsgType <35> = A                                                                                         |
| 98  | EncryptMethod                   | int     | Y    | 0 = 'always unencrypted'                                                                                 |
| 108 | HeartBtInt                      | int     | Y    | should be set to 30                                                                                      |
| 141 | ResetSeqNumFlag                 | Boolean | Y    | should be set to 'Y'                                                                                     |
| 554 | Password                        | String  | Y    | Will be provided by 360T.<br>Note: Without transport-level-encryption, only mini<br>mal security exists. |
|     | <messagefooter></messagefooter> |         |      |                                                                                                          |

Table 6.3: Logon message

### <span id="page-12-0"></span>**6.5 Heartbeat [0]**

The Heartbeat monitors the status of the communication link and identifies when the last of a string of messages was not received.

When either end of a FIX connection has not sent any data for HeartBtInt <108> seconds, it will transmit a Heartbeat message. When either end of the connection has not received any data for (HeartBtInt <108> + "some reasonable transmission time") seconds, it will transmit a Test Request message. If there is still no Heartbeat message received after (HeartBtInt <108> + "some reasonable transmission time") seconds then the connection is considered lost.

| Tag                             | Name      | Type   | Req.             | Description                                                               |
|---------------------------------|-----------|--------|------------------|---------------------------------------------------------------------------|
| <messageheader></messageheader> |           | Y      | MsgType <35> = 0 |                                                                           |
| 112                             | TestReqID | String | N                | Required if the Heartbeat is a response to a TestRe<br>quest [1] message. |
| <messagefooter></messagefooter> |           |        | Y                |                                                                           |

Table 6.4: Heartbeat message

### <span id="page-12-1"></span>**6.6 TestRequest [1]**

The Test Request message forces a heartbeat from the opposing application. The Test Request message checks sequence numbers or verifies communication line status. The opposite application responds to the Test Request with a Heartbeat containing the TestReqID <112>.

| Tag                             | Name                | Type | Req.             | Description                                |
|---------------------------------|---------------------|------|------------------|--------------------------------------------|
| <messageheader></messageheader> |                     | Y    | MsgType <35> = 1 |                                            |
| 112                             | TestReqID<br>String |      | Y                | Identifier that must be used in the reply. |
| <messagefooter></messagefooter> |                     |      | Y                |                                            |

Table 6.5: TestRequest message

### <span id="page-13-0"></span>**6.7 ResendRequest [2]**

The resend request is sent by the receiving application to initiate the retransmission of messages. This function is utilized if a sequence number gap is detected, if the receiving application lost a message, or as a function of the initialization process.

| Tag                             | Name       | Type | Req.             | Description |
|---------------------------------|------------|------|------------------|-------------|
| <messageheader></messageheader> |            | Y    | MsgType <35> = 2 |             |
| 7                               | BeginSeqNo | int  | Y                |             |
| 16<br>EndSeqNo<br>int           |            | Y    |                  |             |
| <messagefooter></messagefooter> |            |      | Y                |             |

Table 6.6: ResendRequest message

- To request a single message: BeginSeqNo <7> = EndSeqNo <16>
- To request a range of messages: BeginSeqNo <7> = first message of range, EndSeqNo <16> = last message of range
- To request all messages subsequent to a particular message: BeginSeqNo <7> = first message of range, EndSeqNo <16> = 0 (represents infinity) .

### <span id="page-13-1"></span>**6.8 Reject [3]**

The Reject message is issued when a message is received but cannot be properly processed due to a session-level rule violation. An example of when a reject may be appropriate would be the receipt of a message with invalid basic data which successfully passes de-encryption, CheckSum <10> and BodyLength <9> checks.

| Tag                             | Name      | Type   | Req.             | Description                               |
|---------------------------------|-----------|--------|------------------|-------------------------------------------|
| <messageheader></messageheader> |           | Y      | MsgType <35> = 3 |                                           |
| 45                              | RefSeqNum | int    | Y                | MsgSeqNum <34> of the rejected message    |
| 58                              | Text      | String | N                | Reason why the message can't be processed |
| <messagefooter></messagefooter> |           |        | Y                |                                           |

Table 6.7: Reject message

### <span id="page-13-2"></span>**6.9 SequenceReset [4]**

| Tag                             | Name     | Type | Req.             | Description                                                         |
|---------------------------------|----------|------|------------------|---------------------------------------------------------------------|
| <messageheader></messageheader> |          | Y    | MsgType <35> = 4 |                                                                     |
| 36                              | NewSeqNo | int  | Y                | Sequence number which is expected to be sent in the<br>next message |
|                                 |          |      |                  | Continued on next page                                              |

| Tag                             | Name | Type | Req. | Description |
|---------------------------------|------|------|------|-------------|
| <messagefooter></messagefooter> |      | Y    |      |             |

6.10

Table 6.8: SequenceReset message

### <span id="page-14-0"></span>**6.10 Logout [5]**

The Logout message initiates or confirms the termination of a FIX session. Disconnection without the exchange of Logout messages should be interpreted as an abnormal condition.

| Tag                             | Name | Type   | Req.             | Description                                                                                    |
|---------------------------------|------|--------|------------------|------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |      | Y      | MsgType <35> = 5 |                                                                                                |
| 58                              | Text | String | N                | This field is used in confirmations only to specify why<br>the message could not be processed. |
| <messagefooter></messagefooter> |      |        | Y                |                                                                                                |

Table 6.9: Logout message

### <span id="page-15-0"></span>**7 Business messages**

### <span id="page-15-1"></span>**7.1 Notes on important details**

- All IDs in messages coming from 360T could include special characters like '-' (short dash), '\_' (underscore), '/' (slash), '.' (dot)
- Mandatory fields for rates and amounts in execution reports where there's no amount booked should be filled with 0.
- Support for Partialallowed (7078)=N can be configured explicitly on 360T's side to minimize order rejections.
- Validation failures and missing fields in ExecutionReport messages in production result in blocking the provider until the the issue is resolved. A BusinessMessageReject message with the issue will be sent. In the test environment blocking is disabled.
- Should orders be submitted with ProductType<7071>=FX-SPOT and no SettlDate<64>, then a rolling spot is assumed. This means that an order placed on 16 Nov 2011 may be executed on 17 Nov. Then the fill will be booked with a spot date relative to the trade day of booking - 21 Nov in the example. When the orders are placed, RefSpotDate<7070> is provided indicating the spot date relative to that trading day as it is in the 360T platform. Furthermore, in OrderStatus messages tag RefSpotDate<7070> will also be provided indicating the spot date relative to the trading day when the message was sent.

The provider must validate if the reference spot rate provided by 360T in order placement messages or in order status requests matched what they would book should there be a fill at that moment. In case of discrepancy identified in the RefSpotDate in NewOrderSingle or NewOrderList, the orders should be rejected. In case of a discrepancy identified in RefSpotDate in OrderStatus messages, 360T support and the client should be contacted immediately.

360T will always book fills for rolling spot orders with the spot date as it's calculated in the 360T system. The SettlDate<64> tag in ExecutionReport is used only for validation and escalation and will not be taken up as the settlement date for the 360T ticket.

### <span id="page-15-2"></span>**7.2 New Order Single [D]**

(360T → bank)

To submit an order to the bank.

| Tag                             | Name     | Type     | Req.             | Description                                            |
|---------------------------------|----------|----------|------------------|--------------------------------------------------------|
| <messageheader></messageheader> |          | Y        | MsgType <35> = D |                                                        |
| 1                               | Account  | String   | Y                | Defines the legal entity of the counterparty.          |
| 11                              | ClOrdID  | String   | Y                | Unique execution identifier assigned by 360T.          |
| 15                              | Currency | Currency | Y                | Notional currency in which the quantities are defined. |
|                                 |          |          |                  | Continued on next page                                 |

| Tag  | Name         | Type         | Req. | Description                                                                                                                                                                                                                                                                                        |
|------|--------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 38   | OrderQty     | Qty          | Y    | Defines the notional amount in the notional currency<br>for which the customer tried to execute.<br>In case of<br>Future, it is the number of contracts.                                                                                                                                           |
| 40   | OrdType      | Char         | Y    | Defines the type of order. Possible values:                                                                                                                                                                                                                                                        |
|      |              |              |      | • '1' = Market                                                                                                                                                                                                                                                                                     |
|      |              |              |      | • '2' = Limit                                                                                                                                                                                                                                                                                      |
|      |              |              |      | • '3' = Stop                                                                                                                                                                                                                                                                                       |
| 44   | Price        | Price        | C    | For Stop orders this is the stop rate, for Limit orders -<br>the limit rate.<br>Limit rates are only supported for FX Outrights<br>(Spot/Forward) and FX Swaps.<br>For FX Swaps, the<br>limit rate refers to the swap points, for all other prod<br>ucts, the limit rate refers to the spot price. |
| 54   | Side         | Char         | Y    | Defines if the requester has bought or sold the given<br>symbol.1 Possible values:                                                                                                                                                                                                                 |
|      |              |              |      | • '1' = Buy                                                                                                                                                                                                                                                                                        |
|      |              |              |      | • '2' = Sell                                                                                                                                                                                                                                                                                       |
| 55   | Symbol       | String       | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2.                                                                                                                                                                                                                                    |
| 59   | TimeInForce  | Char         | Y    | Defines the expiration of the order. Possible values:                                                                                                                                                                                                                                              |
|      |              |              |      | • '1' = Good Till Cancel (GTC)                                                                                                                                                                                                                                                                     |
|      |              |              |      | • '6' = Good Till Date (GTD)                                                                                                                                                                                                                                                                       |
| 60   | TransactTime | UTCTimestamp | Y    | Time when the trade was executed.                                                                                                                                                                                                                                                                  |
| 64   | SettlDate    | UTCDate      | C    | Settlement date, UTC date in YYYYMMDD format.<br>Provided only when ProductType=FX-FWD. Other<br>wise rolling spot is assumed. See 7.1 for more details<br>on settlement and spot dates.                                                                                                           |
| 126  | ExpireTime   | UTCTimestamp | C    | Expiration<br>time<br>of<br>the<br>order<br>in<br>the<br>format<br>YYYYMMDD-HH:MM:SS. Conditionally required<br>if TimeInForce=GTD.                                                                                                                                                                |
| 7070 | RefSpotDate  | UTCDate      | Y    | Reference spot rate. This field contains the spot date<br>for the specified currency couple as it is in the 360T<br>platform. See 7.1 for more details on settlement and<br>spot dates.                                                                                                            |
|      |              |              |      | Continued on next page                                                                                                                                                                                                                                                                             |

<span id="page-16-0"></span><sup>1</sup>The Side<54> tag defines the direction of the Symbol, i.e. the side of the first currency in the symbol and not of the notional currency.

| Tag  | Name                | Type    | Req. | Description                                                                                                                                                                                                                     |
|------|---------------------|---------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 7071 | ProductType         | String  | Y    | The product type inside the order. Possible values:                                                                                                                                                                             |
|      |                     |         |      | • FX-SPOT                                                                                                                                                                                                                       |
|      |                     |         |      | • FX-FWD                                                                                                                                                                                                                        |
|      |                     |         |      | • FX-FUT                                                                                                                                                                                                                        |
|      |                     |         |      |                                                                                                                                                                                                                                 |
| 7075 | FixingReference     | String  | C    | The fixing reference of a Fixing order.                                                                                                                                                                                         |
| 7078 | PartialAllowed      | Boolean | N    | Indicates if partial fills are allowed.<br>If this field is<br>missing, 'Y' is implied.<br>Should this field be set to<br>'N', the order needs to be completed in one fill until<br>the expiration defined in TimeInForce (59). |
| 7543 | FixingDate          | UTCDate | C    | The fixing date of a Fixing order.                                                                                                                                                                                              |
| 7653 | UTIID               | String  | C    | UTI ID assigned by the provider for this fill.                                                                                                                                                                                  |
| 7611 | ExecutionVenueType  | int     | C    | Whether executed on a SEF or off facility:                                                                                                                                                                                      |
|      |                     |         |      | • '1' = SEF                                                                                                                                                                                                                     |
|      |                     |         |      | • '2' = OFF facility (default)                                                                                                                                                                                                  |
|      |                     |         |      | • '3' = MTF                                                                                                                                                                                                                     |
| 7612 | ExecutionVenue      | String  | C    | LEI or name of SEF executed upon.<br>Required if ExecutionVenueType<7611>='1'.                                                                                                                                                  |
| 22   | SecurityIDSource    | int     | C    | Should be '4 '= ISIN                                                                                                                                                                                                            |
| 48   | SecurityID          | String  | C    | Should define the ISIN code in case SecurityIDSource<br>is set.                                                                                                                                                                 |
| 461  | CFICode             | String  | C    | This tag describes the type of instrument for which a<br>quote is being requested.<br>This is based on the ISO<br>10962 standard.<br>Required if ExecutionVenueType<7611>='1'.                                                  |
| 2891 | UPICode             | String  | C    | A Unique Product Identifier for the instrument.<br>Required if ExecutionVenueType<7611>='1' or '3'.                                                                                                                             |
| 7613 | IsLargeTrade        | Boolean | C    | Y/N to indicate if part of a large trade.<br>Required if ExecutionVenueType<7611>='1'.                                                                                                                                          |
| 7614 | RequiredTransaction | Boolean | C    | Y/N to indicate whether it is a required transaction.<br>Required if ExecutionVenueType<7611>='1'.                                                                                                                              |
| 7615 | ReportingParty      | String  | C    | Reporting Party as determined by SEF.<br>Required if ExecutionVenueType<7611>='1'.                                                                                                                                              |
| 7616 | SwapDataRepository  | String  | C    | Name or LEI of the SDR this trade will be reported<br>to.<br>Required if ExecutionVenueType<7611>='1'.                                                                                                                          |
|      |                     |         |      | Continued on next page                                                                                                                                                                                                          |

| Tag       | Name                 | Type       | Req. | Description                                                                                                                                                                                                                                                                                                                                 |
|-----------|----------------------|------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 7617      | ClearingExempted     | Boolean    | C    | Y/N to indicate whether this trade is exempted from<br>clearing. If set to 'N', then a NoPartyID group with<br>PartyRole='4' is required.<br>Required if ExecutionVenueType<7611>='1'.                                                                                                                                                      |
| 7619      | PersonStatus         | int        | C    | US person status of the other entity per SEF account<br>data:<br>• '1' = US - in scope for Dodd Frank<br>• '2' = NON-US - out of scope fort Dodd Frank<br>Required if ExecutionVenueType<7611>='1.                                                                                                                                          |
| 453       | NoPartyIDs           | NumInGroup | C    | Number of iterations of the NoPartyID repeating<br>group. Below is table 7.2 which indicates the possible<br>combination of values for the 3 repeating groups to be<br>expected.<br>Required if ExecutionVenueType<7611>='1' or '3'.                                                                                                        |
| → 448     | PartyID              | String     | C    | 360T company name or MIC of the participant.                                                                                                                                                                                                                                                                                                |
| → 447     | PartyIDSource        | char       | C    | It can contain one of the following values:<br>• 'D' = 'Proprietary custom code'<br>• 'G' = 'MIC'<br>• 'N' = 'LegalEntityIdentifier'                                                                                                                                                                                                        |
| → 452     | PartyRole            | int        | C    | It can be one of the following values:<br>• '1' = 'Executing Firm' for the requester<br>• '13' = 'Order Origination Firm' - The entity,<br>with which the individual initiating the request<br>is associated with. Must be provided if different<br>from requesting entity (PartyRole='1').<br>• '64' = 'MTF'<br>• '73' = 'Execution venue' |
| 1907      | NoRegulatoryTradeIDs | NumInGroup | C    | Repeating Group containing regulatory ID.<br>Required<br>for<br>all<br>IDs<br>if<br>ExecutionVenueType<7611>='3'<br>and<br>for<br>all<br>but<br>TVTIC if ExecutionVenueType<7611>='1'.                                                                                                                                                      |
| →<br>1903 | RegulatoryTradeID    | String     | C    | The regulatory trade id with regard to Regulatory<br>TradeIDType.                                                                                                                                                                                                                                                                           |
|           |                      |            |      | Continued on next page                                                                                                                                                                                                                                                                                                                      |

| Tag                             | Name                      | Type | Req. | Description                                                                                                                                                                            |
|---------------------------------|---------------------------|------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| →<br>1906                       | RegulatoryTradeID<br>Type | int  | C    | Regulatory ID type.<br>• '0' = Current. Default if not set (UTI).<br>• '5'<br>=<br>Trading<br>venue<br>transaction<br>identifier<br>(TVTIC ID)<br>• '6' = Report Tracking Number (RTN) |
| <messagefooter></messagefooter> |                           | Y    |      |                                                                                                                                                                                        |

#### Table 7.1: NewOrderSingle message

<span id="page-19-2"></span>

| PartyID                 | PartyIDSource | PartyIDRole  |
|-------------------------|---------------|--------------|
| MIC value               | 'G'           | '73' or '64' |
| LEI of requester entity | 'N'           | '1'          |
| LEI of TAS entity       | 'N'           | '13'         |

Table 7.2: Party Group possible combinations for the NewOrderSingle message

#### <span id="page-19-0"></span>**7.2.1 Examples**

#### <span id="page-19-1"></span>**7.2.1.1 FX Spot**

Client wants to sell 1m EUR and buy USD.

- Trading day: 2011-11-16
- Notional amount: 1 Mio. EUR
- Order type: STOP
- Stop rate: 1.0815
- TimeInForce: GTC
- Account: ACCOUNT

| Tag                           | Attribute | Value                  |  |
|-------------------------------|-----------|------------------------|--|
| <message header=""></message> |           |                        |  |
| 1                             | Account   | ACCOUNT                |  |
| 11                            | ClOrdID   | 0-*********/1          |  |
| 15                            | Currency  | EUR                    |  |
| 38                            | OrderQty  | 1000000                |  |
|                               |           | continued on next page |  |

| Tag  | Attribute    | Value                  |
|------|--------------|------------------------|
| 40   | OrdType      | 3                      |
| 44   | Price        | 1.0815                 |
| 54   | Side         | 2                      |
| 55   | Symbol       | EUR/USD                |
| 59   | TimeInForce  | 1                      |
| 60   | TransactTime | 2011-11-16<br>11:00:00 |
| 7070 | RefSpotDate  | 2011-11-18             |
| 7071 | ProductType  | FX-SPOT                |

Table 7.3: Order - FX Spot Example

#### <span id="page-20-0"></span>**7.2.1.2 FX Forward**

Client wants to buy 1m EUR and buy USD.

- Trading day: 2011-11-16
- Notional amount: 1 Mio. EUR
- Settlement date: 2011-11-25 (1W)
- Order type: LIMIT
- Limit rate: 1.0815
- TimeInForce: GTD
- Account: ACCOUNT

| Tag                           | Attribute    | Value                  |  |
|-------------------------------|--------------|------------------------|--|
| <message header=""></message> |              |                        |  |
| 1                             | Account      | ACCOUNT                |  |
| 11                            | ClOrdID      | 0-*********            |  |
| 15                            | Currency     | EUR                    |  |
| 38                            | OrderQty     | 1000000                |  |
| 40                            | OrdType      | 2                      |  |
| 44                            | Price        | 1.0815                 |  |
| 54                            | Side         | 1                      |  |
| 55                            | Symbol       | EUR/USD                |  |
| 59                            | TimeInForce  | 6                      |  |
| 60                            | TransactTime | 2011-11-16<br>12:00:00 |  |
| 64                            | SettlDate    | 2011-11-25             |  |
| continued on next page        |              |                        |  |

| Tag  | Attribute   | Value                  |
|------|-------------|------------------------|
| 126  | ExpireTime  | 2011-11-16<br>21:00:00 |
| 7070 | RefSpotDate | 2011-11-18             |
| 7071 | ProductType | FX-FWD                 |

Table 7.4: Order - FX Forward Example

#### <span id="page-21-0"></span>**7.3 New Order List [E]**

(360T → bank)

To submit a multileg order to the bank.

| Tag   | Name                            | Type       | Req. | Description                                                                                                                                              |
|-------|---------------------------------|------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------|
|       | <messageheader></messageheader> |            | Y    | MsgType <35> = E                                                                                                                                         |
| 66    | ListID                          | String     | Y    | Contains the unique reference ID of this collection of<br>orders assigned by 360T.                                                                       |
| 69    | ListExecInst                    | String     | N    | Supported values:<br>• OCO_ORDER<br>• IFDONE_ORDER                                                                                                       |
| 394   | BidType                         | String     | Y    | Not used, must be set to '3' = NoBid.                                                                                                                    |
| 68    | TotNoOrders                     | int        | Y    | Indicates the number of orders. Since Fragmentation<br>is not supported, this field should match the value of<br>NoOrders.                               |
| 73    | NoOrders                        | NumInGroup | Y    | Number of orders in the block. For OCO should be 2.<br>For IFDONE with OCO in second leg, it should be 3,<br>otherwise 2.                                |
| → 67  | ListSeqNo                       | int        | Y    | Order number within the list, starting at 1 and incre<br>menting until TotNoOrders is reached.                                                           |
| → 583 | ClOrdLinkI                      | String     | C    | Parent order id which the leg belongs to. This is re<br>quired for IfDone orders with OCO in the second leg.<br>And it should point to OCO parent id.    |
| → 1   | Account                         | String     | Y    | Defines the legal entity of the counterparty.                                                                                                            |
| → 11  | ClOrdID                         | String     | Y    | Unique execution identifier assigned by 360T.                                                                                                            |
| → 15  | Currency                        | String     | Y    | Notional currency in which the quantities are defined.<br>The currencies for all orders should match.                                                    |
| → 38  | OrderQty                        | Qty        | Y    | Defines the notional amount in the notional currency<br>for which the customer tried to execute.<br>In case of<br>Future, it is the number of contracts. |
|       |                                 |            |      | Continued on next page                                                                                                                                   |

| Tag  | Name              | Type    | Req. | Description                                                                                                                                                                                                                                                                                        |
|------|-------------------|---------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| → 40 | OrdType           | Char    | Y    | Defines the type of order. Possible values:                                                                                                                                                                                                                                                        |
|      |                   |         |      | • '1' = Market                                                                                                                                                                                                                                                                                     |
|      |                   |         |      | • '2' = Limit                                                                                                                                                                                                                                                                                      |
|      |                   |         |      | • '3' = Stop                                                                                                                                                                                                                                                                                       |
| → 44 | Price             | Price   | C    | For Stop orders this is the stop rate, for Limit orders -<br>the limit rate.<br>Limit rates are only supported for FX Outrights<br>(Spot/Forward) and FX Swaps.<br>For FX Swaps, the<br>limit rate refers to the swap points, for all other prod<br>ucts, the limit rate refers to the spot price. |
| → 53 | Side              | Char    | Y    | Defines if the requester has bought or sold the given<br>symbol.2 Possible values:                                                                                                                                                                                                                 |
|      |                   |         |      | • '1' = Buy                                                                                                                                                                                                                                                                                        |
|      |                   |         |      | • '2' = Sell                                                                                                                                                                                                                                                                                       |
| → 55 | Symbol            | String  | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2. The symbols for all orders should match.                                                                                                                                                                                           |
| → 64 | SettlDate         | UTCDate | C    | Settlement date, UTC date in YYYYMMDD format.<br>Provided only when ProductType=FX-FWD. Other<br>wise rolling spot is assumed. See 7.1 for more details<br>on settlement and spot dates.                                                                                                           |
| → 18 | ExecInst          | String  | C    | Order type that the leg belongs to. This is required for<br>IfDone orders with OCO in the second leg. Possible<br>values:<br>• OCO_ORDER                                                                                                                                                           |
|      | →7070 RefSpotDate | UTCDate | Y    | Reference spot rate. This field contains the spot date<br>for the specified currency couple as it is in the 360T<br>platform. See 7.1 for more details on settlement and<br>spot dates.                                                                                                            |
|      | →7071 ProductType | String  | Y    | The product type inside the order. Possible values:                                                                                                                                                                                                                                                |
|      |                   |         |      | • FX-SPOT                                                                                                                                                                                                                                                                                          |
|      |                   |         |      | • FX-FWD                                                                                                                                                                                                                                                                                           |
|      |                   |         |      | • FX-FUT                                                                                                                                                                                                                                                                                           |
|      |                   |         |      | Continued on next page                                                                                                                                                                                                                                                                             |

7.3

<span id="page-22-0"></span><sup>2</sup>The Side<54> tag defines the direction of the Symbol, i.e. the side of the first currency in the symbol and not of the notional currency.

| Tag                             | Name           | Type         | Req. | Description                                                                                                                                                                                                                     |  |  |
|---------------------------------|----------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
| 59                              | TimeInForce    | Char         | Y    | Defines the expiration of the order. Possible values:                                                                                                                                                                           |  |  |
|                                 |                |              |      | • '1' = Good Till Cancel (GTC)                                                                                                                                                                                                  |  |  |
|                                 |                |              |      | • '6' = Good Till Date (GTD)                                                                                                                                                                                                    |  |  |
| 60                              | TransactTime   | UTCTimestamp | Y    | Time when the trade was executed.                                                                                                                                                                                               |  |  |
| 126                             | ExpireTime     | UTCTimestamp | C    | Expiration<br>time<br>of<br>the<br>order<br>in<br>the<br>format<br>YYYYMMDD-HH:MM:SS. Conditionally required<br>if TimeInForce=GTD.                                                                                             |  |  |
| 7078                            | PartialAllowed | Boolean      | N    | Indicates if partial fills are allowed.<br>If this field is<br>missing, 'Y' is implied.<br>Should this field be set to<br>'N', the order needs to be completed in one fill until<br>the expiration defined in TimeInForce (59). |  |  |
| <messagefooter></messagefooter> |                |              | Y    |                                                                                                                                                                                                                                 |  |  |

Table 7.5: NewOrderList message

### <span id="page-23-0"></span>**7.4 Cancel Order [F]**

(360T → bank)

To submit a withdrawal request to the bank.

| Tag | Name                            | Type   | Req. | Description                                                                                                                                                                                       |  |  |  |  |
|-----|---------------------------------|--------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|
|     | <messageheader></messageheader> |        | Y    | MsgType <35> = F                                                                                                                                                                                  |  |  |  |  |
| 1   | Account                         | String | Y    | Defines the legal entity of the counterparty.                                                                                                                                                     |  |  |  |  |
| 11  | ClOrdID                         | String | Y    | Unique identifier assigned by 360T.                                                                                                                                                               |  |  |  |  |
| 40  | OrdType                         | Char   | C    | Defines the type of order. Skipped in case of an OCO<br>and IfDone. Possible values:<br>• '1' = Market<br>• '2' = Limit<br>• '3' = Stop                                                           |  |  |  |  |
| 38  | OrderQty                        | Qty    | C    | Defines the notional amount in the notional currency<br>for which the customer tried to execute.<br>In case of<br>Future, it is the number of contracts. Skipped in case<br>of an OCO and IfDone. |  |  |  |  |
| 41  | OrigClOrdID                     | String | Y    | Client ClOrdID or ListID of Order being canceled.<br>In case of block orders (e.g. OCO), ListID must be<br>provided for the whole block.                                                          |  |  |  |  |
|     | Continued on next page          |        |      |                                                                                                                                                                                                   |  |  |  |  |

| Tag                             | Name         | Type         | Req. | Description                                                                                                                                                                        |
|---------------------------------|--------------|--------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 54                              | Side         | Char         | Y    | Defines if the requester has bought or sold the given<br>symbol. Possible values:<br>• '1' = Buy<br>• '2' = Sell<br>• 'B' = As Defined (for OCO and IfDone order<br>cancellations) |
| 55                              | Symbol       | String       | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2.                                                                                                                    |
| 60                              | TransactTime | UTCTimestamp | Y    | Transaction time.                                                                                                                                                                  |
| <messagefooter></messagefooter> |              |              | Y    |                                                                                                                                                                                    |

Table 7.6: Cancel Order message

### <span id="page-24-0"></span>**7.5 Execution Report [8]**

#### (bank → 360T)

To update the status of an order, including details of the fill-status.

For OCO orders, an execution report is sent only for the order which is being partially filled or filled. In case of a partial fill or a complete fill (OrdStatus=1 or 2) of one side of an OCO, no cancellation report should be sent for the reduced amount on the order side. Any execution reports with any other status (OrdStatus=0, 3, 4, 6, 8, A or C) should include the ListID from NewOrderList in both fields ListID and ClOrdID. Cancelations will be requested only for the whole OCO block and not for an order alone that is part of the block. Fields like Symbol, Side, SettlDate, ProductType, OrderQty, etc. should be populated in the context of the leg that's being filled.

For instance, an OCO order is placed (35=E) where the ListId is OCO-123, the ClOrdID of the first leg is OCO-L1, the second leg - OCO-L2 and the amount on both legs is 100K. Additionally a cancellation may be sent (35=F) with the ClOrdID=OCO-123-cancel and OrigClOrdID=OCO-123. Then the following combinations of tags would be acceptable in an Execution Report (35=8).

| OrdStatus<br><39> | ClOrdID<br><11>    | ListID<br><66> | LastQty<br><32> | OrigClOrdID<br><32> | Description                                               |
|-------------------|--------------------|----------------|-----------------|---------------------|-----------------------------------------------------------|
| A                 | OCO-123            | OCO-123        | 0               | -                   | OCO order is received and will be processed.              |
| 0                 | OCO-123            | OCO-123        | 0               | -                   | OCO order is accepted.                                    |
| 8                 | OCO-123            | OCO-123        | 0               | -                   | OCO order is rejected.                                    |
| 6                 | OCO-123-<br>cancel | OCO-123        | 0               | OCO-123             | Cancelation request is recieved and will be<br>processed. |
| 4                 | OCO-123-<br>cancel | OCO-123        | 0               | OCO-123             | OCO order was canceled.                                   |
| C                 | OCO-123            | OCO-123        | 0               | -                   | OCO order has expired.                                    |
|                   |                    |                |                 |                     |                                                           |

| OrdStatus<br><39> | ClOrdID<br><11> | ListID<br><66> | OrigClOrdID<br><41> | OrigClOrdID<br><32> | Description                                                   |
|-------------------|-----------------|----------------|---------------------|---------------------|---------------------------------------------------------------|
| 1                 | OCO-L1          | OCO-123        | 100K                | -                   | Single partial fill with the full amount on the<br>first leg. |
| 1                 | OCO-L1          | OCO-123        | 50K                 | -                   | Partial fill on the first leg.                                |
| 1                 | OCO-L2          | OCO-123        | 50K                 | -                   | Partial fill on the second leg.                               |
| 2                 | OCO-123         | OCO-123        | 0                   | -                   | OCO order is finalized and booked.                            |

|  |  | Table 7.7: Expected values for OCO execution reports |  |
|--|--|------------------------------------------------------|--|
|  |  |                                                      |  |

| Tag                             | Name     | Type     | Req.             | Description                                                                                                                                                                                                                                                                                                                                              |
|---------------------------------|----------|----------|------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |          | Y        | MsgType <35> = 8 |                                                                                                                                                                                                                                                                                                                                                          |
| 1                               | Account  | String   | Y                | Defines the legal entity of the counterparty.                                                                                                                                                                                                                                                                                                            |
| 6                               | AvgPx    | Price    | Y                | Average price of all fills for the order.<br>For new or<br>expired orders without fills this should be set to 0.                                                                                                                                                                                                                                         |
| 11                              | ClOrdID  | String   | Y                | 360T's order ClOrdID or ListId. In case of cancella<br>tions this is the ClOrdID of the cancellation request.<br>For OCO and IfDone orders ClOrdID of the specific<br>leg being executed should be provided if OrdStatus is<br>Partially Filled <1> or Filled <2>. Orderwise ListId<br>should be provided or the ClOrdID of the cancellation<br>request. |
| 17                              | ExecID   | String   | Y                | Unique execution identifier assigned by the bank.                                                                                                                                                                                                                                                                                                        |
| 14                              | CumQty   | Qty      | Y                | The amount that has been filled so far.                                                                                                                                                                                                                                                                                                                  |
| 15                              | Currency | Currency | Y                | Notional currency in which the quantities are defined.                                                                                                                                                                                                                                                                                                   |
| 31                              | LastPx   | Price    | C                | Price of the last fill.<br>Only present in fill/partial fill<br>report                                                                                                                                                                                                                                                                                   |
| 32                              | LastQty  | Qty      | C                | Quantity (e.g. shares) bought/sold on this (last) fill.<br>Only present in fill/partial fill report                                                                                                                                                                                                                                                      |
| 37                              | OrderID  | String   | Y                | Unique Order ID assigned by the bank.                                                                                                                                                                                                                                                                                                                    |
| 38                              | OrderQty | Qty      | N                | Defines the notional amount in the notional currency<br>for which the customer tried to execute.<br>In case of<br>Future, it is the number of contracts.                                                                                                                                                                                                 |
|                                 |          |          |                  | Continued on next page                                                                                                                                                                                                                                                                                                                                   |

| Tag | Name         | Type         | Req. | Description                                                                                                                                                                      |
|-----|--------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 39  | OrdStatus    | Char         | Y    | Order status. Possible Values:                                                                                                                                                   |
|     |              |              |      | • '0' = New                                                                                                                                                                      |
|     |              |              |      | • '1' = Partially filled                                                                                                                                                         |
|     |              |              |      | • '2' = Filled                                                                                                                                                                   |
|     |              |              |      | • '4' = Cancelled                                                                                                                                                                |
|     |              |              |      | • '6' = Pending Cancel                                                                                                                                                           |
|     |              |              |      | • '8' = Rejected                                                                                                                                                                 |
|     |              |              |      | • 'A' = Pending New                                                                                                                                                              |
|     |              |              |      | • 'C' = Expired                                                                                                                                                                  |
| 40  | OrdType      | Char         | Y    | Defines the type of order. For OCO orders, this is the<br>Possible values:                                                                                                       |
|     |              |              |      | • '1' = Market                                                                                                                                                                   |
|     |              |              |      | • '2' = Limit                                                                                                                                                                    |
|     |              |              |      | • '3' = Stop                                                                                                                                                                     |
|     |              |              |      | For IfDone orders, this is the Possible values:                                                                                                                                  |
|     |              |              |      | • '2' = Limit                                                                                                                                                                    |
|     |              |              |      | • '3' = Stop                                                                                                                                                                     |
| 41  | OrigClOrdID  | String       | C    | Required for response to Order Cancel.<br>Denotes<br>360T's order ClOrdID or ListId to be cancelled. For<br>cancellations of OCO and IfDone orders ListId should<br>be provided. |
| 54  | Side         | Char         | Y    | Defines if the requester has bought or sold the given<br>symbol. Possible values:                                                                                                |
|     |              |              |      | • '1' = Buy                                                                                                                                                                      |
|     |              |              |      | • '2' = Sell                                                                                                                                                                     |
| 55  | Symbol       | String       | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2.                                                                                                                  |
| 58  | Text         | String       | N    | Error message. Present in order reject only.                                                                                                                                     |
| 60  | TransactTime | UTCTimestamp | Y    | Time of transaction.                                                                                                                                                             |
|     |              |              |      | Continued on next page                                                                                                                                                           |

7.5

| Tag  | Name              | Type    | Req. | Description                                                                                                             |
|------|-------------------|---------|------|-------------------------------------------------------------------------------------------------------------------------|
| 64   | SettlDate         | UTCDate | C    | Effective date of the order for forward and spot re<br>quests, UTC date in YYYYMMDD format. Required<br>for ExecType=F. |
| 66   | ListID            | String  | C    | 360T's order ListID in case of an OCO or IfDone or<br>der.                                                              |
| 103  | OrdRejReason      | String  | N    | Error code. Present in order reject only.                                                                               |
| 150  | ExecType          | Char    | Y    | Execution type. Possible Values:                                                                                        |
|      |                   |         |      | • '0' = New                                                                                                             |
|      |                   |         |      | • '4' = Cancelled                                                                                                       |
|      |                   |         |      | • '6' = Pending Cancel                                                                                                  |
|      |                   |         |      | • '8' = Rejected                                                                                                        |
|      |                   |         |      | • 'A' = Pending New                                                                                                     |
|      |                   |         |      | • 'C' = Expired                                                                                                         |
|      |                   |         |      | • 'I' = Order Status                                                                                                    |
|      |                   |         |      | • 'F' = Trade (partial fill or fill)                                                                                    |
| 151  | LeavesQty         | Qty     | Y    | Quantity remaining of this order.                                                                                       |
| 194  | LastSpotRate      | Price   | C    | Fill Spot Rate. Only present in the fill/partial fill re<br>port                                                        |
| 195  | LastForwardPoints | Price   | C    | Fill ForwardPoints. Only present in the fill/partial fill<br>report                                                     |
| 790  | OrdStatusReqID    | String  | C    | Required for response to an Order Status Request<br>message.                                                            |
| 7071 | ProductType       | String  | Y    | The product type inside the order. Possible values:                                                                     |
|      |                   |         |      | • FX-SPOT                                                                                                               |
|      |                   |         |      | • FX-FWD                                                                                                                |
|      |                   |         |      | • FX-FUT                                                                                                                |
| 7078 | PartialAllowed    | Boolean | C    | Indicates if partial fills are allowed. Echoed from the<br>order. Required if set to 'N' in the order.                  |
| 7653 | UTIID             | String  | C    | UTI ID assigned by the provider for this fill.                                                                          |
|      |                   |         |      | Continued on next page                                                                                                  |

| Tag                             | Name               | Type       | Req. | Description                                                                                                                                                                                                                                           |
|---------------------------------|--------------------|------------|------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 453                             | NoPartyIDs         | NumInGroup | C    | Number of iterations of the NoPartyID repeating<br>group. Below is table 7.9 which indicates the possible<br>combination of values for the 2 repeating groups to be<br>expected.<br>Required if ExecutionVenueType<7611>='3'.                         |
| → 448                           | PartyID            | String     | C    | Investment or execution decision maker's ID.                                                                                                                                                                                                          |
| → 447                           | PartyIDSource      | char       | C    | It can contain one of the following values:<br>• 'D' = 'Proprietary custom code' (for algo<br>rithms)<br>• 'P' = 'Short code identifier' (for natural per<br>sons)                                                                                    |
| → 452                           | PartyRole          | int        | C    | It can be one of the following values:<br>• '12' = 'Executing trader' The person or algorith<br>responsible for executing the trade<br>• '122' = 'Investment decision maker' The per<br>son or algorithm responsible for confirming the<br>execution. |
| →<br>2376                       | PartyRoleQualifier | int        | C    | It can be one of the following values:<br>• '22' = 'Algorithm'<br>• '24' = 'Natural person'                                                                                                                                                           |
| 1815                            | TradingCapacity    | String     | C    | Should always be 'DEAL'.<br>Required if ExecutionVenueType<7611>='3'.                                                                                                                                                                                 |
| <messagefooter></messagefooter> |                    | Y          |      |                                                                                                                                                                                                                                                       |

7.5

Table 7.8: ExecutionReport message

<span id="page-28-0"></span>

| PartyID                              | PartyIDSource | PartyRole | PartyRoleQualifier |
|--------------------------------------|---------------|-----------|--------------------|
| Execution<br>decision<br>maker<br>ID | 'P' or 'D'    | '12'      | '24' or '22'       |
| Investment decision maker<br>ID      | 'P' or 'D'    | '122'     | '24' or '22'       |

Table 7.9: Party Group possible combinations for the ExecutionReport message

#### <span id="page-29-0"></span>**7.5.1 Examples**

#### <span id="page-29-1"></span>**7.5.1.1 FX Spot**

Client wants to sell 1m EUR and buy USD.

- Trading day: 2011-11-16
- Notional amount: 1 Mio. EUR
- Settlement date: 2011-11-18 (Spot)
- Order type: STOP
- Stop rate: 1.0815
- Account: ACCOUNT
- Status: NEW

| Tag                           | Attribute    | Value                                   |  |
|-------------------------------|--------------|-----------------------------------------|--|
| <message header=""></message> |              |                                         |  |
| 1                             | Account      | ACCOUNT                                 |  |
| 6                             | AvgPx        | 0                                       |  |
| 11                            | ClOrdID      | 0-*********/1                           |  |
| 14                            | CumQty       | 0                                       |  |
| 15                            | Currency     | EUR                                     |  |
| 17                            | ExecID       | Provider<br>Assigned<br>Execution<br>ID |  |
| 37                            | OrderID      | Provider<br>Assigned<br>Order<br>ID     |  |
| 38                            | OrderQty     | 1000000                                 |  |
| 39                            | OrdStatus    | 0                                       |  |
| 40                            | OrdType      | 3                                       |  |
| 54                            | Side         | 2                                       |  |
| 55                            | Symbol       | EUR/USD                                 |  |
| 60                            | TransactTime | 2011-11-16<br>11:00:00                  |  |
| 64                            | SettlDate    | 2011-11-18                              |  |
| 150                           | ExecType     | 0                                       |  |
| 151                           | LeavesQty    | 1000000                                 |  |
| 7071                          | ProductType  | FX-SPOT                                 |  |

Table 7.10: Execution Report - FX Spot Example

#### <span id="page-29-2"></span>**7.5.1.2 FX Forward**

Client wants to buy 1m EUR and buy USD.

- Trading day: 2011-11-16
- Notional amount: 1 Mio. EUR
- Settlement date: 2011-11-25 (1W)
- Order type: LIMIT
- Limit rate: 1.0815
- Account: ACCOUNT
- Status: Partial Fill
- Fill: 200000 / 1.08011
- Forward points: 1.1
- Spot rate: 1.08

| Tag                           | Attribute         | Value                                   |  |
|-------------------------------|-------------------|-----------------------------------------|--|
| <message header=""></message> |                   |                                         |  |
| 1                             | Account           | ACCOUNT                                 |  |
| 6                             | AvgPx             | 1.08                                    |  |
| 11                            | ClOrdID           | 0-*********                             |  |
| 14                            | CumQty            | 200000                                  |  |
| 15                            | Currency          | EUR                                     |  |
| 17                            | ExecID            | Provider<br>Assigned<br>Execution<br>ID |  |
| 31                            | LastPx            | 1.08011                                 |  |
| 32                            | LastQty           | 200000                                  |  |
| 37                            | OrderID           | Provider<br>Assigned<br>Order<br>ID     |  |
| 38                            | OrderQty          | 1000000                                 |  |
| 39                            | OrdStatus         | 1                                       |  |
| 40                            | OrdType           | 2                                       |  |
| 54                            | Side              | 1                                       |  |
| 55                            | Symbol            | EUR/USD                                 |  |
| 60                            | TransactTime      | 1                                       |  |
| 64                            | SettlDate         | 2011-11-25                              |  |
| 150                           | ExecType          | F                                       |  |
| 151                           | LeavesQty         | 800000                                  |  |
| 194                           | LastSpotRate      | 1.08                                    |  |
| 195                           | LastForwardPoints | 1.1                                     |  |
| 7071                          | ProductType       | FX-FWD                                  |  |

Table 7.11: Execution Report - FX Forward Example

### <span id="page-31-0"></span>**7.6 Order Cancel Reject [9]**

#### (bank → 360T)

The bank can send this message to reject a withdrawal request.

| Tag                             | Name             | Type         | Req.             | Description                                                                                                     |
|---------------------------------|------------------|--------------|------------------|-----------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                  | Y            | MsgType <35> = 9 |                                                                                                                 |
| 11                              | ClOrdID          | String       | Y                | 360T's ClOrdID of the related CancelOrder message.                                                              |
| 41                              | OrigClOrdID      | String       | Y                | The ClOrdID or ListID which could not be cancelled.<br>This is the OrigClOrdID from the CancelOrder mes<br>sage |
| 37                              | OrderID          | String       | Y                | Order ID assigned by the bank.                                                                                  |
| 39                              | OrderStatus      | Char         | Y                | Status of the Order Cancel Request, not of any order.<br>Possible values:<br>• '8' = Rejected                   |
| 54                              | Side             | Char         | N                | Defines if the requester has bought or sold the given<br>symbol. Possible values:<br>• '1' = Buy                |
|                                 |                  |              |                  | • '2' = Sell                                                                                                    |
|                                 |                  |              |                  | • 'B' = As Defined (for OCO and IfDone order<br>cancellations)                                                  |
| 55                              | Symbol           | String       | N                | Contains the delivered currency pair in the format:<br>CC1/CC2.                                                 |
| 58                              | Text             | String       | N                | Rejection message.                                                                                              |
| 60                              | TransactTime     | UTCTimestamp | N                | Time of transaction.                                                                                            |
| 102                             | CxlRejReason     | String       | N                | Error code.                                                                                                     |
| 434                             | CxlRejResponseTo | Char         | Y                | Possible values:                                                                                                |
|                                 |                  |              |                  | • '1' = Order Cancel Request                                                                                    |
| <messagefooter></messagefooter> |                  |              | Y                |                                                                                                                 |

Table 7.12: Order Cancel Reject message

### <span id="page-31-1"></span>**7.7 Order Status [H]**

(360T → bank)

360T sends this message to the bank to query the status of an order. The bank should respond back with one execution report with OrdStatusReqID echoing back the id in this message, ExecType (150) = I and OrdStatus with the latest status of the order.

| Tag                             | Name           | Type    | Req.             | Description                                                                                                                                                                             |
|---------------------------------|----------------|---------|------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                | Y       | MsgType <35> = H |                                                                                                                                                                                         |
| 11                              | ClOrdID        | String  | Y                | The ClOrdID or ListID of the order whose status is<br>being requested.                                                                                                                  |
| 55                              | Symbol         | String  | Y                | Contains the delivered currency pair in the format:<br>CC1/CC2.                                                                                                                         |
| 54                              | Side           | Char    | Y                | Defines if the requester has bought or sold the given<br>symbol. Possible values:<br>• '1' = Buy<br>• '2' = Sell<br>• 'B' = As Defined (for OCO order cancellations)                    |
| 790                             | OrdStatusReqID | String  | Y                | Unique identifier assigned by 360T.                                                                                                                                                     |
| 7070                            | RefSpotDate    | UTCDate | Y                | Reference spot rate. This field contains the spot date<br>for the specified currency couple as it is in the 360T<br>platform. See 7.1 for more details on settlement and<br>spot dates. |
| <messagefooter></messagefooter> |                | Y       |                  |                                                                                                                                                                                         |

For OCO orders this request should echo back the latest execution report for whichever order in the block was executed.

7.8

#### Table 7.13: Order Status message

### <span id="page-32-0"></span>**7.8 Don't Know Trade [Q]**

(bank → 360T)

The bank may send this message to reject an order status request for an unknown order id. Should 360T receive such a rejection, the order in the 360T system will be canceled immediately.

| Tag | Name                            | Type   | Req. | Description                                                                                                                                              |
|-----|---------------------------------|--------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------|
|     | <messageheader></messageheader> |        | Y    | MsgType <35> = Q                                                                                                                                         |
| 17  | ExecID                          | String | Y    | Random identifier string.                                                                                                                                |
| 37  | OrderID                         | String | Y    | The ClOrdID from the OrderStatus.                                                                                                                        |
| 38  | OrderQty                        | Qty    | N    | Defines the notional amount in the notional currency<br>for which the customer tried to execute.<br>In case of<br>Future, it is the number of contracts. |
|     | Continued on next page          |        |      |                                                                                                                                                          |

| Tag                             | Name           | Type   | Req. | Description                                                                       |
|---------------------------------|----------------|--------|------|-----------------------------------------------------------------------------------|
| 54                              | Side           | Char   | Y    | Defines if the requester has bought or sold the given<br>symbol. Possible values: |
|                                 |                |        |      | • '1' = Buy                                                                       |
|                                 |                |        |      | • '2' = Sell                                                                      |
|                                 |                |        |      | • 'B' = As Defined (for OCO order cancellations)                                  |
| 55                              | Symbol         | String | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2.                   |
| 127                             | DKReason       | String | Y    | Reason for rejection. Must be set to 'D' = No match<br>ing order                  |
| 790                             | OrdStatusReqID | String | Y    | Unique identifier assigned by 360T. Echoed from Or<br>derStatus.                  |
| <messagefooter></messagefooter> |                |        | Y    |                                                                                   |

Table 7.14: Don't Know Trade message

### <span id="page-33-0"></span>**7.9 Business Message Reject [j]**

(360T → bank & bank → 360T)

In case of missing conditionally required fields or other validation failures, 360T may reject messages with a BusinessMessageReject. WARNING: Such a rejection of an ExecutionReport results in blocking the provider. When the issue is resolved, the block can be lifted manually by 360T CAS.

The bank can reject messages with a BusinessMessageReject. This is strongly discouraged as it would cause failure in order upload and may trigger an automatic cancellation in already submitted orders. Rejecting with the proper corresponding response message is preferred. A BMR for an NewOrderSingle will result in a failed order upload. A BMR for an OrderStatus may trigger the referenced order to be triggered. A BMR for an OrderCancel-Request results in cancellation failure. Please note that the BusinessRejectRefID field is required.

| Tag                             | Name                            | Type   | Req. | Description                                                                 |
|---------------------------------|---------------------------------|--------|------|-----------------------------------------------------------------------------|
|                                 | <messageheader></messageheader> |        | Y    | MsgType <35> = j                                                            |
| 45                              | RefSeqNum                       | String | N    | MsgSeqNum <34> of rejected message                                          |
| 58                              | Text                            | String | N    | Detailed explanation.                                                       |
| 372                             | RefMsgType                      | String | Y    | The MsgType <35> of the FIX message being refer<br>enced.                   |
| 379                             | BusinessRejectRefID String      |        | Y    | ClOrdID<br>from<br>NewOrderSingle,<br>CancelOrderRe<br>quest or OrderStatus |
| 380                             | BusinessRejectReasonChar        |        | Y    | Reason for rejection.                                                       |
| <messagefooter></messagefooter> |                                 |        | Y    |                                                                             |

Table 7.15: Don't Know Trade message

### <span id="page-34-0"></span>**8 Migration from older versions**

This part of the document describes the details of the specification changes between versions. It's supposed to help application developers migrating to a new specification version.

### <span id="page-34-1"></span>**8.1 From version 1 to Version 2**

- The value for Limit orders in the tag OrdType<40> has changed from FOREX\_LIMIT ('F') to LIMIT ('2')
- ExecType values PARIAL\_FILL (1) and FILLED (2) are replaced with TRADE (F). PENDING\_NEW and PENDING\_CANCEL added to OrdStatus and ExecType.
- PartialAllowed (7078) added to order and execution report
- New fields in order and execution report related spot rate, forward points, product type and settlement date
- Added support for OrderStatus, DontKnowTrade and BusinessMessageReject

#### <span id="page-34-2"></span>**8.2 From version 2 to Version 3**

- SettlDate is conditionally required in NewOrderSingle
- UTIID is added to ExecutionReport
- PENDING\_NEW is added to ExecutionReport

#### <span id="page-34-3"></span>**8.3 From version 3 to Version 4**

- NewOrderList message added for OCO orders
- Added ListID ExecutionReport and extended the descriptions of ClOrdID and OrigClOrdID
- Added Side=B in relation to OCO order in cancel and status requests
- Explanation on ExecutionReport, OrderStatus and OrderCancel extended

#### <span id="page-34-4"></span>**8.4 From version 4 to Version 5**

- RefSpotDate added to NewOrderSingle, NewOrderList and OrderStatus messages
- Explanation on ExecutionReport with regard to OCO orders extended

### <span id="page-35-0"></span>**9 Firewall configuration**

For the connections we use the following IP addresses. The ports will be provided by 360T.

| ❤❤❤❤❤❤<br>❤❤❤❤❤❤<br>Environment<br>Connection<br>❤ | Production      | Integration    |
|----------------------------------------------------|-----------------|----------------|
| Internet<br>(plain)                                | *************   | ************   |
| Stunnel                                            | *************   | ************   |
| Radianz                                            | *************** | ************** |

Table 9.1: 360T IP addresses

### <span id="page-36-0"></span>**10 FIX Session Reset**

By default, a FIX session reset is performed according to the schedule defined in the following table.

| Setting                                                                          | Description                                                    | Default<br>Value |
|----------------------------------------------------------------------------------|----------------------------------------------------------------|------------------|
| Timezone                                                                         | Time zone to be used for the session schedule.                 | America/New York |
| Start Time                                                                       | Time of day that this FIX session becomes activated.           | 17:01:00         |
| End Time                                                                         | Time of day that this FIX session becomes deactivated.         | 17:00:00         |
| Start Day                                                                        | Starting day of week for the session.                          | Saturday         |
| End Day                                                                          | Ending day of week for the session.                            | Saturday         |
| Reset On Logon                                                                   | Sequence number is reset when recieving a logon request.       | Yes              |
| Reset On Logout                                                                  | Sequence number is reset to 1 after normal logout termination. | Yes              |
| Reset On Disconnect<br>Sequence number is reset to 1 after abnormal termination. |                                                                | Yes              |

Table 10.1: 360T FIX Session

### <span id="page-37-0"></span>**11 Version Log**

| Version | Date       | Comments                                                                                                               |
|---------|------------|------------------------------------------------------------------------------------------------------------------------|
| 1.0     | 09.09.2013 | Added initial version                                                                                                  |
| 1.1     | 08.10.2013 | Added Market orders support                                                                                            |
| 1.1.1   | 21.08.2014 | Updated description of tag 64 and tag 6 in the Execution Report table. Updated Execu<br>tion Report table description. |
| 2.0     | 09.02.2015 | Added Support for Forward Products and Order Status Request messages.                                                  |
| 2.0.1   | 20.11.2015 | Add Timeout procedure                                                                                                  |
| 2.0.2   | 11.01.2016 | Example messages                                                                                                       |
| 2.1     | 03.02.2016 | Change OrdType for Limit order from FOREX_LIMIT to LIMIT                                                               |
| 2.2     | 08.02.2016 | Deprecate ExecType FILLED and PARTIALLY_FILLED. Make SettlementDate con<br>ditionally required in ExecutionReports.    |
| 2.3     | 18.03.2016 | Added partial filling indicator. Improved description of fill requirements.                                            |
| 2.4     | 24.05.2016 | Improved flow charts.                                                                                                  |
| 2.5     | 01.08.2016 | Improved examples                                                                                                      |
| 3.0     | 20.01.2017 | Corrections to the settlement date field in orders                                                                     |
| 3.1     | 25.04.2017 | Add UTIID to the execution reports                                                                                     |
| 3.2     | 19.09.2017 | Added PENDING_NEW and PENDING_CANCEL state.                                                                            |
| 4.0     | 14.05.2018 | Added OCO orders.                                                                                                      |
| 4.1     | 21.05.2018 | Improvements of OCO descriptions.                                                                                      |
| 4.2     | 14.12.2018 | Change required fields on OrderCancelReject                                                                            |
| 5.0     | 17.12.2018 | Added reference spot rate and OCO response details.                                                                    |
| 5.1     | 07.02.2022 | Added support for Future product.                                                                                      |
| 6.0     | 09.05.2022 | Updated New Order List for ifDone orders.                                                                              |
| 6.0.1   | 09.05.2023 | Clarified usage of limit rate                                                                                          |
| 6.1     | 31.07.2023 | Added FixingReference and FixingDate to the NewOrderMessage.                                                           |
| 7.0     | 18.11.2024 | Added support for single order MTF and SEF regulatory requirements.                                                    |

Table 11.1: Version history