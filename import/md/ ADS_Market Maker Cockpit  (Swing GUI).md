# **360T AUTODEALING SUITE (ADS)**

![](_page_0_Picture_1.jpeg)

# **MARKET MAKER COCKPIT (MMC)**

# **TO ENABLE**

# **PRICE MAKING, DISTRIBUTION AND RISK MANAGEMENT**

© 360 TREASURY SYSTEMS AG, 2015 THIS FILE CONTAINS PROPRIETARY AND CONF<PERSON><PERSON><PERSON><PERSON> INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG

| 1 |            | INTRODUCTION                                              | 6        |
|---|------------|-----------------------------------------------------------|----------|
|   | 1.1        | MMC<br>COMPONENTS                                         | 7        |
|   | 1.2        | PRICING MODEL<br>                                         | 7        |
|   | 1.3        | OVERALL DATA FLOW<br>                                     | 7        |
|   | 1.4        | SUPPORTED PRODUCTS                                        | 8        |
| 2 |            | MARKET MAKER COCKPIT OVERVIEW<br>                         | 9        |
|   | 2.1        | LOGIN AND LOGOUT                                          | 10       |
|   | 2.2        | PANELS,<br>TABS AND TAB CONTAINER                         | 10       |
|   | 2.3        | RE-ARRANGING TABS                                         | 11       |
|   | 2.4        | MODIFYING TABS AND TAB CONTAINERS<br>                     | 11       |
| 3 |            | CONFIGURE PRICING<br>                                     | 12       |
|   |            |                                                           |          |
|   | 3.1<br>3.2 | PRICING CONTROL<br>ADD AND REMOVE MANAGED INSTRUMENTS<br> | 12<br>13 |
|   | 3.3        | OWNERSHIP OF A MANAGED INSTRUMENT                         | 14       |
|   | 3.4        | START/STOP PRICING OF INSTRUMENTS AND CHANNELS            | 14       |
|   | 3.5        | PRICING AND RISK MANAGEMENT MODE                          | 14       |
|   | 3.6        | TIER SIZE CONFIGURATION<br>                               | 15       |
|   | 3.7        | INSTRUMENT CONFIGURATION                                  | 16       |
|   | 3.8        | ADDITONAL AND FIXED SPREAD                                | 17       |
|   | 3.9        | MANUAL SKEW<br>                                           | 17       |
|   | 3.10       | ROUNDING OUTBOUND QUOTES<br>                              | 18       |
|   | 3.11       | REFERENCE PRICE FINDING                                   | 19       |
|   | 3.12       | SWEEPABLE AND FULL AMOUNT STREAMS<br>                     | 21       |
|   | 3.13       | MANAGING SYNTHETIC CROSSES<br>                            | 21       |
|   | 3.14       | PRICING OF UNMANAGED<br>INSTRUMENTS                       | 22       |
|   | 3.15       | CONFIGURING TIME SLIPPAGE<br>                             | 23       |
|   | 3.16       | CONFIGURING PRICE SLIPPAGE<br>                            | 24       |
|   | 3.17       | QUOTE FILTERING<br>                                       | 26       |
|   | 3.18       | PTMM                                                      | 28       |
| 4 |            | MONITORING PRICING<br>                                    | 29       |
|   | 4.1        | THE PRICING MONITOR PANEL<br>                             | 29       |
|   | 4.2        | INBOUND AND OUTBOUND PRICING TIER MONITOR                 | 29       |
|   | 4.3        | PRICING DETAILS DIALOG                                    | 30       |
| 5 |            | RISK MANAGEMENT                                           | 33       |
|   |            |                                                           |          |
|   | 5.1        | MONITORING POSITIONS<br>                                  | 33       |
|   | 5.2        | PROFIT AND LOSS (P/L)<br>CALCULATIONS                     | 34       |
|   | 5.3<br>5.4 | RISK MANAGEMENT CONFIGURATION<br>POSITION RULES<br>       | 34<br>35 |
|   | 5.5        | PRICING RULES                                             | 37       |
|   | 5.6        | ALERT RULES<br>                                           | 38       |
|   | 5.7        | MANUAL POSITION AMENDMENTS<br>                            | 39       |
|   | 5.8        | AUTO-HEDGING SAFEGUARDS<br>                               | 40       |
|   | 5.9        | RESTRICT THE BANK BASKET FOR HEDGE ORDERS                 | 41       |
|   | 5.10       | CLIENT ORDER HANDLING RULES<br>                           | 42       |
|   | 5.11       | PRICING AND RISK MANAGEMENT SCENARIOS<br>                 | 44       |
|   |            |                                                           |          |
| 6 |            | BLOTTERS<br>                                              | 46       |

|   | 6.1                             | GENERAL BLOTTER FEATURES<br><br>46          |          |  |  |  |  |  |
|---|---------------------------------|---------------------------------------------|----------|--|--|--|--|--|
|   | 6.2                             | CLIENT ORDER BLOTTER<br>                    |          |  |  |  |  |  |
|   | 6.3                             | HEDGE ORDER BLOTTER                         |          |  |  |  |  |  |
|   | 6.4                             | COMBINED CLIENT AND HEDGE ORDER BLOTTER<br> | 47<br>47 |  |  |  |  |  |
|   | 6.5<br>CLIENT ACITIVITY BLOTTER |                                             |          |  |  |  |  |  |
|   | 6.6                             | TRADE BLOTTER                               | 47<br>48 |  |  |  |  |  |
| 7 |                                 | AUDIT                                       | 49       |  |  |  |  |  |
|   |                                 |                                             |          |  |  |  |  |  |
| 8 |                                 | APPENDIX<br>                                | 49       |  |  |  |  |  |
|   | 8.1                             | JAVA REGULAR EXPRESSION SYNTAX<br>          | 49       |  |  |  |  |  |

# **TABLE OF FIGURES**

| Figure 1 Market Maker Cockpit Overview9                         |  |
|-----------------------------------------------------------------|--|
| Figure 2<br>Login and Start the Market Maker Cockpit10          |  |
| Figure 3 Exit the Market Maker Cockpit10                        |  |
| Figure 4: Pricing Control<br>Panel12                            |  |
| Figure 5 Open Global<br>Instrument Configuration13              |  |
| Figure 6 Global Instrument Configuration dialog13               |  |
| Figure 7: Take over instrument ownership14                      |  |
| Figure 8: Start/Stop Pricing<br>14                              |  |
| Figure 9 Emergency button<br>14                                 |  |
| Figure 10: Select risk management mode<br>15                    |  |
| Figure 11: Global Instrument Configuration Dialog<br>15         |  |
| Figure 12: Open instrument configuration dialog<br>16           |  |
| Figure 13: Instrument Configuration Dialog<br>16                |  |
| Figure 14: Basic skew settings<br>17                            |  |
| Figure 15: Tier specific skew factors18                         |  |
| Figure 16 Allowing Skew to cross Mid Price/ Opposite Side<br>18 |  |
| Figure 17: Reducing outbound price precision19                  |  |
| Figure 18: Reference Price Finding rules editor21               |  |
| Figure 19: Cross Currency Pair Configuration<br>22              |  |
| Figure 20: Pricing unmanaged instruments<br>23                  |  |
| Figure 21: Time slippage configuration24                        |  |
| Figure 22: Example for price slippage<br>25                     |  |
| Figure 23: Price slippage configuration26                       |  |
| Figure 24: Quote filter settings27                              |  |
| Figure 25: Monitor filtered quotes<br>27                        |  |
| Figure 26: Pricing Monitor Panel<br>29                          |  |
| Figure 27: Pricing Tier monitor<br>30                           |  |
| Figure 28: Raw Inbound Quote Details31                          |  |
| Figure 29: Filtered Inbound Quote Details<br>31                 |  |
| Figure 30: Outbound Price Details32                             |  |
| Figure 31: Managed Positions blotter with context menu33        |  |
| Figure 32: Risk management position rules35                     |  |
| Figure 33: Risk management pricing rules38                      |  |
| Figure 34 Alert popup message38                                 |  |
| Figure 35 Context menu of Managed Positions<br>39               |  |
| Figure 36 Amend Position<br>39                                  |  |

| Figure 37 Set Position<br>39                           |  |
|--------------------------------------------------------|--|
| Figure 38 Confirmation of Position Reset<br>39         |  |
| Figure 39 Confirmation of Position Flattening<br>40    |  |
| Figure 40 Maximum Hedge Order Size Configuration<br>40 |  |
| Figure 41: Restrict hedge order bank basket<br>41      |  |
| Figure 42: Manage Client Order Handling Rules<br>43    |  |
| Figure 43: Client Order Handling Rule testing<br>44    |  |
| Figure 44: Pricing and Risk Management Scenarios<br>45 |  |
| Figure 45: Scenario selection<br>45                    |  |
| Figure 46: Client Activity Blotter48                   |  |

### <span id="page-5-0"></span>**1 INTRODUCTION**

The 360T Auto Dealing Suite (ADS) includes Pricing Engine functionality to define outbound prices, based on rates streamed inbound from your liquidity provider(s). The outbound price can be adjusted to include trader spreads, skewing etc. Being passed downstream, further adjustments to these prices like sales or customer spreads can be made by setting destination rules via Auto Dealing Suite (ADS)

The Market Maker Cockpit (MMC) allows automated risk management and pre-set actions with subsequent positions created by customer flows.

Position overviews and profit and loss calculation are provided. Users can set up the currency pairs that will be actively managed as well as define currency pairs for price stripping and/or quote pegged currencies.

*DISCLAIMER:*

*Please note that clients shall be solely responsible for the use of 360T's Market Maker Cockpit ("MMC").* 

*The MMC is a fully operational pricing engine with automated pricing functionalities. Each client using the MMC should be aware that an automated setup in general might lead to unsought trade results, and any use of the MMC requires a certain level of experience, requisite knowledge and skills, constant monitoring of the market and periodical review of all settings. 360T is not in the position to monitor any such activities or settings of the MMC and will under no circumstances interfere with any client's MMC setup.*

*With respect to the MMC, 360T will be in no event, regardless of cause, liable for any direct, indirect, special, incidental, punitive or consequential damages of any kind, whether arising under breach of contract, tort (including negligence), or otherwise, and whether based on this agreement or otherwise, even if advised of the possibility of such damages. This applies in particular to the settings of the MMC, its activation or deactivation and any trade executed (or not made) through the MMC.*

*Between the parties using MMC the client shall be solely responsible for the performance and enforcement of any and all trades resulting from using the MMC. Furthermore the MMC is provided to the client on an "as is" and "as available" basis without warranty of any kind (either express or implied).* 

### <span id="page-6-0"></span>**1.1 MMC Components**

The Market Maker Cockpit consists four major components;

- **Overview:** providing lists of defined currency pairs, views for in- and outbound prices, positions and deal blotters.
- **Pricing Controller:** enabling the creation of core prices for each defined currency pair and adjustments with spreads and/or skewing.
- **Risk Management**, enabling the definition of risk parameters for each currency pair and management rules once triggers and alerts have been breached.
- **Reference Price Finding**, enabling the selection of price provider(s) and pricing tiers etc.

### <span id="page-6-1"></span>**1.2 Pricing Model**

The outbound rate to a client (client rate) for a specific quantity tier is determined through the following stages:

- Trader Rate = Reference Rate + Additional Spread + Skew
- Client Rate = Trader Rate + Sales Margin

This product deals primarily with the calculation of the trader rate. Sales margin is a parameter of the 360T Auto Dealing Suite (ADS), and can be applied individually by client.

#### **Reference Rate:**

If not a fixed rate, the reference rate will be derived via price finding. This is a process which takes into account available market data and liquidity, risk management profile, and risk appetite. The reference rate is always a two-way price of bid and ask price. The difference between these two prices is the inbound spread. The middle of these two prices, is the reference mid-price.

#### **Additional spread:**

Spread is defined as the difference between bid and ask price. The difference between the reference bid and ask rate is the so called inbound spread. This inbound spread is often widened for various reasons.

#### **Skew:**

Skewing means to shift the mid-price (the middle of bid and ask rate) either towards the bid or ask side. A skewed and non-skewed two way price has still the same spread!

Trader spread and skew are parameters either manually set by the trader, but also automatically via rules.

### <span id="page-6-2"></span>**1.3 Overall Data Flow**

Below the general data flow within the MMC:

- 1. Liquidity providers (market makers) send quotes into the system. Each provider sends usually different quote levels. Quotes can be both tradable liquidity, but also pure market data.
- 2. Quotes are filtered and aggregated during Reference Price Finding. The basic idea here is to filter out unwanted quotes and create a trustable pool of liquidity for pricing.

- 3. Traders will manipulate the inbound liquidity and increase the spread and/or skew the price to the left or ride side.
- 4. The outbound price is forwarded to clients via RFS, SST, and FIX API's.
- 5. Clients create orders based on the price provided by the pricing engine. Such orders are routed back to the pricing engine.
- 6. Accepted client orders are added to the MMC positions.
- 7. Based on the configured risk management profile, the system will eventually decide to create a hedge order to reduce or flatten a position. Such hedge orders can be created manually too.
- 8. Trades originating from hedge orders flow back into the pricing engine position.

### <span id="page-7-0"></span>**1.4 Supported Products**

Market Maker Cockpit owner can price incoming requests for following products:

- 1. FX Spot: Any incoming Spot requests which are negotiated as RFS (Request for Streaming) or SEP (Streaming Executable Prices) can be routed to and priced by Market Maker Cockpit. Spot outbound quotes are constructed via Reference Price Finding as described in Section 3 and downstreamed to AutoDealingSuite where it can be adjusted with sales margins and trader spreads.
- 2. FX Forward: MMC can price any incoming FX Forward requests by using an additional market data source. MMC owners can provide their own swap points via an interface to 360T or use 360T`s Swap Data Feed (SDF) market data to price incoming Forward requests via MMC. Once a market data source is defined for pricing engine, spot components of the outright rates are received from Outbound quotes (as described in Section 3) and forward points are received from the defined Market Data Source.
- 3. FX Swap: MMC can price any incoming even and uneven FX Swap requests. Similarly to FX Forwards, the MMC generates the spot rate from the outbound quotes and retrieves the forward points on each leg from the defined forward market data source.
- 4. FX Future: MMC facilitates the generation of Eurex Exchange listed FX Future outbound prices in two ways:
  - It utilizes the outbound FX Spot price in order to form the Outbound FX Future price; the outbound FX Future price matches the outbound FX Spot price
  - The outbound FX Forward price is taken for the generation of the outbound FX Future price.

FX Future prices can be either streamed directly into the Eurex Exchange Central Limit Order Book (CLOB) or as off-book liquidity to 360T customers.

- 5. FX Rolling Spot Futures: MMC can generate off-book or on-book FX Rolling Spot Futures by taking the outbound FX Spot prices.
- 6. FX NDF: MMC can price any incoming NDF requests by using additional market data source for forward points. MMC owners can provide their own swap points data for NDFs via an interface to 360T or using 360T`s Swap Data Feed (SDF). Please note that Spot component of the outright rates are received from Outbound quotes (as described in Section 3) which are Onshore Spot rates.

7. Block-Trades: Similar to FX Forward pricing, MMC can price any incoming Block-Trade requests with many legs. These requests are priced via an additional market data source with forward points information. For spot component, net amount of the block is taken into consideration while determining the side and band size.

### **Please note;**

Never leave the ADS MMC unattended when it was started to actively quote and manage currency positions!

### <span id="page-8-0"></span>**2 MARKET MAKER COCKPIT OVERVIEW**

The Market Maker Cockpit provides an overview of currency pairs being set up, current positions including profit and loss and various blotters to monitor client (requester) and hedge orders.

| $\Box$<br>$\mathbf{x}$<br>$\overline{\phantom{0}}$<br><b>图 Market Maker Cockpit</b>      |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
|------------------------------------------------------------------------------------------|--------------------------------------------------------------------|----------------------------|------------------|-------------------|---------------------|-----------------|--------------------------------------------------------------------|--------------------------|---------------|-------------------------------------------|----------------------------------------------------------------------------------------|--------------------|--------------------------|-----------------|--------------------------------|----------------------------------------------------------------------------|------------------------------------------------------------------|----------------------------------------------------------------------------------------------|--------------|--|
| Eile Tools Help                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
| <b>Pricing Monitor</b>                                                                   | $\Box$<br>$\Theta$ Stop All                                        |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           | 田<br><b>Client Orders</b> Client Activity                                              |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
| Core Channel 1                                                                           |                                                                    |                            |                  |                   | Core Channel 2      |                 |                                                                    |                          |               | Order                                     | Time                                                                                   | <b>CCY</b>         |                          |                 |                                |                                                                            |                                                                  | Status Side Limit Notional Am Notional Cu Executed Amount I                                  |              |  |
| <b>E AUD/USD</b><br>Net Position: 0 EUR P/L: Open 0 EUR / Realized 39 EUR / Total 39 EUR |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               | PE- 03:42:58.608 EUR/USD EXEC Buy 1.27199 |                                                                                        |                    |                          |                 | 1,000,000                      | <b>EUR</b>                                                                 | 1.000.000                                                        |                                                                                              |              |  |
| <b>EUR/USD</b>                                                                           | Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           | PE- 03:42:58.572 USD/JPY EXEC Buy 107.167<br>PE- 03:42:58.616 EUR/JPY EXEC Buy 136.315 |                    |                          |                 |                                | 1.271.986<br>1,000,000                                                     | <b>USD</b><br><b>EUR</b>                                         | 1,271,986<br>1.000.000                                                                       |              |  |
| Inbound                                                                                  | <b>Details</b>                                                     | Outbound                   |                  | Details.          | Inbound             |                 | Details.<br>Outbound                                               |                          | Details.      |                                           | PE- 03:42:30.231 AUD/U EXEC Buy 0.87986                                                |                    |                          |                 |                                | 1,000,000                                                                  | <b>AUD</b>                                                       | 1,000,000                                                                                    |              |  |
| $\overline{12}$<br>$1.27 + 89$<br>1 <sub>m</sub>                                         |                                                                    | $1.2720 +$<br>$1.27 + 8.9$ | 1.2              | 1.27201           | $1.27 + 8.9$        | $\bar{u}$       | 1.27201<br>$1.27$ 189                                              | $\bar{u}$                | 1.2720        |                                           | PE- 03:42:14.397 USD/JPY EXEC Buy 107.158<br>PE- 03:42:02.460 EUR/USD EXEC Buy 1.27194 |                    |                          |                 |                                | 1,000,000<br>2.000.000                                                     | <b>USD</b><br><b>EUR</b>                                         | 1.000.000<br>2.000.000                                                                       |              |  |
| $\overline{12}$<br>$1.27 + 8.9$<br>5 <sub>m</sub>                                        |                                                                    | $1.2720 +$<br>$1.27 + 8.9$ | $\overline{12}$  | 1.2720            | 1.27 189            | $\overline{12}$ | 1.27201<br>$1.27 + 89$                                             | $\overline{\mathbf{12}}$ | $1.27$ 201    |                                           | PE- 03:41:46.964 EUR/USD EXEC Buy 1.27179                                              |                    |                          |                 |                                | 1,000,000                                                                  | <b>EUR</b>                                                       | 1.000.000                                                                                    |              |  |
| $1.27$ 184<br>2.1<br>10 <sub>m</sub>                                                     |                                                                    | 1.27 205<br>1.27 185       | 2.1              | 1.27 20c          | $1.27 + 84$         | 2.1             | 1.27 205<br>$1.27$ 185                                             | 2.1                      | 1.27206       |                                           |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
| $1.27$ 184<br>2.1<br>15 <sub>m</sub>                                                     |                                                                    | 1.27 205<br>$1.27$ 185     | 2.1              | 1.2720c           | $1.27 + 8.4$        | 2.1             | 1.27 205<br>$1.27 + 85$                                            | 2.1                      | 1.27206       | $\mathbb{H}$ 4                            |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
| 25 <sub>π</sub>                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          | ٠             |                                           | <b>Hedge Orders</b> All Orders                                                         |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              | 圓            |  |
| <b>EL GRPAJSD</b>                                                                        |                                                                    |                            |                  |                   |                     |                 | Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR |                          |               |                                           |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  | Order Time CCY Status Side Limit Notional Amount Notional Currency Executed Amount Execution |              |  |
| <b>E USD/INR</b>                                                                         |                                                                    |                            |                  |                   |                     |                 | Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR |                          |               |                                           | PE- 03:4 EU EXEC Sell 1.27                                                             |                    |                          |                 | 1.000.000                      | <b>EUR</b>                                                                 |                                                                  | 1.000.000                                                                                    |              |  |
| <b>E USD/JPY</b>                                                                         |                                                                    |                            |                  |                   |                     |                 | Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR |                          |               |                                           | PE- 03:4 US EXEC Sell 107<br>PE- 03:4 AU EXEC Sell 0.87                                |                    |                          |                 | 1.271.986<br>1.000.000         | <b>USD</b><br><b>AUD</b>                                                   |                                                                  | 1,271,986<br>1,000,000                                                                       |              |  |
|                                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           | PE- 03:4 US EXEC Sell 107                                                              |                    |                          |                 | 1,000,000                      | <b>USD</b>                                                                 |                                                                  | 1,000,000                                                                                    |              |  |
|                                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           | PE- 03:4 EU EXEC Sell 1.27                                                             |                    |                          |                 | 2,000,000                      | <b>EUR</b>                                                                 |                                                                  | 2,000,000                                                                                    |              |  |
|                                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           | PE- 03:4 EU EXEC Sell 1.27                                                             |                    |                          |                 | 1.000.000                      | <b>EUR</b>                                                                 |                                                                  | 1,000,000                                                                                    |              |  |
|                                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               | $\mathbb{R}$ + $\mathbb{R}$               |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
|                                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
|                                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           | <b>Currency Pair Positions</b> Trade Blotter                                           |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              | 同            |  |
| <b>Pricing Control</b>                                                                   |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          | $\boxplus$    |                                           |                                                                                        |                    |                          |                 |                                | Total Net Position: 0 EUR P/L: Open 0 EUR / Realized 39 EUR / Total 39 EUR |                                                                  |                                                                                              |              |  |
| Instrument/Ch ^1                                                                         | <b>Status</b>                                                      | Managed                    |                  |                   | Control             |                 | Mode                                                               | Inbound                  | <b>Inboun</b> |                                           | Symbol                                                                                 |                    |                          |                 |                                |                                                                            |                                                                  | Updated Size CCY1 Size CCY2 Open P/L Realized P/L Total P/L Reval. Rate Avg. Buy             |              |  |
|                                                                                          |                                                                    | Bv                         | Start            | <b>Stop</b>       | Ownership Configure |                 |                                                                    | <b>BID</b>               | <b>ASK</b>    | <b>EUR/USD</b><br>USD/JPY                 |                                                                                        | 03:42:5<br>03:42:5 | 0.00<br>0.00             |                 | $\mathbf{0}$<br>$\overline{0}$ | 0.00<br>0.00<br>0.00<br>0.00                                               | 0.00<br>0.00                                                     | 1.27201<br>107.182                                                                           | 1.2<br>10    |  |
| <b>All Instruments</b>                                                                   | $\bullet$                                                          |                            | $\mathbf 0$      | $\mathbf{\Theta}$ |                     | o               | Change v                                                           |                          |               | <b>AUD/USD</b>                            |                                                                                        | 03:42:3            | 0.00                     |                 | 50                             | 0.00<br>39.31                                                              | 39.31                                                            | 0.87989                                                                                      | 0.8          |  |
| <b>E AUD/USD</b>                                                                         | $\bullet$                                                          | PEAPAC.T.                  | $\Theta$         | $\bullet$         | Take                | Ο.              | Managed <b>v</b>                                                   |                          |               | <b>GBP/USD</b><br><b>USD/INR</b>          |                                                                                        | 03:33:1            | $\mathbf{0}$<br>$\Omega$ |                 | $\mathbf{0}$<br>$\Omega$       | 0.00<br>0.00                                                               | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>0.00<br>$\mathbf{0}$ | 1.60673                                                                                      |              |  |
| <b>EUR/USD</b>                                                                           | $\Omega$                                                           | PEAPAC.T                   | $\odot$          | $\Theta$          | Take                | $\bullet$ .     | B <sub>2B</sub><br>$\blacktriangledown$                            |                          |               | Ⅲ 4                                       |                                                                                        | 03:33:3            |                          |                 |                                |                                                                            | 0.00                                                             | 60.9630                                                                                      | $\mathbf{r}$ |  |
| <b>Core Chann</b>                                                                        | $\bullet$                                                          |                            | $\Theta$         | $\bullet$         |                     | Φ.              |                                                                    | 1.27189                  | 1.272         |                                           | <b>Trade ID</b>                                                                        | $Cre$ $v1$         | <b>Type</b>              |                 | <b>Trigger</b>                 | Side<br>Quantity                                                           | Price                                                            | Counterparty                                                                                 |              |  |
| <b>Core Chann</b>                                                                        | $\bullet$                                                          |                            | ⊖                | $\bullet$         |                     | Ο.              |                                                                    | 1.27189                  | 1.272         |                                           | PE-458511                                                                              | 03:42:5            | Cross                    |                 |                                | 1,000,000<br>Buv                                                           |                                                                  | 1.27199 PE-458502                                                                            | m            |  |
| <b>E GBP/USD</b>                                                                         | Θ                                                                  | PEAPAC.T                   | $\circ$          | $\Theta$          | Take                | Ο.              | B <sub>2B</sub>                                                    |                          |               |                                           | $P_{E-458542}$                                                                         | 03:42:5            | Hedge                    |                 | PE-458511                      | Sell                                                                       | 1.000.000 1.27199                                                |                                                                                              |              |  |
| <b>E USD/INR</b>                                                                         | Ω                                                                  | PEAPAC.T.                  | $\mathbf{\circ}$ | ⊖                 | Take                | $\bullet$       | Managed -                                                          |                          |               |                                           | SO-216186 03:42:5<br>PE-458405                                                         | 03:42:0            | Hedge<br><b>RFS</b>      |                 |                                | Sell<br>1,000,000<br>2,000,000<br>Buy                                      |                                                                  | 1.27199 Barclays BARX.D<br>1.27194 360T.Fischer                                              |              |  |
| <b>E USD/JPY</b>                                                                         | $\Omega$                                                           | PEAPAC.T                   | ⊜                | $\bullet$         | Take                | Ο.              | Flow He -                                                          |                          |               |                                           | PE-458413                                                                              | 03:42:0            |                          | Hedge PE-458405 |                                | 2,000,000<br>Sell                                                          | 1.27194                                                          |                                                                                              |              |  |
|                                                                                          |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           | SO-216186<br>PE-458382                                                                 | 03:42:0<br>03:41:4 | Hedge<br><b>RFS</b>      |                 |                                | Sell<br>Buy                                                                |                                                                  | 2,000,000 1.27194 Barclays BARX.D<br>1,000,000 1.27179 360T.Fischer                          |              |  |
|                                                                                          | Ⅲ 4 图                                                              |                            |                  |                   |                     |                 |                                                                    |                          | ı.            |                                           |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  |                                                                                              |              |  |
| $\Theta$                                                                                 |                                                                    |                            |                  |                   |                     |                 |                                                                    |                          |               |                                           |                                                                                        |                    |                          |                 |                                |                                                                            |                                                                  | User: PEAPAC.Trader1, Company: PEBANK_APAC.TEST                                              |              |  |

<span id="page-8-1"></span>Figure 1 Market Maker Cockpit Overview

### <span id="page-9-0"></span>**2.1 Login and Logout**

Login to the MMC user interface via the 360T single sign-on applet. To start the MMC GUI click on the MMC button:

|                                            | <b>EN PROVISCHE BÖRSE</b>                    |
|--------------------------------------------|----------------------------------------------|
| <b>SST</b><br><b>RFS</b>                   | <b>STAR</b><br><b>MMC</b><br>O               |
| Logout<br>Cancel<br>Applet Version: 3.19.1 | Set Password<br>Help<br>TEX-DEMO - connected |
| Started MMC                                | PEAPAC.Trader1                               |

<span id="page-9-2"></span>Figure 2 Login and Start the Market Maker Cockpit

The application buttons are shown in green when they are starting or currently running. More than one application can be started at the same time.

When you activate the radio button right next to the application button, the application will always automatically restart on your next login.

To close the application select menu **File** and click on **Exit** in the drop down menu.

![](_page_9_Figure_9.jpeg)

<span id="page-9-3"></span>Figure 3 Exit the Market Maker Cockpit

### <span id="page-9-1"></span>**2.2 Panels, Tabs and Tab Container**

The user interface is divided into various tabs such as "Pricing Monitor", "Managed Positions", "Pricing Control" and Order blotters.

Each tab consists of a tab header (rectangle box in the upper left corner containing the tab title), and tab content.

Tabs are organized within tab containers. By default there are three tab containers; one upper left, one upper right, and one at the bottom. Each tab container is resizable by either moving the border between tab containers, or by resizing the main window. New tab containers can be added, and existing tab containers can be removed.

Your customized user interface layout will be automatically saved when logging out to be available at the next login

### <span id="page-10-0"></span>**2.3 Re-arranging Tabs**

Tabs can be moved within the same tab container, into another existing tab container, or into a new tab container. Tabs can also be completely detached from the main window and placed somewhere on the screen.

To move a tab, click and hold the tab header with the left mouse button, and move the tab into a new location. Release the mouse button over the new target location.

To re-arrange the tab order within a tab container, drag and drop the tab horizontally within the same tab container.

To move a tab into another tab container, drop the tab onto the tab header of the target tab container.

To create a new tab container, drop the tab into the content of any other tab. Dependent where you drop the tab, the target tab will split either horizontally or vertically, and a new tab container will be added.

When the last tab of a container is removed, the tab container will disappear automatically.

### <span id="page-10-1"></span>2.4 **Modifying Tabs and Tab Containers**

Right clicking with the mouse on a tab header shows the following context menu options:

- **Floating:** This will detach the tab from the main window into a separate window. This "child" window can be placed anywhere on the desktop. The same can be achieved by placing the mouse on the tab header, pressing and holding the left mouse button, and dragging the tab out of the main window.
- **Auto-Hide:** This will shrink the entire tab container to the size of the tab headers. The tab headers will be placed at the nearest window border. To view and hide a tab the user has to click on one of the tab headers.
- **Maximize:** This will maximize the tab container to the size of the enclosing window. The same can be achieved by double clicking the tab header with the left mouse button.
- **Restore:** This option is only available when the tab container is maximized. Clicking it will shrink the tab container to the original size. The same can be achieved by double clicking on the tab header with the left mouse button.

## <span id="page-11-0"></span>**3 CONFIGURE PRICING**

### <span id="page-11-1"></span>**3.1 Pricing Control**

The pricing control tab allows to configure:

- Managed instruments
- Global instrument configuration
- Start and stop pricing and set the pricing/risk management mode
- Configure pricing tiers
- Configure pricing and skewing rules
- Configure reference price finding
- Configure risk management
- Acquire ownership for an instrument or price channel

The pricing control panel is organized into rows and columns. Each row contains information for a specific managed instrument or channel.

For each currency pair the trader can configure multiple pricing channels. Each pricing channel can have different spread/skew settings and Reference Price Finding rules. The assignment of your clients to the different pricing channel prices can be done by admin users who has access to `Stream Group Mapping` within Business Configuration Tool.

| <b>Pricing Control</b>       |               |               |                                                       |                   |             |                   |                              |            |            |                                         | 圓       |
|------------------------------|---------------|---------------|-------------------------------------------------------|-------------------|-------------|-------------------|------------------------------|------------|------------|-----------------------------------------|---------|
| Instrument/Ch $\mathbf{A}^1$ | <b>Status</b> | Managed<br>By | Control<br>Ownershi Configure<br><b>Start</b><br>Stop |                   |             | Mode              | Inbound<br><b>BID</b>        | <b>ASK</b> | <b>BID</b> | Inbound Outbound Outbound<br><b>ASK</b> |         |
| <b>All Instruments</b>       | $\bullet$     |               | $\mathbf O$                                           | 0                 |             | 0                 | Change V                     |            |            |                                         |         |
| <b>E</b> AUD/USD             | $\mathbf O$   | PEAPAC.T      | $\Theta$                                              | $\mathbf{O}$      | Take        | o                 | Managed $\blacktriangledown$ |            |            |                                         |         |
| $\blacksquare$ eur/usd       | $\mathbf{O}$  | PEAPAC.T      | $\circledcirc$                                        | 0                 | <b>Take</b> | Ō<br>$\mathbf{u}$ | B <sub>2</sub> B             |            |            |                                         |         |
| Core Chann                   | O             |               | $\Theta$                                              | $\mathbf{\Theta}$ |             | 0.                |                              | 1.27194    | 1.27206    | 1.27194                                 | 1.27206 |
| Core Chann                   | O             |               | $\odot$                                               | $\mathbf{\Theta}$ |             | $\bullet$         |                              | 1.27194    | 1.27206    | 1.27194                                 | 1.27206 |
| I⊞ GBP/USD                   | $\mathbf{O}$  | PEAPAC.T      | $\mathbf O$                                           | $_{\tiny{\odot}}$ | Take        | 0.                | B <sub>2</sub> B<br>▼        |            |            |                                         |         |
| <b>E</b> USD/INR             | O             | PEAPAC.T      | $\mathbf O$                                           | $\Theta$          | Take        | 0.                | Managed $\blacktriangledown$ |            |            |                                         |         |
| <b>E USD/JPY</b>             | $\mathbf O$   | PEAPAC.T      | $\odot$                                               | $\mathbf 0$       | Take        | O                 | Flow He •                    |            |            |                                         |         |

<span id="page-11-2"></span>Figure 4: Pricing Control Panel

Column description:

- **Instrument/Channel:** Displays the instrument (top level), or a specific channel for the instrument (child level)
- **Status:** Displays the current pricing status per instrument or channel. Green indicates pricing is on, red indicates pricing is off. Yellow applies to a specific instrument or "All Instruments", and indicates a mixed pricing state of channels beneath.
- **Managed By:** Displays which user currently "owns" the instrument. Only the user who owns an instrument can change its pricing and risk management configuration, and start or stop pricing for this instrument. An empty value indicates that nobody currently owns the instrument. This happens only when an instrument was newly added.
- **Start/Stop buttons:** Clicking the green button will start pricing for that instrument or channel, clicking the red button will stop pricing for the instrument or channel. Pressing the start or stop button on an instrument level affects all channels for this

instrument. Pressing start or stop in the "All Instruments" row will affects all instruments the user currently owns.

- **Ownership:** A user can take ownership for a specific instrument by clicking the "Take" button. This button is only enabled for instruments owned by other users.
- **Configure:** Depending on the selected row, a user can either adjust certain settings for all instruments, or pricing and Reference Price Finding for a specific instrument by clicking this button which is only enabled for instruments the user currently owns.
- **Mode:** Here is the current risk management mode per instrument displayed. The mode can be changed by clicking the button, which will open a drop down box. The value can only be changed by the user who currently owns the instrument.
- **Inbound Bid/Ask:** Best available inbound price for this channel
- **Outbound Bid/Ask:** Best available outbound price for this channel

### <span id="page-12-0"></span>**3.2 Add and Remove Managed Instruments**

To manage pricing and risk for a specific instrument, it must be added to the list of managed instruments in the Pricing Control tab. To do so press on the configure button for "All Instruments" in the "Pricing Control" panel.

| <b>Pricing Control</b>       |               |            |              |      |           |           |         |  |
|------------------------------|---------------|------------|--------------|------|-----------|-----------|---------|--|
|                              |               | Managed    |              |      | Control   |           |         |  |
| Instrument/Ch $\mathbf{A}^1$ | <b>Status</b> | By         | <b>Start</b> | Stop | Ownership | Configure | Mode    |  |
| All Instruments              |               |            |              |      |           | <b>M.</b> | Change  |  |
| ⊪⊞ AUD/USD                   |               | PFAPAC.Tr. |              |      | Take      |           | Managed |  |

<span id="page-12-1"></span>Figure 5 Open Global Instrument Configuration

This will open the "Global Instrument Configuration" dialog:

| $\mathbf x$<br>Global Instrument Configuration                                                                                                  |                                                                                                                                                         |  |  |  |  |  |  |
|-------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|
| <b>General Parameters</b><br>$\triangleq$ Instruments<br><b>Tiers</b><br><b>Scenarios</b><br><b>Cross Rules</b><br><b>Client Order Handling</b> | <b>Instruments</b><br>Base CCY<br>Quote C<br><b>USD</b><br><b>JPY</b><br>Add<br>$\mathbf{v}$<br>▼<br>AUD/USD<br>Remove<br>EUR/USD<br>GBP/USD<br>USD/JPY |  |  |  |  |  |  |
|                                                                                                                                                 | QK<br>Cancel<br>Apply                                                                                                                                   |  |  |  |  |  |  |

<span id="page-12-2"></span>Figure 6 Global Instrument Configuration dialog

To add a new managed instrument, select base and quote currency, and press the Add button.

To remove a specific managed instrument, select the currency pair in the list, and press the Remove button.

### <span id="page-13-0"></span>**3.3 Ownership of a Managed Instrument**

A user can only control pricing and risk management for instruments he currently owns. To take ownership of another user's instrument, click on the "Take" button in the Pricing Control tab. The "Take" button is only enabled for the instruments which are currently owned by another user.

|                 | Control |      |             |  |  |  |
|-----------------|---------|------|-------------|--|--|--|
| Managed By      | Start   | Stop | Ownership   |  |  |  |
|                 |         |      |             |  |  |  |
| PEEMEA1.Trader2 |         |      | Take        |  |  |  |
| PEEMEA1.Trader2 |         |      | <b>Take</b> |  |  |  |
| PEEMEA1.Trader2 |         |      | Take        |  |  |  |
| PEEMEA1.Trader1 |         |      | 'Take       |  |  |  |

<span id="page-13-3"></span>Figure 7: Take over instrument ownership

The column "Managed By" shows the user who currently owns a specific instrument.

### <span id="page-13-1"></span>**3.4 Start/Stop Pricing of Instruments and Channels**

To start or stop pricing of an instrument or channel, click the "Start" or "Stop" button in the according row in the "Pricing Control" tab.

![](_page_13_Picture_10.jpeg)

Figure 8: Start/Stop Pricing

<span id="page-13-4"></span>Note: A user can only start or stop his own managed instruments!

To stop **all** pricing of **all** currency pairs click "**Stop All**". No request will be quoted any more.

![](_page_13_Picture_14.jpeg)

<span id="page-13-5"></span>

The 'Stop All" button is an emergency stop button. Pressing this button pricing will stop for all managed instruments!

### <span id="page-13-2"></span>**3.5 Pricing and Risk Management Mode**

To quickly change between pricing and risk management modes for a specific instrument, select the respective mode in the Mode column for the specific instrument.

![](_page_14_Picture_2.jpeg)

Figure 10: Select risk management mode

<span id="page-14-1"></span>The provided modes are:

- **Managed:** All currently defined risk management rules apply. Positions will potentially be accumulated.
- **B2B:** Back to back, all incoming requester orders will first be hedged, and **only accepted** if the hedge trade was successful (last look)
- **Flow Hedge:** All incoming requester orders will be accepted, but immediately hedged (without a last look)

A user can change the risk management mode for all his currently owned instruments, by selecting a risk management mode in row "All Instruments".

### <span id="page-14-0"></span>**3.6 Tier Size Configuration**

To adjust the tier size for instruments, open the global instrument configuration dialog and select option "Tiers":

| $\mathbf x$<br><b>Global Instrument Configuration</b><br>MAC |                   |           |           |                                              |                          |                      |              |  |
|--------------------------------------------------------------|-------------------|-----------|-----------|----------------------------------------------|--------------------------|----------------------|--------------|--|
| <b>General Parameters</b>                                    | <b>Tiers</b>      |           |           |                                              |                          |                      |              |  |
| Instruments<br>$\triangleq$ Tiers                            | <b>Instrument</b> | Tier 1    | Tier 2    | Tier 3                                       | Tier 4                   | Tier 5               | Tier 6       |  |
| <b>Scenarios</b>                                             | AUD/USD           | 1,000,000 |           | 5,000,000 10,000,000 15,000,000 25,000,000   |                          |                      |              |  |
| <b>Cross Rules</b>                                           | EUR/USD           | 1,000,000 | 5,000,000 | $ 10,000,000 $ $ 15,000,000 $ $ 25,000,000 $ |                          |                      |              |  |
| Client Order Handling                                        | GBP/USD           | 500,000   | 1,000,000 | 3,000,000                                    |                          | 5,000,000 10,000,000 |              |  |
|                                                              | USD/JPY           | 1,000,000 | 2,000,000 | 4,000,000                                    |                          | 8,000,000 12,000,000 |              |  |
|                                                              |                   |           |           |                                              |                          |                      |              |  |
|                                                              |                   |           |           |                                              |                          |                      |              |  |
|                                                              |                   |           |           |                                              |                          |                      |              |  |
|                                                              |                   |           |           |                                              |                          |                      |              |  |
|                                                              |                   |           |           |                                              |                          |                      |              |  |
|                                                              |                   |           |           |                                              | $\overline{\mathsf{OK}}$ | Cancel               | <b>Apply</b> |  |

<span id="page-14-2"></span>Figure 11: Global Instrument Configuration Dialog

The dialog allows a user to configure up to 6 pricing tiers of arbitrary size individually for each managed instrument.

To change the size of an existing tier simply overwrite the according cell with a new value. To remove a specific tier, erase the value and leave the cell empty. Tiers can be entered in any order. The MMC GUI will display the tiers automatically sorted by size.

After pressing Ok or Apply, changes in the tier configuration are effective immediately.

Note:

The MMC stores pricing and reference price finding configurations for each cell in the configuration dialog (e.g. EUR/USD – Tier 3). This configuration will be deleted whenever the size of a specific tier is changed!

### <span id="page-15-0"></span>**3.7 Instrument Configuration**

To configure pricing for a specific instrument, click on the Configure button in the Pricing Control tab:

| Managed |
|---------|
| Managed |
| B2B     |

<span id="page-15-1"></span>Figure 12: Open instrument configuration dialog

This will open the 'Instrument Configuration' dialog for the selected instrument:

| Instrument Configuration - EUR/USD               |                                                                               |                                                                                 |                                   | $\mathbf x$                |  |  |  |
|--------------------------------------------------|-------------------------------------------------------------------------------|---------------------------------------------------------------------------------|-----------------------------------|----------------------------|--|--|--|
| Pricing                                          | <b>Pricing.Core Channel 1</b>                                                 |                                                                                 |                                   |                            |  |  |  |
| $\blacklozenge$ Core Channel 1<br>Core Channel 2 | <b>Spread</b>                                                                 | <b>Skew</b>                                                                     |                                   | <b>Slippage</b>            |  |  |  |
| Reference Price Finding                          | M<br>$\blacktriangleright$ + Pips $\blacktriangleright$<br><b>Min</b><br>Tier | <b>Max</b>                                                                      | <b>Skew Factor</b>                | ℅                          |  |  |  |
| Core Channel 1                                   | $0.2 -$<br>1 <sub>m</sub>                                                     | d♡<br>$0.5 -$<br>$0.2 -$                                                        |                                   | $\mathbf{0}$ $\div$<br>8%  |  |  |  |
| Core Channel 2<br>Risk Management                | $0.5 -$<br>5 <sub>m</sub>                                                     | $0.7 -$<br>ж,<br>0.5                                                            |                                   | $0\frac{1}{2}$<br>26%      |  |  |  |
| <b>General Parameters</b>                        | $0.8 -$<br>10 <sub>m</sub>                                                    | $1 -$<br>0.8                                                                    | Œ.                                | $\vert 0 \vert$<br>42%     |  |  |  |
| <b>Position Rules</b>                            | $0.0 -$<br>15m                                                                | $1.2 -$<br>0.0                                                                  | œ                                 | $\mathbf{0}$ $\div$<br>61% |  |  |  |
| <b>Pricing Rules</b>                             | $0.0 -$<br>25m                                                                | $0.0 -$<br>$0.0+$                                                               | Į99                               | $\bullet$ $\div$<br>87%    |  |  |  |
| Quote Filtering                                  |                                                                               | pa,<br>$\mathbf{L}$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\mathbf{L}$ | <b>CONTRACTOR</b><br>$\mathbf{I}$ | 0%                         |  |  |  |
|                                                  | Allow Spread less than Market                                                 |                                                                                 |                                   |                            |  |  |  |
|                                                  | ∨ Allow Skew to cross Mid Price                                               |                                                                                 |                                   |                            |  |  |  |
|                                                  | Allow Skew to cross Opposite Side                                             |                                                                                 |                                   |                            |  |  |  |
|                                                  |                                                                               | Allow Slippage Worse than Client Order Price                                    |                                   |                            |  |  |  |
| QK<br>Cancel<br><b>Apply</b>                     |                                                                               |                                                                                 |                                   |                            |  |  |  |

<span id="page-15-2"></span>Figure 13: Instrument Configuration Dialog

For each managed instruments users can configure:

- Pricing additional or fixed spread, manual skew, and cutoff rules
- Reference Price Finding filter criteria to define reference prices
- Risk Management position and pricing rules

The navigation tree on the left side of the dialog allows to select Pricing or Reference Price Finding for a specific channel, and risk management for the entire instrument.

### <span id="page-16-0"></span>**3.8 Additonal and Fixed Spread**

Configuring spreads can be done in various ways:

- **Fixed:** Fixed outbound spread independent of the inbound spread
- **% Inbound:** Relative outbound spread based on percentage of inbound spread
- **+PIPS:** Outbound spread is always n PIPS wider than the inbound spread

**Fixed** spreads are calculated around the mid-price of Inbound. When chosen, the system applies the defined fixed spread or, if wider, the inbound spread.

If wished, select the checkbox "Allow Spread less than Market". A fixed spread of "0" means that the inbound spread is used.

**% Inb.** adds the defined percentage on the inbound spread.

**+PIPS** adds the specified number of pips to the inbound spread.

Independent of the chosen spread mode, the outbound spread can always be limited by a minimum and a maximum spread.

### <span id="page-16-1"></span>**3.9 Manual Skew**

Manual Skew can be set both in percentage of inbound spread, and in absolute numbers specified in PIPS. Both options can be used individually but also in combination. Applying manual skew is a combination of **basic instrument wide skew settings**, and "**Skew Factors**", which distribute the selected base value to each pricing tier.

Basic skew settings can be adjusted in the "Pricing Control" panel in columns "Skew PIPS" and "Skew Percent". If the columns are not visible, right click on any other column in the "Pricing Control" panel and select option "Choose Columns…".

|                   | <b>Skew</b><br><b>PIPS</b> |                                      | <b>Skew</b><br>Percent                        |    |                                  |  |  |
|-------------------|----------------------------|--------------------------------------|-----------------------------------------------|----|----------------------------------|--|--|
|                   |                            |                                      |                                               |    |                                  |  |  |
| ¥<br>v            | 1.3                        | $\lambda$ $\lambda$<br>$\cdots$      | ×<br>$\checkmark$                             | 0  | ⋩<br>$\hat{\phantom{a}}$<br>1.11 |  |  |
| ¥<br>$\checkmark$ | 0                          | <b>^   ☆</b><br><b>STATE</b>         | ×.<br>$\check{}$                              | 14 | $\hat{\phantom{a}}$<br>☆<br>     |  |  |
| ¥<br>v            | $-2.1$                     | ⋩<br>$\hat{\phantom{a}}$<br>$\cdots$ | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>v | -6 | ⋩<br>$\hat{\phantom{a}}$<br>     |  |  |

<span id="page-16-2"></span>Figure 14: Basic skew settings

In general, clicking on the single arrow buttons increments and decrements PIPS in steps of 0.1, and percentage in steps of 1. Clicking on the double arrow buttons increments and decrements PIPS in steps of 1, and percentage in steps of 10. For certain instruments like **USD/INR** which are quoted in quarterly PIPS, clicking on the single arrow buttons in the "Skew PIPS" column will increment and decrement the value in steps of 0.25.

#### Any change in these values is immediately effective!

The selected values can be distributed to each pricing tier by "Skew Factors". A skew factor of 10% for the 1m tier means, only 10% of the skew value chosen in the pricing control panel will be applied to this tier. The maximum skew factor is 100% which means, whatever skew values are chosen in the pricing control panel, will be fully applied to this tier.

To adjust skew factors for a specific instrument open the "Instrument Configuration" dialog, and chose option "Pricing":

| <b>Skew</b>                                             |     |
|---------------------------------------------------------|-----|
| <b>Skew Factor</b>                                      |     |
| Į3                                                      | 10% |
| Œ,                                                      | 34% |
| ŀ.                                                      | 50% |
| $\sim$                                                  | 74% |
| 四                                                       | 95% |
| u.<br>Ï<br>п<br>ı<br>$\blacksquare$<br>п<br>ı<br>r<br>п | 0%  |

<span id="page-17-1"></span>Figure 15: Tier specific skew factors

If skewing to cross the mid-rate or even the opposite side should be allowed, one MUST tick the respective options ( "Allow Skew to cross Mid Price" and "Allow Skew to cross Opposite Side, respectively) to demonstrate one has taken an informed decision.

|                                                                 |               |            | <b>Pricing.Core Channel 1</b> |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
|-----------------------------------------------------------------|---------------|------------|-------------------------------|--------------------------|-------------|------------------------------------------|------------------------------------------|----|--------------|--------------------|--------------|--------------|--------------|--------------|--------|-----|----------------------------|
|                                                                 | <b>Spread</b> |            |                               |                          | <b>Skew</b> |                                          |                                          |    |              |                    |              |              |              |              |        |     | Slippage                   |
| <b>Tier</b>                                                     | ■             | <b>Min</b> | Fixed $\blacktriangledown$    | П<br><b>Max</b>          |             |                                          |                                          |    |              | <b>Skew Factor</b> |              |              |              |              |        |     | %                          |
| 1 <sub>m</sub>                                                  |               | $0.0 -$    | $10.0 -$                      | $0.0 -$<br>$\rightarrow$ |             |                                          |                                          |    |              | Q                  |              |              |              |              |        | 50% | $0 \div$                   |
| 5m                                                              |               | $0.0 -$    | $0.0 -$                       | $0.0 - \bigcirc$         |             |                                          |                                          |    |              |                    |              |              |              |              |        | 0%  | $0 -$                      |
| 10 <sub>m</sub>                                                 |               | $0.0 -$    | $0.0 -$                       | 0.0 <sub>1</sub>         | ♡           |                                          |                                          |    |              |                    |              |              |              |              |        | 0%  | $\mathbf{0}$ $\frac{1}{x}$ |
| 15m                                                             |               | $0.0 -$    | $\left  \bullet .0 \right $   | $0.0 -$                  | V           |                                          |                                          |    |              |                    |              |              |              |              |        | 0%  | $\mathbf{0}$ $\frac{1}{x}$ |
| 25m                                                             |               | $0.0 -$    | $0.0 -$                       | 0.0                      | ♡           |                                          |                                          |    |              |                    |              |              |              |              |        | 0%  | $\mathbf{0}$ $\div$        |
|                                                                 |               |            |                               |                          | ♡           | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | T. | $\mathbf{I}$ | $\mathbf{I}$       | $\mathbf{L}$ | $\mathbf{I}$ | $\mathbf{I}$ | $\mathbf{L}$ |        | 0%  |                            |
|                                                                 |               |            |                               |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
|                                                                 |               |            |                               |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
| $\frac{1}{\sqrt{2}}$ decimal places<br>Round outbound quotes to |               |            |                               |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
|                                                                 |               |            | Allow Spread less than Market |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
|                                                                 |               |            | Allow Skew to cross Mid Price |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
| Allow Skew to cross Opposite Side                               |               |            |                               |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
| Allow Slippage Worse than Client Order Price                    |               |            |                               |                          |             |                                          |                                          |    |              |                    |              |              |              |              |        |     |                            |
|                                                                 |               |            |                               |                          |             |                                          |                                          |    |              |                    |              |              |              | QK           | Cancel |     | <b>Apply</b>               |

<span id="page-17-2"></span>Figure 16 Allowing Skew to cross Mid Price/ Opposite Side

Hit **Apply** to confirm and apply the defined rule changes.

### <span id="page-17-0"></span>**3.10 Rounding Outbound Quotes**

For each managed instrument, users can configure the decimal places for outbound quote. By default, the quotes are published with 5 decimal places.

From Pricing panel under Instrument Configuration, user can reduce the outbound price precision from 5 dp.

For example, if `Round Outbound Quotes to 4 decimal places` is configured for EURUSD pricing, the outbound quotes of 1,12342 – 1,12347 would be rounded to 1,12340 – 1.12350.

![](_page_18_Picture_1.jpeg)

| Pricing.Core Channel 1                                                   |                                                             |                                                                                                                    |                                 |
|--------------------------------------------------------------------------|-------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------|---------------------------------|
| Core Channel 1<br><b>Spread</b><br>Reference Price Finding               | <b>Skew</b>                                                 |                                                                                                                    |                                 |
| Core Channel 1<br><b>Min</b><br>Tier<br>П<br>Risk Management             | $\left\  + \text{Pips} \right\ $ Max                        | <b>Skew Factor</b>                                                                                                 |                                 |
| $0.0 -$<br>1m<br><b>General Parameters</b>                               | $\overline{0.0}$ $\rightarrow$ $\overline{\bigcirc}$<br>0.0 |                                                                                                                    | 0%                              |
| <b>Position Rules</b><br>0.0<br>5m                                       | $0.0 -$<br>0.0<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!  |                                                                                                                    | 0%                              |
| <b>Pricing Rules</b><br>0.0<br>10 <sub>m</sub><br><b>Quote Filtering</b> | $0.0 -$<br>$\ket{0.0}$ $\oplus$ $\heartsuit$                |                                                                                                                    | 0%                              |
| 0.0<br>15m                                                               | $\ket{0.0}$ $\ominus$<br>$\vert 0.0 \vert \div$             |                                                                                                                    | 0%                              |
| 0.0<br>25m                                                               | 0.0<br>$\ket{0.0}$ $\oplus$ $\heartsuit$                    |                                                                                                                    | 0%                              |
|                                                                          |                                                             |                                                                                                                    | 0%                              |
|                                                                          | <b>COLL</b>                                                 | <b>COLL</b><br><b>Contractor</b><br><b>Contractor</b><br><b>COLLECTION</b><br><b>Contract</b><br><b>COLLECTION</b> | $\sim$ 10 $\sim$<br><b>STEP</b> |
|                                                                          |                                                             |                                                                                                                    |                                 |
| Round outbound quotes to                                                 | $4 \div$ decimal places                                     |                                                                                                                    |                                 |
| Allow Spread less than Market                                            |                                                             |                                                                                                                    |                                 |
|                                                                          |                                                             |                                                                                                                    |                                 |
| Allow Skew to cross Mid Price                                            |                                                             |                                                                                                                    |                                 |

<span id="page-18-1"></span>Figure 17: Reducing outbound price precision

### <span id="page-18-0"></span>**3.11Reference Price Finding**

The definition of the reference price(s) to be the base for the inbound price is required.

Initially no provider is selected. To get inbound prices one has to select **Reference Price Finding** and choose the desired provider(s).

There are various strategies and parameters to filter inbound quotes, and to calculate a reference price for each pricing tier:

| Strategy       | Parameter 1        | Parameter 2       |  |  |  |
|----------------|--------------------|-------------------|--|--|--|
| Best Price     | Minimum Quote Size | Minimum Providers |  |  |  |
| Deep Average   | Minimum Quote Size | Levels            |  |  |  |
| Deep Worst     | Minimum Quote Size | Levels            |  |  |  |
| VWAP Average   | Minimum Quote Size | Maximum Providers |  |  |  |
| VWAP Worst     | Minimum Quote Size | Maximum Providers |  |  |  |
| Fix Core Price | Bid                | Ask               |  |  |  |

**Best Price** will try to find the best single quote, of at least minimum quote size. To find a reference price, there must be at least 'minimum providers' number of quotes, of minimum size available.

This strategy is ideal for B2B hedging when maximum fill ratio is most important (e.g. in case of RFS client orders). When Best Price is used, B2B hedge orders will be placed as limit orders, with the original reference price. This is to ensure that both trader and sales spread will be preserved.

**Deep Average and Deep Worst** consider the top n (levels) quotes in the book, of at least minimum size. Average will calculate a VWAP price of the top n levels, whereas worst will simply pick the nth level down from top of the book. A level is considered as a unique price from a specific provider. In other words, identical prices from two different providers are considered as two levels.

#### Example book:

| Level | Provider | Bid    | Ask    | Provider |
|-------|----------|--------|--------|----------|
| 1     | A        | 1.1240 | 1.1242 | B        |
| 2     | B        | 1.1240 | 1.1243 | B        |
| 3     | B        | 1.1239 | 1.1243 | A        |
| 4     | C        | 1.1238 | 1.1244 | C        |
| 5     | D        | 1.1237 | 1.1245 | D        |

The example shows on the bid side for the first three levels: 1.1240, 1.1240, 1.2339.

Bid levels 1 and 2 are treated as different level because they are from different providers, even though the price is the same.

**VWAP Average and VWAP Worst** will calculate a VWAP reference price by considering all quotes of at least minimum quote size. The required quantity for the VWAP price is in general identical with the pricing tier size. E.g. for a 5m tier, the VWAP algorithm will try to find the best VWAP price for 5m quantity. Only one quote from each provider will be used in the VWAP calculation. The maximum number of providers in the VWAP calculation can be limited.

The strategy will not return a reference price if the total quantity of quotes available (with minimum quote size) is less than the required amount (pricing tier size). This can be overruled by enabling the option **"Allow VWAP price calculation for less than required quantity"**. When this option is enabled, the strategy will try to calculate the best VWAP price, by reducing the required quantity step by step.

VWAP is a good strategy for running positions, but also for B2B hedging when partially filling of client orders is acceptable. With VWAP pricing, B2B hedge orders will be placed as limit orders with the requested price of the client order. In other words, in the worst case the hedge order will be filled with the client order price, and both trader and sales spread are lost.

**Fix Core Price** will directly define a bid and ask outbound price for the selected channel and can be used for pegged currencies.

| $\mathbf x$<br>Instrument Configuration - EUR/USD            |                                                                |       |                                                                                                               |  |  |
|--------------------------------------------------------------|----------------------------------------------------------------|-------|---------------------------------------------------------------------------------------------------------------|--|--|
| Pricing                                                      | Reference Price Finding.Core Channel 1                         |       |                                                                                                               |  |  |
| Core Channel 1<br>Core Channel 2                             | <b>Providers</b><br><b>Tier</b>                                | Hedge | <b>Strategy</b>                                                                                               |  |  |
| Reference Price Finding<br>Core Channel 1                    | BOAL.DEMO; Barclays  ▼<br>1m                                   |       | Min. Quote Size Min. Providers<br>1,000,000<br>$ALL \rightarrow$<br><b>Best Price</b>                         |  |  |
| Core Channel 2<br>Risk Management                            | BOAL.DEMO; Barclays  ▼<br>5 <sub>m</sub>                       |       | Min. Quote Size Min. Providers<br>5,000,000<br>$ALL \rightarrow$<br><b>Best Price</b>                         |  |  |
| <b>General Parameters</b><br><b>Position Rules</b>           | <b>Barclays BARX.DEMO</b><br>10 <sub>m</sub>                   |       | Min. Quote Size Min. Providers<br>10,000,000<br>ALL $\div$<br><b>Best Price</b>                               |  |  |
| <b>Pricing Rules</b><br>Quote Filtering                      | CITIBANK.DEMO<br>15 <sub>m</sub>                               |       | <b>Best Price</b><br>Min. Quote Size Min. Providers<br>Deep Average<br>15,000,000<br>ALL $\div$<br>Deep Worst |  |  |
|                                                              | 25 <sub>m</sub>                                                |       | Min. Quote Size Min. Providers<br><b>WAP Average</b><br>$25,000,000 -$<br><b>ALL</b><br><b>WAP Worst</b>      |  |  |
|                                                              | <b>Override</b>                                                |       | <b>Fix Core Prices</b>                                                                                        |  |  |
|                                                              | All tiers best price and minimum quote size at least tier size |       |                                                                                                               |  |  |
| Allow VWAP price calculation for less than required quantity |                                                                |       |                                                                                                               |  |  |
|                                                              |                                                                |       | $\overline{OK}$<br>Cancel<br>Apply                                                                            |  |  |

<span id="page-20-2"></span>Figure 18: Reference Price Finding rules editor

Click on the tier size label (e.g. 5m) to enable or disable a specific tier. In the example above the 25m tier is currently disabled.

Occasionally a trader might want to switch temporarily all channels to best price without changing the entire Reference Price Finding configuration. This is typically the case when risk management mode is set to B2B.

Selecting the option "**All tiers best price and minimum quote size at least pricing tier size**" allows to temporarily configure all outbound prices as suitable for B2B hedging with "Fill or Kill".

### <span id="page-20-0"></span>**3.12Sweepable and Full Amount Streams**

Each pricing channel can be configured to generate either sweepable or full amount/exclusive pricing. Full amount streams allow the market makers to stream prices which can be executed exclusively by one maker i.e. the client full order amount will be executed by a single liquidity provider while sweepable streams allows the maker to fill a portion of an order that might be executed by multiple providers.

This is mostly relevant to market makers streaming prices to their clients executing spot on 360T Supersonic. All RFQ requests are by default priced as full amount.

This can be done by your admin users who have access to `Stream Group Mapping` within the Business Configuration Tool.

### <span id="page-20-1"></span>**3.13Managing Synthetic Crosses**

To define rules how to strip cross currency pairs, open the 'Global Instrument Configuration Dialog' and select option "Cross Rules":

| Global Instrument Configuration                                                                                                      |                                                                                                                                                                                                                                                                                                                                                                                                                  |       |  |
|--------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|--|
| <b>General Parameters</b><br>Instruments<br><b>Tiers</b><br><b>Scenarios</b><br><b>▶ Cross Rules</b><br><b>Client Order Handling</b> | <b>Cross Rules</b><br>Acti Base Ccy Quote  Cross<br><b>Use Notional Ccy</b><br><br>$\bullet$ $\bullet$<br>$\blacksquare$ USD<br><b>EUR</b><br>EUR/USD<br>$\blacktriangledown$<br>$^\uparrow$<br>Ŧ<br>$\ddotsc$<br>$\blacktriangleright$ $\blacksquare$ GBP<br>$\overline{\phantom{a}}$ GBP $\overline{\phantom{a}}$ GBP/USD<br>$\vert\mathsf{v}\vert$<br>×,<br>T<br>$1 - $<br>Cancel<br>$\overline{\mathsf{OK}}$ | Apply |  |
|                                                                                                                                      |                                                                                                                                                                                                                                                                                                                                                                                                                  |       |  |

<span id="page-21-1"></span>Figure 19: Cross Currency Pair Configuration

Each row in this dialog defines a rule how to handle a specific cross currency pair. New rules can be added by clicking on the "+" button in the right upper corner. The "X"-button is used to delete a rule.

Each rule defines:

- Active: if ticked, the crossing for the defined currency is active
- Base Ccy and Quote Ccy: defines the currency or currency pair which should be cross-calculated using managed currency pairs. One of the two currencies can be defined with a wildcard "\*"
- Cross: defines the managed currency pair used to cross over either base or quote currency
- Use Notional Ccy: if checked, the notional is specified by the quote currency quantity
- Arrow buttons to move a rule up and down

#### **Note:**

Rules will be matched from top down. Therefore wildcard rules shall be rather placed at the end of the list!

#### **Important Note:**

The order of base currency and quote currency while setting cross rules is important. MMC does not support any inconventional currency pair setting. For example, if rule is set as USD/EUR and not as EUR/USD, the rule won`t be matched with incoming request and hence ignored.

Please contact CAS team to receive latest currency ranking to set the cross rules with right order of base/quote currency.

### <span id="page-21-0"></span>**3.14Pricing of Unmanaged Instruments**

By default the MMC will only price managed instruments. Users can configure the MMC to price unmanaged instruments too. To do so, open the "Global Instrument Configuration" dialog, and select option "General Parameters":

| Global Instrument Configuration                                                                                        | $\mathbf x$                                                                                                                                                                                                                                                                      |
|------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| ♦ General Parameters<br>Instruments<br><b>Tiers</b><br>Scenarios<br><b>Cross Rules</b><br><b>Client Order Handling</b> | <b>General Parameters</b><br>○ Allow Time Slippage of $\left  0.6 \right $ sec for all Managed/Flow Hedged instruments<br>Allow Time Slippage of $\left  0.6 \right $ sec for all B2B instruments<br>$\vee$ Price Unmanaged Instruments<br>$\vee$ Price Unmanaged Cross Currency |
|                                                                                                                        | OK<br>Cancel<br>Apply                                                                                                                                                                                                                                                            |

<span id="page-22-1"></span>Figure 20: Pricing unmanaged instruments

If option "Price Unmanaged Instruments" is enabled, the MMC will provide prices, and execute client orders, for any instrument where liquidity is available.

If option "Price Unmanaged Cross Currency" is enabled, the MMC will provide prices, and execute client orders, for any synthetic cross rate where one of the two legs is not managed by the MMC.

Example:

- EUR/USD is a managed instruments
- User configured a rule for synthetic cross rates for EUR/\* cross over to EUR/USD

If option "Price Unmanaged Cross Currency" is enabled, the MMC will provide prices, and execute client orders, for any instrument with base currency EUR.

#### **Note:**

To price unmanaged instruments, the entire bank basket will be used. Client orders for unmanaged instruments will be automatically hedged back-to-back.

### <span id="page-22-0"></span>**3.15Configuring Time Slippage**

By applying time slippage, the MMC has more time (if required) to fill a hedge order, which should improve the overall hedge order fill ratio. Time slippage is off by default. To configure time slippage, open the "Global Instrument Configuration" dialog, and select option "General Parameters":

| Global Instrument Configuration                                                                                               | Х                                                                                                                                                                                                                                                                |
|-------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| ♦ General Parameters<br><b>Instruments</b><br>Tiers<br><b>Scenarios</b><br><b>Cross Rules</b><br><b>Client Order Handling</b> | <b>General Parameters</b><br>Allow Time Slippage of $\left  0.8 \right $ sec for all Managed/Flow Hedged instruments<br>$\Box$ Allow Time Slippage of $\boxed{1.1}$ sec for all B2B instruments<br>Price Unmanaged Instruments<br>Price Unmanaged Cross Currency |
|                                                                                                                               | Cancel<br><b>OK</b><br>Apply                                                                                                                                                                                                                                     |

<span id="page-23-1"></span>Figure 21: Time slippage configuration

Time slippage can be adjusted separately for B2B and flow hedged/managed risk managed mode in steps of 1/10th of a second. Time slippage will only be applied in case the hedge order cannot be filled immediately.

### <span id="page-23-0"></span>**3.16Configuring Price Slippage**

Price slippage occurs, when a client order limit price is worse (from a trader perspective) than the currently published MMC outbound price. This problem typically occurs in fast moving markets, and/or with clients having a high latency between their end and the MMC pricing server.

The following image shows an example for slippage due to a widening market:

- 1. Inbound price spread increased between time T and T+1
- 2. Outbound bid and offer follow inbound bid and offer from time T to T+1
- 3. Client hits at time T+1 the old quote from time T
- 4. The MMC margin at time T+1 is in this case reduced by price slippage

![](_page_24_Figure_2.jpeg)

<span id="page-24-0"></span>Figure 22: Example for price slippage

Without accepting price slippage there are two possible results in this situation:

- 1. In case of B2B risk management mode, the client order will be most likely rejected, because no suitable quote to hedge the order can be found
- 2. In case of flow hedge/managed modes, the MMC will simply reject the client order to fully protect the MMC margin

By accepting price slippage, traders can give clients (if required) a discount on their trader margin, to improve client order fill ratio, and reduce rejections.

Price slippage applies differently for B2B and flow hedge/managed risk management modes. In case of B2B mode, price slippage affects the B2B hedge order limit price. By pro-actively worsening the limit price, a hedge order has a higher chance of getting filled in the market. Price slippage will only be applied when necessary. The MMC will always try to execute a hedge order at the best available price (automatic price improvement).

In case of flow hedge and managed modes, price slippage controls if a client order will be accepted or rejected. It acts in this case as a price and profit protection. The price of any client order hitting the MMC will be compared to the current outbound price (in the example above the price at time T+1). Client orders will only be accepted, if their limit price is within the acceptable slippage as defined by the trader.

To configure price slippage for a specific instrument, open the "Instrument Configuration" dialog, and select option "Pricing". Price slippage can be applied for each pricing tier in column "Slippage". The options are either in percent of current margin, or in absolute PIPS.

| Х<br>Instrument Configuration - EUR/USD      |                                                |                                  |                   |  |
|----------------------------------------------|------------------------------------------------|----------------------------------|-------------------|--|
| Pricing                                      | <b>Pricing.Core Channel 1</b>                  |                                  |                   |  |
| Core Channel 1<br>Core Channel 2             | <b>Spread</b>                                  | <b>Skew</b>                      | <b>Slippage</b>   |  |
| Reference Price Finding                      | M<br>$+$ Pips $-$<br><b>Min</b><br><b>Tier</b> | <b>Skew Factor</b><br><b>Max</b> | %                 |  |
| Core Channel 1                               | $0.5 -$<br>$0.2 -$<br>1 <sub>m</sub>           | ≂<br>$0.2 -$                     | <b>Pips</b><br>8% |  |
| Core Channel 2                               | $0.7 -$<br>$0.5 -$<br>5 <sub>m</sub>           | M.<br>0.5                        | %<br>26%          |  |
| Risk Management<br><b>General Parameters</b> | $1.0 -$<br>$0.8+$<br>10 <sub>m</sub>           | ŀ.<br>0.8                        | $20 -$<br>42%     |  |
| <b>Position Rules</b>                        | $1.2 -$<br>0.0 <sub>1</sub><br>15m             | ŲΞ,<br>0.0                       | $40 -$<br>61%     |  |
| <b>Pricing Rules</b>                         | $0.0 -$<br>$0.0 -$<br>25m                      | t¤<br>$0.0 -$                    | $0 -$<br>87%      |  |
| Quote Filtering                              |                                                | .                                | 0%                |  |
|                                              | Allow Spread less than Market                  |                                  |                   |  |
|                                              | Ⅳ Allow Skew to cross Mid Price                |                                  |                   |  |
|                                              | Allow Skew to cross Opposite Side              |                                  |                   |  |
|                                              | Allow Slippage Worse than Client Order Price   |                                  |                   |  |
| <b>OK</b><br>Cancel<br>Apply                 |                                                |                                  |                   |  |

<span id="page-25-1"></span>Figure 23: Price slippage configuration

By configuring slippage in PIPS, there is always the risk that the actual margin is less than the allowed price slippage. The MMC will automatically limit slippage at the current margin, to prevent traders from accidentally accepting client orders at a loss. This safety measure can be disabled by ticking option "Allow Slippage Worse than Client Order Price".

Configuring price slippage in percent means, a certain percentage of the current margin. If for example the current margin (difference between inbound and outbound price) is 2 PIPS, and the trader configures 50%, he would accept a price slippage of up to 1 PIP. Configuring a price slippage of 100% means, if necessary the trader is willing to give up the entire margin to fill or accept the client order.

Price slippage is set to zero, and "allow slippage worse than client order price" is not enabled, by default!

### <span id="page-25-0"></span>**3.17Quote Filtering**

Problems can arise if the MMC calculates an outbound price based on unreliable inbound quotes such as stale quotes. A quote can be considered as stale if it wasn't updated after the market moved. A B2B hedge order based on a stale quote will often get rejected.

The problem can become more severe with flow hedging and managed positions. In these modes the MMC will accept any client order within an acceptable price slippage range. A subsequent auto-hedge order might result into a financial loss!

Another issue with invalid quotes is revaluation of open positions. A user might set a stop loss rule for an open position. If such a rule is triggered by an invalid quote the position might be closed at an unfavourable price (because the MMC uses market orders to close positions).

Quote filters can help to reduce such risks!

To enable quote filtering for a specific instrument open the "Instrument Configuration" dialog and select option "Quote Filtering":

| Imstrument Configuration - EUR/USD | х                                |
|------------------------------------|----------------------------------|
| Pricing                            | <b>Quote Filtering</b>           |
| Core Channel 1                     | 900 $\div$ ms                    |
| Core Channel 2                     | Remove quotes older than $100 +$ |
| Reference Price Finding            |                                  |
| Core Channel 1                     |                                  |
| Core Channel 2                     |                                  |
| Risk Management                    |                                  |
| <b>General Parameters</b>          |                                  |
| <b>Position Rules</b>              |                                  |
| <b>Pricing Rules</b>               |                                  |
| ♦ Quote Filtering                  |                                  |
|                                    |                                  |
|                                    |                                  |
|                                    |                                  |
|                                    | QK<br>Cancel<br>Apply            |

<span id="page-26-0"></span>Figure 24: Quote filter settings

In example above any EUR/USD quote older than 1,000ms will be removed from the inbound stream. Removed quotes will be added back into the book at the next quote update.

Users can monitor the effect of the quote filter settings in the instrument "Pricing Details" dialog. To open this dialog press on the "Details…" link in the pricing monitor panel. Removed quotes will be displayed in the "Raw Inbound" view as strikethrough prices. See [4.3](#page-29-0) for more information about the Pricing Details.

| $\mathbf x$<br>Pricing Details - EUR/USD Core Channel 2                                                                                                                              |                    |                          |                 |                 |                  |                    |    |  |  |  |  |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|--------------------------|-----------------|-----------------|------------------|--------------------|----|--|--|--|--|
| Raw Inbound<br>$\triangleright$ Raw Inbound<br><b>Filtered Inbound</b><br>1 <sub>m</sub><br>Tier:<br>$\overline{\phantom{a}}$<br>Outbound<br>Snapshot: Fri, 13 Nov 2015 08:03:47 UTC |                    |                          |                 |                 |                  |                    |    |  |  |  |  |
|                                                                                                                                                                                      |                    | <b>Bid</b><br><b>Ask</b> |                 |                 |                  |                    |    |  |  |  |  |
|                                                                                                                                                                                      | <b>Provider</b>    | Quantity                 | Price           | Price           | Quantity         | <b>Provider</b>    |    |  |  |  |  |
|                                                                                                                                                                                      | CITIBANK.DEMO      | 1 <sub>m</sub>           |                 | 1.07771 1.07772 | 2.5 <sub>m</sub> | COBA.DEMO          |    |  |  |  |  |
|                                                                                                                                                                                      | CITIBANK.DEMO      | 2.5 <sub>m</sub>         |                 | 1.07764 1.07778 | 5m               | COBA.DEMO          |    |  |  |  |  |
|                                                                                                                                                                                      | RBS.LND.DEMO       | 1 <sub>m</sub>           |                 | 1.07761 1.07783 | 1 <sub>m</sub>   | CITIBANK.DEMO      |    |  |  |  |  |
|                                                                                                                                                                                      | COBA.DEMO          | 2.5 <sub>m</sub>         | 1.07760 1.07790 |                 | 2.5 <sub>m</sub> | CITIBANK.DEMO      |    |  |  |  |  |
|                                                                                                                                                                                      | Barclays BARX.DEMO | 5m                       |                 | 1.07757 1.07793 | 5m               | Barclays BARX.DEMO |    |  |  |  |  |
|                                                                                                                                                                                      | COBA DEMO          | 5m                       | 1.07754 1.07828 |                 | 1 <sub>m</sub>   | <b>BOAL.DEMO</b>   |    |  |  |  |  |
|                                                                                                                                                                                      | <b>BOAL.DEMO</b>   | 1 <sub>m</sub>           |                 | 1.07727 1.07834 | 2.5 <sub>m</sub> | <b>BOAL.DEMO</b>   |    |  |  |  |  |
|                                                                                                                                                                                      | <b>BOAL,DEMO</b>   | 2.5 <sub>m</sub>         |                 | 1.07721 1.07871 | 1m               | RBS.LND.DEMO       |    |  |  |  |  |
|                                                                                                                                                                                      |                    |                          |                 |                 |                  |                    |    |  |  |  |  |
|                                                                                                                                                                                      |                    |                          |                 |                 |                  | <b>Refresh</b>     | QK |  |  |  |  |

<span id="page-26-1"></span>Figure 25: Monitor filtered quotes

### <span id="page-27-0"></span>**3.18PTMM**

For all requests priced by the MMC, a PTMM (Pre-Trade Mid-Market) rate is published across all FX derivative products and available to the client at the time of quoting as well as post execution.

The PTMM is a non-skewed midrate driven of the market maker liquidity. It is calculated based of the MMC spot "Filtered Inbound" prices (of the respective Channel associated to the client) and the mid-price of the forward points for the requested settlement date.

When the RFQ is quoting, the PTMM is recalculated once a new quote is updated and is also captured at the time of execution.

## <span id="page-28-0"></span>**4 MONITORING PRICING**

### <span id="page-28-1"></span>**4.1 The Pricing Monitor Panel**

The Pricing Monitor tab provides various information for each managed instrument and pricing channel:

- Net Position and P/L in company currency
- Pricing tier size
- Inbound price details
- Outbound price details

The tab contains an emergency "Stop All" button. Clicking this button will stop pricing for **all** instruments and channels, independent who currently owns an instrument.

|                                                                                                 | <b>Pricing Monitor</b> |     |            |            |                    |                                                                               |                    |     |            |          | 圓   | $\Theta$ Stop All |
|-------------------------------------------------------------------------------------------------|------------------------|-----|------------|------------|--------------------|-------------------------------------------------------------------------------|--------------------|-----|------------|----------|-----|-------------------|
| Tier                                                                                            | Core Channel 1         |     |            |            |                    |                                                                               | Core Channel 2     |     |            |          |     |                   |
| E AUD/USD<br>Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR                 |                        |     |            |            |                    |                                                                               |                    |     |            |          |     |                   |
| <b>EUR/USD</b><br>Net Position: 4m EUR P/L: Open -3,572 EUR / Realized 3,608 EUR / Total 37 EUR |                        |     |            |            |                    |                                                                               |                    |     |            |          |     |                   |
|                                                                                                 | Inbound                |     | Details    | Outbound   |                    | Details                                                                       | Inbound<br>Details |     |            | Outbound |     | Details           |
| 1 <sub>m</sub>                                                                                  | 1.36628                |     | 1.36628    | 1.36 620   | $0.2$ $\sqrt{2.0}$ | 1.36640                                                                       | 1.36 628           |     | 1.36628    | 1.36603  | 5.0 | 1.36653           |
| 5 <sub>m</sub>                                                                                  | 1.36628                | 1.4 | 1.36642    | 1.36620    | 0.744              | 1.36 664                                                                      | 1.36628            | 1.4 | 1.36642    | 1.36610  | 5.0 | 1.36660           |
| 10 <sub>m</sub>                                                                                 | 1.36628                | 1.4 | 1.36642    | 1.36619    | 1.15.4             | 1.36673                                                                       | 1.36628            | 1.4 | 1.36642    | 1.36 628 | 14  | 1.36642           |
| 15 <sub>m</sub>                                                                                 | 1.36 628               | 1.4 | 1.36642    | 1.36626    | $1.8 - 5.4$        | 1.36 680                                                                      | 1.36628            | 1.4 | 1.36642    | 1.36 628 | 1,4 | 1.36642           |
| 25 <sub>m</sub>                                                                                 |                        |     |            |            |                    |                                                                               |                    |     |            | н        |     |                   |
|                                                                                                 | □ GBP/USD              |     |            |            |                    | Net Position: 1.2m EUR P/L: Open 1,208 EUR / Realized 0 EUR / Total 1,208 EUR |                    |     |            |          |     |                   |
|                                                                                                 | Inbound                |     | Details    | Outbound   |                    | Details                                                                       | Inbound            |     | Details    | Outbound |     | Details           |
| 2m                                                                                              | $1.67$ 707             | 1.6 | $1.67$ 723 | $1.67$ 707 | 1.6                | $1.67$ 723                                                                    | $1.67$ 707         | 1.2 | 1.67719    | 1.67707  | 1.2 | 1.67719           |
| 5 <sub>m</sub>                                                                                  | $1.67$ 707             | 1.6 | $1.67$ 723 | 1.67692    | 4.6                | 1.67738                                                                       | 1.67707            | 1.4 | $1.67$ 721 | 1.67707  | 1.4 | $1.67$ 721        |
| <b>E USD/INR</b>                                                                                |                        |     |            |            |                    | Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR            |                    |     |            |          |     |                   |
| <b>E USD/JPY</b><br>Net Position: 2.2m EUR P/L: Open -22 EUR / Realized -29 EUR / Total -50 EUR |                        |     |            |            |                    |                                                                               |                    |     |            |          |     |                   |
|                                                                                                 |                        |     |            |            |                    |                                                                               |                    |     |            |          |     |                   |
|                                                                                                 |                        |     |            |            |                    |                                                                               |                    |     |            |          |     |                   |
|                                                                                                 |                        |     |            |            |                    |                                                                               |                    |     |            |          |     |                   |

Each row contains information for a specific instrument.

<span id="page-28-3"></span>Figure 26: Pricing Monitor Panel

The following columns are shown:

- **Tier column**: Shows the tier size. Only configured tiers are shown.
- **Inbound details:** Displays inbound price and spread details for the currently selected channel.
- **Outbound details:** Displays outbound price, spread, and skew (marked with an arrow) details for the currently selected channel.

### <span id="page-28-2"></span>**4.2 Inbound and Outbound Pricing Tier Monitor**

The panel shows for each channel and tier detailed inbound and outbound price information:

|                 | Inbound |     | Details        | Outbound | Details |          |  |
|-----------------|---------|-----|----------------|----------|---------|----------|--|
| 1 <sub>m</sub>  | 1.36648 | 0.9 | 1.36657        | 1.36640  | 0.22.9  | 1.36669  |  |
| 5 <sub>m</sub>  | 1.36643 | 1.4 | 1.36657        | 1.36635  | 0.794.4 | 1.36679  |  |
| 10 <sub>m</sub> | 1.36643 | 1.4 | 1.36657        | 1.36634  | 1.95.4  | 1.36 688 |  |
| 15m             | 1.36643 | 1.4 | 1.36657        | 1.36641  | 1.895.4 | 1.36695  |  |
| 25m             |         |     | $\blacksquare$ |          |         |          |  |

<span id="page-29-1"></span>Figure 27: Pricing Tier monitor

For each currency pair multiple pricing channels can be configured. Each pricing channel can have different spread/skew settings and Reference Price Finding rules.

The assignment of your requesting clients to the different pricing channel prices has to be done by 360T support.

The pricing tier monitor shows one row per pricing tier (e.g. 1m, 5m, …).

![](_page_29_Picture_7.jpeg)

Each row shows the following information:

- Tier size (e.g. 1m)
- Big Figure
- PIPS bid and ask
- Spread blue bar
- Skew orange number shows size and orange arrow shows direction

### <span id="page-29-0"></span>**4.3 Pricing Details Dialog**

Click on the "Details…" link to get a snapshot of current raw, inbound and outbound prices including all details.

A user can select one of the following option in the navigation tree on the left side:

- Raw Inbound to see all available quotes from all liquidity and market data providers in the bank basket.
- Filtered Inbound quotes selected by Reference Price Finding
- Outbound outbound rates for each pricing tier

To see all available quotes from all liquidity and market data providers in bank basket click on "Raw Inbound". Bid quotes are shown on the left, ask quotes are shown on the right. Bank basket can be configured via 360T`s Bank Basket administration panels. For further detail, please contact CAS or your Sales representative.

In "Raw Inbound" panel, bid quotes are sorted in descending order by price, with the best bid rate at the top. Ask quotes are sorted in ascending order by price, with the lowest ask rate at the top. If the strategy for reference price finding is set to `Best Price` or VWAP Average, MMC can use the full amount streams as raw inbound too. In this case, full amount inbound quotes are indicated with an asterisk next to tier size.

At the top of the panel a user can select a specific pricing tier size. Quotes are additionally marked in various colours, depending on the chosen Reference Price Finding settings. Quotes from selected providers are shown in black, all other quotes are shown in grey.

Those quotes selected by Reference Price Finding for the actual outbound price are additionally marked in yellow.

| $\mathbf{x}$<br>PE Pricing Details - EUR/USD Core Channel 1                                                                                                                                 |                          |                       |  |                 |                  |                    |                      |  |  |  |  |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------|-----------------------|--|-----------------|------------------|--------------------|----------------------|--|--|--|--|
| <b>Raw Inbound</b><br>$\triangleright$ Raw Inbound<br><b>Filtered Inbound</b><br>1 <sub>m</sub><br>Tier:<br>$\overline{\phantom{a}}$<br>Outbound<br>Snapshot: Thu, 15 May 2014 13:14:28 UTC |                          |                       |  |                 |                  |                    |                      |  |  |  |  |
|                                                                                                                                                                                             | <b>Bid</b><br><b>Ask</b> |                       |  |                 |                  |                    |                      |  |  |  |  |
|                                                                                                                                                                                             | <b>Provider</b>          | <b>Quantity Price</b> |  | <b>Price</b>    | Quantity         | <b>Provider</b>    |                      |  |  |  |  |
|                                                                                                                                                                                             | Barclays BARX.DEMO       | 5m                    |  | 1.36643 1.36657 | 5 <sub>m</sub>   | Barclays BARX.DEMO |                      |  |  |  |  |
|                                                                                                                                                                                             | RBS.LND.DEMO             | 2.5 <sub>m</sub>      |  | 1.36643 1.36658 | 2.5 <sub>m</sub> | COBA.DEMO          |                      |  |  |  |  |
|                                                                                                                                                                                             | COBA.DEMO                | 2.5 <sub>m</sub>      |  | 1.36642 1.36658 | 1 <sub>m</sub>   | CITIBANK.DEMO      |                      |  |  |  |  |
|                                                                                                                                                                                             | CITIBANK.DEMO            | 1 <sub>m</sub>        |  | 1.36642 1.36666 | 5 <sub>m</sub>   | COBA.DEMO          |                      |  |  |  |  |
|                                                                                                                                                                                             | COBA.DEMO                | 5m                    |  | 1.36634 1.36667 | 3.5 <sub>m</sub> | CITIBANK.DEMO      |                      |  |  |  |  |
|                                                                                                                                                                                             | RBS.LND.DEMO             | 5m                    |  | 1.36634 1.36701 | 1 <sub>m</sub>   | <b>BOAL.DEMO</b>   |                      |  |  |  |  |
|                                                                                                                                                                                             | CITIBANK.DEMO            | 3.5 <sub>m</sub>      |  | 1.36633 1.36708 | 2.5 <sub>m</sub> | <b>BOAL,DEMO</b>   |                      |  |  |  |  |
|                                                                                                                                                                                             | RBS.LND.DEMO             | 7.5 <sub>m</sub>      |  | 1.36629 1.36757 | 2.5 <sub>m</sub> | RBS.LND.DEMO       |                      |  |  |  |  |
|                                                                                                                                                                                             | <b>BOAL.DEMO</b>         | 1 <sub>m</sub>        |  | 1.36599 1.36766 | 5 <sub>m</sub>   | RBS.LND.DEMO       |                      |  |  |  |  |
|                                                                                                                                                                                             | <b>BOAL.DEMO</b>         | 2.5 <sub>m</sub>      |  | 1.36592 1.36771 | 7.5 <sub>m</sub> | RBS.LND.DEMO       |                      |  |  |  |  |
|                                                                                                                                                                                             |                          |                       |  |                 |                  |                    | <b>Refresh</b><br>OK |  |  |  |  |

<span id="page-30-0"></span>Figure 28: Raw Inbound Quote Details

Click on option "Filtered inbound" to see the actual quotes chosen by Reference Price Finding. The panel shows for each tier the selected quotes separated by bid and ask.

| PE Pricing Details - EUR/USD Core Channel 1 |                                         |                                  |  |  |               |                                  |         |  | $\mathbf x$ |  |  |  |
|---------------------------------------------|-----------------------------------------|----------------------------------|--|--|---------------|----------------------------------|---------|--|-------------|--|--|--|
| Raw Inbound                                 | <b>Filtered Inbound</b>                 |                                  |  |  |               |                                  |         |  |             |  |  |  |
| ♦ Filtered Inbound<br>Outbound              | Snapshot: Thu, 15 May 2014 13:14:28 UTC |                                  |  |  |               |                                  |         |  |             |  |  |  |
|                                             | <b>Tier</b>                             | <b>Bid</b>                       |  |  | <b>Spread</b> | <b>Ask</b>                       |         |  |             |  |  |  |
|                                             |                                         | 1.36643                          |  |  |               | 1.36657                          |         |  |             |  |  |  |
|                                             | 1 <sub>m</sub>                          | Barclays BARX.DEMO: 1.36643 (5m) |  |  | 1.4           | Barclays BARX.DEMO: 1.36657 (5m) |         |  |             |  |  |  |
|                                             | 5 <sub>m</sub>                          | 1.36643                          |  |  | 1.4           | 1.36657                          |         |  |             |  |  |  |
|                                             |                                         | Barclays BARX.DEMO: 1.36643 (5m) |  |  |               | Barclays BARX.DEMO: 1.36657 (5m) |         |  |             |  |  |  |
|                                             | 10 <sub>m</sub>                         | 1.36643                          |  |  |               | 1.36657                          |         |  |             |  |  |  |
|                                             |                                         | Barclays BARX.DEMO: 1.36643 (5m) |  |  | 1.4           | Barclays BARX.DEMO: 1.36657 (5m) |         |  |             |  |  |  |
|                                             | 15 <sub>m</sub>                         | 1.36643                          |  |  | 1.4           | 1.36657                          |         |  |             |  |  |  |
|                                             |                                         | Barclays BARX.DEMO: 1.36643 (5m) |  |  |               | Barclays BARX.DEMO: 1.36657 (5m) |         |  |             |  |  |  |
|                                             | Market Best Bid: 1.36643                |                                  |  |  |               |                                  |         |  |             |  |  |  |
|                                             | Market Best Ask: 1.36657                |                                  |  |  |               |                                  |         |  |             |  |  |  |
|                                             |                                         |                                  |  |  |               |                                  | Refresh |  | OK          |  |  |  |

<span id="page-30-1"></span>Figure 29: Filtered Inbound Quote Details

Click on option "Outbound" to see outbound price details by tier:

| <b>PE Pricing Details - EUR/USD Core Channel 1</b>   |                |                                         |                                 |  |                                                                 |     |                                                  |         |             | $\mathbf x$ |
|------------------------------------------------------|----------------|-----------------------------------------|---------------------------------|--|-----------------------------------------------------------------|-----|--------------------------------------------------|---------|-------------|-------------|
| Raw Inbound                                          | Outbound       |                                         |                                 |  |                                                                 |     |                                                  |         |             |             |
| <b>Filtered Inbound</b><br>$\triangleright$ Outbound |                | Snapshot: Thu, 15 May 2014 13:14:28 UTC |                                 |  |                                                                 |     |                                                  |         |             |             |
|                                                      | <b>Tier</b>    |                                         | <b>Inbound</b><br>(Bid/Mid/Ask) |  | <b>Outbound</b><br>(Bid/Mid/Ask)                                |     | <b>Spread</b><br>(Manual/Rule) (Manual/Rule/Cut) |         | <b>Skew</b> |             |
|                                                      | 1 <sub>m</sub> |                                         |                                 |  | 1.36643 1.36650 1.36657 1.36636 1.36653 1.36670                 | 3.4 | 0.0                                              | 0.3     | 0.0         | 0.0         |
|                                                      |                |                                         |                                 |  | 5m   1.36643   1.36650   1.36657   1.36635   1.36657   1.36679  | 4.4 | 0.0                                              | 0.7     | 0.0         | 0.0         |
|                                                      |                |                                         |                                 |  | 10m   1,36643   1,36650   1,36657   1,36634   1,36661   1,36688 | 5.4 | 0.0                                              | 1.1     | 0.0         | 0.0         |
|                                                      |                |                                         |                                 |  | 15m 1.36643 1.36650 1.36657 1.36641 1.36668 1.36695             | 5.4 | 0.0                                              | 1.8     | 0.0         | 0.0         |
|                                                      |                |                                         |                                 |  |                                                                 |     |                                                  |         |             |             |
|                                                      |                |                                         |                                 |  |                                                                 |     |                                                  |         |             |             |
|                                                      |                |                                         |                                 |  |                                                                 |     |                                                  |         |             |             |
|                                                      |                |                                         |                                 |  |                                                                 |     |                                                  |         |             |             |
|                                                      |                |                                         |                                 |  |                                                                 |     |                                                  |         |             |             |
|                                                      |                |                                         |                                 |  |                                                                 |     |                                                  |         |             |             |
|                                                      |                |                                         |                                 |  |                                                                 |     |                                                  | Refresh |             | QK          |

<span id="page-31-0"></span>Figure 30: Outbound Price Details

The panel shows for each pricing tier:

- Tier size
- Inbound quotes
- Outbound quotes
- Manual spread due to configuration
- Automatic spread due to pricing rules
- Manual skew due to configuration
- Automatic skew due to pricing rules
- Skew cut because inbound price reached configured limits

#### **Skew cut:**

Example: bid / ask / mid: 0.9330 / 0.9331 / 0.93305

User sets a manual skew of +1 PIP

In this case the bid rate would cross the mid-rate with 0.5 PIPS

If bid or ask price aren't allowed to cross the mid-rate (the option "Allow Skewing to cross Mid" is not ticked), the price is cut off at the mid-price (in this case by 0.5 PIPS).

Note:

Pressing the "Refresh" button will update the entire dialog with the latest market data snapshot.

# <span id="page-32-0"></span>**5 RISK MANAGEMENT**

Risk Management enables the user to set the notional amount of risk he is prepared or allowed to hold on his own book as well as rules for the Auto Dealer once defined levels are reached.

### <span id="page-32-1"></span>**5.1 Monitoring Positions**

The Managed Position blotter shows currency pair positions for all managed currency pairs, and the trade history for each position in a separate table below. The trade history contains all requester orders and trades from hedge orders, making up the current position.

| <b>Currency Pair Positions</b>                                                          |  |         |                                 |  |  |                                          |      |  |                |             |                           |                           | 圓                                                                                     |
|-----------------------------------------------------------------------------------------|--|---------|---------------------------------|--|--|------------------------------------------|------|--|----------------|-------------|---------------------------|---------------------------|---------------------------------------------------------------------------------------|
| Total Net Position: 7.4m EUR P/L: Open -4,107 EUR / Realized 3,577 EUR / Total -530 EUR |  |         |                                 |  |  |                                          |      |  |                |             |                           |                           |                                                                                       |
| Symbol                                                                                  |  |         |                                 |  |  |                                          |      |  |                |             |                           |                           | Updated Size CCY1 Size CCY2 Open P/L CCY1 Open P/L CCY2 Open P/L Realized P/L CCY1 Re |
| <b>EUR/USD</b>                                                                          |  | 10-10-2 |                                 |  |  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |      |  |                | $-1,228.90$ |                           | $-1,680.00$ $-1,228.90$   | 3,606.23                                                                              |
| USD/JPY                                                                                 |  |         |                                 |  |  | Print Table Content Ctrl-P               |      |  |                | $-6.254.55$ |                           | $-636,000.00$ $-4,575.12$ | $-39.34$                                                                              |
| <b>AUD/USD</b>                                                                          |  |         | <b>Export to CSV</b>            |  |  | Ctrl-E                                   |      |  |                | 0.00        | 0.00                      | 0.00                      | 0                                                                                     |
| <b>GBP/USD</b>                                                                          |  |         |                                 |  |  |                                          |      |  |                | 1,382.81    | 2,320.00                  | 1,697.04                  | 0.00                                                                                  |
| USD/INR                                                                                 |  |         | <sup>1</sup> Amend Position     |  |  | Ctrl+Shift-A                             |      |  |                | 0.00        | 0.00                      | 0.00                      | 0                                                                                     |
|                                                                                         |  |         | $\bigtriangledown$ Set Position |  |  | Ctrl+Shift-S                             |      |  |                |             |                           |                           |                                                                                       |
| <b>EE</b> I <b>BESSIER</b> A Reset Position                                             |  |         |                                 |  |  | Ctrl+Shift-R                             |      |  | 図              |             |                           |                           |                                                                                       |
| Trade                                                                                   |  |         | <b>坐</b> Elatten Position       |  |  | Ctrl+Shift-F                             |      |  |                |             | uantity Price Counterpart |                           |                                                                                       |
| PE-2708                                                                                 |  |         |                                 |  |  |                                          |      |  | $000, 0$ 1.3   |             |                           |                           | Ш                                                                                     |
| PE-2710                                                                                 |  |         |                                 |  |  | Risk Management Ctrl+Shift-M             |      |  | $000, 0$ 1.3   |             |                           |                           |                                                                                       |
| PE-2711                                                                                 |  |         |                                 |  |  |                                          |      |  | $000, 0$ 1.3   |             |                           |                           |                                                                                       |
| $P_{E-270818}$                                                                          |  |         |                                 |  |  | 07:17:5 He PE-27                         | Sell |  | $1,000,0$ 1.3. |             |                           |                           |                                                                                       |
| $SO-196167$                                                                             |  |         | $ 07:17:5 $ Fill                |  |  |                                          | Sell |  |                |             | 1,000,0 1.3 RBS.LND.D     |                           |                                                                                       |
| PE-270806                                                                               |  |         |                                 |  |  | 07:17:5 RFS 360T.F Buy                   |      |  | $1,000,0$ 1.3. |             |                           |                           |                                                                                       |
| $P_{E-271098}$                                                                          |  |         |                                 |  |  | 12:15:2 He PE-27                         | Sell |  | $1,000,0$ 1.3. |             |                           |                           |                                                                                       |
| SO-196196 12:15:2 Fill                                                                  |  |         |                                 |  |  |                                          | Sell |  |                |             | 1,000,0 1.3 RBS.LND.D     |                           |                                                                                       |
| PE-270907                                                                               |  |         |                                 |  |  | 07:54:0 RFS 360TF Buy                    |      |  | 500,000 1.3    |             |                           |                           |                                                                                       |

<span id="page-32-2"></span>Figure 31: Managed Positions blotter with context menu

### **Note:**

By default, positions will be reset at the server side with every new trading day. The exact time is when the value date is rolled. Thus the trade history shows at most the requester order and hedge trades from the current day.

For users who want to keep their position overnight, there is a global configuration which can be done via 360T CAS.

#### The trade history will be wiped/erased with **every manual position reset**!

The upper table shows currency pair position information for each managed instrument. The following columns are available:

- Position size in CCY1 and CCY2
- Time of last update
- Open P/L in CCY1, CCY2, and company currency
- Realized P/L in CCY1, CCY2, and company currency
- Total P/L in CCY1, CCY2, and company currency
- Revaluation rate (rate which was used to calculate open P/L)
- Average buy and sell price

Column sorting can be changed by clicking a column and moving it left or right.

Trade history for each position is shown in the lower table. Select a specific position in the upper table to see the trade history in the lower table. The table shows both client and hedge orders. For hedge orders additionally all executions are shown as children.

#### **Trade history columns:**

- Trade ID a unique ID for each trade
- Created timestamp when the trade was created
- Side buy or sell
- Quantity order or trade quantity
- Price requested or executed price
- Counterparty liquidity provider who filled the hedge order
- Type source system for requester orders (e.g. RFS, SEP) or hedge
- Trigger originating user of the requester order, or hedge order trigger reason

#### **Context menu:**

Right clicking into the blotter data opens the context menu.

For instruments currently owned by the user this dialog offers position blotter specific options:

- Amend Position: Select to amend the position for a specific quantity and price
- Set Position: Select to set the position to a specific size and price
- Reset Position: Reset the position to zero and erase the order and trade history
- Flatten Position: Select to **execute a hedge order in the market** to close the position
- Risk Management: Opens the Risk Management configuration dialog

### <span id="page-33-0"></span>**5.2 Profit and Loss (P/L) Calculations**

Open positions are periodically (every 500ms) revaluated mark-to-market with the best available inbound price to calculate **open P/L**. Long positions are revaluated with best bid price, short positions are revaluated with best ask price.

P/L is calculated by one of these two methods:

- **Average cost method**: This method calculates P/L by taking the difference of how much was spent to build the position, and how much was earned by closing the position
- **First-In, First-out method (FIFO):** This method assumes that every SELL trade covers the oldest open BUY trade, and vice versa

The chosen P/L calculation method is a system setting and affects all users. By default the "average cost" method is used. **To configure your preferred P/L calculation method please contact 360T Support.**

### <span id="page-33-1"></span>**5.3 Risk Management Configuration**

To configure risk management for a specific instrument select option "Risk Management" in the instrument configuration dialog:

| <b>IEI</b> Instrument Configuration - EUR/USD   |                                                                                                                                                                                                                                                      | $\mathbf x$          |
|-------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------|
| <b>Pricing</b>                                  | <b>Risk Management.Position Rules</b>                                                                                                                                                                                                                |                      |
| Core Channel 1<br>Core Channel 2                | <b>Position Rules</b>                                                                                                                                                                                                                                | $+$                  |
| Reference Price Finding                         | 10,000,000 Back2Back<br>$0 -$<br><b>V</b> Position<br>$\mathbf{v}$ $>$ $=$ $\mathbf{v}$<br>$\uparrow$<br>$\overline{\phantom{a}}$                                                                                                                    | $\mathbf x$          |
| Core Channel 1<br>Core Channel 2                | 5,000 Flatten<br>$0$ $\Rightarrow$<br>Open Loss<br>$\  \mathbf{v} \ _{\geq \alpha}$<br>$\uparrow$<br>$\mathbf{r}$<br>$\downarrow$                                                                                                                    | $\mathsf{x}$         |
| Risk Management<br><b>General Parameters</b>    | 3,000 F Flatten<br>$0$ $\neq$ $\uparrow$ $\parallel$<br>Open Profit<br>$\  \cdot \  \cdot \ $<br>▼                                                                                                                                                   | $\parallel$ $\times$ |
| <b>▶ Position Rules</b><br><b>Pricing Rules</b> |                                                                                                                                                                                                                                                      |                      |
|                                                 | B2B<br>$\bullet$ Alert $\blacktriangle$ Pos.                                                                                                                                                                                                         |                      |
|                                                 | $\mathbf{1}$<br>$\circ$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>0.2<br>0.6<br>0.1<br>0.2<br>0.5<br>0.5<br>$\overline{\mathbf{a}} \cdot \mathbf{e}$<br>0.7<br>0.8<br>0.3<br>0.3<br>0.4<br>0.4<br>0.7<br>0.8<br>0.9<br>7.0<br>0.1<br>0.9<br>0.0 | $1.0 \quad 1.1$      |
|                                                 | $\overline{OK}$<br>Cancel                                                                                                                                                                                                                            | <b>Apply</b>         |

<span id="page-34-1"></span>Figure 32: Risk management position rules

Click the + button in the upper right corner to add an additional rule. Rules are top down in priority order. Click arrow down / up, to change the order, x to cancel a rule.

### <span id="page-34-0"></span>**5.4 Position Rules**

The following various rule types will be used to automatically manage position size. Position rules can be based on one of the following options (triggers) for the selected position:

- **Position:** The absolute position size (long and short positions are treated equally)
- **Total Loss:** The negative total P/L
- **Open Profit:** The positive open P/L
- **Open Loss:** The negative open P/L
- **Open Profit (PIPS):** Positive open P/L as a price difference to average position price
- **Open Loss (PIPS):** Negative open P/L as a price difference to average position price

The selected trigger is compared to a specified value with either greater (>), or greaterequals (>=).

As soon as a rule is triggered, the specified action will be executed. The available **actions** are:

- **Back2Back:** The client order will only be accepted after it was successfully hedged (last look)
- **Alert:** Raises an alert to the user when the trigger level is reached
- **Reduce by:** Reduces the position by the specified amount
- **Reduce by %:** Reduces the position by the specified percentage of its current size
- **Reduce to:** Reduces the position to the specified size
- **Flow hedge:** Hedge the client order immediately (no last look)
- **Flatten:** Close the position
- **Switch to Back2Back:** Switch the risk management mode to B2B for this instrument
- **Switch to Flow Hedge**  switch the risk management mode to flow hedging for this instrument

- **Place relative to TOB**: Places a pegged order to 360T`s Central Order Book (COB). (Note: Only available for 360T ECN participants).
- **Stop Pricing for instrument**: Stop publishing outbound prices for the instrument when the trigger is reached.

Back-to-Back (B2B) means that the requester order will only be accepted after it was successfully hedged (last look). A B2B rule which checks a certain position size is triggered when the current position amount, plus the notional of the requester order, would breach the specified limit.

Position rules are checked and possibly triggered:

- Once with each client order
- Periodically All rules are evaluated every 500ms

When a position size rule is triggered, **it will emit a hedge order** to the market based on the specified rule action.

The periodic rules review ensures that positions are regularly reviewed even if no requester order is imminent. This is also a retry mechanism for failed or partially filled hedge orders. Instead of re-attempting the same failed hedge order, the system will simply compare positions against rules at the next periodic review.

### **Note:**

**Even though rules are reviewed every 500ms, for safety reasons the system will wait (by default) at least 3 seconds between subsequent hedge orders for the same currency pair.** 

#### **Example flatten position:**

A rule action could be Flatten. When this rule is triggered, a hedge order will be created to close the entire position in the market. If this hedge order fails or is only partially filled, another hedge order with the remaining position size will be created **latest after 3 seconds.** The hedge order will be created earlier, if there is a requester order for the same currency pair before the 3 seconds expired.

**The auto-hedger will always make sure that:**

- **Positions are not over hedged (flips sides because of a hedge order)**
- **Position never increase because of a hedge order**

#### **Flow Hedging:**

As the name suggests, flow hedging means to hedge the flow of requester order one-to-one. Every incoming requester order will be first executed and the position will be updated accordingly. When the updated position breaches a defined limit, and the specified action is Flow Hedge a reverse hedge order of the same size than the requester order will be emitted to the market.

The goal of flow hedging is to accept all requester orders, without letting positions grow (no guarantee).

#### **Rule based risk management mode switching:**

Actions "Switch to Back2Back" and "Switch to Flow hedge" can be used to automatically switch the risk management mode based on certain criteria. An example is to switch to B2B mode when the total loss for a specific instrument breached a threshold, to avoid any further losses.

#### **Note:**

Switching the risk management mode back to "Managed" is a manual operation to be performed in the Pricing Control tab!

#### **Note:**

- "Reduce by" shall be used with care. Every requester order is a single trigger. A position could grow very quickly if the specified "reduce by"- quantity is small compared to the requester order size!
- Consider to use "Reduce by" and "Reduce by %" with a position size trigger. A P/L based rule can be triggered for any position size, and it is difficult to predict the position size when the rule fires.
- Flow hedging is an option to avoid requester order rejections, and avoid position growth, but because flow hedging only **attempts** to hedge **after** the requester order was accepted, there is always the possibility that position still grows quickly!

## **It is advisable to always specify a Back2Back rule to limit the maximum position size!**

**Without such a rule, positions can potentially increase unreasonably result in a loss in short time!**

### <span id="page-36-0"></span>**5.5 Pricing Rules**

Pricing Rules define how to modify spreads and skew based on position size or P/L.

| <b>Imaging Instrument Configuration - EUR/USD</b>  | $\mathbf x$                                                                                                                                                 |
|----------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Pricing</b>                                     | <b>Risk Management.Pricing Rules</b>                                                                                                                        |
| Core Channel 1<br>Core Channel 2                   | <b>Pricing Rules</b><br>$+$                                                                                                                                 |
| Reference Price Finding<br>Core Channel 1          | 5,000,000 = Skew%<br>$10$ $\pm$<br><b>V</b> Position<br>$\vert \cdot \vert \cdot \vert =$<br>$\overline{\phantom{a}}$<br>$\uparrow$<br>$\pmb{\times}$       |
| Core Channel 2                                     | 8,000,000 Skew%<br>$20$ $\Rightarrow$<br>$\vee$ Position<br>$\uparrow$<br>$\  \cdot \ _{\geq} = \top$<br>$\overline{\phantom{a}}$<br>т<br>$\mathbf{x}$      |
| Risk Management                                    | 10,000,000 Skew%<br>$30 =$<br><b>V</b> Position<br>$\  \cdot \ $ $>$ $=$ $\  \cdot \ $<br>$\uparrow$<br>$\blacktriangledown$<br>т<br>$\mathbf{x}$           |
| <b>General Parameters</b><br><b>Position Rules</b> | 10,000 Spread% -<br>$20 -$<br><b>V</b> Total Loss<br>$\Vert \mathbf{v} \Vert \Vert \mathbf{v} = \Vert \mathbf{v} \Vert$<br>$1$ $\vert \cdot \vert$ $\times$ |
| <b>▶ Pricing Rules</b>                             |                                                                                                                                                             |
|                                                    |                                                                                                                                                             |
|                                                    |                                                                                                                                                             |
|                                                    | Skew % - Spread %                                                                                                                                           |
|                                                    | 30<br>25                                                                                                                                                    |
|                                                    | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>15                                                                                                              |
|                                                    | 10<br>5<br>$\alpha$                                                                                                                                         |
|                                                    | 7.0<br>3.0<br>4.0<br>0.5<br>1.5<br>2.0<br>2.5<br>3.5<br>4.5<br>5.0<br>5.5<br>6.0<br>6.5<br>9.0<br>9.5<br>10.0 10.5<br>0.0<br>7.0<br>7.5<br>8.0<br>8.5       |
|                                                    | QK<br>Cancel<br>Apply                                                                                                                                       |

<span id="page-37-1"></span>Figure 33: Risk management pricing rules

Spread and skew due to such rules is added on top of spread and skew configured in the Pricing Controller tab.

The chart shows the effective "skew curve" as defined by the rules.

### <span id="page-37-0"></span>**5.6 Alert Rules**

Within position rules, a user can create Alert rules to receive a notification when a position size or P/L reaches a certain limit.

When an Alert level is reached, the user receives a pop up notification **and** an acoustic signal.

![](_page_37_Picture_9.jpeg)

Figure 34 Alert popup message

#### <span id="page-37-2"></span>**Note:**

When you acknowledge an Alert, the respective rule will be disabled. **You have to manage the position and thereafter tick the rule again and apply it!**

### <span id="page-38-0"></span>**5.7 Manual Position Amendments**

Positions can be manually amended for each managed currency pair. To do so, select one of the following options in the position blotter context menu.

| F Expand All<br>□ Collapse All |              |
|--------------------------------|--------------|
| <b>Print Table Content</b>     | Ctrl-P       |
| <b>Export to CSV</b>           | Ctrl-E       |
| Amend Position                 | Ctrl+Shift-A |
| Set Position                   | Ctrl+Shift-S |
| <b>Reset Position</b>          | Ctrl+Shift-R |
| <b>V</b> Flatten Position      | Ctrl+Shift-F |
| Risk Management Ctrl+Shift-M   |              |

<span id="page-38-1"></span>Figure 35 Context menu of Managed Positions

| x<br><b>PEI</b> Amend Position for USD/HKD |
|--------------------------------------------|
| 31 SC<br><b>TRADING NETWORKS</b>           |
|                                            |
| 0.0000                                     |
|                                            |
| OK<br>Cancel                               |
|                                            |

<span id="page-38-2"></span>Figure 36 Amend Position

**Amend** a position: Enter a quantity and price and click the "OK" button. This will add the specified quantity to the position and impact the P/L based on the captured price. To reduce the position, enter a negative quantity.

| $\overline{\mathbf{x}}$<br><b>PE Set Position for USD/HKD</b> |                         |  |  |  |  |  |  |
|---------------------------------------------------------------|-------------------------|--|--|--|--|--|--|
|                                                               | <b>TRADING NETWORKS</b> |  |  |  |  |  |  |
| Amount:                                                       |                         |  |  |  |  |  |  |
| Price:                                                        | 0.0000                  |  |  |  |  |  |  |
|                                                               | OK<br>Cancel            |  |  |  |  |  |  |

<span id="page-38-3"></span>Figure 37 Set Position

**Set** a position: Enter a quantity and price and click the "OK" button. This will set the position to the specified position size and impact the P/L based on the captured price.

**Reset** a position: Choose the Reset command and the entire position will be erased and reset to zero. A message is displayed in order to request a confirmation for the reset before it is executed.

| <b>PE Confirm Reset Position</b>                                                                                                                  |
|---------------------------------------------------------------------------------------------------------------------------------------------------|
| NETWORKS<br>TRADING                                                                                                                               |
| Please confirm that you want to reset your USD/HKD position. The entire position information<br>including order and trade history will be purged. |
| Yes<br>No                                                                                                                                         |

<span id="page-38-4"></span>Figure 38 Confirmation of Position Reset

None of the above actions will create "real" deals with the market!

**Flatten** a position: Choose the Flatten command to create a hedge order to close the position IN THE MARKET. This action will create real deals with external market makers.

| <b>PE</b> Confirm Flatten Position<br>w                                                   |  |
|-------------------------------------------------------------------------------------------|--|
| <b>TRADING NETWORKS</b>                                                                   |  |
| Please confirm that you want to flatten your USD/HKD position by executing a hedge order. |  |
| <b>Yes</b><br>No                                                                          |  |

<span id="page-39-1"></span>Figure 39 Confirmation of Position Flattening

#### **Note:**

Flatten will attempt to execute your Notional position out in the market back to zero, you will be asked to confirm this action and if it is unable to bring it to zero, you will receive a notification.

### <span id="page-39-0"></span>**5.8 Auto-Hedging Safeguards**

The MMC has built in certain so called "Safeguards" to limit the risk of eventual losses in case of technical problems. Certain safeguard parameters can be adjusted by users.

#### **Maximum hedge order size:**

The maximum hedge order size limits the order quantity of auto-hedge orders. The default value is 10 million in base currency. Users can adjust this value individually for each managed instrument. The upper limit for this parameter is 50 million.

To adjust the maximum hedge order size or a specific managed instrument, open the instrument configuration dialog, and select option "General Parameters":

| <b>EUR/USD</b> Instrument Configuration - EUR/USD |                                                |       |  |  |  |  |  |
|---------------------------------------------------|------------------------------------------------|-------|--|--|--|--|--|
| Pricing                                           | <b>Risk Management.General Parameters</b>      |       |  |  |  |  |  |
| Core Channel 1<br>Core Channel 2                  | 10,000,000<br><b>Maximum Hedge Order Size:</b> |       |  |  |  |  |  |
| Reference Price Finding                           |                                                |       |  |  |  |  |  |
| Core Channel 1<br>Core Channel 2                  |                                                |       |  |  |  |  |  |
| Risk Management                                   |                                                |       |  |  |  |  |  |
| General Parameters<br><b>Position Rules</b>       |                                                |       |  |  |  |  |  |
| <b>Pricing Rules</b>                              |                                                |       |  |  |  |  |  |
| Quote Filtering                                   |                                                |       |  |  |  |  |  |
|                                                   |                                                |       |  |  |  |  |  |
|                                                   | QK<br>Cancel                                   | Apply |  |  |  |  |  |

<span id="page-39-2"></span>Figure 40 Maximum Hedge Order Size Configuration

Independent of position size and auto-hedging rules, no auto-hedge order will be larger than this value.

Example:

• The maximum hedge order size is set to 10 million

- A user defines a position rule to flatten the position if it is larger than 20 million
- The position reaches 22 million

The MMC will create a first hedge order over 10 million. Assuming the hedge order got fully filled, the position will go down to 12 million. At the next position review, the position rule will trigger again, and a second hedge order over 10 million will be created.

#### Note:

This parameter only applies to instruments with risk management mode "Managed". This parameter has no effect on back-to-back or flow hedge orders.

#### **Minimum hedge order size:**

**The minimum hedge order size is an auto hedge order safety mechanism which prevents any auto hedge order to be placed below certain amount in order to avoid a larger number of small hedge order attempts. The size is set to 100.000 in base currency by default. Please contact CAS in case you would like to amend the minimum hedge order size.**

### <span id="page-40-0"></span>**5.9 Restrict the bank basket for hedge orders**

By default, when the MMC places hedge orders, quotes of the entire client's bank basket are considered. Users can restrict the bank basket for B2B and flow hedge orders to make sure hedge orders are only sent to selected providers.

To restrict the hedge order bank basket for a specific instrument, open the "Instrument Configuration" dialog, and select option "Reference Price Finding".

| $\mathbf x$<br><b>Example 1</b> Instrument Configuration - EUR/USD |                                                                                                                                                   |                 |                                                                                 |  |  |  |
|--------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|---------------------------------------------------------------------------------|--|--|--|
| <b>Pricing</b>                                                     | Reference Price Finding.Core Channel 1                                                                                                            |                 |                                                                                 |  |  |  |
| Core Channel 1<br>Core Channel 2                                   | <b>Providers</b><br><b>Tier</b>                                                                                                                   | <b>Hedge</b>    | <b>Strategy</b>                                                                 |  |  |  |
| Reference Price Finding<br>Core Channel 1                          | BOAL.DEMO; Barclays ▼<br>1 <sub>m</sub>                                                                                                           |                 | Min. Quote Size Min. Providers<br>1,000,000<br>ALL⊢<br><b>Best Price</b>        |  |  |  |
| Core Channel 2<br>Risk Management                                  | BOAL.DEMO; Barclays ▼<br>5 <sub>m</sub>                                                                                                           | $\triangledown$ | Min. Quote Size Min. Providers<br>5,000,000<br>ALL $\div$<br><b>Best Price</b>  |  |  |  |
| <b>General Parameters</b><br><b>Position Rules</b>                 | <b>Barclays BARX.DEMO</b><br>10 <sub>m</sub>                                                                                                      | ☑               | Min. Quote Size Min. Providers<br>10,000,000<br>$ALL -$<br><b>Best Price</b>    |  |  |  |
| <b>Pricing Rules</b><br>Quote Filtering                            | CITIBANK.DEMO<br>15 <sub>m</sub>                                                                                                                  |                 | Min. Quote Size Min. Providers<br>15,000,000<br><b>ALL</b><br><b>Best Price</b> |  |  |  |
|                                                                    | 25 <sub>m</sub>                                                                                                                                   |                 | Min. Quote Size Min. Providers<br>25,000,000<br><b>ALL</b><br><b>Best Price</b> |  |  |  |
|                                                                    | <b>Override</b><br>All tiers best price and minimum quote size at least tier size<br>Allow VWAP price calculation for less than required quantity |                 |                                                                                 |  |  |  |
|                                                                    |                                                                                                                                                   |                 | OK<br>Cancel<br><b>Apply</b>                                                    |  |  |  |

<span id="page-40-1"></span>Figure 41: Restrict hedge order bank basket

To restrict the hedge order bank basket for a specific pricing tier, tick the checkbox in column "Hedge". Any client order hitting this pricing tier, will be hedged with the selected providers for the same tier.

## <span id="page-41-0"></span>**5.10Client Order Handling Rules**

Client Order Handling rules allow users to setup rules to override the instrument's risk management mode for individual client orders. Users can setup an arbitrary number of such client order handling rules. The configuration can be instrument specific but also across multiple or all instruments using wildcards.

Examples for client order handling rules:

- All client orders for EUR/USD up to 1m quantity shall be managed on the position, but all orders larger than 1m shall be flow hedged.
- Pricing for instrument GBP/USD is configured for up to 10m with risk management mode "Flow Hedging". All client orders larger than 5m from counterparty "ABC Ltd." shall be hedged back-to-back.

A client order handling rule consists of client order matching parameters, and an action. The action defines what to do with the client order if the rule matches. If more than one rule is defined, only the first matching rule will be executed!

Parameters to match client orders are:

- **Instrument** … either exactly (e.g. EUR/USD) or defined with wildcards (e.g. EUR/\*)
- **Order quantity** and comparison operator (bigger, bigger equals, equals, smaller equals, smaller)
- **Counterparty** and comparison operator to match exactly (e.g. equals "ABC Ltd.") but also pattern matching (all counterparties starting with "Bank").

Possible actions are:

- Hedge B2B
- Flow Hedge
- Reject (to reject a client order)

A client order handling rule can **override** the current risk management mode of an instrument, but only **towards less risk**! As an example, if the current risk management mode is "Flow Hedge", a client order processing rule can only force order handling with "B2B" or "Reject" but not "Managed".

The following table provides an overview of possible actions for each instrument risk management mode:

| Instr. risk management mode | Managed | Flow Hedge | B2B | Reject |
|-----------------------------|---------|------------|-----|--------|
| Managed                     | Yes     | Yes        | Yes | Yes    |
| Flow Hedge                  | No      | Yes        | Yes | Yes    |
| B2B                         | No      | No         | Yes | Yes    |

Client order handling rules where the action is identical with the current risk management mode will be matched, but have no effect!

To configure client order handling rules, open the "Global Instrument Configuration" dialog and select option "Client Order Handling". Click on the "+" button to add new rules:

| $\mathbf{x}$<br><b>ESS</b> Global Instrument Configuration |                         |                              |                                                     |                                   |            |                                             |  |                                    |            |                           |                       |  |
|------------------------------------------------------------|-------------------------|------------------------------|-----------------------------------------------------|-----------------------------------|------------|---------------------------------------------|--|------------------------------------|------------|---------------------------|-----------------------|--|
| <b>General Parameters</b>                                  |                         | <b>Client Order Handling</b> |                                                     |                                   |            |                                             |  |                                    |            |                           |                       |  |
| <b>Instruments</b><br><b>Tiers</b>                         |                         |                              | Instrument                                          |                                   | Quantity   | Counterparty                                |  | Action                             | $+$        | Test                      |                       |  |
| <b>Scenarios</b>                                           | $\overline{\mathsf{v}}$ | ÷                            | $\overline{\phantom{a}}$                            | $\Vert \mathbf{v} \Vert_{\geq 0}$ |            | 2,000,000 Ends With v Fund                  |  | Reject<br>▾                        | T.         | Ť.                        | ×                     |  |
| <b>Cross Rules</b>                                         | $\overline{\mathsf{v}}$ | ¥.                           | $\mathbf{v}$ USD $\mathbf{v}$                       | $\overline{\phantom{a}}$          |            | 0 Exactly as $\blacktriangleright$ Abc Corp |  | B <sub>2</sub> B<br>$\blacksquare$ | $\uparrow$ | Ť                         | $\boldsymbol{\times}$ |  |
| ♦ Client Order Handling                                    | $\blacktriangledown$    |                              | EUR $\blacktriangleright$ USD $\blacktriangleright$ | $\overline{\phantom{a}}$          | 3,000,000  | $\overline{\phantom{a}}$                    |  | Flow Hed $\blacktriangledown$      | $\uparrow$ | $\downarrow$              | ×                     |  |
|                                                            | $\overline{\mathsf{v}}$ | ×.                           | $\mathbf{v}$ $\mathbf{x}$                           | $\overline{\phantom{a}}$<br>▼     | 10,000,000 | $\mathbf{r}$                                |  | B <sub>2</sub> B                   | $-1$       | $\downarrow$ $\mathbf{X}$ |                       |  |
|                                                            |                         |                              |                                                     |                                   |            |                                             |  |                                    |            |                           |                       |  |
|                                                            |                         |                              |                                                     |                                   |            |                                             |  | QK                                 | Cancel     |                           | <b>Apply</b>          |  |

<span id="page-42-0"></span>Figure 42: Manage Client Order Handling Rules

Rules are always evaluated for a match starting at the top of the list towards the bottom. The first matching rule will be executed, all remaining rules will be ignored.

#### Explanation for the examples above:

Rule 1: Any client order bigger or equals 2m from a counterparty ending with "Fund" will be rejected

Rule 2: Any client order with terms currency USD from "Abc Corp" will be hedged B2B

Rule 3: Any EUR/USD client order with quantity bigger than 3m will be flow hedged

Rule 4: Any client order with quantity bigger or equals 10m will be hedged B2B

It is important to order rules in the right way! More restrictive rules should be placed before more generic rules. An example are rules 2 and 3. If rule 3 would be placed before rule 2, orders from "Abc Corp" bigger than 3m would be flow hedged! Assuming a user wants to hedge any order \*/USD from "Abc Corp" B2B, it is important to make sure that a more generic rule doesn't override it.

#### **Counterparty comparison operators:**

The system offers a number of comparison operators to match counterparties in various ways:

| Operator    | Explanation                                                   |
|-------------|---------------------------------------------------------------|
| Exactly as  | Compares for an exact match of the specified counterparty     |
| All except  | Matches all counterparties except the specified one           |
| Contains    | Matches any counterparty which contains the specified text    |
| Starts With | Matches any counterparty which starts with the specified text |
| Ends With   | Matches any counterparty which ends with the specified text   |
| All like    | Complex pattern search using Java regular expressions         |

#### **Counterparty pattern matching:**

The provided standard counterparty comparison operators should be sufficient for most cases to match either a specific counterparty, or a group of counterparties. Operator "All like" provides an alternative in cases where the standard operators don't help. This operator uses so called Java regular expressions to match counterparties. Such regular expressions provide extreme flexibility but can be eventually complex to define.

The general syntax for Java regular expression can be found in the appendix chapter [8.1.](#page-48-2) Further information about Java regular expressions can be found in the internet in the official Java documentation (https://docs.oracle.com/javase/tutorial/essential/regex/).

#### **Rule testing:**

Rules can be tested without any risk with a dedicated testing dialog. Click on button "Test" to open the "Client Order Handling Testing" dialog:

| <b>MAC</b>                           | Х                        |  |  |  |  |  |  |  |
|--------------------------------------|--------------------------|--|--|--|--|--|--|--|
| <b>Client Order Handling Testing</b> |                          |  |  |  |  |  |  |  |
| <b>Client Order Parameters</b>       |                          |  |  |  |  |  |  |  |
| Instrument                           | <b>USD</b><br><b>EUR</b> |  |  |  |  |  |  |  |
| Quantity                             | 3,000,000                |  |  |  |  |  |  |  |
| Counterparty                         | <b>Test Comp</b>         |  |  |  |  |  |  |  |
| <b>Instrument Settings</b>           |                          |  |  |  |  |  |  |  |
| <b>Risk Management</b>               | Managed                  |  |  |  |  |  |  |  |
| <b>Test Results</b>                  |                          |  |  |  |  |  |  |  |
| <b>Rule Result</b>                   | <b>Flow Hedge</b>        |  |  |  |  |  |  |  |
| <b>Effective action</b>              | Flow Hedge               |  |  |  |  |  |  |  |
|                                      | Close<br><b>Test</b>     |  |  |  |  |  |  |  |
|                                      |                          |  |  |  |  |  |  |  |

<span id="page-43-1"></span>Figure 43: Client Order Handling Rule testing

This dialog can be used to simulate client orders to verify if any of the defined rules will match. To test the current set of rules, specify a currency pair, order quantity, counterparty, and press on the "Test" button. The action of the first matching rule will be displayed in the "Rule Result" field. If no matching rule can be found "No match" will be displayed. New rules and changes can be tested without applying the change!

As mentioned earlier, a client order handling rule can only override the instrument's risk management mode towards less risk. In other words, a rule defines an action, but the effective action will be determined in combination with the instrument's risk management mode. This behavior can be simulated in the rule testing dialog too by selecting a risk management mode. The effective action will be either the result of a matching rule, or the current instrument's risk management mode.

### <span id="page-43-0"></span>**5.11Pricing and Risk Management Scenarios**

Pricing and Risk management scenarios (or short scenarios) allow users to quickly adjust pricing and risk management settings with one mouse click. Users can define an arbitrary number of such scenarios.

Each scenario defines:

- Name
- Spread factor
- Maximum quote size
- Risk management mode

Only the name is mandatory, all other parameters are optional.

If set, parameters influence pricing and risk management in various ways. The current outbound spread will be widened by the selected spread factor. A spread factor of e.g. 2, will widen the outbound spread by 100%. Maximum quote size can be used to limit the available liquidity for your requesters. If e.g. 5m maximum quote size is chosen, any pricing tier above 5m will be disabled. The selected risk management mode of the scenario will override the current instrument's risk management mode, but only towards less risk. If e.g. the current instruments risk management mode is "B2B" and the scenario defines "Flow Hedge", the parameter will be ignored.

Scenarios are defined globally and can be applied to any managed instrument. Scenarios override current settings only temporarily as long as the scenario is applied.

To add a new scenario open the "Global Instrument Configuration" dialog and select option "Scenarios":

| Global Instrument Configuration                                            |                                      |                              |                        |                                | $\mathbf x$                              |  |  |  |  |  |
|----------------------------------------------------------------------------|--------------------------------------|------------------------------|------------------------|--------------------------------|------------------------------------------|--|--|--|--|--|
| <b>General Parameters</b><br>Instruments<br>Tiers<br>$\triangle$ Scenarios | <b>Scenarios</b>                     |                              |                        |                                |                                          |  |  |  |  |  |
|                                                                            | <b>Name</b><br><b>Moderate</b>       | <b>Spread Fac</b><br>$1.5 -$ | Max. Quot<br>Undefined | <b>Risk Managem</b>            | $+$<br>X                                 |  |  |  |  |  |
| <b>Cross Rules</b><br><b>Client Order Handling</b>                         | <b>Volatile</b><br><b>Unattended</b> | $2\div$<br>$1.0 -$           | 5,000,000<br>3,000,000 | Flow Hedge<br>B <sub>2</sub> B | ▼<br>X<br>$\blacksquare$<br>$\mathbf{x}$ |  |  |  |  |  |
|                                                                            |                                      |                              |                        |                                |                                          |  |  |  |  |  |
|                                                                            |                                      |                              |                        |                                |                                          |  |  |  |  |  |
|                                                                            |                                      |                              |                        |                                |                                          |  |  |  |  |  |
|                                                                            |                                      |                              |                        |                                |                                          |  |  |  |  |  |
|                                                                            |                                      |                              | QK                     | Cancel                         | <b>Apply</b>                             |  |  |  |  |  |

<span id="page-44-0"></span>Figure 44: Pricing and Risk Management Scenarios

Scenarios can be applied to instruments in the "Pricing Control" panel.

| <b>Pricing Control</b> Trade Blotter     |              |                 |                                          |                         |                          |           |                  |                 |  |
|------------------------------------------|--------------|-----------------|------------------------------------------|-------------------------|--------------------------|-----------|------------------|-----------------|--|
|                                          |              | Managed         | Control                                  |                         |                          |           |                  |                 |  |
| Instrument/Channel ▲ <sup>1</sup> Status |              | By              | <b>Start</b>                             |                         | Stop Ownership Configure |           | Mode             | Scenario        |  |
| <b>All Instruments</b>                   | $\mathbf{O}$ |                 | U                                        | O                       |                          | $\bullet$ | Change           | Change          |  |
| I ⊞ EUR/USD                              | O            | PEEMEA1.Trader1 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                         | Take                     | o         | B <sub>2</sub> B | Moderate        |  |
| I⊞ GBP/USD                               | $\mathbf{O}$ | PEEMEA1.Trader1 |                                          |                         | Take                     | $\bullet$ | Flow Hedge       | <b>Volatile</b> |  |
| I⊞ USD/CAD                               | O            | PEEMEA1.Trader1 | $\bigcirc$                               | $\overline{\mathbf{O}}$ | Take                     | $\bullet$ | <b>Managed</b>   |                 |  |

<span id="page-44-1"></span>Figure 45: Scenario selection

If not visible at startup, the column "Scenario" can be added by right clicking on the panel header and selecting the option "Choose Columns …".

The current scenario can be changed for all instruments simultaneously by selecting a scenario in the "All Instruments" row.

# <span id="page-45-0"></span>**6 BLOTTERS**

The MMC offers various blotters to monitor orders, trade flow, and positions. The blotter currently available are:

- Currency Pair Position Blotter
- Client Order Blotter
- Hedge Order Blotter
- Combined Client and Hedge Order Blotter
- Client Activity Blotter
- Trade Blotter

### <span id="page-45-1"></span>**6.1 General Blotter Features**

All blotters offer the same general set of features which are:

- Choose, move, and resize columns
- Sorting by one or more columns
- Filters
- Save and load settings
- Print and export blotter data

Columns can be resized simply by moving the boundary between two columns left or right. The columns order can be changed via drag and drop.

To move a column, click with the left mouse button on the column header and hold it. Move the column to the next target location and release the mouse button.

To show and hide columns, right click with the mouse on the column headers, and select "Choose Columns …" from the popup menu.

Changes can be reverted back **to default** by right clicking on the column headers, and select "Load Default Settings" from the popup menu.

Blotter data can be exported to CSV by right clicking into the blotter data, and selecting "Export to CSV". Similar applies to printing blotter data.

Blotter data can be sorted by one or more columns, in ascending or descending order, by clicking one or more time on the according column header. To sort by more than one column, press and hold the CTRL key on your keyboard, and click on the specific column headers. A small triangle in the column header will show the sort direction. The small number next to the triangle indicates the sort order if more than one sort column is selected.

### <span id="page-45-2"></span>**6.2 Client Order Blotter**

The client order blotter shows every client order received by the pricing engine. For each client order the following information is available:

- Order ID
- Time of arrival
- Currency pair
- Status (e.g. EXECUTED, REJECTED, ..)
- Action (BUY or SELL)
- Order limit price
- Notional amount and currency
- Executed amount and rate

- Type (originating system like RFS or SEP)
- Trigger (originating organization and user)
- Fill or Kill flag (FoK)
- Realized By ID filled for B2B orders
- Requester
- Requester Institution

The blotter is pre-filled at startup with orders from the current trading day.

The font color for each entry changes with the execution status:

- Green order was successfully executed
- Red order was rejected or failed

#### **Note:**

- Order action (buy or sell) is shown from the perspective of the requester (a buy order means, the requester bought and the ADS MMC sold)!
- In case of a cross rate strip order, the blotter will show three requester orders. The actual cross rate order, and two additional orders for each leg.

### <span id="page-46-0"></span>**6.3 Hedge Order Blotter**

The hedge order blotter shows an entry for each hedge order created for the current trading day. For each hedge order the following information is available:

- Hedge Order ID
- Time of creation
- Currency Pair
- Status
- Action (BUY or SELL)
- Order limit price
- Order notional amount and quantity
- Executed amount and for which average price
- Trigger indication why the hedge order was created
- Fill or Kill order flag

The blotter is pre-filled at startup with orders from the current trading day.

The font color for each entry changes with the execution status:

- Green order was successfully executed
- Red order was rejected or failed

### <span id="page-46-1"></span>**6.4 Combined Client and Hedge Order Blotter**

Blotter with title "All orders" combines client and hedge orders into a single blotter. The two different order types can be easily separated by the type column.

The available columns are the same as for the individual requester and hedge order blotters.

### <span id="page-46-2"></span>**6.5 Client Acitivity Blotter**

The client activity blotter shows one entry for every requested quote from a client:

| <b>Client Activity</b>            |                      |                       |                  |             | 目                |  |  |  |  |
|-----------------------------------|----------------------|-----------------------|------------------|-------------|------------------|--|--|--|--|
| Q-<br>21 out of 21 Activities     |                      |                       |                  |             |                  |  |  |  |  |
| Id                                | $-1$<br>Time (UTC)   | <b>Type</b>           | <b>Status</b>    | Description |                  |  |  |  |  |
| $\Box$ 6164423-PEBANK APAC.TEST   | 5/15/14 2:48:46 PM   | EUR/USD RFS           | <b>EXECUTED</b>  | The RFS '6  | Ш                |  |  |  |  |
| <b>EXECUTED</b>                   | 5/15/14 2:48:48 PM   | <b>RFS</b>            | EXECUTED         | The RFS '6  | $\blacktriangle$ |  |  |  |  |
| <b>OUOTED</b>                     | 5/15/14 2:48:47 PM   | <b>RFS</b>            | <b>OUOTED</b>    | The RFS '6  |                  |  |  |  |  |
| <b>STARTED</b>                    | 5/15/14 2:48:46 PM   | <b>RFS</b>            | <b>STARTED</b>   | The RFS 'S  |                  |  |  |  |  |
| $\equiv$ 6164417-PEBANK_APAC.TEST | 5/15/14 2:47:16 PM   | USD/INR RFS           | CANCELED         | The RFS '6  |                  |  |  |  |  |
| <b>CANCELED</b>                   | 5/15/14 2:48:16 PM   | <b>RFS</b>            | <b>CANCELED</b>  | The RFS '6  |                  |  |  |  |  |
| <b>REJECTED</b>                   | 5/15/14 2:47:18 PM   | <b>RFS</b>            | <b>REJECTED</b>  | The RFS '6  |                  |  |  |  |  |
| <b>QUOTED</b>                     | 5/15/14 2:47:16 PM   | <b>RFS</b>            | QUOTED           | The RFS '6  |                  |  |  |  |  |
| <b>STARTED</b>                    | 5/15/14 2:47:16 PM   | <b>RFS</b>            | <b>STARTED</b>   | The RFS 'S  |                  |  |  |  |  |
| $\Box$ 6164401-PEBANK APAC.TEST   | 5/15/14 2:32:26 PM   | <b>EUR/USD RFS</b>    | <b>CANCELED</b>  | The RFS '6  |                  |  |  |  |  |
| <b>CANCELED</b>                   | 5/15/14 2:33:22 PM   | <b>RFS</b>            | <b>CANCELED</b>  | The RFS '6  |                  |  |  |  |  |
| <b>QUOTED</b>                     | 5/15/14 2:32:26 PM   | <b>RFS</b>            | <b>QUOTED</b>    | The RFS '6  |                  |  |  |  |  |
| <b>STARTED</b>                    | 5/15/14 2:32:26 PM   | <b>RFS</b>            | <b>STARTED</b>   | The RFS 'B  |                  |  |  |  |  |
| <b>CACASTA BEDANIK ABACTECT</b>   | $E/1E/1A$ DIONEC DM. | <b>FLID /LICO DEC</b> | <b>CANICELED</b> | The DEC IC  |                  |  |  |  |  |

<span id="page-47-1"></span>Figure 46: Client Activity Blotter

Green rows show accepted requests, red rows show either cancelled (by the user), or rejected (by the MMC) requests. Accepted and system rejected requets are also shown in the client order blotter. Requests cancelled by the user (e.g. because he didn't accept the price) are only shown in this blotter!

Users can search for specific blotter entries with the search box at the top. Click on the magnifier glass to select search attributes, and enter comparison values.

### <span id="page-47-0"></span>**6.6 Trade Blotter**

The trade blotter shows all trades from client and hedge orders across all managed instruments. For each trade the following information is available:

- Order ID
- Trade ID
- Time of creation
- Currency Pair
- Side (BUY or SELL)
- Type (e.g. RFS, Hedge, Cross, …)
- Trigger (order which caused the trade)
- Executed quantity
- Execution price
- Counterparty

# <span id="page-48-0"></span>**7 AUDIT**

All changes to the Market Maker Cockpit or ANY action by a user or triggered by the engine are stored in audit logs.

# <span id="page-48-1"></span>**8 APPENDIX**

### <span id="page-48-2"></span>**8.1 Java Regular Expression Syntax**

Here is the table listing down all the regular expression metacharacter syntax available in Java:

| Expression   | Matches                                                                     |
|--------------|-----------------------------------------------------------------------------|
| ^            | Matches beginning of line.                                                  |
| \$           | Matches end of line.                                                        |
|              | Matches any single character                                                |
| []           | Matches any single character in brackets.                                   |
| [^]          | Matches any single character not in brackets                                |
| \A           | Beginning of entire string                                                  |
| \z           | End of entire string                                                        |
| \Z           | End of entire string except allowable final line terminator.                |
| re*          | Matches 0 or more occurrences of preceding expression.                      |
| re+          | Matches 1 or more of the previous thing                                     |
| re?          | Matches 0 or 1 occurrence of preceding expression.                          |
| re{ n}       | Matches exactly n number of occurrences of preceding expression.            |
| re{ n,}      | Matches n or more occurrences of preceding expression.                      |
| re{ n, m}    | Matches at least n and at most m occurrences of preceding expression.       |
| a  b         | Matches either a or b.                                                      |
| (re)         | Groups regular expressions and remembers matched text.                      |
| (?: re)      | Groups regular expressions without remembering matched text.                |
| (?> re)      | Matches independent pattern without backtracking.                           |
| \w           | Matches word characters.                                                    |
| \W           | Matches nonword characters.                                                 |
| \s           | Matches whitespace. Equivalent to [\t\n\r\f].                               |
| \S           | Matches nonwhitespace.                                                      |
| \d           | Matches digits. Equivalent to [0-9].                                        |
| \D           | Matches nondigits.                                                          |
| \A           | Matches beginning of string.                                                |
| \Z           | Matches end of string. If a newline exists, it matches just before newline. |
| \z           | Matches end of string.                                                      |
| \G           | Matches point where last match finished.                                    |
| \n           | Back-reference to capture group number "n"                                  |
| \b           | Matches word boundaries when outside brackets                               |
| \B           | Matches nonword boundaries.                                                 |
| \n, \t, etc. | Matches newlines, carriage returns, tabs, etc.                              |
| \Q           | Escape (quote) all characters up to \E                                      |
| \E           | Ends quoting begun with \Q                                                  |

# <span id="page-49-0"></span>**9 CONTACT 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **Middle East Asia Pacific**

**United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

### **EMEA Americas**

### **USA**

*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #27-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6536 0662