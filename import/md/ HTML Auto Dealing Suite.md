![](_page_0_Picture_0.jpeg)

# AUTO DEALING SUITE

# TEX MULTIDEALER TRADING SYSTEM

# User Guide 360T: Auto Dealing Suite (HTML)

Release 4.22 (November 2024)

© 360 Treasury Systems AG, 2024 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG

![](_page_1_Picture_0.jpeg)

# CONTENTS

| 1  |                               | Introduction                                            | 6  |  |  |  |  |
|----|-------------------------------|---------------------------------------------------------|----|--|--|--|--|
| 2  |                               | Accessing the Autodealing Suite and Auto Dealer Control | 7  |  |  |  |  |
| 3  | HTML ADS Configuration Groups |                                                         |    |  |  |  |  |
|    | 3.1                           | Requester Groups                                        | 10 |  |  |  |  |
|    | 3.2                           | Provider Groups                                         | 17 |  |  |  |  |
|    | 3.3                           | Product Groups                                          | 18 |  |  |  |  |
|    | 3.4                           | Activation Period Groups                                | 18 |  |  |  |  |
|    | 3.5                           | MM Currency Groups                                      | 20 |  |  |  |  |
|    | 3.6                           | Currency Couple Groups                                  | 21 |  |  |  |  |
|    | 3.7                           | Notional Amounts Groups                                 | 22 |  |  |  |  |
|    | 3.8                           | Time Period Groups                                      | 23 |  |  |  |  |
|    | 3.9                           | RFS Algorithm Groups                                    | 24 |  |  |  |  |
|    | 3.10                          | Fixing Reference Groups                                 | 24 |  |  |  |  |
|    | 3.11                          | Order Strategy Groups                                   | 25 |  |  |  |  |
|    | 3.12                          | Negotiation Groups                                      | 26 |  |  |  |  |
|    | 3.13                          | Manual Routing Groups                                   | 26 |  |  |  |  |
|    | 3.14                          | Margin Groups                                           | 27 |  |  |  |  |
| 4  |                               | Pricing Routing Rules                                   | 33 |  |  |  |  |
|    | 4.1                           | Creation and Modification of Rules                      | 34 |  |  |  |  |
|    | 4.2                           | Rule Parameters                                         | 35 |  |  |  |  |
|    | 4.3                           | Pricing Routing                                         | 39 |  |  |  |  |
| 5  |                               | Margin Rules                                            | 47 |  |  |  |  |
|    | 5.1                           | Margin Application                                      | 50 |  |  |  |  |
| 6  | Rule Search                   |                                                         |    |  |  |  |  |
| 7  | Audit Log                     |                                                         |    |  |  |  |  |
| 8  |                               | Auto Dealer Control                                     | 54 |  |  |  |  |
| 10 | Contacting 360T               |                                                         |    |  |  |  |  |

![](_page_2_Picture_0.jpeg)

# TABLE OF FIGURES

| Figure 1 360T HTML Self Service Portal.  7                                      |  |
|---------------------------------------------------------------------------------|--|
| Figure 2 Entity selection to reach the ADS overview page.  8                    |  |
| Figure 3 ADS Rules Tool with Configuration Groups, Pricing and Margin Rules.  8 |  |
| Figure 4 Auto Dealer Control Panel for RFS, Orders and SEP Configuration.  9    |  |
| Figure 5 Administration of ADS Configuration Groups  10                         |  |
| Figure 6 Default Requester Group.  11                                           |  |
| Figure 7 Creation of ADS Configuration Groups  11                               |  |
| Figure 8 Requester Group: Selection of Group Members.  12                       |  |
| Figure 9 Creation of custom Requester Groups 12                                 |  |
| Figure 10 Requester Groups: Creation or Modification via CSV Upload.  13        |  |
| Figure 11 Editing requesters in Requester Group overview mode.  14              |  |
| Figure 12 Accessing and editing single custom Requester Group.  14              |  |
| Figure 13 Requesters tab.  15                                                   |  |
| Figure 14 Editing group membership in Requesters tab.  16                       |  |
| Figure 15 Creation of a new Provider Group.  17                                 |  |
| Figure 16 Providers tab.  17                                                    |  |
| Figure 17 Providers tab.  18                                                    |  |
| Figure 18 Creation of a custom Activation Period Group  19                      |  |
| Figure 19 Adding multiple time periods to a single Activation Period Group  19  |  |
| Figure 20 Creation of MM Currency Group.  20                                    |  |
| Figure 21 Currency Groups  20                                                   |  |
| Figure 22 Creation of a Currency Couple Group 21                                |  |
| Figure 23 Creation of a Notional Amount Group  22                               |  |
| Figure 24 Time Period Groups  23                                                |  |
| Figure 25 Time Period Group with discontinuous time ranges.  23                 |  |
| Figure 26 Market Link Algorithm definition.  24                                 |  |
| Figure 27 Fixing Reference Groups  25                                           |  |
| Figure 28 Creation of an Order Strategy Group.  25                              |  |
| Figure 29 Creation of a Negotiation Group.  26                                  |  |
| Figure 30 Manual Routing Group configuration.  26                               |  |
| Figure 31 Margin Groups and their types 28                                      |  |
| Figure 32 Example of supported margin values.  29                               |  |
| Figure 33 Margin groups: hiding margin columns.  32                             |  |

© 2024 – 360 Treasury Systems AG <sup>3</sup>

![](_page_3_Picture_0.jpeg)

| Figure 34 Pricing Routing Rules.  33                                                                                  |
|-----------------------------------------------------------------------------------------------------------------------|
| Figure 35 New Pricing Routing Rule.  34                                                                               |
| Figure 36 Selecting a Trading Venue  37                                                                               |
| Figure 37 Route column.  39                                                                                           |
| Figure 38 Creation of Margin Rules.  47                                                                               |
| Figure 39 Applying margin transformation.  51                                                                         |
| Figure 40 Rule Search 52                                                                                              |
| Figure 41 Saving Rule Search Filters.  52                                                                             |
| Figure 42 HTML ADS Audit Log.  53                                                                                     |
| Figure 43 Audit Log: Text Search.  53                                                                                 |
| Figure 44 Auto Dealer Control enabling and disabling Auto Dealer switch 54                                            |
| Figure 45 Auto Dealer Control - Auto Dealer Schedule Enabled switch  54                                               |
| Figure 46 Auto Dealer Control – Auto Dealer Start and Stop Times  54                                                  |
| Figure 47 Auto Dealer Control - Auto Dealer Start Enabled switch  55                                                  |
| Figure 48 Auto Dealer Control – Auto Dealer Start related alert  55                                                   |
| Figure 49 Auto Dealer Control Day by Day Definition schedule table  56                                                |
| Figure 50 Removing a time range from Day by Day Definition schedule table  57                                         |
| Figure 51 Adding a time range in Day by Day Definition: First click (start time)  57                                  |
| Figure 52 Adding a time range in Day by Day Definition: Second click (stop time) on<br>same day  57                   |
| Figure 53 Adding a time range in Day by Day Definition: first click on one day and second<br>click on another day  57 |
| Figure 54 Example of Auto Dealer Schedule setup to run continuously from Sunday to<br>Friday  58                      |

![](_page_4_Picture_0.jpeg)

# TABLES

| Table 1 Pricing Routing availability by Negotiation, Product and Order Type.  46 |  |
|----------------------------------------------------------------------------------|--|
| Table 2 Margin Type availability by Negotiation, Product and Order Type.  50     |  |

![](_page_5_Picture_0.jpeg)

# 1 Introduction

Liquidity providers retrieve price information from different channels to price their customers. The 360T Auto Dealing Suite (ADS) is a routing component embedded within 360T's trading platform that forwards negotiation requests and prices between the customer and a price source based on a custom ruleset. Price sources can be, inter alia, a pricing server, a manual trader, or a market link.

360T has offered so far various versions of Auto Dealing Suite (ADS). The newest, HTML supported version, is a successor solution that offers enhanced and improved rule management capabilities, including pricing and margin rules separation, better organization and structuring by facilitation of product grouping, and in general, user experience simplification.

The new HTML ADS can be accessed via the 360T HTML Self Service Portal or Bridge Administration tool.

The legacy ADS versions will be decommissioned in future. Clients utilizing legacy ADS in BCT or Bridge Administration ADS will be upgraded to the HTML ADS version. They will be contacted by the 360T CAS team with further information on the upgrade process.

This user guide also describes the complimentary ADS tool allowing to start and stop auto-pricing, either manually or automatically according to a schedule. The ADS Control Panel is available in Bridge Administration tool as a category called Auto Dealer Control and will be described in Section 8.

![](_page_6_Picture_0.jpeg)

# 2 Accessing the Autodealing Suite and Auto Dealer Control

The ADS tool can then be accessed either via 360T HTML Self Service Portal by clicking on the "ADS" icon, or via Bridge Administration, which can be accessed under the menu option "Administration" in the screen header of the Bridge application.

![](_page_6_Picture_3.jpeg)

Figure 1 360T HTML Self Service Portal.

The Bridge Administration feature opens to a homepage "Administration Start" with available shortcuts to different configuration tools and actions for the user. The new HTML ADS can be accessed by clicking on the "HTML ADS" icon . Clients transitioning from Bridge Administration ADS version will be still able to access it via "ADS Rules".

Entities accessible to the user are displayed in the left navigation panel. The user can select the entity for which the ADS rules are to be configured.

![](_page_7_Picture_0.jpeg)

![](_page_7_Picture_2.jpeg)

Figure 2 Entity selection to reach the ADS overview page.

This opens the Auto Dealing Suite Rules tool for the selected entity and displays all administration functions enabled for the user.

| 侖                                | Q Search For Institution<br>$\rightarrow$ | ADS Configuration Groups | Pricing Routing Rules Margin Rules Audit log                                                                                                                        |             |  |               |                |                      |                     |             |
|----------------------------------|-------------------------------------------|--------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|--|---------------|----------------|----------------------|---------------------|-------------|
|                                  | 350TBANK APACTEST                         | Requester Groups         | Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups >> |             |  |               |                |                      |                     |             |
| $\mathcal{L}_{\mathcal{F}}$<br>_ | 360TBANK APACTEST                         |                          |                                                                                                                                                                     | $\mathsf Q$ |  | $\rightarrow$ |                |                      |                     |             |
| 马                                |                                           | Name                     | Group Members                                                                                                                                                       |             |  |               | No. of Members | No. of Routing Rules | No. of Margin Rules |             |
| ₿                                |                                           | DEFAULT                  | BankE, HanscoTAS, SLEntity 2.TEST, SLEntity 1.TEST, SLONE TEST, Hanscompany, SLMain, 360T.APAC, LIM EMS TEST, ADS COMP.TEST                                         |             |  |               | 10             |                      |                     | $\triangle$ |
| $\overline{u}$                   |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |
| 69                               |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |
| $\overline{\mathbb{L}}$ .        |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |
| $\leftrightarrow$                |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |
| å.                               |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |
|                                  |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |

Figure 3 ADS Rules Tool with Configuration Groups, Pricing and Margin Rules.

The Auto Dealer Control can be accessed by clicking on the correspondent icon.

Selecting the entity accessible to the user from the left navigation panel (Figure 2) opens the Auto Dealer Control tool for the selected entity and displays all administration functions enabled for the user.

![](_page_8_Picture_0.jpeg)

| <b>TRADER WORKSHEET</b>                                                                  | T.<br><b>BRIDGE ADMINISTRATION</b>                                                                   |                                                                                                                                                                                                                   |                                                                                                                                            | $\vee$ Preferences $\vee$ Administration $\vee$ Help | $\sum$ $\theta$ AA $ \theta$ X |
|------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|--------------------------------|
| Q 带 1 血<br>侖<br>血 BankD<br>$\mathcal{L}_{\mathcal{T}}$<br><b>e</b><br>$\Rightarrow$<br>_ | <b>RFS Auto Dealer Configuration</b><br>Orders Auto Dealer Configuration<br>RFS Auto Dealer Schedule | SEP Auto Dealer Configuration<br>RFS Auto Dealer Enabled<br>RFS Auto Dealer Schedule Enabled<br>RFS Auto Dealer Start Enabled<br>Day by Day Definition<br>RFS Auto Dealer Start Time<br>RFS Auto Dealer Stop Time | <b>CO</b> Enabled<br>CO Enabled<br><b>CO</b> Enabled<br>O o Disabled<br>$\bullet$ 06:00 $\bullet$<br>(07:00 CEST)<br>$2200$ $\circledcirc$ |                                                      | $n \approx \equiv$             |
|                                                                                          |                                                                                                      |                                                                                                                                                                                                                   | (23:00 CEST)                                                                                                                               |                                                      |                                |

Figure 4 Auto Dealer Control Panel for RFS, Orders and SEP Configuration.

# 3 HTML ADS Configuration Groups

The enhanced HTML ADS allows the user to define a wide range of rules using customized and centrally managed groups of parameters such as negotiations (RFS, Orders, SEP), FX or MM products, order types, currencies, currency pairs, notional ranges, rule activation time windows, maturities, etc. which can be defined within the ADS Configuration Groups, refer to Figure 7.

A rule is a combination of:

- Conditions, which define the specific criteria/constraints that trigger an action.
- Outcome, which defines the action to be undertaken when the said criteria are met.

In general, ADS Configuration Groups can be re-used for both Pricing Routing Rules and Margin rules. Elements of the groups and combinations of those groups determine which pricing route or margin group type can be configured. For example, an "Order to Order" pricing route will only be selectable, if the Negotiation Group only contains the negotiation "Orders", whereas the "No Pricing" routing is available for all combinations of negotiations, products or order types.

| <b>Requester Groups</b> | Providers<br>Requesters | <b>Activation Period Groups</b><br>Provider Groups<br>Product Groups                       |          | $\gg$<br>MM Currency Groups<br>Currency Couple Groups                                                                                                  |                     |             |
|-------------------------|-------------------------|--------------------------------------------------------------------------------------------|----------|--------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|-------------|
| Name                    | <b>Group Members</b>    | Q                                                                                          | No. of I | Notional Amount Groups<br><b>Time Period Groups</b>                                                                                                    | No. of Margin Rules |             |
| DEFAULT                 |                         | BankE, HansCoTAS, SLEntity 2.TEST, SLEntity 1.TEST, SLONE.TEST, HansCompany, SLM<br>$^{+}$ | 10       | RFS Algorithm Groups<br><b>Fixing Reference Groups</b><br>Order Strategy Groups<br><b>Negotiation Groups</b><br>Manual Routing Groups<br>Margin Groups | $\circ$             | $\triangle$ |

![](_page_9_Picture_0.jpeg)

#### Figure 5 Administration of ADS Configuration Groups

The available rule parameters in the ADS Configuration Groups are:

- Requester Groups (accessible via tabs "Requester Groups" and "Requesters")
- Negotiation Groups
- Product Groups
- Activation Period Groups
- MM Currency Groups
- Currency Couple Groups
- Notional Amount Groups
- Time Period Groups
- RFS Algorithm Groups
- Fixing Reference Groups
- Order Strategy Groups

Margin Groups are not rule parameters but are used to configure the outcome of a Margin Rule which results in the application of a set of margin values of a specific type.

Provider Groups (accessible via tabs "Provider Groups" and "Providers") and Manual Routing Groups also belong to rule output category.

The next section focuses on the available ADS Configuration Groups within the enhanced ADS. The order of the Configuration Groups, as shown in the enhanced ADS, is reflected in the same order in the subsequent sub-sections.

General user interface features relevant for ADS Configuration Groups will be described in detail using the example of the Requester Groups.

# 3.1 Requester Groups

The various customers of a provider are referred to as Requesters. Requester entities can be added or removed from the group in a central place without the need to edit the rules themselves.

After setup and acceptance of the counterparty relationship in Bridge Administration Counterparty Relationship tool, a requester entity is first assigned to the "DEFAULT" Requester Group. As soon as the requester entity

![](_page_10_Picture_0.jpeg)

is assigned to a custom, non-default Requester Group, it will be removed automatically from the Default Requester Group.

The group is shown as a table entry, containing the following headers:

- Name (either "Default" or a custom name, must be unique within a single configuration group)
- Group Members (preview of selected group elements, however limited to the width of the column)
- No. of Members (number of selected group elements)
- No. of Routing Rules (number of Pricing Routing Rules configured with this group)
- No. of Margin Rules (number of Margin Rules configured with this group)

|                  | <b>ADS Configuration Groups</b> | <b>Pricing Routing Rules</b> | <b>Margin Rules</b>                                                             | Audit log      |                          |                             |                     |               |
|------------------|---------------------------------|------------------------------|---------------------------------------------------------------------------------|----------------|--------------------------|-----------------------------|---------------------|---------------|
| Requester Groups | Requesters                      | Providers                    | Provider Groups                                                                 | Product Groups | Activation Period Groups | $\gg$<br>MM Currency Groups |                     |               |
|                  |                                 |                              |                                                                                 |                |                          | $\rightarrow$               |                     |               |
| Name             | <b>Group Members</b>            |                              |                                                                                 |                | No. of Members           | No. of Routing Rules        | No. of Margin Rules |               |
| DEFAULT          |                                 |                              | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany,   10 |                |                          | $\circ$                     | $\circ$             | $\Rightarrow$ |
|                  |                                 |                              |                                                                                 |                | $^+$                     |                             |                     |               |

Figure 6 Default Requester Group.

A list of all group members (in this case Requesters) with their group assignments can be downloaded as a CSV file. This file can be used for creating new groups or re-assigning memberships via CSV upload. Download

and upload icons are available for each Configuration Group and can be found in the upper right corner of the page.

## 3.1.1 Creation of a new Requester Group

Creation of a new group starts with clicking on the green plus icon where the user then has the possibility to enter the group name and select the group members.

| Requester Groups | Providers<br>Requesters                                                      | Provider Groups<br>Product Groups | <b>Activation Period Groups</b> | MM Currency Groups<br>$\gg$ |                     |                         |
|------------------|------------------------------------------------------------------------------|-----------------------------------|---------------------------------|-----------------------------|---------------------|-------------------------|
|                  | $\alpha$                                                                     |                                   |                                 | $\rightarrow$               |                     |                         |
| Name             | <b>Group Members</b>                                                         |                                   | No. of Members                  | No. of Routing Rules        | No. of Margin Rules |                         |
| DEFAULT          | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany 10 |                                   |                                 | O                           | Đ                   | $\approx$ 11            |
| ter Name         | Select Group Members                                                         |                                   |                                 |                             |                     | $\times \triangleright$ |

Figure 7 Creation of ADS Configuration Groups

![](_page_11_Picture_0.jpeg)

Each rule must have a unique name within the configuration group. The user can select multiple elements within the rule either by selecting the value from the drop-down list or by typing its name. A parent requester entity with all underlying legal entities can be added by clicking on the "Select Group" icon next to its name.

|         | $\alpha$                                                                                                                                                             |                | $\rightarrow$                            |                     |               |
|---------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|------------------------------------------|---------------------|---------------|
| Name    | Group Members                                                                                                                                                        | No. of Members | No. of Routing Rules                     | No. of Margin Rules |               |
| DEFAULT | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany,                                                                                           | 10             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | $\circ$             | $\Rightarrow$ |
| AAA+    | Select Group Members                                                                                                                                                 |                |                                          |                     | $\times$      |
|         | STATUTE CITY PASS INTERNATIONAL EXCEPTION<br>360T.APAC<br><b>Select Group</b><br>ADS COMPTEST<br>ADS COMP.TEST<br>BankE (Select Group<br>BankE<br>Hans1 Select Group |                |                                          |                     |               |

#### Figure 8 Requester Group: Selection of Group Members.

The creation of a single group is finished by applying the green checkbox icon.

All changes can be saved or discarded in one step by using the corresponding "Discard all Changes" or "Save" icons.

|                  | Q                                                                          |                | $\rightarrow$        |                     |                 |
|------------------|----------------------------------------------------------------------------|----------------|----------------------|---------------------|-----------------|
| Name             | <b>Group Members</b>                                                       | No. of Members | No. of Routing Rules | No. of Margin Rules |                 |
| $AAA+$           | ADS COMP.TEST                                                              | $\mathbf{1}$   | $\circ$              | $\circ$             | <b>今面</b>       |
| DEFAULT          | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany, | $\overline{9}$ | $\circ$              | $\circ$             | Ũ<br>$\approx$  |
| <b>B</b> Clients | 360T.APAC                                                                  | $\,1$          | $\circ$              | $\circ$             | <b>今回</b>       |
|                  |                                                                            |                |                      |                     |                 |
| Enter Name       | Select Group Members                                                       |                |                      |                     | $\times$ $\vee$ |
|                  |                                                                            |                |                      |                     |                 |
|                  |                                                                            |                |                      |                     |                 |

Figure 9 Creation of custom Requester Groups.

![](_page_12_Picture_0.jpeg)

Various groups within one configuration group can contain the same element, for example the same requester entity can be a member of multiple requester groups (excluding the Default Requester Group).

The trash bin symbol deletes a configured group and all its values. In case there are pricing or margin rules where this group is used, the group cannot be deleted.

A new group or multiple groups can alternatively be created via CSV upload. In this case it is sufficient to add the name of the new group to the list of downloaded group elements in the column "Groups membership", separated by a pipe. The group will be automatically created, and the group members will be selected. A group assignment can be removed by removing the name of the group from "Groups membership". After the upload, a results file will be generated, and the user can review all changes before saving them in the application.

The figure below shows how Requesters can be assigned to existing or new groups:

| $\overline{ }$ |                                                     |                                | D                    |                             |                                                                           |
|----------------|-----------------------------------------------------|--------------------------------|----------------------|-----------------------------|---------------------------------------------------------------------------|
| Requester      | Legal entity                                        | Long Name                      | Negotiation          | <b>Trading venue</b>        | Groups membership                                                         |
|                | 360T.EMSCorporate 360T.EMSCorporate.FOND.A1 *Empty* |                                | Orders               | OTC. EU MTF, UK MTF DEFAULT |                                                                           |
| SubsidiaryG    | SubsidiaryG                                         | *Empty*                        | RFS, Orders, SEP OTC |                             | Gold   Platinium   Bronze                                                 |
| SubsidiaryG    | Fund <sub>G.1</sub>                                 | Fund 1 SubsidiaryG RFS, Orders |                      |                             | OTC, EU MTF, UK MTF Gold   Platinium   Requester1   Silver2   Top Clients |
| SubsidiaryG    | FundSubG.1                                          | FundSubG.1                     | <b>RFS. Orders</b>   | <b>OTC</b>                  | Gold   Platinium   Requester1   Silver2   Top Clients                     |
| SubsidiaryG    | FundSubG.2                                          | FundSubG.2                     | <b>RFS, Orders</b>   | <b>OTC</b>                  | <b>DEFAULT</b>                                                            |

Figure 10 Requester Groups: Creation or Modification via CSV Upload.

## 3.1.2 Adding and removing members of a Requester Group

The members of a group can be added or removed either directly in the "Requester Groups" tab in Group Members columns by simply double-clicking

on it, or by accessing the individual custom group's tab via the arrow icon placed in the last column of each group entry. Modifications of group assignments can be alternatively done via CSV upload, as explained in previous chapter.

![](_page_13_Picture_0.jpeg)

| Requester Groups | Providers<br>Requesters  | Provider Groups | $\gg$<br>Product Groups |                     |          |
|------------------|--------------------------|-----------------|-------------------------|---------------------|----------|
|                  | Q                        |                 |                         | $\rightarrow$       |          |
| Name             | <b>Group Members</b>     | No. of Members  | No. of Routing Rules    | No. of Margin Rules |          |
| AAA+             | ADS COMP.TEST $\chi$     | 1               | $\mathbf{o}$            | $\overline{0}$      | ゆ面       |
| <b>B</b> Clients | 360T.APAC                | $\mathbf{1}$    | ö                       | o                   | 2回       |
| DEFAULT          | BankE, HansCoTAS, SLEnti | $_{\rm 8}$      | $\circ$                 | $\circ$             | ाति<br>È |

Figure 11 Editing requesters in Requester Group overview mode.

The name of a custom group can be changed by double clicking and modifying it.

In case a group has many elements, it is more convenient to use the single group's edit mode which opens a new tab and allows to add or remove its members.

| $\leftarrow$  | Requester Groups > Group AAA+ |                  |                      |                       |                        |
|---------------|-------------------------------|------------------|----------------------|-----------------------|------------------------|
|               |                               | $\alpha$         |                      | $\rightarrow$         |                        |
| Requester     | Legal entity                  | Negotiation      | <b>Trading venue</b> | All Groups Membership |                        |
| LIM EMS TEST  | LIM EMS TEST                  | RFS              | OTC                  | AAA+                  | $\widehat{\boxplus}$   |
| ADS COMP.TEST | ADS COMP.TEST                 | RFS, Orders, SEP | OTC                  | AAA+                  | $\widehat{\mathbb{U}}$ |
| Banke         | BankE                         | RFS. Orders      | <b>OTC</b>           | AAA+                  | $\widehat{\mathbb{U}}$ |
| Hans1         | HansCoTAS                     | RFS              | OTC                  | AAA+                  | û                      |
| SLONE TEST    | SLEntity2.TEST                | RFS, Orders      | OTC.                 | AAA+                  | $\widehat{\mathbb{U}}$ |
| SLONE.TEST    | SLEntity1.TEST                | RFS. Orders      | OTC                  | AAA+                  | Û                      |
| SLONE.TEST    | SLONE TEST                    | RFS, Orders      | <b>OTC</b>           | AAA+                  | $\widehat{\mathbb{U}}$ |
| SLONE.TEST    | SLMain                        | RFS, Orders      | OTC                  | AAA+                  | Û                      |
| 360T.APAC     | 360T.APAC                     | RFS, Orders, SEP | OTC, EU MTF          | AAA+, B Clients       | ⑪                      |

Figure 12 Accessing and editing single custom Requester Group.

![](_page_14_Picture_0.jpeg)

## 3.1.3 Requesters tab

All individual requester entities and their group memberships are listed alphabetically in a table, in the "Requesters" tab.

|              |                | $\alpha$      |                  |                      | $\rightarrow$     |  |
|--------------|----------------|---------------|------------------|----------------------|-------------------|--|
| Requester    | Legal Entity   | Long Name     | Negotiation      | <b>Trading Venue</b> | Groups Membership |  |
| 360T.APAC    | 360T.APAC      | 360T.APAC     | RFS, Orders, SEP | OTC. EU MTF          | AAA+, B Clients   |  |
| ADS COMPTEST | ADS COMP.TEST  | ADS COMP.TEST | RFS, Orders, SEP | OTC                  | AAA+              |  |
| BankE        | <b>BankE</b>   | "Empty"       | RFS, Orders      | OTC                  | AAA+              |  |
| Hans1        | HansCoTAS      | HansCoTAS     | <b>RFS</b>       | OTC                  | AAA+              |  |
| HansCompany  | HansCompany    | Hans Company  | <b>RFS</b>       | OTC                  | DEFAULT           |  |
| LIM EMS TEST | LIM EMS TEST   | *Empty*       | <b>RFS</b>       | OTC                  | AAA+              |  |
| SLONE TEST   | SLONE.TEST     | "Empty"       | RFS, Orders      | OTC                  | AAA+              |  |
| SLONE.TEST   | SLEntity1.TEST | "Empty"       | RFS, Orders      | OTC                  | AAA+              |  |
| SLONE.TEST   | SLEntity2.TEST | "Empty"       | RFS, Orders      | OTC                  | AAA+              |  |
| SLONE TEST   | SLMain         | SLMain Ltd    | RFS, Orders      | OTC                  | AAA+              |  |
| SLMain       | SLMain         | SLMain Ltd    | RFS, Orders      | OTC                  | AAA+              |  |
| SLMain       | SLEntity1.TEST | "Empty"       | RFS, Orders      | OTC                  | AAA+              |  |
| SLMain       | SLEntity2.TEST | "Empty"       | RFS, Orders      | OTC                  | AAA+              |  |
| SLMain       | SLONE TEST     | *Empty*       | RFS, Orders      | OTC                  | $AAA+$            |  |

#### Figure 13 Requesters tab.

Following column headers are available:

- Requester: system name of the legal entity
- Legal Entity: indicates if the legal entity has another parent entity; in case of a parent entity both Requester and Legal entity are the same
- Long Name: Legal name of the entity
- Negotiation: type of negotiation (RFS, Orders or SEP), for which the provider and requester legal entity have an accepted counterparty relationship
- Trading Venue: shows the trading venue for which a requester entity is enabled (OTC, EU MTF, UK MTF)

Groups Membership: shows Requester Groups of which the legal entity is a member; double clicking on a cell opens up the editing view of the groups

![](_page_15_Picture_0.jpeg)

| Requester Groups | Requesters        | Providers<br>Provider Groups | Product Groups   | Activation Period Groups | $\gg$<br>MM Currency Groups |  |
|------------------|-------------------|------------------------------|------------------|--------------------------|-----------------------------|--|
|                  |                   | $\alpha$                     |                  | $\rightarrow$            |                             |  |
| Requester        | Legal Entity      | Long Name                    | Negotiation      | <b>Trading Venue</b>     | Groups Membership           |  |
| 360T.APAC        | 360T.APAC         | 360T.APAC                    | RFS, Orders, SEP | OTC, EU MTF              | AAA+, B Clients             |  |
| ADS COMP.TEST    | ADS COMP.TEST     | ADS COMP.TEST                | RFS, Orders, SEP | OTC                      | AAA+                        |  |
| BankE            | BankE             | "Empty"                      | RFS, Orders      | OTC                      | AAA+                        |  |
| Hans1            | HansCoTAS         | HansCoTAS                    | <b>RFS</b>       | OTC                      | AAA+                        |  |
| HansCompany      | HansCompany       | <b>Hans Company</b>          | <b>RFS</b>       | <b>OTC</b>               | DEFAULT X)                  |  |
| LIM EMS TEST     | LIM EMS TEST      | *Empty*                      | <b>RFS</b>       | OTC                      | AAA+                        |  |
| SLONE TEST       | <b>SLONE TEST</b> | "Empty"                      | RFS, Orders      | OTC                      | <b>B</b> Clients            |  |
| SLONE.TEST       | SLEntity1.TEST    | *Empty*                      | RFS, Orders      | OTC                      | DEFAULT                     |  |
| SLONE TEST       | SLEntity2.TEST    | "Empty"                      | RFS, Orders      | OTC                      | AAA+                        |  |
| SLONE TEST       | SLMain            | SLMain Ltd                   | RFS, Orders      | OTC                      | AAA+                        |  |
| SLMain           | SLMain            | SLMain Ltd                   | RFS, Orders      | OTC                      | AAA+                        |  |
| SLMain           | SLEntity1.TEST    | "Empty"                      | RFS, Orders      | OTC                      | AAA+                        |  |
| SLMain           | SLEntity2.TEST    | "Empty"                      | RFS, Orders      | OTC                      | AAA+                        |  |
| SLMain           | SLONE TEST        | *Empty*                      | RFS, Orders      | OTC                      | AAA+                        |  |

#### membership, allowing to add or remove a group (see

#### Figure 14 Editing group membership in Requesters tab.

The search area allows to search by specific text within the columns Requester, Legal Entity and Long Name.

![](_page_16_Picture_0.jpeg)

# 3.2 Provider Groups

The ADS facilitates the option to forward incoming requests to a group of Liquidity Providers (referred to as Market Link routing) or to single provider in case of Orders. These groups can be defined under the menu item Provider Groups before including them in the respective market link destination rules.

Creation or editing of Provider Groups can be done in the tabs Provider Groups and Providers, in the same manner as for the Requester Groups or Requesters, as described in the previous chapter.

The main difference between both groups is the absence of a Default group in case of Providers. By default, a provider is not a member of a group. However a default pricing rule setting includes all providers (value "Any") with an accepted CRM relationship.

|                         |                          |  | Activation Period Groups | MM Currency Groups      | $\gg$                |          |
|-------------------------|--------------------------|--|--------------------------|-------------------------|----------------------|----------|
|                         | $\alpha$                 |  |                          | $\rightarrow$           |                      |          |
| Name                    | <b>Group Members</b>     |  |                          | No. of Members          | No. of Routing Rules |          |
| Market Link RFS         | 360TBANK.TEST, BOAL.DEMO |  |                          | $\overline{\mathbf{c}}$ | $\circ$              | ゆ面       |
| ter Name<br><b>CEN-</b> | Select Group Members     |  |                          |                         |                      | $\times$ |

Figure 15 Creation of a new Provider Group.

The tab Providers shows all available providers, negotiation types and trading venues they are pricing on.

| Requester Groups   | Providers<br>Requesters | Provider Groups                                                  | MM Currency Groups    Currency Couple Groups<br>Product Groups<br>Activation Period Groups |                      |                   |
|--------------------|-------------------------|------------------------------------------------------------------|--------------------------------------------------------------------------------------------|----------------------|-------------------|
|                    |                         | $\alpha$                                                         | $\rightarrow$                                                                              |                      |                   |
| Provider           | Legal Entity            | Long Name                                                        | Negotiation                                                                                | <b>Trading Venue</b> | Groups Membership |
| 360TBANKTEST       | 360TBANKTEST            | PLEASE DONT SEF ENABLE THIS BANK - CREATE A SEPARATE ONE INSTEAD | RFS, Orders, SEP                                                                           | OTC. EU MTF          | Market Link RFS   |
| BOAL DEMO          | BOAL DEMO               | BOAL DEMO                                                        | RFS, Orders, SEP                                                                           | OTC, EU MTF          | Market Link RFS   |
| Barclays BARX.DEMO | Barclays BARX.DEMO      | Barclays BARX.DEMO                                               | RFS, Orders, SEP                                                                           | OTC. EU MTF          |                   |
| CITIBANK.DEMO      | CITIBANK.DEMO           | CITIBANK DEMO                                                    | RFS, Orders, SEP                                                                           | OTC                  |                   |
| RBS.LND.DEMO       | RBS.LND.DEMO            | RBS.LND.DEMO                                                     | RFS, Orders, SEP                                                                           | OTC, EU MTF          |                   |
| SEB.DEMO           | SEB.DEMO                | SEB.DEMO                                                         | RFS, Orders, SEP                                                                           | OTC                  |                   |

#### Figure 16 Providers tab.

A list of all group members (in this case Providers) with their group assignments can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

![](_page_17_Picture_0.jpeg)

# 3.3 Product Groups

Product groups can contain the following products:

- FX Spot
- FX Forward
- FX Time Option
- FX Future
- FX Swap
- Block Trade
- NDF
- NDS
- FX Option
- Loan / Deposit
- Cross Currency Portfolio (EMS)
- Commodity Asian Swap
- Commodity Bullet Swap
- Metals products: Outrights, Quarterly Strips and Metals Spreads

|                 | Requesters<br>Providers<br>Requester Groups                                                                                 | Provider Groups    |          | Product Groups Activation Period Groups MM Currency Groups | Currency Couple Groups | Notional Amount Groups >>> |                     |            |
|-----------------|-----------------------------------------------------------------------------------------------------------------------------|--------------------|----------|------------------------------------------------------------|------------------------|----------------------------|---------------------|------------|
|                 |                                                                                                                             |                    | $\alpha$ |                                                            | $\rightarrow$          |                            |                     |            |
| Name            | <b>Group Members</b>                                                                                                        |                    |          |                                                            | No. of Members         | No. of Routing Rules       | No. of Margin Rules |            |
|                 |                                                                                                                             |                    |          |                                                            |                        |                            |                     | <b>N</b> 面 |
| Spot and Forwar | $Fx$ Forward $\times$                                                                                                       | $Fx$ Spot $\times$ |          |                                                            |                        |                            |                     | $\times$   |
|                 | Block-Trade<br>Commodity Asian Swap<br>Commodity Bullet Swap<br>Cross Currency Portfolio<br>Deposit<br>$\sqrt{}$ Fx Forward |                    |          |                                                            |                        |                            |                     |            |
|                 | Fx Future                                                                                                                   |                    |          |                                                            |                        |                            |                     |            |
|                 | Fx Option                                                                                                                   |                    |          |                                                            |                        |                            |                     |            |

#### Figure 17 Providers tab.

A list of all Products with their group assignments can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

# 3.4 Activation Period Groups

Often, the set of rules that apply depend on the time of the day the request is made. The ADS capabilities allow the definition of activation periods, which can be used in conjunction with other parameters to simplify rule creation and vastly reduce the number of rules to achieve the same outcome.

In the field "Time Zone", it is possible to have a time zone defined other than UTC. In case the Company time zone is a time zone where Daylight Savings Time (DST) is considered, there is no need for manual adjustments of any rules to accommodate DST. The system will automatically take DST into account.

![](_page_18_Picture_0.jpeg)

Groups with activation time windows can be created, removed, and renamed similarly to Requester Groups.

|      |                     |                     |                   | Time Zone | Europe/Berlin, Currently: +02:00 (UTC+02:00) |                | $\checkmark$         |       |
|------|---------------------|---------------------|-------------------|-----------|----------------------------------------------|----------------|----------------------|-------|
|      |                     |                     |                   |           |                                              |                |                      |       |
|      |                     | $\alpha$            |                   |           |                                              | $\rightarrow$  |                      |       |
| Name | <b>Time Periods</b> |                     |                   |           |                                              | No. of Members | No. of Routing Rules | No. 0 |
|      |                     |                     |                   |           |                                              |                |                      | ゆ画    |
| Day  | 00:00               |                     | $\Theta$<br>17:59 | Ő         |                                              |                |                      |       |
|      | $\checkmark$<br>ĸе  | $\approx$ 00 $\sim$ |                   |           |                                              |                |                      |       |
|      | $\times$ Cancel     | $\sqrt{$ Apply      |                   | $^+$      |                                              |                |                      |       |

#### Figure 18 Creation of a custom Activation Period Group

Within a group, an activation time window can be defined. The user can define a broken / discontinuous activation time for the group by simply adding more than a one-time window. This must be done in the single Activation Period Group preview which can be opened by clicking on the arrow icon.

| <b>ADS Configuration Groups</b><br>Providers<br>Requester Groups<br>Requesters | <b>Pricing Routing Rules</b><br><b>Margin Rules</b><br>Provider Groups | Settings<br>Product Groups | Audit log<br><b>Activation Period Groups</b> | MM Currency Groups | Currency Couple Groups | Notional Amount Groups | $\gg$ |
|--------------------------------------------------------------------------------|------------------------------------------------------------------------|----------------------------|----------------------------------------------|--------------------|------------------------|------------------------|-------|
|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |
| Activation Period Groups > Group Day                                           | StartTime                                                              |                            |                                              | End Time           |                        |                        |       |
|                                                                                | 00:00                                                                  |                            |                                              | 23:59              |                        | $\widehat{\mathbb{U}}$ |       |
|                                                                                | 00:00                                                                  | Θ<br>23:59                 | O.                                           |                    |                        |                        |       |
|                                                                                |                                                                        |                            | $^+$                                         |                    |                        |                        |       |
|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |
|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |
|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |
|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |

#### Figure 19 Adding multiple time periods to a single Activation Period Group

For example, let's assume that Swaps are manually priced, and the Swap desk is situated in Singapore and New York. In this case, a group can be created to cover the availability times of this desk, and this group would contain a row each to hold the availability time in each city.

The system allows the creation of groups with overlapping activation time windows as these groups could have applicability in different rules.

The setting "Any" available in pricing and margin rules tabs encompasses all times of the day, denoted by a time window from 00:00 GMT to 00:00 GMT representing start and end times.

Note: Activation periods can be used for all negotiation types. However, for SEP, the activation period is only assessed at the time of a new subscription.

![](_page_19_Picture_0.jpeg)

A list of all activation time ranges with their Groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

## 3.5 MM Currency Groups

MM (Money Market) Currency Groups are intended to allow the classification of currencies. Once created, a currency group can be used to simplify rule creation for interest rate products like Loans or Deposits. Currencies can be added or removed from the group in a central place without the need to edit the rules themselves.

Creation of a new group starts with clicking on the green plus icon:

#### Figure 20 Creation of MM Currency Group.

The user can select multiple currencies within the rule either by selecting the value from the drop-down list or by typing its name. The symbol "\*\*\*" stands for any available currency. The system does not restrict the creation of groups with an overlapping set of currencies.

|                  | Requester Groups Requesters Providers Provider Groups |          | Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups >>> |               |                |                      |                     |     |
|------------------|-------------------------------------------------------|----------|--------------------------------------------------------------------------------------------------------------|---------------|----------------|----------------------|---------------------|-----|
|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |
|                  |                                                       | $\alpha$ |                                                                                                              | $\rightarrow$ |                |                      |                     |     |
| Name             | Currencies                                            |          |                                                                                                              |               | No. of Members | No. of Routing Rules | No. of Margin Rules |     |
|                  |                                                       |          |                                                                                                              |               |                |                      |                     | ☆ 面 |
|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |
|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |
|                  | GBP $\times$ (EUR $\times$ )                          |          |                                                                                                              |               |                |                      |                     |     |
|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |
|                  | AUD                                                   |          |                                                                                                              |               |                |                      |                     |     |
|                  | CAD                                                   |          |                                                                                                              |               |                |                      |                     |     |
|                  | CHF                                                   |          |                                                                                                              |               |                |                      |                     |     |
|                  | $\sqrt{$ FUR                                          |          |                                                                                                              |               |                |                      |                     |     |
|                  | $\sqrt{GBP}$                                          |          |                                                                                                              |               |                |                      |                     |     |
|                  | <b>HKD</b>                                            |          |                                                                                                              |               |                |                      |                     |     |
| Autopriced curre | JPY                                                   |          |                                                                                                              |               |                |                      |                     | X   |

#### Figure 21 Currency Groups

A list of all Currencies assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

![](_page_20_Picture_0.jpeg)

# 3.6 Currency Couple Groups

Currency Couple Groups allow bucketing of currency pairs. Once created, a currency couple group can be used to simplify rule definition, among others, for FX Spots, Forwards, Swaps, NDF, NDS, Options and Block Trades.

Per default, pricing or routing rules are configured with value "Any" including all currency pairs available.

Currency Couple Groups can be created, removed, and renamed similarly to Requesters Groups, as explained in Section 3.5.

|        | Requester Groups Requesters Providers Provider Groups                                                                    |   | Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups >>> |                |                      |                     |                |
|--------|--------------------------------------------------------------------------------------------------------------------------|---|--------------------------------------------------------------------------------------------------------------|----------------|----------------------|---------------------|----------------|
|        |                                                                                                                          | Q |                                                                                                              | $\rightarrow$  |                      |                     |                |
| Name   | Couples                                                                                                                  |   |                                                                                                              | No. of Members | No. of Routing Rules | No. of Margin Rules |                |
|        |                                                                                                                          |   |                                                                                                              |                |                      |                     | ☆ 面            |
| Majors | EUR/USD $\times$ )<br>$EUR/GBP \times$                                                                                   |   |                                                                                                              |                |                      |                     | $\times \sqrt$ |
|        | $\sqrt{\mathsf{EUR}/\mathsf{USD}}$<br>$\sqrt{\text{EUR/GBP}}$<br>EUR/CHF<br>$EUR/***$<br>GBP/USD<br>GBP/CHF<br>$GBP/***$ |   |                                                                                                              |                |                      |                     |                |

Figure 22 Creation of a Currency Couple Group

Within a group, the green plus button "Add Currency Couple" creates a new line in which a currency pair can be added.

New currency pairs can be added to, or removed from a group, thus providing the ability to impact targeted rules, whilst not needing to manipulate rules individually.

The system does not restrict the creation of groups with an overlapping set of currencies.

A list of all Currency Pairs can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

![](_page_21_Picture_0.jpeg)

# 3.7 Notional Amounts Groups

Typically, the range of notional amounts is an important factor in determining the pricing and routing mechanisms for a product, besides others. The Notional Amounts Group allows to bucket ranges of continuous or discontinuous notional amounts within a group. This group, when created, can be used in rule definition across products, currencies, and currency pairs in ADS RFS and ADS Order Rules. The Notional Amounts Groups are not relevant for SEP Order Rules configuration.

The "Any" notional group value, which is set per default when creating new rules, includes all notional amount, set up as zero to unlimited, which can be modified to hold a different set of values.

Notional Amount Groups can be created, removed and renamed similarly to other ADS Configuration Groups.

![](_page_21_Picture_6.jpeg)

Figure 23 Creation of a Notional Amount Group

Within a group, the lower and upper bounds of notional amounts can be used to establish a notional range. If there is a need to establish a discontinuous range of amounts within a group, this can be achieved by creating new rows while editing a single notional amount group. The group details can be accessed via clicking on the arrow icon.

Note: The notional amounts are expressed in the home currency of the Liquidity Provider (entity using the ADS).

The "Delete" option can be used to remove notional ranges that are not required. The lower and upper bounds are both included in the range of notional amounts.

The system does not restrict the creation of groups with overlapping amounts as these groups could have applicability in different rules.

A list of all configured notional amount ranges assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

![](_page_22_Picture_0.jpeg)

# 3.8 Time Period Groups

For products involving a maturity date/tenor, it is possible to encapsulate maturity ranges within the Time Period Groups. Tenors are defined as a range of maturities, with both "From" and "To" values included. A tenor can form part of different groups to allow their use in different rules.

Time periods specific for Money Market (MM) instruments (OVERNIGHT or TOMNEXT) cannot be combined with FX specific time periods (SPOT, 15 MONTHS, 21 MONTHS or 10 YEARS) within one Time Period Group.

Groups to hold maturity ranges can be created, removed and renamed similarly to other Configuration Groups. The list of available Time Periods appears after typing the space key.

|            | Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups >> |          |  |               |                |                      |                     |                |
|------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|--|---------------|----------------|----------------------|---------------------|----------------|
|            |                                                                                                                                                                                      | $\alpha$ |  | $\rightarrow$ |                |                      |                     |                |
| Name       | Time Periods                                                                                                                                                                         |          |  |               | No. of Members | No. of Routing Rules | No. of Margin Rules |                |
|            |                                                                                                                                                                                      |          |  |               |                |                      |                     | ◇ Ⅲ            |
| Enter Name | Select To<br>Select From<br>$\widehat{\phantom{0}}$                                                                                                                                  | $\vee$   |  |               |                |                      |                     | $\times \sqrt$ |
|            | Q                                                                                                                                                                                    |          |  |               |                |                      |                     |                |
|            | UNLIMITED                                                                                                                                                                            |          |  |               |                |                      |                     |                |
|            | TODAY                                                                                                                                                                                |          |  |               |                |                      |                     |                |
|            | OVERNIGHT                                                                                                                                                                            |          |  |               |                |                      |                     |                |
|            | TOMORROW                                                                                                                                                                             |          |  |               |                |                      |                     |                |
|            | TOMNEXT                                                                                                                                                                              |          |  |               |                |                      |                     |                |
|            | SPOT                                                                                                                                                                                 |          |  |               |                |                      |                     |                |
|            | SPOTNEXT                                                                                                                                                                             |          |  |               |                |                      |                     |                |
|            | 1 WEEK                                                                                                                                                                               |          |  |               |                |                      |                     |                |
|            |                                                                                                                                                                                      |          |  |               |                |                      |                     |                |

Figure 24 Time Period Groups

A tenor range can be defined within a group. The ability to add discontinuous tenor ranges is provided via the plus icon in the Time Period detail view.

For example, there could be a need to route tenors between 1 WEEK to 1 MONTH to a different market link group. Here the ability to create discontinuous tenor ranges comes in handy, as maturities from TODAY to 1 WEEK (and) 1 MONTH to 2 MONTHS are routed similarly.

| ADS Configuration Groups Pricing Routing Rules Margin Rules | <b>Settings</b><br>Audit log                                                                                |                           |                                               |
|-------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|---------------------------|-----------------------------------------------|
| Requester Groups Requesters                                 | Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups |                           | Notional Amount Groups Time Period Groups >>> |
| ← Time Period Groups > Group Market Link 1 Tenors           |                                                                                                             |                           |                                               |
|                                                             | From                                                                                                        | $\sim$<br>To              |                                               |
|                                                             | <b>TODAY</b>                                                                                                | 1 WEEK                    |                                               |
|                                                             | 1 MONTH                                                                                                     | Select To<br>$\checkmark$ |                                               |
|                                                             |                                                                                                             | $Q$ 2M                    |                                               |
|                                                             |                                                                                                             | 2 MONTHS                  |                                               |
|                                                             |                                                                                                             |                           |                                               |
|                                                             |                                                                                                             |                           |                                               |

Figure 25 Time Period Group with discontinuous time ranges.

A list of all configured time ranges assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

![](_page_23_Picture_0.jpeg)

# 3.9 RFS Algorithm Groups

RFS Algorithm Groups are used for the route option "Market Link" or "Order to RFS" by which the conditions are defined how a price provided by a market link provider will be forwarded to the requester. Two parameters are considered: the time elapsed since the request was received and forwarded to the back-to-back market link providers, and the number of quotes returned by the market link providers.

| Requester Groups Requesters Providers Provider Groups |                           | Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups |   | RFS Algorithm Groups >>> |
|-------------------------------------------------------|---------------------------|-----------------------------------------------------------------------------------------------------------------------------|---|--------------------------|
| ← RFS Algo Groups > Group Exotics                     |                           |                                                                                                                             |   |                          |
|                                                       | Request Runtime (seconds) | Number of Quotes                                                                                                            |   |                          |
|                                                       |                           |                                                                                                                             | Û |                          |
|                                                       |                           |                                                                                                                             | û |                          |
|                                                       |                           |                                                                                                                             |   |                          |

Figure 26 Market Link Algorithm definition.

In the example above, if the Market Link Algorithm "Exotics" is used in a pricing rule, a request will be sent to the market link providers and waits for quotes from at least 3 different providers within the first 5 seconds before starting to stream the best quote to the requester. If after 7 seconds, there have not been 3 competitive quotes, 2 competitive quotes will be sufficient to stream the best quote to the requester.

A list of all configured RFS algorithm parameters assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

# 3.10 Fixing Reference Groups

In case NDF and NDS requests are priced, inter alia, based on Fixing Reference conditions then the Fixing Reference Groups feature can be used. The Fixing Reference Groups are only relevant for ADS RFS or Order Rules and are not applicable to ADS SEP Rules.

Groups of Fixing References can be created, removed, and renamed similarly to other ADS Configuration Groups

![](_page_24_Picture_0.jpeg)

|                    |                                                      | Q | $\rightarrow$ |                |                      |                     |                                        |
|--------------------|------------------------------------------------------|---|---------------|----------------|----------------------|---------------------|----------------------------------------|
| Name               | <b>Fixing References</b>                             |   |               | No. of Members | No. of Routing Rules | No. of Margin Rules |                                        |
|                    |                                                      |   |               |                |                      |                     | $\hat{\varphi}$ $\widehat{\mathbf{u}}$ |
| <b>BRL Fixings</b> | USD/BRL BRLD1 $\times$ (USD/BRL BRLD2 $\times$ ) bri |   |               |                |                      |                     | $\times$ V                             |
|                    |                                                      |   |               |                |                      |                     |                                        |
|                    | GBP/BRL NDF Asiatica                                 |   |               |                |                      |                     |                                        |
|                    | $\sqrt{}$ USD/BRL BRLO1                              |   |               |                |                      |                     |                                        |
|                    |                                                      |   |               |                |                      |                     |                                        |
|                    | $\sqrt{150/8}$ RL BRL02                              |   |               |                |                      |                     |                                        |
|                    | USD/BRL BRL03                                        |   |               |                |                      |                     |                                        |
|                    | USD/BRL BRL10                                        |   |               |                |                      |                     |                                        |
|                    | USD/BRL BRL11                                        |   |               |                |                      |                     |                                        |
|                    | USD/BRL PTAX (BRL09)                                 |   |               |                |                      |                     |                                        |
|                    | USD/BRL Pontos sobre PTAX                            |   |               |                |                      |                     |                                        |

Figure 27 Fixing Reference Groups

The configured Fixing Reference Groups are then available in the "Fixing Reference" drop down menu when defining the rules and rules templates for NDF and NDS. The default value "Any" set during rule creation encompasses all fixing references.

A list of all available Fixing References with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

## 3.11 Order Strategy Groups

Various order types can be grouped together in custom Order Strategy Groups.

Currently available order strategies are Market Orders, Limit Orders, Stop Orders, Fixing Orders, Loop Orders, If-Done Orders, OCO Orders, ALGO Orders and Call Orders.

| Requester Groups<br>stration start | Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Croups Currency Couple Groups Notional Amount Groups Time Period Groups RPS Algorithm Groups Fixing Reference Groups |          |  |     |                | Order $\gg$<br>Strategy        |                     |     |
|------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|--|-----|----------------|--------------------------------|---------------------|-----|
| Name                               | <b>Order Strategies</b>                                                                                                                                                                                       | $\alpha$ |  | mb. | No. of Members | Groups<br>No. of Routing Rules | No. of Margin Rules |     |
| Stop and Limit                     | $\boxed{\text{Stop Order } \times}$ (Limit Order $\times$ )                                                                                                                                                   |          |  |     |                |                                |                     | p ⊕ |
|                                    | ALGO Order<br>Call Order<br>Fixing Order<br>If-Done Order<br>√ Limit Order<br>Loop Order<br>Market Order<br>OCO Order                                                                                         |          |  |     |                |                                |                     |     |
|                                    | Stop Order                                                                                                                                                                                                    |          |  |     |                |                                |                     |     |

Figure 28 Creation of an Order Strategy Group.

While grouping various order types, the user should consider, if they have common pricing routes or margin types which are supported. Rule validations will only allow selection of pricing or margin rules, if they are supported for all selected order strategies, negotiations and products configured in the specific rule.

![](_page_25_Picture_0.jpeg)

A list of all available Order Strategies with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

# 3.12 Negotiation Groups

Negotiations currently supported in ADS are RFS (Request for Streams), Orders (Single Bank Orders) or SEP (Streaming Executable Prices).

Please note, that Negotiations, Products and Order Strategies selected in pricing or margin rules are validated and only commonly supported pricing routes or margin types will be selectable.

|                 | > ADS Configuration Groups Pricing Routing Rules Margin Rules Settings Audit log<br>Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups RFS Algorithm Groups Fixing Reference Groups |          |               |                |                      |                     |    |
|-----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|---------------|----------------|----------------------|---------------------|----|
| istration Start |                                                                                                                                                                                                                                                                                                                    | $\alpha$ | $\rightarrow$ |                | Groups               |                     |    |
| Name.           | Negotiations                                                                                                                                                                                                                                                                                                       |          |               | No. of Members | No. of Routing Rules | No. of Margin Rules |    |
|                 |                                                                                                                                                                                                                                                                                                                    |          |               |                |                      |                     | ☆面 |
| RFS and Order   | Belect Negotiations                                                                                                                                                                                                                                                                                                |          |               |                |                      |                     |    |
|                 | Orders<br>RFS<br>SEP                                                                                                                                                                                                                                                                                               |          |               |                |                      |                     |    |
|                 |                                                                                                                                                                                                                                                                                                                    |          |               |                |                      |                     |    |

#### Figure 29 Creation of a Negotiation Group.

A list of all available Negotiation types with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.

## 3.13 Manual Routing Groups

360T ADS facilitates the routing of client requests to physical traders who are using the 360T Trader Worksheet (TWS) to manually provide prices. Based on the conditions of the routing rules, it might be necessary to route a request to a pre-defined group of traders. These can be defined in the section "Manual Routing Groups". The Manual Routing Groups are not relevant for ADS SEP Rules.

All new users are automatically assigned to the "DEFAULT" Group. A user is not automatically removed from the Default group after reassignment to a custom group.

| ADS Configuration Groups | Pricing Routing Rules Margin Rules Settings Audit log                                                                                                                                                                                     |                             |                      |        |
|--------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------|----------------------|--------|
|                          | Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Groups Notional Amount Groups Time Period Groups RFS Algorithm Groups Fixing Reference Groups<br>$\Omega$<br>÷. | Manual<br>Routing<br>Groups |                      |        |
| Name                     | Group Members                                                                                                                                                                                                                             | No. of Members              | No. of Routing Rules |        |
| DEFAULT                  | 360TBANKAPACTrader1, 360TBANKAPACTrader2, 360TBANKAPACTrader3, 360TBANKAPACTrader5, 360TBANKAPACTrader4                                                                                                                                   |                             |                      | $\phi$ |
| Manual Croup 1           | 360TBANKAPAC.Trader1                                                                                                                                                                                                                      |                             |                      | ◇ 回    |
| Enter Name               | Select Group Members                                                                                                                                                                                                                      |                             |                      |        |
|                          | 360TBANKAPAC.Trader1<br>360TBANKAPAC.Trader2<br>360TBANKAPAC.Trader3<br>360TBANKAPAC.Trader4<br>360TBANKAPAC.TraderS                                                                                                                      |                             |                      |        |

Figure 30 Manual Routing Group configuration.

![](_page_26_Picture_0.jpeg)

Per default, ADS re-publishes an incoming request to the Default or custom manual trading group in certain scenarios:

- 1. CRM relationship was requested but not yet accepted by the provider: republish to Default Group.
- 2. Rule is missing for a request: send to Default Group.
- 3. Route is set to Pricing Server, but pricing server rejects or is not available, or a republish timeout is configured in ADS instance, or the Request has a Comment and a republish is configured in ADS Instance, or the Request is unsupported by the Pricing Server (e.g. trade-on-behalf request, FX Prolongation/Early Settlement, Historical Rollover): request goes to the Manual Routing Group configured for the rule.
- 4. Route is set to Market Link and for any reason pricing is not available and a republish timeout is configured in ADS instance: request goes to the Manual Routing Group configured for the rule.
- 5. Offline confirmations (RFS or EMS Directed Negotiations), which always go to manual intervention, are treated like the Manual intervention rule of the corresponding product.
- 6. A request on a specific trading venue, e.g. SEF, EU MTF or UK MTF will be only routed to traders enabled for the respective trading venue.
- 7. If none of the traders is logged in, request is not shown.

A list of all Manual Traders with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or modifying memberships via CSV upload.

# 3.14 Margin Groups

A feature of the ADS is the configuration of margins which can be applied to the price received from a price source before delivering the end price to the customers.

Margins can be organized and structured within the enhanced Auto Dealer Suite and denoted in different ways e.g. percent, pips, or a fixed amount.

Different margins can be created and applied to requests, in conjunction with other conditions. For example, margin tiers can be established by requester (customer) groups and then used in rules across all products for the said customers.

Margin tiers can be achieved by first creating different Margin Groups as shown in the below illustration.

![](_page_27_Picture_0.jpeg)

![](_page_27_Figure_2.jpeg)

#### Figure 31 Margin Groups and their types.

The green plus icon enables the creation of margin tiers. The margin group name can be edited to have something meaningful that is instantly recognizable when defining rules.

The "Delete" option facilitates the removal of margin groups that are not needed anymore.

The enhanced ADS offers several margin types which can be categorized into either a "constant margin" or "variable margin".

Constant margin means that a pre-defined margin will be added/deducted from the base price. This margin can either be expressed in pips, in percent or a fixed amount in the home currency, according to the selection in this field.

Variable margin maintains the price constant while the margin changes during the price negotiation based on the market price.

## 3.14.1 Creation of margin groups.

Following margin types can be selected while creating a new margin group with margin values:

- Pips
- Percent
- Hybrid: allows the spot bid/offer margin to be defined in pips and the forward bid/offer margin or the swap bid/offer margin to be set in percent. Hybrid margin type is available for FX Spot, Forward and Swap products.
- Fixed Amount (Home CCY): margin is added to/deducted from the opposite amount after which it is converted into a markup/markdown in pips of the effective rate provided to the subsidiary/customer. Due to the defined precision of the exchange rate, depending on the notional amount of the trade, the fixed amount margin might have no impact at all (large notional) or lead to an abnormal effective rate (small notional).
- Variable: allows to set fix bid and fix ask prices for Spot or Forward and applicable only to Market Link and Pricing Server routes. Minimum Variable Margin threshold can contain the value UNLIMITED or any negative or positive number (Pips).
- Annual Percentage: allows to define percentage margin on an annual basis and differentiate the margin based on the maturity of the forward

![](_page_28_Picture_0.jpeg)

and/or swap transactions. The defined spot or forward percentage margin is extended with pro rata addition where the defined percentage rate is multiplied by maturity/360.

Depending on the margin type selected, the system highlights margin value entries supported for this margin type in a light grey colour:

|                | ADS Configuration Groups Pricing Routing Rules Margin Rules Settings Audit log |                        |                 |                 |               |               |                     |                           |                                                                                                                                                                                                                                |                              |                        |
|----------------|--------------------------------------------------------------------------------|------------------------|-----------------|-----------------|---------------|---------------|---------------------|---------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|------------------------|
| stration Start |                                                                                |                        |                 |                 |               |               |                     |                           | Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups RFS Aleorithm Groups Fixing Reference Groups | $M$ aritin $\gg$<br>Groups   |                        |
|                |                                                                                |                        |                 |                 |               |               |                     | $\rightarrow$             |                                                                                                                                                                                                                                |                              |                        |
| Name           | Margin Type                                                                    | <b>Bid Spot Margin</b> | Ask Spot Margin | Min Spot Spread | Fix Bid Price | Fix Ask Price | Min Variable Margin | <b>Bid Forward Margin</b> | Ask Forward Margin                                                                                                                                                                                                             | % Forward Margin Calculation | <b>Bid Swap Margin</b> |
| Tier 1 Clients | Variable                                                                       |                        |                 |                 |               |               | <b>UNLIMITED</b>    |                           |                                                                                                                                                                                                                                |                              |                        |
|                |                                                                                |                        |                 |                 |               |               |                     |                           |                                                                                                                                                                                                                                |                              |                        |

Figure 32 Example of supported margin values.

When either "Pips", "Percent" or "Fixed Amount" is selected from the "Margin Type" field, the margins for spots, forwards, swaps, futures, interest rate products and commodities can be entered. This allows the same Margin Group to be re-used across product types. Depending on the product in the incoming request, the system applies the appropriate margin.

The details of each margin which can be added per margin group are listed below:

Bid Spot Margin: This margin will be deducted from the spot price when the client requests a sell quote. The margin is expressed in pips based on intervals of one decimal place, in percent with a maximum of 3 decimal places or as a fixed amount in the home currency. This margin can be set up for the instrument FX spot, FX forward, NDF and Block-Trade.

Ask Spot Margin: This margin will be added to the spot price when the client requests a buy quote. The margin is expressed in pips based on intervals of one decimal place in percent with a maximum of 3 decimal places or as a fixed amount in the home currency. This margin can be set up for the instrument FX spot, FX forward, NDF and Block-Trade.

Min. Spot Spread: The minimum spot spread is applied to the spot bid and offer rate for two-way SEP prices. It is expressed in pips based on intervals of one decimal place. If configured, the minimum spread check is done per each band of each stream separately before applying possible margins. Note that minimum spreads are only considered for the routing rules "Pricing Server" and "Market Link" and only for SEP negotiation, it will be ignored for other negotiation types and their products.

Fix Bid Price: Non-changing bid price can be defined for FX Spot and FX Forward instruments. The bid price provided to the customer remains unchanged throughout the RFQ negotiation. Fix Bid Price for FX Forward instruments refers to the Forward rate while the underlying FX Spot price might fluctuate. In the case of a market link scenario, the FX Spot bid price is taken from the market link provider. If the price request is routed to the Pricing Server,

![](_page_29_Picture_0.jpeg)

the FX Spot bid price offered to the customer corresponds to the FX Spot bid rate as provided by the Pricing Server.

Fix Offer Price: Non-changing offer price can be defined for FX Spot and FX Forward instruments. The offer price provided to the customer remains unchanged throughout the RFQ negotiation. Fix Offer Price for FX Forward instruments refers to the Forward rate while the underlying FX Spot price might fluctuate. In the case of a market link scenario, the FX Spot offer price is taken from the market link provider. If the price request is routed to the Pricing Server, the FX Spot offer price exposed to the customer corresponds to the FX Spot offer rate as provided by the Pricing Server.

Min Variable Margin: A threshold margin (in PIPS) which prevents quotation if margin fluctuates below the specified amount due to varying market prices.

Bid Forward Margin: This margin will be deducted from the forward points when the client requests to sell forward. The margin is expressed in pips based on intervals of 3 decimal places, in percent with a maximum of 3 decimal places. This margin can be set up for the instrument FX forward, NDF and Block-Trade.

Ask Forward Margin: A margin will be added to the forward points when the client requests to buy forward. The margin is expressed in pips based on intervals of 3 decimal places, in percent with a maximum of 3 decimal places. This margin can be set up for the instrument FX forward, NDF and Block-Trade.

% Forward Margin Calculation: relevant only for Percent and Hybrid margin types. Allows to define, if the percentage margin is calculated based on forward points or on spot rate of a forward request.

Bid Swap Margin: For any swap request, ADS applies only swap margin (either bid or offer based on the side of the action). In the case of a sell swap request, a defined `bid swap margin` will be deducted from the swap points of the non-spot leg. In case both legs are non-spot, for ex. near leg= today, far leg = 1 month, then the margin will be deducted from the swap points of the far leg.

The margin can be defined in terms of a) pips based on intervals of 3 decimal places, b) in percent with a maximum of 3 decimal places or c) as a fixed amount in the home currency.

Note: Spot and Forward margins (as well as other margin parameters) are not used for FX Swap requests. Similarly, swap margins are only regarded to calculate margins for swap requests and are not effective for any other instrument.

Ask Swap Margin: For any swap request, ADS applies only swap margin (either bid or offer based on the side of the action). In case of a buy swap request, the defined `offer swap margin` will be deducted from the swap points of the non-spot leg. In case both legs are non-spot, for example near leg= today, far leg = 1 month, then the margin will be deducted from the swap points of the far leg.

![](_page_30_Picture_0.jpeg)

The margin can be defined in terms of a) pips based on intervals of 3 decimal places, b) in percent with a maximum of 3 decimal places or c) as a fixed amount in the home currency.

Note: Spot and Forward margins (as well as other margin parameters) are not used for FX Swap requests. Similarly, swap margins are only regarded to calculate margins for swap requests and are not effective for any other instrument.

% Swap Margin Calculation: relevant only for Percent and Hybrid margin types. Allows to define, if the percentage margin is calculated based on swap points or on spot rate of a Swap request. Bid Swap Margins expressed in percentage terms are calculated using the spot rate.

Apply Spot Margin to Uneven Swaps: this option allows to apply a margin to the spot rate of uneven swaps. The margin will be applied to the side and swap leg with the higher amount. It is available for pips, percentage, and hybrid margin types.

- Example 1: Client buys 3 million EUR/USD spot and sells 2 million EUR/USD in 1 month. Since the client buys the overhanging amount, the defined Offer Spot Margin will be added to the spot rate. The application of the swap point margin is unchanged, in this case the Bid Swap Margin on the all-in far rate.
- Example: Client buys 2 million EUR/USD spot and sells 3 million EUR/USD in 1 month. Since the client sells the overhanging amount, the defined Bid Spot Margin will be subtracted from the spot rate. The application of the swap point margin is unchanged, in this case also the Bid Swap Margin on the all-in far rate.

Bid Future Margin: This margin will be deducted from the FX Future price when the client requests a sell quote. The margin is expressed in pips based on intervals of one decimal place, in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.

Ask Future Margin: This margin will be added to the FX Future price when the client requests a buy quote. The margin is expressed in pips based on intervals of one decimal place in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.

Interest Margin: A margin will be added to the interest rate if the client requests a quote for a loan; or deducted if the client requests a quote for a deposit. The margin is expressed in basis points (using the selection "pips") or in percent. It can be set up for money market instruments Loan and Deposit.

Bid Commodity Margin: Available for Fixed amount and Percentage margin and applicable only to RFS Commodity Asian Swap and RFS Commodity Bullet Swap. Margin will be deducted from the unit price.

![](_page_31_Picture_0.jpeg)

Ask Commodity Margin: Available for Fixed amount and Percentage margin and applicable only to RFS Commodity Asian Swap and RFS Commodity Bullet Swap.

In case a specific margin field should not be needed across all margin groups, the user can simply hide it from the margin groups table. For example, Min Spot Spread applicable only to SEP negotiations can be removed from the view by right-clicking on the Min Spot Spread column header and accessing the columns options menu:

| Bid Spot Margin<br>Margin Type<br>Ask Spot Margin<br>Min Spot Spread<br>Name<br>0.0<br>0.0<br>0.0<br>Tier 1 Clients<br>Hybrid<br>0.00<br>Fixed Amount (Home CCY)<br>0.00<br>0.0<br>Zero Fixed Am | Fix Bid Price<br>>> A - E<br>>> F-N<br>>> others | Fix Ask Price<br>Min Variable Margin<br>√ Apply Spot Margin to Uneven Swap<br>V Ask Commodity Margin | Bid Forward<br><b>bo</b> |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------|------------------------------------------------------------------------------------------------------|--------------------------|
|                                                                                                                                                                                                  |                                                  |                                                                                                      |                          |
|                                                                                                                                                                                                  |                                                  |                                                                                                      |                          |
|                                                                                                                                                                                                  | Ø Hide This Column "Min Spot"<br>Spread"         | Ask Forward Margin<br>√ Ask Future Margin<br>Ask Spot Margin<br>√ Ask Swap Margin                    |                          |
|                                                                                                                                                                                                  |                                                  | √ Bid Commodity Margin<br>$\sqrt{ }$ Bid Forward Margin                                              |                          |

Figure 33 Margin groups: hiding margin columns.

The order of the columns can be changed by dragging and dropping a column to the desired place.

Once the groups for the different parameters are setup, as described in the previous sections, they can be used to define the routing rules describing the conditions, price source destination and margins.

A list of all Margins with their corresponding group names can be downloaded as a CSV file. This CSV file can be used for creating new margin groups or modifying margin values via upload. The uploaded margin values will be validated for compatibility with the margin type defined for each margin group. Additionally, the number of decimal places for each margin will be checked, similar how it is done in the GUI.

![](_page_32_Picture_0.jpeg)

# 4 Pricing Routing Rules

The menu item "Pricing Rules" allows creation and management of various pricing routing rules across all products and negotiations using the previously created ADS Configuration Groups as rule parameters.

|                | $Q \vee$ Type to filter |                    |           | Q Search        | <b>xx</b> Save As | $\checkmark$          | $\widehat{\mathbb{U}}$ |
|----------------|-------------------------|--------------------|-----------|-----------------|-------------------|-----------------------|------------------------|
| #              | $\vee$ Rule ID          | Comment            | Requester | Negotiation     | Product           | <b>Order Strategy</b> |                        |
| $\mathbf 1$    | 2200                    | Blacklisted CCY pa | Any       | Any             | Any               | Any                   | 40 位                   |
| $\mathbf 2$    | 2201                    | Cut-off            | Any       | Any             | Any               | Any                   | 面<br>⊕                 |
| $\bar{\rm{3}}$ | 2203                    | Manual             | Any       | Orders          | Spot, Forward     | Limit, Stop           | ⊕ □                    |
| $\overline{4}$ | 2208                    | auto pricing       | Any       | RFS, Order, SEP | Spot, Forward     | Limit, Stop           | 中 市                    |

Figure 34 Pricing Routing Rules.

A rule is a combination of:

- Conditions, which define the specific criteria/constraints that trigger an action.
- Outcome, which defines the action to be undertaken when the said criteria are met.

Every variation in a parameter that makes up a condition or an outcome results in a new rule.

A list of all Pricing Routing Rules can be downloaded as a CSV file. This CSV file can be used as a template for creating new rules or updating existing rules.

Download and upload icons are available in the upper right corner of the page.

![](_page_33_Picture_0.jpeg)

# 4.1 Creation and Modification of Rules

A new rule can be created by clicking on the green plus icon or by cloning an existing rule using the button. Multiple rules can be created or cloned before final confirmation with the "Save" icon. Please note that confirming with green checkbox icon does not yet save a rule, but it only allows to finish the creation mode of a single rule.

| <b>Trading Venue</b><br>□ □ |
|-----------------------------|
|                             |
|                             |
| 40 10                       |
| 40                          |
| ⊕ □                         |
| $\times$                    |
|                             |

#### Figure 35 New Pricing Routing Rule.

While creating a rule, options are offered for each parameter to select among the Configuration Groups that have been previously defined. In case a one-time exceptional value is required, which is not part of the group, individual values can be selected for certain rule parameters, such as Requester, Negotiation, Product, Order Strategy, Currency Couples, MM Currency, Fixing Reference, Manual Routing or Provider. It is however recommended to use ADS Configuration Groups to centrally manage the rules. The user can enter a text comment for each rule which helps to better understand the rule logic.

The parameters should be defined from the left side to the right side of the rules table. Pricing Route should be done after all criteria have been defined.

In case any condition of an existing rule is modified, and a specific pricing route has already been selected, not all values of the parameters will be available for selection, but only the values compatible with the selected pricing route. The route can be changed to "No Pricing" and adjusted as desired.

For each parameter, a drop-down is provided where a list of existent Configuration Groups or available individual values is displayed. The default setting "Any" is pre-selected in most of the columns. The default pricing route is "No Pricing". Once a rule is saved, a rule system ID is automatically generated.

The user can continue to add rule groups for the selected product group by proceeding with the "Add Rule" option.

Note: The order (column #) of rules determines their priority top-down. The desired rule position can be adjusted via drag&drop feature. When a negotiation request meets the condition of several single rules or rules within the routing

![](_page_34_Picture_0.jpeg)

configuration, then the order dictates the priority. If a single rule should take precedence over other rules, then it must be placed above them.

If no rule is specified, the request will be sent to manual intervention to Bridge TWS to the Default Group of traders.

"Delete" can be used to remove a rule group that is no longer needed.

If a rule must be adjusted, the user should double-click on the rule row or cell in the rule table and the edit mode will be activated.

Pricing Routing Rules can be created or modified via CSV upload and some of their parameters will be validated during the upload (see Table 1 Pricing Routing availability by Negotiation, Product and Order Type.). Rule ID is used as an indicator if a rule should be created (rule ID is empty or not found) or updated (rule ID is given and found).

In case of modification via CSV upload, it is recommendable to download the existing rules and simply adjust the parameters which should be changed (except from Rule ID and Position number). After upload, a results CSV file will be automatically generated and indicate in the column "Results" if the upload was successful. The rules will be pre-loaded in the GUI and only successful changes (Result = OK) can be either saved or discarded via "Discard all changes" icon. In case of any upload errors, the user can in this way discard all changes, adjust the file and repeat the upload until all rules successfully passed the validations.

Please note that when updating existing rules using the Rule ID as identifier, the rule position value will be ignored. This basically means that the order of rules cannot be modified via CSV upload. This must be done manually in the GUI by dragging and dropping the rules.

# 4.2 Rule Parameters

The rule parameters for a new rule should be ideally entered from the left to the right side of the table. Certain parameters will be applied or not, depending on negotiation, product or order type. This means that in case negotiations or products are combined in Configuration Groups, the HTML ADS will only pick the values relevant for a specific request.

## 4.2.1 Position number (#)

Rule position number is assigned automatically by the order of rule creation. It will be changed accordingly whenever it is dragged and dropped between two other rules. This is only possible if no sorting is applied on any of the rule headers. A rule with a lower rule position number will be applied before a rule with a higher rule number.

![](_page_35_Picture_0.jpeg)

A cloned rule will be automatically assigned the highest position number i.e. will appear at the bottom of all the rules.

#### 4.2.2 The field is not mandatory for CSV upload and will be ignored. If new rules are created via CSV upload, they will be automatically appended with the highest position numbers, however in the same order as listed in the CSV file.Rule ID

Rule ID is a system ID granted automatically whenever a rule is created or cloned. It does not have an impact on rule application and is used for reference purposes e.g. while searching for Audit logs

Rule ID is used to identify the rule which should be modified via CSV upload. The value should be blank in case a new rule is created via upload.

## 4.2.3 Comment

Comment field is only used for administrative purposes and has no impact on routing, meaning it is not a parameter as such. It can be used is search filters and for alphabetical column sorting.

This field can be left blank during CSV upload.

## 4.2.4 Requester

Requester means a legal entity or a parent entity with all underlying entities requesting a quote. It can be selected from previously defined Requester Groups or chosen individually from the list of all available counterparties in the drop-down. It is relevant for all rules.

The default value of this field is "Any" (meaning all requesters). The field must be populated in the CSV upload file.

## 4.2.5 Negotiation

Negotiation groups, value "Any" or individual values can be selected. It is one of the mains parameters used for pricing route or margin group/type validation. More information can be found in Chapter 3.12.

The field is mandatory for CSV upload.

## 4.2.6 Product

Negotiation groups, value "Any" or individual values can be selected. It is one of the mains parameters used for pricing route or margin group/type validation. More information can be found in Chapter 3.3.

The field is mandatory for CSV upload.

![](_page_36_Picture_0.jpeg)

## 4.2.7 Order Strategy

Order groups, value "Any" or individual order types can be selected. It will be used for pricing route or margin group/type validation for Orders. Order strategy value will be ignored for RFS or SEP negotiations. More information can be found in Chapter 3.11.

The field is mandatory for CSV upload.

#### 4.2.8 Activation Period

Activation period groups or value "Any" can be selected. More information can be found in Chapter 3.4. Activation period is not considered for RFS Cross Currency Portfolio product.

The field is mandatory for CSV upload.

#### 4.2.9 Trading Venue

The parameter Trading Venue offers the possibility to differentiate the routing of a price request depending on the venue (EU MTF, UK MTF or OTC) where it was originated. This parameter will only be considered for the following RFS products: Fx Forward, Fx Swap, Block, Fx Time Option, Option, NDF or NDS. Default value is "Any".

The field is mandatory for CSV upload.

![](_page_36_Figure_10.jpeg)

Figure 36 Selecting a Trading Venue

#### 4.2.10 Currency Couple

Currency couple can be set for all negotiations either to "Any" (all currency pairs) or a custom currency couple group can be chosen. This parameter will be considered for almost all negotiations and products, except from MM products and Cross Currency Portfolio.

The field is mandatory for CSV upload.

![](_page_37_Picture_0.jpeg)

## 4.2.11 MM Currency

This parameter (Any, custom Configuration Group, or a single value) will be considered only for Loan and Deposit requests but ignored for all other types of products. More information can be found in Chapter 3.5.

The field is mandatory for CSV upload.

## 4.2.12 Requester Action

Requester action (Any, Two-way, Buy or Sell) can be defined as routing condition for RFS requests for Spot, Forward, Swap, Options, NDF, NDS and Blocks. It will be ignored for other products configured within the same rule.

The field is mandatory for CSV upload.

## 4.2.13 Notional Amount

Notional amount (Any or custom Configuration Group) can be used as parameter for almost all RFS and Order products except from Commodity Asian Swap, Commodity Bullet Swap, Cross Currency Portfolio.

The field is mandatory for CSV upload.

## 4.2.14 Overhang

Additional parameter called Overhang is available for uneven FX Swaps. Overhang is the difference between the notional of the far and the near leg which can be defined in the Notional Amount Groups, or "Any" value can be selected.

The field is mandatory for CSV upload.

## 4.2.15 Swap/NDS Near Period

Relevant only for Fx Swaps and NDS products and will be not considered for any other products configured within the same rule.

The field is mandatory for CSV upload.

## 4.2.16 Time Period

Time Period parameter (Any or custom Configuration Group) can be used to specify the outright period of an FX product, maturity of MM product or far leg period for Swaps and NDS. It is not relevant for Fx Futures, Metals and Commodities products or Cross Currency Portfolios. More information can be found in Chapter 3.8.

The field is mandatory for CSV upload.

![](_page_38_Picture_0.jpeg)

## 4.2.17 Fixing Reference

Fixing Reference (Any, custom Configuration Group or single value) can be defined as routing condition for NDF and NDS products for RFS and Orders negotiations. More information can be found in Chapter 3.10.

The field is mandatory for CSV upload.

## 4.2.18 In Competition

"In competition" is a criterion that can be used when the requester notifies the exclusive Liquidity Provider as part of the RFS message that the request was not submitted to any other competitor. The "In competition" parameter offers three different condition values (No=LP is not in competition; Yes=LP competes with other LPs, Any=no restriction).

The field is mandatory for CSV upload.

# 4.3 Pricing Routing

Once rule conditions are defined, the price source must be specified through the selection of the Route option.

Routes available for combinations of selected negotiations, products and order types within a single rule will become selectable, whereas routes not supported for those combinations will be greyed-out. An overview of all pricing routes available per negotiation, product and order type can be found in Table 1. If negotiations, products, or order types are bundled in groups, only pricing routes common for the group elements will be selectable. For example, Manual Intervention route will not be available for any rule containing SEP negotiation.

Some routes require additional configuration to become selectable, for example to select Pricing Server route, Pricing Server type must be selected.

![](_page_38_Picture_12.jpeg)

Defining Pricing Route is mandatory for CSV upload.

Figure 37 Route column.

The following pricing routes are available:

 Manual Intervention: The incoming request is routed to the Trader Worksheet of the manual trader users (as defined in the 'Manual Routing Group' tab) for pricing. Note: If a request does not match the conditions of

![](_page_39_Picture_0.jpeg)

any destination rule, then the request is routed to traders who are members of Default Group (Manual Routing Groups).

 Pricing Server: The price basis is either the 360T price feed from the 360T Market Maker Cockpit, Infotec (market data), or a provider individual price feed through an adapter interface to the provider's pricing system. The Auto Dealer automatically sends a price back to the requester.

Note: This route will only be selectable if any of the pricing servers from the column "Pricing Server" is defined.

- Pricing Server with Acknowledgement: This route is available for Spot, Forward, Time Option and Swaps. It requires a manual trader to acknowledge the price parameters before the ADS quotes are forwarded to the requester. The trader has the option to manually adjust margins or Forward points for auto-priced quotes for the pricing side of the request.
- No Pricing: The Auto Dealer does not forward the negotiation request to any price source. Consequently, the request is not sent to any manual traders. Therefore, quotes are not provided to the customer at all.
- Market Link: The request is forwarded to a set of market link providers and a selected trading venue. Therefore, this rule is used in combination with the selections under Market Link Provider, Market Link Trading Venue and Market Link Algorithm (see more details below). The Auto Dealer continuously updates the pricing with the best quote available from the group of external providers. The execution of the requesting entity automatically executes a back-to-back deal with the best price available at the time of execution. Upon completion, two trades are generated: one between the requesting entity and the provider; and a second one between the provider and the external market maker (market link provider).

Note: Only limited to spot and forward requests and to some defined currency pairs, requests are forwarded to the selected market link providers as two-way requests by default, irrespective of whether incoming requests are one sided (bid or ask) or two-way. Please contact 360T Client Advisory Services if you would like back-to-back requests to be routed to your Autodealer using the originating request`s selection.

 Forward Strip: This rule is only available for the RFS negotiation and product FX Forward and is a combination of Market Link and Pricing Server. The forward request is sent as an FX Spot request to the configured set of market link providers while the forward points are retrieved from an internal price source. The execution of the FX Forward by the requesting entity automatically executes a back-to-back FX Spot deal with the best price available at the time of execution. Upon completion, two trades are generated: an FX forward between the requesting entity and the provider; and a Spot trade between the

![](_page_40_Picture_0.jpeg)

providing bank and the external market maker (market link provider). Note 1: Forward Strip requires an internal market data source to be configured. Please contact 360T Client Advisory Services if you would like to implement such a data source.

Note 2: For some defined currency pairs, requests are forwarded as FX Spot to the selected market link providers as two-way requests by default, irrespective of whether incoming requests are one sided (bid or ask) or two-way. Please contact 360T Client Advisory Services if you would like back-to-back requests to be routed to your Autodealer using the originating request`s selection.

- Block Strip: This rule is an extension of Forward Strips. In a block containing spots and forwards, the net FX Spot portion of the block request is sent to a configured set of market link providers, while the forward points are retrieved from an internal market data/pricing source. The execution of the block request by the requesting entity automatically executes a back-to-back FX Spot deal with the best price available at the time of execution. Upon completion, a trade is generated between the requesting entity and the provider bank for each leg contained in the block. A trade for the net spot amount is also generated between the provider bank and the external market maker (market link provider)
- Time Option Forward: This rule is only available for the RFS product FX Time Option and is a Market Link strategy which generates two standard FX Forward requests back-to-back, picks the rate of the worst leg to automatically price the original request and when the original request is executed, executes back-to-back with the rate of the best leg. Upon completion, two trades are generated: an FX Time Option between the requesting entity and the provider bank; and a Forward trade between the provider bank and the external market maker (market link provider). The advantage is to enable completely automated pricing of Time Option Forwards without the need for the interbank market to support Time Option Forwards.
- Order To RFS: The Market Order is forwarded to a group of providers using a request for quote. Upon completion, two trades are generated: one between the order-placing I-TEX entity and the in-house bank; and a second one between the in-house bank and the external market maker.
- Order To Order: The order is forwarded to a provider as a new order. The execution of this order automatically executes a back-to-back deal at the moment of execution. Upon completion, two trades are generated: one between the order-placing I-TEX entity and the in-house bank; and a second one between the in-house bank and the external market maker.

Depending on the pricing route selected, additional pricing routing settings are required such as:

![](_page_41_Picture_0.jpeg)

Manual Routing Group is using the Manual Routing Groups as defined under the Configuration Groups. In case a Route "Manual Intervention" is defined, or in case a request that is routed to a pricing engine is republished for dealer intervention for any reason (e.g. disconnection of the external engine or reject from the external engine), then this parameter will be considered to route the request to the traders configured in the relevant Manual Routing Group. In case the traders of the Manual Routing Group are not logged into the 360T Trader Worksheet, then the request not shown to any manual trader (see also more details in Chapter 3.13).

The field is mandatory for CSV upload.

Provider is used to define the bank basket to be used for the back-to-back request. You can select any group that was defined under Provider Groups. Please note that in case of orders sent back-to-back to a single provider, the Market Link Provider group must contain only one provider, or a single provider must be selected in the Provider column. Default value is "Any".

The field is mandatory for CSV upload.

Market Link (ML) Trading Venue is used to define whether the linked back-toback request in the context of a market link route should be executed on a specific 360T venue. It is not relevant for the product Fx Spot. Default value is "OTC."

The field is mandatory for CSV upload.

Market Link Algorithm allows selecting the Algorithm group with regards to the duration of the request and number of quotes to be considered. Default value is "Any". The field is mandatory for CSV upload.

Liquidity Limit defines the maximum limit (notional in Home Currency) of liquidity available in the Market Link route in case of SEP negotiation.

Please note that the amount defined as liquidity limit is not the maximum amount that respective SEP requester is allowed to trade. However, it determines the maximum amount the SEP requester can trade in one ticket.

For example, when the SEP provider defines liquidity limit as 2 million, the SEP requester could still receive prices for 5 million from the SEP provider. The price would be constructed by different quotes which are provided for tier sizes up to defined liquidity limit of 2 million.

This field is optional for the upload and will be per default to "UNLIMTED" if no value is provided in the CSV file.

Wholesale: Enabling this field allows the SEP provider to price streaming requests based on its market link providers` price feeds without an equivalent back-to-back trade being generated with the market. This way, SEP provider can create stream prices through their market link providers while at the same time can wholesale the position.

![](_page_42_Picture_0.jpeg)

Please note that this feature is supported for the trades between a subsidiary and its parent entity i.e. when requester is a subsidiary (I-TEX) of the SEP provider.

This field is optional for the upload and will be per default to "FALSE" if no value is provided in the CSV file.

Up To Wholesale Amount: When the wholesale option is enabled, the notional of SEP trades that do not generate a back-to-back trade with the market can be specified. For example, setting a value of 1 million would mean that any trades in the currency of the entity up to 1 million would be wholesaled while trades over this notional would be hedged via the market link. Trade notionals are calculated into the currency of the entity using the values in the "Company Exchange Rates" to validate the said wholesale limits.

This field is optional for the upload and will be per default to "UNLIMITED" if no value is provided in the CSV file.

Min Execution Amount defines the minimum notional amount of the SEP execution which can be priced in the selected route.

This field is optional for the upload and will be per default to "0,00" if no value is provided in the CSV file.

The table below shows available pricing routes per negotiation, product and order strategy. If the parameter values are combined, only common pricing routes will be selectable. Combinations of parameters are not shown in the table.

| Parameters      |                | Availability of Pricing Routes |                            |                           |                    |                        |                              |                   |
|-----------------|----------------|--------------------------------|----------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|
| Negotiat<br>ion | Product        | Strate<br>gy                   | Manual<br>Intervent<br>ion | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |
| RFS             | Spot           | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Forward        | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Swap           | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Block          | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Time<br>Option | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Option         | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | NDF            | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |

© 2024 – 360 Treasury Systems AG <sup>43</sup>

![](_page_43_Picture_0.jpeg)

| Parameters      |                                    | Availability of Pricing Routes |                            |                           |                    |                        |                              |                   |
|-----------------|------------------------------------|--------------------------------|----------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|
| Negotiat<br>ion | Product                            | Strate<br>gy                   | Manual<br>Intervent<br>ion | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |
| RFS             | NDS                                | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Loan                               | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Deposit                            | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Metals<br>Outright<br>s            | n/a                            | Yes                        | Yes                       | No                 | No                     | No                           | Yes               |
| RFS             | Metals<br>Quarterl<br>y Strips     | n/a                            | Yes                        | Yes                       | No                 | No                     | No                           | Yes               |
| RFS             | Metals<br>Spreads                  | n/a                            | Yes                        | Yes                       | No                 | No                     | No                           | Yes               |
| RFS             | Commo<br>dity Asia<br>Swap         | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Commo<br>dity<br>Bullet<br>Swap    | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |
| RFS             | Future                             | n/a                            | Yes                        | No                        | Yes                | No                     | No                           | Yes               |
| RFS             | Cross<br>Currenc<br>y<br>Portfolio | n/a                            | Yes                        | No                        | No                 | No                     | No                           | Yes               |
| Order           | Forward                            | Algo<br>Order                  | No                         | Yes                       | No                 | No                     | No                           | Yes               |
| Order           | NDF                                | Algo<br>Order                  | No                         | Yes                       | No                 | No                     | No                           | Yes               |
| Order           | Spot                               | Algo<br>Order                  | No                         | Yes                       | No                 | No                     | No                           | Yes               |
| Order           | Forward                            | Fixing<br>Order                | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |

![](_page_44_Picture_0.jpeg)

| Parameters      |         |                     | Availability of Pricing Routes |                           |                    |                        |                              |                   |  |  |  |
|-----------------|---------|---------------------|--------------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|--|--|--|
| Negotiat<br>ion | Product | Strate<br>gy        | Manual<br>Intervent<br>ion     | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |  |  |  |
| Order           | Spot    | Fixing<br>Order     | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |
| Order           | Forward | If<br>Done<br>Order | Yes                            | Yes                       | No                 | No                     | No                           | Yes               |  |  |  |
| Order           | Spot    | If<br>Done<br>Order | Yes                            | Yes                       | No                 | No                     | No                           | Yes               |  |  |  |
| Order           | Forward | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |
| Order           | Future  | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |
| Order           | Spot    | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |
| Order           | Swap    | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | No                           | Yes               |  |  |  |
| Order           | Forward | Loop<br>Order       | Yes                            | No                        | No                 | No                     | No                           | Yes               |  |  |  |
| Order           | Spot    | Loop<br>Order       | Yes                            | No                        | No                 | No                     | No                           | Yes               |  |  |  |
| Order           | Forward | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |
| Order           | Future  | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |
| Order           | Spot    | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |
| Order           | Swap    | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |
| Order           | NDF     | Market<br>Orer      | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |
| Order           | Forward | OCO                 | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |
| Order           | Spot    | OCO                 | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |

![](_page_45_Picture_0.jpeg)

| Parameters      |               | Availability of Pricing Routes |                            |                           |                    |                        |                              |                   |  |  |
|-----------------|---------------|--------------------------------|----------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|--|--|
| Negotiat<br>ion | Product       | Strate<br>gy                   | Manual<br>Intervent<br>ion | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |  |  |
| Order           | Forward       | Stop<br>Order                  | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |
| Order           | Future        | Stop<br>Order                  | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |
| Order           | Spot          | Stop<br>Order                  | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |
| SEP             | Fx Spot       | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |
| SEP             | Fx<br>Forward | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |
| SEP             | NDF           | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |
| SEP             | NDS           | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |
| SEP             | Fx<br>Swap    | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |
| SEP             | Fx<br>Future  | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |

Table 1 Pricing Routing availability by Negotiation, Product and Order Type.

For Block requests HTML ADS checks the routing rules for the net amount as Spot (even if the block does not include Spot) and each leg of the block using the time period, amount and side defined on the leg. If all legs have the same routing defined, then the block is sent to that route. However, if legs have different routes, then the following priority is applied:

- No pricing
- Manual Intervention
- Pricing Server with Acknowledgement
- Pricing Server
- Market Link
- Block Strip

![](_page_46_Picture_0.jpeg)

# 5 Margin Rules

Margin Rules creation and modification works in a similar way to Pricing Routing Rules (see Chapter 4.1). For each rule a set of conditions must be defined, which trigger the outcome. The same configuration groups can be used for margin rules, as for Pricing Routing Rules (see Chapter 4.2).

Margin rules can be created or modified via CSV upload, similar to Pricing Routing Rules.

Note: Margins are not yet supported for Metals Outrights, Metals Quarterly Strips, Metals Spreads, Commodity Asian Swap, Options or Cross Currency Portfolios. Therefore, those products should be excluded from any Product Configuration Group used for Margin Rules.

The default Margin Group value while creating a new rule is the value "None", however the system does not allow saving a rule with this value.

![](_page_46_Figure_7.jpeg)

#### Figure 38 Creation of Margin Rules.

In case any condition of an existing rule is modified, and a specific margin group type has already been selected, not all values of the parameters will be available for selection, but only the values compatible with the selected margin type.

Margin groups of a specific margin type are available for combinations of selected negotiations, products and order types within a single rule. They will become selectable, whereas margin groups with margin types not supported for those combinations will be greyed-out in the "Margin Group" drop-down.

An overview of all margin types available per negotiation, product and order type can be found in Table 1. If negotiations, products, or order types are bundled in groups, only margin types/groups common for the group elements will be selectable. For example, a Fixed Amount margin will not be available for any rule containing SEP negotiation.

![](_page_47_Picture_0.jpeg)

| Parameters        |                                | Supported Margin Types |     |     |        |                 |          |             |  |
|-------------------|--------------------------------|------------------------|-----|-----|--------|-----------------|----------|-------------|--|
| Negotiation       | Order<br>Product               | Order<br>Strategy      | Pip | %   | Hybrid | Fixed<br>amount | Variable | Annual<br>% |  |
| RFS               | Spot                           | n/a                    | Yes | Yes | Yes    | Yes             | Yes      | Yes         |  |
| RFS               | Forward                        | n/a                    | Yes | Yes | Yes    | Yes             | Yes      | Yes         |  |
| RFS               | Swap                           | n/a                    | Yes | Yes | Yes    | Yes             | No       | Yes         |  |
| RFS               | Block                          | n/a                    | Yes | Yes | No     | Yes             | No       | No          |  |
| RFS               | Time<br>Option                 | n/a                    | Yes | Yes | No     | Yes             | No       | Yes         |  |
| RFS               | Option                         | n/a                    | No  | No  | No     | No              | No       | No          |  |
| RFS               | NDF                            | n/a                    | Yes | Yes | Yes    | Yes             | No       | Yes         |  |
| RFS               | NDS                            | n/a                    | Yes | Yes | Yes    | Yes             | No       | No          |  |
| RFS               | Loan                           | n/a                    | Yes | Yes | No     | Yes             | No       | Yes         |  |
| RFS               | Deposit                        | n/a                    | Yes | Yes | No     | Yes             | No       | Yes         |  |
| RFS               | Metals<br>Outrights            | n/a                    | No  | No  | No     | No              | No       | No          |  |
| RFS               | Metals<br>Quarterly<br>Strips  | n/a                    | No  | No  | No     | No              | No       | No          |  |
| RFS               | Metals<br>Spreads              | n/a                    | No  | No  | No     | No              | No       | No          |  |
| RFS               | Commodity<br>Asia Swap         | n/a                    | No  | Yes | No     | Yes             | No       | No          |  |
| RFS               | Commodity<br>Bullet<br>Swap    | n/a                    | No  | Yes | No     | Yes             | No       | No          |  |
| RFS               | Future                         | n/a                    | Yes | Yes | No     | Yes             | No       | No          |  |
| RFS               | Cross<br>Currency<br>Portfolio | n/a                    | No  | No  | No     | No              | No       | No          |  |
| Orders<br>Forward |                                | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders            | Forward                        | Fixing<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |

![](_page_48_Picture_0.jpeg)

| Parameters  |                  | Supported Margin Types |     |     |        |                 |          |             |  |
|-------------|------------------|------------------------|-----|-----|--------|-----------------|----------|-------------|--|
| Negotiation | Order<br>Product | Order<br>Strategy      | Pip | %   | Hybrid | Fixed<br>amount | Variable | Annual<br>% |  |
| Orders      | Forward          | If Done<br>Order       | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Forward          | Limit<br>Order         | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Forward          | Loop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Forward          | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Forward          | OCO                    | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Forward          | Stop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | Fixing<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | If Done<br>Order       | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | Limit<br>Order         | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | Loop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | OCO                    | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Spot             | Stop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Swap             | Limit<br>Order         | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | Swap             | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | NDF              | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | NDF              | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |

![](_page_49_Picture_0.jpeg)

| Parameters  |                  | Supported Margin Types |     |     |        |                 |          |             |  |
|-------------|------------------|------------------------|-----|-----|--------|-----------------|----------|-------------|--|
| Negotiation | Order<br>Product | Order<br>Strategy      | Pip | %   | Hybrid | Fixed<br>amount | Variable | Annual<br>% |  |
| Orders      | Future           | Limit<br>Order         | Yes | Yes | Yes    | Yes             | No       | No          |  |
| Orders      | Future           | Market<br>Order        | Yes | Yes | Yes    | Yes             | No       | No          |  |
| Orders      | Future           | Stop<br>Order          | Yes | Yes | Yes    | Yes             | No       | No          |  |
| Orders      | NDF              | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |
| Orders      | NDF              | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |
| SEP         | Fx Spot          | n/a                    | Yes | Yes | No     | No              | No       | No          |  |
| SEP         | Fx Forward       | n/a                    | Yes | Yes | No     | No              | No       | No          |  |
| SEP         | NDF              | n/a                    | Yes | Yes | No     | No              | No       | No          |  |
| SEP         | NDS              | n/a                    | Yes | Yes | No     | No              | No       | No          |  |
| SEP         | Fx Swap          | n/a                    | Yes | Yes | No     | No              | No       | No          |  |
| SEP         | Fx Future        | n/a                    | Yes | Yes | No     | No              | No       | No          |  |

Table 2 Margin Type availability by Negotiation, Product and Order Type.

# 5.1 Margin Application

The output of the margin rules conditions is application of the defined Margin Group. In addition to the margin values taken from the pre-configured Margin Groups, an additional margin multiplicator called "% Transformation" and a setting "Round to Full Pips" can be defined per each rule.

![](_page_50_Picture_0.jpeg)

![](_page_50_Figure_2.jpeg)

#### Figure 39 Applying margin transformation.

The % margin transformation is expressed as a percentage. Negative values are allowed, which implies a reduction of the original margin.

When a rule is applied, the margins specified within the rule are first calculated. The margin transformation is then determined and added onto or subtracted from the original margin.

For example, if the Bid Spot margin specified within a rule is 10 pips and the margin transformation is 5%, this would translate into a total margin of 10.5 pips (10 + 10 x 5%) being applied to the request.

If the Bid Spot margin was specified in percent, say 1%, and the margin transformation is -5%, this would imply a total applied margin of 0.95% (1% - 5% x 1%) on top of the supplied bid price.

If the Bid Spot margin within a rule was a fixed amount, say EUR 10 per transaction and the margin transformation is 5%, then the total margin applied on a transaction would be EUR 10.5 (10 + 10 x 5%).

Round to full pips is per default set to False but can be changed to True meaning that all outgoing rates are going to be rounded to full pips. Depending on auto-dealer configuration, it can be either rounding favouring provider or mathematical rounding.

Apart from margin transformation and rounding to pips, it is possible to change the way margins are applied on block requests. Per default, ADS picks the spot and forward margin based on the rule that matches the furthest leg and the net amount. The same forward margin is then applied to all legs. 360T CAS can activate the new "Split Block Margin" logic where margin is derived separately on each leg as well as the net spot amount. If the setting is enabled for ADS, the Spot margin is derived based on the Spot margin rule of the net Spot amount of the block and the side of the net Spot. Similarly, the forward margin is derived based on the forward margin rule for each leg of the block separately i.e. considering the amount on the leg, the time period and the side of the leg to do the matching.

![](_page_51_Picture_0.jpeg)

# 6 Rule Search

The rule search is a useful tool to search for a specific rule that applies to an incoming request at a specific time or to have a better overview of rules for a specific requester, currency pair or any other parameter.

|              |                                   |                    |           | $Q \vee$ Product= |                         |                    |         | Q Search      | Siz Save As     | Spot EURUSD Margin Rules                 | 而                  |             |         |      |
|--------------|-----------------------------------|--------------------|-----------|-------------------|-------------------------|--------------------|---------|---------------|-----------------|------------------------------------------|--------------------|-------------|---------|------|
|              | Currency Couple = EURUSD $\times$ |                    |           | Fx Swap           |                         |                    |         |               |                 |                                          |                    |             |         | €    |
|              | Rule ID                           | Comment            | Requester | Any<br>NDF        |                         |                    |         | Period        | Currency Couple | % Transformation                         | Margin Group       | MM Currency | Request |      |
|              | 3553                              | Special margin set | Platinium | Only Spot         |                         |                    |         | ay-2 weeks    | Majors          | 10                                       | Basis Fixed Amount | Any         | Any     | 40   |
| $\mathbf{r}$ | 3593                              |                    | Platinium | <b>NDS</b>        | Metals Quarterly Strips |                    |         | 114           | Majors          | 50                                       | Basis Fixed Amount | Any         | Any     | 60   |
| 10           | 3597                              |                    | Platinium | Fx Forward        |                         |                    |         | 6M            | Majors          | 350                                      | Basis Fixed Amount | Any         | Any     | 40   |
| 17           | 3601                              |                    | Platinium | Deposit           |                         |                    |         | 1V            | Majors          | 470                                      | Basis Fixed Amount | Any         | Any     | 40 百 |
| 19           | 3605                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | $O-2M$  | 1Y-18M        | Majors.         | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Basis Fixed Amount | Any         | Any     | 中 市  |
| 22           | 3558                              |                    | cold      |                   | Any                     | Spot, Forward, NDF | $O-1M$  | Today-2 weeks | <b>Majors</b>   | 950                                      | Basis Fixed Amount | Any         | Any     | 电音   |
| 26           | 3609                              |                    | Cold      |                   | Any                     | Spot, Forward, NDF | $0-1M$  | $2W-1M$       | Majors          | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Basis Fixed Amount | Any         | Any     | 40 面 |
| 30           | 3623                              |                    | Cold      |                   | Any                     | Spot, Forward, NDF | 0.1M    | 1M·6M         | Majors          | 200                                      | Basis Fixed Amount | Any         | Any     | 60   |
| 35           | 3782                              |                    | cold      |                   | Any                     | Spot, Forward, NDF | $O-1M$  | Today-2 weeks | Majors          | 950                                      | Basis Fixed Amount | Any         | Any     | 电音   |
| 39           | 3786                              |                    | Cold      |                   | Any                     | Spot, Forward, NDF | $0-1M$  | $2W-1M$       | <b>Majors</b>   | 200                                      | Basis Fixed Amount | Any         | Any     | 40 面 |
|              | 3582                              |                    | Platinium |                   | Any                     | Only Spot          | $1M-SM$ | Today-2 Weeks | Malors.         | 170                                      | Basis Fixed Amount | Any         | Any     | 中 市  |
|              | 3594                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | $1M-SM$ | 2W-1M         | Majors          | 200                                      | Basis Fixed Amount | Any         | Any     | 电音   |
| 11           | 3598                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | 1M-5M   | 1M-6M         | Majors          | 400                                      | Basis Fixed Amount | Any         | Any     | 40 面 |
| 13           | 3606                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | $1M-SM$ | 1Y-18M        | Malors          | 750                                      | Basis Fixed Amount | Any         | Any     | 中 百  |

#### Figure 40 Rule Search.

The user has the option to define, apply and save individual advanced filters for pricing or margin rules using a set of operators.

Depending on the rule parameter type, a search operator will automatically appear, e.g.=, ≠; ≥,>, ≤, < or other such as IN or NOT IN, which give the option to include or exclude a set of values in one data field at the same time. An additional IN condition value can be added after adding a comma to the first IN condition value added. For example, it is possible to search for the rules applicable to multiple Requester Groups. After confirming a filter by pressing the Enter key, additional filters can be added (AND relationship) to narrow down the results.

A search filter can be saved under a custom name and loaded, whenever needed.

|        |                |                                       | Q v Type to filter         |                          |                                     | Q Search ( $\frac{1}{24}$ Save As ) |                          | $\vee$ $\overline{w}$ |                        |
|--------|----------------|---------------------------------------|----------------------------|--------------------------|-------------------------------------|-------------------------------------|--------------------------|-----------------------|------------------------|
|        |                | Currency Couple = EURUSD $\times$ And | $Product = FX Spot \times$ |                          |                                     |                                     |                          |                       |                        |
|        | $\vee$ Rule ID | Comment                               | Requester                  | Margin Group             | Product                             | Negotiation                         | <b>Activation Period</b> | <b>Trading Venue</b>  | <b>Currency Couple</b> |
|        | 3819           | Test3                                 | TOD Clients                | Basis Percent            | Any                                 | Any                                 | Any                      | Any                   | Any                    |
|        | 3582           |                                       | Platinium                  | Basis Fixed Amount       | Only Spot                           | Any                                 | Any                      | Any                   | <b>Majors</b>          |
|        | 3583           |                                       | Platinium                  | Basis Fixed Amount       | Spot, Forward, NDF                  | Any                                 | Any                      | Any                   | Majors                 |
|        | 3584           |                                       | <b>Platinium</b>           | Basis Percent            | Spot, Forward, NDF                  | Any                                 | Any                      | Any                   | Majors                 |
|        | 3593           |                                       | <b>Platinium</b>           | Basis Fixed Amount       | Spot, Forward, NDF                  | Any                                 | Any                      | Any                   | Majors                 |
|        | 3594           |                                       | Plat                       | New Name of Query        | X                                   | Any                                 | Any                      | Any                   | Majors                 |
|        | 3595           |                                       | Plat                       | Spot EURUSD Margin Rules |                                     | Any                                 | Any                      | Any                   | Majors                 |
|        | 3596           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |
| 10     | 3597           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |
| 11     | 3598           |                                       | plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |
| 12     | 3599           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |
| $13 -$ | 3606           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |
| 14     | 3600           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |
| 15     | 3603           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |
| 16     | 3602           |                                       | Plat                       |                          | $(\times$ Cancel<br>$\checkmark$ ok | Any                                 | Any                      | Any                   | Majors                 |
|        |                |                                       |                            |                          |                                     |                                     |                          |                       |                        |

Figure 41 Saving Rule Search Filters.

![](_page_52_Picture_0.jpeg)

# 7 Audit Log

All changes of the ADS configuration, including their date & time, user who did the change and its details can be tracked in Audit Log.

| <b>Audit log</b><br>ADS Configuration Groups<br><b>Pricing Routing Rules</b><br><b>Margin Rules</b> |                                          |                                                                                             |               |  |
|-----------------------------------------------------------------------------------------------------|------------------------------------------|---------------------------------------------------------------------------------------------|---------------|--|
|                                                                                                     |                                          | Q                                                                                           | $\rightarrow$ |  |
| Log Date & Time                                                                                     | <b>User</b>                              | Description                                                                                 |               |  |
| Mar 19, 2024, 1:32:18                                                                               | GroupG.HybridA                           | Configuration Disabled                                                                      |               |  |
| Mar 10, 2024, 11:34:58.                                                                             | GroupG.TreasurerA                        | Removed Routing Rule 4085 (Position:1)                                                      |               |  |
| Mar 10, 2024, 11:34:43.                                                                             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Product Group(Name:Spot, Forward, NDF, Id:3571) Members Added (Fx Swap)                     |               |  |
| Mar 10, 2024, 11:33:26.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 position from: 7 to: 1                                            |               |  |
| Mar 10, 2024, 11:33:06.                                                                             | GroupG.Treasurer.A                       | Updated Routing Rule 4085 Requester: Any                                                    |               |  |
| Mar 10, 2024, 11:32:01.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 Route: Pricing Server                                             |               |  |
| Mar 10, 2024, 11:32:01.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 pricing server: Default PS (id:ax6455-a055a-gymhbes4-cqc_DEFAULT) |               |  |
| Mar 10, 2024, 11:32:01.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 Negotiation: RFS                                                  |               |  |
| Mar 10, 2024, 11:19:37.                                                                             | GroupG.HybridA                           | Updated Routing Rule 3857 Route: Pricing Server with Ack                                    |               |  |
| Mar 10, 2024, 11:19:04.                                                                             | GroupG.HybridA                           | Updated Routing Rule 3857 Route: Pricing Server                                             |               |  |
| Mar 10, 2024, 11:18:55.                                                                             | GroupG.HybridA                           | Updated Routing Rule 3857 Negotiation: RFS                                                  |               |  |
| Mar 10, 2024, 11:16:08.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Route: Pricing Server                                             |               |  |
| Mar 10, 2024, 11:15:46.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Negotiation: RFS                                                  |               |  |
| Mar 10, 2024, 11:14:59.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Manual routing: Any                                               |               |  |
| Mar 10, 2024, 11:14:59.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Min Execution Amount: 0                                           |               |  |
| Mar 10, 2024, 11:14:59                                                                              | GroupG.HybridA                           | Updated Routing Rule 7081 Wholesale: Disabled                                               |               |  |
| Mar 10, 2024, 11:14:59                                                                              | GroupG.HybridA                           | Updated Routing Rule 7081 Wholesale Amount: UNLIMITED                                       |               |  |

#### Figure 42 HTML ADS Audit Log.

Audit Log has a text search feature which can be for example used to find all modifications of a single rule by simply searching its ID:

|                        |                     |                                                       | Q Rule 7081                                              | $\rightarrow$ |
|------------------------|---------------------|-------------------------------------------------------|----------------------------------------------------------|---------------|
| Log Date & Time        | <b>User</b>         | <b>Description</b>                                    |                                                          |               |
| Mar 10, 2024, 11:16:08 | GroupG.HybridA      | Updated Routing Rule 7081 Route: Pricing Server       |                                                          |               |
| Mar 10, 2024, 11:15:46 | GroupG.HybridA      | Updated Routing Rule 7081 Negotiation: RFS            |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Manual routing: Any         |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Min Execution Amount: 0     |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Wholesale: Disabled         |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      |                                                       | Updated Routing Rule 7081 Wholesale Amount: UNLIMITED    |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Liquidity Limit: UNLIMITED  |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Fixing reference: Any       |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      |                                                       | Updated Routing Rule 7081 Market link trading venue: OTC |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Trading venue: Any          |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      |                                                       | Updated Routing Rule 7081 Swap/NDS Near period: Any      |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Requester action: Any       |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 In competition: Any         |                                                          |               |
| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Overhang: Any               |                                                          |               |
| $$                     | compared to deviate | the description of the model theoleced construct that |                                                          |               |

Figure 43 Audit Log: Text Search.

![](_page_53_Picture_0.jpeg)

# 8 Auto Dealer Control

All functionalities described in this section work for RFS Auto Dealer, Orders Auto Dealer and SEP Auto Dealer.

The enhanced Auto Dealer Control allows the user to start and stop the Auto Dealer by changing the "Auto Dealer Enabled" switch to enabled (started) or disabled (stopped) as desired and as shown in Figure 38. The activation or deactivation will have immediate effect.

![](_page_53_Picture_5.jpeg)

Figure 44 Auto Dealer Control enabling and disabling Auto Dealer switch

To have the Auto Dealer enabled only during specific times of the day, it is possible to enable or disable the Auto Dealer Schedule by changing the "Auto Dealer Schedule Enabled" switch accordingly and by keying in the start and stop times, which determine when the

Auto Dealer should be active. The times are expressed in UTC and the equivalent local times of the current user are displayed in brackets as shown in Figure 44.

| RFS Auto Dealer Schedule Enabled | $\vee$ Enabled |
|----------------------------------|----------------|
|                                  |                |

#### Figure 45 Auto Dealer Control - Auto Dealer Schedule Enabled switch

| RES Auto Dealer Start Time | $06:00$ $\Box$ |
|----------------------------|----------------|
|                            | (07:00 CEST)   |
| RFS Auto Dealer Stop Time  | 22:00<br>eb    |
|                            | (23:00 CEST)   |

Figure 46 Auto Dealer Control – Auto Dealer Start and Stop Times

![](_page_54_Picture_0.jpeg)

Important: To start the Auto Dealer as per schedule times, the "Auto Dealer Start Enabled" switch must be enabled too. Otherwise, the Auto Dealer stops at the scheduled time but must be manually started.

| RFS Auto Dealer Start Enabled | $\bigcirc$ Enabled |
|-------------------------------|--------------------|
|                               |                    |

Figure 47 Auto Dealer Control - Auto Dealer Start Enabled switch

In case the Schedule is enabled but the Start is disabled, the system displays an alert as shown in Figure 48.

![](_page_54_Picture_5.jpeg)

Figure 48 Auto Dealer Control – Auto Dealer Start related alert

In case a customized schedule is required, it is possible to make use of a new functionality: "Day by Day Definition". By changing the "Day by Day Definition" switch to enabled, a week schedule table is displayed and the daily time periods in which the Auto Dealer should be active can be keyed in as needed. The schedule table is displayed as shown in Figure 49.

| Day by Day Definition | $\sim$ Fnabled |
|-----------------------|----------------|
|                       |                |

Figure 43 Auto Dealer Control - Day by Day Definition switch

![](_page_55_Picture_0.jpeg)

| Monday                           | 07:00 Start UTC<br>(Monday, 08:00 CEST) | Stop UTC 3 19:00<br>(Monday, 20:00 CEST) |
|----------------------------------|-----------------------------------------|------------------------------------------|
|                                  |                                         |                                          |
| Tuesday<br>(Tuesday, 08:00 CEST) | 07:00 Start UTC                         | 19:00<br>Stop UTC<br>$\Rightarrow$       |
|                                  |                                         | (Tuesday, 20:00 CEST)                    |
| Wednes (Wednesday, 08:00 CEST)   | 07:00 Start UTC                         | Stop UTC $\implies$ 19:00                |
|                                  |                                         | (Wednesday, 20:00 CEST)                  |
| Thursday                         | 07:00 Start UTC                         | Stop UTC<br>19:00                        |
|                                  | (Thursday, 08:00 CEST)                  | (Thursday, 20:00 CEST)                   |
| Friday                           | $07:00$ $(3)$<br>Start UTC              | Stop UTC $\bigcirc$ 19:00                |
|                                  | (Friday, 08:00 CEST)                    | (Friday, 20:00 CEST)                     |

#### Figure 49 Auto Dealer Control Day by Day Definition schedule table

When "Day by Day Definition" is enabled, all times setup in the week schedule table overwrite the times previously setup as shown in Figure 49. In case "Day by Day Definition" is further disabled, the previously defined daily schedule will be valid again.

Important notes:

- a) Start and Stop times can be defined by either:
- moving the sliders through the time range on a given day of the week; or
- typing the times directly into the Start and Stop fields; or
- using +/- keys.

Only one start time and only one stop time are allowed on the same day:

b) A time range can be removed by right-clicking on the range line:

![](_page_56_Picture_0.jpeg)

| Saturday<br>(Saturday, 08:00 CEST) | 07:00 Start UTC | Stop UTC ● 19:00 ●<br>(Saturday, 20:00 CEST) |
|------------------------------------|-----------------|----------------------------------------------|
|                                    |                 | Remove this range                            |

Figure 50 Removing a time range from Day by Day Definition schedule table

c) Time ranges can be inserted by clicking twice on grayed range line; first click adds start time and second click adds stop time:

| O 07:00 Start UTC    |  |  |
|----------------------|--|--|
|                      |  |  |
| (Friday, 08:00 CEST) |  |  |

Figure 51 Adding a time range in Day by Day Definition: First click (start time)

|        | O 07:00 Start UTC    | Stop UTC 3 19:00     |
|--------|----------------------|----------------------|
| Friday | (Friday, 08:00 CEST) | (Friday, 20:00 CEST) |
|        |                      |                      |

Figure 52 Adding a time range in Day by Day Definition: Second click (stop time) on same day

d) Start time and stop time can be set on the same day as shown above or among different days as shown below:

| Sunday | 22:00 Start UTC<br>(Sunday, 23:00 CEST) |                      |
|--------|-----------------------------------------|----------------------|
|        |                                         | Stop UTC ● 19:00 ●   |
| Monday |                                         | (Monday, 20:00 CEST) |

Figure 53 Adding a time range in Day by Day Definition: first click on one day and second click on another day

e) Setting stop time further in subsequent days allows Auto Dealer to run continuously and stop on a specific weekday:

![](_page_57_Picture_0.jpeg)

| Sunday   | 22:00 Start UTC<br>(Sunday, 23:00 CEST) |                                            |
|----------|-----------------------------------------|--------------------------------------------|
| Monday   |                                         |                                            |
| Tuesday  |                                         |                                            |
| Wednes   |                                         |                                            |
| Thursday |                                         |                                            |
| Friday   |                                         | Stop UTC ● 19:00 ●<br>(Friday, 20:00 CEST) |
| Saturday |                                         |                                            |

#### Figure 54 Example of Auto Dealer Schedule setup to run continuously from Sunday to Friday

Setting of the Start time in the past in Day-by-Day Definition does not activate the Auto Dealer if the Auto Dealer is already disabled at the time of setting this start time. However, if the Auto Dealer is enabled, the stop time set for the future will be applied.

![](_page_58_Picture_0.jpeg)

# 10 Contacting 360T

#### Global Support

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### Germany

360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### Asia Pacific South Asia

#### Singapore

360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756

#### Middle East

#### United Arab Emirates

360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134

#### EMEA Americas

#### USA

360 Trading Networks, Inc 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902

#### India

ThreeSixty Trading Networks (India) Pvt Ltd Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437