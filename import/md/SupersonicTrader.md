# **SUPERSONIC TRADER**

![](_page_0_Picture_1.jpeg)

# **TEX MULTIDEALER TRADING SYSTEM**

USER GUIDE SUPERSONIC TRADER

© 360 TREASURY SYSTEMS AG, 2018 THIS FILE CONTAINS PROPRIETARY AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> INFORMATION INCLUDING <PERSON><PERSON><PERSON> SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG

| 1  | INTRODUCTION                                                                                                                                                                         | 5                               |
|----|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------|
| 2  | VIEW CONFIGURATION                                                                                                                                                                   | 6                               |
|    | VIEWS<br><br>STANDARD VIEW WITH TOP OF BOOK,<br>FIXED AMOUNT BUTTONS AND BASIC AREA WITH<br>AVERAGE PRICE<br>VWAP<br>VIEW<br>LIQUIDITY DEPTH VIEW<br>QUOTE DEPTH VIEW<br>NARROW VIEW | 8<br>10<br>12<br>13<br>14<br>14 |
| 3  | SUPERSONIC<br>TRADER EXECUTION TYPES15                                                                                                                                               |                                 |
|    | FILTERING LIQUIDITY FOR DISPLAY AND EXECUTION<br>                                                                                                                                    | 15                              |
| 4  | EXPRESSING ORDER INTENTION<br><br>DISCRETIONAL SLIPPAGE<br>16                                                                                                                        | 16                              |
|    | PRICE SLIPPAGE                                                                                                                                                                       | 17                              |
|    | TIME SLIPPAGE<br>                                                                                                                                                                    | 17                              |
| 5  | RFS BOOST<br>18                                                                                                                                                                      |                                 |
| 6  | SUPERSONIC TRADER RESTING ORDERS20                                                                                                                                                   |                                 |
|    | RESTING ORDERS DEFINITION                                                                                                                                                            | 20                              |
|    | FAST ORDERS<br>                                                                                                                                                                      | 24                              |
|    | AMENDING<br>RESTING STREAM ORDERS<br>DEACTIVATING AND REACTIVATING RESTING STREAM ORDERS<br>                                                                                         | 26<br>28                        |
|    | User defined deactivation / reactivation<br>                                                                                                                                         | 28                              |
|    | System-controlled deactivation / reactivation<br>                                                                                                                                    | 29                              |
|    | UPLOADING RESTING ORDERS<br>                                                                                                                                                         | 30                              |
|    | TWAP<br>ALGORITHM                                                                                                                                                                    | 31                              |
|    | Order definition                                                                                                                                                                     | 31                              |
|    | Minimum Parameters                                                                                                                                                                   | 32                              |
|    | Extended Parameters<br>                                                                                                                                                              | 32                              |
| 7  | EXECUTION REPORTS<br>35                                                                                                                                                              |                                 |
| 8  | STREAMS BLOTTER AND SESSIONS<br>40                                                                                                                                                   |                                 |
| 9  | ABILITY TO VIEW AND DEACTIVATE RESTING AND TWAP ORDERS                                                                                                                               | 41                              |
| 10 | SUPERSONIC TRADER POSITIONS42                                                                                                                                                        |                                 |
| 11 | FILLING OF CUSTOM FIELDS<br>43                                                                                                                                                       |                                 |
| 12 | CONTACT 360T<br>45                                                                                                                                                                   |                                 |

# **TABLE OF FIGURES**

| Figure 1 Supersonic Trader main screen5                                  |  |
|--------------------------------------------------------------------------|--|
| Figure 2 Save display configuration5                                     |  |
| Figure 3 Top of Book and Basic view<br>6                                 |  |
| Figure 4 Selecting providers6                                            |  |
| Figure 5 Floating view for EUR/USD<br>7                                  |  |
| Figure 6 Deactivate pending order and pending order symbol7              |  |
| Figure 7 Enable discretional slippage button<br>8                        |  |
| Figure 8 View Options<br>8                                               |  |
| Figure 9 General Board Settings<br>9                                     |  |
| Figure 10<br>Board Settings10                                            |  |
| Figure 11 Standard view with average price area10                        |  |
| Figure 12 Changing standard amounts11                                    |  |
| Figure 13 Showing available liquidity11                                  |  |
| Figure 14 Activating the VWAP view<br>12                                 |  |
| Figure 15 VWAP view12                                                    |  |
| Figure 16 Changing Notional in VWAP view13                               |  |
|                                                                          |  |
| Figure 17 Top of Book and Liquidity Depth view13                         |  |
| Figure 18 Basic and Quote Depth view<br>14                               |  |
| Figure 19 Narrow view14                                                  |  |
| Figure 20 Narrow view context menu for Fast orders<br>15                 |  |
| Figure 21 Filtering liquidity15                                          |  |
| Figure 22 Execution types<br>16                                          |  |
| Figure 23 Board setting bar with active slippage button17                |  |
| Figure 24 Price slippage in pips by stream view<br>17                    |  |
| Figure 25 Time Slippage in seconds by stream view<br>18                  |  |
| Figure 26 RFS Boost in Basic view18                                      |  |
| Figure 27 RFS Boost in Narrow view18                                     |  |
| Figure 28<br>RFS Boost activated with better offer RFS price19           |  |
| Figure 29 Best rate available using RFS Boost<br>19                      |  |
| Figure 30 Activating RFS Boost mode in VWAP view19                       |  |
| Figure 31 RFS Boost mode activated for 5m and for 15m EUR/USD<br>20      |  |
| Figure 32 RFS Boost mode running for 5m EUR/USD in VWAP view<br>20       |  |
| Figure 33 Resting Order definition Buttons in the Top of Book view<br>21 |  |
| Figure 34 Order definition21                                             |  |
| Figure 35 Stop Order triggering definition22                             |  |
| Figure 36 OCO Order definition22                                         |  |
| Figure 37<br>Order definition details23                                  |  |
| Figure 38 Select Resting Stream Orders view<br>23                        |  |
| Figure 39 Resting Stream Orders view23                                   |  |
| Figure 40 Select Fast Order View24                                       |  |
| Figure 41 Fast Order View and Resting Orders View<br>24                  |  |
| Figure 42 Select previously defined order<br>25                          |  |
| Figure 43 Define the incremental precision by right mouse click25        |  |
| Figure 44 Incremental Price Ladder26                                     |  |
| Figure 45 Increment limit price by arrow buttons<br>26                   |  |
|                                                                          |  |
| Figure 46 Select order to amend<br>27                                    |  |
| Figure 47 Order definition27                                             |  |
| Figure 48 Amend Resting Order28                                          |  |
| Figure 49 Deactivate all active resting orders button<br>28              |  |
| Figure 50 Deactivate all orders28                                        |  |
| Figure 51 Action on Resting Order<br>29                                  |  |
| Figure 52 Reactivation of resting orders<br>29                           |  |
| Figure 53: Import resting orders<br>30                                   |  |
| Figure 54 TWAP Order definition32                                        |  |

| Figure 55<br>TWAP additional execution parameters33                                               |    |
|---------------------------------------------------------------------------------------------------|----|
| Figure 56 Supersonic Trader screen including execution reports<br>35                              |    |
| Figure 57 Execution summary36                                                                     |    |
| Figure 58 Execution details<br>36                                                                 |    |
| Figure 59 Trade totally executed<br>36                                                            |    |
| Figure 60 Trade partially executed<br>37                                                          |    |
| Figure 61 Failed trade execution attempt<br>37                                                    |    |
| Figure 62 Failure details37                                                                       |    |
| Figure 63 Active resting order, partially executed<br>38                                          |    |
| Figure 64 Deactivated order, partially executed38                                                 |    |
| Figure 65 Execution reports area resizing<br>38                                                   |    |
| Figure 66 Board configuration for execution reports<br>38                                         |    |
| Figure 67 Supersonic screen with Execution Reports as animated notifications                      | 39 |
| Figure 68 Closing execution reports<br>39                                                         |    |
| Figure 69 Executed Streams table in Today's session40                                             |    |
| Figure 70 Switch between Trade-As Entities and Sessions<br>40                                     |    |
| Figure 71 Searching for old Supersonic Trader deals41                                             |    |
| Figure 72 Active Resting Orders Blotter<br>41                                                     |    |
| Figure 73 Active Group Orders Blotter41                                                           |    |
| Figure 74 Deactivating a single order or group of Resting or TWAP orders                          | 42 |
| Figure 75 Empty Active Group Orders tab -<br>since all orders have been deactivated42             |    |
| Figure 76 Deactivated orders are still visible in the Passive tab of the original requesting user |    |
| 42                                                                                                |    |
| Figure 77 Total positions by currency pair<br>42                                                  |    |
| Figure 78 Close currency positions43                                                              |    |
| Figure 79 Switching from Session to Session43                                                     |    |
| Figure 80 Filling of custom fields<br>44                                                          |    |

# <span id="page-4-0"></span>**1 INTRODUCTION**

This user manual describes the functionality of 360T Supersonic Trader, the execution tool for streaming FX Spot.

After entering the Supersonic Trader application the initial standard screen will open up. That screen can be customized according to the user's needs.

| <b>SST Supersonic Trader</b>                                                                                                                                                                                                               |                                                                                                                                               |                                                                                                    | $\begin{array}{ c c c c c c c c c c c c c c c c c c c$     |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|------------------------------------------------------------|
| File View Executions Import                                                                                                                                                                                                                |                                                                                                                                               |                                                                                                    | Help                                                       |
| $\omega$<br>$\Box$ USD - G7 $\times$<br>ORDERS X<br>$\circledcirc$<br>$\Box$ EUR - G10 $\times$<br>$\Box$ Asia $\times$                                                                                                                    | Total or Partial Fill at Rate<br>Best Price<br>4 ▶ 国                                                                                          | Views -<br>Ō.                                                                                      | 目<br><b>Executions</b><br>e                                |
| $B$ $Q$ $B$<br>$B \oplus a$<br>$USD - \odot x$<br>EUR $\blacktriangledown$ CAD $\blacktriangledown$ O<br>$\circ$<br>$EUR -$<br>$\circledcirc$<br>$\times$                                                                                  | 999<br>EUR $\blacktriangleright$ CHF $\blacktriangleright$ $\heartsuit$ x<br>$^{\circ}$                                                       | $\circledcirc$<br>$EUR -  $<br>$GBP - O \times$                                                    | 999                                                        |
| $\begin{array}{ c c c c c c c c c c c c c c c c c c c$<br>pips $\begin{array}{ c c c c c c c c c c c c c c c c c c c$<br>o                                                                                                                 | pips $\boxed{0}$ 0.6 $\boxed{ }$ sec<br>$\circledcirc$                                                                                        | pips $\begin{array}{ c c c c c }\hline 0.6 & . \end{array}$ sec<br>$\omega$                        |                                                            |
| $1.52$ 1.52<br>1.16<br>Sell USD<br>Sell EUR<br><b>Buy EUR</b><br><b>Buy USD</b><br>1.16<br>470<br>$48_4$<br>95 <sub>8</sub><br>946<br><b>RES</b><br><b>RFS</b><br>2,000,000.00<br>1,000,000.00                                             | $1.12$ 1.12<br><b>Buy EUR</b><br>Sell EUR<br>449<br>441<br><b>RFS</b><br>1,000,000.00                                                         | 0.89<br>0.89<br>Sell EUR<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br><b>RFS</b><br>1,000,000.00 | <b>Buy EUR</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |
| $B$ $Q$ $B$<br>$B \oplus a$<br>$\circ$<br>$EUR -$<br>$JPY = \bigcirc x$<br>$^{\circ}$<br>$EUR -$<br>$USD - O \times$<br>pips $\boxed{0}$ 0.6 $\rightarrow$ sec<br>pips $\begin{array}{ c c c }\hline 0&0.6&\div \end{array}$ sec<br>O<br>囫 | $B$ $Q$ $B$<br>$^{\circ}$<br>$EUR - \blacksquare$<br>$SEK = \bigcirc$ $\times$<br>pips $\begin{array}{ c c c }\hline 0&0.6&-$ sec<br>$\omega$ | $^{\circ}$<br>EUR $\blacktriangledown$ NOK $\blacktriangledown$ $\times$<br>pips 0.6 + sec<br>G    | 999                                                        |
| 128.<br>Sell EUR<br>1.16<br>128.<br><b>Buy EUR</b><br>1.16<br><b>Buy EUR</b><br>Sell EUR                                                                                                                                                   | 10.59<br>10.59<br><b>Buy EUR</b><br>Sell EUR                                                                                                  | 9.79<br>9.80<br>Sell EUR                                                                           | <b>Buy EUR</b>                                             |
| $\frac{1}{\text{RHS}}$ 482<br>99 <sub>6</sub><br>998<br>472⊺<br><b>RFS</b><br>1,000,000.00<br>1,000,000.00                                                                                                                                 | $119$ $\overline{\phantom{1}}$ $\overline{\phantom{1}}$ 203<br>1,000,000.00                                                                   | 97 <sub>2</sub><br><b>RFS</b><br>1,000,000.00                                                      | 049                                                        |
| $B$ $Q$ $B$<br>$HKD - O \times$<br>$EUR -$<br>$\circ$                                                                                                                                                                                      |                                                                                                                                               |                                                                                                    |                                                            |
| pips 0 0.6 - sec<br>$\omega$                                                                                                                                                                                                               |                                                                                                                                               |                                                                                                    |                                                            |
| 9.14<br>9.14<br><b>Buy EUR</b><br>Sell EUR<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>239<br><b>RFS</b><br>1,000,000.00                                                                                                                |                                                                                                                                               |                                                                                                    |                                                            |
| Executed Today   All Today   Active   Passive   Old   Positions   Open Positions   Closed Positions                                                                                                                                        |                                                                                                                                               | Session:                                                                                           | V New Session<br>Today                                     |
| Execution Execution Id Requested Provider Requester Requester Action Currency Notional Amount Executed Amount Notional Currency Execution Rate Effective Date Reference Id Execution Status Changed Trading Time                           |                                                                                                                                               |                                                                                                    |                                                            |
|                                                                                                                                                                                                                                            |                                                                                                                                               |                                                                                                    |                                                            |
| Sep 7, 2018 7:59:44 AM GMT                                                                                                                                                                                                                 |                                                                                                                                               |                                                                                                    | User: 360T.Luetjens, Company: 360TGROUP                    |

<span id="page-4-1"></span>Figure 1 Supersonic Trader main screen

The screen is basically divided into three areas;

- the executable streams area (top left)
- the execution reports (top right)
- the table views in the deal blotter for executed streams, orders and positions (bottom)

These components along with their configuration and functionality are described in more detail in the following chapters.

All configurations are automatically saved when the user logs out of the application. It is also possible to save one's configuration at any time by selecting "Save perspective" over the View menu.

<span id="page-4-2"></span>![](_page_4_Picture_13.jpeg)

Figure 2 Save display configuration

# <span id="page-5-0"></span>**2 VIEW CONFIGURATION**

The initial screen can be configured according to the individual needs of each user.

Further screen tabs (top left) can be added by clicking on the button in the left upper corner of the screen. The tabs can be individually named by double-clicking on the name field.

It is possible to add stream views by clicking on the button in the menu bar. The new currency pair appears below the existing screen pair.

When a new stream view is opened, it is set by default to the currency couple EUR/USD. Different currencies can be selected from the drop-down lists. Up to ten currency couples can be displayed on one tab.

Stream providers can be selected for each currency couple individually from the list of available providers. Providers can be defined separately for Supersonic and for RFQ Boost.

In order to select providers click the button in the upper right corner of a stream view.

![](_page_5_Figure_9.jpeg)

<span id="page-5-1"></span>Figure 3 Top of Book and Basic view

The list of available stream providers will open and you can select providers from this list by using the arrow buttons. The list contains providers that the selected trade-as entity has counterpart relationships with.

<span id="page-5-2"></span>![](_page_5_Picture_12.jpeg)

Figure 4 Selecting providers

Providers can be selected individually per currency couple. Note that the bank basket is unique per currency pair, in case the same currencies are configured several times on one or several tabs, the subscriptions will go to the same configured providers.

#### **Lock Tabs or Screen**

You can lock a particular stream view or a whole tab by clicking on the lock and unlock buttons either in the upper right side of the menu bar (for the whole tab) or in the upper right corner of one stream view (for this particular currency couple). Locking will prevent a user from executing trades accidently while it is still possible to follow market rates, edit the notional field and change currencies in the stream view.

#### **Floating Window**

It is possible to open currency couples in separate **floating windows** by clicking the floating view button . The selected currency couple will open in a separate window.

| <b>SST EUR/USD</b>                                                                                                                                                              | $\mathbf{x}$<br>Θ<br>▭  |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|
| F<br>Views $\blacktriangledown$<br><b>Fill at Best</b><br>$\overline{\phantom{a}}$                                                                                              | 陰<br><b>Executions</b>  |
| $\theta$<br><b>USD</b><br>×<br><b>EUR</b><br>$\circ$<br>▼<br>$\div$ pips $\begin{array}{ c c c }\hline \text{O} & \text{O.6} & \div \text{sec} \end{array}$<br>0.3 <sup>1</sup> |                         |
| $1.16$   1.16<br>Sell USD<br><b>Buy USD</b><br>35<br><b>RFS</b><br>2,000,000.00                                                                                                 | <b>SERVICE SERVICES</b> |

<span id="page-6-0"></span>Figure 5 Floating view for EUR/USD

Please note that in the floating view, only executions done from this floating window will be displayed. To get a full view of **all** executions you need to look at the main Supersonic Trader screen. If you close and re-open the floating view, past executions will not be shown.

If the view is changed in a floating screen, you will need to resize the floating screen to see the full display.

In case Resting orders have been placed and the Resting Orders view is not activated, a shopping cart symbol and a number will appear in the top area of a currency stream to indicate that pending orders are monitored.

The sign allows to deactivate all placed orders (top left) or all orders placed in a specific currency pair (in each currency stream selection). You will find more details on orders further below in Chapter 6 of this manual.

![](_page_6_Figure_13.jpeg)

<span id="page-6-1"></span>Figure 6 Deactivate pending order and pending order symbol

The symbol displayed next to the views selection option allows to activate and deactivate the slippage function. When activated, the symbol will be highlighted in green as shown above.

| When deactivated, the symbol is shown in grey |  |
|-----------------------------------------------|--|
|-----------------------------------------------|--|

![](_page_7_Picture_4.jpeg)

Figure 7 Enable discretional slippage button

<span id="page-7-1"></span>You will find more details on the slippage definition options for board and streams in Chapter 4 of this user guide.

# <span id="page-7-0"></span>**Views**

To optimize your viewing setup, it is possible to customize the information visible in a stream view by clicking the Views menu found in the middle of the screen.

![](_page_7_Picture_9.jpeg)

<span id="page-7-2"></span>Figure 8 View Options

The standard setup is a best practice view. The options related to Resting Orders (Fast Order view and Resting Stream Orders) are described below:

By using the symbol Stream Board Preferences can be configured for the concerned board.

<span id="page-8-0"></span>Figure 9 General Board Settings

If the Resting Orders function is active for the user, he/she can configure whether a screen should be displayed upon logout when resting orders are still active. The second option allows to define that the user's attention should be attracted by a sound when a resting order is executed.

Users with a small monitor or a small screen resolution can hide the execution report area (top right of the Supersonic Trader screen) and get small ticket popups on the right side of the screen instead, by selecting the option "Display execution reports as animated notifications".

In the Board Settings section, the number of rows displayed for each currency pair in a maximized and minimized view can be configured.

The minimum and maximum number of rows can be defined for the VWAP view, for the liquidity depth view and for the quote depth view.

Default Slippage configurations can be defined for price and time. You will find more details on the slippage definition options for board and streams in Chapter 4 of this user guide.

| <b>SST</b> Options                               | $\overline{\mathbf{x}}$                                                                     |
|--------------------------------------------------|---------------------------------------------------------------------------------------------|
| <b>General Settings</b><br><b>Board Settings</b> | <b>Board Settings</b><br><b>VWAP Rows-</b><br>3<br>Min Rows:<br>5<br>Max Rows:              |
|                                                  | <b>Liquidity Rows</b><br>Min Rows:<br>$\overline{2}$<br>5<br>Max Rows:                      |
|                                                  | <b>Quote Rows-</b><br>$\overline{a}$<br>Min Rows:<br>5<br>Max Rows:                         |
|                                                  | <b>Pip Slippage</b><br><b>OFF</b><br>Default:<br>$0.3 -$<br>Default Value:<br>Time Slippage |
|                                                  | <b>ON</b><br>Default:<br>$0.6 -$<br>Default Value:                                          |
|                                                  | Ok<br>Cancel<br>Apply                                                                       |

<span id="page-9-1"></span>Figure 10 Board Settings

By using the symbol , the views will be reduced to the number of rows defined as minimum

number. By clicking the symbol , the defined maximum number of rows per view will be displayed again. The plus/minus tab is found next to the lock keys.

### <span id="page-9-0"></span>**Standard view with top of book, fixed amount buttons and basic area with average price**

The default view set up when first using Supersonic Trader shows Top of Book Area with maximum liquidity at best quote. You can add the Basic view to see the average quote for a given notional amount.

<span id="page-9-2"></span>![](_page_9_Figure_8.jpeg)

You will select the trade-as entity, currency couple, notional currency and define your standard execution amounts at best quote.

Right-clicking onto a field will allow to change a standard amount. Select one of the given values from a drop-down list or enter any other value manually by selecting "Other". If not needed and to gain space on the screen, the standard fixed amount buttons can be removed by unchecking the View "Fixed Amounts".

| <b>331 Supersonic Trader</b>                                                                                   |                 |                |                  |      |                  |                |                      |  |
|----------------------------------------------------------------------------------------------------------------|-----------------|----------------|------------------|------|------------------|----------------|----------------------|--|
| Eile<br>View                                                                                                   | Executions      | Import         |                  |      |                  |                |                      |  |
| Views -<br>Total or Partial Fill at Rate v<br>NEW TAB X<br><b>4 ▷ 国</b><br>0                                   |                 |                |                  |      |                  |                |                      |  |
| $\blacksquare$ 0.1 $\div$ p $\blacksquare$ 0.6 $\div$ s<br>€<br><b>EUR</b><br><b>USD</b><br>×<br>▼∩<br>$\circ$ |                 |                |                  |      |                  |                |                      |  |
| <b>BID</b>                                                                                                     | <b>Sell EUR</b> |                | 1.31             | 1.31 |                  | <b>Buy EUR</b> | <b>ASK</b>           |  |
| 5                                                                                                              |                 |                |                  |      | $\mathbf{4}_{6}$ |                |                      |  |
| m                                                                                                              |                 | 1 <sub>m</sub> |                  |      | 1 <sub>m</sub>   | v              | 500k                 |  |
| 5 <sub>m</sub>                                                                                                 | 4m              | 3m             | 2m               | 2m   | 3 <sub>m</sub>   |                | 1 <sub>m</sub><br>2m |  |
|                                                                                                                | 1,000,000       |                | <b>RFS Boost</b> |      |                  | 1.             | 3m                   |  |
|                                                                                                                |                 |                |                  |      |                  | 1.3            | 4m                   |  |
|                                                                                                                | 1.31 943        |                | 0.3              |      |                  |                | 5m                   |  |
|                                                                                                                |                 |                |                  |      |                  |                | 10 <sub>m</sub>      |  |
|                                                                                                                |                 |                |                  |      |                  |                | 15m                  |  |
|                                                                                                                |                 |                |                  |      |                  |                | Entry Field (EF)     |  |
|                                                                                                                |                 |                |                  |      |                  |                | Other                |  |

<span id="page-10-0"></span>Figure 12 Changing standard amounts

The outer fields on both sides of Top of Book stream view show **maximum liquidity at best quote.**

Whether a quote is available for a certain notional or not can also be seen by moving the mouse over the various amount buttons. If requested liquidity is available, the amount will be underlined in green, if not available, the amount will be underlined in red.

In the screenshot below, the maximum liquidity at best quote for Buy EUR/USD is 2 million EUR, therefore the 5 million amount button shows the red line and cannot be hit for execution.

![](_page_10_Figure_9.jpeg)

<span id="page-10-1"></span>Figure 13 Showing available liquidity

If an amount is available, it can be executed by double-click on it (or, on request, by singleclick-execution).

Where treasurer limits (intraday and/or per trade) have been set and already reached, the available price will be displayed, but will be underlined in red to show that it's not executable.

# <span id="page-11-0"></span>**VWAP view**

You can display the Volume Weighted Average Price (VWAP) view by selecting "VWAP" from the Views drop-down list.

| Views $\star$         |  |  |  |
|-----------------------|--|--|--|
| Top of Book Area      |  |  |  |
| <b>Fixed Amounts</b>  |  |  |  |
| Fast Order View       |  |  |  |
| Basic View            |  |  |  |
| VWAP                  |  |  |  |
| Resting Stream Orders |  |  |  |
| Liquidity Depth       |  |  |  |
| Quote Depth           |  |  |  |
|                       |  |  |  |
| Οk<br>Cancel          |  |  |  |
|                       |  |  |  |

<span id="page-11-1"></span>Figure 14 Activating the VWAP view

| <b>350 Supersonic Trader</b>                                                                                  |                  |                  |  |  |  |
|---------------------------------------------------------------------------------------------------------------|------------------|------------------|--|--|--|
| File<br>Executions<br>Import<br>View                                                                          |                  |                  |  |  |  |
| Total or Partial Fill at Rate ▼<br>Views $\blacktriangledown$<br>VWAP<br>×<br>目<br>Þ                          |                  |                  |  |  |  |
| $\bigcirc$ 0.1 $\bigcirc$ p $\bigcirc$ 0.6 $\bigcirc$ s<br>₿<br>€<br><b>EUR</b><br><b>USD</b><br>×<br>$\circ$ |                  |                  |  |  |  |
| 1,000,000                                                                                                     | <b>RFS Boost</b> | 1,000,000        |  |  |  |
| 1.31 968                                                                                                      | 0.3              | 1.31 971         |  |  |  |
| 1.31 966<br>3m                                                                                                | 0.5              | $1.31$ 971<br>Зm |  |  |  |
| $1.31$ 965<br>5 <sub>m</sub>                                                                                  | 0.6              | $1.31$ 971<br>5m |  |  |  |
| $1.31$ 964<br>7 <sub>m</sub>                                                                                  | 0.8              | 1.31972<br>7m    |  |  |  |
| 1.31963<br>10 <sub>m</sub>                                                                                    | 0.9              | 1.31972<br>10m   |  |  |  |
| 1.31962<br>15m                                                                                                | 1.1              | 1.31973<br>15m   |  |  |  |

<span id="page-11-2"></span>Figure 15 VWAP view

In VWAP available quotes for various amounts are displayed. The quotes displayed show either **the average or the best of the streamed quotes** for the respective underlying amount depending on the selected execution type (see below Chapter 3). As VWAP is only for the order types which allow splitting over several quote providers, it must be understood that the displayed average rate is best effort and cannot be guraranteed.

The amounts shown in the VWAP view can be changed by right-clicking left or right on a price button and selecting an amount from the drop-down list or by entering a certain value.

| <b>350 Supersonic Trader</b>                                                                                                          |                        |                |  |  |  |
|---------------------------------------------------------------------------------------------------------------------------------------|------------------------|----------------|--|--|--|
| Eile<br>View<br>Executions<br>Import                                                                                                  |                        |                |  |  |  |
| Views $\blacktriangledown$<br>Total or Partial Fill at Rate ▼<br><b>VWAP</b><br>×<br>▷ 国                                              |                        |                |  |  |  |
| $\vert 0.1 \leftarrow \vert p \vert \vert 0 \vert 0.6 \leftarrow \vert s \vert$<br>E<br>€<br><b>USD</b><br><b>EUR</b><br>×<br>$\circ$ |                        |                |  |  |  |
| 1,000,000                                                                                                                             | <b>RFS Boost</b>       | 1,000,000      |  |  |  |
| 1.31 923                                                                                                                              | $-0.6$                 | 1.31917        |  |  |  |
| 1.31922                                                                                                                               | RFS Boost Mode<br>500k | 1.31917<br>Зm  |  |  |  |
| 1.31<br>920<br>1 <sub>m</sub><br>2m                                                                                                   |                        | 1.31921<br>5m  |  |  |  |
| 1.31919<br>3m<br>v<br>4m                                                                                                              |                        | 1.31923<br>7m  |  |  |  |
| 5m<br>1.31917<br>10 <sub>m</sub>                                                                                                      |                        | 1.31925<br>10m |  |  |  |
| 15m<br>1.31917                                                                                                                        | Other                  | 1.31926<br>15m |  |  |  |

<span id="page-12-1"></span>Figure 16 Changing Notional in VWAP view

This amount will be executed then when double-clicking (or single click left mouse button on request) the price button.

# <span id="page-12-0"></span>**Liquidity Depth view**

The Liquidity Depth view is another option to be selected from the Views drop-down list. It shows an aggregated view of available amounts quoted, sorted from best quote on top to worst.

| <b>350 Supersonic Trader</b>                                       |                                                           |                 |                 |                |                |                                          |
|--------------------------------------------------------------------|-----------------------------------------------------------|-----------------|-----------------|----------------|----------------|------------------------------------------|
| File<br>View                                                       | Executions<br>Import                                      |                 |                 |                |                |                                          |
| Views $\star$<br>Total or Partial Fill at Rate ▼<br>] MISC.<br>▷ 国 |                                                           |                 |                 |                |                |                                          |
| <b>EUR</b><br>$\circledast$                                        | $0.1 - p$ 0.6 $-$ s<br>$\theta$<br><b>USD</b><br>$\times$ |                 |                 |                |                |                                          |
| <b>BID</b>                                                         | <b>Sell EUR</b>                                           | 1.31            | 1.31            |                | <b>Buy EUR</b> | <b>ASK</b>                               |
| $\overline{2}$                                                     | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                  |                 |                 | $93_2\,$       |                | 4                                        |
| m                                                                  | 1 <sub>m</sub>                                            |                 |                 | 1 <sub>m</sub> |                | m                                        |
| 5 <sub>m</sub><br>4 <sub>m</sub>                                   | 3 <sub>m</sub>                                            | 2m              | 2m              | 3m             | 4m             | 5 <sub>m</sub>                           |
| Depth                                                              | <b>Amount</b>                                             | Quote           | Quote           | Amount         | Depth          |                                          |
|                                                                    | 2m                                                        | 936             | 93 <sub>2</sub> | 4m             |                | ▲                                        |
|                                                                    | 1m                                                        | $93_5$          | 940             | 10m            |                |                                          |
| inistrial                                                          | 5m                                                        | 934             | $94_{2}$        | 10.5m          |                | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |
|                                                                    | 7m                                                        | 93 <sub>2</sub> | 943             | 5m             |                |                                          |
|                                                                    | 1m                                                        | $93_1$          | 944             | 7m             |                | ÷                                        |

<span id="page-12-2"></span>Figure 17 Top of Book and Liquidity Depth view

# <span id="page-13-0"></span>**Quote depth view**

Quote Depth view is another option available to be selected from the Views drop-down list. It shows the quote depth of selected providers sorted by quote value.

| <b>350 Supersonic Trader</b>                                                          |                                                       |       |                 |         |                         |  |
|---------------------------------------------------------------------------------------|-------------------------------------------------------|-------|-----------------|---------|-------------------------|--|
| File<br>View<br>Executions                                                            | Import                                                |       |                 |         |                         |  |
| Views $\star$<br>Total or Partial Fill at Rate $\blacktriangledown$<br>◯ MISC.<br>▷ 国 |                                                       |       |                 |         |                         |  |
| <b>EUR</b><br>$\bullet$                                                               | $0.1 - p 0.6 - s$<br>$\theta$<br>且<br><b>USD</b><br>× |       |                 |         |                         |  |
| <b>RFS Boost</b><br>1,000,000<br>1,000,000                                            |                                                       |       |                 |         |                         |  |
| 1.31 920                                                                              |                                                       | 0.2   |                 | 1.31922 |                         |  |
| Provider<br>Band                                                                      |                                                       | Quote | Quote           | Band    | Provider                |  |
| RBS.LND.DEMO                                                                          | Зm                                                    | 920   | 92 <sub>2</sub> | 1m      | BNPP.DEMO<br>▲          |  |
| RBS.LND.DEMO                                                                          | 5m                                                    | 919   | 92 <sub>2</sub> | 1m      | DB.DEMO                 |  |
| ilining<br>RBS.LND.DEMO<br>7m                                                         |                                                       | 91a   | 92 <sub>2</sub> | 1m      | <b>Investec Bank.TE</b> |  |
| SEB.DEMO                                                                              | 2.5m                                                  | 91a   | 92 <sub>2</sub> | 1m      | LLOYDS.DEMO             |  |
| <b>BOAL.DEMO</b>                                                                      | 2m                                                    | 91a   | 92 <sub>7</sub> | 2m      | <b>BOAL.DEMO</b>        |  |

<span id="page-13-2"></span>Figure 18 Basic and Quote Depth view

A certain provider may occur several times in the list as they might offer different quotes depending on the tradeable amount band.

All views mentioned above can be displayed at the same time.

# <span id="page-13-1"></span>**Narrow view**

Narrow view corresponds to the basic view without the spread oscillator between bid and ask price and thus allows to reduce the space used by the currency streams in the user interface.

![](_page_13_Picture_10.jpeg)

Figure 19 Narrow view

<span id="page-13-3"></span>Configurations and features from the basic view are available but reordered with the aim to win space: the slippage configurators are moved down and there is only one amount field placed in the middle.

The narrow view can be combined with the Fast Order and the VWAP views.

In case the VWAP view is added to the basic view, the different execution modes and defined amounts can be defined over the right mouse click context menu.

| <b>USD</b><br><b>GBP</b><br>$\blacksquare$<br>⊚     | 自動日<br>$\bullet$ $\circ$ $\times$        |  |  |  |  |  |
|-----------------------------------------------------|------------------------------------------|--|--|--|--|--|
| 0 0.2 pips 0 0.6<br>sec                             |                                          |  |  |  |  |  |
| 1.12400<br>1.24761                                  |                                          |  |  |  |  |  |
| LO 1m GBP / EOTD                                    |                                          |  |  |  |  |  |
| $1.24$ 761                                          | $112 - 4$<br><b>DIRECT TRADING</b>       |  |  |  |  |  |
| $1.24$ $759$                                        | <b>SEP</b><br><b>RES Boost</b>           |  |  |  |  |  |
| $1.24$ $758$                                        | <b>RESTING ORDERS</b><br><b>BID</b>      |  |  |  |  |  |
| $\frac{1.24}{1.24}$ $\frac{756}{755}$ $\frac{1}{1}$ | LO<br><b>SO</b>                          |  |  |  |  |  |
|                                                     | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |  |  |  |  |  |
|                                                     | 500 <sub>k</sub>                         |  |  |  |  |  |
|                                                     | 1 <sub>m</sub>                           |  |  |  |  |  |
|                                                     | 2 <sub>m</sub>                           |  |  |  |  |  |
|                                                     | 3m                                       |  |  |  |  |  |
|                                                     | 4 <sub>m</sub>                           |  |  |  |  |  |
|                                                     | 5 <sub>m</sub><br>10 <sub>m</sub>        |  |  |  |  |  |
|                                                     | 15 <sub>m</sub>                          |  |  |  |  |  |
|                                                     | Other                                    |  |  |  |  |  |

<span id="page-14-2"></span>Figure 20 Narrow view context menu for Fast orders

You can switch between direct click and trade execution (SEP), which displays the quote buttons in green, RFS Boost mode, which displays the quote buttons in black, and Resting Orders, which displays the quote buttons in blue.

# <span id="page-14-0"></span>**3 SUPERSONIC TRADER EXECUTION TYPES**

### <span id="page-14-1"></span>**Filtering liquidity for display and execution**

There are 3 options with regards to displaying liquidity:

![](_page_14_Figure_9.jpeg)

<span id="page-14-3"></span>Figure 21 Filtering liquidity

**"Full Amount"** mode will include liquidity which can be executed exclusively, i.e. the order amount will be filled by one liquidity provider. RFS Boost capability is active as well, since RFS quotes can only be filled for the requested amount totally.

**"Sweep"** mode displays order book style (Standard) streams which can be aggregated. No RFS Boost is possible to extend that liquidity. Note that there will be no speed-bumps after execution.

**"Best Price"** includes all liquidity (standard and exclusive streams and RFS Boost capability).

# <span id="page-15-0"></span>**Expressing order intention**

Supersonic Trader provides **four** different modes to fill requests.

The options available are

- Total or Partial Fill at Rate
- Fill at Best
- Fill or Kill
- Partial Fill or Kill

![](_page_15_Picture_9.jpeg)

Figure 22 Execution types

<span id="page-15-2"></span>They have the following definitions:

"**Total or Partial Fill at Rate**" means that the requested amount will either be fully or partially executed with one or more (up to five) providers. The execution rate will be at the quote displayed for the selected amount (or better).

"**Fill at best**" means that the requested amount will either be fully or partially executed with one or more (up to five) providers. The execution will be with the best available rate(s).

"**Fill or Kill**" means that only the full requested amount will be executed with one provider. The execution rate will be the price displayed for this amount (or better). If full amount is not available at requested rate, the execution will **not** take place.

"**Partial Fill or Kill**" means that the requested amount will be either fully or partially executed with one provider only. The execution rate will be the quote displayed for this amount (or better).

Please note also that quotes for notional volumes below 1 million are considered as non aggregatable, therefore will only be executed with one provider. This rule does not apply for precious metals.

Depending on the selected execution type, the available amounts and quotes in the Supersonic Trader stream view might vary.

Depending on the selected execution type, the system will **not fill or may only partially execute** amounts as it will not execute, if the requested rate is not available.

### <span id="page-15-1"></span>**4 DISCRETIONAL SLIPPAGE**

When the user clicks on a quote to execute a trade in Supersonic, an order is sent to the platform to execute at the rate that the user clicked (or potentially at a better if Total or Partial Fill at Rate was configured). In fast moving markets the execution might be rejected if, by the time the quotes arrive at the client applet, the provider has already updated the rate. To increase execution probability, one can define discretional slippage.

**This functionality needs to be requested and enabled explicitly per user.** Please call our Business Client Advisory Services team or your dedicated sales person if you would like to use this function.

| 4 D <b>E</b> Total or Partial Fill at Rate ▼ Full Amount ▼   Views ▼   1   2   4   4   4   E |  |  |  |
|----------------------------------------------------------------------------------------------|--|--|--|
|----------------------------------------------------------------------------------------------|--|--|--|

<span id="page-16-2"></span>Figure 23 Board setting bar with active slippage button

To activate the slippage, the button on the board symbol bar needs to be pushed, so that

it is highlighted in green .

Two types of slippage definition are possible by currency stream: price slippage and time slippage. In periods of high volatility, the user can directly activate the application of the price and/or time slippage for each currency couple.

If slippage is activated for a currency couple, the slippage value can be set by the entry field next to the slippage button. Values are given between 0.0 and 9.9.

Slippage application can be turned off either separately for each currency couple or in general for the whole board. When the slippage function was turned off in general and is activated again, each currency couple formerly configured with slippage is automatically activated again for slippage application.

Default slippage values can be pre-defined in the Stream Board Preferences via in the symbol bar.

### <span id="page-16-0"></span>**Price Slippage**

The price slippage which is defined in pips informs the platform execution algorithm that it should try to execute with the best quote within the given price range.

The values can be configured for each stream view on the board separately. This allows different configurations for each currency couple[. Figure 24](#page-16-3) below shows 4 stream views where 3 views have price slippage activated and 1 has price slippage de-activated.

| Price slippage activated<br>with 0.4 pips | Price slippage not<br>activated           |
|-------------------------------------------|-------------------------------------------|
|                                           |                                           |
| Price slippage activated<br>with 0.2 pips | Price slippage activated<br>with 0.5 pips |

<span id="page-16-3"></span>Figure 24 Price slippage in pips by stream view

# <span id="page-16-1"></span>**Time Slippage**

The time slippage function turns an immediate-or-cancel order into a short lived resting order within the defined time frame. In case the attempted IOC order is rejected, the platform will immediately try to execute with the next provider providing the same best price. The execution attempts will last until the order is executed within the defined time.

The values can be configured for each stream view on the board separately. This allows different configurations for each currency couple[. Figure 25](#page-17-1) below shows 4 stream views where 2 views have time slippage activated and 2 which have time slippage de-activated.

|  | Time slippage activated with<br>600 ms |  | Time slippage activated with<br>500 ms     |
|--|----------------------------------------|--|--------------------------------------------|
|  | Time slippage not activated            |  | Time slippage activated with<br>one second |

<span id="page-17-1"></span>Figure 25 Time Slippage in seconds by stream view

Please bear in mind that with the Fill at Best option, if you set a high time slippage, the market can potentially run against you.

# <span id="page-17-0"></span>**5 RFS BOOST**

The RFS Boost functionality sends a request for stream (RFS) to the bank basket configured for a given currency pair and amount at company level or at user level. This configuration can

be made using the "Select Providers" button on each stream view. This may not only add better quotes to the Supersonic Trader price streams but it might also be used to get a quote when no stream is available for the relevant requested amount.

The RFS prices will only be executable in case a better rate is provided and is only with one provider. It is not available in the "Top of Book" view mode.

The RFS Boost can be activated in two different ways:

1. In the Basic View and the Narrow View, simply click the "RFS Boost" button in the middle of the pricing panel.

![](_page_17_Figure_10.jpeg)

<span id="page-17-2"></span>Figure 26 RFS Boost in Basic view

| GBP                                       | <b>USD</b> | ×    |         |  |
|-------------------------------------------|------------|------|---------|--|
| pip<br>0.6<br>0.1<br>sec<br>$\rightarrow$ |            |      |         |  |
| Sell GBP                                  | 1.52       | 1.52 | Buy GBP |  |
| <b>RFS</b>                                |            |      |         |  |
|                                           | 1,000,000  |      |         |  |

<span id="page-17-3"></span>Figure 27 RFS Boost in Narrow view

After hitting the "RFS Boost" button, a request for stream will run for 30 seconds. As long as it is active, the yellow RFS Boost bar will be displayed next to the amount and in the rates window. No action other than execution is possible then. The RFQ can't be stopped and the amount can't be modified as long as the RFQ runs.

![](_page_18_Figure_3.jpeg)

<span id="page-18-0"></span>Figure 28 RFS Boost activated with better offer RFS price

If a price received by the RFS Boost request is better than the streamed price, the rate will be displayed in yellow. If that yellow rate is hit, an RFS trade is executed.

![](_page_18_Picture_6.jpeg)

Figure 29 Best rate available using RFS Boost

<span id="page-18-1"></span>2. The RFS Boost functionality can be included in the VWAP view:

RFS Boost can be permanently added to any given stream(s) by right-clicking onto the desired amount and then selecting "RFS Boost Mode" from the drop-down menu.

![](_page_18_Figure_10.jpeg)

<span id="page-18-2"></span>Figure 30 Activating RFS Boost mode in VWAP view

After activation of the RFS Boost mode, "RFS" is marked in yellow next to the amount. The RFS Boost is now fixed for the chosen tier until disabled by the user.

| $0.1 - p 0.6 - s$<br>目<br>a<br><b>USD</b><br><b>EUR</b><br>$\mathbf{v} \cup \mathbf{x}$<br>$\circledast$ |             |      |                                                 |                      |
|----------------------------------------------------------------------------------------------------------|-------------|------|-------------------------------------------------|----------------------|
| <b>BID</b><br><b>Sell EUR</b><br>$\overline{\mathbf{1}}$<br>m<br>1 <sub>m</sub>                          | 1.31<br>11з | 1.31 | <b>Buy EUR</b><br><u> 112</u><br>1 <sub>m</sub> | <b>ASK</b><br>4<br>m |
| $1.31$ 112<br>3m                                                                                         | [0.0]       |      | $1.31$ 112<br>Зm                                |                      |
| $131$ 111<br><b>RFS</b><br>5m                                                                            | 0.2         |      | $1.31$ 113<br>l5m                               | <b>RFS</b>           |
| $1.31$ 110<br>7m                                                                                         | 0.5         |      | $1.31$ 115<br>7m                                |                      |
| $1.31$ 109<br>10 <sub>m</sub>                                                                            | 0.7         |      | $1.31$ 116<br>10m                               |                      |
| RFS $1.31$ 108<br>15m                                                                                    | 1.0         |      | $1.31$ 118<br>15m                               | <b>RFS</b>           |

<span id="page-19-2"></span>Figure 31 RFS Boost mode activated for 5m and for 15m EUR/USD

The first click on this button will activate the RFS Boost. The RFS Boost will then run for 30 seconds and work as described above.

![](_page_19_Picture_5.jpeg)

Figure 32 RFS Boost mode running for 5m EUR/USD in VWAP view

<span id="page-19-3"></span>Execution thereafter is done again by double-clicking (or single click if so configured) on the respective quote button.

Whether a deal has been executed in streaming or RFS Boost mode can also be seen in the Executed Streams area. The suffix of the Execution ID in the table either contains an S (= stream, for example SO-10042173-**S**1)) or a R (= RFS Boost, for example SO-10042174-**R**1).

# <span id="page-19-0"></span>**6 SUPERSONIC TRADER RESTING ORDERS**

#### <span id="page-19-1"></span>**Resting Orders Definition**

In addition to execute by directly clicking on the streamed price (technically speaking an immediate or cancel (IOC) order), it is possible to place resting limit orders (limit, stop, OCO or market orders) against streams. Resting orders can be placed in two ways: the live placing method by defining an order explicitly and placing it; and/or the fast order placing method, based on previously defined order template. The fast orders are described in detail in the next section of this chapter. The TWAP Taking Algo Order is described in detail in the last section 6.6 further below in this chapter.

Each of the four corner buttons in the "Top of Book" view are available to be configured for orders.

'BID' on the left side is the shortcut to create a buy order; 'ASK' on the right side is the short cut to create a sell order; 'LO' opens a limit order definition, 'SO' a stop order. 'OCO' is a onecancels-the-other order. 'IOC' means 'Immediate or Cancel' which can be used to configure the buttons for immediate trade execution. In addition, the short cuts can be used to define the default amount for the order. For all non-IOC orders, an order definition screen will always open prior to placing the order.

In case only the "Basic view" is enabled, resting orders can be still be created, but obviously not using the four corner buttons of the "Top of Book" view. The resting order definition screen can be opened via a right mouse click the price button. See below.

![](_page_20_Picture_6.jpeg)

Figure 33 Resting Order definition Buttons in the Top of Book view

<span id="page-20-0"></span>Double-clicking on one of the predefined order buttons or right-mouse clicking on the average price in the Basic view opens the order definition screen where limit type, limit rate (in case of limit/stop orders) and notional amount can be defined.

| <b>331 Order Definition</b> |                                         |  |  |  |
|-----------------------------|-----------------------------------------|--|--|--|
| 1 <sub>m</sub>              | <b>EUR/USD</b> $\div$ Limit Order<br>2m |  |  |  |
| 5 <sub>m</sub>              | 1.30180<br>10 <sub>m</sub>              |  |  |  |
| Buy                         | $1,000,000,00$ $\sim$<br><b>EUR</b>     |  |  |  |
| >>                          | Place<br>Close                          |  |  |  |

<span id="page-20-1"></span>Figure 34 Order definition

The order type field allows selecting between a limit, a stop (triggered on the BID or on the OFFER), a market order or a one-cancels-the-other (OCO) order.

A **limit order** is an order to buy at not more or to sell at not less than the specified limit rate. The order will be monitored against the best stream of the selected providers and will be triggered and executed when the best rate reaches the defined limit. The executed rate might be better than the defined limit.

A **stop order** is an order to buy or sell once the price has climbed above or dropped below the specified stop rate. **When the specified stop rate is reached, the stop order turns into a market order (no rate defined)**. The executed rate can therefore be worse or better than the specified stop rate if the order is placed in a moving market.

You have to choose whether you would like the stop order to be triggered on the BID or on the OFFER side.

|                | <b>331 Order Definition</b> |                                                    |                 |
|----------------|-----------------------------|----------------------------------------------------|-----------------|
| 1 <sub>m</sub> | GBP/USD<br>Stop Order       |                                                    | 2m              |
| 5 <sub>m</sub> |                             | 1.61890                                            | 10 <sub>m</sub> |
| Sell           | $1,000,000.00$ $\sim$       |                                                    |                 |
|                | <b>Stop Loss Settings</b>   |                                                    |                 |
|                | Stop Loss Triggering Side:  | Trigger side is not specified $\blacktriangledown$ |                 |
|                |                             | Trigger side is not specified                      |                 |
| >>             |                             | Trigger on BID<br>Trigger on OFFER                 | llose           |

<span id="page-21-0"></span>Figure 35 Stop Order triggering definition

An **OCO order** is a combination of a limit and a stop order for the defined action and notional amount. The first executed order automatically cancels the other order. Partial fill will reduce the opposite order accordingly.

|  |  | Rate of the limit order |
|--|--|-------------------------|
|  |  |                         |
|  |  |                         |
|  |  | Rate of the stop order  |
|  |  |                         |
|  |  |                         |
|  |  |                         |
|  |  |                         |
|  |  |                         |

<span id="page-21-1"></span>Figure 36 OCO Order definition

Define the limit order rate first, then in the Stop Loss Settings area, the stop rate. Similar to the simple stop order, you must select whether the stop order should be triggered on the offer or the bid price.

A **market order** is an order placed against the streams for immediate execution at best, i.e. the next available rate.

The default definition of an order is that it can be split amongst several stream providers and be filled partially. This default setting can be modified by clicking to the arrow >> buttons at the bottom left of the order definition screen.

Please be aware that if partial fill is not allowed, in case the order amount is higher than the available liquidity for the order limit, the order will **not** be executed and will be displayed with a negative distance to the best market rate in the Resting Order View.

The standard expiry is Good till cancelled.

**Please note that with 360T's standard maintenance windows, all pending orders will be deactivated system-side if you are still logged in Supersonic at that time. Deactivation will also take place automatically when the user logs out or when there is a connection issue between the user applet and the 360T servers.**

See further details below in the Section [Deactivating and Reactivating resting stream orders.](#page-27-0)

| <b>880 Order Definition</b> |                                               |              |                 |  |
|-----------------------------|-----------------------------------------------|--------------|-----------------|--|
| 1 <sub>m</sub>              | $EUR/USD \equiv$ Limit Order                  |              | 2m              |  |
| 5 <sub>m</sub>              |                                               | 1.30205      | 10 <sub>m</sub> |  |
| Sell                        |                                               | 1,000,000.00 | <b>EUR</b>      |  |
| Expiry:                     |                                               | <b>GTC</b>   |                 |  |
|                             | $\sqrt{}$ splitting $\sqrt{}$ partial filling |              |                 |  |
| <<                          |                                               | Place        | Close           |  |

<span id="page-22-0"></span>Figure 37 Order definition details

When the defined limit rate is already in the market, it will be displayed in red to notify the user that the order would be executed immediately if this order is placed.

Depending on user settings, it is possible to prevent the user from placing an order when the rate is already in the market. Please contact 360T Business Support if you would like this setting feature to be enabled. Fast orders (see next section) by default cannot be placed in the market, even if the user setting basically permit this.

Once placed, orders can be viewed under the Active tab of the Blotter view, or, if selected, in the Resting Streams Orders views. The resting order stream view displays the resting orders for the selected trade-as entity only.

![](_page_22_Figure_7.jpeg)

<span id="page-22-1"></span>Figure 38 Select Resting Stream Orders view

<span id="page-22-2"></span>![](_page_22_Picture_9.jpeg)

Figure 39 Resting Stream Orders view

If Resting Stream Orders is not selected but orders have been placed as the selected tradeas entity, the shopping cart symbol followed by the number of active orders will be displayed in the stream view symbol bar as well as in the Top of Book currency pair stream view.

Clicking on the shopping cart symbol in the symbol bar will automatically open the Resting Stream Orders view to allow next steps.

### <span id="page-23-0"></span>**Fast Orders**

The Fast Orders functionality allows high speed placing of resting orders based on templates created beforehand.

The view can be selected by ticking the option in Views.

![](_page_23_Figure_7.jpeg)

<span id="page-23-1"></span>Figure 40 Select Fast Order View

![](_page_23_Figure_9.jpeg)

<span id="page-23-2"></span>Figure 41 Fast Order View and Resting Orders View

#### **Select limit type and notional**

Standard order type and notional can be preconfigured by clicking on the blue bar in the middle. With a left mouse click, the order details can be defined as described in the previous section of this chapter. With the right mouse click, you can chose orders definitions that were made previously or a different standard notional.

| <b>USD</b><br><b>GBP</b><br>×<br>$\circ$<br>$\overline{\mathbf{v}}$ | $\bullet$ 0.1 $\div$<br>a<br>旧                                                                          |
|---------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------|
| LO 25m GBP / (<br>1.61877<br>Ccy<br>Rate<br>Type<br>Amt             | SO-BID 5m GBP / GTC<br>LO 1m GBP / GTC<br>Ďе<br>LO 15m GBP / GTC<br>LO 5m GBP / GTC<br>LO 10m GBP / GTC |
| 1.61877<br>1.6<br>Rate<br>Amt<br>Type<br>Ccy                        | 500k<br>1 <sub>m</sub><br>2m<br>3m<br>De<br>4m                                                          |
|                                                                     | 5m<br>10 <sub>m</sub><br>15m<br>Other amount                                                            |
|                                                                     | Show amount field<br>v                                                                                  |

<span id="page-24-0"></span>Figure 42 Select previously defined order

If you don't want the amount field to be displayed under the blue button, please untick "Show amount field" in the menu.

#### **Select the ladder type for price steps**

![](_page_24_Figure_6.jpeg)

<span id="page-24-1"></span>Figure 43 Define the incremental precision by right mouse click

When you click with your mouse right click the position of the cursor in the limit rate field, the system will move the cursor to the selected position and allows the display from 1/10, 1, 10 or 100 pips steps in the price selection ladder.

#### **Place Fast Order**

The execution buttons displaying the current market rate can be used to select the requested limit rate: a left mouse click on the button will display a price ladder incremented by the pip position defined previously (max 10th of pips) with the current best price in red. By clicking on the limit rate of your choice, the defined order at that rate will immediately be placed.

| 原 1<br><b>USD</b><br>$\bullet$<br>GBP<br>×<br>$\circledcirc$<br>$\overline{\phantom{a}}$<br>▼∣( | $\theta$<br>$\blacksquare$ 0.1 $\rightarrow$ p $\blacksquare$ 0.6 $\rightarrow$ s<br>थ |
|-------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------|
| <b>BID</b><br>1.50<br>1.50<br>Sell GBP                                                          | <b>ASK</b><br>Buy GBP                                                                  |
| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>5.5<br>m<br>1 <sub>m</sub>                          | $\overline{7}$<br>m<br>原 1<br>1 <sub>m</sub>                                           |
| 4 <sub>m</sub><br>3m<br>2m<br>2m<br>5 <sub>m</sub>                                              | 3 <sub>m</sub><br>4 <sub>m</sub><br>5 <sub>m</sub>                                     |
| LO 15m GBP / GTC<br>1.50997<br>$15,000,000.00$ $\rightarrow$                                    | 1.50991                                                                                |
| Ccy<br>Rate<br>Rate<br>Type<br>Amt<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$      |                                                                                        |
|                                                                                                 | 1.51001<br>≜                                                                           |
|                                                                                                 | 1.50991<br>$\overline{\phantom{a}}$                                                    |
| $1.50$ $997$<br>1.50991<br>1.50018                                                              | 1.50981<br>$\overline{\phantom{a}}$                                                    |
|                                                                                                 | 1.50971                                                                                |
| Type<br>Amt<br>Ccy<br>Rate<br>Rate<br>▼                                                         | ⋥<br>1.50961                                                                           |
|                                                                                                 | 1.50951                                                                                |
|                                                                                                 | 1.50941                                                                                |
|                                                                                                 | 1.50931                                                                                |
|                                                                                                 | 1.50921                                                                                |
|                                                                                                 | 1.50911                                                                                |
|                                                                                                 | 1.50901                                                                                |
|                                                                                                 | 1.50891                                                                                |
|                                                                                                 | 1.50881                                                                                |

<span id="page-25-1"></span>Figure 44 Incremental Price Ladder

You can also use the arrow buttons next to the rate to increment the limit price. The price button will then be displayed in green, so that you can directly click on the button to place the order at the displayed rate.

![](_page_25_Figure_5.jpeg)

<span id="page-25-2"></span>Figure 45 Increment limit price by arrow buttons

#### **Please mind your selection as the order placing is immediate!**

#### **Press Esc button or click in inactive area of your workspace to exit or close the price ladder.**

#### <span id="page-25-0"></span>**Amending resting stream orders**

Placed resting stream order can be amended at any time by reopening the stream order definition for a trade-as entity.

There are two ways to re-open the definition of a resting stream order. In case the Resting Stream Order view is open, you can simply double-click on the order which you would like to amend. Else you can go to the Active blotter, select the order and select the command "Initialize amend" over the right-mouse click context menu.

| Figure 46 Select order to amend |              |                                                 |  | Double-click on order to<br>open its definition… |  |  |  |
|---------------------------------|--------------|-------------------------------------------------|--|--------------------------------------------------|--|--|--|
|                                 | context menu | … or alternatively use<br>the right mouse-click |  |                                                  |  |  |  |
|                                 |              |                                                 |  |                                                  |  |  |  |
|                                 |              |                                                 |  |                                                  |  |  |  |
|                                 |              |                                                 |  |                                                  |  |  |  |
|                                 |              |                                                 |  |                                                  |  |  |  |

<span id="page-26-1"></span><span id="page-26-0"></span>Figure 47 Order definition

The fields of the order can immediately be amended. As soon as one of the original values was modified, the Amend Button will be active in order to save the changed parameters and place the amended order. As long as this has not happened, the original order will still be in the market and could be executed anytime. Therefore, in case the order is already very close to be executed, it is advised to deactivate the order first, before placing a new amended order.

Closing the order definition will cancel the order amendment action.

|                | <b>331 Order Definition</b>             |  |
|----------------|-----------------------------------------|--|
| 1 <sub>m</sub> | <b>EUR/GBP</b> $\neq$ Limit Order<br>2m |  |
| 5 <sub>m</sub> | 0.803 00 <br>10 <sub>m</sub>            |  |
| Buy            | 1,000,000.00<br><b>EUR</b>              |  |
| >>             | Close<br>Amend<br>Deactivate            |  |

<span id="page-27-2"></span>Figure 48 Amend Resting Order

# <span id="page-27-0"></span>**Deactivating and Reactivating resting stream orders**

Placed resting orders can be de-activated at any time. Deactivating an order corresponds to withdrawing (cancelling) it, but gives the opportunity to re-place (activate) the order quickly without having to enter all details anew.

In Supersonic Trader, resting orders can be deactivated in two ways: by manual deactivation and by automatic or system-controlled deactivation.

#### <span id="page-27-1"></span>**User defined deactivation / reactivation**

The user can deactivate resting orders using different methods.

#### **Fast withdraw of all orders**

The symbol on the top right under the main menu of your Supersonic Trader application allows to deactivate all placed orders.

![](_page_27_Picture_11.jpeg)

<span id="page-27-3"></span>Figure 49 Deactivate all active resting orders button

In the Active Orders tab by using the right mouse click context menu, the command "Deactivate all resting orders" will withdraw all existing orders in all currency pairs.

| <b>Fi</b> Expand All                    | *      |
|-----------------------------------------|--------|
| <b>F</b> Collapse All                   |        |
| Print Table Content                     | Ctrl-P |
| open ⊃                                  | Ctrl-O |
| $\bullet$ Deactivate                    | Ctrl-D |
| Initialize Amend                        | Ctrl-T |
| Withdraw                                | Ctrl-W |
| $\bullet$ Deactivate all Resting Orders | Ctrl-R |

<span id="page-27-4"></span>Figure 50 Deactivate all orders

#### **Withdrawing all orders in one currency pair**

By clicking on the symbol at the top right of a currency pair stream view, all orders active in the relevant currency pair will be deactivated. You will be asked to confirm the withdrawal of the currency pair relevant orders.

#### **Withdrawing one specific order**

To disable one specific order, there are two methods. In case the Resting Stream order view is open, a double-click on the order will open the order definition. It is also possible to use the right mouse-click context menu in the Active orders tab and either select "Deactivate" (in case the order should be replaced later) or "Withdraw" to cancel it completely. It is also possible to double-click on the order row in the Active orders tab and select then the "Deactivate" button.

|                | <b>331 Order Definition</b>             |
|----------------|-----------------------------------------|
| 1 <sub>m</sub> | <b>EUR/GBP</b> $\neq$ Limit Order<br>2m |
| 5 <sub>m</sub> | 0.80350<br>10 <sub>m</sub>              |
| Buy            | 1,000,000.00<br>EUR.                    |
| >>             | Close<br>Deactivate<br>Amend            |

<span id="page-28-1"></span>Figure 51 Action on Resting Order

#### **Reactivating deactivated orders**

Deactivated orders will not be completely cancelled but saved the "Passive" orders tab.

By selecting "Activate" via the right-mouse click context menu or the Activate button in the order definition, the order will be held and monitored against streams again.

#### <span id="page-28-0"></span>**System-controlled deactivation / reactivation**

In case the user logs out during the day or gets disconnected from the system, **all his pending orders will be deactivated**. When the connection is re-established again or when the user logs in again on the same day, a confirmation screen with the deactivated orders will be presented to the user. These orders can then be placed again if required.

| Login                                        |             |                  |          |              |                                     |                |         | $\overline{\mathsf{x}}$ |
|----------------------------------------------|-------------|------------------|----------|--------------|-------------------------------------|----------------|---------|-------------------------|
| Order Type                                   | Order ID    | Requester Action | Currency |              | Notional Amount   Notional Currency | Execution Rate | %       |                         |
| $\mathcal{M}'$ Activatable Orders (2)<br>IE- |             |                  |          |              |                                     |                |         |                         |
| $\div$ Limit Order                           | SO-10133497 | Buy              | EUR/USD  | 1,000,000.00 | <b>EUR</b>                          | 1.37950        | 0.47544 |                         |
| ÷ Limit Order                                | 50-10133092 | Buy              | GBP/USD  | 1,000,000.00 | GBP                                 | 1.59000        | 0.20712 |                         |
|                                              |             |                  |          |              |                                     |                |         |                         |
|                                              |             |                  |          |              |                                     |                |         |                         |
|                                              |             |                  |          |              |                                     |                | Done    |                         |

<span id="page-28-2"></span>Figure 52 Reactivation of resting orders

By selecting "Activate" via the right mouse-click context menu while the top node line is selected, all orders below that node will be placed again. If only specific orders should be placed, the cursor can be placed on the respective order before selecting "Activate" over the right mouse-click context menu.

Alternatively, all orders active at the time of the logout will be available under the Passive tab of the Streams Deal Blotter and can be activated individually via right-mouse button.

In case of technical connection issues of the user's trading application with the 360T servers, resting orders will automatically be deactivated.

Resting Orders which were placed with expiry GTC ("Good till cancelled") will automatically be deactivated during 360T's maintenance windows. The maintenance windows are as follows:

- **Always** during week ends.

- **If needed**, 360T avails itself a maintenance window **daily at 5 pm EST (New York, America)** to resolve urgent issues which cannot wait for the week end. In case such a maintenance window has to be used, GTC resting orders would be deactivated automatically if the user was still logged in Supersonic.

An alert e-mail will be automatically sent to the user when pending resting orders were deactivated system- side.

**360T strongly advises the Supersonic user to not let the application open with active resting orders if the user is not present at his desk.**

# <span id="page-29-0"></span>**Uploading Resting Orders**

Supersonic Trader allows uploading resting orders via a comma-separated-value (CSV) file. To upload a file, the menu entry *Import* has to be clicked. [Figure 53](#page-29-1) shows the menu entry. Resting Stream Order rights need to be setup for the user.

Resting Orders will by default be uploaded as Passive Orders. It is however possible to include the status in the upload record, which will then automatically place the order against the respective currency stream.

| Import                                |  |
|---------------------------------------|--|
| Import Resting Order from File Strg-I |  |

<span id="page-29-1"></span>Figure 53: Import resting orders

The following resting order types can be uploaded:

- Limit Orders
- Market Orders
- Stop Orders
- OCO Orders

The CSV file has to include the following parameters in the given order:

| Field            | Possible values /format                                    | Example    |  |
|------------------|------------------------------------------------------------|------------|--|
| Order Type       | Market, Limit, StopOnBid,<br>StopOnAsk, OCO                |            |  |
| Currency1        | Currency ISO code                                          | EUR        |  |
| Currency2        | Currency ISO code                                          | USD        |  |
| ActionType       | Buy, Sell                                                  | Sell       |  |
| notionalCurrency | Currency ISO code                                          | EUR        |  |
| notionalAmount   | decimal                                                    | 1,000,000  |  |
| limitRate        | decimal                                                    | 1.29122    |  |
| expiryDate       | dd.MM.yyyy - if empty date is<br>set to end of trading day | 05.01.2012 |  |
| expiryTime       | hh:mm<br>15:00                                             |            |  |
| splittingAllowed | TRUE, FALSE                                                | TRUE       |  |

| partialFillingAllowed      | TRUE, FALSE                                      | FALSE   |
|----------------------------|--------------------------------------------------|---------|
| Status                     | Active, Passive – if empty<br>"Passive" is taken | Active  |
| Stop Loss Limit Rate (OCO) | decimal                                          | 1.30555 |
| Trigger Side (OCO)         | BID, OFFER                                       | BID     |

Table 1: Fields and values for resting order upload

The following lines show an example of a csv file with all resting order types includes. It also includes a header which can be send in the csv file.

```
Order Type,Currency 1,Currency 2,Action,Notional Currency,Amount,Limit Rate,Expiration 
Date,Expiration Time,Splitting,Partial Filling,Status,Stop Loss Limit Rate,Trigger Side
Limit,EUR,USD,Buy,EUR,1000000,1.30134,,,TRUE,FALSE,Active,,
Limit,EUR,USD,Sell,EUR,1000000,1.34123,08.09.2015,,FALSE,TRUE,Passive,,
Mrkt,EUR,USD,Sell,EUR,1000000,1.34123,08.09.2015,,FALSE,TRUE,Pssive,,
Market,EUR,USD,Buy,USD,1000000,,08.09.2015,14:00,TRUE,TRUE,,,
StopOnBid,EUR,USD,Sell,USD,1000000,1.34123,11.09.2015,14:00,,,,,
StopOnBid,EUR,USD,Sell,USD,1000000,1.34123,20.12.2015,,,,,,
StopOnBid,EUR,USD,Sell,USD,1000000,1.34123,07.08.2015,10:00,,,,,
Limit,EUR,USD,Sell,USD,1000000,,11.08.2015,14:00,,,,,
Limit,EUR,EUR,Buy,EUR,1000000,1.30134,,,TRUE,FALSE,Active,,
Limit,EUR,USD,Buy,CHF,1000000,1.30134,,,TRUE,FALSE,Active,,
OCO,EUR,USD,Buy,EUR,1000000,1.30134,,,TRUE,FALSE,Passive,1.30334,BID
OCO,EUR,USD,Buy,EUR,1000000,1.30134,,,TRUE,FALSE,Passive,1.30334,OFFER
OCO,EUR,USD,Buy,EUR,1000000,1.30134,,,TRUE,FALSE,Passive,,BID
Limit,EUR,USD,Buy,EUR,1000000,1.30134,,,TRUE,FALSE,Passive,,OFFER
Limit,EUR,USD,Buy,EUR,-1000000,1.30134,,,TRUE,FALSE,Active,,OFFER
Limit,EUR,USD,Sell,EUR,1000000,1.34123,,,FALSE,TRUE,Passive
```

If the uploaded file contains any errors, no orders are uploaded. Only a valid file will be uploaded. A respective message is shown, if the upload was successful. Otherwise the error message points to the faulty line / lines in the file.

### <span id="page-30-0"></span>**TWAP Algorithm**

Supersonic Trader allows, on demand, to execute trades using a Time Weighted Average Price (TWAP) algorithm.

The **TWAP Taking** algorithm is designed to offload a larger order over time while minimizing market impact and preventing information leakage. Slices of randomized size will be sent to your streaming liquidity providers at randomized intervals in order to soften the market effect of a larger order. The slices can also be spread across multiple banks for further information dispersion. The net effect is that the trader receives a "Time Weighted Average Price" for his execution by using the TWAP algorithm.

The **TWAP Taking** algorithm does not place any resting orders with the liquidity provider, but only hits bids or pays offers which are already published in Supersonic streaming quotes.

#### <span id="page-30-1"></span>**Order definition**

The basic criteria to define a TWAP Taking algorithm are as shown in the figure below.

|                                  | <b>BST</b> Order Definition - EUR/USD |                      | $\mathbf{x}$    |
|----------------------------------|---------------------------------------|----------------------|-----------------|
| 1 <sub>m</sub>                   | EUR/USD   / TWAP Order                |                      | 2m              |
| 5 <sub>m</sub>                   |                                       | 1.09250              | 10 <sub>m</sub> |
| Sell<br>$\overline{\phantom{a}}$ |                                       | $100,000,000$ $\tau$ | $EUR -$         |
|                                  | <b>Slice Settings</b>                 |                      |                 |
|                                  | Avg. Quantity:                        | 5000000              |                 |
|                                  | Avg. Frequency (sec):                 | $30 -$               |                 |
|                                  | <b>Other Settings</b>                 |                      |                 |
|                                  | Algo Stopped Alert (min):             | $5 -$                |                 |
| $\rightarrow$                    |                                       | Place<br>Close       |                 |

<span id="page-31-2"></span>Figure 54 TWAP Order definition

The rate field in the TWAP order definition represents the Overall Limit Price of the TWAP Taking algorithm.

In case the desired Overall Limit Price is displayed in red, it is in the market and the order would be executed immediately, unless the user's settings prevent this.

| Parameter           | Units    | Required | Description                                            |
|---------------------|----------|----------|--------------------------------------------------------|
| Overall Limit Price | Rate     | Yes      | The worst price that the algorithm will be allowed to  |
|                     |          |          | execute (with the exception of Would Limit Rate        |
|                     |          |          | described below). This price is not disclosed on       |
|                     |          |          | individual slices, which will have the price of the    |
|                     |          |          | quote which is being acted upon.                       |
| Total Quantity      | Base or  | Yes      | Total amount to be executed with the TWAP              |
|                     | Quote    |          | algorithm                                              |
|                     | currency |          |                                                        |
| Slice Settings      |          |          |                                                        |
| Average Quantity    | Same as  | Yes      | Indicates the size of the slices which will be sent by |
|                     | total    |          | the time slice. If the slice sizes are randomized then |
|                     | quantity |          | this is the average slice size.                        |
| Average Frequency   | Seconds  | Yes      | Indicates the average time which should separate       |
|                     |          |          | slices sent to the market. If randomization is used    |
|                     |          |          | then this is the average target frequency. If no       |
|                     |          |          | randomization is used then this is the frequency       |
| Other Settings      |          |          |                                                        |
| Algo Stopped Alert  | Minutes  | Yes      | Alert if the market is outside the tradable range for  |
| (Threshold)         |          |          | more than the defined number of minutes. This          |
|                     |          |          | occurs if the time slice has stalled due to market     |
|                     |          |          | prices being above the limit rate for an extended      |
|                     |          |          | period.                                                |

#### <span id="page-31-0"></span>**Minimum Parameters**

The default expiry of the TWAP Taking Algo order is set to GTC, similar to all Supersonic resting orders. Alternatively, they can be set to expire at a user defined time or at the end of the trading day. The same automatic deactivation rules are applied as for other Supersonic resting orders.

#### <span id="page-31-1"></span>**Extended Parameters**

In order to make the TWAP Algorithm more effective and more difficult to be detected, the "Slice Randomized Settings" and further parameters can be specified.

They can be defined after the button on the bottom left of the TWAP Order definition.

|                                                         | <b>SST</b> Order Definition - EUR/USD                                                                                                                                                                                                                                                                                      |            |                      | $\mathbf{x}$    |  |  |
|---------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|----------------------|-----------------|--|--|
| 1 <sub>m</sub>                                          | EUR/USD   / TWAP Order                                                                                                                                                                                                                                                                                                     |            |                      | 2m              |  |  |
| 5 <sub>m</sub>                                          |                                                                                                                                                                                                                                                                                                                            |            | 1.09250              | 10 <sub>m</sub> |  |  |
| $Sell \triangleright$                                   |                                                                                                                                                                                                                                                                                                                            |            | $100,000,000$ $\sim$ | <b>EUR</b>      |  |  |
|                                                         | <b>Slice Settings</b>                                                                                                                                                                                                                                                                                                      |            |                      |                 |  |  |
|                                                         | Avg. Quantity:                                                                                                                                                                                                                                                                                                             |            | 5000000              |                 |  |  |
|                                                         | Avg. Frequency (sec):                                                                                                                                                                                                                                                                                                      |            | 30 <sup>1</sup>      |                 |  |  |
|                                                         | <b>Other Settings</b>                                                                                                                                                                                                                                                                                                      |            |                      |                 |  |  |
|                                                         | Algo Stopped Alert (min):                                                                                                                                                                                                                                                                                                  |            | 5 ÷                  |                 |  |  |
| Expiry:                                                 |                                                                                                                                                                                                                                                                                                                            | <b>GTC</b> |                      |                 |  |  |
|                                                         | <b>Slice Randomized Settings</b><br>$1000000 -$<br>7500000<br>Max Quantity:<br>Min Quantity:<br>$15 -$<br>120<br>Min Frequency (sec):<br>Max Frequency (sec):<br>$0 -$<br>Provider Exclusion Min I<br>Provider Exclusion Max I<br>$\mathbf{0}$<br><b>Blacklisting Settings</b><br>Max Rejections Per Provider:<br>4<br>600 |            |                      |                 |  |  |
|                                                         | Rejections Reset Interval (sec):<br><b>Other Settings</b>                                                                                                                                                                                                                                                                  |            |                      |                 |  |  |
| 100 <sub>1</sub><br>Max Available Liquidity Percentage: |                                                                                                                                                                                                                                                                                                                            |            |                      |                 |  |  |
| Min Required Quoting Banks:                             |                                                                                                                                                                                                                                                                                                                            |            |                      |                 |  |  |
| Max Spread:                                             |                                                                                                                                                                                                                                                                                                                            |            |                      |                 |  |  |
| <b>Would Limit Rate:</b>                                |                                                                                                                                                                                                                                                                                                                            |            |                      | Ō               |  |  |
|                                                         | Round slices to lots of:                                                                                                                                                                                                                                                                                                   |            |                      | $1,000 -$       |  |  |
| 44                                                      |                                                                                                                                                                                                                                                                                                                            | Place      | Close                |                 |  |  |

<span id="page-32-0"></span>Figure 55 TWAP additional execution parameters

| Parameter                              | Units                         | Required | Description                                                                                                                                                                                                                                                       |
|----------------------------------------|-------------------------------|----------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Slice Randomized Settings              |                               |          |                                                                                                                                                                                                                                                                   |
| Min/Max Quantity                       | Units of<br>Total<br>Quantity | No       | Indicates the range of the slices around the Slice<br>Average Quantity. Both a min and max must be<br>specified. If the min and max are the same or blank                                                                                                         |
|                                        | Currency                      |          | then no randomization will occur.                                                                                                                                                                                                                                 |
| Min/Max Frequency                      | Seconds                       | No       | Indicates the range of durations allowed between<br>slices around the Slice Average Frequency. Both a<br>min and max must be specified. If the min and max<br>are the same or blank then no randomization will<br>occur.                                          |
| Provider Exclusion<br>Min/Max Interval | Seconds                       | No       | If specified, requires rotation of time slice orders<br>amongst banks. After a slice is executed with a<br>particular bank that bank is excluded from<br>subsequent deals (regardless of price) for a random<br>time between the min and max specified intervals. |

| Parameter                | Units    | Required | Description                                        |  |
|--------------------------|----------|----------|----------------------------------------------------|--|
| Blacklisting Settings    |          |          |                                                    |  |
| Max Rejections Allowed   | Positive | No       | The number of rejections allowed per bank before   |  |
| per Provider             | integer  |          | the algorithm places the bank on a "blacklist".    |  |
| Rejection Reset Interval | Seconds  | No       | Specifies how long before the algorithm's "memory" |  |
|                          |          |          | of prior bank rejections is reset and banks which  |  |
|                          |          |          | previously rejected can be retried.                |  |

| Parameter                     | Units                                     | Required | Description                                                                                                                                                                                                                                          |  |
|-------------------------------|-------------------------------------------|----------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| Other Settings                |                                           |          |                                                                                                                                                                                                                                                      |  |
| Max Available Liquidity       | Percent                                   | No       | Maximum percentage of the available liquidity to use.<br>For instance if the total of all offer volume is 20<br>million, a 10% value would mean 2 million would be<br>the maximum slice size.                                                        |  |
|                               |                                           |          | (If Top of Book Only is specified, then the lesser of<br>the Max % of Available Liquidity and the Top of Book<br>size will be sent on slices.)                                                                                                       |  |
|                               |                                           |          | This value overrides and constrains the Slice<br>Average Quantity.                                                                                                                                                                                   |  |
| Min Required Quoting<br>Banks | Positive<br>Integer                       | Yes      | Specifies the minimum number of banks required to<br>be quoting for the algorithm to run. If an insufficient<br>number is present then the algorithm waits. An alert<br>may be generated if it is dormant too long.                                  |  |
| Max Spread                    | Pips with<br>1dp                          | No       | Specifies the maximum spread the algorithm will<br>accommodate. The algorithm will not deal further into<br>the book than allowed by the max spread. The<br>spread is calculated by the current price versus the<br>best price on the opposing side. |  |
| Would Limit Rate              | Quote                                     | No       | This is a price at which the customer would be willing<br>to execute the entire remaining balance of the time<br>slice and end the algorithm. This can be worse than<br>the Overall Limit Price.                                                     |  |
| Round Slices to Lots of       | Units of<br>Total<br>Quantity<br>Currency | No       | Specifies the quantity to which slices should be<br>rounded. Note that this amount should be smaller or<br>equal to the minimum quantity if defined.                                                                                                 |  |

# <span id="page-34-0"></span>**7 EXECUTION REPORTS**

Executed trades are shown on the right side in the Executions report area. In case you would like to gain space to show more streams on your screen, it is also possible to hide the execution report area on the right and display the execution reports as animated notifications appearing on the right side of your screen. See more details at the end of this chapter.

![](_page_34_Figure_4.jpeg)

<span id="page-34-1"></span>Figure 56 Supersonic Trader screen including execution reports

The execution reports show details about the trade or order:

In the header of the report:

- Currency pair and action for each currency
- Market or Limit execution for a given notional
- The currently already filled amount with the average rate at which the amount was filled.

In the summary, a progress bar with the ratio of the filled amount in green as well as the current fill amount and state is displayed. In the screenshot below, the total 4million USD was executed successfully.

![](_page_35_Picture_2.jpeg)

Figure 57 Execution summary

<span id="page-35-0"></span>To get more details on the execution, click on "Display Execution Details". If the trade was split to several providers, the detail will be visible also by displaying the execution details.

| Buy USD / Sell JPY<br>4m USD @ Limit 79.309<br>4m Exec @ 79.309<br>80-11278218 |         |      |                    |  |  |  |
|--------------------------------------------------------------------------------|---------|------|--------------------|--|--|--|
| <b>Execution Summary</b>                                                       |         |      |                    |  |  |  |
| DONE<br>$4m$ / $4m$                                                            |         |      |                    |  |  |  |
| ↓ Display Execution Details<br><b>Execution Details</b>                        |         |      |                    |  |  |  |
|                                                                                |         |      |                    |  |  |  |
| Status                                                                         | Amount. | Rate | Provider           |  |  |  |
| <b>EXECUTED</b>                                                                | 1m      |      | 79.309 LLOYDS.DEMO |  |  |  |
| <b>EXECUTED</b>                                                                | 1m      |      | 79.309 RBS.LND     |  |  |  |
| <b>EXECUTED</b>                                                                | 1m      |      | 79.309 DB.DEMO     |  |  |  |

<span id="page-35-1"></span>Figure 58 Execution details

The following statuses can be displayed in the summary line:

**DONE** The trade is finalized and the total amount is executed. In the dark area, the detail of the execution is shown, including the average rate over the total amount and the Stream Order ID ("SO-reference").

| Buy USD / Sell JPY                                       |      |  |
|----------------------------------------------------------|------|--|
| 4m USD @ Limit 79.309<br>4m Exec @ 79.309<br>SO-11278218 |      |  |
| -Execution Summary                                       |      |  |
| $4m$ / $4m$                                              | DONE |  |
| <b>Display Execution Details</b>                         |      |  |

<span id="page-35-2"></span>Figure 59 Trade totally executed

**PARTIALLY DONE** The trade is finalized but a part of it could not be executed, which is visualized by a status bar partly colored in red. The execution details will provide details on the failures.

![](_page_36_Picture_2.jpeg)

Figure 60 Trade partially executed

<span id="page-36-0"></span>**NOT DONE** the trade execution was attempted but failed. The execution report additionally shows the failure comment. The report ticket is market in red.

![](_page_36_Picture_5.jpeg)

Figure 61 Failed trade execution attempt

<span id="page-36-1"></span>

| Buy EUR / Sell CHF<br>1m EUR @ Limit 1.20082<br>Ok Exec<br>SO-11278241 |                |      |          |  |  |  |  |  |
|------------------------------------------------------------------------|----------------|------|----------|--|--|--|--|--|
| Execution Summary                                                      |                |      |          |  |  |  |  |  |
| NOT DONE<br>0k / 1m                                                    |                |      |          |  |  |  |  |  |
| $\vee$ Display Execution Details<br><b>Execution Failures</b>          |                |      |          |  |  |  |  |  |
| <b>Failure Comment</b><br>Provider                                     |                |      |          |  |  |  |  |  |
| DB.DEMO                                                                | demo rejection |      |          |  |  |  |  |  |
| <b>Execution Details</b>                                               |                |      |          |  |  |  |  |  |
| <b>Status</b>                                                          | Amount.        | Rate | Provider |  |  |  |  |  |
| CLOSED                                                                 | 1m             |      | DB.DEMO  |  |  |  |  |  |

<span id="page-36-2"></span>Figure 62 Failure details

For resting orders, additional statuses will also be displayed.

**ACTIVE** The order is monitoring the market and will trigger as soon as a quote matches the execution criteria. The status bar is displayed in yellow and will turn to green potentially in steps in case the order gets partially filled.

![](_page_37_Picture_2.jpeg)

Figure 63 Active resting order, partially executed

<span id="page-37-0"></span>**NOT ACTIVE** The order was deactivated by the user or the system.

![](_page_37_Picture_5.jpeg)

Figure 64 Deactivated order, partially executed

<span id="page-37-1"></span>Users with a small screen or a high resolution might consider the right side of the Supersonic screen showing the Execution reports as a waste of space. This area can be hidden by moving the vertical scroll bar to the right of by clicking on the small right arrow at the top of the scroll bar.

![](_page_37_Picture_8.jpeg)

Figure 65 Execution reports area resizing

<span id="page-37-2"></span>Over the board settings symbol , you can configure the execution reports to fly into the screen from the bottom right corner.

| $\epsilon$ -Execution Reports $\epsilon$                            |  |
|---------------------------------------------------------------------|--|
| $\triangledown$ Display execution reports as animated notifications |  |

<span id="page-37-3"></span>Figure 66 Board configuration for execution reports

This allows to gain space by generally hiding the Execution report area on the right side of the screen and still see the execution reports whenever a trade is executed.

| <b>331 Supersonic Trader</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |                                                                                                     | →<br>$\Box$ $\times$<br>l a l                                                                                    |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------|
| View Executions Import<br><b>File</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                                     | Help                                                                                                             |
| O WAP X<br>$\bullet$<br>$\Box$ Majors $\times$<br>$\Box$ Orders $\times$<br>G.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | Total or Partial Fill at Rate v<br>4 ♪ 国                                                            | $\begin{tabular}{ c c c c c c c c c c c c c c c c c c c$<br>Views $\blacktriangledown$<br>a<br>ளி<br>$\Box$<br>伽 |
| <b>0</b> 0.4 P 0.6 S<br>$- 4$<br><b>EUR</b><br>USD $\bullet$ $\circ$ $\times$<br>$\overline{\phantom{a}}$<br>$\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | 0 0.1 P 0 0.5 S<br>$USD - \bullet$ $\circ$ $\times$<br>$\bigcirc$<br>GBP<br>$\left  \cdot \right $  | 99<br>₿                                                                                                          |
| <b>BID</b><br><b>ASK</b><br>Sell EUR<br>1.31<br><b>Buy EUR</b><br>1.31                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | <b>BID</b><br>1.61<br>1.61<br>Buy USD                                                               | <b>ASK</b><br>Sell USD                                                                                           |
| $13$ 2<br>$12\mathrm{s}$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | 51<br>5                                                                                             |                                                                                                                  |
| $\overline{1}$<br>5<br>m<br>m                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | $\overline{4}$<br>m                                                                                 | 4.8<br>m                                                                                                         |
| 1 <sub>m</sub><br>1 <sub>m</sub>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | 1 <sub>m</sub><br>1m                                                                                | 0202020202020202020202020202020<br>⊠                                                                             |
| $\boxed{ \bigcirc \hspace{-0.2cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0.1cm} \circ \hspace{-0$<br>$\bullet$<br>$\mathbf{a}$<br>$\overline{\mathbf{y}}$ $\mathbf{y}$<br>$EUR - \blacktriangledown$<br>GBP<br>$\circledcirc$ | $\bigcirc$ 0.6 $\div$ p<br>⊚∫<br><b>USD</b><br>$JPY \rightarrow \bigcirc x$<br>$\blacktriangledown$ | Buy USD / Sell JPY<br>1m USD @ Limit 86.957                                                                      |
| 0.81<br>BID.<br>0.81<br><b>ASK</b><br>Buy EUR<br>Sell EUR                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | 86.<br><b>BID</b><br>Sell USD<br>86.                                                                | 1m Exec @ 86.957                                                                                                 |
| 189<br>195<br>$\overline{\mathbf{S}}$<br>2.5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | 96<br>965<br>$\overline{1}$                                                                         | SO-12460624                                                                                                      |
| m<br>m<br>1 <sub>m</sub><br>1 <sub>m</sub>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | $\mathsf{m}$<br>1 <sub>m</sub><br>1 <sub>m</sub>                                                    | <b>Execution Summary</b><br>$1m \neq 1m$<br><b>DONE</b>                                                          |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | Display Execution Details                                                                                        |
| <b>O</b> 0.1 PO0.6 S<br>9 9<br>$EUR - \bullet$<br>$CHF$ $\blacktriangleright$ $\bigcirc$ $x$<br>$\circledcirc$                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | $\bigcirc$ 0.1 $\bigcirc$ p<br>$CAD - O X$<br>$\circ$<br><b>USD</b><br>$\overline{\phantom{a}}$     |                                                                                                                  |
| <b>ASK</b><br><b>BID</b><br>1.20<br>1.20<br><b>Buy EUR</b><br><b>Sell EUR</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | <b>BID</b><br>0.98<br>0.98<br>Sell USD                                                              | $\blacksquare$                                                                                                   |
| 88s<br>884<br>2.5<br>$\overline{2}$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | 54 $\scriptstyle{8}$<br>54<br>$\overline{\mathbf{1}}$                                               | Buy EUR / Sell USD<br>10m EUR @ Limit 1.31131                                                                    |
| m<br>m<br>1 <sub>m</sub><br>1 <sub>m</sub>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | m<br>1 <sub>m</sub><br>1 <sub>m</sub>                                                               | 10m Exec @ 1.31131                                                                                               |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | 80-12460511                                                                                                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | <b>Execution Summary</b><br>10m / 10m<br>DONE                                                                    |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | Display Execution Details                                                                                        |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     |                                                                                                                  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     |                                                                                                                  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | 圖<br>Buy GBP / Sell USD                                                                                          |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | 1m USD @ Limit 1.61512<br>1m Exec @ 1.61512                                                                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | SO-12460505                                                                                                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                     | <b>Execution Summary</b>                                                                                         |

<span id="page-38-0"></span>Figure 67 Supersonic screen with Execution Reports as animated notifications

Open execution reports can be closed clicking on the "x" icon in the upper right corner of a single ticket.

A larger number of tickets can be closed by going to the menu item "Executions" and there selecting to close either all or all successful records.

| Executions                 |                                     |        |
|----------------------------|-------------------------------------|--------|
|                            | Close All Successful Reports Ctrl-S |        |
| <b>o</b> Close All Reports |                                     | Ctrl-T |

<span id="page-38-1"></span>Figure 68 Closing execution reports

# <span id="page-39-0"></span>**8 STREAMS BLOTTER AND SESSIONS**

At the bottom of the window all executed trades are displayed. It is possible to view all trades executed today or at all trades requested today (inlcuding rejected deals or partial non-filled). On the tab "Old",a search functionality for trades done in the past is available.

By default, all trades done by an entity, within a trading day will be saved in the "Today" Session.

It is possible to split the blotter into additional Sessions. This is done by creating a "New Session". The created session is available for all trade-as entities.

At 5pm EST (New York, America) (date roll), ALL sessions will be closed for a new day session.

In case Resting Stream Orders were placed in a Session, you can decide if the order should be kept when starting a new session. If the message is negated, the resting orders will be deactivated. If the resting orders should be kept, please be aware that their execution remains attached to the Session it was created in.

| <b>Executed Today</b> All Today                     | Active Passive Old  | Positions Open Positions  | <b>Closed Positions</b> |      |                |              |              | <b>TradeAs:</b>                                                        | 360TGROUP             | ▾▏                    | Session:     | Today                                                                      | New Session |
|-----------------------------------------------------|---------------------|---------------------------|-------------------------|------|----------------|--------------|--------------|------------------------------------------------------------------------|-----------------------|-----------------------|--------------|----------------------------------------------------------------------------|-------------|
| Execution                                           | <b>Execution Id</b> | <b>Requested Provider</b> | Requester               |      |                |              |              | Requester Acti Currency Notional Amo Executed Amount Notional Currency | <b>Execution Rate</b> | <b>Effective Date</b> | Reference Id | <b>Execution Status Changed</b>                                            |             |
| Provider Execution At Rate                          | SO-********-S1      | LLOYDS.DEMO               | 360TTAS.Treasurer1      | Buy  | EUR/USD        | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22778               | 10/12/14              | 6462163      | 08/12/14 15:55:04                                                          |             |
| Provider Execution At Rate                          | SO-********-S1      | PEBANK EMEA1.TEST         | 360TTAS.Treasurer1      | Sell | EUR/USD        | 1.000.000.00 | 1.000.000.00 | <b>EUR</b>                                                             | 1.22770               | 10/12/14              | 6462162      | 08/12/14 15:55:00                                                          |             |
| Provider Execution At Rate                          | SO-********-S1      | <b>Barclays BARX.DEMO</b> | 360TTAS.Treasurer1      | Sell | <b>EUR/USD</b> | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22770               | 10/12/14              | 6462154      | 08/12/14 15:54:46                                                          |             |
| $=$ Provider Execution At Rate                      | SO-********-S1      | <b>Barclays BARX.DEMO</b> | 360TTAS.Treasurer1      | Sell | EUR/USD        | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22498               | 10/12/14              | 6461906      | 08/12/14 12:03:45                                                          |             |
| $=$ Provider Execution At Rate                      | SO-********-S1      | PEBANK EMEA1.TEST         | 360TTAS.Treasurer1      | Sell | <b>EUR/USD</b> | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22770               | 10/12/14              | 6462160      | 08/12/14 15:54:56                                                          |             |
| $=$ Provider Execution At Rate                      | SO-********-S3      | PEBANK EMEA1.TEST         | 360TTAS.Treasurer1      | Sell | <b>EUR/USD</b> | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22769               | 10/12/14              | 6462169      | 08/12/14 15:55:35                                                          |             |
| $=$ Provider Execution At Rate                      | SO-********-S2      | <b>Barclays BARX.DEMO</b> | 360TTAS.Treasurer1      | Sell | <b>EUR/USD</b> | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22769               | 10/12/14              | 6462168      | 08/12/14 15:55:35                                                          |             |
| <b><math>\div</math> Provider Execution At Rate</b> | SO-********-S1      | <b>BOAL.DEMO</b>          | 360TTAS.Treasurer1      | Sell | <b>EUR/USD</b> | 2,000,000.00 | 2,000,000.00 | <b>EUR</b>                                                             | 1.22769               | 10/12/14              | 6462166      | 08/12/14 15:55:35                                                          |             |
| $=$ Provider Execution At Rate                      | SO-22228165-S1      | <b>Barclays BARX.DEMO</b> | 360TTAS.Treasurer1      | Buy  | EUR/USD        | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22775               | 10/12/14              | 6462165      | 08/12/14 15:55:27                                                          |             |
| <b><math>=</math> Provider Execution At Rate</b>    | SO-22227886-S1      | <b>BOAL.DEMO</b>          | 360TTAS.Treasurer1      | Sell | <b>EUR/USD</b> | 1.000.000.00 | 1.000.000.00 | <b>EUR</b>                                                             | 1.22808               | 10/12/14              | 6462101      | 08/12/14 15:32:09                                                          |             |
| Provider Execution At Rate                          | SO-********-S1      | <b>Barclays BARX.DEMO</b> | 360TTAS.Treasurer1      | Buy  | EUR/USD        | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22776               | 10/12/14              | 6462155      | 08/12/14 15:54:48                                                          |             |
| Provider Execution At Rate                          | SO-********-S1      | <b>PEBANK APAC.TEST</b>   | 360TTAS.Treasurer1      | Buy  | EUR/USD        | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1,22776               | 10/12/14              | 6462158      | 08/12/14 15:54:54                                                          |             |
| <b><math>\div</math> Provider Execution At Rate</b> | SO-********-S1      | <b>BOAL.DEMO</b>          | 360TTAS.Treasurer1      | Sell | <b>EUR/USD</b> | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22770               | 10/12/14              | 6462156      | 08/12/14 15:54:51                                                          |             |
| Provider Execution At Rate                          | SO-********-S1      | <b>Barclays BARX.DEMO</b> | 360TTAS.Treasurer1      | Sell | EUR/USD        | 1,000,000.00 | 1,000,000.00 | <b>EUR</b>                                                             | 1.22770               | 10/12/14              | 6462164      | 08/12/14 15:55:21                                                          |             |
| $\mathbf{H}$<br>HH.                                 |                     |                           |                         |      |                |              |              |                                                                        |                       |                       |              |                                                                            |             |
| ๏                                                   |                     |                           |                         |      |                |              |              |                                                                        |                       |                       |              | Login as - 360T.Srinivasan as User: 360TTAS.Treasurer1, Company: 360TGROUP |             |

<span id="page-39-1"></span>Figure 69 Executed Streams table in Today's session

Switching between sessions and trade-as entities (if applicable), can be done over the dropdown list

| TradeAs:   | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!        |   | Today<br>Session: |         |                      | <b>New Session</b> |  |
|------------|-------------------------------------------------|---|-------------------|---------|----------------------|--------------------|--|
|            | 360TGROUP                                       |   |                   |         | Todav                |                    |  |
| I Currency | 360TGROUP.TAS.A1                                | R | Refel             |         | Session-2 [15:55:32] | nged               |  |
| UR         | 360TGROUP.TAS.A2                                |   | 640               |         | Session-1 [12:04:26] |                    |  |
| UR         | 360TGROUP.TAS.A3                                |   |                   | 6462162 | 08/12/14 15:55:00    |                    |  |
| UR         | 360TGROUP.TAS.B1                                |   |                   | 6462154 | 08/12/14 15:54:46    |                    |  |
| UR         | 360TGROUP.TAS.B2                                |   |                   | 6461906 | 08/12/14 12:03:45    |                    |  |
| UR         | 360TGROUP.TAS.B3                                |   |                   | 6462160 | 08/12/14 15:54:56    |                    |  |
| UR         | 360TGROUP.TAS.C1<br>1. <i>22105</i><br>10/12/14 |   |                   | 6462169 | 08/12/14 15:55:35    |                    |  |

<span id="page-39-2"></span>Figure 70 Switch between Trade-As Entities and Sessions

Within each tab, a filter can be applied and columns can be added or removed by right-clicking on the table's header line and then selecting or deselecting certain columns.

Furthermore, it is possible to display the ticket belonging to each trade by right-clicking on the respective line in the executed streams table and then selecting the option "Show Ticket".

The table content can be printed by right-clicking within a table and then selecting "Print Table Content". The content of a certain table can be copied and pasted into a spreadsheet software, e.g. MS Excel. This can be done by clicking onto any line within in table and then pressing the Ctrl and C keys. The table content will then be copied and, once the spreadsheet software is open, be pasted into a sheet there.

In the "Old" tab it is possible to define a period of time for which the deals shall be displayed.

The "All Today" tab, among other information, allows viewing the weighted average rate of execution for an order that has had more than one fill.

| $\ldots$<br>All Today Old<br>Executed Today                                                           |                                          |              |  |                                                                |                  |                       |                                                |            |                                                                   |  |              |          |                      |
|-------------------------------------------------------------------------------------------------------|------------------------------------------|--------------|--|----------------------------------------------------------------|------------------|-----------------------|------------------------------------------------|------------|-------------------------------------------------------------------|--|--------------|----------|----------------------|
| <b>Table Filter Field</b>                                                                             |                                          |              |  |                                                                |                  |                       |                                                |            |                                                                   |  |              |          |                      |
| $\triangledown$ To: 11-May-2010<br>Get Elements   Q-<br>$\overline{\phantom{0}}$<br>From: 10-May-2010 |                                          |              |  |                                                                |                  |                       |                                                |            |                                                                   |  |              |          |                      |
| <b>Filtered Table</b>                                                                                 |                                          |              |  |                                                                |                  |                       |                                                |            |                                                                   |  |              |          |                      |
|                                                                                                       |                                          |              |  |                                                                |                  | 67 out of 67 Elements |                                                |            |                                                                   |  |              |          |                      |
| Execution                                                                                             |                                          |              |  | Execution Id Execution Status   Requester   Requested Provider | Requester Action |                       | Currency   Notional Amount   Notional Currency |            | Executed Amount   Execution Rate   Failed Amount   Effective Date |  |              |          |                      |
| El / Stream Execution  50-10038000                                                                    |                                          | Closed       |  |                                                                | Buy              | EUR/USD               | 5,000,000.00                                   | <b>EUR</b> | 5,000,000.00                                                      |  | 0.00         | 12/05/10 | $\frac{1}{\sqrt{2}}$ |
| $\equiv$ $\neq$ Stream Execution  SO-10038505                                                         |                                          | Closed       |  |                                                                | Sell             | EUR/USD               | 1,000,000.00                                   | <b>EUR</b> | 1,000,000.00                                                      |  | 0.00         | 13/05/10 |                      |
| / Stream Execution  50-10037956                                                                       |                                          | Closed       |  |                                                                | Sell             | EUR/USD               | 7,000,000.00                                   | <b>EUR</b> | 0.00                                                              |  | 7,000,000.00 | 12/05/10 |                      |
| E / Stream Execution  50-10037960                                                                     |                                          | Closed       |  |                                                                | Sell             | EUR/USD               | 5,000,000.00                                   | <b>EUR</b> | 5,000,000.00                                                      |  | 0.00         | 12/05/10 |                      |
| $\blacksquare$ / Stream Execution  50-10038080                                                        |                                          | Closed       |  |                                                                | Sell             | EUR/USD               | 1,000,000,00                                   | <b>EUR</b> | 1.000.000.00                                                      |  | 0.00         | 12/05/10 |                      |
| E / Stream Execution  50-10038036                                                                     |                                          | Closed       |  |                                                                | Sell             | EUR/USD               | 5,000,000.00                                   | <b>EUR</b> | 5,000,000.00                                                      |  | 0.00         | 12/05/10 |                      |
| $\equiv$ $\equiv$ Stream Execution  SO-10038424                                                       |                                          | Closed<br>__ |  |                                                                | Sell             | EUR/USD               | 1,000,000.00                                   | <b>EUR</b> | 1,000,000.00                                                      |  | 0.00         | 13/05/10 |                      |
| $\Theta$                                                                                              | User: 360T.Steinmetz, Company: 360TGROUP |              |  |                                                                |                  |                       |                                                |            |                                                                   |  |              |          |                      |

<span id="page-40-1"></span>Figure 71 Searching for old Supersonic Trader deals

The Executed Streams area can be removed completely from the Supersonic Trader panel by either going to the menu item "View/Executed Streams" or by hitting the Ctrl and O key. Ctrl O will also bring the Streams Deal Blotter back onto the display.

In case the Resting Stream Orders functionality is activated, additional tabs displaying currently placed and watched Active orders as well as currently deactivated Passive orders. For more details, please refer to the Resting Orders Chapter of this user guide.

| <b>350 Supersonic Trader</b>                                                                       |              |                  |          |                 |                   |                 |                 |                  |                                                          |         |  |  |  |
|----------------------------------------------------------------------------------------------------|--------------|------------------|----------|-----------------|-------------------|-----------------|-----------------|------------------|----------------------------------------------------------|---------|--|--|--|
| Help<br>Executions<br>Import<br>File<br>View                                                       |              |                  |          |                 |                   |                 |                 |                  |                                                          |         |  |  |  |
| Today<br>Session:<br>New Session<br>All Today<br>old<br><b>Executed Today</b><br>Active<br>Passive |              |                  |          |                 |                   |                 |                 |                  |                                                          |         |  |  |  |
| Execution                                                                                          | Execution Id | Requester Action | Currency | Notional Amount | Notional Currency | Executed Amount | Limit/Stop Rate | Points To Market | Percentage To Market                                     | Session |  |  |  |
| C 000 Order                                                                                        | 50-11789841  | Sell             | GBP/USD  | 10,000,000,00   | GBP               | 0.00            |                 |                  |                                                          |         |  |  |  |
| Stream Execut 50-11789                                                                             |              | <b>Buy</b>       | GBP/USD  | 15,000,000.00   | <b>GBP</b>        | 0.00            | 1.61768         | 0.00035          | 0.02164                                                  |         |  |  |  |
|                                                                                                    |              |                  |          |                 |                   |                 |                 |                  |                                                          |         |  |  |  |
|                                                                                                    |              |                  |          |                 |                   |                 |                 |                  | User: 360T.Treasurer, Company: 360TGROUP <b>EXAMPLES</b> |         |  |  |  |

<span id="page-40-2"></span>Figure 72 Active Resting Orders Blotter

# <span id="page-40-0"></span>**9 ABILITY TO VIEW AND DEACTIVATE RESTING AND TWAP ORDERS**

This functionality is only available upon request and can be enabled for a group of users within the same company entity. (Please contact Client Advisory Services - CAS or your dedicated sales representative to have this functionality enabled).

It is possible to configure a group of users, within the same company, to be able to deactivate each other's Supersonic Resting and TWAP group orders. All users configured for this setting will see an additional deal blotter tab in Supersonic called **"Active Group Orders"**. The "**Active Group Orders**" tab will display all active Resting and TWAP orders belonging to members of the said user group.

| Executed Today   All Today      | Passive<br>Active   | O <sub>Id</sub><br><b>Positions</b> | Open Positions | Closed Positions       | <b>Active Group Orders</b> | <b>New Session</b><br>Session:<br>Today  |                 |                       |                                       |               |  |  |
|---------------------------------|---------------------|-------------------------------------|----------------|------------------------|----------------------------|------------------------------------------|-----------------|-----------------------|---------------------------------------|---------------|--|--|
|                                 |                     |                                     |                |                        |                            |                                          |                 |                       |                                       |               |  |  |
| <b>Execution</b>                | <b>Execution Id</b> | <b>Requester Action</b>             | Currency       | <b>Notional Amount</b> | <b>Notional Currency</b>   | <b>Executed Amount</b>                   | Limit/Stop Rate | <b>Execution Rate</b> | <b>Requester Legal Entity</b>         | Requester     |  |  |
| <b>TWAP Order</b>               | SO-3245003          | Buy                                 | <b>GBP/USD</b> | 100,000,000.00         | <b>GBP</b>                 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 1.25000         |                       | 360TGROUP                             | 360T.Luetjens |  |  |
| $\div$ Stream Execution At Rate | SO-3244996          | Sell                                | <b>EUR/USD</b> | 20,000,000.00          | <b>EUR</b>                 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 1.15000         |                       | 360TGROUP                             | 360T.Luetjens |  |  |
|                                 |                     |                                     |                |                        |                            |                                          |                 |                       |                                       |               |  |  |
| Jun 14, 2017 3:55:16 PM GMT     |                     |                                     |                |                        |                            |                                          |                 |                       | User: 360T. Yaren, Company: 360TGROUP |               |  |  |

<span id="page-40-3"></span>Figure 73 Active Group Orders Blotter

In addition, the original requesting user can still view their own orders within the "Active" tab of the Supersonic deal blotter.

| Executed Today   All Today      | Active Passive      | Old<br><b>Positions</b> | Open Positions | <b>Closed Positions</b> | <b>Active Group Orders</b> |                                            |                 | Session:              | Today                         | <b>New Session</b> |  |
|---------------------------------|---------------------|-------------------------|----------------|-------------------------|----------------------------|--------------------------------------------|-----------------|-----------------------|-------------------------------|--------------------|--|
| Execution                       | <b>Execution Id</b> | <b>Requester Action</b> | Currency       | <b>Notional Amount</b>  | <b>Notional Currency</b>   | <b>Executed Amount</b>                     | Limit/Stop Rate | <b>Execution Rate</b> | <b>Requester Legal Entity</b> | Requester          |  |
| $   \cdot   $ TWAP Order        | SO-3245003          | Buy                     | GBP/USD        | 100,000,000.00          | <b>GBP</b>                 | 0.001                                      | 1.25000         |                       | 360TGROUP                     | 360T.Luetjens      |  |
| $\div$ Stream Execution At Rate | SO-3244996          | Sell                    | <b>EUR/USD</b> | 20,000,000.00           | <b>EUR</b>                 | <b>Expand All</b>                          |                 |                       | 360TGROUP                     | 360T.Luetjens      |  |
|                                 |                     |                         |                |                         |                            | <b>E</b> Collapse All                      |                 |                       |                               |                    |  |
|                                 |                     |                         |                |                         |                            | <b>Print Table Content</b>                 | Ctrl-P          |                       |                               |                    |  |
|                                 |                     |                         |                |                         |                            | <b>Export to CSV</b>                       | Ctrl-E          |                       |                               |                    |  |
|                                 |                     |                         |                |                         |                            | Open                                       | Ctrl-O          |                       |                               |                    |  |
|                                 |                     |                         |                |                         |                            | <b>Deactivate</b>                          | Ctrl-D          |                       |                               |                    |  |
|                                 |                     |                         |                |                         |                            | Deactivate all Resting Group Orders Ctrl-R |                 |                       |                               |                    |  |
|                                 |                     |                         |                |                         |                            |                                            |                 |                       |                               |                    |  |

<span id="page-41-1"></span>Figure 74 Deactivating a single order or group of Resting or TWAP orders

Once an order is deactivated it will no longer be visible within the "**Active Group Orders**" tab:

| <b>Executed Today</b><br>All Today                                   | Active<br>Passive   | <b>Positions</b><br>Old |          | Open Positions Closed Positions | <b>Active Group Orders</b> |                        |                 | Today                 | New Session                   |           |  |
|----------------------------------------------------------------------|---------------------|-------------------------|----------|---------------------------------|----------------------------|------------------------|-----------------|-----------------------|-------------------------------|-----------|--|
| <b>Execution</b>                                                     | <b>Execution Id</b> | <b>Requester Action</b> | Currency | <b>Notional Amount</b>          | <b>Notional Currency</b>   | <b>Executed Amount</b> | Limit/Stop Rate | <b>Execution Rate</b> | <b>Requester Legal Entity</b> | Requester |  |
|                                                                      |                     |                         |          |                                 |                            |                        |                 |                       |                               |           |  |
|                                                                      |                     |                         |          |                                 |                            |                        |                 |                       |                               |           |  |
|                                                                      |                     |                         |          |                                 |                            |                        |                 |                       |                               |           |  |
| User: 360T. Yaren, Company: 360TGROUP<br>Jun 14, 2017 4:00:04 PM GMT |                     |                         |          |                                 |                            |                        |                 |                       |                               |           |  |
|                                                                      |                     |                         |          |                                 |                            |                        |                 |                       |                               |           |  |

<span id="page-41-2"></span>Figure 75 Empty Active Group Orders tab - since all orders have been deactivated

Please note that the original requesting user will still be able to view the deactivated order in the "**Passive**" tab of the Supersonic deal blotter. In addition, he/she will be able to see which user, from the group, deactivated the order and at what time:

| <b>Executed Today</b> | All Today<br>Active                                                    | <b>Passive</b> |      |                | Old Positions   Open Positions   Closed Positions |            | <b>Active Group Orders</b> |         |  |         |  |                                                                                                                                                                         | Session: | Today                           | <b>New Session</b>              |  |
|-----------------------|------------------------------------------------------------------------|----------------|------|----------------|---------------------------------------------------|------------|----------------------------|---------|--|---------|--|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|---------------------------------|---------------------------------|--|
| <b>Execution</b>      | <b>Execution Id</b>                                                    |                |      |                |                                                   |            |                            |         |  |         |  | Requester Action Currency Notional Amount Notional Currency Executed Amount Limit/Stop Rate Execution Rate Points To Market Percentage To Market Requester Legal Entity |          | <b>Execution Status Changed</b> | <b>Execution Status Origina</b> |  |
| $=$ Stream Execut     | SO-3244996                                                             |                | Sell | <b>EUR/USD</b> | 20,000,000.00                                     | <b>EUR</b> | 0.00                       | 1.15000 |  | 0.02234 |  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>1.98109                                                                                                                     |          | 6/14/17 5:59:58 PM              | 360T. Yaren                     |  |
| <b>IVAP Order</b>     | SO-3245003                                                             |                | Buy  | GBP/USD        | 100,000,000.00                                    | <b>GBP</b> | 0.00                       | 1.25000 |  |         |  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                                                                                |          | 6/14/17 5:59:34 PM              | 360T.Yaren                      |  |
|                       |                                                                        |                |      |                |                                                   |            |                            |         |  |         |  |                                                                                                                                                                         |          |                                 |                                 |  |
|                       | User: 360T.Luetjens, Company: 360TGROUP<br>Jun 14, 2017 4:20:09 PM GMT |                |      |                |                                                   |            |                            |         |  |         |  |                                                                                                                                                                         |          |                                 |                                 |  |

<span id="page-41-3"></span>Figure 76 Deactivated orders are still visible in the Passive tab of the original requesting user

The original requesting user can choose to reactivate his/her "Passive" orders at any time via a right mouse click in the main body of the "Passive" tab and selecting "Activate" or "Activate all Resting Orders". The newly activated orders will then be visible in both the "Active" tab (of the original requesting user) and the "Active Group Orders" tab for all users within the group.

# <span id="page-41-0"></span>**10 SUPERSONIC TRADER POSITIONS**

This functionality is only available upon request and can be enabled for individual users. (Please contact Client Advisory Services - CAS or your dedicated sales to have this functionality enabled).

Executed transactions are grouped in the Positions tab. Here they are constantly marked-tomarket against a current market rate.

|      | <b>Executed Today</b> | Active<br>All Today | <b>Positions</b><br>O <sup>1</sup><br>Passive | Open Positions                  | <b>Closed Positions</b> | TradeAs:           | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                           | Session: | Today                                                                      | <b>New Session</b> |
|------|-----------------------|---------------------|-----------------------------------------------|---------------------------------|-------------------------|--------------------|------------------------------------------|---------------------------|----------|----------------------------------------------------------------------------|--------------------|
|      | Position              | Open Base Amount    | Open Rate                                     | Closed Base Amount   Close Rate |                         | <b>Market Rate</b> | P/L                                      | Quote Currency Originator |          |                                                                            |                    |
|      | <b>EUR/USD</b>        | $-11,000,000.00$    | 1.22767                                       | $-4,000,000.00$                 |                         | 1.22848            | $-9,340,00$                              | <b>USD</b>                |          |                                                                            |                    |
|      | <b>GBP/USD</b>        | $-1.000.000.00$     | 1.56247                                       | 0.00                            |                         | 1.23100            | 331.470.00                               | <b>USD</b>                |          |                                                                            |                    |
|      | $\bigcirc$ NZD/USD    | $-1,000,000.00$     | 0.76547                                       | 0.00                            |                         | 0.76580            | $-330.00$                                | <b>USD</b>                |          |                                                                            |                    |
|      |                       |                     |                                               |                                 |                         |                    |                                          |                           |          |                                                                            |                    |
| II G |                       |                     |                                               |                                 |                         |                    |                                          |                           |          | Login as - 360T.Srinivasan as User: 360TTAS.Treasurer1, Company: 360TGROUP |                    |

<span id="page-41-4"></span>Figure 77 Total positions by currency pair

The Positions tab shows the aggregated positions by currency pair, while the Open Positions and Closed Positions tabs display the detail of each position.

For each single position, Supersonic Trader constantly calculates the profit and loss in the quote currency and, if selected, also in the currency set up for the requestor company, by using the current market rates.

The Open Rate of a single position is the rate at which the trade underlying to the respective single position was executed. On the summary line of a currency pair position, the displayed Open Rate represents the weighted average buy rate in case the position is long, or the weighted average sell rate, if the position is short. Positions already closed are not taken into account in that open rate average calculation.

Via right mouse click context menu, positions can be closed by generating an IOC market order.

Positions can be closed one-by-one by selecting "Close Position" or to close a total position by selecting "Close Currency Couple Position". To close **all** open positions, select "Close All Open Positions".

| $\Box$ Expand All              | *      |
|--------------------------------|--------|
| □ Collapse All                 |        |
| Print Table Content            | Ctrl-P |
| Close Currency Couple Position | Ctrl-C |
| Close Position                 | Ctrl-L |
| Close All Open Positions       | Ctrl-A |

<span id="page-42-1"></span>Figure 78 Close currency positions

Positions are managed by Sessions per Trade-As entity. The Session "Today" includes ALL positions and transactions created on the give trading day as the selected entity. When creating a "New Session", positon calculation starts from scratch and transactions within this Session are calculated a new positions.

| ession: | Session-2 [11:17:41] | New Session |
|---------|----------------------|-------------|
|         | Today                |             |
|         | Session-3 [11:18:24] |             |
|         | Session-2 [11:17:41] |             |
|         | Session-1 [11:17:23] |             |

<span id="page-42-2"></span>Figure 79 Switching from Session to Session

<span id="page-42-0"></span>One can switch from Session to Session to manage different intra day positions independently.

# **11 FILLING OF CUSTOM FIELDS**

By double clicking an executed transaction in the "Executed Today" tab, the deal confirmation ticket will open. The tab "Post trade" allows to fill custom fields. The following figure displays a Post Trade tab. On demand, the ticket can pop up automatically to prompt the user for post trade custom field entry. Please contact 360T to get this option enabled.

| $Q -$                  |                     |                           | <b>BST TEX BETA Platform - Deal Confirmation</b> |                                         |                          | $\boldsymbol{\mathsf{x}}$<br><b>ISCOVE</b>                         |
|------------------------|---------------------|---------------------------|--------------------------------------------------|-----------------------------------------|--------------------------|--------------------------------------------------------------------|
|                        |                     |                           |                                                  |                                         |                          |                                                                    |
| <b>Filtered Table</b>  |                     |                           |                                                  |                                         |                          |                                                                    |
|                        |                     |                           | 360                                              |                                         |                          | Local Date: 07/03/2011 16:08:41<br>Trade Date: 07/03/2011 15:08:41 |
| Execution              | <b>Execution Id</b> | <b>Requested Provider</b> |                                                  |                                         |                          |                                                                    |
| $F$ Provi              | SO-********-S1      | RBS.LND.DEMO              | <b>Deal Confirmation</b>                         | Post Trade<br><b>Competitive Quotes</b> | <b>Trade Allocations</b> |                                                                    |
| $F$ Provi              | SO-********-S1      | CITIBANK.DEMO             |                                                  |                                         |                          |                                                                    |
| $#$ Provi              | SO-********-S1      | RBS.LND.DEMO              | Portfolio                                        | P1                                      |                          |                                                                    |
| M' Provi               | SO-********-S1      | Barclays BARX.DEMO        |                                                  |                                         |                          |                                                                    |
| M' Provi               | SO-********-S6      | RBS.LND.DEMO              | Internal Ref. No.                                |                                         |                          |                                                                    |
| M' Provi               | SO-********-S7      | CITIBANK.DEMO             |                                                  |                                         |                          |                                                                    |
| $H$ <sup>I</sup> Provi | SO-********-S5      | Barclays BARX.DEMO        | Link No.                                         |                                         |                          |                                                                    |
| M <sup></sup> Provi    | SO-********-S2      | BOAL.DEMO                 | Instructions                                     | SSI                                     |                          | $\overline{\phantom{0}}$                                           |
| $M$ Provi              | SO-********-S3      | CITIBANK.DEMO             |                                                  |                                         |                          |                                                                    |
| $M$ Provi              | SO-********-S4      | RBS.LND.DEMO              | Entity                                           | SUB1                                    |                          | $\overline{\phantom{a}}$                                           |
| $M$ Provi              | SO-********-S1      | Barclays BARX.DEMO        |                                                  |                                         |                          |                                                                    |
| $M$ Provi              | SO-********-S1      | Barclays BARX.DEMO        | local                                            | $\overline{\mathbf{v}}$                 |                          |                                                                    |
| M' Provi               | SO-********-S1      | CITIBANK.DEMO             | user                                             |                                         |                          |                                                                    |
| M' Provi               | SO-********-S1      | Barclays BARX.DEMO        |                                                  |                                         |                          |                                                                    |
| M' Provi               | SO-********-S4      | CITIBANK.DEMO             |                                                  |                                         |                          |                                                                    |
| M' Provi               | SO-********-S1      | Barclays BARX.DEMO        |                                                  |                                         |                          |                                                                    |
| M <sup>'</sup> Provi   | SO-********-S3      | RBS.LND.DEMO              |                                                  |                                         |                          |                                                                    |

<span id="page-43-0"></span>Figure 80 Filling of custom fields

# <span id="page-44-0"></span>**12 CONTACT 360T**

#### **Global Client Advisory Services**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360Treasury Systems AG* Grüneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **Middle East Asia Pacific**

**United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

#### **EMEA Americas**

#### **USA**

*360 Trading Networks, Inc* 521 Fifth Avenue, 38th Floor New York, NY 10175 Phone: ****** 776 2900

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756