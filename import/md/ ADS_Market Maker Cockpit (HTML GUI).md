# **360T AUTODEALING SUITE (ADS)**

![](_page_0_Picture_1.jpeg)

# **MARKET MAKER COCKPIT (MMC)**

# **TO ENABLE**

# **PRICE MAKING, DISTRIBUTION AND RISK MANAGEMENT**

© 360 TREASURY SYSTEMS AG, 2015 THIS FILE CONTAINS PROPRIETARY AND CONF<PERSON>EN<PERSON><PERSON> INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG

| 1            | INTRODUCTION                                                      | 6        |
|--------------|-------------------------------------------------------------------|----------|
| 1.1          | MMC<br>COMPONENTS                                                 | 7        |
| 1.2          | PRICING MODEL<br>                                                 | 7        |
| 1.3          | OVERALL DATA FLOW<br>                                             | 7        |
| 1.4          | SUPPORTED PRODUCTS                                                | 8        |
|              |                                                                   |          |
| 2            | MARKET MAKER COCKPIT OVERVIEW<br>                                 | 9        |
| 2.1          | LOGIN AND LOGOUT                                                  | 9        |
| 2.2          | PANELS,<br>TABS AND TAB CONTAINER                                 | 10       |
| 2.3          | MODIFYING TABS AND TAB CONTAINERS<br>                             | 10       |
| 3            | CONFIGURE PRICING<br>                                             | 12       |
| 3.1          | INSTRUMENT CONTROL                                                | 12       |
| 3.2          | ADD AND REMOVE MANAGED INSTRUMENTS<br>                            | 13       |
| 3.3          | OWNERSHIP OF A MANAGED INSTRUMENT                                 | 14       |
| 3.4          | START/STOP PRICING OF INSTRUMENTS AND CHANNELS                    | 14       |
| 3.5          | PRICING AND RISK MANAGEMENT MODE                                  | 16       |
| 3.6          | TIER SIZE CONFIGURATION<br>                                       | 17       |
| 3.7          | INSTRUMENT CONFIGURATION                                          | 17       |
| 3.8          | REFERENCE PRICE FINDING                                           | 18       |
| 3.9          | ADDITONAL AND FIXED SPREAD                                        | 20       |
| 3.10         | MANUAL SKEW<br>                                                   | 21       |
| 3.11         | ROUNDING OUTBOUND QUOTES<br>                                      | 22       |
| 3.12         | REFERENCE STREAM AND PERCENT TOLERANCE                            | 23       |
| 3.13         | NON-EXECUTABLE STREAMS                                            | 24       |
| 3.14         | SWEEPABLE AND FULL AMOUNT STREAMS<br>                             | 25       |
| 3.15         | MANAGING SYNTHETIC CROSSES<br>                                    | 25       |
| 3.16<br>3.17 | PRICING OF UNMANAGED INSTRUMENTS<br>CONFIGURING TIME SLIPPAGE<br> | 26<br>27 |
| 3.18         | CONFIGURING PRICE SLIPPAGE<br>                                    | 27       |
| 3.19         | QUOTE FILTERING<br>                                               | 29       |
| 3.20         | PTMM                                                              | 31       |
|              |                                                                   |          |
| 4            | MONITOR PRICING                                                   | 32       |
| 4.1          | THE LIQUIDITY DETAILS PANEL                                       | 32       |
| 4.2          | INBOUND AND OUTBOUND PRICING TIER MONITOR                         | 32       |
| 4.3          | PRICING<br>DETAILS DIALOG                                         | 33       |
| 5            | RISK MANAGEMENT                                                   | 36       |
| 5.1          | MONITORING POSITIONS<br>                                          | 36       |
| 5.2          | PROFIT AND LOSS (P/L)<br>CALCULATIONS                             | 37       |
| 5.3          | POSITION FILTERING CONFIGURATION<br>                              | 37       |
| 5.4          | RISK MANAGEMENT CONFIGURATION                                     | 38       |
| 5.5          | POSITION RULES<br>                                                | 40       |
| 5.6          | PRICING RULES                                                     | 42       |
| 5.7          | ALERT RULES<br>                                                   | 43       |
| 5.8          | MANUAL POSITION AMENDMENTS<br>                                    | 44       |
| 5.9          | RESTRICT THE BANK BASKET FOR HEDGE ORDERS                         | 46       |
| 5.10         | CLIENT ORDER HANDLING RULES<br>                                   | 46       |
| 5.11         | PRICING AND RISK MANAGEMENT SCENARIOS<br>                         | 48       |
| 6            | BLOTTERS<br>                                                      | 50       |

| 8 | CONTACT 360T<br><br>53 |                                             |    |  |  |  |
|---|------------------------|---------------------------------------------|----|--|--|--|
| 7 |                        | AUDIT                                       | 52 |  |  |  |
|   | 6.5                    | CLIENT ACTIVITY BLOTTER<br>                 | 51 |  |  |  |
|   | 6.4                    | COMBINED CLIENT AND HEDGE ORDER BLOTTER<br> | 51 |  |  |  |
|   | 6.3                    | HEDGE ORDER BLOTTER                         | 51 |  |  |  |
|   | 6.2                    | CLIENT ORDER BLOTTER<br>                    | 50 |  |  |  |
|   | 6.1                    | GENERAL BLOTTER FEATURES<br>                | 50 |  |  |  |

# **TABLE OF FIGURES**

| Figure 1 Market Maker Cockpit Overview9                                   |  |
|---------------------------------------------------------------------------|--|
| Figure 2<br>Login and Start the Market Maker Cockpit10                    |  |
| Figure 3 Exit the Market Maker Cockpit10                                  |  |
| Figure 4 Instrument Control Tab12                                         |  |
| Figure 5 Open Global Instrument Configuration13                           |  |
| Figure 6 Global Instrument Configuration<br>14                            |  |
| Figure 7: Take over instrument ownership14                                |  |
| Figure 8 Start/Stop Pricing<br>15                                         |  |
| Figure 9 Start/Stop Channels<br>15                                        |  |
| Figure 10 Emergency Stop15                                                |  |
| Figure 11: Select risk management mode<br>16                              |  |
| Figure 12 Different Risk Management Assignment to Different Streams<br>16 |  |
| Figure 13 Define Tier Sizes<br>17                                         |  |
| Figure 14: Open instrument configuration dialog<br>17                     |  |
| Figure 15: Instrument Configuration Dialog<br>18                          |  |
| Figure 16: Reference Price Finding rules editor20                         |  |
| Figure 17 Defining Outbound Spread<br>20                                  |  |
| Figure 18: Basic skew settings<br>21                                      |  |
| Figure 19: Tier specific skew factors21                                   |  |
| Figure 20<br>Allowing Skew to cross Mid Price/ Opposite Side<br>22        |  |
| Figure 21: Reducing outbound spot rate precision22                        |  |
| Figure 22Reference Stream and Percent Tolerance23                         |  |
| Figure 23 Configure Non-Executable Streams<br>24                          |  |
| Figure 24<br>Non-executable Stream Selection<br>24                        |  |
| Figure 25: Cross Currency Pair Configuration<br>25                        |  |
| Figure 26: Pricing unmanaged instruments<br>26                            |  |
| Figure 27: Time slippage configuration27                                  |  |
| Figure 28: Example for price slippage<br>28                               |  |
| Figure 29: Price slippage configuration29                                 |  |
| Figure 30: Quote filter settings30                                        |  |
| Figure 31: Monitor filtered quotes<br>30                                  |  |
| Figure 32: Liquidity Details Panel<br>32                                  |  |
| Figure 33: Pricing Tier monitor<br>32                                     |  |
| Figure 34: Raw Inbound Quote Details34                                    |  |
| Figure 35: Filtered Inbound Quote Details<br>34                           |  |
| Figure 36: Outbound Price Details35                                       |  |
|                                                                           |  |

| Figure 37: Managed Positions blotter with context menu36 |
|----------------------------------------------------------|
| Figure 38 Context menu for managing open position<br>37  |
| Figure 39 Position Filtering38                           |
| Figure 40: Risk management configuration<br>39           |
| Figure 41 Position Rules<br>41                           |
| Figure 42: Risk management pricing rules43               |
| Figure 43 Alert popup message43                          |
| Figure 44 Context menu of Managed Positions<br>44        |
| Figure 45 Taskbar for manual position update44           |
| Figure 46<br>Amend Position<br>44                        |
| Figure 47 Set Position<br>44                             |
| Figure 48 Confirmation of Position Reset<br>45           |
| Figure 49 Confirmation of Position Flattening<br>45      |
| Figure 50: Restrict hedge order bank basket<br>46        |
| Figure 51: Manage Client Order Handling Rules<br>47      |
| Figure 52: Pricing and Risk Management Scenarios<br>49   |
| Figure 53: Scenario selection<br>49                      |
| Figure 54: Client Activity Blotter51                     |
| Figure 55: Accessing the Audit Log<br>52                 |
| Figure 56: Search by Action<br>53                        |

# <span id="page-5-0"></span>**1 INTRODUCTION**

The 360T Auto Dealing Suite (ADS) includes Pricing Engine functionality to define outbound prices, based on rates streamed inbound from your liquidity provider(s). The outbound price can be adjusted to include trader spreads, skewing etc. Being passed downstream, further adjustments to these prices like sales or customer spreads can be made by setting destination rules via Auto Dealing Suite (ADS)

The Market Maker Cockpit (MMC) allows automated risk management and pre-set actions with subsequent positions created by customer flows.

Position overviews and profit and loss calculation are provided. Users can set up the currency pairs that will be actively managed as well as define currency pairs for price stripping and/or quote pegged currencies.

*DISCLAIMER:*

*Please note that clients shall be solely responsible for the use of 360T's Market Maker Cockpit ("MMC").* 

*The MMC is a fully operational pricing engine with automated pricing functionalities. Each client using the MMC should be aware that an automated setup in general might lead to unsought trade results, and any use of the MMC requires a certain level of experience, requisite knowledge and skills, constant monitoring of the market and periodical review of all settings. 360T is not in the position to monitor any such activities or settings of the MMC and will under no circumstances interfere with any client's MMC setup.*

*With respect to the MMC, 360T will be in no event, regardless of cause, liable for any direct, indirect, special, incidental, punitive or consequential damages of any kind, whether arising under breach of contract, tort (including negligence), or otherwise, and whether based on this agreement or otherwise, even if advised of the possibility of such damages. This applies in particular to the settings of the MMC, its activation or deactivation and any trade executed (or not made) through the MMC.*

*Between the parties using MMC the client shall be solely responsible for the performance and enforcement of any and all trades resulting from using the MMC. Furthermore the MMC is provided to the client on an "as is" and "as available" basis without warranty of any kind (either express or implied).* 

# <span id="page-6-0"></span>**1.1 MMC Components**

The Market Maker Cockpit consists four major components;

- **Overview:** providing lists of defined currency pairs, views for in- and outbound prices, positions and deal blotters.
- **Pricing Controller:** enabling the creation of core prices for each defined currency pair and adjustments with spreads and/or skewing.
- **Risk Management**, enabling the definition of risk parameters for each currency pair and management rules once triggers and alerts have been breached.
- **Reference Price Finding**, enabling the selection of price provider(s) and pricing tiers etc.

# <span id="page-6-1"></span>**1.2 Pricing Model**

The outbound rate to a client (client rate) for a specific quantity tier is determined through the following stages:

- Trader Rate = Reference Rate + Additional Spread + Skew
- Client Rate = Trader Rate + Sales Margin

This product deals primarily with the calculation of the trader rate. Sales margin is a parameter of the 360T Auto Dealing Suite (ADS), and can be applied individually by client.

#### **Reference Rate:**

If not a fixed rate, the reference rate will be derived via price finding. This is a process which takes into account available market data and liquidity, risk management profile, and risk appetite. The reference rate is always a two-way price of bid and ask price. The difference between these two prices is the inbound spread. The middle of these two prices, is the reference mid-price.

#### **Additional spread:**

Spread is defined as the difference between bid and ask price. The difference between the reference bid and ask rate is the so called inbound spread. This inbound spread is often widened for various reasons.

#### **Skew:**

Skewing means to shift the mid-price (the middle of bid and ask rate) either towards the bid or ask side. A skewed and non-skewed two way price has still the same spread!

Trader spread and skew are parameters either manually set by the trader, but also automatically via rules.

# <span id="page-6-2"></span>**1.3 Overall Data Flow**

Below the general data flow within the MMC:

- 1. Liquidity providers (market makers) send quotes into the system. Each provider sends usually different quote levels. Quotes can be both tradable liquidity, but also pure market data.
- 2. Quotes are filtered and aggregated during Reference Price Finding. The basic idea here is to filter out unwanted quotes and create a trustable pool of liquidity for pricing.

- 3. Traders will manipulate the inbound liquidity and increase the spread and/or skew the price to the left or ride side.
- 4. The outbound price is forwarded to clients via RFS, SST, and FIX API's.
- 5. Clients create orders based on the price provided by the pricing engine. Such orders are routed back to the pricing engine.
- 6. Accepted client orders are added to the MMC positions.
- 7. Based on the configured risk management profile, the system will eventually decide to create a hedge order to reduce or flatten a position. Such hedge orders can be created manually too.
- 8. Trades originating from hedge orders flow back into the pricing engine position.

# <span id="page-7-0"></span>**1.4 Supported Products**

Market Maker Cockpit owner can price incoming requests for following products:

- 1. FX Spot: Any incoming Spot requests which are negotiated as RFS (Request for Streaming) or SEP (Streaming Executable Prices) can be routed to and priced by Market Maker Cockpit. Spot outbound quotes are constructed via Reference Price Finding as described in Section 3 and downstreamed to AutoDealingSuite where it can be adjusted with sales margins and trader spreads.
- 2. FX Forward: MMC can price any incoming FX Forward requests by using an additional market data source. MMC owners can provide their own swap points via an interface to 360T or use 360T`s Swap Data Feed (SDF) market data to price incoming Forward requests via MMC. Once a market data source is defined for pricing engine, spot components of the outright rates are received from Outbound quotes (as described in Section 3) and forward points are received from the defined Market Data Source.
- 3. FX Swap: MMC can price any incoming even and uneven FX Swap requests. Similarly to FX Forwards, the MMC generates the spot rate from the outbound quotes and retrieves the forward points on each leg from the defined forward market data source.
- 4. FX Future: MMC facilitates the generation of Eurex Exchange listed FX Future outbound prices in two ways:
  - It utilizes the outbound FX Spot price in order to form the Outbound FX Future price; the outbound FX Future price matches the outbound FX Spot price
  - The outbound FX Forward price is taken for the generation of the outbound FX Future price.

FX Future prices can be either streamed directly into the Eurex Exchange Central Limit Order Book (CLOB) or as off-book liquidity to 360T customers.

- 5. FX Rolling Spot Futures: MMC can generate off-book or on-book FX Rolling Spot Futures by taking the outbound FX Spot prices.
- 6. FX NDF: MMC can price any incoming NDF requests by using additional market data source for forward points. MMC owners can provide their own swap points data for NDFs via an interface to 360T or using 360T`s Swap Data Feed (SDF). Please note that Spot component of the outright rates are received from Outbound quotes (as described in Section 3) which are Onshore Spot rates.
- 7. Block-Trades: Similar to FX Forward pricing, MMC can price any incoming Block-Trade requests with many legs. These requests are priced via an additional market

data source with forward points information. For spot component, net amount of the block is taken into consideration while determining the side and band size.

## **Please note;**

Never leave the ADS MMC unattended when it was started to actively quote and manage currency positions!

# <span id="page-8-0"></span>**2 MARKET MAKER COCKPIT OVERVIEW**

The Market Maker Cockpit provides an overview of currency pairs being set up, current positions including profit and loss and various blotters to monitor client (requester) and hedge orders.

| <b>Market Maker Cockpit</b><br><b>Audit Log</b>                                                                                                                                               |                                                                                                                |                                                                                                                                                                                                |                                                     |                                                                  | $\bullet$ $\circ$ $\bullet$ $\circ$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------|------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Liquidity Details</b>                                                                                                                                                                      |                                                                                                                |                                                                                                                                                                                                |                                                     |                                                                  | ⊘                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| EUR/GBP<br>Inbound<br>1 <sub>m</sub><br>085750<br>0.86094<br>5 <sub>m</sub><br>$34.4 -$<br>10 <sub>m</sub><br>0.86201<br>0.8564 <sub>3</sub><br>15 <sub>m</sub><br>$-55.8$<br>25 <sub>m</sub> | Core Channel 1 (2)<br>Outbound<br>0.86094<br>0.8575 <sub>0</sub><br>$34.4 -$<br>0.564<br><b>081201</b><br>55.8 | Inbound<br>1 <sub>m</sub><br>1 <sub>m</sub><br>085797<br>5 <sub>m</sub><br>5m<br>$-25.8$<br>10 <sub>m</sub><br>10 <sub>m</sub><br>15 <sub>m</sub><br>15 <sub>m</sub><br>25 <sub>m</sub><br>25m | Core Channel 2 Q2<br>0.8505 <sub>1</sub><br>0.85797 | Outbound<br>0.8605 <sub>1</sub><br>$-25.8$                       | 1m<br>5 <sub>m</sub><br>10 <sub>m</sub><br>15m<br>25 <sub>m</sub>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| EUR/USD<br>Inbound<br>0.90829<br>0.98765<br>1 <sub>m</sub><br>$6.4 -$<br>0.98704<br>09889n<br>5 <sub>m</sub><br>18.6                                                                          | Core Channel 1 (2)<br>Outbound<br>09377<br>091837<br>$0.83$ $6.4$<br>0.58704<br>09889n<br>18.6                 | Inbound<br>1 <sub>m</sub><br>1m<br>5 <sub>m</sub><br>5m                                                                                                                                        | Core Channel 2 22                                   | Outbound                                                         | 1 <sub>m</sub><br>5 <sub>m</sub>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| <b>Instrument Control</b><br><b>Client Orders</b><br>All Orders<br><b>Hedge Orders</b>                                                                                                        | <b>Client Activity</b>                                                                                         | $\sqrt{2}$<br><b>Instrument Positions</b>                                                                                                                                                      |                                                     |                                                                  | $\sqrt{ }$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| Mode<br>Instrument<br>Managed By                                                                                                                                                              | Skew %<br>Skew PIPS                                                                                            | symbol<br>Scenario                                                                                                                                                                             | <b>Updated</b><br>Size CCY1                         | Size CCY2<br>Open PL                                             | Realized PL<br>Total PL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Managed<br>٠<br><b>All Instruments</b><br>Managed<br>EUR/CBP<br>360TMMCTrader1                                                                                                                | $-00$<br>းဝေ<br>Ő.<br>$\bullet$<br>$-4.1$<br><b>SO</b>                                                         | $\circ$<br>EUR/GBP<br>$= 0$<br><b>EUR/USD</b><br>ane.                                                                                                                                          | $\circ$<br>07:21:11.671<br>$\circ$<br>07:21:11.701  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>0.00<br>0.00<br>0.00 | $\forall \exists \emptyset$ = $\textcircled{8}$<br>0.00<br>0.00<br>0.00<br>$\mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{V} \rightarrow \mathcal{$<br>0.00 |
| Managed<br>EUR/USD<br>360TMMCTrader1                                                                                                                                                          | $-99$<br>$- 0 + 00$<br>$\blacksquare$<br>$\bullet$<br>$-4$                                                     | one<br>$\triangle$ $\circ$<br>CBP/USD                                                                                                                                                          | $\circ$<br>07:21:11.713                             | 0.00<br>0.00                                                     | $y \rightarrow 0 = 0$<br>0.00<br>0.00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| Managed<br>CBP/USD<br>360TMMCTrader1                                                                                                                                                          | 00<br>$ 00\rangle$<br>61<br>$\bullet$                                                                          | $\equiv$ $\alpha$<br>MXN/JPY<br>ane.                                                                                                                                                           | $\circ$<br>07:21:11.715                             | 0.00<br>0.00                                                     | $V + Q = Q$<br>0.00<br>0.00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Managed<br>MXN/JPY<br>360TMMCTrader1                                                                                                                                                          | 00<br>00<br>۰<br>$\circ$                                                                                       | $\triangle$ $\alpha$<br><b>LISD/TRY</b>                                                                                                                                                        | $\circ$<br>07:21:11.668                             | 0.00<br>0.00                                                     | 0.00<br>$\forall \exists \emptyset = \textcircled{x}$<br>0.00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| Managed<br><b>USD/TRY</b><br>360TMMCTrader1                                                                                                                                                   | O <sup>o</sup><br>00<br>$\sim$<br>-<br>-                                                                       | $\alpha$<br><b>XAU/USD</b>                                                                                                                                                                     | $\circ$<br>07:21:11.682                             | 0.00<br>0.00                                                     | 0.00<br>0.00<br>$\not\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Managed<br>XAU/USD<br>360TMMCTrader1<br>Я зботммс. Trader1, 360T.MMC                                                                                                                          | 00<br>GI<br>$0 + 00$<br>$\bullet$<br>$\overline{\phantom{a}}$                                                  | none<br>$B \triangleq Q$<br>$F = C$                                                                                                                                                            |                                                     |                                                                  | 02.11.2022.08:48.01 // Connected                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |

<span id="page-8-2"></span>Figure 1 Market Maker Cockpit Overview

# <span id="page-8-1"></span>**2.1 Login and Logout**

Users can login to the MMC user interface through an internet browser by below link:

### [https://ext01.360t.com/PE\\_1/static/login.html](https://ext01.360t.com/PE_1/static/login.html)

Login is possible after entering the username, password and the token sent to the user`s email address.

![](_page_9_Picture_2.jpeg)

Figure 2 Login and Start the Market Maker Cockpit

<span id="page-9-2"></span>To logout, click on logout button on top right or close the browser.

<span id="page-9-3"></span>Figure 3 Exit the Market Maker Cockpit

# <span id="page-9-0"></span>**2.2 Panels, Tabs and Tab Container**

The user interface is divided into various tabs such as Liquidity Details, Instrument Control, Order blotters and Instrument Positions.

Each tab consists of a tab header (rectangle box in the upper left corner containing the tab title), and tab content.

Tabs are organized within tab containers. By default there are four tab containers; one upper left, one bottom left, one upper right, and one at the bottom left. Each tab container is resizable by either moving the border between tab containers, or by maximizing/minimizing the view.

Your customized layout will be automatically saved when logging out to be available at the next login.

# <span id="page-9-1"></span>**2.3 Modifying Tabs and Tab Containers**

Tabs can be moved within the same tab container, into another existing tab container, or into a new tab container. Tabs can also be displayed separately by maximising the view.

To move a tab, click and hold the tab header with the left mouse button, and move the tab into a new location. Release the mouse button over the new target location.

To re-arrange the tab order within a tab container, drag and drop the tab horizontally within the same tab container.

To move a tab into another tab container, drop the tab onto the tab header of the target tab container.

To create a new tab container, drop the tab into the content of any other tab. Dependent where you drop the tab, the target tab will split either horizontally or vertically, and a new tab container will be added.

When the last tab of a container is removed, the tab container will disappear automatically.

Clicking on maximize icon ( ) will maximize the tab container to the size of the enclosing window. View can be restored by minimizing the tab container by clicking on icon.

# <span id="page-11-0"></span>**3 CONFIGURE PRICING**

# <span id="page-11-1"></span>**3.1 Instrument Control**

The instrument control tab allows to configure:

- Managed instruments
- General parameters such as time slippage and handling of unmanaged instruments and crosses
- Start and stop pricing and set the pricing/risk management mode
- Pricing tiers
- Client order handling
- Position rules
- pricing and skewing rules
- reference price finding
- risk management
- ownership for an instrument or price channel

The instrument control panel is organized into rows and columns. Each row contains information for a specific managed instrument or channel.

For each currency pair the trader can configure multiple pricing channels. Each pricing channel can have different spread/skew settings and Reference Price Finding rules. The assignment of your clients to the different pricing channel prices can be done by your admin users who have access to `Stream Group Mapping` within Business Configuration Tool.

| <b>Instrument Control</b> |  |                   |                                              |                                           |                                                                    |                      |    | ⊘                            |                                             |
|---------------------------|--|-------------------|----------------------------------------------|-------------------------------------------|--------------------------------------------------------------------|----------------------|----|------------------------------|---------------------------------------------|
| Instrument                |  | <b>Managed By</b> | Mode                                         | <b>Skew PIPS</b>                          | Skew %                                                             | Scenario             |    |                              |                                             |
| <b>All Instruments</b>    |  |                   | <b>Managed</b>                               |                                           |                                                                    | none                 |    |                              | ≎                                           |
| EUR/GBP                   |  | 360TMMC.Trader1   | Managed                                      | $\Theta$<br>$\Box$<br>2.1                 | $+$ $+$ $+$ $+$ $+$ $+$<br>- 91 -<br>$\mathbf{a}$                  | none                 | ⊳  |                              | ∎≏≎                                         |
| EUR/USD                   |  | 360TMMCTrader1    | <b>B2B</b>                                   | $\Theta$<br>$\blacksquare$                | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>-915-<br>10 | none<br>$\checkmark$ |    |                              | $\triangleright$ 0 $\triangle$ $\heartsuit$ |
| GBP/USD                   |  | 360TMMC.Trader1   | <b>B2B</b>                                   | $\Theta$<br>⋴                             | $\Theta$<br>$\blacksquare$<br>n                                    | none<br>$\checkmark$ |    | $\blacktriangleright$ $\Box$ | 8 ⇔                                         |
| USD/BRL                   |  | 360TMMC.Trader1   | B <sub>2</sub> B<br>$\overline{\phantom{0}}$ | $\Theta$<br>- 01-<br>n                    | $+$ $+$ $+$ $+$ $+$ $+$<br>$\blacksquare$<br>$\sqrt{2}$            | $\checkmark$<br>none | >1 |                              | ∎≙≎                                         |
| USD/TRY                   |  | 360TMMC.Trader1   | <b>B2B</b><br>$\overline{\phantom{a}}$       | $\Theta$<br>$\bullet$ $-$<br>$\mathbf{0}$ | $\Theta$<br>$\bullet$ $-$<br>$\Omega$                              | test                 |    |                              | $\blacktriangleright$                       |
| XAU/USD                   |  | 360TMMC.Trader1   | <b>Managed</b>                               | $\Theta$<br>$\Box$                        | $\Theta$<br>$\bullet$ $-$                                          | none                 |    | ▶ ■                          | Α Φ                                         |

<span id="page-11-2"></span>Figure 4 Instrument Control Tab

Column description:

- **Instrument:** Displays the managed instruments defined in Global Instrument Configuration panel.
- **Status Lights:** Displays the current pricing status of instrument. Green indicates pricing is on, red indicates pricing is off. Yellow applies to a specific instrument or "All Instruments", and indicates a mixed pricing state of channels beneath.
- **Managed By:** Displays which user currently "owns" the instrument. Only the user who owns an instrument can modify pricing and risk management configuration, and start or stop pricing for this instrument.
- **Mode:** Current risk management mode of the instrument is displayed. The mode can be changed by clicking on the drop down box which will bring the three different modes: B2B, Flow Hedge and Managed. The value can only be changed by the user who currently owns the instrument.
- **Skew PIPS:** Displays the manual skew in terms of absolute numbers specified in PIPS. Instrument owner can define the manual skew in terms of pips using this

column. The value defined here will be used in combination with the skew factor of the instrument. (Please see…)

- **Skew %:** Displays the manual skew in terms of percentage of the outbound spread. Instrument owner can define the manual skew in terms of percentage using this column. The value defined here will be used in combination with the skew factor of the instrument. (Please see…)
- **Scenario:** Displays the currently active risk management scenario. Instrument owner can change the scenario per instrument by clicking on the dropdown menu.
- **Start/Stop buttons:** By clicking on the green button ( ) on the respective row, admin user will start pricing all instruments or specific instrument for all channels. Likewise, clicking on the red button ( ) will stop pricing respective instrument or all instruments for all channels. Pressing start or stop in the "All Instruments" row will affects all instruments the user currently owns.
- **Ownership:** A user can take ownership for a specific instrument by clicking on icon. This button is only enabled for instruments owned by other users.
- **Configure ( ):** By clicking on icon for specific instrument (currency pair), instrument owner can open the Instrument Configuration panel. From this panel, instrument owner can configure pricing, manage reference price finding settings and risk management parameters. Clicking on this icon for all instruments opens the Global Instrument Configuration panel where admin user can configure general parameters, instruments, cross rules, tier sizes, risk management scenarios and client order handling.

# <span id="page-12-0"></span>**3.2 Add and Remove Managed Instruments**

To manage pricing and risk for a specific instrument, currency pair must be added to the list

of managed instruments in the Pricing Control tab. To do so press on the button for "All Instruments" in the "Instrument Control" tab.

| <b>Instrument Control</b> |                   |                |                                                                                       |                                                         |                      |                                                           | ⊘   |
|---------------------------|-------------------|----------------|---------------------------------------------------------------------------------------|---------------------------------------------------------|----------------------|-----------------------------------------------------------|-----|
| Instrument                | <b>Managed By</b> | Mode           | Skew PIPS                                                                             | Skew %                                                  | Scenario             |                                                           |     |
| <b>All Instruments</b>    |                   | <b>Managed</b> |                                                                                       |                                                         | none                 |                                                           | Þ   |
| <b>EUR/GBP</b>            | 360TMMC.Trader1   | Managed<br>СZ. | $ \oplus \infty $<br>$\bigcap -2.1$                                                   | $  \oplus \otimes$<br>$\left  \bullet \right $<br>- 0   | none<br>$\checkmark$ |                                                           | ⊩ ధ |
| EUR/USD                   | 360TMMC.Trader1   | <b>B2B</b>     | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>$\blacksquare$<br>$\mathbf{0}$ | $+$ $+$ $+$ $\odot$<br>10<br>$\blacksquare$             | none                 | $\triangleright \blacksquare \triangle \triangleright$    |     |
| USD/BRL                   | 360TMMC.Trader1   | <b>B2B</b>     | $\Theta$<br>▭                                                                         | $  \oplus \otimes$<br>$\blacksquare$                    | $\checkmark$<br>none | $\triangleright \blacksquare \triangle \lozenge$          |     |
| USD/TRY                   | 360TMMCTrader1    | <b>B2B</b>     | $+100$<br>$\Box$<br>$\mathbf{u}$                                                      | $+108$<br>--                                            | test<br>$\checkmark$ | $\triangleright$ $\blacksquare$ $\uparrow$ $\updownarrow$ |     |
| <b>XAU/USD</b>            | 360TMMC.Trader1   | Managed        | $\Theta$<br>a si pro                                                                  | AB <br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>e 1- | $\checkmark$<br>none | $\triangleright \blacksquare \triangle \lozenge$          |     |
| GBP/USD                   | 360TMMC.Trader1   | <b>B2B</b>     | $\Theta$<br>- OF                                                                      | $+100$<br>- کالگ                                        | $\checkmark$<br>none | $\blacktriangleright$ $\Box$ $\triangle$ $\varnothing$    |     |

<span id="page-12-1"></span>Figure 5 Open Global Instrument Configuration

This will open the "Global Instrument Configuration" dialog:

![](_page_13_Picture_1.jpeg)

<span id="page-13-2"></span>To add a new managed instrument, select base and quote currency, and press add ( ) button.

To remove a specific managed instrument, click on the remove button ( ) next to the respective currency pair in the Active Instruments list.

# <span id="page-13-0"></span>**3.3 Ownership of a Managed Instrument**

A user can only control pricing and risk management for instruments he currently owns. To take ownership of another user's instrument, user should click on the (take ownership) button in the Pricing Control tab. The button is only enabled for the instruments which are currently owned by another user.

|                 |                                     |                                                          |                                                                        |                      | ĸ                                                             |
|-----------------|-------------------------------------|----------------------------------------------------------|------------------------------------------------------------------------|----------------------|---------------------------------------------------------------|
| Managed By      | Mode                                | <b>Skew PIPS</b>                                         | Skew %                                                                 | Scenario             |                                                               |
|                 | <b>Managed</b>                      |                                                          |                                                                        | none                 | o                                                             |
| 360TMMC.Trader2 | Managed                             | 2.1 + $\Theta$<br>0                                      | $+$ $+$ $\oplus$<br>$\Box$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                      | $\theta$                                                      |
| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>c | $+$ $+$ $\oplus$<br>6<br>10                                            | $\checkmark$<br>none | ∎≏ ≎                                                          |
| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $\infty$<br>$\blacksquare$                   | $+$ $+$ $+$ $\infty$<br>$\Box$<br>$\Omega$                             | $\checkmark$<br>none | ≙≎                                                            |
| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $+$ $+$<br>c                                 | $+$ $+$ $+$ $+$ $+$<br>œ.<br>$\Omega$                                  | $\checkmark$<br>none | ∎≙≎                                                           |
| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>c | $+$ $+$ $\oplus$ $\otimes$<br>$\Omega$                                 | test<br>$\checkmark$ | ∎≙≎<br>$\mathbf{v}$                                           |
| 360TMMC.Trader1 | Managed<br>$\overline{\phantom{a}}$ | $+$ $+$ $\oplus$ $\otimes$<br>$\blacksquare$             | $+$ $+$ $\odot$ $\odot$<br>- 91-<br>$\Omega$                           | $\checkmark$<br>none | $\blacktriangleright$ $\blacksquare$ $\triangle$ $\heartsuit$ |

<span id="page-13-3"></span>Figure 7: Take over instrument ownership

The column "Managed By" shows the user who currently owns a specific instrument.

# <span id="page-13-1"></span>**3.4 Start/Stop Pricing of Instruments and Channels**

By clicking on the green button ( ) on the respective row, admin user will start pricing all instruments or specific instrument for all channels. Likewise, clicking on the red button ( ) will stop pricing respective instrument or all instruments for all channels. Pressing start or stop in the "All Instruments" row will affects all instruments the user currently owns.

| Instrument      | <b>Managed By</b> | Mode           | <b>Skew PIPS</b>           | Skew %                              | Scenario |     |
|-----------------|-------------------|----------------|----------------------------|-------------------------------------|----------|-----|
| All Instruments |                   | Managed        |                            |                                     | none     | ≎   |
| EUR/GBP         | 360TMMC.Trader2   | Managed        | $2.1 + \bigoplus$          | $+$ $+$ $\otimes$<br>-              |          | Ö   |
| EUR/USD         | 360TMMC.Trader1   | <b>B2B</b>     | AB<br>-                    | $\Theta$<br>$\blacksquare$<br>10    | none     | ⊩ ⇔ |
| GBP/USD         | 360TMMC.Trader1   | <b>B2B</b>     | $\Theta$<br>-              | $\bigoplus$<br>e –                  | none     | Ö   |
| USD/BRL         | 360TMMC.Trader1   | <b>B2B</b>     | $\Theta$<br>-              | $\Theta$<br>- OF                    | none     | ) ບ |
| USD/TRY         | 360TMMC.Trader1   | <b>B2B</b>     | $ \oplus \otimes$<br>-     | $\bigoplus$<br>$\blacksquare$<br>n. | test     | ΡΦ  |
| XAU/USD         | 360TMMC.Trader1   | <b>Managed</b> | $\Theta$<br>$\blacksquare$ | $\Theta$<br>-91-                    | none     | 10  |

<span id="page-14-0"></span>Figure 8 Start/Stop Pricing

In order to stop/start pricing specific channel of an instrument, user can right-cllick on the respective instrument. This will pop-up a window where owner can start or stop pricing of a certain stream channel.

| <b>Instrument Control</b> |                                                                                                          |                 |
|---------------------------|----------------------------------------------------------------------------------------------------------|-----------------|
| Instrument                |                                                                                                          | Managed By      |
| <b>All Instruments</b>    |                                                                                                          |                 |
| EUR/GBP                   |                                                                                                          | 360TMMC.Trader1 |
| EUR/USD                   |                                                                                                          | 360TMMC.Trader1 |
| GBP/USD                   |                                                                                                          | 360TMMC Trader1 |
| USD/BRL                   |                                                                                                          | 360TMMC.Trader1 |
| USD/TRY                   |                                                                                                          | der 1           |
| XAU/USD                   | <b>USD/TRY</b><br>Core Channel 1<br><b>Core Channel 2</b><br><b>Take Ownership</b><br>Configuration<br>o | er 1            |

<span id="page-14-1"></span>Figure 9 Start/Stop Channels

Note: A user can only start or stop his own managed instruments!

To stop **all** pricing of **all** currency pairs click on `Stop All` button on top right or on (**Stop All Instruments**) in the "All Instruments" row. No request will be quoted any more.

![](_page_14_Picture_10.jpeg)

<span id="page-14-2"></span>Figure 10 Emergency Stop

The 'Stop All" button is an emergency stop button. Pressing this button pricing will stop for all managed instruments.

# <span id="page-15-0"></span>**3.5 Pricing and Risk Management Mode**

To quickly change between pricing and risk management modes for a specific instrument, select the respective mode in the Mode column for the specific instrument.

![](_page_15_Picture_4.jpeg)

Figure 11: Select risk management mode

<span id="page-15-1"></span>The provided modes are:

- **Managed:** All currently defined risk management rules apply. Positions will potentially be accumulated.
- **B2B:** Back to back, all incoming requester orders will first be hedged, and **only accepted** if the hedge trade was successful (last look)
- **Flow Hedge:** All incoming requester orders will be accepted, but immediately hedged (without a last look).

A user can change the risk management mode for all his currently owned instruments, by selecting a risk management mode in row "All Instruments".

Instrument owner can also assign different risk management mode for different streaming channels. This can be done by right-click on the instrument name under Instrument Control panel.

|                   | EUR/GBP                                      |                   |  |
|-------------------|----------------------------------------------|-------------------|--|
| GB                | Mode                                         |                   |  |
|                   | Core Channel 1<br><b>B2B</b><br>$\checkmark$ | annel 1 $\alpha$  |  |
| Instrum           | Core Channel 2 Flow Hedge                    |                   |  |
| Instrume          | Take Ownership                               | lode              |  |
| <b>All Instru</b> | Configuration                                | Managed           |  |
| EUR/GBP           | 360TMMC.Trader1                              | <b>Flow Hedge</b> |  |

<span id="page-15-2"></span>Figure 12 Different Risk Management Assignment to Different Streams

# <span id="page-16-0"></span>**3.6 Tier Size Configuration**

To adjust the tier size for instruments, open the global instrument configuration dialog and select option "Tiers":

| <b>General Parameters</b>    | Instrument | Tier 1       | Tier <sub>2</sub> | Tier 3        | Tier <sub>4</sub> | Tier 5        | Tier <sub>6</sub> |
|------------------------------|------------|--------------|-------------------|---------------|-------------------|---------------|-------------------|
| Instruments                  | EUR/GBP    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |
| $\sum$ Tiers                 | EUR/USD    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |
| Scenarios                    | GBP/USD    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |
| <b>Cross Rules</b>           | USD/BRL    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |
| <b>Client Order Handling</b> | USD/TRY    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |
|                              | XAU/USD    | 100.00       | 200.00            | 500.00        | 1,000.00          | 5,000.00      | 0.00              |
|                              |            |              |                   |               |                   |               |                   |

<span id="page-16-2"></span>Figure 13 Define Tier Sizes

The dialog allows a user to configure up to 6 pricing tiers of arbitrary size individually for each managed instrument.

To change the size of an existing tier simply overwrite the according cell with a new value. To remove a specific tier, erase the value and leave the cell empty. Tiers can be entered in any order. The MMC GUI will display the tiers automatically sorted by size.

After clicking on `Apply` on bottom right, changes in the tier configuration are effective immediately.

Note:

The MMC stores pricing and reference price finding configurations for each cell in the configuration dialog (e.g. EUR/USD – Tier 3). This configuration will be deleted whenever the size of a specific tier is changed.

# <span id="page-16-1"></span>**3.7 Instrument Configuration**

To configure pricing, reference price finding and risk management for a specific instrument, click on the Configure button in the Instrument Control tab:

<span id="page-16-3"></span>![](_page_16_Picture_13.jpeg)

Figure 14: Open instrument configuration dialog

| Instrument Configuration EUR/USD |            |                                                                                                                                                                                           |                 |       |                                                                    |                         |                         | X |
|----------------------------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|-------|--------------------------------------------------------------------|-------------------------|-------------------------|---|
| $\vee$ Pricing                   |            |                                                                                                                                                                                           | Spread          |       |                                                                    |                         | Slippage                |   |
| > Core Channel 1                 | Tier       | Min                                                                                                                                                                                       | <b>Add Pips</b> | Max   | <b>Skew Factor</b>                                                 |                         | Percent                 |   |
| Core Channel 2                   | 1,000,000  | $\pm$                                                                                                                                                                                     | $\pm$           | ÷     | 100 %                                                              |                         |                         | Ŧ |
| <b>Reference Price Finding</b>   | 5,000,000  |                                                                                                                                                                                           |                 | ÷     | 100 %                                                              |                         |                         | Ð |
| <b>Risk Management</b>           | 15,000,000 | $\pm$                                                                                                                                                                                     | ÷               | $\pm$ | 0%                                                                 |                         |                         | ÷ |
|                                  | 25,000,000 |                                                                                                                                                                                           |                 | m     | 0%                                                                 |                         |                         |   |
|                                  |            | Round outbound quotes to decimals:<br>Allow spread less than Market<br>Allow skew to cross Mid Price<br>Allow skew to cross Opposite Side<br>Allow slippage Worse than Client Order Price |                 |       | Reference stream:<br>Percent tolerance:<br>Non executable streams: | 360TEDF.FEED<br>51<br>ヺ |                         |   |
|                                  |            |                                                                                                                                                                                           |                 |       |                                                                    |                         | <b>Discard</b><br>Apply |   |

This will open the 'Instrument Configuration' dialog for the selected instrument:

<span id="page-17-1"></span>Figure 15: Instrument Configuration Dialog

For each managed instruments users can configure:

- Pricing to create outbound prices based on reference prices with additional or fixed spread, manual skew, slippage and cutoff rules
- [Reference Price Finding](#page-22-0) to determine filter criteria to define reference prices
- [Risk Management](#page-35-0) to manage position and pricing rules

The navigation tree on the left side of the dialog allows to select Pricing or Reference Price Finding for a specific channel, and risk management for the entire instrument.

## <span id="page-17-0"></span>**3.8 Reference Price Finding**

In order to create outbound prices, MMC admin should first define the filter criteria for inbound streams per specific stream channel and tier size.

Initially no provider is selected to create inbound and as a result, outbound prices. To get inbound prices one has to select **Reference Price Finding** and choose the desired provider(s).

There are various strategies and parameters to filter inbound quotes, and to calculate a reference price for each pricing tier:

| Strategy     | Parameter 1        | Parameter 2       |
|--------------|--------------------|-------------------|
| Best Price   | Minimum Quote Size | Minimum Providers |
| Deep Average | Minimum Quote Size | Levels            |
| Deep Worst   | Minimum Quote Size | Levels            |
| VWAP Average | Minimum Quote Size | Maximum Providers |

| Strategy       | Parameter 1        | Parameter 2       |
|----------------|--------------------|-------------------|
| VWAP Worst     | Minimum Quote Size | Maximum Providers |
| Fix Core Price | Bid                | Ask               |

**Best Price** strategy tries to find the best single quote, of at least minimum quote size. To find a reference price, there must be at least 'minimum providers' number of quotes, of minimum quote size available.

This strategy is ideal for B2B hedging when maximum fill ratio is most important (e.g. in case of RFS client orders). When Best Price is used, B2B hedge orders will be placed as limit orders, with the original reference price. This is to ensure that both trader and sales spread will be preserved.

**Deep Average and Deep Worst** consider the top n (levels) quotes in the book, of at least minimum size. Average will calculate a VWAP price of the top n levels, whereas worst will simply pick the nth level down from top of the book. The number of levels can be configured via the Min Providers/Ask column. A level is considered as a unique price from a specific provider. In other words, identical prices from two different providers are considered as two levels.

| Level | Provider | Bid    | Ask    | Provider |
|-------|----------|--------|--------|----------|
| 1     | A        | 1.1240 | 1.1242 | B        |
| 2     | B        | 1.1240 | 1.1243 | B        |
| 3     | B        | 1.1239 | 1.1243 | A        |
| 4     | C        | 1.1238 | 1.1244 | C        |
| 5     | D        | 1.1237 | 1.1245 | D        |

Example book:

The example shows on the bid side for the first three levels: 1.1240, 1.1240, 1.2339.

Bid levels 1 and 2 are treated as different level because they are from different providers, even though the price is the same.

**VWAP Average and VWAP Worst** will calculate a VWAP reference price by considering all quotes of at least minimum quote size. The required quantity for the VWAP price is in general identical with the pricing tier size. E.g. for a 5m tier, the VWAP algorithm will try to find the best VWAP price for 5m quantity. Only one quote from each provider will be used in the VWAP calculation. The maximum number of providers in the VWAP calculation can be limited.

The strategy will not return a reference price if the total quantity of quotes available (with minimum quote size) is less than the required amount (pricing tier size).

VWAP is a good strategy for running positions, but also for B2B hedging when partially filling of client orders is acceptable. With VWAP pricing, B2B hedge orders will be placed as limit orders with the requested price of the client order. In other words, in the worst case the hedge order will be filled with the client order price, and both trader and sales spread are lost.

**Fix Core Price** will directly define a bid and ask outbound price for the selected channel and can be used for pegged currencies.

| Instrument Configuration EUR/USD |  |
|----------------------------------|--|
|                                  |  |

| Pricing                        | Tier | <b>Providers</b>                         | Hedge        | <b>Strategy</b>                                                             | Min Ouote Size/Bid | Min Providers/Ask |
|--------------------------------|------|------------------------------------------|--------------|-----------------------------------------------------------------------------|--------------------|-------------------|
| $\vee$ Reference Price Finding |      | 360TEDF.FEED 360TEXEC.T =/<br>1,000,000  | <b>Hedge</b> | <b>Best Price</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!               | 1,000,000          |                   |
| Core Channel 1                 |      | 5,000,000<br>Barclays BARX.DEMO RBS =    | Hedge        | VWAP Average $\vee$                                                         | 2,000,000          |                   |
| <b>Core Channel 2</b>          |      | 15,000,000<br>Barclays BARX.DEMO BOA = ∕ | Hedge        | $\left\langle \right\rangle$ Deep Average $\left\langle \vee \right\rangle$ | 1,000,000          |                   |
| <b>Risk Management</b>         |      | 25,000,000                               | Hedge        | <b>Best Price</b><br>$\sim$                                                 |                    |                   |

<span id="page-19-1"></span>Figure 16: Reference Price Finding rules editor

Click on the activate button under tier label (e.g. 5m) to enable or disable a specific tier. In the example above the 25m tier is currently disabled.

# <span id="page-19-0"></span>**3.9 Additonal and Fixed Spread**

Configuring spreads can be done in various ways:

- **Fix Pips:** Fixed outbound spread defined in terms of PIPS independent of the inbound spread
- **Add to Inbound Spread:** Relative outbound spread based on percentage of inbound spread
- **Add Pips:** Outbound spread is always n PIPS wider than the inbound spread

|                                  |            |                                              |                   |                        |                         |                               |                         | X |
|----------------------------------|------------|----------------------------------------------|-------------------|------------------------|-------------------------|-------------------------------|-------------------------|---|
| Instrument Configuration EUR/GBP |            |                                              |                   |                        |                         |                               |                         |   |
| $\vee$ Pricing                   |            |                                              | Spread            |                        |                         |                               | Slippage                |   |
| > Core Channel 1                 | Tier       | VO)<br>Min                                   | Add to Inb $\vee$ | $\bigtriangledown$ Max | <b>Skew Factor</b>      |                               | Percent                 |   |
| Core Channel 2                   | 1,000,000  | $\mathbf{Z}$                                 | 100               | 20<br>Ð                | 0%                      |                               |                         | D |
| <b>Reference Price Finding</b>   | 4,000,000  |                                              |                   |                        | 0%                      |                               |                         | Ð |
| <b>Risk Management</b>           | 10,000,000 |                                              |                   | $\pm$                  | 0%                      |                               |                         | Ð |
|                                  | 15,000,000 |                                              |                   |                        | 0%                      |                               |                         |   |
|                                  | 25,000,000 |                                              |                   |                        | 0%                      |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |
|                                  | VO         | Round outbound quotes to decimals:           | $-4$ +            |                        | Reference stream:       | 360TEDF.FEED                  |                         |   |
|                                  |            | Allow spread less than Market                |                   |                        | Percent tolerance:      | $\mathbf{z}$                  |                         |   |
|                                  |            | Allow skew to cross Mid Price                |                   |                        | Non executable streams: | 360TEXEC.TEST 360TEDF.FEED =/ |                         |   |
|                                  |            | Allow skew to cross Opposite Side            |                   |                        |                         |                               |                         |   |
|                                  |            | Allow slippage Worse than Client Order Price |                   |                        |                         |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |
|                                  |            |                                              |                   |                        |                         |                               | <b>Discard</b><br>Apply |   |
|                                  |            |                                              |                   |                        |                         |                               |                         |   |

<span id="page-19-2"></span>Figure 17 Defining Outbound Spread

**Fixed** spreads are calculated around the mid-price of Inbound. When chosen, the system applies the defined fixed spread or, if wider, the inbound spread.

If wished, select the checkbox "Allow Spread less than Market". A fixed spread of "0" means that the inbound spread is used.

**Add to Inbound Spread** adds the defined percentage on the inbound spread.

**Add Pips** adds the specified number of pips to the inbound spread.

Independent of the chosen spread mode, the outbound spread can always be limited by a minimum and a maximum spread in terms of pips.

# <span id="page-20-0"></span>**3.10Manual Skew**

Manual Skew can be set both in percentage of inbound spread, and in absolute numbers specified in PIPS. Both options can be used individually but also in combination. Applying manual skew is a combination of **basic instrument wide skew settings**, and "**Skew Factors**", which distribute the selected base value to each pricing tier.

Basic skew settings can be adjusted in the "Instrument Control" panel in columns "Skew PIPS" and "Skew %".

| łe            |              | <b>Skew PIPS</b> |          |                           | Skew % | <b>Sce</b> |                 |                |  |
|---------------|--------------|------------------|----------|---------------------------|--------|------------|-----------------|----------------|--|
| naged         | $\checkmark$ |                  |          |                           |        |            |                 | n <sub>c</sub> |  |
| naged         | $\checkmark$ |                  | 4        | $\boxplus \boxtimes$<br>÷ |        | 40         | $\Theta$        | n              |  |
|               |              |                  | $\bf{0}$ | $+$ $+$ $\circ$           |        | 10         | $+$ $+$ $\circ$ |                |  |
| B             | $\checkmark$ |                  | $\bf{0}$ | $ \oplus \otimes$<br>÷    |        | 0          | $\Theta$<br>÷   | nc             |  |
| B             | $\checkmark$ |                  | 0        | $\Theta$<br>÷             |        | o          | $\Theta$<br>÷   | nc             |  |
| B             | $\checkmark$ |                  | $\bf o$  | $ \bigoplus$<br>٠         |        | $\bf{o}$   | $\Theta$        | te             |  |
| <b>Inaged</b> | $\vee$       |                  | $\bf{0}$ | H(X)<br>٠                 |        | 0          | $+ x$           | nc             |  |

<span id="page-20-1"></span>Figure 18: Basic skew settings

Clicking on the inner `+` and `-` buttons increments and decrements PIPS in steps of 0.1, and percentage in steps of 1. Clicking on the outer `+` and `-` buttons buttons increments and decrements PIPS in steps of 1, and percentage in steps of 10.

#### Please note: Any changes in these values is immediately effective.

The selected values can be distributed to each pricing tier by "Skew Factors". A skew factor of 10% for the 1m tier means, only 10% of the skew value chosen in the pricing control panel will be applied to this tier. The maximum skew factor is 100% which means, whatever skew values are chosen in the pricing control panel, will be fully applied to this tier.

To adjust skew factors for a specific instrument open the "Instrument Configuration" dialog, and chose option "Pricing":

![](_page_20_Figure_15.jpeg)

<span id="page-20-2"></span>Figure 19: Tier specific skew factors

In order to allow skewing to cross the mid-rate or even the opposite side, user MUST enable "Allow Skew to cross Mid Price" and "Allow Skew to cross Opposite Side" options, respectively.

![](_page_21_Figure_3.jpeg)

<span id="page-21-1"></span>Figure 20 Allowing Skew to cross Mid Price/ Opposite Side

Click on **Apply** to confirm and apply the defined rule changes.

# <span id="page-21-0"></span>**3.11 Rounding Outbound Quotes**

For each pricing channel of a managed instrument, users can configure the decimal places for outbound quote. By default, the quotes are published with the spot rate precision of the respective currency.

From Pricing panel under Instrument Configuration, user can reduce the outbound spot rate precision.

| Instrument Configuration EUR/GBP |                                   |                                                                                                                                                                                           |                     |              |     |                                                                    |                                                     |         |          |  |
|----------------------------------|-----------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|--------------|-----|--------------------------------------------------------------------|-----------------------------------------------------|---------|----------|--|
| Pricing                          |                                   |                                                                                                                                                                                           | Spread              |              |     |                                                                    |                                                     |         | Slippage |  |
| > Core Channel 1                 | Tier                              | <b>KO</b><br>Min                                                                                                                                                                          | <b>Fixed (PIPS)</b> | VO)          | Max | <b>Skew Factor</b>                                                 |                                                     | Percent |          |  |
| Core Channel 2                   | 1,000,000                         |                                                                                                                                                                                           | 8                   |              | 18  | 0%                                                                 |                                                     |         | 17       |  |
| <b>Reference Price Finding</b>   | 5,000,000                         |                                                                                                                                                                                           |                     | ÷            |     | 0 %                                                                |                                                     |         | 100      |  |
| <b>Risk Management</b>           | 10,000,000                        |                                                                                                                                                                                           | ÷                   | $\pm$        | Ð   | 0 %                                                                |                                                     |         |          |  |
|                                  | 15,000,000                        |                                                                                                                                                                                           |                     |              |     | 0%                                                                 |                                                     |         |          |  |
|                                  | 25,000,000                        |                                                                                                                                                                                           |                     |              | ÷   | 0%                                                                 |                                                     |         |          |  |
|                                  | vo<br>$\circ$<br>KO)<br><b>VO</b> | Round outbound quotes to decimals:<br>Allow spread less than Market<br>Allow skew to cross Mid Price<br>Allow skew to cross Opposite Side<br>Allow slippage Worse than Client Order Price |                     | - 9<br>$4 -$ |     | Reference stream:<br>Percent tolerance:<br>Non executable streams: | 360TEDF.F<br>360TBANK.TEST 360TEDF.FEED 360TEXEC F/ |         |          |  |

<span id="page-21-2"></span>Figure 21: Reducing outbound spot rate precision

For example, if `Round Outbound Quotes to 4 decimal places` is configured for EURUSD pricing, the outbound quotes of 1,11342 – 1,11347 would be rounded to 1,11340 – 1.11350.

# <span id="page-22-0"></span>**3.12Reference Stream and Percent Tolerance**

Instrument owner can define a reference stream per instrument on channel level to filter out certain inbound quotes by using the reference stream`s quote as benchmark. Together with defined percent tolerance, MMC will calculate a price range that can be used for price construction and will consider any quote outside this as off-market and therefore filter those quotes out.

| $\sqrt{2}$<br>Instrument Configuration EUR/USD |                         |                                    |                 |     |                                          |                         |              |          |    |
|------------------------------------------------|-------------------------|------------------------------------|-----------------|-----|------------------------------------------|-------------------------|--------------|----------|----|
| $\vee$ Pricing                                 |                         |                                    | Spread          |     |                                          |                         |              | Slippage |    |
| > Core Channel 1                               | Tier                    | Min                                | <b>Add Pips</b> |     | Max                                      | <b>Skew Factor</b>      |              | Percent  |    |
| Core Channel 2                                 | 1,000,000               |                                    |                 |     |                                          | 100 %                   |              |          |    |
| <b>Reference Price Finding</b>                 | 5,000,000               |                                    |                 |     | v.                                       | 100 %                   |              |          |    |
| <b>Risk Management</b>                         | 15.000.000              | $+$                                |                 | $+$ | --                                       | $0\%$ (                 |              |          | 62 |
|                                                | 25,000,000              | - 1                                |                 |     |                                          | 0%                      |              |          |    |
|                                                |                         |                                    |                 |     |                                          |                         |              |          |    |
|                                                | $\mathcal{U}(\cdot)$    | Round outbound quotes to decimals: |                 |     | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Reference stream:       | 360TEDF.FEED |          |    |
|                                                |                         | Allow spread less than Market      |                 |     |                                          | Percent tolerance:      | 0.02         |          |    |
|                                                | $\overline{\mathsf{v}}$ | Allow skew to cross Mid Price      |                 |     |                                          | Non executable streams: | ₹∕           |          |    |

<span id="page-22-1"></span>Figure 22Reference Stream and Percent Tolerance

For example, as in *Figure 21*, user defined Reference Stream as 360T`s Essential Data Feed and tolerance level as 0.02%.

EURUSD bid/ask from 360T EDF is 1.13250 / 1.13260.

The valid inbound bid/ask is calculated with below formula:

*(1-x%)\*ReferenceBid <= Valid Inbound Bid <= (1+x%)\*ReferenceBid*

*(1-x%)\*ReferenceAsk<= Valid Inbound Ask <= (1+x%)\*ReferenceAsk*

*Where x is the configured Percent Tolerance.*

Using 360T EDF as reference, valid bid and ask range is calculated as below:

- (1- 0.0002)\*1.13250 = 1.13227 <= Valid Inbound Bid <= (1+0.0002)\*1.13260 = 1.13273
- (1- 0.0002)\*1.13260 = 1.13237 <= Valid Inbound Ask <= (1+0.0002)\*1.13260 = 1.13283

| Provider | Bid     | Provider | Ask     |
|----------|---------|----------|---------|
| E        | 1.13285 | B        | 1.13225 |
| A        | 1.13270 | C        | 1.13272 |
| C        | 1.13265 | A        | 1.13280 |
| D        | 1.13220 | D        | 1.13282 |
| B        | 1.13210 | E        | 1.13290 |

Below is list of bid/ask quotes from different providers.

As per Reference Stream and tolerance definition, for bid 1.13285, 1.13270 and 1.13210; for ask 1.13225 and 1.13290 will be not used for any price construction.

## <span id="page-23-0"></span>**3.13Non-Executable Streams**

When creating a reference price, it is possible to use streaming prices that are not executable. As an example, 360T`s Essential Data Feed can be used as a source to create EURUSD outbound prices but when it comes to managing the risk, 360T EDF is not a source where MMC owner can hedge back onto.

In order to use the non-executable streams in price construction while excluding them in hedging, instrument owner can configure these streams as non-executable within Pricing panel under Instrument Configuration dialog.

| Instrument Configuration EUR/USD |            |                                       |                 |                                          |                                                      |                         |                               |          | X |
|----------------------------------|------------|---------------------------------------|-----------------|------------------------------------------|------------------------------------------------------|-------------------------|-------------------------------|----------|---|
|                                  |            |                                       |                 |                                          |                                                      |                         |                               |          |   |
| $\vee$ Pricing                   |            |                                       | Spread          |                                          |                                                      |                         |                               | Slippage |   |
| Core Channel 1                   | Tier       | Min                                   | <b>Add Pips</b> |                                          | Max                                                  | <b>Skew Factor</b>      |                               | Percent  |   |
| Core Channel 2                   | 1,000,000  | ÷<br>$\sim$                           | -               | $\pm 1$                                  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\equiv$ | 100 %                   |                               | -        |   |
| <b>Reference Price Finding</b>   | 5,000,000  |                                       |                 |                                          |                                                      | 100 %                   |                               |          |   |
| <b>Risk Management</b>           | 15,000,000 | ÷                                     |                 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | $\pm$                                                | 0%                      |                               |          |   |
|                                  | 25,000,000 |                                       |                 |                                          |                                                      | 0 %                     |                               |          |   |
|                                  |            |                                       |                 |                                          |                                                      |                         |                               |          |   |
|                                  |            | VO Round outbound quotes to decimals: |                 | $4 +$                                    |                                                      | Reference stream:       | 360TEDF.FEED                  |          |   |
|                                  |            | Allow spread less than Market         |                 |                                          |                                                      | Percent tolerance:      | $0.02 -$<br>$+$               |          |   |
|                                  | M.         | Allow skew to cross Mid Price         |                 |                                          |                                                      | Non executable streams: | 360TEDF.FEED 360TEXEC.TEST =/ |          |   |
|                                  |            |                                       | $-1$            |                                          |                                                      |                         |                               |          |   |

<span id="page-23-1"></span>Figure 23 Configure Non-Executable Streams

Clicking on icon will open a pop-up window where all streaming providers appear and can be chosen. After selecting the desired stream as non-executable and clicking on save from the first channel, **the configuration will be immediately effective for all stream channels.**

**Please note that, non-executable streams can only work in combination with Managed or Flow Hedge risk management modes. When risk management is selected as B2B mode, defining a non-executable stream effectively means removing that stream from price construction too, since MMC has to match the inbound with an outbound price to attempt hedge trade before accepting the client order.**

| 360TEDF.FEED<br>$\mathcal{S}$ | 360TEXEC.TEST<br>▽⊂ |
|-------------------------------|---------------------|
| ALBARAKA.FEED                 | BOAL.DEMO           |
| Barclays BARX.DEMO            | COBA.DEMO           |
| PEBANK_APAC.TEST              | RBS.LND.DEMO        |
|                               |                     |
|                               |                     |

<span id="page-23-2"></span>Figure 24 Non-executable Stream Selection

# <span id="page-24-0"></span>**3.14Sweepable and Full Amount Streams**

Each pricing channel can be configured to generate either sweepable or full amount/exclusive pricing. Full amount streams allow the market makers to stream prices which can be executed exclusively by one maker i.e. the client full order amount will be executed by a single liquidity provider while sweepable streams allows the maker to fill a portion of an order that might be executed by multiple providers.

This is mostly relevant to market makers streaming prices to their clients executing spot on 360T Supersonic. All RFQ requests are by default priced as full amount.

This can be done by your admin users who have access to `Stream Group Mapping` within the Business Configuration Tool.

# <span id="page-24-1"></span>**3.15Managing Synthetic Crosses**

To define rules how to strip cross currency pairs, open the 'Global Instrument Configuration Dialog' and select option "Cross Rules":

![](_page_24_Figure_8.jpeg)

<span id="page-24-2"></span>Figure 25: Cross Currency Pair Configuration

Each row in this dialog defines a rule how to handle a specific cross currency pair. New rules can be added by clicking on the "+" button in the center. The button can be used to delete a rule.

Each rule defines:

- **Active**: if ticked, the crossing for the defined currency is active.
- **Base Ccy and Quote Ccy**: defines the currency or currency pair which should be cross-calculated using managed currency pairs. One of the two currencies can be defined with a wildcard "\*"
- **Cross:** defines the managed currency pair used to cross over either base or quote currency.
- **Use Notional Ccy**: if checked, the notional is specified by the quote currency quantity. For example, for EURTRY 1m request, when use notional ccy selected, cross legs would be 1m EUR of EURUSD and 1m EUR equivalent USD of USDTRY.

When unchecked, USDTRY cross would be executed in TRY notional (TRY amount = opposite amount of EURTRY).

• **Arrow buttons** can be used to move a rule up and down.

#### **Note:**

Rules will be matched from top down. Therefore wildcard rules shall be rather placed at the end of the list!

#### **Important Note:**

The order of base currency and quote currency while setting cross rules is important. MMC does not support any inconventional currency pair setting. For example, if rule is set as USD/EUR and not as EUR/USD, the rule won`t be matched with incoming request and hence ignored.

Please contact CAS team to receive latest currency ranking to set the cross rules with right order of base/quote currency.

# <span id="page-25-0"></span>**3.16Pricing of Unmanaged Instruments**

By default the MMC will only price managed instruments. Users can configure the MMC to price unmanaged instruments too. To do so, open the "Global Instrument Configuration" dialog, and select option "General Parameters":

| <b>Global Instrument Configuration</b> |                                                                   | $\times$                       |  |
|----------------------------------------|-------------------------------------------------------------------|--------------------------------|--|
| > General Parameters                   |                                                                   |                                |  |
|                                        | Allow time slippage for Managed/Flow Hedged instruments (ms):     |                                |  |
| Instruments                            | Allow time slippage for B2B instruments (ms):<br>0.6 <sub>1</sub> |                                |  |
| Tiers                                  | <b>Price Unmanaged instruments</b>                                |                                |  |
| Scenarios                              | VO Price Unmanaged Crosses                                        |                                |  |
| <b>Cross Rules</b>                     |                                                                   |                                |  |
| <b>Client Order Handling</b>           |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   |                                |  |
|                                        |                                                                   | <b>Discard</b><br><b>Apply</b> |  |

<span id="page-25-1"></span>Figure 26: Pricing unmanaged instruments

If option "Price Unmanaged Instruments" is enabled, then MMC will provide prices, and execute client orders, for any instrument where liquidity is available. Please note that, if the requested instrument is configured in Cross Rules, then it will get priced as per cross rule.

If option "Price Unmanaged Cross Currency" is enabled, then MMC will provide prices, and execute client orders, for any synthetic cross rate where one of the two legs is not managed by the MMC.

Example:

• EUR/USD is a managed instrument

• User configured a rule for synthetic cross rates for EUR/\* cross over to EUR/USD

If option "Price Unmanaged Cross Currency" is enabled together with "Price Unmanaged Instruments", the MMC will provide prices, and execute client orders, for any instrument with base currency EUR.

### **Note:**

To price unmanaged instruments, the entire bank basket will be used.

# <span id="page-26-0"></span>**3.17Configuring Time Slippage**

By applying time slippage, the MMC has more time (if required) to fill a hedge order, which should improve the overall hedge order fill ratio. Time slippage is off by default. To configure time slippage, open the "Global Instrument Configuration" dialog, and select option "General Parameters":

| Global Instrument Configuration |                                                                               | X     |
|---------------------------------|-------------------------------------------------------------------------------|-------|
| > General Parameters            | V Allow time slippage for Managed/Flow Hedged instruments (ms):<br>0.9<br>$+$ |       |
| Instruments                     | Allow time slippage for B2B instruments (ms):<br>$1.1 +$<br>VO)               |       |
| Tiers                           | <b>Price Unmanaged instruments</b>                                            |       |
| Scenarios                       | <b>Price Unmanaged Crosses</b>                                                |       |
| <b>Cross Rules</b>              |                                                                               |       |
| <b>Client Order Handling</b>    |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 |                                                                               |       |
|                                 | <b>Discard</b>                                                                | Apply |

<span id="page-26-2"></span>Figure 27: Time slippage configuration

Time slippage can be adjusted separately for B2B and flow hedged/managed risk managed mode in steps of 1/10th of a second. Time slippage will only be applied in case the hedge order cannot be filled immediately.

# <span id="page-26-1"></span>**3.18Configuring Price Slippage**

Price slippage occurs, when a client order limit price is worse (from a trader perspective) than the currently published MMC outbound price. This problem typically occurs in fast moving markets, and/or with clients having a high latency between their end and the MMC pricing server.

The following image shows an example for slippage due to a widening market:

- 1. Inbound price spread increased between time T and T+1
- 2. Outbound bid and offer follow inbound bid and offer from time T to T+1
- 3. Client hits at time T+1 the old quote from time T
- 4. The MMC margin at time T+1 is in this case reduced by price slippage

![](_page_27_Figure_2.jpeg)

<span id="page-27-0"></span>Figure 28: Example for price slippage

Without accepting price slippage there are two possible results in this situation:

- 1. In case of B2B risk management mode, the client order will be most likely rejected, because no suitable quote to hedge the order can be found
- 2. In case of flow hedge/managed modes, the MMC will simply reject the client order to fully protect the MMC margin

By accepting price slippage, traders can give clients (if required) a discount on their trader margin, to improve client order fill ratio, and reduce rejections.

Price slippage applies differently for B2B and flow hedge/managed risk management modes. In case of B2B mode, price slippage affects the B2B hedge order limit price. By pro-actively worsening the limit price, a hedge order has a higher chance of getting filled in the market. Price slippage will only be applied when necessary. The MMC will always try to execute a hedge order at the best available price (automatic price improvement).

In case of flow hedge and managed modes, price slippage controls if a client order will be accepted or rejected. It acts in this case as a price and profit protection. The price of any client order hitting the MMC will be compared to the current outbound price (in the example above the price at time T+1). Client orders will only be accepted, if their limit price is within the acceptable slippage as defined by the trader.

To configure price slippage for a specific instrument, open the "Instrument Configuration" dialog, and select option "Pricing". Price slippage can be applied for each pricing tier in column "Slippage". The options are either in percent of current margin, or in absolute PIPS.

| Instrument Configuration EUR/USD |            |                                                                                                                                                                                           |                         |       |                                                                    |                                                           |                         |                       |
|----------------------------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|-------|--------------------------------------------------------------------|-----------------------------------------------------------|-------------------------|-----------------------|
| $\vee$ Pricing                   |            |                                                                                                                                                                                           | Spread                  |       |                                                                    |                                                           | Slippage                |                       |
| > Core Channel 1                 | Tier       | Min                                                                                                                                                                                       | <b>Add Pips</b>         | Max   | <b>Skew Factor</b>                                                 |                                                           | Percent                 | $\boldsymbol{\wedge}$ |
| Core Channel 2                   | 1,000,000  |                                                                                                                                                                                           |                         | Ð     | 29 %                                                               |                                                           | <b>Pips</b>             |                       |
| <b>Reference Price Finding</b>   | 5,000,000  | $\pm$                                                                                                                                                                                     | $\overline{2}$          | Ð     | 42%                                                                |                                                           | $\sqrt{}$ Percent       |                       |
| <b>Risk Management</b>           | 15,000,000 | $\ddot{}$                                                                                                                                                                                 | $\overline{\mathbf{3}}$ | ÷     | 60%                                                                |                                                           | 50                      |                       |
|                                  | 25,000,000 | ÷                                                                                                                                                                                         |                         | Ð     | 75%                                                                |                                                           |                         |                       |
|                                  | VO<br>VC   | Round outbound quotes to decimals:<br>Allow spread less than Market<br>Allow skew to cross Mid Price<br>Allow skew to cross Opposite Side<br>Allow slippage Worse than Client Order Price |                         | $4 +$ | Reference stream:<br>Percent tolerance:<br>Non executable streams: | 360TEDF.FEED<br>$0.02 +$<br>360TEDF.FEED 360TEXEC.TEST =/ |                         |                       |
|                                  |            |                                                                                                                                                                                           |                         |       |                                                                    |                                                           | <b>Discard</b><br>Apply |                       |

<span id="page-28-1"></span>Figure 29: Price slippage configuration

By configuring slippage in PIPS, there is always the risk that the actual margin is less than the allowed price slippage. The MMC will automatically limit slippage at the current margin, to prevent traders from accidentally accepting client orders at a loss. This safety measure can be disabled by ticking option "Allow Slippage Worse than Client Order Price".

Configuring price slippage in percent means, a certain percentage of the current margin can be given up to accept client order. If for example the current margin (difference between inbound and outbound price) is 2 PIPS, and the trader configures 50%, he would accept a price slippage of up to 1 PIP. Configuring a price slippage of 100% means, if necessary the trader is willing to give up the entire margin to fill or accept the client order.

By default, price slippage is set to zero, and slippage worse than client order price is not enabled. If margin is set to zero and "Allow Slippage Worse than Client Order Price" option is not enabled, then the slippage defined in practice is redundant.

## <span id="page-28-0"></span>**3.19Quote Filtering**

Problems can arise if the MMC calculates an outbound price based on unreliable inbound quotes such as stale quotes. A quote can be considered as stale if it wasn't updated after the market moved. A B2B hedge order based on a stale quote will often get rejected.

The problem can become more severe with flow hedging and managed positions. In these modes the MMC will accept any client order within an acceptable price slippage range. A subsequent auto-hedge order might result into a financial loss!

Another issue with invalid quotes is revaluation of open positions. A user might set a stop loss rule for an open position. If such a rule is triggered by an invalid quote the position might be closed at an unfavourable price (**because the MMC uses market orders to close positions).**

Quote filters can help to reduce such risks!

To enable quote filtering for a specific instrument open the "Instrument Configuration" dialog, select Risk Management/General Parameters and enable `Remove quotes older than ms)` option

| Pricing                        | <b>General Parameters</b>      |                                                                     |  |  |  |  |  |
|--------------------------------|--------------------------------|---------------------------------------------------------------------|--|--|--|--|--|
| <b>Reference Price Finding</b> | Maximum Hedge Order Size:      | 10,000,000.00<br>$\begin{pmatrix} - & 1,000 \end{pmatrix}$<br>$\pm$ |  |  |  |  |  |
| $\vee$ Risk Management         | Remove quotes older than (ms): |                                                                     |  |  |  |  |  |
| > General Parameters           |                                |                                                                     |  |  |  |  |  |
| <b>Position Rules</b>          |                                |                                                                     |  |  |  |  |  |
| <b>Pricing Rules</b>           |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |
|                                |                                |                                                                     |  |  |  |  |  |

<span id="page-29-0"></span>Figure 30: Quote filter settings

In example above any EUR/USD quote older than 1,000ms will be removed from the inbound stream. Removed quotes will be added back into the book at the next quote update.

Users can monitor the effect of the quote filter settings in the instrument "Pricing Details" dialog. To open this dialog press on the "Details…" link in the Liquidity Details panel. Removed quotes will be displayed in the "Raw Inbound" view as strikethrough prices. See [4.3](#page-32-0) for more information about the Pricing Details.

| Pricing Details // Core Channel 1@EUR/USD |  |  |  |  |
|-------------------------------------------|--|--|--|--|
|-------------------------------------------|--|--|--|--|

| > Raw Inbound    | Snapshot: Wed, 16 Nov 2022 16:44:50 UTC |                  |                |         |         |                 |                  |  |  |
|------------------|-----------------------------------------|------------------|----------------|---------|---------|-----------------|------------------|--|--|
| Filtered Inbound |                                         |                  |                |         |         |                 |                  |  |  |
| Outbound         |                                         | <b>Bid</b>       |                |         | Ask     |                 |                  |  |  |
|                  |                                         | Provider         | Quantity       | Price   | Price   | Quantity        | Provider         |  |  |
|                  | 1 <sub>m</sub>                          | <b>COBA.DEMO</b> | 4m             | 1.03914 | 4.03962 | 4m              | BOAL.DEMO        |  |  |
|                  |                                         | BOAL.DEMO        | $\pm m$        | 4.03910 | 4.03966 | $+m$            | COBA DEMO        |  |  |
|                  |                                         | 360TEDF.FEED     | 1 <sub>m</sub> | 1.03907 | 1.03969 | 1 <sub>m</sub>  | 360TEDF.FEED     |  |  |
|                  |                                         | 360TEDF.FEED     | <b>500k</b>    | 1.03907 | 1.03969 | <b>500k</b>     | 360TEDF.FEED     |  |  |
|                  |                                         | <b>COBA DEMO</b> | 2m             | 4.03836 | 1.04044 | 2m              | <b>COBA DEMO</b> |  |  |
|                  |                                         | 360TEDF.FEED     | 5m             | 1.03801 | 1.04075 | 5m              | 360TEDF.FEED     |  |  |
|                  |                                         | COBA.DEMO        | 4m             | 4.03784 | 4.04096 | 4 <sub>ff</sub> | COBA DEMO        |  |  |
|                  |                                         | <b>COBA DEMO</b> | 8m             | 1.03732 | 1.04148 | 8m              | <b>COBA DEMO</b> |  |  |
|                  |                                         | COBA DEMO        | 16m            | 1.03602 | 1.04278 | 46m             | <b>COBA DEMO</b> |  |  |
|                  |                                         | <b>Bid</b>       |                |         | Ask     |                 |                  |  |  |
|                  |                                         | Provider         | Quantity       | Price   | Price   | Quantity        | Provider         |  |  |
|                  | 5 <sub>m</sub>                          | COBA DEMO        | 4m             | 1.03914 | 4.03962 | $4m$            | BOAL, DEMO       |  |  |
|                  |                                         | BOAL.DEMO        | $+m$           | 4.03910 | 4.03966 | $+m$            | COBA DEMO        |  |  |
|                  |                                         | 360TEDF.FEED     | 1 <sub>m</sub> | 1.03907 | 1.03969 | 1 <sub>m</sub>  | 360TEDF.FEED     |  |  |
|                  |                                         | 360TEDF.FEED     | <b>500k</b>    | 1.03907 | 1.03969 | <b>500k</b>     | 360TEDF.FEED     |  |  |
|                  |                                         | COBA DEMO        | 2m             | 4.03836 | 4.04044 | 2m              | <b>COBA DEMO</b> |  |  |
|                  |                                         | 360TEDF.FEED     | 5m             | 1.03801 | 1.04075 | 5m              | 360TEDF.FEED     |  |  |

<span id="page-29-1"></span>Figure 31: Monitor filtered quotes

# <span id="page-30-0"></span>**3.20PTMM**

For all requests priced by the MMC, a PTMM (Pre-Trade Mid-Market) rate is published across all FX derivative products and available to the client at the time of quoting as well as post execution.

The PTMM is a non-skewed midrate driven of the market maker liquidity. It is calculated based of the MMC spot "Filtered Inbound" prices (of the respective Channel associated to the client) and the mid-price of the forward points for the requested settlement date.

When the RFQ is quoting, the PTMM is recalculated once a new quote is updated and is also captured at the time of execution.

# <span id="page-31-0"></span>**4 MONITOR PRICING**

# <span id="page-31-1"></span>**4.1 The Liquidity Details Panel**

The Pricing Monitor tab provides various information for each managed instrument and pricing channel:

- Pricing tier size
- Inbound price details
- Outbound price details

Each row contains information for a specific instrument.

![](_page_31_Figure_9.jpeg)

<span id="page-31-3"></span>Figure 32: Liquidity Details Panel

The following details are shown:

- **Tier**: Shows the tier size.
- **Inbound details:** Displays inbound price and spread details for the currently selected channel.
- **Outbound details:** Displays outbound price, spread, and skew (marked with an arrow) details for the currently selected channel.

# <span id="page-31-2"></span>**4.2 Inbound and Outbound Pricing Tier Monitor**

The panel shows for each channel and tier detailed inbound and outbound price information:

![](_page_31_Figure_17.jpeg)

<span id="page-31-4"></span>Figure 33: Pricing Tier monitor

For each currency pair multiple pricing channels can be configured. Each pricing channel can have different spread/skew settings and Reference Price Finding rules.

The assignment of your requesting clients to the different pricing channel prices has to be done by 360T support.

The pricing tier monitor shows one row per pricing tier (e.g. 1m, 5m, …).

![](_page_32_Picture_5.jpeg)

Each row shows the following information:

- Tier size (e.g. 1m)
- Big Figure
- PIPS bid and ask
- Spread blue bar
- Skew orange number shows size and orange arrow shows direction

# <span id="page-32-0"></span>**4.3 Pricing Details Dialog**

Click on the icon to get a snapshot of current raw, inbound and outbound prices including all details.

A user can select one of the following option in the navigation tree on the left side:

- Raw Inbound to see all available quotes from all liquidity and market data providers in the bank basket.
- Filtered Inbound quotes selected by Reference Price Finding
- Outbound outbound rates for each pricing tier

To see all available quotes from all liquidity and market data providers in bank basket click on "Raw Inbound". Bid quotes are shown on the left, ask quotes are shown on the right. Bank basket can be configured via 360T`s Bank Basket administration panels. For further detail, please contact CAS or your Sales representative.

In "Raw Inbound" panel, bid quotes are sorted in descending order by price, with the best bid rate at the top. Ask quotes are sorted in ascending order by price, with the lowest ask rate at the top. If the strategy for reference price finding is set to `Best Price` or VWAP Average, MMC can use the full amount streams as raw inbound too. In this case, full amount inbound quotes are indicated with an asterisk next to tier size.

At the top of the panel a user can select a specific pricing tier size. Quotes are additionally marked in various colours, depending on the chosen Reference Price Finding settings. Quotes from selected providers are shown in black, all other quotes are shown in grey.

Those quotes selected by Reference Price Finding for the actual outbound price are additionally marked in yellow.

| red Inbound |
|-------------|
|             |

|                | --------------------------------------- |                 |         |         |                 |                  |
|----------------|-----------------------------------------|-----------------|---------|---------|-----------------|------------------|
|                | <b>Bid</b>                              |                 |         | Ask     |                 |                  |
|                | Provider                                | Quantity        | Price   | Price   | Quantity        | Provider         |
| 1 <sub>m</sub> | COBA.DEMO                               | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | COBA.DEMO        |
|                | <b>BOAL.DEMO</b>                        | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | <b>BOAL.DEMO</b> |
|                | 360TEDF.FEED                            | 1 <sub>m</sub>  | 1.03885 | 1.03983 | 1 <sub>m</sub>  | 360TEDF.FEED     |
|                | 360TEDF.FEED                            | <b>500k</b>     | 1.03885 | 1.03983 | <b>500k</b>     | 360TEDF.FEED     |
|                | COBA.DEMO                               | 2m              | 1.03830 | 1.04038 | 2m              | COBA.DEMO        |
|                | 360TEDF.FEED                            | 5m              | 1.03789 | 1.04079 | 5m              | 360TEDF.FEED     |
|                | COBA.DEMO                               | 4 <sub>m</sub>  | 1.03778 | 1.04090 | 4 <sub>m</sub>  | COBA.DEMO        |
|                | COBA.DEMO                               | 8m              | 1.03726 | 1.04142 | 8m              | COBA.DEMO        |
|                | COBA.DEMO                               | 16 <sub>m</sub> | 1.03596 | 1.04272 | 16 <sub>m</sub> | COBA.DEMO        |
|                | <b>Bid</b>                              |                 |         | Ask     |                 |                  |
|                | Provider                                | Quantity        | Price   | Price   | Quantity        | Provider         |
| 5m             | COBA.DEMO                               | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | COBA.DEMO        |
|                | BOAL.DEMO                               | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | BOAL.DEMO        |
|                | 360TEDF.FEED                            | 1 <sub>m</sub>  | 1.03885 | 1.03983 | 1 <sub>m</sub>  | 360TEDF.FEED     |
|                | 360TEDF.FEED                            | <b>500k</b>     | 1.03885 | 1.03983 | <b>500k</b>     | 360TEDF.FEED     |
|                | COBA.DEMO                               | 2m              | 1.03830 | 1.04038 | 2m              | COBA.DEMO        |
|                | 360TEDF.FEED                            | 5m              | 1.03789 | 1.04079 | 5m              | 360TEDF.FEED     |

<span id="page-33-0"></span>Figure 34: Raw Inbound Quote Details

Click on option "Filtered inbound" to see the actual quotes chosen by Reference Price Finding. The panel shows for each tier the selected quotes separated by bid and ask.

![](_page_33_Figure_9.jpeg)

<span id="page-33-1"></span>Figure 35: Filtered Inbound Quote Details

Click on option "Outbound" to see outbound price details by tier:

| <b>Contract Contract</b><br><b>STATE</b><br><b>Contractor</b><br>Raw Inbound<br>Snapshot: Wed, 16 Nov 2022 17:08:48 UTC<br><b>Filtered Inbound</b> |      |                          |         |         |                           |         |         |                         |                |
|----------------------------------------------------------------------------------------------------------------------------------------------------|------|--------------------------|---------|---------|---------------------------|---------|---------|-------------------------|----------------|
| $\gt$ Outbound                                                                                                                                     | Tier | Inbound<br>(Bid/Mid/Ask) |         |         | Outbound<br>(Bid/Mid/Ask) |         |         | Spread<br>(Manual/Rule) |                |
|                                                                                                                                                    | 1m   | 1.03927                  | 1.03960 | 1.03993 | 1.03935                   | 1.03968 | 1.04001 | 6.6                     | 0.0            |
|                                                                                                                                                    | 5m   | 03861                    | 1.03960 | 104059  | 1.03861                   | 103960  | 104059  | 10.8                    | 0 <sup>n</sup> |

<span id="page-34-0"></span>Figure 36: Outbound Price Details

The panel shows for each pricing tier:

- Tier size
- Inbound quotes
- Outbound quotes
- Manual spread due to configuration
- Automatic spread due to pricing rules
- Manual skew due to configuration
- Automatic skew due to pricing rules
- Skew cut because inbound price reached configured limits

#### **Skew cut:**

Example: bid / ask / mid: 0.9330 / 0.9331 / 0.93305

User sets a manual skew of +1 PIP

In this case the bid rate would cross the mid-rate with 0.5 PIPS

If bid or ask price aren't allowed to cross the mid-rate (the option "Allow Skewing to cross Mid" is not ticked), the price is cut off at the mid-price (in this case by 0.5 PIPS).

# <span id="page-35-0"></span>**5 RISK MANAGEMENT**

Risk Management enables the user to set the notional amount of risk he is prepared or allowed to hold on his own book as well as rules for the Auto Dealer once defined levels are reached.

# <span id="page-35-1"></span>**5.1 Monitoring Positions**

The Instrument Position blotter shows currency pair positions for all managed currency pairs.

| <b>Instrument Positions</b> |              |              |               |           |             |                 |             |               |                |                                                                                          |
|-----------------------------|--------------|--------------|---------------|-----------|-------------|-----------------|-------------|---------------|----------------|------------------------------------------------------------------------------------------|
| Symbol                      | Updated      | Size CCY1    | Size CCY2     | Open PL   | Realized PL | <b>Total PL</b> | Reeval Rate | Avg Buy Price | Avg Sell Price |                                                                                          |
| EUR/GBP                     | 07:59:00.273 |              | 0.00          | 0.00      | 0.00        | 0.00            | 0.87271     |               |                | $\mathbb{R} \times \mathbb{R} \times \mathbb{R} \times \mathbb{R} \times \mathbb{R}$     |
| EUR/USD                     | 07:59:00.302 |              | 0.00          | 0.00      | 0.00        | 0.00            | 1.03481     |               |                | $\triangleright \rightarrow \triangleright \triangleright \bigcirc = \bigcirc \setminus$ |
| GBP/USD                     | 11:07:47.930 | 1,000,000.00 | $-1.186,100$  | $-632.72$ | 0.00        | $-632.72$       | 1.18535     | 1,18610       |                | $\mathbb{R} \rightarrow \mathbb{R} \rightarrow \mathbb{R}$                               |
| MXN/JPY                     | 07:59:00.337 |              | 0.00          | 0.00      | 0.00        | 0.00            | 7.19474     |               |                | $V \rightarrow \vert \mathcal{L} \rangle = \langle \mathcal{R} \vert$                    |
| USD/TRY                     | 11:07:47.931 | 1,186,100.13 | $-22,079,930$ | $-163.77$ | 0.00        | $-163.77$       | 18,61300    | 18.61557      |                | $\forall \exists \emptyset$ = $\textcircled{x}$                                          |
| <b>XAU/USD</b>              | 07:59:00.292 |              | 0.00          | 0.00      | 0.00        | 0.00            | 1,764.216   | $\circ$       |                | $\forall \exists \forall \Theta = \textcircled{x}$                                       |

<span id="page-35-2"></span>Figure 37: Managed Positions blotter with context menu

The following columns are available:

- Position size in CCY1 and CCY2
- Time of last update
- Open P/L (calculated in terms of company currency)
- Realized P/L (calculated in terms of company currency)
- Total P/L (calculated in terms of company currency)
- Revaluation rate (rate which was used to calculate open P/L)
- Average buy and sell price

By default, blotter tracks the position due to client orders and hedge orders booked via MMC as well as any manual position update done via Trader on the application and/or automated position updates triggered via MMC Position Upload API.

Please note that, Instrument Position Blotter can be defined to capture all trades done within 360T regardless whether they are manually booked on other 360T trading applications or autopriced/hedged via other pricing sources. MMC Admins will then still have the option to filter in or out certain type of trades that are not done via MMC from the Global Instrument Configuration panel by defining the respective trading parameters. Details of filtering will be explained in Filtering Position section.

The upgraded position blotter is also able to split the cross position as per defined cross rules into cross legs using MMC`s revaluation rate at the time of booking.

Please get in touch with 360T CAS if you would like to be rolled out for the upgraded version.

### **Note:**

By default, positions will be reset at the server side with every new trading day. The exact time is when the value date is rolled. Thus the trade history shows at most the requester order and hedge trades from the current day.

For users who want to keep their position overnight, there is a global configuration which can be done via 360T CAS.

The trade history will be wiped/erased with **every manual position reset**!

Trade history for each position is shown in the lower table. Select a specific position in the upper table to see the trade history in the lower table. The table shows both client and hedge orders. For hedge orders additionally all executions are shown as children.

#### **Context menu:**

Right clicking into the blotter data opens the context menu.

![](_page_36_Picture_4.jpeg)

Figure 38 Context menu for managing open position

<span id="page-36-2"></span>For instruments currently owned by the user this dialog offers position blotter specific options:

- Amend Position: Select to amend the position for a specific quantity and price
- Set Position: Select to set the position to a specific size and price
- Reset Position: Reset the position to zero and erase the order and trade history
- Flatten Position: Select to **execute a hedge order in the market** to close the position
- Cancel Open Orders: Cancel all open hedge orders for the specified currency pair.

# <span id="page-36-0"></span>**5.2 Profit and Loss (P/L) Calculations**

Open positions are periodically (every 500ms) revaluated mark-to-market with the best available inbound price to calculate **open P/L**. Long positions are revaluated with best bid price, short positions are revaluated with best ask price.

P/L is calculated by one of these two methods:

- **Average cost method**: This method calculates P/L by taking the difference of how much was spent to build the position, and how much was earned by closing the position
- **First-In, First-out method (FIFO):** This method assumes that every SELL trade covers the oldest open BUY trade, and vice versa

The chosen P/L calculation method is a system setting and affects all users. By default the "average cost" method is used. **To configure your preferred P/L calculation method please contact 360T CAS.**

# <span id="page-36-1"></span>**5.3 Position Filtering Configuration**

It is possible for MMC users to opt in to include trades done outside MMC into their currency pair position by reaching out to CAS for enablement. After this is enabled, MMC admins will see a menu called Position Filtering inside Global Instrument Configuration dialog.

<span id="page-37-1"></span>Figure 39 Position Filtering

The configuration consists of 5 filters (Dealer, Legal Entity, Fx Time Period, Product, Execution Method), two operations (include only or exclude) as well as a dual list which shows available and selected values for the filters.

MMC admins can determine what needs to be included or excluded in position. For example if any non-spot trade to be excluded, admin can use product filter where only Spot is selected together with include operation.

Please also note that any changes done in this configuration requires the roll of the positions which is at NY 5 PM unless you opted in to roll the positions to next day. In that case, changes will take affect with weekend maintenance of 360T platform.

## <span id="page-37-0"></span>**5.4 Risk Management Configuration**

Risk management configuration contains three sub-section: General Parameters, Position Rules and Pricing Rules.

| Pricing                        | <b>General Parameters</b>      |               |  |
|--------------------------------|--------------------------------|---------------|--|
| <b>Reference Price Finding</b> | Maximum Hedge Order Size:      | 20,000,000.00 |  |
| $\vee$ Risk Management         | Remove quotes older than (ms): | $-5,100 +$    |  |
| > General Parameters           |                                |               |  |
| <b>Position Rules</b>          |                                |               |  |
| <b>Pricing Rules</b>           |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |
|                                |                                |               |  |

<span id="page-38-0"></span>Figure 40: Risk management configuration

The MMC has built in certain so called "Safeguards" to limit the risk of eventual losses in case of technical problems. Certain safeguard parameters can be adjusted by users.

#### **Maximum hedge order size:**

The maximum hedge order size limits the order quantity of auto-hedge orders. The default value is 10 million in base currency. Users can adjust this value individually for each managed instrument. The upper limit for this parameter is 50 million.

To adjust the maximum hedge order size or a specific managed instrument, open the instrument configuration dialog, and select option "General Parameters":

Independent of position size and auto-hedging rules, no auto-hedge order will be larger than this value.

Example:

- The maximum hedge order size is set to 10 million
- A user defines a position rule to flatten the position if it is larger than 20 million
- The position reaches 22 million

The MMC will create a first hedge order over 10 million. Assuming the hedge order got fully filled, the position will go down to 12 million. At the next position review, the position rule will trigger again, and a second hedge order over 10 million will be created.

Note:

This parameter only applies to instruments with risk management mode "Managed". This parameter has no effect on back-to-back or flow hedge orders.

#### **Minimum hedge order size:**

**The minimum hedge order size is an auto hedge order safety mechanism which prevents any auto hedge order to be placed below certain amount in order to avoid a larger number of small hedge order attempts. The size is set to 100.000 in base**  **currency by default. Please contact CAS in case you would like to amend the minimum hedge order size.**

# <span id="page-39-0"></span>**5.5 Position Rules**

Instrument owners can manage their currency risks and define some action depending on certain triggers by creating position rules. Position rules can be based on one of the following options (triggers) for the selected position:

- **Position:** The absolute position size (long and short positions are treated equally)
- **Total Loss:** The negative total P/L
- **Open Profit:** The positive open P/L
- **Open Loss:** The negative open P/L
- **Open Profit (PIPS):** Positive open P/L as a price difference to average position price
- **Open Loss (PIPS):** Negative open P/L as a price difference to average position price

The selected trigger is compared to a specified value with either greater (>), or greaterequals (>=).

As soon as a rule is triggered, the specified action will be executed. The available **actions** are:

- **Back2Back:** The client order will only be accepted after it was successfully hedged (last look)
- **Alert:** Raises an alert to the user when the trigger level is reached
- **Reduce by:** Reduces the position by the specified amount
- **Reduce by %:** Reduces the position by the specified percentage of its current size
- **Reduce to:** Reduces the position to the specified size
- **Flow hedge:** Hedge the client order immediately (no last look)
- **Flatten:** Close the position
- **Switch to Back2Back:** Switch the risk management mode to B2B for this instrument
- **Switch to Flow Hedge**  switch the risk management mode to flow hedging for this instrument
- **Place relative to TOB**: Places a pegged order to 360T`s Central Order Book (COB). (Note: Only available for 360T ECN participants).
- **Stop Pricing for instrument**: Stop publishing outbound prices for the instrument when the trigger is reached.

![](_page_40_Picture_2.jpeg)

![](_page_40_Picture_3.jpeg)

<span id="page-40-0"></span>Figure 41 Position Rules

Back-to-Back (B2B) means that the requester order will only be accepted after it was successfully hedged (last look). A B2B rule which checks a certain position size is triggered when the current position amount, plus the notional of the requester order, would breach the specified limit.

Position rules are checked and possibly triggered:

- Once with each client order
- Periodically All rules are evaluated every 500ms

When a position size rule is triggered, **it will emit a hedge order** to the market based on the specified rule action.

The periodic rules review ensures that positions are regularly reviewed even if no requester order is imminent. This is also a retry mechanism for failed or partially filled hedge orders. Instead of re-attempting the same failed hedge order, the system will simply compare positions against rules at the next periodic review.

### **Note:**

**Even though rules are reviewed every 500ms, for safety reasons the system will wait (by default) at least 3 seconds between subsequent hedge orders for the same currency pair.** 

#### **Example flatten position:**

A rule action could be Flatten. When this rule is triggered, a hedge order will be created to close the entire position in the market. If this hedge order fails or is only partially filled, another hedge order with the remaining position size will be created **latest after 3 seconds.** The hedge order will be created earlier, if there is a requester order for the same currency pair before the 3 seconds expired.

#### **The auto-hedger will always make sure that:**

• **Positions are not over hedged (flips sides because of a hedge order)**

### • **Position never increase because of a hedge order**

#### **Flow Hedging:**

As the name suggests, flow hedging means to hedge the flow of requester order one-to-one. Every incoming requester order will be first executed and the position will be updated accordingly. When the updated position breaches a defined limit, and the specified action is Flow Hedge a reverse hedge order of the same size than the requester order will be emitted to the market.

The goal of flow hedging is to accept all requester orders, without letting positions grow (no guarantee).

#### **Rule based risk management mode switching:**

Actions "Switch to Back2Back" and "Switch to Flow hedge" can be used to automatically switch the risk management mode based on certain criteria. An example is to switch to B2B mode when the total loss for a specific instrument breached a threshold, to avoid any further losses.

#### **Note:**

Switching the risk management mode back to "Managed" is a manual operation to be performed in the Pricing Control tab!

#### **Note:**

- "Reduce by" shall be used with care. Every requester order is a single trigger. A position could grow very quickly if the specified "reduce by"- quantity is small compared to the requester order size!
- Consider to use "Reduce by" and "Reduce by %" with a position size trigger. A P/L based rule can be triggered for any position size, and it is difficult to predict the position size when the rule fires.
- Flow hedging is an option to avoid requester order rejections, and avoid position growth, but because flow hedging only **attempts** to hedge **after** the requester order was accepted, there is always the possibility that position still grows quickly!

# **It is advisable to always specify a Back2Back rule to limit the maximum position size!**

**Without such a rule, positions can potentially increase unreasonably result in a loss in short time!**

## <span id="page-41-0"></span>**5.6 Pricing Rules**

Pricing Rules define how to modify spreads and skew based on position size or P/L.

| Instrument Configuration EUR/USD |           |                                   |                             |                      |                               |                     |                          | $\times$                                                                                                 |
|----------------------------------|-----------|-----------------------------------|-----------------------------|----------------------|-------------------------------|---------------------|--------------------------|----------------------------------------------------------------------------------------------------------|
| Pricing                          | Enabled   | <b>Trigger</b>                    | Operator                    | <b>Trigger Level</b> | Action                        | <b>Action Value</b> |                          |                                                                                                          |
| <b>Reference Price Finding</b>   | $\bullet$ | Position<br>$\blacktriangledown$  | $\rightarrow$ $\rightarrow$ | $5,000,000 +$        | Skew%<br>$\blacktriangledown$ | Œ<br>$10 +$         | $\hat{\wedge}$           | û<br>$\forall$                                                                                           |
| $\vee$ Risk Management           | $\bullet$ | Position<br>$\checkmark$          | $\rightarrow$ $\sim$        | 7,000,000<br>u za    | Skew%<br>$\checkmark$         | $15 +$              | $\hat{\frown}$           | $\vee$                                                                                                   |
| <b>General Parameters</b>        | S         | <b>Total Loss</b><br>$\checkmark$ | $\Rightarrow = \sqrt{ }$    | 10,000<br>Ð          | $\checkmark$<br>Spread%       | $10 +$              | $\hat{\curvearrowright}$ | $\begin{array}{c} \widehat{\boxdot} \\ \widehat{\boxdot} \end{array}$<br>$\mathrel{\mathop{\mathbb{V}}}$ |
| <b>Position Rules</b>            |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
| > Pricing Rules                  |           |                                   | $\pm$                       |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               | <b>Discard</b>      |                          | <b>Apply</b>                                                                                             |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |
|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |

<span id="page-42-1"></span>Figure 42: Risk management pricing rules

Spread and skew due to such rules is added on top of spread and skew configured in the Pricing Controller tab.

# <span id="page-42-0"></span>**5.7 Alert Rules**

Within position rules, a user can create Alert rules to receive a notification when a position size or P/L reaches a certain limit.

When an Alert level is reached, the user receives a pop up notification **and** an acoustic signal.

![](_page_42_Picture_8.jpeg)

<span id="page-42-2"></span>![](_page_42_Figure_9.jpeg)

#### **Note:**

When you acknowledge an Alert, the respective rule will be disabled. **You have to manage the position and thereafter tick the rule again and apply it!**

# <span id="page-43-0"></span>**5.8 Manual Position Amendments**

Positions can be manually amended for each managed currency pair. To do so, you can right click on the position to open the context menu or you can use the icons in the instrument position table.

![](_page_43_Picture_4.jpeg)

Figure 44 Context menu of Managed Positions

<span id="page-43-1"></span>

| <b>Instrument Positions</b> |              |              |              |              |                    |                 |                                                                            |
|-----------------------------|--------------|--------------|--------------|--------------|--------------------|-----------------|----------------------------------------------------------------------------|
| Symbol                      | Updated      | Size CCY1    | Size CCY2    | Open PL      | <b>Realized PL</b> | <b>Total PL</b> |                                                                            |
| EUR/GBP                     | 14:11:40.092 | 0.00         | 41,400       | 0.00         | 47,196.70          |                 | 47,196.70 $\cancel{v}$ $\rightarrow$ $\Diamond$ $=$ $\otimes$              |
| EUR/USD                     | 15:50:32.405 | 8,000,000.00 | $-8,266,970$ | $-10,928.86$ | 1,307.97           |                 | $-9,620.89$ $\Rightarrow$ $\Rightarrow$ $\Rightarrow$ $\Rightarrow$<br>⊗ I |

<span id="page-43-2"></span>Figure 45 Taskbar for manual position update

|  | Figure 46 Amend Position |  |
|--|--------------------------|--|

<span id="page-43-3"></span>**Amend** a position ( ): Enter a quantity and price and click the "OK" button. This will add the specified quantity to the position and impact the P/L based on the captured price. To reduce the position, enter a negative quantity.

![](_page_43_Figure_10.jpeg)

<span id="page-43-4"></span>Figure 47 Set Position

**Set** a position ( ): Enter a quantity and price and click the "OK" button. This will set the position to the specified position size and impact the P/L based on the captured price.

**Reset** a position ( ): Choose the Reset command and the entire position will be erased and reset to zero. A message is displayed in order to request a confirmation for the reset before it is executed.

![](_page_44_Picture_6.jpeg)

<span id="page-44-0"></span>Figure 48 Confirmation of Position Reset

None of the above actions will create "real" deals with the market!

**Flatten** a position : Choose the Flatten command to create a hedge order to close the position. This action will create real deals with external market makers.

| <b>Flatten Position</b>                                            |               |
|--------------------------------------------------------------------|---------------|
| Please confirm that you want to flatten your position for EUR/GBP. |               |
|                                                                    |               |
|                                                                    |               |
|                                                                    |               |
| Cancel                                                             | Confirm<br>J. |

<span id="page-44-1"></span>Figure 49 Confirmation of Position Flattening

Flatten will attempt to execute your Notional position out in the market back to zero, you will be asked to confirm this action and if it is unable to bring it to zero, you will receive a notification.

# <span id="page-45-0"></span>**5.9 Restrict the bank basket for hedge orders**

By default, when the MMC places hedge orders, quotes of the entire client's bank basket are considered. Users can restrict the bank basket for B2B and flow hedge orders to make sure hedge orders are only sent to selected providers.

To restrict the hedge order bank basket for a specific instrument, open the "Instrument Configuration" dialog, and select option "Reference Price Finding".

| <b>Best Price</b><br><b>KO</b><br><b>KO</b><br>1,000,000<br>360TBANK.TEST 360TEDF =><br>Hedge<br>1,000,000<br><b>SO</b><br>360TBANK.TEST 360TEDF =/<br><b>VWAP Average</b><br>5,000,000<br>Hedge<br>5,000,000<br>Core Channel 2<br><b>SO</b><br>8,000,000<br><b>Best Price</b><br>8,000,000<br>360TBANK.TEST 360TEDF = /<br>Hedge<br><b>SO</b><br><b>Best Price</b><br>BOAL.DEMO Barclays BAR<br>Hedge<br>15,000,000<br>15,000,000<br>$\bar{z}$ /<br><b>Best Price</b><br>$\vert 0 \rangle$<br>Hedge<br>25,000,000<br>₩ | Pricing                        | Tier | Providers | Hedge | <b>Strategy</b> | Min Quote Size/Bid | Min Providers/Ask |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|------|-----------|-------|-----------------|--------------------|-------------------|
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | $\vee$ Reference Price Finding |      |           |       |                 |                    | $\mathbf{1}$      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | > Core Channel 1               |      |           |       |                 |                    | 3 <sup>°</sup>    |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | <b>Risk Management</b>         |      |           |       |                 |                    | $\mathbf{0}$      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    | $\bullet$         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |

<span id="page-45-2"></span>Figure 50: Restrict hedge order bank basket

To restrict the hedge order bank basket for a specific pricing tier, tick the checkbox in column "Hedge". Any client order hitting this pricing tier, will be hedged with the selected providers for the same tier.

# <span id="page-45-1"></span>**5.10Client Order Handling Rules**

Client Order Handling rules allow users to setup rules to override the instrument's risk management mode for individual client orders. Users can setup an arbitrary number of such client order handling rules. The configuration can be instrument specific but also across multiple or all instruments using wildcards.

Examples for client order handling rules:

- All client orders for EUR/USD up to 1m quantity shall be managed on the position, but all orders larger than 1m shall be flow hedged.
- Pricing for instrument GBP/USD is configured for up to 10m with risk management mode "Flow Hedging". All client orders larger than 5m from counterparty "ABC Ltd." shall be hedged back-to-back.

A client order handling rule consists of client order matching parameters, and an action. The action defines what to do with the client order if the rule matches. If more than one rule is defined, only the first matching rule will be executed!

Parameters to match client orders are:

• **Instrument** … either exactly (e.g. EUR/USD) or defined with wildcards (e.g. EUR/\*)

- **Order quantity** and comparison operator (bigger, bigger equals, equals, smaller equals, smaller)
- **Counterparty** and comparison operator to match exactly (e.g. equals "ABC Ltd.") but also pattern matching (all counterparties starting with "Bank").

Possible actions are:

- Hedge B2B
- Flow Hedge
- Reject (to reject a client order)

A client order handling rule can **override** the current risk management mode of an instrument, but only **towards less risk**! As an example, if the current risk management mode is "Flow Hedge", a client order processing rule can only force order handling with "B2B" or "Reject" but not "Managed".

The following table provides an overview of possible actions for each instrument risk management mode:

| Instr. risk management mode | Managed | Flow Hedge | B2B | Reject |
|-----------------------------|---------|------------|-----|--------|
| Managed                     | Yes     | Yes        | Yes | Yes    |
| Flow Hedge                  | No      | Yes        | Yes | Yes    |
| B2B                         | No      | No         | Yes | Yes    |

Client order handling rules where the action is identical with the current risk management mode will be matched, but have no effect!

To configure client order handling rules, open the "Global Instrument Configuration" dialog and select option "Client Order Handling". Click on the "+" button to add new rules:

![](_page_46_Figure_13.jpeg)

<span id="page-46-0"></span>Figure 51: Manage Client Order Handling Rules

Rules are always evaluated for a match starting at the top of the list towards the bottom. The first matching rule will be executed, all remaining rules will be ignored.

### Explanation for the examples above:

Rule 1: Any client order bigger or equals 2m from a counterparty ending with "Fund" will be rejected

Rule 2: Any client order with terms currency USD from "Abc Corp" will be hedged B2B

Rule 3: Any EUR/USD client order with quantity bigger than 3m will be flow hedged

Rule 4: Any client order with quantity bigger or equals 10m will be hedged B2B

It is important to order rules in the right way! More restrictive rules should be placed before more generic rules. An example are rules 2 and 3. If rule 3 would be placed before rule 2, orders from "Abc Corp" bigger than 3m would be flow hedged! Assuming a user wants to hedge any order \*/USD from "Abc Corp" B2B, it is important to make sure that a more generic rule doesn't override it.

#### **Counterparty comparison operators:**

The system offers a number of comparison operators to match counterparties in various ways:

| Operator    | Explanation                                                   |
|-------------|---------------------------------------------------------------|
| Exactly as  | Compares for an exact match of the specified counterparty     |
| All except  | Matches all counterparties except the specified one           |
| Contains    | Matches any counterparty which contains the specified text    |
| Starts With | Matches any counterparty which starts with the specified text |
| Ends With   | Matches any counterparty which ends with the specified text   |
| All like    | Complex pattern search using Java regular expressions         |

#### **Counterparty pattern matching:**

The provided standard counterparty comparison operators should be sufficient for most cases to match either a specific counterparty, or a group of counterparties. Operator "All like" provides an alternative in cases where the standard operators don't help. This operator uses so called Java regular expressions to match counterparties. Such regular expressions provide extreme flexibility but can be eventually complex to define.

The general syntax for Java regular expression can be found in the appendix chapter [1.](#page-52-2) Further information about Java regular expressions can be found in the internet in the official Java documentation (https://docs.oracle.com/javase/tutorial/essential/regex/).

# <span id="page-47-0"></span>**5.11Pricing and Risk Management Scenarios**

Pricing and Risk management scenarios (or short scenarios) allow users to quickly adjust pricing and risk management settings with one mouse click. Users can define an arbitrary number of such scenarios.

Each scenario defines:

- Name
- Spread factor
- Maximum quote size
- Risk management mode

Only the name is mandatory, all other parameters are optional.

If set, parameters influence pricing and risk management in various ways. The current outbound spread will be widened by the selected spread factor. A spread factor of e.g. 2, will widen the outbound spread by 100%. Maximum quote size can be used to limit the available liquidity for your requesters. If e.g. 5m maximum quote size is chosen, any pricing tier above 5m will be disabled. The selected risk management mode of the scenario will override the current instrument's risk management mode, but only towards less risk. If e.g. the current instruments risk management mode is "B2B" and the scenario defines "Flow Hedge", the parameter will be ignored.

Scenarios are defined globally and can be applied to any managed instrument. Scenarios override current settings only temporarily as long as the scenario is applied.

To add a new scenario open the "Global Instrument Configuration" dialog and select option "Scenarios":

| Global Instrument Configuration                                                 |                              |                                                                    |                                       |                                                                                                  | $\times^1$                                     |
|---------------------------------------------------------------------------------|------------------------------|--------------------------------------------------------------------|---------------------------------------|--------------------------------------------------------------------------------------------------|------------------------------------------------|
| <b>General Parameters</b><br>Instruments<br><b>Tiers</b>                        | Name<br>Moderate<br>Volatile | <b>Spread Factor</b><br>$1.5 +$<br>$\rightarrow$<br>2 <sup>2</sup> | Max Quote Size<br>Ð<br>5,000,000<br>Ð | <b>Risk Management Mode</b><br>$\checkmark$<br>Managed<br>$\overline{\vee}$<br><b>Flow Hedge</b> | $\widehat{\boxplus}$<br>$\widehat{\mathbb{U}}$ |
| > Scenarios                                                                     | Unattended                   | $1 - 1$                                                            | 3,000,000<br><b>Report</b>            | $\overline{\vee}$<br>B2B                                                                         | $\widehat{\mathbb{U}}$                         |
| <b>Cross Rules</b><br><b>Client Order Handling</b><br><b>Position Filtering</b> |                              | ╫                                                                  |                                       |                                                                                                  |                                                |

<span id="page-48-0"></span>Figure 52: Pricing and Risk Management Scenarios

Scenarios can be applied to instruments in the "Pricing Control" panel.

| Market Maker Cockpit      | <b>Audit Log</b> |                   |                                           |                                   |                   |
|---------------------------|------------------|-------------------|-------------------------------------------|-----------------------------------|-------------------|
| <b>Instrument Control</b> |                  |                   |                                           |                                   |                   |
| Instrument                | Managed By       | Mode              | Skew PIPS                                 | Skew %                            | Scenario          |
| <b>All Instruments</b>    |                  | <b>Managed</b>    |                                           |                                   | none<br>$\sim$    |
| EUR/GBP                   | 360TMMC.Trader1  | <b>Flow Hedge</b> | 0 <sup>o</sup><br>$\bullet$ $-$<br>$-4.1$ | $50 + \bigoplus \infty$<br>$\Box$ | none              |
| EUR/USD                   | 360TMMCTrader1   | <b>Managed</b>    | $\Theta$<br>$\blacksquare$                | $+ 0.0$<br>n<br>ei                | moderate          |
| GBP/USD                   | 360TMMC.Trader2  | Managed           | 00                                        | $+0.0$<br>61<br><b>CONTENT</b>    | volatile          |
| MXN/JPY                   | 360TMMC.Trader1  | <b>Managed</b>    | $\Theta$<br>$\bullet$ $-$                 | $+ 0.0$<br>$\Box$<br><b>D</b>     | $\checkmark$ none |
| USD/TRY                   | 360TMMC.Trader1  | Managed           | 0<br>- 62<br>n                            | $+ 0.0$<br>ei                     | <b>THORNE</b>     |
| XAU/USD                   | 360TMMC.Trader1  | <b>Managed</b>    | $\Theta$<br>a Cir<br><b>n</b>             | $+100$<br>$\bullet$<br><b>n</b>   | none              |

<span id="page-48-1"></span>Figure 53: Scenario selection

The current scenario can be changed for all instruments simultaneously by selecting a scenario in the "All Instruments" row.

# <span id="page-49-0"></span>**6 BLOTTERS**

The MMC offers various blotters to monitor orders, trade flow, and positions. The blotter currently available are:

- Currency Pair Position Blotter
- Client Order Blotter
- Hedge Order Blotter
- Combined Client and Hedge Order Blotter
- Client Activity Blotter
- Trade Blotter

# <span id="page-49-1"></span>**6.1 General Blotter Features**

All blotters offer the same general set of features which are:

- Choose, move, and resize columns
- Sorting by one or more columns

Columns can be resized simply by moving the boundary between two columns left or right. The columns order can be changed via drag and drop.

To move a column, click with the left mouse button on the column header and hold it. Move the column to the next target location and release the mouse button.

Blotter data can be sorted by one or more columns, in ascending or descending order, by clicking one or more time on the according column header. To sort by more than one column, press and hold the CTRL key on your keyboard, and click on the specific column headers. A small triangle in the column header will show the sort direction. The small number next to the triangle indicates the sort order if more than one sort column is selected.

# <span id="page-49-2"></span>**6.2 Client Order Blotter**

The client order blotter shows every client order received by the pricing engine. For each client order the following information is available:

• Order ID

.

- Time of arrival
- Currency pair
- Status (e.g. EXECUTED, REJECTED, ..)
- Action (BUY or SELL)
- Order limit price
- Notional amount and currency
- Executed amount and rate
- Type (originating system like RFS or SEP)
- Trigger (originating organization and user)
- Realized By ID filled for B2B orders
- Requester

The blotter is pre-filled at startup with orders from the current trading day.

#### **Note:**

© 360 Treasury Systems AG - 50 - Version 4.17

- Order action (buy or sell) is shown from the perspective of the requester (a buy order means, the requester bought and the ADS MMC sold)!
- In case of a cross rate strip order, the blotter will show three requester orders. The actual cross rate order, and two additional orders for each leg.

# <span id="page-50-0"></span>**6.3 Hedge Order Blotter**

The hedge order blotter shows an entry for each hedge order created for the current trading day. For each hedge order the following information is available:

- Hedge Order ID
- Time of creation
- Currency Pair
- Status
- Action (BUY or SELL)
- Order limit price
- Order notional amount and quantity
- Executed amount and for which average price
- Trigger indication why the hedge order was created

The blotter is pre-filled at startup with orders from the current trading day.

## <span id="page-50-1"></span>**6.4 Combined Client and Hedge Order Blotter**

Blotter with title "All orders" combines client and hedge orders into a single blotter. The two different order types can be easily separated by the type column.

The available columns are the same as for the individual requester and hedge order blotters.

# <span id="page-50-2"></span>**6.5 Client Activity Blotter**

The client activity blotter shows one entry for every status of a each requested quote:

| <b>Hedge Orders Glient Activity</b><br><b>Client Orders</b><br>All Orders |                           |            |                |                                                               |  |  |  |  |
|---------------------------------------------------------------------------|---------------------------|------------|----------------|---------------------------------------------------------------|--|--|--|--|
|                                                                           | $\wedge$ Time(UTC)        | Type       | <b>Status</b>  | Description                                                   |  |  |  |  |
| 168333345-360T.MMC                                                        | 17 Nov. 2022 16:09:53.910 | <b>RFS</b> | QUOTED         | The RFS '168333345-360T.MMC received its first guote '1.0304  |  |  |  |  |
| 168333345-360T.MMC                                                        | 17 Nov. 2022 16:09:53.825 | RFS        | <b>STARTED</b> | The RFS ' EUR/USD SPOT 5000000.00 EUR' was started for '360T  |  |  |  |  |
| 168333345-360T.MMC                                                        | 17 Nov. 2022 16:09:56.591 | <b>RFS</b> | CANCELED       | The RFS '168333345-360T.MMC was canceled                      |  |  |  |  |
| 168320542-360TMMC                                                         | 17 Nov. 2022 14:58:27.135 | RFS        | <b>STARTED</b> | The RFS ' EUR/USD SPOT 50000000.00 EUR' was started for '360  |  |  |  |  |
| 168320542-360T.MMC                                                        | 17 Nov. 2022 14:58:32.061 | <b>RFS</b> | CANCELED       | The RFS '168320542-360T.MMC was canceled                      |  |  |  |  |
| 168312124-360T.MMC                                                        | 17 Nov. 2022 13:53:49.551 | RFS        | <b>STARTED</b> | The RFS 'Sell USD/BRL SPOT 5000000.00 USD' was started for '3 |  |  |  |  |
| 168312124-360T.MMC                                                        | 17 Nov. 2022 13:53:52.877 | <b>RFS</b> | CANCELED       | The RFS '168312124-360T.MMC was canceled                      |  |  |  |  |

<span id="page-50-3"></span>Figure 54: Client Activity Blotter

Status = Started shows time of quote request while the request details are shown in Description.

Status=Quoted shows when first quote by MMC is provided to the client`s request. The details of the first quote is shown in Description.

Status = Canceled shows that the request is either cancelled by the requester or rejected by MMC. In case it is a rejection, details are shown in Description.

Status = Executed indicates that the request is executed. In the details, user can see the client order ID and request ID.

# <span id="page-51-0"></span>**7 AUDIT**

An audit trail can be accessed within the MMC to view the historical configuration adjustments made on input and output streams. It offers the "before" and "after" values for each configuration update. This is particularly helpful to understand which configuration settings were applied during a specific trade event.

| <b>Market Maker Cockpit</b>                                                                                              | <b>Audit Log</b>         |                                    |                                |                         | ◙<br>∽   |  |  |
|--------------------------------------------------------------------------------------------------------------------------|--------------------------|------------------------------------|--------------------------------|-------------------------|----------|--|--|
|                                                                                                                          |                          |                                    |                                |                         |          |  |  |
|                                                                                                                          | <b>Qv</b>                |                                    | 01.03.2022 - 16.03.2022        | <b>Search</b>           |          |  |  |
| UTC Timestamp >= 01.03.2022<br>And<br>UTC Timestamp <= $16.03.2022$<br>And<br>Source = Instrument Configuration $\times$ |                          |                                    |                                |                         |          |  |  |
| Timestamp                                                                                                                | <b>Change Originator</b> | Source                             | Config                         | Event                   | Action   |  |  |
| Mar 16, 2022 3:40:38 PM                                                                                                  | PFAPAC Trader1           | Instrument Configuration - EUR/GBP | <b>Instrument Positions</b>    | <b>Position Amended</b> | Modified |  |  |
| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Hedge                   | Modified |  |  |
| Mar 10, 2022 5:22:16 PM                                                                                                  | PFAPAC Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Hedge                   | Modified |  |  |
| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | <b>Min Providers</b>    | Modified |  |  |
| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Min Quote Size          | Modified |  |  |
| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Strategy                | Modified |  |  |
| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |
| Mar 10, 2022 5:21:00 PM                                                                                                  | <b>PEAPACTrader1</b>     | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |
| Mar 10, 2022 5:21:00 PM                                                                                                  | <b>PEAPAC Trader1</b>    | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |
| Mar 10, 2022 5:21:00 PM                                                                                                  | <b>PFAPAC Trader1</b>    | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |
| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | Reference Price Finding        | Provider                | Added    |  |  |
| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |
| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | Reference Price Finding        | Provider                | Added    |  |  |
| Mar 10, 2022 5:20:41 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Instrument Control</b>      | <b>Pricing Started</b>  | Modified |  |  |

The audit trail is accessible in MMC via the tab "Audit Log".

<span id="page-51-1"></span>Figure 55: Accessing the Audit Log

The audit trail has a built-in search functionality. It offers the possibility to search event logs within a predefined time period as well as by any of the following parameters (matches the table names of the audit trail):

- Change Originator: The user who adjusted the configuration
- Source: Refers either to the global or instrument configuration settings
- Config: Refers to the configuration category
- Event: The configuration item which was adjusted
- Action: Type of configuration event (Added, Modified, Removed, Enabled, Disabled, Started, Stopped)

#### Example:

If modified configurations must be retrieved in the event logs, type "Action=" into the Search field. A drop-down popup will appear with the possible values.

<span id="page-52-2"></span>

|         | $Q \vee$ Action = | 11.11.2022 - 17.11.2022 | 雦<br>Search |
|---------|-------------------|-------------------------|-------------|
| Added   |                   |                         |             |
|         | Modified          |                         |             |
|         | Removed           | Event                   | Action      |
| Started |                   |                         |             |
|         | Stopped           |                         |             |
|         | Enabled           |                         |             |
|         | Disabled          |                         |             |

<span id="page-52-1"></span>Figure 56: Search by Action

Select "Modified from the drop down field and press enter. The applied filter rule is then shown below the search field.

# <span id="page-52-0"></span>**8 CONTACT 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **Middle East Asia Pacific**

#### **United Arab Emirates**

*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036

### **EMEA Americas**

### **USA**

*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #27-01 Republic Plaza Tower 1 Singapore 048619

Dubai Phone: +971 4 431 5134 Phone: +65 6325 9970 Fax: +65 6536 0662