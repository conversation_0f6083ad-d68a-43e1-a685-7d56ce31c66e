![](_page_0_Picture_0.jpeg)

## **AUTO DEALING SUITE (BRIDGE ADMINISTRATION)**

# **TEX MULTIDEALER TRADING SYSTEM**

**User Guide 360T Bridge Administration: Auto Dealing Suite**

Release 4.16 (November 2022)

© 360 Treasury Systems AG, 2022 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG

![](_page_1_Picture_0.jpeg)

# **CONTENTS**

| 1 | Introduction                                            | 5  |
|---|---------------------------------------------------------|----|
| 2 | Accessing the Autodealing Suite and Auto Dealer Control | 6  |
| 3 | ADS Configuration Groups                                | 9  |
|   | 3.1<br>Defining Currency<br>Groups                      | 10 |
|   | 3.2<br>Defining Currency Couple<br>Groups               | 12 |
|   | 3.3<br>Defining Notional Amounts<br>Groups              | 14 |
|   | 3.4<br>Defining Margin<br>Groups                        | 15 |
|   | 3.5<br>Defining FX Time Period<br>Groups                | 20 |
|   | 3.6<br>Defining MM Period Groups                        | 21 |
|   | 3.7<br>Defining Activation Period Groups                | 21 |
|   | 3.8<br>Defining Manual Routing<br>Groups                | 23 |
|   | 3.9<br>Defining Fixing Reference<br>Groups              | 24 |
| 4 | ADS RFS Rules                                           | 25 |
|   | 4.1<br>RFS Provider<br>Groups                           | 25 |
|   | 4.2<br>RFS Algorithm<br>Groups                          | 26 |
|   | 4.3<br>RFS Rules<br>Templates                           | 28 |
|   | 4.4<br>RFS Satellite<br>Groups                          | 36 |
|   | 4.5<br>RFS Satellite Rules                              | 39 |
| 5 | Currency Blacklist                                      | 43 |
| 6 | ADS Rule Evaluator Tool                                 | 43 |
| 7 | Auto Dealer Control                                     | 45 |
| 8 | Change Tracking                                         | 50 |
| 9 | Contacting 360T                                         | 52 |

![](_page_2_Picture_0.jpeg)

## **TABLE OF FIGURES**

| Figure 2 Bridge Administration: Homepage<br>7                                 |  |
|-------------------------------------------------------------------------------|--|
| Figure 3 Entity selection to reach the ADS overview page7                     |  |
| Figure 4 ADS Rules Tool with Configuration Groups and ADS functions8          |  |
| Figure 5 Auto Dealer Control Panel for RFS, Orders and SEP Configuration<br>8 |  |
| Figure 6 Administration of Configuration Groups<br>9                          |  |
| Figure 7 Currency Groups<br>10                                                |  |
| Figure 8 Creation of a Single Currency Group11                                |  |
| Figure 9 Selection of a Currency Group<br>12                                  |  |
| Figure 10 Selection of a Currency Couple Group<br>13                          |  |
| Figure 11 Selection of a Notional Amount Group14                              |  |
| Figure 12 Margin Groups15                                                     |  |
| Figure 13 Margin Types16                                                      |  |
| Figure 14 FX Time Period Groups20                                             |  |
| Figure 15 Money Market Time Period Groups<br>21                               |  |
| Figure 16 Display of the selection of an Activation Period Group22            |  |
| Figure 17 Manual Routing Group configuration<br>23                            |  |
| Figure 18 Fixing Reference Groups24                                           |  |
| Figure 19 Menu item: ADS RFS Rules<br>25                                      |  |
| Figure 20 Definition of RFS Provider Groups26                                 |  |
| Figure 21 Market Link Algorithm definition26                                  |  |
| Figure 22 RFS Rules Template<br>29                                            |  |
| Figure 23 Creating rules within a rule template29                             |  |
| Figure 24 Selecting a Trading Venue<br>30                                     |  |
| Figure 25 Entering custom values for a parameter within a template group31    |  |
| Figure 25 FX Swaps: Overhang<br>31                                            |  |
| Figure 26 Route column<br>32                                                  |  |
| Figure 27 Creating new satellite group<br>36                                  |  |
| Figure 28 Adding requester members to RFS Satellite Groups<br>37              |  |
| Figure 29 Specifying Products of an RFS Satellite Group37                     |  |
| Figure 30 Satellite Group: Assignments<br>38                                  |  |
| Figure 31 RFS Satellite Rules<br>39                                           |  |
| Figure 32 Using RFS Rules Templates across Satellite Groups40                 |  |

© <sup>2022</sup> – 360 Treasury Systems AG <sup>3</sup>

![](_page_3_Picture_0.jpeg)

| Figure 33 Using rules in conjunction with rule templates<br>41                                                          |
|-------------------------------------------------------------------------------------------------------------------------|
| Figure 34 Applying margin transformations<br>42                                                                         |
| Figure 35 Currency Backlist<br>43                                                                                       |
| Figure 36 Activate Toggle Rule Evaluator<br>43                                                                          |
| Figure 37 Enter Request details to toggle to the relevant rule44                                                        |
| Figure 38 Identified rule after a rule search<br>44                                                                     |
| Figure 39 Auto Dealer Control enabling and disabling Auto Dealer switch45                                               |
| Figure 40 Auto Dealer Control -<br>Auto Dealer Schedule Enabled switch45                                                |
| Figure 41 Auto Dealer Control –<br>Auto Dealer Start and Stop Times<br>45                                               |
| Figure 42 Auto Dealer Control -<br>Auto Dealer Start Enabled switch<br>46                                               |
| Figure 43 Auto Dealer Control –<br>Auto Dealer Start related alert<br>46                                                |
| Figure 44 Auto Dealer Control Day by Day Definition schedule table<br>47                                                |
| Figure 45 Removing a time range from Day by Day Definition schedule table48                                             |
| Figure 46 Adding a time range in Day by Day Definition: First click (start time)<br>48                                  |
| Figure 47 Adding a time range in Day by Day Definition: Second click (stop time) on<br>same day<br>48                   |
| Figure 48 Adding a time range in Day by Day Definition: first click on one day and second<br>click on another day<br>48 |
| Figure 49 Example of Auto Dealer Schedule setup to run continuously from Sunday to<br>Friday49                          |
| Figure 50 Change tracking features50                                                                                    |
| Figure 51 Live Audit Trail details<br>50                                                                                |

![](_page_4_Picture_0.jpeg)

## <span id="page-4-0"></span>**1 Introduction**

Liquidity providers retrieve price information from different channels to price their customers. The 360T Auto Dealing Suite (ADS) is a routing component embedded within 360T's trading platform that forwards negotiation requests and prices between the customer and a price source based on a custom ruleset. Price sources can be, inter alia, a pricing server,a manual trader, or a market link.

360T has offered so far two different Auto Dealing Suite (ADS) packages, both initially accessible via 360T's Business Configuration Tool (BCT): the "original ADS" and the "enhanced ADS". The latter is a successor solution that offers enhanced and improved rule management capabilities, including better organization and structuring, re-use enablement, override facilitation, and in general, user experience simplification.

The enhanced ADS is now also available in the Bridge Administration tool. This has the advantage that the enhanced ADS can be utilized in conjunction with the integrated Bridge Administration features, such as the Audit Trail.

Similarly, the ADS Control Panel is now also available in Bridge Administration tool as a new category called Auto Dealer Control. Additionally to the start/stop functionality, the new control panel has an enhanced schedule functionality that will be described ahead in Section [7.](#page-44-0)

The legacy ADS versions and ADS Control Panel currently available in the Business Configuration Tool (BCT) will be decommissioned. Clients utilizing legacy ADS in BCT will be upgraded to the Bridge Administration Tool. They will be contacted by the 360T CAS team with further information on the upgrade process.

![](_page_5_Picture_0.jpeg)

## <span id="page-5-0"></span>**2 Accessing the Autodealing Suite and Auto Dealer Control**

The Bridge Administration tool can then be accessed via the menu option "Administration" in the screen header of the Bridge application. Several features may be visible in the left menu depending on user permissions.

![](_page_5_Picture_3.jpeg)

**Figure 1 Access to Bridge Administration**

<span id="page-5-1"></span>The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools and actions for the user.

The enhanced ADS can be accessed by clicking on the "ADS Rules" icon as shown in [Figure](#page-6-0) [3.](#page-6-0)

![](_page_6_Picture_0.jpeg)

![](_page_6_Picture_1.jpeg)

**Figure 2 Bridge Administration: Homepage**

<span id="page-6-0"></span>Entities accessible to the user are displayed in the left navigation panel. The user can select the entity for which the ADS rules are to be configured.

<span id="page-6-1"></span>![](_page_6_Picture_4.jpeg)

**Figure 3 Entity selection to reach the ADS overview page**

![](_page_7_Picture_0.jpeg)

This opens the Auto Dealing Suite Rules tool for the selected entity and displays all administration functions enabled for the user.

|                                                        |                                                         |                                                         |                         | $\vee$ Preferences<br>$\vee$ Administration       | $Q \bullet AA - P \times$<br>$\vee$ Help                           |
|--------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|-------------------------|---------------------------------------------------|--------------------------------------------------------------------|
|                                                        | <b>TRADER WORKSHEET</b><br><b>BRIDGE ADMINISTRATION</b> | $+$                                                     |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        | Q 泰   1   三  <br>$\overline{\left( \right. }%$          | <b>ADS Configuration Groups</b><br><b>ADS RFS Rules</b> | <b>ADS Orders Rules</b> | <b>Currency Blacklist</b><br><b>ADS SEP Rules</b> | ☆<br>$n \approx \equiv$                                            |
| 合                                                      |                                                         |                                                         |                         |                                                   |                                                                    |
| $\mathcal{L}_{\mathcal{F}}$                            | TT 360TBANK EMEA.TEST                                   | <b>Currency Groups</b><br><b>Currency Couple Groups</b> | Notional Amount Groups  | Margin Groups<br>FX Time Period Groups            | Activa $\gg$<br>MM Time Period Groups                              |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         | Group Name                                              |                         |                                                   |                                                                    |
| <sup>(1)</sup>                                         |                                                         | Default Group                                           |                         |                                                   | aI m                                                               |
|                                                        |                                                         |                                                         |                         |                                                   | Create Group                                                       |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
|                                                        |                                                         |                                                         |                         |                                                   |                                                                    |
| *<br>-                                                 |                                                         |                                                         |                         |                                                   |                                                                    |
| $\mathbb C$                                            |                                                         | <b>Create Change Request</b>                            |                         |                                                   | Discard All Changes<br>Save                                        |
| $\bigcirc$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                                                         |                                                         |                         |                                                   |                                                                    |
| $\circ$                                                |                                                         | 360TBANK EMEA.TEST $\cdot$ $\times$                     |                         |                                                   |                                                                    |
|                                                        | 360TBANKEMEA.Trader6, 360TBANK EMEA.TEST // DEMO        |                                                         | <b>EECT</b>             |                                                   | Mo, 04. Mai 2020, 12:17:13 GMT // Connected [FFM] ·<br><b>DEMO</b> |

<span id="page-7-0"></span>**Figure 4 ADS Rules Tool with Configuration Groups and ADS functions**

The Auto Dealer Control can be accessed by clicking on the correspondent icon as shown in [Figure 2.](#page-6-0)

Entities accessible to the user are displayed in the left navigation panel. The user can select the entity for which the Auto Dealer Control is to be configured, as shown in [Figure 3.](#page-6-1)

This opens the Auto Dealer Control tool for the selected entity and displays all administration functions enabled for the user.

| <b>TRADER WORKSHEET</b>                                         | <b>BRIDGE ADMINISTRATION</b><br>$+$                                     |                                         |                                      |                           | $\vee$ Preferences $\vee$ Administration | $\vee$ Help $\sum$ <sup>1</sup> ( <b>)</b> AA - $\Box$ X |  |                                      |
|-----------------------------------------------------------------|-------------------------------------------------------------------------|-----------------------------------------|--------------------------------------|---------------------------|------------------------------------------|----------------------------------------------------------|--|--------------------------------------|
| Q 泰   1 m<br>合<br><b>血 BankD</b><br>$\mathcal{L}_{\mathcal{T}}$ | <b>RFS Auto Dealer Configuration</b><br><b>RFS Auto Dealer Schedule</b> | <b>Orders Auto Dealer Configuration</b> | <b>SEP Auto Dealer Configuration</b> |                           |                                          |                                                          |  | $\varphi_1 \circ \varphi_2 \equiv 0$ |
| Loo                                                             |                                                                         |                                         | RFS Auto Dealer Enabled              | <b>CO</b> Enabled         |                                          |                                                          |  |                                      |
| $\Rightarrow$                                                   |                                                                         |                                         | RFS Auto Dealer Schedule Enabled     | C Enabled                 |                                          |                                                          |  |                                      |
|                                                                 |                                                                         |                                         | RFS Auto Dealer Start Enabled        | <b>O</b> Enabled          |                                          |                                                          |  |                                      |
|                                                                 |                                                                         |                                         | Day by Day Definition                | O o Disabled              |                                          |                                                          |  |                                      |
|                                                                 |                                                                         |                                         | RFS Auto Dealer Start Time           | $\bullet$ 06:00 $\bullet$ |                                          |                                                          |  |                                      |
|                                                                 |                                                                         |                                         |                                      | (07:00 CEST)              |                                          |                                                          |  |                                      |
|                                                                 |                                                                         |                                         | RFS Auto Dealer Stop Time            | 22:00<br>(23:00 CEST)     |                                          |                                                          |  |                                      |
|                                                                 |                                                                         |                                         |                                      |                           |                                          |                                                          |  |                                      |
|                                                                 |                                                                         |                                         |                                      |                           |                                          |                                                          |  |                                      |

<span id="page-7-1"></span>**Figure 5 Auto Dealer Control Panel for RFS, Orders and SEP Configuration**

![](_page_8_Picture_0.jpeg)

## <span id="page-8-0"></span>**3 ADS Configuration Groups**

The enhanced ADS allows the user to define a wide range of rules using customized and centrally managed **parameters** such as currencies, currency pairs, notional ranges, rule activation time windows, maturities, etc. which can be defined within the ADS Configuration Groups, refer to [Figure](#page-8-1) 7.

A rule is a combination of:

- Conditions, which define the specific criteria/constraints that trigger an action
- Outcome, which defines the action to be undertaken when the said criteria are met

Every variation in a parameter that makes up a condition or an outcome results in a new rule.

| <b>TRADER WORKSHEET</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                         | <b>BRIDGE ADMINISTRATION</b> | $\overline{+}$                                                                           |                                                          | $\vee$ Preferences                           | $\vee$ Administration                                     | $\vee$ Help | $\odot$<br>△<br>AA                        | ෙ<br>-                                                                  |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|------------------------------------------------------------------------------------------|----------------------------------------------------------|----------------------------------------------|-----------------------------------------------------------|-------------|-------------------------------------------|-------------------------------------------------------------------------|
| Q<br>泰<br>$\perp$<br>$\mathbb{R}$<br>合<br>TT 360TBANK EMEA.TEST<br>$\mathcal{L}_{\mathcal{F}}$                                                                                                                                                                                                                                                                                                                                                                                  | <b>Currency Groups</b>       | <b>ADS Configuration Groups</b><br><b>ADS RFS Rules</b><br><b>Currency Couple Groups</b> | <b>ADS Orders Rules</b><br><b>Notional Amount Groups</b> | <b>ADS SEP Rules</b><br><b>Margin Groups</b> | <b>Currency Blacklist</b><br><b>FX Time Period Groups</b> | ☆           | <b>MM Time Period Groups</b>              | $\begin{array}{ccc} \circ & \circ & \equiv \end{array}$<br>Activa $\gg$ |
| $\frac{1}{2} \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac{1}{2} \right) \left( \frac$<br>e |                              | Group Name<br>Default Group                                                              |                                                          |                                              |                                                           |             | a<br>I $\bar{\mathbb{B}}$<br>Create Group |                                                                         |

<span id="page-8-1"></span>**Figure 6 Administration of Configuration Groups**

The available parameters in the ADS Configuration Groups are:

- Currency Groups
- Currency Couple Groups
- Notional Amount Groups
- Margin Groups
- FX Time Period Groups
- MM Time Period Groups
- Activation Period Groups
- Manual Routing Groups
- Fixing Reference Groups

In general, ADS Configuration Groups can be re-used across the different trading workflow related ADS rules, including ADS RFS Rules, ADS Orders

![](_page_9_Picture_0.jpeg)

Rules, and ADS SEP Rules. Groups for the parameters "Products", "Requestors" and "Providers" are bound to the workflow. In terms of RFS, the groups for these parameters are configurable under the menu item "ADS RFS Rules", refer to Section [4](#page-24-0) for more information.

This section focuses on the available "Configuration Groups" within the enhanced ADS. The order of the Configuration Groups, as shown in the enhanced ADS, is reflected in the same order in the subsequent sub-sections.

### <span id="page-9-0"></span>**3.1 Defining Currency Groups**

Currency Groups are intended to allow the classification of currencies. Once created, a currency group can be used to simplify rule creation for interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and Tri Party Repo, wherein a single rule can be created for all currencies specified in the group instead of creating multiple rules per currency. Currencies can be added or removed from the group in a central place without the need to edit the rules themselves.

| <b>ADS Configuration Groups</b> |                               | <b>ADS RFS Rules</b> | <b>ADS Orders Rules</b> | ADS SEP Rules        | <b>Currency Blacklist</b> | ☆                            |                              | $\sqrt{2}$<br>$\mathcal{P}$     | 亖             |
|---------------------------------|-------------------------------|----------------------|-------------------------|----------------------|---------------------------|------------------------------|------------------------------|---------------------------------|---------------|
| <b>Currency Groups</b>          | <b>Currency Couple Groups</b> |                      | Notional Amount Groups  | <b>Margin Groups</b> |                           | <b>FX Time Period Groups</b> | <b>MM Time Period Groups</b> | <b>Activation Period Groups</b> | $\rightarrow$ |
|                                 |                               | <b>Group Name</b>    |                         |                      |                           |                              |                              |                                 |               |
|                                 |                               | Default Group        |                         |                      |                           |                              | al 面                         |                                 |               |
|                                 |                               |                      |                         |                      |                           |                              | Create Group                 |                                 |               |
|                                 |                               |                      |                         |                      |                           |                              |                              |                                 |               |

<span id="page-9-1"></span>**Figure 7 Currency Groups**

![](_page_10_Picture_0.jpeg)

The Default Group encompasses all currencies. This group cannot be removed or renamed. However, the values in this group can be altered as described below.

**Create Group** – Allows the creation of new groups. The user can specify a name which is appropriate for his purposes

| Please, provide a name for a new group |  |
|----------------------------------------|--|
| Special MM currencies                  |  |
|                                        |  |
|                                        |  |
| Cancel<br><b>Create Group</b>          |  |

<span id="page-10-0"></span>**Figure 8 Creation of a Single Currency Group**

**Rename Group** – Provides the ability to change the display label of the group without impacting any of the rules

**Delete Group** – Deletes a configured group and all its values. In case there are auto dealing destination rules where this group is used, the group cannot be deleted.

Note that a copy function of rules is currently not available.

To view the details of the configured group, the user can click on the Currency Group name in the list of Groups.

![](_page_11_Picture_0.jpeg)

| Default Group                |                              |                                           | aT<br>而             |
|------------------------------|------------------------------|-------------------------------------------|---------------------|
| Special MM curencies         |                              |                                           | aI<br>画             |
|                              |                              |                                           | Create Group        |
|                              |                              | Select Members for "Special MM curencies" | $\odot$<br>$\times$ |
| <b>Available Currencies</b>  |                              | <b>Selected Currencies</b>                |                     |
| $- - - -$<br>CAD             | $\left\langle \right\rangle$ |                                           |                     |
| CHF                          |                              |                                           |                     |
| <b>EUR</b>                   |                              |                                           |                     |
| <b>GBP</b>                   |                              |                                           |                     |
| <b>HKD</b>                   | $\gg$                        |                                           |                     |
| <b>JPY</b>                   |                              |                                           |                     |
| <b>Create Change Request</b> |                              | <b>Discard All Changes</b>                | Save                |

<span id="page-11-1"></span>**Figure 9 Selection of a Currency Group**

When a group is selected, two columns of values are presented to the user. The values on the left represent all possible values for the parameter i.e. currency in this case. The values on the right are the values that the user has selected to be part of the group. The user can add or remove values from the group by using the arrow buttons between the columns of values. After the currencies are moved to the right columns, the "Save" button must be pressed to persist the currency group. If currencies were moved to a column by mistake, the "Discard All Changes" can be used to undo the changes.

The system does not restrict the creation of groups with an overlapping set of currencies.

**Note:** All Configuration Groups employ a similar paradigm for creation, renaming and removal of groups. Hence, these parts are not repeated in the subsequent sections

## <span id="page-11-0"></span>**3.2 Defining Currency Couple Groups**

Currency Couple Groups allow bucketing of currency pairs. Once created, a currency couple group can be used to simplify rule definition, among others, for FX Spots, Forwards, Swaps, NDF, NDS, Options and Block Trades.

The Default Group encompasses all currency pairs, denoted as \*\*\* / \*\*\* for the base and quote currency, respectively.

![](_page_12_Picture_0.jpeg)

Currency Couple Groups can be created, removed and renamed similarly to Currency Groups, as explained in Section [3.1.](#page-9-0)

| <b>Group Name</b>                                                 |                             |                                   |
|-------------------------------------------------------------------|-----------------------------|-----------------------------------|
| Default Group                                                     |                             | aI<br>面                           |
| Majors                                                            |                             | aI<br>Ш                           |
| Exotics                                                           |                             | aI<br>画                           |
|                                                                   | Select Members for "Majors" | $\odot$<br>$\times$               |
| <b>Base Currency</b>                                              | <b>Quote Currency</b>       |                                   |
| <b>EUR</b>                                                        | CHF                         | Ū<br>$\mathcal{I}$                |
| <b>EUR</b>                                                        | <b>DKK</b>                  | 而<br>$=$ /                        |
| <b>Special MM curencies</b>                                       | <b>AUD</b><br>$\wedge$      | $\checkmark$<br>Ū<br>$\checkmark$ |
| Default Group<br><b>Special MM curencies</b><br>***<br><b>AUD</b> | 0                           | Add Currency Couple               |
| CAD<br>Cr <sub>f</sub><br>CHF<br><b>EUR</b>                       |                             | Discard All Changes<br>Save       |
| 36<br>GBP                                                         |                             |                                   |

<span id="page-12-0"></span>**Figure 10 Selection of a Currency Couple Group**

Within a group (including the Default Group), the button "Add Currency Couple" creates a new line in which a currency pair can be defined by selecting base and quote currency. Newly created currency pairs must be locked in via the button before they can be saved.

Currency Groups, which were previously configured, are also available from the base- or quote currency drop-down menus. This facilitates the coverage of all currencies of the currency group within a single currency pair definition.

New currency pairs can be added to, or removed from a group, thus providing the ability to impact targeted rules, whilst not needing to manipulate rules individually.

The system does not restrict the creation of groups with an overlapping set of currencies.

![](_page_13_Picture_0.jpeg)

### <span id="page-13-0"></span>**3.3 Defining Notional Amounts Groups**

Typically, the range of notional amounts is an important factor in determining the pricing and routing mechanisms for a product, besides others. The Notional Amounts Group allows to bucket ranges of continuous or discontinuous notional amounts within a group. This group, when created, can be used in rule definition across products, currencies, and currency pairs.

The default notional group includes all notional amount, set up as zero to unlimited, which can be modified to hold a different set of values.

Notional Amount Groups can be created, removed and renamed similarly to Currency Groups, as explained in Section [3.1.](#page-9-0)

| <b>Currency Groups</b> | <b>Currency Couple Groups</b> | <b>Notional Amount Groups</b> | <b>Margin Groups</b> | FX Time Period Groups              | <b>MM Time Period Groups</b> | <b>Activation Period Groups</b>               |
|------------------------|-------------------------------|-------------------------------|----------------------|------------------------------------|------------------------------|-----------------------------------------------|
|                        |                               |                               |                      |                                    |                              |                                               |
|                        |                               | Group Name                    |                      |                                    |                              |                                               |
|                        |                               | Default Group                 |                      |                                    |                              | aI面                                           |
|                        |                               | Small amounts                 |                      |                                    |                              | $a$ <sup><math>\boxed{a}</math></sup>         |
|                        |                               |                               |                      |                                    |                              | <b>Create Group</b>                           |
|                        |                               |                               |                      |                                    |                              |                                               |
|                        |                               |                               |                      |                                    |                              |                                               |
|                        |                               |                               |                      |                                    |                              |                                               |
|                        |                               |                               |                      | Select Members for "Small amounts" |                              |                                               |
|                        |                               | From                          |                      | To                                 |                              |                                               |
|                        |                               | $\overline{0}$                |                      | 100,000                            |                              | $\widehat{\text{III}}$<br>$\bar{z}/$          |
|                        |                               |                               |                      |                                    |                              |                                               |
|                        |                               | 100,000                       |                      | 500,000                            |                              | Ŵ<br>$\left\lfloor \mathcal{N} \right\rfloor$ |
|                        |                               |                               |                      |                                    |                              | Add Notional Amount Range                     |
|                        |                               |                               |                      |                                    |                              |                                               |

<span id="page-13-1"></span>**Figure 11 Selection of a Notional Amount Group**

Within a group, the lower and upper bounds of notional amounts can be used to establish a notional range. If there is a need to establish a discontinuous range of amounts within a group, this can be achieved by creating new rows provided by the "Add Notional Amount Range" option.

Note: The notional amounts are expressed in the home currency of the Liquidity Provider (entity using the ADS).

The "Delete" option can be used to remove notional ranges that are not required. The lower and upper bounds are both included in the range of notional amounts.

The system does not restrict the creation of groups with overlapping amounts as these groups could have applicability in different rules.

![](_page_14_Picture_0.jpeg)

### <span id="page-14-0"></span>**3.4 Defining Margin Groups**

A feature of the ADS is the configuration of margins which can be applied to the price received from a price source before delivering the end price to the customers.

Margins can be organized and structured within the enhanced Auto Dealer Suite and denoted in different ways e.g. percent, pips, or a fixed amount.

Different hierarchy of margins can be created and applied to requests, in conjunction with other conditions. For example, margin tiers can be established by requestor (customer) groups and then used in rules across all products for the said customers.

Margin tiers can be achieved by first creating different Margin Groups as shown in the below illustration.

| <b>ADS Configuration Groups</b> | <b>ADS RFS Rules</b>          | <b>ADS Orders Rules</b>       | <b>ADS SEP Rules</b> | ⇔<br><b>Currency Blacklist</b> | $\sqrt{ }$                   | 目<br>$\sqrt{2}$       |
|---------------------------------|-------------------------------|-------------------------------|----------------------|--------------------------------|------------------------------|-----------------------|
| <b>Currency Groups</b>          | <b>Currency Couple Groups</b> | <b>Notional Amount Groups</b> | <b>Margin Groups</b> | <b>FX Time Period Groups</b>   | <b>MM Time Period Groups</b> | $\rightarrow$<br>Acti |
|                                 | Group Name                    |                               |                      |                                |                              |                       |
|                                 | Default Group                 |                               |                      |                                | aII                          |                       |
|                                 | New Group 1                   |                               |                      |                                | aIf                          |                       |
|                                 |                               |                               |                      |                                | <b>Create Group</b>          |                       |
|                                 |                               |                               |                      |                                |                              |                       |
|                                 |                               |                               |                      |                                |                              |                       |
|                                 |                               |                               |                      |                                |                              |                       |
|                                 |                               |                               |                      |                                |                              |                       |
|                                 |                               |                               |                      |                                |                              |                       |

<span id="page-14-1"></span>**Figure 12 Margin Groups**

The "Create Group" option enables the creation of margin tiers. The margin group name can be edited to have something meaningful that is instantly recognizable when defining rules.

The "Delete" option facilitates the removal of margin categories that are not needed anymore.

A Default Group exists, which is by default set without any margin for any of the products.

The enhanced ADS offers several **margin types** which can be categorized into either a "constant margin" or "variable margin".

Constant margin means that a pre-defined margin will be added/deducted from the base price. This margin can either be expressed in pips, in percent or a fixed amount in the home currency, according to the selection in this field.

![](_page_15_Picture_0.jpeg)

Hybrid margins allows the spot bid/offer margin to be defined in pips and the forward bid/offer margin or the swap bid/offer margin to be set in percent. Hybrid margin type is available for FX Spot, Forward and Swap products.

Please note that the fixed amount margin is added to/deducted from the opposite amount after which it is converted into a markup/markdown in pips of the effective rate provided to the subsidiary/customer. Due to the defined precision of the exchange rate, depending on the notional amount of the trade, the fixed amount margin might have no impact at all (large notional) or lead to an abnormal effective rate (small notional).

Variable margin maintains the price constant while the margin changes during the price negotiation based on the market price.

| <b>ADS Configuration Groups</b><br><b>ADS RFS Rules</b><br><b>ADS Orders Rules</b>       | ♦<br><b>Currency Blacklist</b><br><b>ADS SEP Rules</b>                    |                                                                         |                                   |                              | $\text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and}\quad \text{and$ |
|------------------------------------------------------------------------------------------|---------------------------------------------------------------------------|-------------------------------------------------------------------------|-----------------------------------|------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Currency Groups</b><br><b>Currency Couple Groups</b><br><b>Notional Amount Groups</b> | <b>Margin Groups</b><br><b>FX Time Period Groups</b>                      | <b>MM Time Period Groups</b>                                            | <b>Activation Period Groups</b>   | <b>Manual Routing Groups</b> | <b>Fixing Reference Groups</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
|                                                                                          | Group Name<br>Default Group<br>New Group 1                                |                                                                         | a <br>al f<br><b>Create Group</b> |                              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|                                                                                          |                                                                           |                                                                         |                                   |                              | $-$ 0 $\times$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
|                                                                                          | Margin Type                                                               | Margins for "Default Group"<br>Pips                                     | ⌒                                 |                              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|                                                                                          | <b>Bid Spot Margin</b><br>Offer Spot Margin<br>Min Spot Spread            | Pips<br>Percent<br>Hybrid<br>Fixed Amount (Home CCY)<br>Variable Margin |                                   |                              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|                                                                                          | <b>Bid Forward Margin</b><br>Offer Forward Margin<br>Min Forward Spread   | 0.000<br>0.000<br>0.000                                                 |                                   |                              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|                                                                                          | Bid Swap Margin<br>Offer Swap Margin<br>Apply spot margin to uneven swaps | 0.000<br>0.000<br>O 0 Disabled                                          |                                   |                              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|                                                                                          | <b>Bid Future Margin</b><br>Offer Future Margin                           | 0.0<br>0.0                                                              |                                   |                              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| Create Change Request<br>BankH $\times$                                                  |                                                                           |                                                                         |                                   |                              | Discard All Changes ) (Save                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |

<span id="page-15-0"></span>**Figure 13 Margin Types**

#### **1.1.1 Constant margin parameters**

When either "Pips", "Percent" or "Fixed Amount" is selected from the "Margin Type" field, the configuration options capture margins for spots, forwards, swaps, futures and interest rate products. This allows the same Margin Group to be re-used across product types. However, depending on the product in the incoming request, the system applies the appropriate margin. The details of each configuration option are listed below:

**Bid Spot Margin**: This margin will be deducted from the spot price when the client requests a sell quote. The margin is expressed in pips based on intervals of one decimal place, in percent with a maximum of 3 decimal places or as a

![](_page_16_Picture_0.jpeg)

fixed amount in the home currency. This margin can be set up for the instrument FX spot, FX forward, NDF and Block-Trade.

**Offer Spot Margin**: This margin will be added to the spot price when the client requests a buy quote. The margin is expressed in pips based on intervals of one decimal place in percent with a maximum of 3 decimal places or as a fixed amount in the home currency. This margin can be set up for the instrument FX spot, FX forward, NDF and Block-Trade.

**Bid Forward Margin**: This margin will be deducted from the forward points when the client requests to sell forward. The margin is expressed in pips based on intervals of 3 decimal places, in percent with a maximum of 3 decimal places. This margin can be set up for the instrument FX forward, NDF and Block-Trade.

**Offer Forward Margin**: A margin will be added to the forward points when the client requests to buy forward. The margin is expressed in pips based on intervals of 3 decimal places, in percent with a maximum of 3 decimal places. This margin can be set up for the instrument FX forward, NDF and Block-Trade.

**Bid Swap Margin**: For any swap request, ADS applies only swap margin (either bid or offer based on the side of the action). In the case of a sell swap request, a defined `bid swap margin` will be deducted from the swap points of the non-spot leg. In case both legs are non-spot, for ex. near leg= today, far leg = 1 month, then the margin will be deducted from the swap points of the far leg.

The margin can be defined in terms of a) pips based on intervals of 3 decimal places, b) in percent with a maximum of 3 decimal places or c) as a fixed amount in the home currency. Bid Swap Margins expressed in percentage terms are calculated using the spot rate and subtracted from the swap points.

Note: Spot and Forward margins (as well as other margin parameters) are not used for FX Swap requests. Similarly, swap margins are only regarded to calculate margins for swap requests and are not effective for any other instrument.

**Offer Swap Margin**: For any swap request, ADS applies only swap margin (either bid or offer based on the side of the action). In case of a buy swap request, the defined `offer swap margin` will be deducted from the swap points of the non-spot leg. In case both legs are non-spot, for example near leg= today, far leg = 1 month, then the margin will be deducted from the swap points of the far leg.

The margin can be defined in terms of a) pips based on intervals of 3 decimal places, b) in percent with a maximum of 3 decimal places or c) as a fixed amount in the home currency.

![](_page_17_Picture_0.jpeg)

Offer Swap Margins expressed in percentage terms are calculated using the spot rate and subtracted from the swap points.

Note: Spot and Forward margins (as well as other margin parameters) are not used for FX Swap requests. Similarly, swap margins are only regarded to calculate margins for swap requests and are not effective for any other instrument.

**Apply spot margin to uneven swaps:** this option allows to apply a margin to the spot rate of uneven swaps. The margin will be applied to the side and swap leg with the higher amount. It is available for pips, percentage and hybrid margin types.

- Example 1: Client buys 3 million EUR/USD spot and sells 2 million EUR/USD in 1 month. Since the client buys the overhanging amount, the defined Offer Spot Margin will be added to the spot rate. The application of the swap point margin is unchanged, in this case the Bid Swap Margin on the all-in far rate.
- Example: Client buys 2 million EUR/USD spot and sells 3 million EUR/USD in 1 month. Since the client sells the overhanging amount, the defined Bid Spot Margin will be subtracted from the spot rate. The application of the swap point margin is unchanged, in this case also the Bid Swap Margin on the all-in far rate.

**Bid Future Margin**: This margin will be deducted from the FX Future price when the client requests a sell quote. The margin is expressed in pips based on intervals of one decimal place, in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.

**Offer Future Margin**: This margin will be added to the FX Future price when the client requests a buy quote. The margin is expressed in pips based on intervals of one decimal place in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.

**Interest Margin**: A margin will be added to the interest rate if the client requests a quote for a loan; or deducted if the client requests a quote for a deposit. The margin is expressed in basis points (using the selection "pips") or in percent. It can be set up for money market instruments Loan and Deposit.

#### **3.4.1 Variable margin parameters**

**Fix Bid Price:** Non-changing bid price can be defined for FX Spot and FX Forward instruments. The bid price provided to the customer remains unchanged throughout the RFQ negotiation. Fix Bid Price for FX Forward instruments refers to the Forward rate while the underlying FX Spot price might fluctuate. In the case of a market link scenario, the FX Spot bid price is taken from the market link provider. If the price request is routed to the Pricing Server, the FX Spot bid price offered to the customer corresponds to the FX Spot bid rate as provided by the Pricing Server.

![](_page_18_Picture_0.jpeg)

**Fix Offer Price:** Non-changing offer price can be defined for FX Spot and FX Forward instruments. The offer price provided to the customer remains unchanged throughout the RFQ negotiation. Fix Offer Price for FX Forward instruments refers to the Forward rate while the underlying FX Spot price might fluctuate. In the case of a market link scenario, the FX Spot offer price is taken from the market link provider. If the price request is routed to the Pricing Server, the FX Spot offer price exposed to the customer corresponds to the FX Spot offer rate as provided by the Pricing Server.

**Min Variable Margin:** A threshold margin (in PIPS) which prevents quotation if margin fluctuates below the specified amount due to varying market prices.

![](_page_19_Picture_0.jpeg)

### <span id="page-19-0"></span>**3.5 Defining FX Time Period Groups**

For products involving a maturity date/tenor, it is possible to encapsulate maturity ranges within the FX Time Period Groups. These groups can be applied to rules involving all products other than Spots and Multi-leg swaps where these are not relevant.

For example, if maturities with certain ranges are to be directed to different market link groups, for all products, these can be accomplished by creating different groups of maturities.

The Default Group encompasses all possible maturities, expressed as TODAY to UNLIMITED. As with others, the values in the default group can be modified, but the group itself cannot be removed or renamed.

Groups to hold maturity ranges can be created, removed and renamed similarly to Currency Groups, as explained in Section [3.1.](#page-9-0)

| <b>Margin Groups</b> | FX Time Period Groups | MM Time Period Groups | <b>Activation Period Groups</b>  | <b>Manual Routing Groups</b> |                                       | <b>Fixing Reference Groups</b> |
|----------------------|-----------------------|-----------------------|----------------------------------|------------------------------|---------------------------------------|--------------------------------|
|                      | <b>Group Name</b>     |                       |                                  |                              |                                       |                                |
|                      | Default Group         |                       |                                  |                              | a】 画                                  |                                |
|                      | Special range         |                       |                                  |                              | $a$ <sup><math>\parallel</math></sup> |                                |
|                      |                       |                       |                                  |                              | $ -$                                  |                                |
|                      |                       |                       | Time Periods for "Special range" |                              |                                       |                                |
|                      | From                  |                       | To                               |                              |                                       |                                |
|                      | <b>TODAY</b>          |                       | 1 WEEK                           |                              | $\mathbb{Z}^{\mathbb{Z}}$             | 画                              |
|                      | 1 MONTH               |                       | 2 MONTHS                         |                              | $\mathcal{I}$                         | Ū                              |
|                      |                       |                       |                                  |                              | Add Time Period                       |                                |
|                      |                       |                       |                                  |                              |                                       |                                |

#### <span id="page-19-1"></span>**Figure 14 FX Time Period Groups**

A tenor range can be defined within a group. The ability to add discontinuous tenor ranges is provided via the "Add Time Period" option.

For example, in the above illustration, there could be a need to route tenors between 1 WEEK to 1 MONTH to a different market link group. Here the ability to create discontinuous tenor ranges comes in handy, as maturities from TODAY to 1 WEEK (and) 1 MONTH to 2 MONTHS are routed similarly.

The "Delete" option can be used to remove tenor ranges that are no longer required.

![](_page_20_Picture_0.jpeg)

Tenors are defined as a range of maturities, with both "From" and "To" values included. A tenor can form part of different groups to allow their use in different rules.

### <span id="page-20-0"></span>**3.6 Defining MM Period Groups**

If Period Groups are required specifically for Money Market (MM) instruments, then the MM Period Groups can be used. The MM Period Groups are relevant for ADS RFS Rules.

The default time period group covers all possible maturities related to Money Market instruments, expressed as OVERNIGHT to UNLIMITED. As with others, the values in the Default Group can be modified, but the group itself cannot be removed or renamed.

Groups to hold maturity ranges can be created, removed and renamed similarly to Currency Groups, as explained in Section [3.1.](#page-9-0)

| <b>Margin Groups</b> | <b>FX Time Period Groups</b> | <b>MM Time Period Groups</b> | <b>Activation Period Groups</b>        | <b>Manual Routing Groups</b> | <b>Fixing Referen</b>  |   |
|----------------------|------------------------------|------------------------------|----------------------------------------|------------------------------|------------------------|---|
|                      | <b>Group Name</b>            |                              |                                        |                              |                        |   |
|                      | Default Group                |                              |                                        |                              | aT                     | 面 |
|                      | <b>Short MM Maturities</b>   |                              |                                        |                              | aI                     | 画 |
|                      |                              |                              |                                        |                              | $P_{i}$                |   |
|                      |                              |                              | Time Periods for "Short MM Maturities" |                              |                        |   |
|                      | From                         |                              | To                                     |                              |                        |   |
|                      | OVERNIGHT                    |                              | <b>TOMNEXT</b>                         |                              | $\mathcal{I}'$         | 而 |
|                      | SPOTNEXT                     |                              | 1 WEEK                                 |                              | $\mathcal{I}'$         | û |
|                      |                              |                              |                                        |                              | <b>Add Time Period</b> |   |

<span id="page-20-2"></span>**Figure 15 Money Market Time Period Groups**

The configuration options available within the MM Period Groups are in line with the FX Time Period Groups, refer to Section [3.5](#page-19-0) for more details.

### <span id="page-20-1"></span>**3.7 Defining Activation Period Groups**

![](_page_21_Picture_0.jpeg)

Often, the set of rules that apply depend on the time of the day the request was made. The ADS capabilities allow the definition of time periods, which can be used in conjunction with other parameters to simplify rule creation and vastly reduce the number of rules to achieve the same outcome.

The Default Group encompasses all times of the day, denoted by a time window from 00:00 GMT to 00:00 GMT representing start and end times.

Note: In the field "Time Zone", it is possible to have a time zone defined other than UTC. In case the Company time zone is a time zone where Daylight Savings Time (DST) is considered, there is no need for manual adjustments of any rules to accommodate DST. The system will automatically take DST into account.

Groups to hold activation time windows can be created, removed and renamed similarly to Currency Groups, as explained in Section [3.1.](#page-9-0)

| <b>Notional Amount Groups</b>         | <b>Margin Groups</b> | <b>FX Time Period Groups</b> |                 | <b>MM Time Period Groups</b>           | <b>Activation Period Groups</b> |                          |
|---------------------------------------|----------------------|------------------------------|-----------------|----------------------------------------|---------------------------------|--------------------------|
|                                       |                      |                              |                 |                                        |                                 |                          |
| Time zone                             |                      |                              |                 | UTC, +00:00 (UTC+00:00)                | $\checkmark$                    |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
| <b>Group Name</b>                     |                      |                              |                 |                                        |                                 |                          |
| Default Group                         |                      |                              |                 |                                        | aŢ                              | $\widehat{\mathbb{III}}$ |
| New time schedule                     |                      |                              |                 |                                        |                                 | 勔<br>aI                  |
|                                       |                      |                              |                 |                                        | <b>Create Group</b>             |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 | Select Members for "New time schedule" |                                 |                          |
| <b>Start Time</b>                     |                      |                              | <b>End Time</b> |                                        |                                 |                          |
| 09:00                                 |                      |                              | 17:00           |                                        |                                 | 勔<br>$\bar{z}$ /         |
| $17:00$ $\bigoplus$<br>$\blacksquare$ |                      |                              | ◒               | $00:00$ $\bigoplus$                    |                                 | $\sqrt{}$<br>ŵ           |
|                                       |                      |                              |                 |                                        | <b>Add Activation Period</b>    |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |
|                                       |                      |                              |                 |                                        |                                 |                          |

<span id="page-21-0"></span>**Figure 16 Display of the selection of an Activation Period Group**

Within a group, an activation time window can be defined. The user can define a broken / discontinuous activation time for the group by simply adding more than a one-time window. For example, let's assume that Swaps are manually priced and the Swap desk is situated in Singapore and New York. In this case, a

![](_page_22_Picture_0.jpeg)

group can be created to cover the availability times of this desk, and this group would contain a row each to hold the availability time in each city.

The "Delete" option can be used to remove time windows that are no longer required.

Time periods are defined as a range, expressed by a start and end time. The time is set and stored in GMT.

The system allows the creation of groups with overlapping activation time windows as these groups could have applicability in different rules.

### <span id="page-22-0"></span>**3.8 Defining Manual Routing Groups**

360T ADS facilitates the routing of client requests to physical traders who are using the 360T Trader Worksheet (TWS) to manually provide prices. Based on the conditions of the routing rules, it might be necessary to route a request to a pre-defined group of traders. These can be defined in the section "Manual Routing Groups".

| <b>Currency Couple Groups</b> | <b>Notional Amount Groups</b>            | <b>Margin Groups</b>     | <b>FX Time Period Groups</b>                                                                                                                                                                                                                                                                                                                              | <b>MM Time Period Groups</b> | <b>Activation Period Groups</b>                        | <b>Manual Routing Groups</b> |
|-------------------------------|------------------------------------------|--------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|--------------------------------------------------------|------------------------------|
|                               | Group Name<br>Default Group<br>Swap desk |                          |                                                                                                                                                                                                                                                                                                                                                           |                              | aI 面<br>a $I \parallel \hat{w}$<br><b>Create Group</b> |                              |
|                               |                                          |                          | Select Members for "Swap desk"                                                                                                                                                                                                                                                                                                                            |                              |                                                        |                              |
|                               |                                          | <b>Available Traders</b> |                                                                                                                                                                                                                                                                                                                                                           | <b>Selected Traders</b>      |                                                        |                              |
|                               | 360TBANKEMEA.Trader1                     |                          |                                                                                                                                                                                                                                                                                                                                                           | 360TBANKEMEA.Trader2         |                                                        |                              |
|                               | 360TBANKEMEA.Trader5                     |                          | $\geq$                                                                                                                                                                                                                                                                                                                                                    | 360TBANKEMEA.Trader4         |                                                        |                              |
|                               | 360TBANKEMEA.Trader3                     |                          | $\overline{\mathcal{L}}$                                                                                                                                                                                                                                                                                                                                  |                              |                                                        |                              |
|                               | 360TBANKEMEA.Trader6                     |                          | $\begin{picture}(20,20)(-20,0) \put(0,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,0){\line(1,0){10}} \put(15,$<br>$\ll$ |                              |                                                        |                              |
|                               |                                          |                          |                                                                                                                                                                                                                                                                                                                                                           |                              |                                                        |                              |

<span id="page-22-1"></span>**Figure 17 Manual Routing Group configuration**

Manual routing groups are added and defined by a name. Traders can be assigned to these groups by moving them from "Available Traders" to "Selected Traders" using the arrow buttons.

![](_page_23_Picture_0.jpeg)

Subsequently, these groups can be referred to in the column **Manual Routing Group** in the RFS Satellite Rules definition.

## <span id="page-23-0"></span>**3.9 Defining Fixing Reference Groups**

In case NDF and NDS requests are priced, inter alia, based on Fixing Reference conditions then the Fixing Reference Groups feature can be used.

The default fixing reference group encompasses all fixing references, denoted as \*\*\* / \*\*\* Pre- Agreed Fixing.

Groups of Fixing References can be created, removed and renamed similarly to Currency Groups, as explained in Section [3.1.](#page-9-0)

![](_page_23_Picture_6.jpeg)

**Figure 18 Fixing Reference Groups**

<span id="page-23-1"></span>After a new Fixing Reference Group was created, Fixing References can be assigned to this group by moving them from "Available Fixing References" to "Selected Fixing References" using the arrow buttons.

The configured Fixing Reference Groups are then available in the "Fixing Reference" drop down menu when defining the rules and rules templates for NDF and NDS

![](_page_24_Picture_0.jpeg)

## <span id="page-24-0"></span>**4 ADS RFS Rules**

The menu item "ADS RFS Rules" encompasses several features which are described in the subsequent subsections in the order as set out in the illustration below.

| <b>RFS Provider Groups</b><br><b>RFS Algorithm Groups</b><br><b>RFS Satellite Rules</b><br><b>RFS Rules Template</b><br><b>RFS Satellite Groups</b><br><b>Group Name</b><br>aI<br>画<br>Default Group<br>Ŵ<br>aI<br>Alternative Market Link | <b>ADS Configuration Groups</b> | <b>ADS RFS Rules</b> | <b>ADS Orders Rules</b> | <b>ADS SEP Rules</b> | <b>Currency Blacklist</b> | ☆ | € | $\sim$ |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------|----------------------|-------------------------|----------------------|---------------------------|---|---|--------|
|                                                                                                                                                                                                                                            |                                 |                      |                         |                      |                           |   |   |        |
|                                                                                                                                                                                                                                            |                                 |                      |                         |                      |                           |   |   |        |
|                                                                                                                                                                                                                                            |                                 |                      |                         |                      |                           |   |   |        |
|                                                                                                                                                                                                                                            |                                 |                      |                         |                      |                           |   |   |        |

<span id="page-24-2"></span>**Figure 19 Menu item: ADS RFS Rules**

### <span id="page-24-1"></span>**4.1 RFS Provider Groups**

The ADS facilitates the option to forward incoming RFS requests to a group of Liquidity Providers (referred to as Market Link routing). These groups can be defined under the menu item RFS Provider Groups before including them in the respective market link destination rules.

![](_page_25_Picture_0.jpeg)

![](_page_25_Picture_2.jpeg)

**Figure 20 Definition of RFS Provider Groups**

<span id="page-25-1"></span>The default group encompasses all Providers to which an RFS counterpart relationship exists.

RFS Provider Groups can be created, removed and renamed similarly to Currency Groups, as explained in Section [3.1.](#page-9-0)

### <span id="page-25-0"></span>**4.2 RFS Algorithm Groups**

RFS Algorithm Groups are used for the route option "Market Link" by which the conditions are defined how a price provided by a market link provider will be forwarded to the requestor. Two parameters are considered: the time elapsed since the request was received and forwarded to the back-to-back market link providers, and the number of quotes returned by the market link providers.

| <b>RFS Provider Groups</b> | <b>RFS Algorithm Groups</b> | <b>RFS Rules Template</b> | <b>RFS Satellite Groups</b>     | <b>RFS Satellite Rules</b> |                           |                 |   |
|----------------------------|-----------------------------|---------------------------|---------------------------------|----------------------------|---------------------------|-----------------|---|
| <b>Group Name</b>          |                             |                           |                                 |                            |                           |                 |   |
| Default Group              |                             |                           |                                 |                            |                           | aT 面            |   |
| <b>Exotics</b>             |                             |                           |                                 |                            |                           | $aI$ 0          |   |
|                            |                             |                           |                                 |                            | Create Group              |                 |   |
|                            |                             |                           |                                 |                            |                           |                 |   |
|                            |                             |                           | Define Algorithms for "Exotics" |                            |                           | $\qquad \qquad$ | O |
| <b>Request Runtime</b>     |                             |                           | <b>Number Of Quotes</b>         |                            |                           |                 |   |
| 5                          |                             | 3                         |                                 |                            | $\mathbb{Z}^{\mathbb{Z}}$ | 画               | X |

<span id="page-25-2"></span>**Figure 21 Market Link Algorithm definition**

![](_page_26_Picture_0.jpeg)

In the example above, if the Market Link Algorithm "Exotics" is used in a rule or a rule template, a request will be sent to the market link providers and waits for quotes from at least 3 different providers within the first 5 seconds before starting to stream the best quote to the requestor. If after 7 seconds, there have not been 3 competitive quotes, 2 competitive quotes will be sufficient to stream the best quote to the requester.

![](_page_27_Picture_0.jpeg)

### <span id="page-27-0"></span>**4.3 RFS Rules Templates**

Once the groups for the different parameters are setup, as described in the previous sections, they can now be used to define the routing rules describing the conditions, price source destination and margins.

To further simplify rule creation, an additional concept of "Rule Templates" (groups of various rules) has been introduced. We advise best practices to use rule templates to simplify the rule structure.

Assuming that the rules for a particular product are identical for different customer bases, it is helpful to define these rules once as a Rule Template and re-use them across the different customer bases. This allows the user to centrally manage any changes in a re-used Rule Template.

Rule templates and rules must be established independently for RFS, Supersonic and Orders.

Rule templates can be defined for the following products:

- FX Spot
- FX Forward
- FX Time Option
- Fx Future
- FX Swap
- Block Trade
- NDF
- NDS
- FX Option / FX Option Strip
- Loan / Deposit
- Tri Party Repo
- Cross Currency Portfolio (EMS)
- Commodity products
- Metals products

For every product, one or more rule templates can be created. Each rule template can have a collection of rules.

#### **4.3.1 Creating RFS Rule Template for a Product**

When a product is selected, options are available to create a new rule template or to rename, copy or delete and existing rule template.

![](_page_28_Picture_0.jpeg)

| <b>ADS Configuration Groups</b> | <b>ADS RFS Rules</b>        | <b>ADS Orders Rules</b>   | <b>ADS SEP Rules</b>        | ⇔<br><b>Currency Blacklist</b> |                   |                      |
|---------------------------------|-----------------------------|---------------------------|-----------------------------|--------------------------------|-------------------|----------------------|
| <b>RFS Provider Groups</b>      | <b>RFS Algorithm Groups</b> | <b>RFS Rules Template</b> | <b>RFS Satellite Groups</b> | <b>RFS Satellite Rules</b>     |                   |                      |
|                                 |                             |                           | RFS Rules Template Type     |                                | <b>Fx Forward</b> | $\checkmark$         |
|                                 |                             |                           | Group Name                  |                                |                   |                      |
|                                 |                             |                           | Default Group               |                                |                   | 可要自                  |
|                                 |                             |                           | MTF Forward                 |                                |                   | aŢ<br>$\circledcirc$ |
|                                 |                             |                           | Majors / Germany            |                                |                   | al图面                 |
|                                 |                             |                           |                             |                                |                   | <b>Create Group</b>  |
|                                 |                             |                           |                             |                                |                   |                      |
|                                 |                             |                           |                             |                                |                   |                      |
|                                 |                             |                           |                             |                                |                   |                      |
|                                 |                             |                           |                             |                                |                   |                      |
|                                 |                             |                           |                             |                                |                   |                      |

<span id="page-28-0"></span>**Figure 22 RFS Rules Template**

"Copy" allows to copy an existing rule template with all single rules and parameters defined within this specific template.

"Delete" removes the Rule Template. Rule Templates cannot be deleted in case they are used within rules.

#### <span id="page-28-2"></span>**4.3.2 Defining Rules for RFS Rule Template**

Within a Rule Template, the user can begin defining single rules.

|                     | <b>RFS Provider Groups</b>       | <b>RFS Algorithm Groups</b> | <b>RFS Rules Template</b> | <b>RFS Satellite Groups</b>         | <b>RFS Satellite Rules</b>                       |                |                            |                            |                             |                  |
|---------------------|----------------------------------|-----------------------------|---------------------------|-------------------------------------|--------------------------------------------------|----------------|----------------------------|----------------------------|-----------------------------|------------------|
|                     |                                  |                             |                           | RFS Rules Template Type             | <b>Fx Forward</b>                                |                | $\checkmark$               |                            |                             |                  |
|                     |                                  |                             |                           | <b>Group Name</b>                   |                                                  |                |                            |                            |                             |                  |
|                     |                                  |                             |                           | Default Group                       |                                                  |                | al 窗 面                     |                            |                             |                  |
|                     |                                  |                             |                           | <b>MTF Forward</b>                  |                                                  |                | aI ® I                     |                            |                             |                  |
|                     |                                  |                             |                           | Majors / Germany                    |                                                  |                | a]   [a]   [i]             |                            |                             |                  |
|                     | Provide text for filtering rules |                             |                           |                                     | Define rules for "Fx Forward - Majors / Germany" |                |                            |                            |                             | $ \Box$ $\times$ |
|                     | POS Activation Period            | <b>Trading Venue</b>        | <b>Currency Couple</b>    | <b>Notional Amount</b>              | <b>FX Time Period</b>                            | In Competition | Route                      | <b>Pricing Server</b>      | <b>Manual Routing Group</b> | Marke            |
|                     | 1 Frankfurt desk time            | <b>OTC</b>                  | Majors                    | Anv                                 | <b>Short Maturities</b>                          | Any            | Manual Intervention        | Default PS                 | Default Group               | Defau シ (函 前     |
|                     |                                  |                             |                           |                                     | Any                                              | Any            | No Pricing                 | Default PS                 | Default Group               | Defau / 9 章      |
|                     | 2 After 5pm                      | <b>OTC</b>                  | Majors                    | Any                                 |                                                  |                |                            |                            |                             |                  |
|                     |                                  | <b>OTC</b>                  | <b>Exotics</b>            | Any                                 | SPOTNEXT - 1 YEAR                                | Any            | Market Link                | Default PS                 | Swap Desk                   | Triple ジ 園 自     |
| $\frac{1}{2}$ 3 Any | 4 Any<br>$\checkmark$            | Any<br>$\checkmark$         | Any                       | Any<br>$\checkmark$<br>$\checkmark$ | Any<br>$\checkmark$                              | Any<br>$\vee$  | No Pricing<br>$\checkmark$ | Default PS<br>$\checkmark$ | Default Group $\sim$        | Defa v 面         |

<span id="page-28-1"></span>**Figure 23 Creating rules within a rule template**

**Add Rule** – Creates a new Rule Template for the selected product. Alternatively, a single rule can be replicated using the copy feature and modified subsequently.

Once a rule is created, the parameters can be filled out. For each parameter, a drop-down is provided where a list of existent Configuration Groups is

![](_page_29_Picture_0.jpeg)

displayed. When hovering with the mouse over the respective selection in the dropdown lists, the details of the conditions are summarized. The default group of each parameter is pre-selected in each column.

A rule within the rule template applies if an RFQ request meets all the criteria left to the column

"Route". These criteria are mostly based on the pre-definable Configuration Groups.

The parameter "**Trading Venue**" offers the possibility to differentiate the routing of a price request depending on the venue where it was originated.

This parameter is available for derivative products supported by the 360T MTF venue and is only displayed if the provider entity is MTF-enabled. It allows us to create distinct rules for requests originating from the OTC platform or the MTF venue. You can restrict a rule to respectively OTC or MTF, but you can also use the value "ANY", should no different treatment be wished based on the trading venue.

|                                           | <b>ADS Configuration Groups</b>  |              | <b>ADS RFS Rules</b>                 | <b>ADS Orders Rules</b>   | <b>ADS SEP Rules</b>            | <b>Currency Blacklist</b> | ⇔                                                |                            |                                           |                                          |                                            | $\Omega \circ \mathbb{R}$        |
|-------------------------------------------|----------------------------------|--------------|--------------------------------------|---------------------------|---------------------------------|---------------------------|--------------------------------------------------|----------------------------|-------------------------------------------|------------------------------------------|--------------------------------------------|----------------------------------|
|                                           | <b>RFS Provider Groups</b>       |              | <b>RFS Algorithm Groups</b>          | <b>RFS Rules Template</b> | <b>RFS Satellite Groups</b>     |                           | <b>RFS Satellite Rules</b>                       |                            |                                           |                                          |                                            |                                  |
|                                           |                                  |              |                                      |                           | RFS Rules Template Type         |                           | <b>Fx Forward</b>                                |                            | $\checkmark$                              |                                          |                                            |                                  |
|                                           |                                  |              |                                      |                           | Group Name                      |                           |                                                  |                            |                                           |                                          |                                            |                                  |
|                                           |                                  |              |                                      |                           | Default Group                   |                           |                                                  |                            | al窗面                                      |                                          |                                            |                                  |
|                                           |                                  |              |                                      |                           | MTF Forward<br>Majors / Germany |                           |                                                  |                            | aI 窗 面                                    |                                          |                                            |                                  |
|                                           |                                  |              |                                      |                           |                                 |                           |                                                  |                            | $\mathbf{a}$ $\mathbf{b}$ $\mathbf{b}$    |                                          |                                            | $ \circ$ $\times$                |
|                                           |                                  |              |                                      |                           |                                 |                           | Define rules for "Fx Forward - Majors / Germany" |                            |                                           |                                          |                                            |                                  |
|                                           |                                  |              |                                      |                           |                                 |                           |                                                  |                            |                                           |                                          |                                            |                                  |
|                                           |                                  |              |                                      |                           |                                 |                           |                                                  |                            |                                           |                                          |                                            |                                  |
|                                           | Provide text for filtering rules |              |                                      |                           |                                 |                           |                                                  |                            |                                           |                                          |                                            |                                  |
| <b>POS</b>                                | <b>Activation Period</b>         |              | <b>Trading Venue</b>                 | <b>Currency Couple</b>    | Notional Amount                 |                           | <b>FX Time Period</b>                            | In Competition             | Route                                     | <b>Pricing Server</b>                    | <b>Manual Routing Group</b>                | Marl                             |
| $\mathbf{1}$                              | Frankfurt desk time              |              | <b>OTC</b>                           | Majors                    | Any                             |                           | <b>Short Maturities</b>                          | Any                        | Manual Intervention                       | Default PS                               | Default Group                              | Defa / @<br>前                    |
| $\overline{2}$<br>$\overline{\mathbf{3}}$ | After 5pm<br>Any                 | $\checkmark$ | <b>OTC</b><br><b>OTC</b><br>$\wedge$ | Majors<br>Exotics         | Any<br>Any<br>$\checkmark$      | $\checkmark$              | Any<br>SPOTNEXT - 1 YV                           | Any<br>Any<br>$\checkmark$ | No Pricing<br>Market Link<br>$\checkmark$ | Default PS<br>Default PS<br>$\checkmark$ | Default Group<br>Swap Desk<br>$\checkmark$ | Defa ジ (@        <br>m<br>Trij V |

<span id="page-29-0"></span>**Figure 24 Selecting a Trading Venue**

"**In competition**" is a criterion that can be used when the requester notifies the exclusive Liquidity Provider as part of the RFS message that the request was not submitted to any other competitor. The "In competition" column is not available by default. Please ask the 360T Client Advisory Services to activate this feature, if required. The "In competition" parameter offers three different condition values (No=LP is not in competition; Yes=LP competes with other LPs, Any=condition is not applicable).

While creating a Rule Template, options are offered for each parameter to select among the Configuration Groups that have been previously defined. In case a one-time exceptional value is required, which is not part of the group, the

![](_page_30_Picture_0.jpeg)

"Custom" option within the dropdown can be selected. This opens a window, where specific values for this parameter can be set.

For example, as shown in the illustration below, the selection of the "Custom" value for the column "Time Period" opens a pop-up window where a custom tenor range can be defined.

| <b>RFS Algorithm Groups</b>                    | <b>RFS Rules Template</b>                                           | <b>RFS Satellite Groups</b>        | <b>RFS Satellite Rules</b> |                         |
|------------------------------------------------|---------------------------------------------------------------------|------------------------------------|----------------------------|-------------------------|
|                                                |                                                                     | RFS Rules Template Type            | <b>Fx Forward</b>          |                         |
|                                                | From                                                                | Select Custom FX Time Period<br>To |                            | $\times$                |
|                                                | <b>TODAY</b><br><b>TODAY</b><br><b>TOMORROW</b><br>SPOT<br>SPOTNEXT | UNLIMITED<br>$\wedge$              | $\checkmark$               | <b>Majors / Germany</b> |
| ering rules<br>boins<br><b>Currency Couple</b> | 1 WEEK<br>2 WEEKS<br>3 WEEKS<br>1 MONTH                             | <b>Time Period</b>                 | Select<br>Cancel<br>Route  | <b>Pricing Server</b>   |
| Any<br>$\vee$                                  | 2 MONTHS<br>3 MONTHS                                                | <b>ustom</b>                       | No Pricing<br>$\vee$       | Default PS<br>$\vee$    |
|                                                |                                                                     |                                    |                            |                         |

<span id="page-30-0"></span>**Figure 25 Entering custom values for a parameter within a template group**

Additional parameter called "Overhang" is available for uneven FX Swaps. Overhang is the difference between the notional of the far and the near leg which can be defined is the Notional Amount Groups or a Custom value can be selected.

| <b>Configuration Groups</b> |              | <b>ADS RFS Rules</b>        | <b>ADS Orders Rules</b>   | <b>ADS SEP Rules</b>       |                               | <b>Currency Blacklist</b> | ☆                          |                    |                       |              |    |
|-----------------------------|--------------|-----------------------------|---------------------------|----------------------------|-------------------------------|---------------------------|----------------------------|--------------------|-----------------------|--------------|----|
| ovider Groups               |              | <b>RFS Algorithm Groups</b> | <b>RFS Rules Template</b> |                            | <b>RFS Satellite Groups</b>   |                           | <b>RFS Satellite Rules</b> |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           |                            |                    |                       |              |    |
|                             |              |                             |                           |                            | <b>Select Satellite Group</b> |                           |                            | Default Group      |                       |              |    |
|                             |              |                             |                           | <b>Select Product</b>      |                               |                           |                            | Fx Swap            |                       |              |    |
|                             |              |                             |                           |                            |                               |                           |                            |                    |                       |              |    |
| le text for filtering rules |              |                             |                           |                            |                               |                           |                            |                    |                       |              |    |
| <b>Currency Couple</b>      |              | <b>Notional Amount</b>      |                           | <b>Near FX Time Period</b> | <b>Far FX Time Period</b>     |                           | Overhang                   |                    | <b>In Competition</b> |              | Ro |
| Any                         |              |                             |                           |                            |                               |                           |                            |                    |                       |              |    |
|                             | $\checkmark$ | Any                         | Any<br>$\checkmark$       | $\checkmark$               | Any                           | $\checkmark$              | Any                        | $\curvearrowright$ | Any                   | $\checkmark$ | P  |
|                             |              |                             |                           |                            |                               |                           | Any                        |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           | Default Group              |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           | Small amounts              |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           | 1 mill - 200 mill          |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           | 10 mill - 15 mill          |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           | 500k - 1 mill.             |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           | $1$ mill - $2$ mill        |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           | Custom                     |                    |                       |              |    |
|                             |              |                             |                           |                            |                               |                           |                            |                    |                       |              |    |

<span id="page-30-1"></span>**Figure 26 FX Swaps: Overhang** 

![](_page_31_Picture_0.jpeg)

Once the rule conditions are defined, the price source must be specified through the selection of the **Route** option.

|                              |                                                            |                        | Define rules for "Fx Forward - Majors / Germany" |                     |                                                                     |                            |                         |
|------------------------------|------------------------------------------------------------|------------------------|--------------------------------------------------|---------------------|---------------------------------------------------------------------|----------------------------|-------------------------|
| POS                          | Provide text for filtering rules<br><b>Currency Couple</b> | <b>Notional Amount</b> | <b>FX Time Period</b>                            | In Competition      | Route                                                               | <b>Pricing Server</b>      | <b>Manual Routing (</b> |
| $\vee$                       | $\checkmark$<br>Majors                                     | Any<br>$\checkmark$    | Short Maturities                                 | $\checkmark$<br>Any | Manual Interv. A                                                    | Default PS<br>$\checkmark$ | Default Group           |
| 2                            | Majors                                                     | Any                    | Any                                              | Any                 | <b>Manual Intervention</b>                                          | ault PS                    | Default Group           |
| $\overline{\mathbf{3}}$<br>÷ | Exotics                                                    | Any                    | SPOT NEXT - 1 year                               | Any                 | <b>Pricing Server</b><br>Market Link<br>Forward Strip<br>No Pricing | ault PS                    | Default Group<br>Ad     |

<span id="page-31-0"></span>**Figure 27 Route column**

![](_page_32_Picture_0.jpeg)

The following Routes are available:

Manual Intervention: The incoming request is routed to the Trader Worksheet of the manual trader users (as defined in the 'Manual Routing Group' tab) for pricing.

Note: If an RFS request does not match the conditions of any destination rule then the request is routed to traders who are members of Default Group (Manual Routing Groups).

Pricing Server: The price basis is either the 360T price feed from the 360T Market Maker Cockpit, Infotec (market data), or a provider individual price feed through an adapter interface to the provider's pricing system. The Auto Dealer automatically sends a price back to the requestor.

Note: In case more than one Pricing Server exists, the destination of the request can be defined by choosing between the Pricing Server or the Alternative Pricing Server from the column "Pricing Server". The behavior is the same as for the regular Pricing Server: The Auto Dealer automatically sends a price back to the requestor.

No Pricing: The Auto Dealer does not forward the negotiation request to any price source. Consequently, the request is **not** sent to any manual traders. Therefore, quotes are not provided to the customer at all.

![](_page_33_Picture_0.jpeg)

Market Link: The request is forwarded to a set of market link providers and a selected trading venue (OTC platform or MTF venue). Therefore, this rule is used in combination with the selections under Market Link Provider, Market Link Trading Venue and Market Link Algorithm (see more details below). The Auto Dealer continuously updates the pricing with the best quote available from the group of external providers. The execution of the requesting entity automatically executes a back-to-back deal with the best price available at the time of execution. Upon completion, two trades are generated: one between the requesting entity and the provider; and a second one between the provider and the external market maker (market link provider).

Note: Only limited to spot and forward requests and to some defined currency pairs, requests are forwarded to the selected market link providers as two-way requests by default, irrespective of whether incoming requests are one sided (bid or ask) or two-way.

Please contact 360T Client Advisory Services if you would like back-to-back requests to be routed to your Autodealer using the originating request`s selection.

Forward Strip: This rule is only available for the product FX Forward and is a combination of Market Link and Pricing Server. The forward request is sent as an FX Spot request to the configured set of market link providers while the forward points are retrieved from an internal price source. The execution of the FX Forward by the requesting entity automatically executes a back-to-back FX Spot deal with the best price available at the time of execution. Upon completion, two trades are generated: an FX forward between the requesting entity and the provider; and a Spot trade between the providing bank and the external market maker (market link provider).

Note1: Forward Strip requires an internal market data source to be configured. Please contact 360T Client Advisory Services if you would like to implement such a data source.

Note2: For some defined currency pairs, requests are forwarded as FX Spot to the selected market link providers as two-way requests by default, irrespective of whether incoming requests are one sided (bid or ask) or two-way.

Please contact 360T Client Advisory Services if you would like back-to-back requests to be routed to your Autodealer using the originating request`s selection.

Block Strip: This rule is an extension of Forward Strips. In a block containing spots and forwards, the net FX Spot portion of the block request is sent to a configured set of market link providers, while the forward points are retrieved from an internal market data/pricing source. The execution of the block request by the requesting entity automatically executes a back-to-back FX Spot deal

![](_page_34_Picture_0.jpeg)

with the best price available at the time of execution. Upon completion, a trade is generated between the requesting entity and the provider bank *for each leg* contained in the block. A trade for the net spot amount is also generated between the provider bank and the external market maker (market link provider)

Time Option Forward: This rule is only available for the product FX Time Option and is a Market Link strategy which generates two standard FX Forward requests back-to-back, picks the rate of the worst leg to automatically price the original request and when the original request is executed, executes back-toback with the rate of the best leg. Upon completion, two trades are generated: an FX Time Option between the requesting entity and the provider bank; and a Forward trade between the provider bank and the external market maker (market link provider). The advantage is to enable completely automated pricing of Time Option Forwards without the need for the interbank market to support Time Option Forwards.

**Manual Routing Group** is using the Manual Routing Groups as defined under the Configuration Groups. In case a Route "Manual Intervention" is defined, or in case a request that is routed to a pricing engine is republished for dealer intervention for any reason (e.g disconnection of the external engine or reject from the external engine), then this parameter will be taken into account to route the request to the traders configured in the relevant Manual Routing Group. In case the traders of the Manual Routing Group are not logged into the 360T Trader Worksheet, then the request not shown to any manual trader.

**Market Link Provider** is used to define the bank basket to be used for the back-to-back request. You can select any group that was defined under RFS Provider Groups.

**Market Link Trading Venue** is used to define whether the linked back-to-back request in the context of a market link route should be executed on the 360T MTF Venue or the OTC platform.

**Market Link Algorithm** allows selecting the Algorithm group with regards to the duration of the request and number of quotes to be considered.

The user can continue to add rule groups for the selected product group by proceeding with the "Add Rule" option.

**Note: The order of rules in a rule determines their priority top-down.** Select one rule and move it to the desired position via drag&drop to re-organize the order.

"Delete" can be used to remove a rule group that is no longer needed.

If a rule must be adjusted, press the "Edit" button before changing the values of the columns.

**Note:** Defining rules within a rule template does not enable routing. Rule templates are templates that can be included in the RFS Satellite Rules (refer to Section [4.5.1\)](#page-39-1) before routing takes place.

![](_page_35_Picture_0.jpeg)

### <span id="page-35-0"></span>**4.4 RFS Satellite Groups**

The various customer bases (referred to as Satellite Groups) alongside the corresponding products are defined within the Satellite Groups sections. Satellite Groups for the RFS are defined in the tab "RFS Satellite Groups".

After setup and acceptance of the counterparty relationship, an entity is first assigned to the "Default" Satellite Group.

A new group can be added by clicking "Create Group". You will be prompted with a dialogue box to enter the name of the new satellite group.

| <b>RFS Algorithm Groups</b> | RFS Rules Template<br>RFS Satellite Groups<br><b>RFS Satellite Rules</b> |              |
|-----------------------------|--------------------------------------------------------------------------|--------------|
|                             | <b>Group Name</b>                                                        |              |
|                             | Default Group                                                            | al 面         |
|                             | $\times$                                                                 | Create Group |
|                             | Please, provide a name for a new group                                   |              |
|                             |                                                                          |              |
|                             | Customers DECHATLI                                                       |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             | <b>Create Group</b><br>Cancel                                            |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             |                                                                          |              |
|                             |                                                                          |              |

<span id="page-35-1"></span>**Figure 28 Creating new satellite group**

A group can be renamed, and the rules can be deleted except for the "Default Group" since it is the default system group created together with the Auto Dealer.

To select a new group, click on the group name on the main screen. Select the entities from the left column "Available Requesters" and assign them to the "Selected Requesters" group with the help of the arrow buttons.

![](_page_36_Picture_0.jpeg)

| <b>RFS Algorithm Groups</b><br><b>RFS Satellite Groups</b><br><b>RFS Provider Groups</b><br><b>RFS Rules Template</b><br><b>RFS Satellite Rules</b><br>と<br>Group Name<br>a] 前<br>Default Group<br>a] 前<br><b>Customers DECHATLI</b><br>aI m<br><b>Gold Customers</b><br>a I û<br><b>Premium Customers</b><br>aI 前<br><b>Standard Customers</b><br>al 0<br>xauusdtest<br>$-$ 0 $\times$<br>Configure "Premium Customers" Satellite Group<br>Requesters<br><b>Products</b><br><b>Assignments</b><br><b>Available Requesters</b><br><b>Selected Requesters</b><br>血 360T.QA [Default Group]<br>血 360T.RMS [Default Group]<br>血 360T.SEF.TAKER [Default Group]<br>$\langle \rangle$<br>血 360T.SEP.BENCHMARK.TEST [Default Gro<br>血 360TEXEC.TEST [Default Group]<br>$\leq$<br>$\sqrt{\hat{\mathbf{m}}}$ 360TFFM.TEST<br>$\sqrt{\underline{\widehat{\mathrm{m}}}}$ 360TGROUP<br>$\rightarrow$<br>血 360TGROUP.TAS.A1 [Default Group]<br>血 360TGROUP2 [Customers DECHATLI]<br>$\propto$<br>$\sqrt{m}$ 360TGROUP3<br>血 360TGROUP3 [Default Group]<br>血 360TGROUP3.TAS.A1 [Default Group]<br>金 360TGUIDE.TEST [Default Group] | <b>ADS Configuration Groups</b><br><b>ADS RFS Rules</b><br><b>ADS Orders Rules</b> | ٥<br><b>ADS SEP Rules</b><br><b>Currency Blacklist</b> | $\equiv$<br>$\Omega$ |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------|--------------------------------------------------------|----------------------|
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                    |                                                        |                      |

<span id="page-36-0"></span>**Figure 29 Adding requester members to RFS Satellite Groups**

Once done, click on the "Products" section and select the products which should be supported for the defined customers.

![](_page_36_Figure_4.jpeg)

<span id="page-36-1"></span>**Figure 30 Specifying Products of an RFS Satellite Group**

![](_page_37_Picture_0.jpeg)

The tab "Assignments" allows to automatically add all new daughter requester companies of a selected client to the same Satellite group as configured for the parent entity. This auto-inclusion of daughter entities will only happen after the relationship with the new requester legal entity has been approved in the Customer Relationship tool. The Assignment feature is only available upon request and is activated by 360T CAS.

| <b>RFS Algorithm Groups</b><br><b>RFS Provider Groups</b><br><b>RFS Rules Template</b> | <b>RFS Satellite Rules</b><br><b>RFS Satellite Groups</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                 |  |
|----------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------|--|
| と                                                                                      | <b>Group Name</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |                                                                                 |  |
|                                                                                        | Default Group                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | aI 面                                                                            |  |
|                                                                                        | <b>Customers DECHATLI</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | al 面                                                                            |  |
|                                                                                        | <b>Gold Customers</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | aI 面                                                                            |  |
|                                                                                        | <b>Premium Customers</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | $a$ <sub>I</sub> $\frac{a}{b}$                                                  |  |
|                                                                                        | <b>Standard Customers</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | al 血                                                                            |  |
|                                                                                        | xauusdtest                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | aI面                                                                             |  |
|                                                                                        | <b>Requesters</b><br><b>Products</b><br><b>Available Assignments</b><br>1000 - Boston Sci Corp.TEST [Default Group]<br>1000 - Boston Sci Corp2.TEST [Default Grou<br>file: 360T-PECHECK.TEST [Default Group]<br>$\,>\,$<br>血 360T.ALIAS [Default Group]<br>血 360T.AMERICAS.TEST [Default Group]<br>$\langle$<br>血 360T.EMS [Default Group]<br>血 360T.INT-LIMITAPI.TEST [Default Group]<br>$\geq$<br>血 360T.INT-ORDER.TEST [Default Group]<br>1 360T.INT-RFQ.TEST [Default Group]<br>$\ll$<br>file: 360T.INT_LIMAPI.TEST [Default Group]<br>血 360T.ONBEHALF [Default Group]<br>血 360T.QA [Default Group]<br>血 360T.RMS [Default Group] | <b>Assignments</b><br><b>Selected Assignments</b><br><b>血 360T.EMSCorporate</b> |  |
| <b>Create Change Request</b><br><b>360TRANK TEST * X</b>                               |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                                 |  |

<span id="page-37-0"></span>**Figure 31 Satellite Group: Assignments**

All Satellite Groups can be downloaded as a CSV file using the download icon.

![](_page_38_Picture_0.jpeg)

### <span id="page-38-0"></span>**4.5 RFS Satellite Rules**

The core ruleset is defined and maintained in the section RFS Satellite Rules on a Satellite Group and product level, as shown in the figure below.

|                 | <b>ADS Configuration Groups</b>  | <b>ADS RFS Rules</b>        | <b>ADS Orders Rules</b><br><b>ADS SEP Rules</b> | <b>Currency Blacklist</b>   | ٥                          |                           |                         |                |                       |                       |    |    | $\Omega \quad \Omega =$                   |  |
|-----------------|----------------------------------|-----------------------------|-------------------------------------------------|-----------------------------|----------------------------|---------------------------|-------------------------|----------------|-----------------------|-----------------------|----|----|-------------------------------------------|--|
|                 | <b>RFS Provider Groups</b>       | <b>RFS Algorithm Groups</b> | <b>RFS Rules Template</b>                       | <b>RFS Satellite Groups</b> | <b>RFS Satellite Rules</b> |                           |                         |                |                       |                       |    |    |                                           |  |
| と               |                                  |                             |                                                 |                             |                            |                           |                         |                |                       |                       |    |    |                                           |  |
|                 |                                  |                             |                                                 | Select Satellite Group      |                            | <b>Standard Customers</b> |                         | $\checkmark$   |                       |                       |    |    |                                           |  |
|                 |                                  |                             |                                                 | <b>Select Product</b>       |                            | <b>Ex Forward</b>         |                         | $\checkmark$   |                       |                       |    |    |                                           |  |
|                 |                                  |                             |                                                 |                             |                            |                           |                         |                |                       |                       |    |    |                                           |  |
|                 | Provide text for filtering rules |                             |                                                 |                             |                            |                           |                         |                |                       |                       |    |    |                                           |  |
| POS             | <b>Rules Template</b>            | <b>Activation Period</b>    | <b>Trading Venue</b>                            | <b>Currency Couple</b>      | <b>Notional Amount</b>     |                           | <b>FX Time Period</b>   | In Competition | Route                 | <b>Pricing Server</b> | Ma |    |                                           |  |
| $\frac{1}{2}$ 1 |                                  | After noon                  | Any                                             | Majors 12am                 | Any                        |                           | Default Group           | Any            | <b>Pricing Server</b> | Default PS            |    |    | $SW \not\equiv \circledcirc \circledcirc$ |  |
| $\wedge$ : 2    | Majors / Germany                 |                             |                                                 |                             |                            |                           |                         |                |                       |                       |    | 51 | 90                                        |  |
|                 |                                  | Frankfurt desk time         | <b>OTC</b>                                      | Majors                      | Any                        |                           | <b>Short Maturities</b> | Any            | Manual Intervention   | Default PS            | De |    |                                           |  |
|                 |                                  | After 5pm                   | <b>OTC</b>                                      | Majors                      | Any                        |                           | Any                     | Any            | No Pricing            | Default PS            | De |    |                                           |  |
|                 |                                  | Any                         | <b>OTC</b>                                      | <b>Exotics</b>              | Any                        |                           | SPOTNEXT - 1 YEAR       | Any            | Market Link           | Default PS            | Sw |    |                                           |  |
| $\vee$ : 3      | MTF Forward                      |                             |                                                 |                             |                            |                           |                         |                |                       |                       |    | Ŧ/ | 50 0                                      |  |
|                 |                                  |                             |                                                 |                             |                            |                           |                         |                |                       | <b>Add Rule</b>       |    |    | <b>Add Template</b>                       |  |
|                 |                                  |                             |                                                 |                             |                            |                           |                         |                |                       |                       |    |    |                                           |  |

<span id="page-38-1"></span>**Figure 32 RFS Satellite Rules**

The RFS Satellite Rules allow both the option to include existent rule templates and to create single routing rules.

**Add Template** – Provides the ability to use one of the rule templates created earlier

When more than one rule template was set up previously, as is the case here, the user is presented with a drop-down to use an appropriate rule group when clicking the "Add Template" button. The user can add more than one rule template and re-order them as appropriate. This allows flexibility to structure rules for different purposes, according to the needs of an organization.

**Add Rule** – Offers the ability to add a rule that is not present in any of the rule groups

**Copy** - Allows copying a rule or rule Template

**Delete** – Allows deleting a rule or a rule group

The order of rules and rule groups determines their priority top-down. Hence, **"Move Up"** and **"Move Down"** offers the ability to re-order rules/rule groups. It is to be noted that rules within a rule template itself cannot be deleted or reordered here.

The different conditions and routing mechanisms pertaining to a routing rule are already described in Section [4.3.2.](#page-28-2) The focus of the subsequent Sub-Sections is on the ways rules templates can be included and used within the RFS Satellite Rules.

![](_page_39_Picture_0.jpeg)

All Satellite Rules can be downloaded as a CSV file using the download icon.

### <span id="page-39-1"></span>**4.5.1 Using RFS Rule Template to define RFS Satellite Rules**

A rule template is defined on a product basis. Once a rule template for a specific product has been created, it can be re-used within routing rules for the same product across various Satellite Groups. This has the advantage that the same rules do not have to be set up repetitively for each Satellite Group. This is visualized in the illustration below.

|                   | Select Satellite Group<br>Select Product                  |                          | Premium<br><b>Fx Forward</b> |                   | Select Satellite Group<br>Select Product  |                          | Standard<br>Fx Forward |
|-------------------|-----------------------------------------------------------|--------------------------|------------------------------|-------------------|-------------------------------------------|--------------------------|------------------------|
|                   |                                                           |                          |                              |                   |                                           |                          |                        |
|                   | Provide text for filtering rules<br><b>Rules Template</b> | <b>Activation Period</b> | <b>Trading Venue</b>         |                   | Provide text for filtering rules          |                          |                        |
| POS<br>$\vee$ : 1 | Majors / Germany                                          |                          |                              | POS<br>$\vee$ : 1 | <b>Rules Template</b><br>Majors / Germany | <b>Activation Period</b> | <b>Trading Venue</b>   |

#### <span id="page-39-0"></span>**Figure 33 Using RFS Rules Templates across Satellite Groups**

In this scenario, the same rule set for the product "Fx Forward" applies to the entities belonging to the Satellite Groups "Premium" **or** "Standard".

![](_page_40_Picture_0.jpeg)

### **4.5.2 Using single rules alongside Rule Templates**

If Rule Templates do not cover all routing scenarios required for a given product, then single rules can be specified in addition.

|                  |                | <b>RFS Provider Groups</b>       | <b>RFS Algorithm Groups</b> | RFS Rules Template       |            | <b>RFS Satellite Groups</b> | <b>RFS Satellite Rules</b> |              |                |   |
|------------------|----------------|----------------------------------|-----------------------------|--------------------------|------------|-----------------------------|----------------------------|--------------|----------------|---|
|                  |                |                                  |                             |                          |            |                             |                            |              |                |   |
|                  |                |                                  | Select Satellite Group      |                          | Standard   |                             |                            | $\checkmark$ |                |   |
|                  |                |                                  | Select Product              |                          | Fx Forward |                             |                            | $\checkmark$ |                |   |
|                  |                |                                  |                             |                          |            |                             |                            |              |                |   |
|                  |                | Provide text for filtering rules |                             |                          |            |                             |                            |              |                |   |
|                  |                |                                  |                             |                          |            |                             |                            |              |                |   |
| POS              |                | <b>Rules Template</b>            |                             | <b>Activation Period</b> |            | <b>Trading Venue</b>        | <b>Currency Couple</b>     | Notional Ar  |                |   |
| $\ddot{\cdot}$ . | $\mathbf{1}$   |                                  |                             | After noon               | Any        |                             | Majors                     | Any          | $\mathcal{I}$  | Ū |
| $\wedge$ :       | $\overline{2}$ | Majors / Germany                 |                             |                          |            |                             |                            |              | $\frac{1}{2}$  | Û |
|                  |                |                                  |                             | Frankfurt desk time      | <b>OTC</b> |                             | Majors                     | Any          |                |   |
|                  |                |                                  |                             | After 5pm                | <b>OTC</b> |                             | Majors                     | Any          |                |   |
|                  |                |                                  |                             | Any                      | <b>OTC</b> |                             | Exotics                    | Any          |                |   |
| $\wedge$ :       | 3              | MTF Forward                      |                             |                          |            |                             |                            |              | $\overline{z}$ | Ū |

<span id="page-40-0"></span>**Figure 34 Using rules in conjunction with rule templates**

When a negotiation request meets the condition of several single rules or rules within the templates, then the order dictates the priority. If a single rule should take precedence over the rule templates, then it must be placed above them as shown in the above illustration. Consequently, the single rule overwrites the rules within the rule templates which consist of the same conditions.

If no rule is specified, the request will be sent to manual intervention to Bridge TWS to the Default Group of traders.

#### **4.5.3 Manipulate margins on rule groups**

Similar to the need to use individual rules to create exceptions/overrides to rule templates, there is also a possibility to manipulate margins within rule groups. This ability is called "Margin Transformations".

![](_page_41_Picture_0.jpeg)

|                    | <b>RFS Satellite Rules</b> | <b>RFS Satellite Groups</b> | RFS Rules Template     | <b>RFS Algorithm Groups</b>      | <b>RFS Provider Groups</b> |             |                           |
|--------------------|----------------------------|-----------------------------|------------------------|----------------------------------|----------------------------|-------------|---------------------------|
|                    |                            |                             |                        |                                  |                            |             |                           |
|                    | $\checkmark$               |                             | Standard               | Select Satellite Group           |                            |             |                           |
|                    | $\checkmark$               |                             | Fx Forward             | Select Product                   |                            |             |                           |
|                    |                            |                             |                        |                                  |                            |             |                           |
|                    |                            |                             |                        | Provide text for filtering rules |                            |             |                           |
|                    |                            |                             |                        |                                  |                            |             |                           |
|                    | Margin                     | Market Link Algorithm       | Market Link Trading Ve | <b>Market Link Provider</b>      | <b>IUD</b>                 |             | POS                       |
| $\Rightarrow$<br>画 | 2%                         | Default Group               | <b>OTC</b>             | Default Group                    |                            | $1\,$       | $\ddot{\cdot}$            |
| 面<br>$\mathcal{I}$ | <b>5% Transformation</b>   |                             |                        |                                  |                            | $\mathbf 2$ |                           |
|                    | Default Group              | Default Group               | <b>OTC</b>             | Default Group                    |                            |             |                           |
|                    | Default Group              | Default Group               | OTC                    | Default Group                    |                            |             | $\widehat{\phantom{a}}$ : |
|                    | 2%                         | Default Group               | <b>OTC</b>             | Default Group                    |                            |             |                           |

#### <span id="page-41-0"></span>**Figure 35 Applying margin transformations**

The margin transformation is expressed as a percentage. Negative values are allowed, which implies a reduction of the original margin.

When a rule is applied, the margins specified within the rule are first calculated. The margin transformation is then determined and added onto or subtracted from the original margin.

For example, if the Bid Spot margin specified within a rule is 10 pips and the margin transformation is 5%, this would translate into a total margin of 10.5 pips (10 + 10 x 5%) being applied to the request.

If the Bid Spot margin was specified in percent, say 1%, and the margin transformation is -5%, this would imply a total applied margin of 0.95% (1% - 5% x 1%) on top of the supplied bid price.

If the Bid Spot margin within a rule was a fixed amount, say EUR 10 per transaction and the margin transformation is 5%, then the total margin applied on a transaction would be EUR 10.5 (10 + 10 x 5%).

![](_page_42_Picture_0.jpeg)

## <span id="page-42-0"></span>**5 Currency Blacklist**

The Currency Blacklist allows defining all currencies for which no pricing should be provided. Any request involving one of the blacklisted currencies received by the ADS will be neglected. This applies to the RFQ, Orders and SEP ADS. (A blacklisted currency will be neglected in all modules).

Note: Configured routing rules that have a blacklisted currency included will not be applied.

| <b>ADS Configuration Groups</b>       | <b>ADS RFS Rules</b>        | <b>ADS Orders Rules</b> | <b>ADS SEP Rules</b> | <b>Currency Blacklist</b>  | $\equiv$<br>$\curvearrowleft$<br>$\sim$ |
|---------------------------------------|-----------------------------|-------------------------|----------------------|----------------------------|-----------------------------------------|
|                                       | <b>Available Currencies</b> |                         |                      | <b>Selected Currencies</b> |                                         |
| ARS                                   |                             |                         | <b>AFN</b>           |                            |                                         |
| <b>BBD</b>                            |                             |                         | ALL                  |                            |                                         |
| <b>BND</b>                            |                             |                         | AMD                  |                            |                                         |
| <b>BOB</b>                            |                             |                         | ANG                  |                            |                                         |
| <b>BRL</b>                            |                             | $\gg$                   | <b>AOA</b>           |                            |                                         |
| <b>BSD</b>                            |                             |                         | AWG                  |                            |                                         |
| <b>BWP</b>                            |                             | $\ll$                   | AZN                  |                            |                                         |
| <b>BYR</b>                            |                             |                         | <b>BAM</b>           |                            |                                         |
| 1000000000000000000000000000000000000 |                             |                         | <b>BDT</b>           |                            |                                         |

<span id="page-42-2"></span>**Figure 36 Currency Backlist**

To blacklist a currency, it must be moved from the "Available Currencies" to the "Selected Currencies" column using the Arrow button.

## <span id="page-42-1"></span>**6 ADS Rule Evaluator Tool**

The **ADS Rule Evaluator** is a useful tool to search for a specific rule that would have applied to an incoming RFS at a specific time. Note that if searching, e.g. for a Spot request, you do not have to select a specific area within the Satellite Group Rules. As soon as you select the Toggle Rule Evaluator and enter the search parameters, the tool will find the applicable rule regardless of which Group the rule is configured in.

| FX Time Period Groups<br>Notional Amount Groups<br>Margin Groups<br><b>Currency Couple Groups</b> | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | <b>ADS RFS Rules</b> | <b>ADS Orders Rules</b> | <b>ADS SEP Rules</b> | <b>Currency Blacklist</b> | $\vert \circ \vert$ |                              |  |
|---------------------------------------------------------------------------------------------------|------------------------------------------|----------------------|-------------------------|----------------------|---------------------------|---------------------|------------------------------|--|
|                                                                                                   | <b>Currency Groups</b>                   |                      |                         |                      |                           |                     | <b>MM Time Period Groups</b> |  |

<span id="page-42-3"></span>**Figure 37 Activate Toggle Rule Evaluator**

![](_page_43_Picture_0.jpeg)

After clicking on the ADS Rule Evaluator icon ( ) in the symbol bar, selection and data capture fields are displayed, where the details of the request can be entered.

| <b>Negotiation Type</b><br>Product Type | Fx Spot                                  | $\checkmark$<br>$\checkmark$ |
|-----------------------------------------|------------------------------------------|------------------------------|
| Requester                               | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | $\checkmark$                 |
| Use UTC Time Zone                       | Using Time Zone: UTC, +00:00 (UTC+00:00) |                              |
| Time                                    | $\bullet$                                | 13:00 ↔                      |
| Currency Couple                         | $EUR / USD \sim$                         |                              |
| Notional Amount                         | 1,000,000                                |                              |
| In Competition                          | Yes                                      | $\checkmark$                 |
|                                         | Find ADS Rule                            |                              |

<span id="page-43-0"></span>**Figure 38 Enter Request details to toggle to the relevant rule**

When clicking on the button "Find ADS Rule", the system will jump to the rule that would be applied to the submitted request. In addition, the identified rule is highlighted as shown in [Figure 39.](#page-43-1)

|            | $\varphi_1 \varphi_2 \equiv 0$                               |                       |                      |                             | <b>Currency Blacklist</b>  | <b>ADS SEP Rules</b>        | <b>ADS Orders Rules</b>        | <b>ADS RFS Rules</b> | <b>ADS Configuration Groups</b>  |                            |
|------------|--------------------------------------------------------------|-----------------------|----------------------|-----------------------------|----------------------------|-----------------------------|--------------------------------|----------------------|----------------------------------|----------------------------|
|            |                                                              |                       |                      |                             | <b>RFS Satellite Rules</b> | <b>RFS Satellite Groups</b> | <b>RFS Rules Template</b>      | RFS Algorithm Groups | RFS Provider Groups              |                            |
|            |                                                              |                       | $\checkmark$         | Customers DECHATLI          |                            | Select Satellite Group      |                                |                      |                                  |                            |
|            |                                                              |                       | $\checkmark$         | Fx Forward                  |                            |                             | Select Product                 |                      |                                  |                            |
|            |                                                              |                       |                      |                             |                            |                             |                                |                      |                                  |                            |
|            |                                                              |                       |                      |                             |                            |                             |                                |                      | Provide text for filtering rules |                            |
|            | Margin                                                       | Market Link Algorithm | Market Link Provider | <b>Manual Routing Group</b> | Pricing Server             |                             | <b>FX Time Period</b><br>Route | Notional Amount      | uple                             | POS                        |
|            | 15% Transformation                                           |                       |                      |                             |                            |                             |                                |                      |                                  | $\wedge$ : 1               |
| <b>シ</b> 面 | Default Group                                                | Default Group         | Default Group        | Default Group               | Default PS                 | No Pricing                  | Any                            | Any                  |                                  |                            |
|            | Default Group                                                | Default Group         | Default Group        | Swap desk                   | Default PS                 | Pricing Server              | Any                            | 1,000,000 - UNLIMI   |                                  |                            |
| 前          | % Transformation<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                       |                      |                             |                            |                             |                                |                      |                                  | $\overline{2}$<br>$\wedge$ |

<span id="page-43-1"></span>**Figure 39 Identified rule after a rule search**

![](_page_44_Picture_0.jpeg)

## <span id="page-44-0"></span>**7 Auto Dealer Control**

All functionalities described in this section work the same for RFS Auto Dealer, Orders Auto Dealer and SEP Auto Dealer.

The enhanced Auto Dealer Control allows the user to start and stop the Auto Dealer by changing the "Auto Dealer Enabled" switch to enabled (started) or disabled (stopped) as desired and as shown in Figure 38. The activation or deactivation will have immediate effect.

![](_page_44_Picture_4.jpeg)

**Figure 40 Auto Dealer Control enabling and disabling Auto Dealer switch**

<span id="page-44-1"></span>To have the Auto Dealer enabled only during specific times of the day, it is possible to enable or disable the Auto Dealer Schedule by changing the "Auto Dealer Schedule Enabled" switch accordingly and by keying in the start and stop times, which determine when the

Auto Dealer should be active. The times are expressed in UTC and the equivalent local times of the current user are displayed in brackets as shown in [Figure 40.](#page-44-1)

| $\sqrt{ }$ Fnabled<br>RES Auto Dealer Schedule Enabled |
|--------------------------------------------------------|
|--------------------------------------------------------|

<span id="page-44-2"></span>**Figure 41 Auto Dealer Control - Auto Dealer Schedule Enabled switch**

| RFS Auto Dealer Start Time | $\bullet$ 06:00 $\bullet$ |
|----------------------------|---------------------------|
|                            | (07:00 CEST)              |
| RFS Auto Dealer Stop Time  | 22:00                     |
|                            | (23:00 CEST)              |

<span id="page-44-3"></span>**Figure 42 Auto Dealer Control – Auto Dealer Start and Stop Times**

![](_page_45_Picture_0.jpeg)

Important: To start the Auto Dealer as per schedule times, the "Auto Dealer Start Enabled" switch must be enabled too. Otherwise, the Auto Dealer stops at the scheduled time but must be manually started.

<span id="page-45-0"></span>**Figure 43 Auto Dealer Control - Auto Dealer Start Enabled switch**

In case the Schedule is enabled but the Start is disabled, the system displays an alert as shown in [Figure 44.](#page-45-1)

![](_page_45_Picture_5.jpeg)

**Figure 44 Auto Dealer Control – Auto Dealer Start related alert**

<span id="page-45-1"></span>In case a customized schedule is required, it is possible to make use of a new functionality: "Day by Day Definition". By changing the "Day by Day Definition" switch to enabled, a week schedule table is displayed and the daily time periods in which the Auto Dealer should be active can be keyed in as needed. The schedule table is displayed as shown in [Figure 45.](#page-46-0)

| Day by Day Definition | <b>VO</b> Enabled |
|-----------------------|-------------------|
|-----------------------|-------------------|

**Figure 43 Auto Dealer Control - Day by Day Definition switch**

![](_page_46_Picture_0.jpeg)

| Sunday   |                                                                   |                                                                 |
|----------|-------------------------------------------------------------------|-----------------------------------------------------------------|
| Monday   | $07:00$ $\leftrightarrow$ Start UTC<br>(Monday, 08:00 CEST)       | Stop UTC $\bigcirc$ 19:00 $\bigcirc$<br>(Monday, 20:00 CEST)    |
| Tuesday  | $07:00$ $\leftarrow$ Start UTC<br>(Tuesday, 08:00 CEST)           | $- 19:00$<br>Stop UTC<br>œ<br>(Tuesday, 20:00 CEST)             |
|          | 07:00 Start UTC<br>Wednes (Wednesday, 08:00 CEST)                 | Stop UTC<br>$19:00$ $\bigoplus$<br>-<br>(Wednesday, 20:00 CEST) |
| Thursday | $07:00$ $\bigoplus$<br><b>Start UTC</b><br>(Thursday, 08:00 CEST) | $19:00$ $\bigoplus$<br>Stop UTC<br>(Thursday, 20:00 CEST)       |
| Friday   | $07:00$ $\oplus$<br>Start UTC<br>(Friday, 08:00 CEST)             | 19:00<br>Stop UTC<br>(Friday, 20:00 CEST)                       |
| Saturday |                                                                   |                                                                 |

#### <span id="page-46-0"></span>**Figure 45 Auto Dealer Control Day by Day Definition schedule table**

When "Day by Day Definition" is enabled, all times setup in the week schedule table overwrite the times previously setup as shown in [Figure 45.](#page-46-0) In case "Day by Day Definition" is further disabled, the previously defined daily schedule will be valid again.

Important notes:

- a) Start and Stop times can be defined by either:
- moving the sliders through the time range on a given day of the week; or
- typing the times directly into the Start and Stop fields; or
- using +/- keys.

Only one start time and only one stop time are allowed on the same day:

b) A time range can be removed by right-clicking on the range line:

![](_page_47_Picture_0.jpeg)

**Figure 46 Removing a time range from Day by Day Definition schedule table**

<span id="page-47-0"></span>c) Time ranges can be inserted by clicking twice on grayed range line; first click adds start time and second click adds stop time:

|        | O <sub>7:00</sub> Start UTC |
|--------|-----------------------------|
| Friday | (Friday, 08:00 CEST)        |
|        |                             |

<span id="page-47-1"></span>**Figure 47 Adding a time range in Day by Day Definition: First click (start time)**

|        | $\bullet$ 07:00 $\bullet$ Start UTC | Stop UTC $\left( 0, 19:00 \right)$ |
|--------|-------------------------------------|------------------------------------|
| Friday | (Friday, 08:00 CEST)                | (Friday, 20:00 CEST)               |
|        |                                     |                                    |

#### <span id="page-47-2"></span>**Figure 48 Adding a time range in Day by Day Definition: Second click (stop time) on same day**

d) Start time and stop time can be set on the same day as shown above or among different days as shown below:

| Sunday | $\bullet$ 22:00 $\bullet$ Start UTC<br>(Sunday, 23:00 CEST) |                                                              |
|--------|-------------------------------------------------------------|--------------------------------------------------------------|
| Monday |                                                             | Stop UTC $\bigcirc$ 19:00 $\bigcirc$<br>(Monday, 20:00 CEST) |

<span id="page-47-3"></span>**Figure 49 Adding a time range in Day by Day Definition: first click on one day and second click on another day**

e) Setting stop time further in subsequent days allows Auto Dealer to run continuously and stop on a specific weekday:

![](_page_48_Picture_0.jpeg)

| Sunday   | 22:00 + Start UTC<br>∍<br>(Sunday, 23:00 CEST)               |
|----------|--------------------------------------------------------------|
| Monday   |                                                              |
| Tuesday  |                                                              |
| Wednes   |                                                              |
| Thursday |                                                              |
| Friday   | Stop UTC $\bigcirc$ 19:00 $\bigcirc$<br>(Friday, 20:00 CEST) |
| Saturday |                                                              |

#### <span id="page-48-0"></span>**Figure 50 Example of Auto Dealer Schedule setup to run continuously from Sunday to Friday**

Setting of the Start time in the past in Day by Day Definition does not activate the Auto Dealer if the Auto Dealer is already disabled at the time of setting this start time. However, if the Auto Dealer is enabled, the stop time set for the future will be applied.

![](_page_49_Picture_0.jpeg)

## <span id="page-49-0"></span>**8 Change Tracking**

The ADS offers change tracking features that can be found on the right side of the ADS menu.

<span id="page-49-1"></span>

| Figure 51 | Change tracking | features |  |  |  |
|-----------|-----------------|----------|--|--|--|

The Live Audit Trail feature offers an overview of changes that were locked in but not saved so far. The details of these changes can be viewed by clicking the button .

<span id="page-49-2"></span>In case the last change must be reverted, the user can click the undo button within the Live Audit Trail tool or in the ADS menu.

![](_page_50_Picture_0.jpeg)

After a change was undone, the redo button can be used to revert the last performed undo an action.

Same features are also available from ADC menu.

![](_page_51_Picture_0.jpeg)

## <span id="page-51-0"></span>**9 Contacting 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360 Treasury Systems AG* Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **Asia Pacific South Asia**

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756

#### **Middle East**

#### **United Arab Emirates**

*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134

#### **EMEA Americas**

#### **USA**

*360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902

#### **India**

*ThreeSixty Trading Networks (India) Pvt Ltd* Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437