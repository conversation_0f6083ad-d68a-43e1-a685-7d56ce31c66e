## **EMS – CSV FILE FORMAT**

![](_page_0_Picture_1.jpeg)

# **TEX MULTIDEALER TRADING SYSTEM**

© 360 TREASURY SYSTEMS AG, 2023

THIS FILE CONTAINS PROPRIETARY AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> INFORMATION INCLUDING <PERSON><PERSON><PERSON> SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG

## **1 CSV FORMAT FOR EMS**

| Field name                         | Type   | Possible values                               | Description and Remarks                                                                                                                                                                                       |
|------------------------------------|--------|-----------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| CF Product / Product*              | String | Spot                                          | Depending on the selected product                                                                                                                                                                             |
|                                    |        | Forward                                       | type, some fields either become<br>mandatory or must not be filled.                                                                                                                                           |
|                                    |        | Swap                                          |                                                                                                                                                                                                               |
|                                    |        | NDF                                           |                                                                                                                                                                                                               |
|                                    |        | NDS                                           |                                                                                                                                                                                                               |
|                                    |        | Deposit                                       |                                                                                                                                                                                                               |
|                                    |        | Loan                                          |                                                                                                                                                                                                               |
|                                    |        | Option                                        |                                                                                                                                                                                                               |
|                                    |        | Metals Outrights                              |                                                                                                                                                                                                               |
|                                    |        | Metals Spreads                                |                                                                                                                                                                                                               |
|                                    |        | Metals Quarterly<br>Strips                    |                                                                                                                                                                                                               |
| CF Group Id / Group Id**           | String | All<br>characters<br>allowed<br>(max.         | Unique external group id to create a<br>block of orders or an OCO order.                                                                                                                                      |
|                                    |        | 255)                                          | Can also be used for aggregated<br>swaps and NDS.                                                                                                                                                             |
|                                    |        |                                               | Can also be used for aggregated<br>NDFs.                                                                                                                                                                      |
|                                    |        |                                               | Can also be used to create an Option<br>Strategy.                                                                                                                                                             |
|                                    |        |                                               | Field is only mandatory if a block of<br>orders or an OCO orders is to be<br>uploaded.                                                                                                                        |
|                                    |        |                                               | Field should be empty for single<br>orders.                                                                                                                                                                   |
| CF External Id / External Id       | String | All<br>characters<br>allowed<br>(max.<br>255) | Unique client id for the orders. Can<br>be empty if not needed. A warning is<br>shown in the preview window if not<br>filled. In case an automated csv<br>upload is used however, the id must<br>be provided. |
| CF Legal entity / Legal<br>entity* | String | 360T.EMS.FOND.<br>A4                          | Only configured entities on 360T are<br>allowed                                                                                                                                                               |
| CF Action / Action*                | String | Buy / Sell                                    | Refers to base currency / currency 1                                                                                                                                                                          |
|                                    |        | deposit / borrow                              | For swaps and NDS the action<br>refers to base currency / currency 1<br>of the far leg.                                                                                                                       |
|                                    |        |                                               | Buy / Sell for FX products                                                                                                                                                                                    |
|                                    |        |                                               | Deposit / borrow for MM products                                                                                                                                                                              |
| CF Currency 1 / Currency           | String | CCY ISO Code                                  | Not required for loan / Deposit                                                                                                                                                                               |
| 1**                                |        | Element Code for<br>Metals                    |                                                                                                                                                                                                               |
| CF Currency 2 / Currency<br>2**    | String | CCY ISO Code                                  | Not required for loan / Deposit                                                                                                                                                                               |

| CF Notional Currency /                        | String  | CCY ISO Code                                                                     |                                                                                                                |
|-----------------------------------------------|---------|----------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------|
| Notional Currency *                           |         | Element Code for<br>Metals                                                       |                                                                                                                |
| CF Premium currency                           | String  | CCY ISO Code                                                                     | Mandatory for FX options                                                                                       |
| CF Notional amount 1 /<br>Notional amount 1*  | Decimal |                                                                                  |                                                                                                                |
| CF Notional amount 2 /<br>Notional amount 2** | Decimal |                                                                                  | Field is only mandatory if a Swap or<br>an NDS is uploaded.                                                    |
| CF Effective Date*                            | Date    |                                                                                  | Must be a valid trading day.                                                                                   |
|                                               |         |                                                                                  | Near Leg Date for Swaps, NDS,<br>Loan and Deposit.                                                             |
|                                               |         |                                                                                  | Premium Date for FX Options.                                                                                   |
| CF Maturity Date / Maturity                   | Date    |                                                                                  | Must be a valid trading day.                                                                                   |
| Date **                                       |         |                                                                                  | Must be after the effective date.                                                                              |
|                                               |         |                                                                                  | Field is only mandatory if a Swap or<br>an NDS is uploaded.                                                    |
|                                               |         |                                                                                  | Far Leg Date for Swaps, NDS, Loan<br>and Deposit.                                                              |
|                                               |         |                                                                                  | Exercise Date for FX Options.                                                                                  |
| CF Limit rate / Limit rate **                 | Decimal |                                                                                  | By providing the limit rate a limit<br>order will be uploaded.                                                 |
|                                               |         |                                                                                  | Field is only mandatory if a Limit<br>Order or an OCO order is uploaded.<br>Otherwise the field must be empty. |
|                                               |         |                                                                                  | A limit can be uploaded for Spot,<br>Forward and Swap Orders.                                                  |
| CF Stop rate / Stop rate **                   | Decimal |                                                                                  | By providing the stop rate a stop<br>order will be uploaded.                                                   |
|                                               |         |                                                                                  | Field is only mandatory if a Stop<br>Order or an OCO order is uploaded.<br>Otherwise the field must be empty.  |
| CF Fixing date 1                              | Date    | DD.MM.YYYY for                                                                   | Must be a valid trading day.                                                                                   |
|                                               |         | NDF and NDS<br>DD.MM.YYYY<br>HH:MM:SS for<br>fixing orders with<br>custom fixing | Field is used for NDF and NDS<br>where it must be provided.                                                    |
|                                               |         |                                                                                  | Field is also used for Fixing Orders<br>where it needs to be provided for<br>the custom fixing.                |
| CF Fixing date 2<br>Date                      |         | DD.MM.YYYY for<br>NDS                                                            | Must be a valid trading day.                                                                                   |
|                                               |         |                                                                                  | Field is only used for NDS and must<br>be provided.                                                            |
| CF Fixing                                     | String  | e.g. BRL01 for<br>NDF & NDS                                                      | Currency specific fixing references<br>used for NDF and NDS. If no value                                       |
|                                               |         | For Fixing<br>Orders:                                                            | is provided EMS falls back on the<br>default fixing reference "Pre-Agreed<br>Fixing"                           |
|                                               |         | BFIX                                                                             | Field is also used for Fixing Orders.                                                                          |
|                                               |         | WMR Intraday<br>London                                                           |                                                                                                                |

|                         |         | WMR Closing<br>London       |                                                                                                                                                                                                                                                                               |
|-------------------------|---------|-----------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                         |         | WMR Australia               |                                                                                                                                                                                                                                                                               |
|                         |         | WMR New York                |                                                                                                                                                                                                                                                                               |
|                         |         | BOC Noon Rate               |                                                                                                                                                                                                                                                                               |
|                         |         | LBMA AM                     |                                                                                                                                                                                                                                                                               |
|                         |         | LBMA PM                     |                                                                                                                                                                                                                                                                               |
|                         |         | TKFE                        |                                                                                                                                                                                                                                                                               |
|                         |         | CNHFIX                      |                                                                                                                                                                                                                                                                               |
|                         |         | ECB                         |                                                                                                                                                                                                                                                                               |
|                         |         | Custom Fixing               |                                                                                                                                                                                                                                                                               |
| CF Spot rate            | Decimal |                             | Mandatory field for FX Options<br>when order should be with Delta<br>Hedge                                                                                                                                                                                                    |
| CF Strike rate          | Decimal |                             | Mandatory field for FX Options                                                                                                                                                                                                                                                |
| CF Option type          | String  | CALL, PUT                   | Mandatory field for FX Options                                                                                                                                                                                                                                                |
| CF Option style         | String  | AMERICAN,<br>EUROPEAN       | Mandatory field for FX Options                                                                                                                                                                                                                                                |
| CF Expiry time (option) | String  | e.g. New York<br>10:00      | Mandatory field for FX Options. Full<br>list can be found in Table 2                                                                                                                                                                                                          |
| Target Premium          | String  | Pay, Receive,<br>Zero, None | Optional field for FX Option<br>Strategies                                                                                                                                                                                                                                    |
|                         |         |                             | For Zero Premium Risk Reversals<br>the Target Premium is given with<br>"Zero" on one leg, with the Target<br>Premium =0 and with the strike<br>price. The other leg needs to be<br>delivered without Target Premium<br>and Target Premium Value and with<br>"0" strike price. |
|                         |         |                             | If Target Premium should be "None"<br>then it is added to one leg and<br>empty on the second leg.                                                                                                                                                                             |
| Target Premium Value    | Decimal |                             | Optional field for FX Option<br>Strategies                                                                                                                                                                                                                                    |
|                         |         |                             | If provided on Option leg, then the<br>strike rate of the other leg must be<br>"0".                                                                                                                                                                                           |
| Delivery date           | Date    |                             | Mandatory field for FX Options                                                                                                                                                                                                                                                |
|                         |         |                             | Must be after the effective /<br>premium date and maturity /<br>exercise date.                                                                                                                                                                                                |
| Bank basket             | String  | COBA.DEMO                   | It is possible to define one or more<br>preferred providers in the upload. In<br>that case the request can only be<br>send to these providers.                                                                                                                                |
|                         |         |                             | Providers are separated by comma                                                                                                                                                                                                                                              |
|                         |         |                             | If required a validation can be<br>enabled so that orders are not<br>uploaded with incorrect providers.                                                                                                                                                                       |

| <b>DEUTSCHE BORSE</b> |  |
|-----------------------|--|
| -----                 |  |

| Expiration                                     | String  | GTC, GTD                              | Provide expiration type for the<br>order. Can be GTC = Good till<br>cancel or GTD = good till date.                                                                                   |
|------------------------------------------------|---------|---------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Expiration date**                              | Date    | 15.12.2019<br>21:00:00                | Field is only mandatory if expiration<br>type GTD is used.                                                                                                                            |
| CF OCO Leg**                                   | Boolean | TRUE, FALSE                           | If an OCO order should be<br>uploaded, then the 2 respective<br>orders must be grouped by a group<br>id and the OCO leg field must be set<br>to TRUE.                                 |
| CF Editable                                    | Boolean | TRUE, FALSE                           | By setting this field to TRUE or if it<br>is kept empty the order will be<br>amendable in EMS. If amending of<br>an order should be blocked, then<br>this field must be set to FALSE. |
| MM Day count                                   | String  | ONE_ONE (1/1)<br>ACT_ACT<br>(ACT/ACT) | For loan / deposit orders the day<br>count convention can be provided.                                                                                                                |
|                                                |         | ACT_365_FIXED<br>(ACT/365<br>(FIXED)) |                                                                                                                                                                                       |
|                                                |         | ACT_360<br>(ACT/360)                  |                                                                                                                                                                                       |
|                                                |         | THIRTY_360<br>(30/360)                |                                                                                                                                                                                       |
|                                                |         | THIRTY_E_360<br>(30E/360)             |                                                                                                                                                                                       |
|                                                |         | BUS_252<br>(BUS/252)                  |                                                                                                                                                                                       |
| Trading capacity                               | String  | DEAL<br>MTCH<br>AOTC                  | MiFID II field. Non mandatory field.<br>If not provided the company default<br>value is used                                                                                          |
|                                                |         | NONE                                  |                                                                                                                                                                                       |
| Investment decision maker<br>is natural person | Boolean | TRUE, FALSE                           | MiFID II field.                                                                                                                                                                       |
| Investment decision maker                      | String  | User ID on 360T                       | MiFID II field. Non mandatory field.<br>If not provided the company default<br>value is used.                                                                                         |
| Order Uploader                                 | String  | User ID on 360T                       | MiFID II field. Defines the Execution<br>Decision Maker                                                                                                                               |
| Order Uploader is natural<br>person            | Boolean | TRUE, FALSE                           | MiFID II field.                                                                                                                                                                       |
| MTF                                            | Boolean | TRUE, FALSE                           | MiFID II field. Defines if the order is<br>traded on the MTF or on the OTC<br>platform                                                                                                |
| Minimum Banks                                  | Integer | 3                                     | Minimal number of banks that must<br>be sending a quote for auto<br>execution of the order                                                                                            |
| Max RFS TTL                                    | Integer | 120                                   | Maximal RFS request time in<br>seconds for the auto execution                                                                                                                         |
| Min Wait Time                                  | Integer | 0                                     | Minimal waiting time in seconds for<br>the auto execution after minimal<br>number of banks have quoted                                                                                |

| Negotiation Wait Time                 | Integer or<br>Date | 10<br>29.10.2020,17:40:<br>25 | Waiting time before auto execution<br>rule is sending the request to the<br>market. Value can be in<br>•<br>Sec<br>•<br>HH:MM<br>•<br>HH:MM:SS<br>•<br>DD.MM.YYYY HH:MM:SS<br>•<br>DD.MM.YYYY HH:MM<br>•<br>DD.MM.YYYY                                             |
|---------------------------------------|--------------------|-------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Min Spread Tolerance                  | Decimal            |                               | min allowed distance from the<br>configured price source                                                                                                                                                                                                           |
| Max Spread Tolerance                  | Decimal            |                               | max allowed distance from the<br>configured price source                                                                                                                                                                                                           |
| Value Type                            | String             | ABSOLUTE,<br>PERCENT          | Tolerance type given in absolute<br>value or percentage value                                                                                                                                                                                                      |
| Fee Type                              | String             | PER TICKET,<br>PER MILLION    | Per Ticket fee or fee per million                                                                                                                                                                                                                                  |
| Fee Currency                          | String             | CCY ISO Code                  |                                                                                                                                                                                                                                                                    |
| Fee Value                             | Decimal            | 100,00                        |                                                                                                                                                                                                                                                                    |
| Custodian                             | String             | COBA                          | Name of the custodian as<br>configured on 360T Platform                                                                                                                                                                                                            |
| Check Custodian for Auto<br>Execution | Boolean            | TRUE, FALSE                   | Defines if the auto execution should<br>check for a price from the uploaded<br>or configured custodian. If set to<br>true and custodian is not pricing,<br>then the request is not executed<br>automatically                                                       |
| CF Order Book                         | STRING             |                               | Name of the order book to which a<br>requester entity / fund belongs to.<br>Only required if an entity has more<br>than one order book assigned to it.                                                                                                             |
| Order Group Id                        | STRING             | Unblock id                    | Parameter to allow a block order to<br>be unblocked. When this parameter<br>is set, the block order is imported as<br>single orders so that it can be<br>merged with other matching orders<br>either with RFS for block request or<br>through portfolio execution. |
| Offline SpotRate                      | Decimal            | 1.12345                       | Spot Rate for the Directed<br>Negotiation Workflow for offline<br>Confirmation.                                                                                                                                                                                    |
|                                       |                    |                               | Supported for Spot and Forward.                                                                                                                                                                                                                                    |
| Offline ForwardPoints                 | Decimal            | 0.123                         | Forward Points for the Directed<br>Negotiation Workflow for offline<br>Confirmation.                                                                                                                                                                               |
|                                       |                    |                               | Supported for Forward.                                                                                                                                                                                                                                             |

Table 1: Field definitions for csv order upload

### **1.1 Csv file versioning**

EMS supports csv file versioning. This means that even when 360T adds new products and therefore new fields to the csv file, your existing csv file would still be supported.

### **1.2 Csv file example**

| CF Product;CF Group Id;CF External Id;CF Legal entity;CF Action;CF Currency 1;CF Currency 2;CF Notional<br>Currency;CF Premium currency;CF Notional amount 1;CF Notional amount 2;CF Effective Date;CF Maturity Date;CF<br>Limit rate;CF Stop rate;CF Spot rate;CF Strike rate;CF Fixing date 1;CF Fixing date 2;CF Fixing;CF Option<br>type;CF Option style;CF Expiry time (option);Delivery date;Bank basket;Expiration;Expiration date; CF OCO Leg;CF<br>Editable;MM Day count;Trading capacity;Investment decision maker is natural person;Investment decision<br>maker;Order Uploader;Order Uploader is natural person;MTF; Minimum Banks;Max RFS TTL;Min Wait Time;Min Spread<br>Tolerance;Max Spread Tolerance;Value Type;Fee Type;Fee Currency;Fee Value;Custodian;Check Custodian for Auto<br>Execution; Negotiation Wait Time;CF Order Book |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Spot;;Spot Example;360T.EMS.FOND.A1;Buy;EUR;GBP;GBP;;2000;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| Forward;;Forward<br>Example;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;20000;;17.12.2019;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| Swap;;Swap<br>Example;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;20000;20000;16.12.2019;23.12.2019;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| NDF;;NDF<br>Example;360T.EMS.FOND.A4;Buy;USD;BRL;USD;;20000;;18.12.2019;;;;;;16.12.2019;;BRL01;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| NDS;;NDS<br>Example;360T.EMS.FOND.A4;Buy;USD;BRL;USD;;20000;20000;18.12.2019;09.01.2020;;;;;16.12.2019;07.01.2020;BRL01;;;;;<br>;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Spot;Block example;leg id 1;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;2000;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| Forward;Block example;leg id<br>2;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;20000;;17.12.2019;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Forward;Block example;leg id<br>3;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;20000;;17.12.2019;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Forward;;Limit Order<br>example;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;20000;;16.12.2019;;1,12132;;;;;;;;;;;;;;;FALSE;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| Forward;;Stop Order<br>example;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;20000;;16.12.2019;;;1,43323;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Forward;;Fixing Order<br>example;360T.EMS.FOND.A1;Buy;EUR;USD;EUR;;5000000;;16.12.2019;;;;;;;;ECB;;;;;360TBANK.TEST;;;;;;;;;;;;;;;;;;;;;;<br>;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| Forward;;Fixing Order example - custom<br>Fixing;360T.EMS.FOND.A1;Buy;EUR;USD;EUR;;5000000;;16.12.2019;;;;;;16.12.2019 10:00;;Custom<br>Fixing;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Forward;OCO Example;OCO<br>leg1;360T.EMS.FOND.A1;Buy;EUR;GBP;GBP;;20000;;17.12.2019;;1,12343;;;;;;;;;;;;;;TURE;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| Forward;OCO Example;OCO<br>leg2;360T.EMS.FOND.A1;Buy;EUR;GBP;GBP;;20000;;16.12.2019;;;1,43232;;;;;;;;;;;;;TRUE;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| Deposit;;Loan<br>example;360T.EMS.FOND.A1;deposit;;;GBP;;123456,12;;29.11.2019;16.12.2019;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| Loan;;Deposit<br>example;360T.EMS.FOND.A1;borrow;;;EUR;;123443,32;;27.11.2019;16.12.2019;;;;;;;;;;;;;GTD;27.11.2019;;;;;;;;;;;;;;<br>;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| Forward;;MTF<br>example;360T.EMS.FOND.A1;Buy;EUR;USD;EUR;;5000000;;16.12.2019;;;;;;;;;;;;;;;;;;;AOTC;TRUE;360T.EMSUser2;360T.EMS<br>User4;YES;TRUE;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| Forward;;fixed fee, tolerance & auto execution<br>example;360T.EMS.FOND.A1;Buy;EUR;USD;EUR;;5000000;;16.12.2019;;;;;;;;;;;;;;;;;;;;;;;;;3;120;0;1;2;PERCENT;PER<br>MILLION;EUR;100;COBA.DEMO;true;10;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| Forward;;preferred provider<br>example;360T.EMS.FOND.A4;Buy;EUR;GBP;GBP;;20000;;18.12.2019;;;;;;;;;;;;;COBA.DEMO,CITIBANK.DEMO;;;;;;;;;;;;;;;;;<br>;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Option;;FX Option<br>example;360T.EMS.FOND.A1;Buy;EUR;USD;EUR;EUR;20000;;08.10.2019;04.11.2019;;;1,5644;1,4;;;;PUT;AMERICAN;New York<br>10:00;08.11.2019;;;;;;;;;;;;;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| Metals Outrights;;Exampe for metals;360T.EMS.FOND.A1;Buy;FE1;USD;FE1;20000;;10.12.2021;;;;;;;;;;;;;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |

Figure Figure 1: CSV upload example 2: csv example

## **1.3 Uploading an abbreviated csv file**

EMS supports the upload of an abbreviated csv file. If only a subset of products is to be uploaded, then unrelated fields can be omitted in the upload file. Mandatory fields for the respective products must be included and filled.

## **2 EXPIRY / CUTOFF TIME FOR FX OPTIONS**

FX options require an expiry or cut-off time to be provided in the upload. The following table shows all supported cut-off times.

| Almaty 11:00              | Almaty 12:00          | Beijing 09:15            | Beijing 16:00         |
|---------------------------|-----------------------|--------------------------|-----------------------|
| Beijing 17:00             | Berlin 14:15          | Bogota 10:30             | Bogota 12:30          |
| Bucharest 13:00           | Budapest 12:00        | Buenos<br>Aires<br>12:00 | Buenos Aires<br>12:30 |
| Calcutta 13:00            | ECB 14:15             | Frankfurt 13:00          | Frankfurt 14:00       |
| Frankfurt 14:15           | Hong Kong 11:00       | Hong Kong 11:15          | Hong Kong 16:00       |
| Istanbul 14:00            | Jakarta 10:00         | Jakarta 10:30            | Kuala Lumpur<br>11:00 |
| Kuala Lumpur<br>11:30     | Lima 11:00            | London 09:30             | London 10:30          |
| London 11:00              | London 12:00          | London 15:00             | London 16:00          |
| London 17:00              | Manila 11:00          | Manila 11:15             | Manila 11:30          |
| Manila 12:00              | Manila 12:30          | Mexico City<br>12:00     | Mexico City 12:30     |
| Moscow 11:00              | Moscow 12:30          | Moscow 13:30             | Moscow 15:00          |
| Moscow 16:30              | Mumbai 12:00          | Mumbai 12:30             | Mumbai 14:30          |
| New York 10:00            | New York 10:30        | New York 12:00           | New York 15:00        |
| New York Bullion<br>09:30 | New York Cut<br>12:30 | Santiago 10:30           | Sao Paulo 12:00       |
| Sao Paulo 12:30           | Sao Paulo 13:15       | Sao Paulo 18:00          | Sao Paulo 20:30       |
| Seoul 09:00               | Seoul 11:00           | Seoul 14:00              | Seoul 15:30           |
| Seoul 17:30               | Shanghai 09:30        | Singapore 10:00          | Singapore 11:00       |
| Sydney 15:00              | Taipei 11:00          | Tel Aviv 12:00           | Tel Aviv 15:00        |
| Tokyo 09:00               | Tokyo 10:00           | Tokyo 12:00              | Tokyo 14:00           |
| Tokyo 15:00               | Warsaw 11:00          | Warsaw 12:30             | Wellington 15:00      |
| Zurich 10:00              | Zurich 18:00          |                          |                       |

<span id="page-8-0"></span>Table 2: Cut-Off times for FX options

## **3 FIXING SOURCES FOR NDF/NDS**

NDF and NDS orders require a fixing reference which depends on the selected currency pair. The table below shows all supported fixing references.

| Symbol | List of supported fixing references                                                                                                        |
|--------|--------------------------------------------------------------------------------------------------------------------------------------------|
| EURARS | 'EMTA (ARS05)-BFIX EUR L080', 'EMTA (ARS05)-BFIX EUR<br>L130', 'EMTA<br>(ARS05)-BFIX EUR L160', 'EMTA (ARS05)-<br>WMCo 4pm LDN'            |
| EURBRL | 'PTAX-BFIX EUR L080', 'PTAX-BFIX EUR L130', 'PTAX-BFIX<br>EUR L160',<br>'PTAX-WMCo 4pm LDN'                                                |
| EURCLP | 'CLPOBS (CLP10)-BFIX EUR L080', 'CLPOBS (CLP10)-BFIX<br>EUR L130',<br>'CLPOBS (CLP10)-BFIX EUR L160', 'CLPOBS<br>(CLP10)-WMCo 4pm LDN'     |
| EURCNY | 'SAEC (CNY01)-BFIX EUR L080', 'SAEC (CNY01)-BFIX EUR<br>L130', 'SAEC<br>(CNY01)-BFIX EUR L160', 'SAEC (CNY01)-<br>WMCo<br>4pm LDN'         |
| EURCOP | 'TRM (COP02)-BFIX EUR L080', 'TRM (COP02)-BFIX EUR<br>L130', 'TRM<br>(COP02)-BFIX EUR L160', 'TRM (COP02)-WMCo<br>4pm LDN'                 |
| EURINR | 'FBIL'                                                                                                                                     |
| EURIDR | 'JISDOR (IDR04)-BFIX EUR L080', 'JISDOR (IDR04)-BFIX EUR<br>L130', 'JISDOR<br>(IDR04)-BFIX EUR L160', 'JISDOR (IDR04)-<br>WMCo 4pm LDN'    |
| EURKRW | 'KFTC18-BFIX EUR L080', 'KFTC18-BFIX EUR L130',<br>'KFTC18-BFIX EUR<br>L160', 'KFTC18-WMCo 4pm LDN'                                        |
| EURMYR | 'ABSIRFIX01-BFIX EUR L080', 'ABSIRFIX01-BFIX EUR L130',<br>'ABSIRFIX01-BFIX EUR L160', 'MYRFIX2-ECB37'                                     |
| EURPEN | 'PEN05-BFIX EUR L080', 'PEN05-BFIX EUR L130', 'PEN05-<br>BFIX EUR L160','PEN05-WMCo 4pm LDN'                                               |
| EURPHP | 'PDSPESO-BFIX<br>EUR L080', 'PDSPESO-BFIX EUR L130',<br>'PDSPESO-BFIX<br>EUR L160', 'PDSPESO-WMCo 4pm LDN'                                 |
| EURRUB | 'CNE-EMTA (RUB03)-BFIX EUR L080', 'CNE-EMTA (RUB03)-<br>BFIX EUR L130','CNE-EMTA (RUB03)-BFIX EUR L160', 'CNE<br>EMTA (RUB03)-WMCo 4pmLDN' |
| EURTWD | 'TAIFX1 (TWD03)-BFIX EUR L080', 'TAIFX1 (TWD03)-BFIX<br>EUR L130','TAIFX1 (TWD03)-BFIX EUR L160', 'TAIFX1<br>(TWD03)-WMCo 4pm LDN'         |
| GBPARS | 'EMTA (ARS05)-WMCo 4pm LDN'                                                                                                                |

| GBPBRL | 'PTAX-WMCo 4pm LDN'                                                       |
|--------|---------------------------------------------------------------------------|
| GBPCLP | 'CLPOBS-WMCo 4pm LDN'                                                     |
| GBPCNY | 'SAEC-WMCo 4pm LDN'                                                       |
| GBPCOP | 'COP TRM-WMCo 4pm LDN'                                                    |
| GBPEGP | 'FEMF-WMCo 4pm LDN'                                                       |
| GBPIDR | 'JISDOR-WMCo 4pm LDN'                                                     |
| GBPINR | 'FBIL'                                                                    |
| GBPKRW | 'KFTC18-WMCo 4pm LDN'                                                     |
| GBPKZT | 'KZFXWA-WMCo<br>4pm LDN'                                                  |
| GBPMYR | 'MYR PPKM-WMCo 4pm LDN'                                                   |
| GBPPEN | 'PEN05-WMCo 4pm LDN'                                                      |
| GBPPHP | 'PDSPESO-WMCo 4pm LDN'                                                    |
| GBPRSD | 'RSDFIX-WMCo 4pm LDN'                                                     |
| GBPRUB | 'CNE EMTA (RUB03)-WMCo 4pm LDN'                                           |
| GBPTWD | 'TAIFX1-WMCo 4pm LDN'                                                     |
| GBPUAH | 'EMTAUAHFUIX-WMCo 4pm LDN'                                                |
| GBPVEF | 'VEB01-WMCo 4pm LDN'                                                      |
| GBPVND | 'ABSIRFIX01-WMCo 4pm LDN'                                                 |
| USDARS | 'ARS01', 'ARS02', 'EMTA (ARS05)'                                          |
| USDBRL | 'BRL01', 'BRL02', 'BRL03', 'BRL10', 'BRL11', 'PTAX (BRL09)'               |
| USDCLP | 'CLP01', 'CLP02', 'CLP03', 'CLP04', 'CLP08', 'CLP09',<br>'CLPOBS (CLP10)' |
| USDCNY | 'SAEC (CNY01)'                                                            |
| USDCOP | 'COP01', 'COP TRM (COP02)', 'CO/COL3'                                     |
| USDEGP | 'FEMF (EGP01)'                                                            |
| USDIDR | 'JISDOR (IDR04)', 'ABSIRFIX01', 'INR01', 'RBIB'                           |
| USDINR | 'FBIL'                                                                    |

| USDKRW | 'KRW02', 'KFTC18'                             |
|--------|-----------------------------------------------|
| USDKZT | 'KZFXWA'                                      |
| USDMYR | 'MYR PPKM (MYR03)'                            |
| USDPEN | 'PEN01', 'PEN02', 'PEN05'                     |
| USDPHP | 'PHP01', 'PHP02', 'PHP03', 'PHP04', 'PDSPESO' |
| USDRSD | 'RSDFIX'                                      |
| USDRUB | 'RUB01', 'RUB02', 'CNE-EMTA (RUB03)'          |
| USDTWD | 'TWD01', 'TWD02', 'TAIFX1 (TWD03)'            |
| USDUAH | 'EMTAUAHFIX'                                  |
| USDVEF | 'VEB01'                                       |
| USDVND | 'ABSIRFIX01'                                  |

Table 3: Supported fixing references

## **4 ELEMENTS FOR METALS**

| Symbol             | List of supported metals | Products supported      |
|--------------------|--------------------------|-------------------------|
| Aluminum           | AL                       | Metals Outrights        |
|                    |                          | Metals Spreads          |
| Copper             | CU                       | Metals Outrights        |
|                    |                          | Metals Spreads          |
| Iron Ore<br>100Ton | FE1                      | Metals Outrights        |
|                    |                          | Metals Spreads          |
|                    |                          | Metals Quarterly Strips |
| Iron Ore<br>500Ton | FE5                      | Metals Outrights        |
|                    |                          | Metals Spreads          |
|                    |                          | Metals Quarterly Strips |
| Lead               | PB                       | Metals Outrights        |
|                    |                          | Metals Spreads          |
| Nickel             | NI                       | Metals Outrights        |
|                    |                          | Metals Spreads          |
| Tin                | SN                       | Metals Outrights        |
|                    |                          | Metals Spreads          |
| Zinc               | ZN                       | Metals Outrights        |
|                    |                          | Metals Spreads          |

Table 4: Elements for Metals

## **5 CONTACT 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **United Arab Emirates**

*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 491 5134

#### **EMEA Americas**

### **USA**

*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900 Fax: ****** 776 2902

### **Middle East Asia Pacific**

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #27-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6536 0662