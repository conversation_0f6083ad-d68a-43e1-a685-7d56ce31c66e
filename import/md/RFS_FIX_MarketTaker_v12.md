<span id="page-0-0"></span>![](_page_0_Picture_0.jpeg)

# RFS Market Taker API

FIX Rules of Engagement

API Version: 12.14 Platform Release: 4.22

Implements FIX Protocol Version 4.4

360 Treasury Systems AG Grüneburgweg 16-18 / Westend Carrée D-60322 Frankfurt am Main Tel: +49 69 900289 0 Fax: +49 69 900289 29 Email: <EMAIL> Commercial Register Frankfurt, No. HRB 49874 Executive Board: <PERSON>, <PERSON>, <PERSON> Supervisory Board: <PERSON>

## <span id="page-1-0"></span>**1 Overview**

The purpose of this document is to provide an overview of the FIX services offered by 360 Treasury Systems for FX products. It focuses on the technical aspects and is intended to provide clients with a clear understanding of how a successful connection could be made.

It contains an overview of the general workflow, as well as detailed specifications of the utilized FIX messages. The API is implemented to meet FIX 4.4 standards.

## **Table of Contents**

| 1 | Overview                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 2                                                                         |
|---|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------|
| 2 | Product Offering<br>2.1<br>Products currently supported by API<br>2.2<br>Market data<br>.                                                                                                                                                                                                                                                                                                                                                                 | 6<br>6<br>6                                                               |
| 3 | Important Disclaimer for API Clients                                                                                                                                                                                                                                                                                                                                                                                                                      | 7                                                                         |
| 4 | Connecting to 360T<br>4.1<br>Network Connectivity<br>4.1.1<br>Radianz<br>4.1.2<br>Stunnel<br>.<br>4.1.3<br>Cross Connect<br>.<br>4.1.4<br>N7<br>4.1.5<br>Plain Internet<br>4.2<br>Connection and Firewall Configuration<br>4.3<br>FIX Engine Compatibility Testing<br>.<br>4.4<br>FIX Protocol Levels<br>4.4.1<br>User Credentials<br>.<br>4.4.2<br>FIX Session Reset<br>.<br>4.5<br>Availability<br>.<br>4.6<br>Trading Hours<br>4.7<br>Timeout handling | 8<br>8<br>8<br>8<br>8<br>8<br>8<br>8<br>9<br>9<br>9<br>9<br>9<br>10<br>10 |
| 5 | Utilization of the FIX Protocol<br>5.1<br>System Messages<br>.<br>5.2<br>Business Messages<br>.<br>5.3<br>360T customization details<br>.<br>5.3.1<br>Side<br>5.3.2<br>Quote Cancels and Business Rejections<br>.<br>5.4<br>General Workflow                                                                                                                                                                                                              | 11<br>11<br>11<br>12<br>12<br>12<br>12                                    |
| 6 | System Messages<br>6.1<br>Notes on important details<br>.<br>6.2<br>Message Header<br>6.3<br>Message Footer<br>.<br>6.4<br>Logon [A]<br>6.5<br>Heartbeat [0]<br>.<br>6.6<br>TestRequest [1]<br>.<br>6.7<br>ResendRequest [2]<br>.<br>6.8<br>Reject [3]<br>.<br>6.9<br>SequenceReset [4]<br>.<br>6.10 Logout [5]                                                                                                                                           | 14<br>14<br>14<br>14<br>15<br>15<br>15<br>16<br>16<br>17<br>18            |
| 7 | Business Messages<br>7.1<br>News [B]<br>.                                                                                                                                                                                                                                                                                                                                                                                                                 | 19<br>19                                                                  |

©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 3

|   | 7.2 |           | QuoteRequest [R]<br>20                        |
|---|-----|-----------|-----------------------------------------------|
|   | 7.3 |           | QuoteRequestReject [AG]<br>.<br>29            |
|   | 7.4 |           | BusinessMessageReject [j]<br>.<br>30          |
|   | 7.5 | Quote [S] | .<br>31                                       |
|   | 7.6 |           | QuoteCancel [Z]<br>.<br>34                    |
|   |     | 7.6.1     | Customer → 360T<br>.<br>34                    |
|   |     | 7.6.2     | Customer ← 360T<br>.<br>34                    |
|   | 7.7 |           | New Order Single [D]<br>36                    |
|   | 7.8 |           | New Order Multileg [AB]<br>40                 |
|   | 7.9 |           | Execution Report [8]<br>.<br>42               |
|   |     |           | 7.10 SecurityDefinitionRequest [c]<br>.<br>51 |
|   |     |           | 7.11 SecurityDefinition [d]<br>52             |
| 8 |     |           | Example Messages<br>54                        |
|   | 8.1 | News [B]  | .<br>54                                       |
|   | 8.2 |           | QuoteRequest [R]<br>55                        |
|   |     | 8.2.1     | Tradeable Quotes: Spot<br>55                  |
|   |     | 8.2.2     | Tradeable Quotes: Forward<br>55               |
|   |     | 8.2.3     | Tradeable Quotes: FX Time Options<br>56       |
|   |     | 8.2.4     | Tradeable Quotes: Swap<br>.<br>56             |
|   |     | 8.2.5     | Tradeable Quotes: NDF<br>.<br>57              |
|   |     | 8.2.6     | Tradeable Quotes: NDS<br>.<br>58              |
|   |     | 8.2.7     | Tradeable Quotes: FX Block Trade<br>.<br>58   |
|   |     | 8.2.8     | Tradeable Quotes: NDF Block Trade<br>.<br>59  |
|   |     | 8.2.9     | Tradeable Quotes: Loan/Deposit<br>60          |
|   |     | 8.2.10    | Request for Market data (FORWARD)<br>61       |
|   | 8.3 |           | QuoteRequestReject [AG]<br>.<br>62            |
|   |     |           |                                               |
|   | 8.4 |           | BusinessMessageReject [j]<br>.<br>62          |
|   | 8.5 | Quote [S] | .<br>63                                       |
|   |     | 8.5.1     | Spot Quote - Buy<br>63                        |
|   |     | 8.5.2     | Forward Quote - Two-Way<br>63                 |
|   |     | 8.5.3     | FX Time Options quote - Two-Way<br>.<br>64    |
|   |     | 8.5.4     | Swap Quote - Two-Way<br>.<br>64               |
|   |     | 8.5.5     | NDF quote - Two-Way<br>65                     |
|   |     | 8.5.6     | NDS quote - Two-Way<br>65                     |
|   |     | 8.5.7     | Market Data quote - Forward, Two-Way<br>65    |
|   | 8.6 |           | QuoteCancel [Z]<br>.<br>66                    |
|   |     | 8.6.1     | Customer → 360<br>.<br>66                     |
|   |     | 8.6.2     | Customer ← 360T<br>.<br>66                    |
|   | 8.7 |           | NewOrderSingle [D]<br>.<br>66                 |
|   |     | 8.7.1     | Previously Quoted - Forward<br>66             |
|   |     | 8.7.2     | Previously Quoted - FX Time Option<br>.<br>67 |
|   |     | 8.7.3     | Previously Quoted - Swap<br>.<br>68           |
|   |     | 8.7.4     | Previously Quoted - NDF<br>.<br>68            |
|   |     | 8.7.5     | Previously Quoted - NDS<br>.<br>69            |
|   |     | 8.7.6     | Market order - Forward<br>70                  |
|   |     | 8.7.7     | Limit order - Forward<br>.<br>70              |
|   | 8.8 |           | NewOrderMultileg [AB]<br>.<br>71              |
|   |     | 8.8.1     | Previously Quoted - FX Block Trade<br>71      |
|   |     | 8.8.2     | Market order - NDF Block Trade<br>.<br>72     |

|                      | 8.9<br>ExecutionReport [8]<br>8.9.1<br>Execution report - Order received<br>8.9.2<br>Execution report - Order executed<br>8.10 SecurityDefinitionRequest [c]<br>8.11 SecurityDefinition [d] | .<br>.<br>. | 73<br>73<br>74<br>74<br>75 |  |  |
|----------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|----------------------------|--|--|
| 9                    | Firewall Configuration                                                                                                                                                                      |             | 77                         |  |  |
| 10 FIX Session Reset |                                                                                                                                                                                             |             | 78                         |  |  |
|                      | 11 Appendix<br>11.1 CFI Codes<br>11.2 Fixing Sources for NDF/NDS                                                                                                                            | .           | 79<br>79<br>79             |  |  |
|                      | 12 Version Log                                                                                                                                                                              |             | 83                         |  |  |

## **List of Tables**

| 6.1 | Standard Header for FIX Messages<br>14                                                                             |  |
|-----|--------------------------------------------------------------------------------------------------------------------|--|
| 6.2 | Standard Footer for FIX Messages<br>.<br>14                                                                        |  |
| 6.3 | Logon message<br>.<br>15                                                                                           |  |
| 6.4 | Heartbeat message<br>.<br>15                                                                                       |  |
| 6.5 | TestRequest message<br>.<br>16                                                                                     |  |
| 6.6 | ResendRequest message<br>.<br>16                                                                                   |  |
| 6.7 | Reject message<br>.<br>17                                                                                          |  |
| 6.8 | SequenceReset message<br>18                                                                                        |  |
| 6.9 | Logout message<br>18                                                                                               |  |
|     |                                                                                                                    |  |
| 7.1 | News message<br>19                                                                                                 |  |
| 7.2 | QuoteRequest message<br>.<br>28                                                                                    |  |
| 7.3 | QuoteRequestReject message<br>29                                                                                   |  |
| 7.4 | BusinessMessageReject message<br>30                                                                                |  |
| 7.5 | Quote message<br>.<br>33                                                                                           |  |
| 7.6 | QuoteCancel message from customer to 360T<br>34                                                                    |  |
| 7.7 | QuoteCancel message from 360T to customer<br>35                                                                    |  |
| 7.8 | NewOrderSingle message<br>39                                                                                       |  |
| 7.9 | NewOrderMultileg message<br>.<br>41                                                                                |  |
|     | 7.10 ExecutionReport message<br>50                                                                                 |  |
|     | 7.11 SecurityDefinitionRequest message<br>.<br>51                                                                  |  |
|     | 7.12 SecurityDefinition message<br>52                                                                              |  |
|     | 7.13 FX tenors<br>.<br>53                                                                                          |  |
|     |                                                                                                                    |  |
| 8.1 | Example News message<br>54                                                                                         |  |
| 8.2 | Example News message<br>54                                                                                         |  |
| 8.3 | Example QuoteRequest for Spot quotes<br>.<br>55                                                                    |  |
| 8.4 | Example QuoteRequest for Forward quotes<br>.<br>56                                                                 |  |
| 8.5 | Example QuoteRequest for Forward quotes<br>.<br>56                                                                 |  |
| 8.6 | Example QuoteRequest for Swap quotes<br>57                                                                         |  |
| 8.7 | Example QuoteRequest for NDF quotes<br>.<br>58                                                                     |  |
| 8.8 | Example QuoteRequest for NDS quotes<br>.<br>58                                                                     |  |
| 8.9 | Example QuoteRequest for FX Block Trade quotes<br>.<br>59                                                          |  |
|     | 8.10 Example QuoteRequest for NDF Block Trade quotes<br>.<br>60                                                    |  |
|     |                                                                                                                    |  |
|     |                                                                                                                    |  |
|     | 8.11 Example QuoteRequest for Loan/Deposit quotes<br>61                                                            |  |
|     | 8.12 Example QuoteRequest for Spot Market data quotes<br>62                                                        |  |
|     | 8.13 Example BusinessMessageReject message<br>62                                                                   |  |
|     | 8.14 Example BusinessMessageReject message<br>62                                                                   |  |
|     | 8.15 Example Spot quote<br>63                                                                                      |  |
|     | 8.16 Example Forward quote<br>64                                                                                   |  |
|     | 8.17 Example Forward quote<br>64                                                                                   |  |
|     | 8.18 Example Forward quote<br>65                                                                                   |  |
|     | 8.19 Example Forward Market data quote<br>66                                                                       |  |
|     | 8.20 Example QuoteCancel message from customer<br>.<br>66<br>8.21 Example QuoteCancel message from 360T<br>.<br>66 |  |

|     | 8.22 Example NewOrderSingle message (forward)<br>.                  | 67 |
|-----|---------------------------------------------------------------------|----|
|     | 8.23 Example NewOrderSingle message (forward)<br>.                  | 68 |
|     | 8.24 Example NewOrderSingle message (swap)                          | 68 |
|     | 8.25 Example NewOrderSingle message (NDF)                           | 69 |
|     | 8.26 Example NewOrderSingle message (swap)                          | 70 |
|     | 8.27 Example NewOrderSingle message (Market order)<br>.             | 70 |
|     | 8.28 Example NewOrderSingle message (Limit order)<br>.              | 71 |
|     | 8.29 Example NewOrderMultileg message (FX Block Trade)              | 72 |
|     | 8.30 Example NewOrderMultileg message (NDF Block Trade)             | 73 |
|     | 8.31 Example ExecutionReport message (confirmation of receipt)<br>. | 73 |
|     | 8.32 Example ExecutionReport message (execution confirmation)<br>.  | 74 |
|     | 8.33 Example SecurityDefinitionRequest message<br>.                 | 75 |
|     | 8.34 Example SecurityDefinition message                             | 76 |
|     |                                                                     |    |
| 9.1 | 360T IP addresses                                                   | 77 |
|     |                                                                     |    |
|     | 10.1 360T FIX Session<br>.                                          | 78 |
|     |                                                                     |    |
|     | 11.1 Supported CFI codes.                                           | 79 |
|     | 11.2 Supported fixing source string for NDF/NDS.<br>.               | 82 |

![](_page_7_Picture_0.jpeg)

## **List of Figures**

| 5.1 | Basic RFS Message Flow | 13 |
|-----|------------------------|----|
|-----|------------------------|----|

## <span id="page-8-0"></span>**2 Product Offering**

360T's FIX offering acts as an intermediary connecting Banks and other market participants. Request For Quote is available through a FIX connection. The aim is to provide sub-second latency from a client's FIX engine to the 360T FX marketplace. During normal operation, the link from the client to the system is entirely automatic (i.e. no human intervention).

## <span id="page-8-1"></span>**2.1 Products currently supported by API**

- 1. FX Spot
- 2. FX Forward
- 3. FX NDF
- 4. FX Swap
- 5. FX NDS
- 6. FX Time Options
- 7. FX Block Trade
- 8. NDF Block Trade
- 9. Metals Outright
- 10. Metals Spread
- 11. Metals Quarterly Strip
- 12. MM Loan
- 13. MM Deposit

## <span id="page-8-2"></span>**2.2 Market data**

The FIX RFS Market Taker service can also be used to request market data for all the products mentioned above.

## <span id="page-9-0"></span>**3 Important Disclaimer for API Clients**

When executing trades over the API, the client has to consider that the counterparties to each trade are the client and the respective provider. Between the trading parties, the client is solely responsible for the performance, accuracy, reliability, completeness, timelines and enforcement of the trade. The client acknowledges that 360T is not a counterparty to the trade and is neither responsible for the clients nor any third partys use of any information transmitted through the API nor is able to negotiate the details of any trade.

This raises the following issues for clients of the API:

- The client has to ensure that every execution request sent to the API receives a timely and definitive response (either executed or not executed) from the API and is solely responsible for implementing any suitable technical procedures which protects the client against all technical problems that can result in an unclear trade status, including, but not limited to, network disconnections, implementation bugs and incorrect protocol implementations.
- In addition the client is solely responsible for reconciling trades executed over the API with the respective providers and for contacting the providers directly in the event of any issues relating to specific trades.

In the event of a trade discrepancy, the client should check the Deal Blotter to determine the status of the trade on the 360T platform. The 360T Technical Support Help will be able to assist if a specific trade cannot be found in the Deal Blotter or if a specific trade initiated over the API needs to be reconciled with a trade shown in the Deal Blotter.

360T IS NOT ABLE TO CANCEL OR EXECUTE A TRADE. However, if requested in writing by both counterparties of the trade, the 360T Technical Support Help Desk is able to change the status displayed in the Deal Blotter for a trade from executed to cancelled. This will also change the status of the trade for the purposes of reporting and statistics. However, for the avoidance of doubt, this action does not cancel the executed trade legally or effectively.

## <span id="page-10-0"></span>**4 Connecting to 360T**

Once interest has been established in connecting a client to 360T's FIX services, a project manager will be assigned. The project manager will manage the process of connecting the FIX engines of the two companies, performing FIX engine tests, and bringing the connection live.

## <span id="page-10-1"></span>**4.1 Network Connectivity**

Only incoming connections are supported, i.e. the customer always initiates connections to 360T. Clients may connect to 360T's electronic FX trading system using one of the following options:

### <span id="page-10-2"></span>**4.1.1 Radianz**

If a client is already connected to Radianz, lead-time to establish connectivity between the client and 360T is ten days, taking into account change control procedures. If the client is not already connected, the lead-time will be advised at the time the order is placed. Radianz will quote a low network latency and will offer a highlevel of encryption for traffic passing through its network. This is the preferred connectivity option. For more information see [http://www.radianz.com](http://www.radianz.com/)

### <span id="page-10-3"></span>**4.1.2 Stunnel**

Connections via Internet should be secured by establishing a Stunnel connection to the 360T data center. Certificates have to be negotiated between the network teams of the client and 360T.

### <span id="page-10-4"></span>**4.1.3 Cross Connect**

Cross-connections inside the Equinix London LD4/LD5, Equinix Tokyo TY3 and Equinix New York NY4 data centers are available for enhanced network performance. In London we recommend to order cross-connects to both sites in order to have redundancy in case of failure on one site.

### <span id="page-10-5"></span>**4.1.4 N7**

Connectivity to 360T is available from Deutsche Börse's N7 global exchange network. N7 provides thousands of connections in 32 countries across Europe, North America and Asia. It is built to deliver speed, reliability and performance.

### <span id="page-10-6"></span>**4.1.5 Plain Internet**

A plain internet connection is only available on our testing environments.

## <span id="page-10-7"></span>**4.2 Connection and Firewall Configuration**

See appendix [9](#page-79-0) [Firewall Configuration](#page-79-0) for the necessary firewall rules to access this 360T service.

## <span id="page-11-0"></span>**4.3 FIX Engine Compatibility Testing**

We offer clients the ability to test against our FIX engine by connecting over the Internet. This allows compatibility issues to be discovered early in the process. The process for going live has three stages:

### 1. Initial Development & Testing

During this stage the client connection will be linked to the 360T development system.

### 2. Conformance Testing

Once a client is ready to go live, 360T will conduct a series of conformance tests with the client to certify the connection is compatible with the production environment. Conformance testing takes place over the test network connection.

### 3. Switch to Live System

Due to the need to make firewall changes and changes to the proxy servers the switch to the live system will take at least a week. This is because changes to these services can only be performed out of business hours, i.e., at the weekend.

## <span id="page-11-1"></span>**4.4 FIX Protocol Levels**

FIX is the communications protocol 360T and its clients will both have to support to fully communicate with each other. 360T systems are compliant with FIX version 4.4. In addition, some systems support transport layer FIXT.1.1 and application layer FIX.5.0.

Only the messages and fields relevant for the communication between 360T and its clients are included in this document. Each message described here lists 360T required fields and their supported values plus any fields that result in rejection. Additional fields will be ignored and unsupported field values will be rejected. The official FIX specification, available on [http://www.fixprotocol.org,](http://www.fixprotocol.org) should be consulted for in-depth descriptions.

### <span id="page-11-2"></span>**4.4.1 User Credentials**

360T will provide the values for the fields SenderCompID[49] and TargetCompID[56].[1](#page-11-5) 360T will also provide a password that must be used in the Logon message for the client to be authenticated if needed.

### <span id="page-11-3"></span>**4.4.2 FIX Session Reset**

For established connections, a FIX session reset is performed according to the schedule detailed in appendix [10](#page-80-0) [FIX Session Reset.](#page-80-0)

## <span id="page-11-4"></span>**4.5 Availability**

- The 360T Production environment is available between Sunday 6 pm America/New York and Friday 5 pm America/New York.
- Every day during the week the environment has a maintenance window between 5 pm and 6 pm America/New York during which the application may not be available.
- The non-availability of the 360T platform does not mean that the client will be disconnected from their FIX session(s) for the entire duration of the maintenance window. If the client remains connected during the maintenance window, the functionality of the API will not be available.

<span id="page-11-5"></span><sup>1</sup>Sender/TargetSubID[50/57] or Sender/TargetLocationID[142/143] will be defined if needed

## <span id="page-12-0"></span>**4.6 Trading Hours**

As defined in the MTF Rulebook: trading hours are between 7 AM Monday (New Zealand/Auckland) and 5 PM Friday (America/New York).

After 5 PM Friday (America/New York), the RFS MTF will not accept any new RFS requests.

## <span id="page-12-1"></span>**4.7 Timeout handling**

Should the client not receive an execution report - either rejected or confirmed - for an order that was submitted, it is the responsibility of the client to contact 360T support to clarify the status of the order. This includes cases when the fix connection goes down due to network connectivity or issues either on 360T or on client's side, when the client disconnects shortly after sending an order or any other cases that prevent 360T to send a execution report to the client at all or within the agreed timeout period.

## <span id="page-13-0"></span>**5 Utilization of the FIX Protocol**

## <span id="page-13-1"></span>**5.1 System Messages**

The following administrative system messages are supported as prescribed by the FIX standard. The supported message types are:

- Logon [A]
- Heartbeat [0] (interval configured to 30 seconds on 360T side)
- Test Request [1]
- Resend Request [2]
- Reject [3]
- Sequence Reset [4]
- Logout [5]

### <span id="page-13-2"></span>**5.2 Business Messages**

The business messages are used as prescribed by the FIX standard and interpreted in a way that all business relevant actions for RFS can be communicated. The supported message types are:

- News [B]
- QuoteRequest [R]
- QuoteRequestReject [AG]
- Business Message Reject [j]
- Quote [S]
- QuoteCancel [Z]
- NewOrderSingle [D]
- Execution Report [8]
- SecurityDefinitionRequest [c]
- SecurityDefinition [d]

### <span id="page-14-0"></span>**5.3 360T customization details**

### <span id="page-14-1"></span>**5.3.1 Side**

In the 360t platform sides are relative to base currency (currency 1). For swaps the side is relative to the base currency of the far leg. This hold true by default for all market taker sessions.

There is a possibility to configure a session to allow all Side tags in the communication - quote requests, quotes, orders and execution reports - to be relative to the notional currency. Please note that in such a case for swaps the side will still be relative to the far side. Also, the side of the product in the 360T platform will still be shown as relative to currency 1. This special configuration aims better interoperability. Please inform 360T if you need this configured during the setup and testing process.

### <span id="page-14-2"></span>**5.3.2 Quote Cancels and Business Rejections**

360T will send a BusinessMessageReject message whenever there is an issue with the previous request, when the platform is unavailable or if there is an issue with the business setup in terms of rights or quote routing.

QuoteCancel messages are sent when a specific quote is canceled, when the quote request expires or as a confirmation of a quote cancel. When a single quote is canceled, the quoteId would be defined in the message. When the whole quote request is canceled, quoteId will be 0.

360T will confirm a QuoteCancel message sent by the client with a QuoteCancel message with quoteId=0. When 360T initiates a quote cancel for a single quote or for the whole stream (upon quote request expiration), the client must not confirm by sending back a QuoteCancel. Doing this will result in a business rejection.

### <span id="page-14-3"></span>**5.4 General Workflow**

The general workflow for trading through the FIX API is shown in the following diagram

5.4. General Workflow Chapter 5: Utilization of the FIX Protocol

<span id="page-15-0"></span>![](_page_15_Figure_4.jpeg)

Figure 5.1: Basic RFS Message Flow

## <span id="page-16-0"></span>**6 System Messages**

## <span id="page-16-1"></span>**6.1 Notes on important details**

- The customer connects to 360T.
- All timestamps used use time in UTC.

## <span id="page-16-2"></span>**6.2 Message Header**

Each message is preceded by a standard header. It identifies the message type, length, destination, sequence number, origination point and time.

<span id="page-16-4"></span>

| Tag  | Name          | Type         | Req. | Description                                                                                    |
|------|---------------|--------------|------|------------------------------------------------------------------------------------------------|
| 8    | BeginString   | String       | Y    | FIX.4.4 (must be first field in message)                                                       |
| 9    | BodyLength    | Length       | Y    | must be second field in message                                                                |
| 35   | MsgType       | String       | Y    | Shown in individual message specifications in this<br>document, must be third field in message |
| 34   | MsgSeqNum     | SeqNum       | Y    | Message Sequence Number (incremented on 360T<br>side)                                          |
| 49   | SenderCompID  | String       | Y    | Set to an agreed value                                                                         |
| 50   | SenderSubID   | String       | Y    | Set to an agreed value. Used to identify the specific<br>message originator.                   |
| 52   | SendingTime   | UTCTimestamp | Y    | Time of message transmission                                                                   |
| 56   | TargetCompID  | String       | Y    | Set to an agreed value                                                                         |
| 1129 | CstmApplVerID | String       | N    | specifies internal API version, defaults to '1'                                                |

Table 6.1: Standard Header for FIX Messages

## <span id="page-16-3"></span>**6.3 Message Footer**

This footer will be used in all messages sent between 360T and the customer system.

<span id="page-16-5"></span>

| Tag | Name     | Type   | Req. | Description                                                                                                               |
|-----|----------|--------|------|---------------------------------------------------------------------------------------------------------------------------|
| 10  | CheckSum | String | Y    | Standard FIX value. Always defined as three charac<br>ters (unencrypted). Must be the last field in every FIX<br>message. |

Table 6.2: Standard Footer for FIX Messages

## <span id="page-17-0"></span>**6.4 Logon [A]**

The customer starts the communication by sending a Login message. 360T checks the supplied credentials and answers with a Logon reply if successful.

<span id="page-17-3"></span>

| Tag                             | Name                            | Type    | Req.                                                                                                     | Description              |
|---------------------------------|---------------------------------|---------|----------------------------------------------------------------------------------------------------------|--------------------------|
|                                 | <messageheader></messageheader> |         | Y                                                                                                        | MsgType <35> = A         |
| 98                              | EncryptMethod                   | int     | Y                                                                                                        | 0 = 'always unencrypted' |
| 108                             | HeartBtInt                      | int     | Y                                                                                                        | should be set to 30      |
| 141                             | ResetSeqNumFlag                 | Boolean | Y                                                                                                        | should be set to 'Y'     |
| 554<br>Password<br>String       |                                 | Y       | Will be provided by 360T.<br>Note: Without transport-level-encryption, only mini<br>mal security exists. |                          |
| <messagefooter></messagefooter> |                                 |         | Y                                                                                                        |                          |

Table 6.3: Logon message

## <span id="page-17-1"></span>**6.5 Heartbeat [0]**

The Heartbeat monitors the status of the communication link and identifies when the last of a string of messages was not received.

When either end of a FIX connection has not sent any data for HeartBtInt <108> seconds, it will transmit a Heartbeat message. When either end of the connection has not received any data for (HeartBtInt <108> + "some reasonable transmission time") seconds, it will transmit a Test Request message. If there is still no Heartbeat message received after (HeartBtInt <108> + "some reasonable transmission time") seconds then the connection is considered lost.

<span id="page-17-4"></span>

| Tag                             | Name                            | Type | Req.                                                                      | Description      |
|---------------------------------|---------------------------------|------|---------------------------------------------------------------------------|------------------|
|                                 | <messageheader></messageheader> |      | Y                                                                         | MsgType <35> = 0 |
| 112<br>TestReqID<br>String      |                                 | N    | Required if the Heartbeat is a response to a TestRe<br>quest [1] message. |                  |
| <messagefooter></messagefooter> |                                 |      | Y                                                                         |                  |

Table 6.4: Heartbeat message

## <span id="page-17-2"></span>**6.6 TestRequest [1]**

The Test Request message forces a heartbeat from the opposing application. The Test Request message checks sequence numbers or verifies communication line status. The opposite application responds to the Test Request with a Heartbeat containing the TestReqID <112>.

<span id="page-17-5"></span>

| Tag | Name                            | Type | Req. | Description      |                        |
|-----|---------------------------------|------|------|------------------|------------------------|
|     | <messageheader></messageheader> |      | Y    | MsgType <35> = 1 |                        |
|     |                                 |      |      |                  | Continued on next page |

6.7. ResendRequest [2] Chapter 6: System Messages

| Tag                             | Name      | Type   | Req. | Description                                |
|---------------------------------|-----------|--------|------|--------------------------------------------|
| 112                             | TestReqID | String | Y    | Identifier that must be used in the reply. |
| <messagefooter></messagefooter> |           |        | Y    |                                            |

Table 6.5: TestRequest message

## <span id="page-18-0"></span>**6.7 ResendRequest [2]**

The resend request is sent by the receiving application to initiate the retransmission of messages. This function is utilized if a sequence number gap is detected, if the receiving application lost a message, or as a function of the initialization process.

<span id="page-18-2"></span>

| Tag                             | Name       | Type | Req.             | Description |
|---------------------------------|------------|------|------------------|-------------|
| <messageheader></messageheader> |            | Y    | MsgType <35> = 2 |             |
| 7                               | BeginSeqNo | int  | Y                |             |
| 16<br>EndSeqNo<br>int           |            | Y    |                  |             |
| <messagefooter></messagefooter> |            |      | Y                |             |

Table 6.6: ResendRequest message

- To request a single message: BeginSeqNo <7> = EndSeqNo <16>
- To request a range of messages: BeginSeqNo <7> = first message of range, EndSeqNo <16> = last message of range
- To request all messages subsequent to a particular message: BeginSeqNo <7> = first message of range, EndSeqNo <16> = 0 (represents infinity) .

## <span id="page-18-1"></span>**6.8 Reject [3]**

The Reject message is issued when a message is received but cannot be properly processed due to a session-level rule violation. An example of when a reject may be appropriate would be the receipt of a message with invalid basic data which successfully passes de-encryption, CheckSum <10> and BodyLength <9> checks.

<span id="page-18-3"></span>

| Tag                             | Name       | Type   | Req.             | Description                                      |
|---------------------------------|------------|--------|------------------|--------------------------------------------------|
| <messageheader></messageheader> |            | Y      | MsgType <35> = 3 |                                                  |
| 45                              | RefSeqNum  | int    | Y                | MsgSeqNum <34> of the rejected message           |
| 58                              | Text       | String | N                | Reason why the message can't be processed        |
| 371                             | RefTagID   | int    | N                | The tag number of the FIX field being referenced |
| 372                             | RefMsgType | String | N                | The MsgType of the FIX message being referenced  |
| Continued on next page          |            |        |                  |                                                  |

6.9. SequenceReset [4] Chapter 6: System Messages

| Tag                             | Name                    | Type | Req. | Description                                               |
|---------------------------------|-------------------------|------|------|-----------------------------------------------------------|
| 373                             | SessionRejectReason int |      | N    | Session-level reject reason. Values:                      |
|                                 |                         |      |      | • '0' = Invalid tag number                                |
|                                 |                         |      |      | • '1' = Required tag missing                              |
|                                 |                         |      |      | • '2' = Tag not defined for this message type             |
|                                 |                         |      |      | • '3' = Undefined Tag                                     |
|                                 |                         |      |      | • '4' = Tag specified without a value                     |
|                                 |                         |      |      | • '5' = Value is incorrect (out of range) for this<br>tag |
|                                 |                         |      |      | • '6' = Incorrect data format for value                   |
|                                 |                         |      |      | • '7' = Decryption problem                                |
|                                 |                         |      |      | • '8' = Signature problem                                 |
|                                 |                         |      |      | • '9' = CompID problem                                    |
|                                 |                         |      |      | • '10' = SendingTime accuracy problem                     |
|                                 |                         |      |      | • '11' = Invalid MsgType                                  |
|                                 |                         |      |      | • '12' = XML Validation error                             |
|                                 |                         |      |      | • '13' = Tag appears more than once                       |
|                                 |                         |      |      | • '14' = Tag specified out of required order              |
|                                 |                         |      |      | • '15' = Repeating group fields out of order              |
|                                 |                         |      |      | • '16' = Incorrect NumInGroup count                       |
|                                 |                         |      |      | • '17' = Non "Data" value includes field delim<br>iter    |
|                                 |                         |      |      | • '99' = Other                                            |
| <messagefooter></messagefooter> |                         | Y    |      |                                                           |

Table 6.7: Reject message

## <span id="page-19-0"></span>**6.9 SequenceReset [4]**

<span id="page-19-1"></span>

| Tag | Name                            | Type | Req. | Description      |                        |
|-----|---------------------------------|------|------|------------------|------------------------|
|     | <messageheader></messageheader> |      | Y    | MsgType <35> = 4 |                        |
|     |                                 |      |      |                  | Continued on next page |

6.10. Logout [5] Chapter 6: System Messages

| Tag                             | Name     | Type | Req. | Description                                                         |
|---------------------------------|----------|------|------|---------------------------------------------------------------------|
| 36                              | NewSeqNo | int  | Y    | Sequence number which is expected to be sent in the<br>next message |
| <messagefooter></messagefooter> |          | Y    |      |                                                                     |

Table 6.8: SequenceReset message

## <span id="page-20-0"></span>**6.10 Logout [5]**

The Logout message initiates or confirms the termination of a FIX session. Disconnection without the exchange of Logout messages should be interpreted as an abnormal condition.

<span id="page-20-1"></span>

| Tag                             | Name           | Type | Req.             | Description                                                                                    |
|---------------------------------|----------------|------|------------------|------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                | Y    | MsgType <35> = 5 |                                                                                                |
| 58                              | Text<br>String |      | N                | This field is used in confirmations only to specify why<br>the message could not be processed. |
| <messagefooter></messagefooter> |                |      | Y                |                                                                                                |

Table 6.9: Logout message

## <span id="page-21-0"></span>**7 Business Messages**

## <span id="page-21-1"></span>**7.1 News [B]**

### (360T → customer)

This News message is initialized by customer to request for available providers (aka Bank Basket). The request News message sent from customer should contain the list of requesters who are interested in receiving the Bank Baskets. All requesters are listed in the LinesOfText group.

360T sends a News message back to the customer to inform about the list of available providers. All available providers are listed in the LinesOfText group and the NoRoutingIDs contains the target requester. For each supported product and each requester, an individual News message is sent (e.g. if Spots, Forwards and Swaps are supported, 360T will send three different messages for each requester). The messages are sent to the customer only when requested.

<span id="page-21-2"></span>

| Tag   | Name                            | Type       | Req. | Description                                                                                                                                                                                    |
|-------|---------------------------------|------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|       | <messageheader></messageheader> |            | Y    | MsgType <35> = B                                                                                                                                                                               |
| 148   | Headline                        | String     | Y    | Is set to the name of the product if sent by 360T. Pos<br>sible values are 'Fx Spot', 'Fx Forward', 'Fx Swap',<br>'NDF', 'NDS' and 'Block trade'.<br>Is set to any string if sent by customer. |
| 33    | LinesOfText                     | NumInGroup | Y    | Number of parties delivered in the group.                                                                                                                                                      |
| → 58  | Text                            | String     | Y    | Short name of the available provider if sent by 360T.<br>Short name of the requester if sent by customer.                                                                                      |
| 215   | NoRoutingIDs                    | NumInGroup | N    | Number of News Requestors delivered in the group.<br>For the moment only support 1 requestor. Used only<br>in the message sent by 360T.                                                        |
| → 216 | RoutingType                     | int        | N    | Indicate the type of Routing.<br>Possible values:<br>• '1' = Target Firm (The value of the RoutingID<br>is sent to one receiver only)                                                          |
| → 217 | RoutingID                       | String     | N    | Short name of the requestor who expects to receive<br>available providers.                                                                                                                     |
|       | <messagefooter></messagefooter> |            | Y    |                                                                                                                                                                                                |

Table 7.1: News message

## <span id="page-22-0"></span>**7.2 QuoteRequest [R]**

### (Customer → 360T)

The QuoteRequest message is sent by the customer to the 360T. Normally it requests the banks to start sending tradeable quotes for the instrument specified in this message.

QuoteRequest is also used to request market data for a specific currency pair and product. Those requests are distinguished by the QuoteType<537> field which can be either '1' to request tradable quotes or '0' to request market data.

Note: The default number of parallel requests is 20. Please contact 360T support if you require a higher number.

<span id="page-22-1"></span>

| Tag    | Name                            | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                       |
|--------|---------------------------------|--------------|------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|        | <messageheader></messageheader> |              | Y    | MsgType <35> = R                                                                                                                                                                                                                                                                                                                                                                  |
| 131    | QuoteReqID                      | String       | Y    | Unique identifier for this request. This field must<br>not be longer than 50 characters.                                                                                                                                                                                                                                                                                          |
| 146    | NoRelatedSym                    | NumInGroup   | Y    | Number of related symbols in request. Value must<br>always be '1'                                                                                                                                                                                                                                                                                                                 |
| → 55   | Symbol                          | String       | Y    | Defines the currency pair for FX products, speci<br>fied using two 3-letter ISO 4217 codes separated<br>by a slash ('/') delimiter.<br>For Base Metals,<br>the Symbol<55> field con                                                                                                                                                                                               |
|        |                                 |              |      | tains the chemical element symbol of the metal<br>(e.g. "AL" for aluminium, "CU" for copper etc.),<br>apart from "Iron Ore 100 ton" and "Iron Ore 100<br>ton", which use "FE1" and "FE5" respectively.                                                                                                                                                                            |
|        |                                 |              |      | For<br>Money<br>Market<br>(MM)<br>products,<br>the<br>Symbol<55> field defines the currency, specified<br>using one 3-letter ISO 4217 code.                                                                                                                                                                                                                                       |
| → 541  | MaturityDate                    | LocalMktDate | C    | Defines the Fixing Date for an NDF (or the near<br>leg for an NDS).                                                                                                                                                                                                                                                                                                               |
|        |                                 |              |      | For<br>Loan/Deposit<br>products,<br>the<br>Maturity<br>Date<541> field defines the end of the loan or<br>deposit's term (required for this product).                                                                                                                                                                                                                              |
| → 7541 | MaturityDate2                   | LocalMktDate | N    | Defines the far leg Fixing Date for an NDS.                                                                                                                                                                                                                                                                                                                                       |
| → 106  | Issuer                          | String       | N    | To be populated with the name of a provider that<br>is in the bank basket and from whom quotes are<br>requested.<br>If multiple providers should be in<br>cluded, this field must be a comma-separated list<br>of providers.<br>If not present, all providers from the bank basket<br>are included.<br>When market data is requested (QuoteType = '0'),<br>this field is ignored. |
|        |                                 |              |      | Continued on next page                                                                                                                                                                                                                                                                                                                                                            |

| Tag   | Name      | Type | Req. | Description                                                                                                                                                          |
|-------|-----------|------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| → 537 | QuoteType | int  | Y    | Identifies the type of quote.<br>Possible values:<br>• '0' = indicative (request market data. Trad<br>ing will not be possible on those quotes)<br>• '1' = tradeable |
|       |           |      |      | Continued on next page                                                                                                                                               |

| Tag  | Name | Type | Req. | Description                                                                                                                                                                                                                                |
|------|------|------|------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| → 54 | Side | char | C    | Defines if the Market Taker is intending to buy<br>or sell the given symbol for FX and Base Metals<br>products.                                                                                                                            |
|      |      |      |      | • The Side<54> field defines the side of the<br>first currency in the Symbol<55> field (the<br>base currency) and not of the notional cur<br>rency.                                                                                        |
|      |      |      |      | • For<br>FX<br>Swap<br>and<br>NDS<br>products,<br>the<br>Side<54> field indicates the side of the far<br>leg.                                                                                                                              |
|      |      |      |      | • If the Side<54> field is not present, then the<br>request will be for a two-sided quote.                                                                                                                                                 |
|      |      |      |      | For Money Market (MM) products, the Side<54><br>field is mandatory and defines if the client wants<br>to lend or borrow money.                                                                                                             |
|      |      |      |      | Possible values:                                                                                                                                                                                                                           |
|      |      |      |      | • '1' = Buy                                                                                                                                                                                                                                |
|      |      |      |      | • '2' = Sell                                                                                                                                                                                                                               |
|      |      |      |      | • 'F' = Lend (MM Deposit only)                                                                                                                                                                                                             |
|      |      |      |      | • 'G' = Borrow (MM Loan only)                                                                                                                                                                                                              |
|      |      |      |      | • 'B' = As Defined (Block Trades only)                                                                                                                                                                                                     |
|      |      |      |      | Note:<br>For FX and NDF Block Trades, the Side<54> field<br>is mandatory and must be set to 'B' (As Defined)<br>or its value must match the overall side of the net<br>ted leg quantities (defined using LegSide<624><br>and LegQty<687>). |
|      |      |      |      | • When 'B' (As Defined) is used, the top<br>level side of the block trade will be auto<br>matically set to the overall side of the net<br>ted legs, or to sell if the netted quantity of<br>the legs is zero.                              |
|      |      |      |      | • If required, the customer can choose the<br>top-level side ('1' = Buy or '2' = Sell) for<br>zero-netted block trades by providing the<br>desired side in the Side<54> field.                                                             |
|      |      |      |      | Continued on next page                                                                                                                                                                                                                     |

| Tag     | Name            | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
|---------|-----------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| → 38    | OrderQty        | Qty          | Y    | Order quantity (for swaps of near leg). If market<br>data is requested, send '0'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| → 64    | SettlDate       | LocalMktDate | Y    | Trade settlement date. For swaps this is the near<br>leg settlement date. For block trades this should<br>match the RefSpotDate<7070>.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| → 14101 | SplitSettlDate  | LocalMktDate | N    | Split settlement date. For swaps this is the near leg split<br>settlement date. This field refers to the second currency.<br>The SettlDate<64> field (which refers to the first cur<br>rency) must be provided if this tag is used.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| → 193   | SettlDate2      | LocalMktDate | N    | Far settlement date. If present, this must be inter<br>preted as a swap. In the 360T platform the tenor<br>on the far leg is relative to the near leg.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| → 14102 | SplitSettlDate2 | LocalMktDate | N    | Far split settlement date. If present, this must be inter<br>preted as a swap. This field refers to the second cur<br>rency. The SettlDate2<193> field (which refers to the<br>first currency) must be provided if this tag is used.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| → 192   | OrderQty2       | Qty          | N    | Order quantity of far leg for swaps. Mandatory if<br>tradeable quotes for swaps are requested.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| → 15    | Currency        | Currency     | N    | Currency that OrderQty is expressed in. Manda<br>tory if tradeable quotes are requested.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| → 1     | Account         | String       | Y    | If the sender is trading for another legal entity<br>(Trade-As), this field defines the name of this en<br>tity. Otherwise this is the same value as in Sender<br>CompID                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| → 126   | ExpireTime      | UTCTimestamp | C    | The time when this request will expire.<br>• Mandatory field if the QuoteRequest is for<br>tradeable quotes (QuoteType<537> = '1').<br>Expiry<br>times<br>for<br>tradeable<br>quote<br>re<br>quests are limited depending on the product<br>(Spot/Forward/Swap/NDF).<br>This<br>limit<br>is<br>by default 1 minute for Spot and 5 minutes<br>for all other products, but can be configured<br>per customer and product.<br>• Optional field if the QuoteRequest is for<br>indicative quotes (QuoteType<537> = '0').<br>If<br>not<br>provided,<br>the<br>value<br>defaults<br>to<br>the system/account defaults.<br>The expiry<br>time of market data requests is not restricted<br>(long running requests are permitted). |

| 7.2. OuoteRequest [R] |  |
|-----------------------|--|

| Tag                                                                           | Name                                                                          | Type         | Req. | Description                                                                                                                                  |  |
|-------------------------------------------------------------------------------|-------------------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------|--|
| → 78                                                                          | NoAllocs                                                                      | NumInGroup   | N    | Repeating group of allocation instructions. In case<br>of swaps, only one allocation per leg is needed as<br>uneven swaps are not supported. |  |
| → → 79                                                                        | AllocAccount                                                                  | String       | Y    | Entity for which an amount gets allocated                                                                                                    |  |
| → → 80                                                                        | AllocQty                                                                      | Qty          | Y    | Amount allocated. Can be negative to indicate in<br>verse to the side given in field 54.                                                     |  |
| →→ 539                                                                        | NoNestedPartyIDs                                                              | NumInGroup   | N    | Repeating group for MiFID properties of the allocation.                                                                                      |  |
|                                                                               | NestedPartyID Repeating Group for Investment Decision within Firm (Algorithm) |              |      |                                                                                                                                              |  |
| →→→ 524                                                                       | NestedPartyID                                                                 | String       | N    | Algorithm-Identifier                                                                                                                         |  |
| →→→ 525                                                                       | NestedPartyIDSource                                                           | char         | N    | 'D': Proprietary custom code.                                                                                                                |  |
| →→→ 538                                                                       | NestedPartyRole                                                               | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |
| →→→ 2384                                                                      | NestedParty<br>RoleQualifier                                                  | int          | N    | '22' = Algorithm                                                                                                                             |  |
|                                                                               | NestedPartyID Repeating Group for Investment Decision within Firm (Person)    |              |      |                                                                                                                                              |  |
| →→→ 524                                                                       | NestedPartyID                                                                 | String       | N    | 360T Username                                                                                                                                |  |
| →→→ 525                                                                       | NestedPartyIDSource                                                           | char         | N    | 'P': Short code identifier                                                                                                                   |  |
| →→→ 538                                                                       | NestedPartyRole                                                               | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |
| →→→ 2384                                                                      | NestedParty<br>RoleQualifier                                                  | int          | N    | '24' = Natural Person                                                                                                                        |  |
| → 555                                                                         | NoLegs                                                                        | NumInGroup   | N    | Repeating group of leg instructions. Used only for non<br>swap multi-leg instruments.                                                        |  |
| →→ 600                                                                        | LegSymbol                                                                     | String       | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2                                                                               |  |
| →→ 611                                                                        | LegMaturityDate                                                               | LocalMktDate | N    | Defines the Fixing Date for an NDF Block trade leg.                                                                                          |  |
| →→ 624                                                                        | LegSide                                                                       | char         | Y    | Defines if the customer wants to buy or sell the given<br>symbol on this leg. Possible values:                                               |  |
|                                                                               |                                                                               |              |      | • '1' = Buy                                                                                                                                  |  |
|                                                                               |                                                                               |              |      | • '2' = Sell                                                                                                                                 |  |
| →→ 687                                                                        | LegQty                                                                        | char         | Y    | The notional amount to be dealt on one leg                                                                                                   |  |
| →→ 670                                                                        | NoLegAllocs                                                                   | NumInGroup   | Y    | Repeating group of allocations per leg. Only one is ac<br>cepted per leg.                                                                    |  |
| →→→ 671                                                                       | LegAllocAccount                                                               | String       | Y    | Entity for which an amount gets allocated                                                                                                    |  |
| →→→ 673                                                                       | LegAllocQty                                                                   | Qty          | Y    | Amount allocated.                                                                                                                            |  |
| →→→ 539                                                                       | NoNestedPartyIDs                                                              | NumInGroup   | N    | Repeating group for MiFID properties of the leg level<br>allocation.                                                                         |  |
| NestedPartyID Repeating Group for Investment Decision within Firm (Algorithm) |                                                                               |              |      |                                                                                                                                              |  |
| →→→→ 524                                                                      | NestedPartyID                                                                 | String       | N    | Algorithm-Identifier                                                                                                                         |  |
| →→→→ 525                                                                      | NestedPartyIDSource                                                           | char         | N    | 'D': Proprietary custom code.                                                                                                                |  |
| Continued on next page                                                        |                                                                               |              |      |                                                                                                                                              |  |

| Tag       | Name                                                                       | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                |
|-----------|----------------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| →→→→ 538  | NestedPartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                                                                                                                                                                                                                                                          |
| →→→→ 2384 | NestedParty<br>RoleQualifier                                               | int          | N    | '22' = Algorithm                                                                                                                                                                                                                                                                                                                                                           |
|           | NestedPartyID Repeating Group for Investment Decision within Firm (Person) |              |      |                                                                                                                                                                                                                                                                                                                                                                            |
| →→→→ 524  | NestedPartyID                                                              | String       | N    | 360T Username                                                                                                                                                                                                                                                                                                                                                              |
| →→→→ 525  | NestedPartyIDSource                                                        | char         | N    | 'P': Short code identifier                                                                                                                                                                                                                                                                                                                                                 |
| →→→→ 538  | NestedPartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                                                                                                                                                                                                                                                          |
| →→→→ 2384 | NestedParty<br>RoleQualifier                                               | int          | N    | '24' = Natural Person                                                                                                                                                                                                                                                                                                                                                      |
| →→ 654    | LegRefID                                                                   | String       | Y    | Unique reference id of the leg                                                                                                                                                                                                                                                                                                                                             |
| →→ 588    | LegSettlDate                                                               | LocalMktDate | Y    | Settlement date of one leg                                                                                                                                                                                                                                                                                                                                                 |
| 453       | NoPartyIDs                                                                 | NumInGroup   | N    | Repeating group containing the participants of the<br>trade. As we have one provider and one requester this<br>will at least 2. If requested we can also send the en<br>tity for which the trade is exported with PartyRole 'In<br>terested Party'. This can be useful in a ITEX scenario<br>where both sides of an internal deal are exported to the<br>same FIX session. |
| → 448     | PartyID                                                                    | String       | N    | 360T-Individualname or for ALGOs a custom identi<br>fier of the Executing Trader and Invenstment Decision<br>Maker                                                                                                                                                                                                                                                         |
| → 447     | PartyIDSource                                                              | char         | N    | It can contain one of the following values:<br>• 'D': Proprietary custom code<br>• 'P': Short code identifier                                                                                                                                                                                                                                                              |
| → 452     | PartyRole                                                                  | int          | N    | Possible values:<br>• '4' = 'Clearing Firm'<br>• '12' = 'Executing Trader'<br>• '122' = 'Investment decision maker'<br>• '63' = 'Systematic Internaliser (SI)'                                                                                                                                                                                                             |
| → 2376    | PartyRoleQualifier                                                         | int          | N    | '24' = Natural Person                                                                                                                                                                                                                                                                                                                                                      |
|           | PartyID Repeating Group for Clearing Firm                                  |              |      |                                                                                                                                                                                                                                                                                                                                                                            |
| → 448     | PartyID                                                                    | String       | N    | Which Clearing house (DCO) this trade will be cleared<br>at.                                                                                                                                                                                                                                                                                                               |
| → 447     | PartyIDSource                                                              | char         | N    | 'D': Proprietary custom code.                                                                                                                                                                                                                                                                                                                                              |
| → 452     | PartyRole                                                                  | int          | N    | '4' = Clearing Firm                                                                                                                                                                                                                                                                                                                                                        |
|           | PartyID Repeating Group for Investment Decision within Firm (Algorithm)    |              |      |                                                                                                                                                                                                                                                                                                                                                                            |
| → 448     | PartyID                                                                    | String       | N    | Algorithm-Identifier                                                                                                                                                                                                                                                                                                                                                       |
| → 447     | PartyIDSource                                                              | char         | N    | 'D': Proprietary custom code.                                                                                                                                                                                                                                                                                                                                              |
|           |                                                                            |              |      | Continued on next page                                                                                                                                                                                                                                                                                                                                                     |

©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 28

| Tag                                                        | Name                                                                 | Type         | Req. | Description                                                                                                                                  |  |
|------------------------------------------------------------|----------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------|--|
| → 452                                                      | PartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |
| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '22' = Algorithm                                                                                                                             |  |
|                                                            | PartyID Repeating Group for Investment Decision within Firm (Person) |              |      |                                                                                                                                              |  |
| → 448                                                      | PartyID                                                              | String       | N    | 360T Username                                                                                                                                |  |
| → 447                                                      | PartyIDSource                                                        | char         | N    | 'P': Short code identifier                                                                                                                   |  |
| → 452                                                      | PartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |
| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '24' = Natural Person                                                                                                                        |  |
|                                                            | PartyID Repeating Group for Execution within Firm (Algorithm)        |              |      |                                                                                                                                              |  |
| → 448                                                      | PartyID                                                              | String       | N    | Algorithm-Identifier                                                                                                                         |  |
| → 447                                                      | PartyIDSource                                                        | char         | N    | 'D': Proprietary custom code.                                                                                                                |  |
| → 452                                                      | PartyRole                                                            | int          | N    | '12' = Executing Trader                                                                                                                      |  |
| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '22' = Algorithm                                                                                                                             |  |
| PartyID Repeating Group for Execution within Firm (Person) |                                                                      |              |      |                                                                                                                                              |  |
| → 448                                                      | PartyID                                                              | String       | N    | 360T Username                                                                                                                                |  |
| → 447                                                      | PartyIDSource                                                        | char         | N    | 'P': Short code identifier                                                                                                                   |  |
| → 452                                                      | PartyRole                                                            | int          | N    | '12' = Executing Trader                                                                                                                      |  |
| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '24' = Natural Person                                                                                                                        |  |
|                                                            | PartyID Repeating Group for SI-Requests                              |              |      |                                                                                                                                              |  |
| → 448                                                      | PartyID                                                              | String       | N    | MIC of Systematic Internaliser / Requester                                                                                                   |  |
| → 447                                                      | PartyIDSource                                                        | char         | N    | 'G': MIC                                                                                                                                     |  |
| → 452                                                      | PartyRole                                                            | int          | N    | '63' = Systematic Internaliser (SI)                                                                                                          |  |
| 2593                                                       | NoOrderAttributes                                                    | NumInGroup   | N    | OrderAttributes for Commodity-Derivatives                                                                                                    |  |
| → 2594                                                     | OrderAttributeType                                                   | int          | N    | '3' = Risk reduction order                                                                                                                   |  |
| → 2595                                                     | OrderAttributeValue                                                  | String       | N    | Possible values:                                                                                                                             |  |
|                                                            |                                                                      |              |      | • 'Y' = Risk Decreasing                                                                                                                      |  |
|                                                            |                                                                      |              |      | • 'N' = Risk Increasing                                                                                                                      |  |
| 9515                                                       | OptionDate                                                           | LocalMktDate | N    | Defines the end date of the Time Option (for FX Time<br>Options).                                                                            |  |
| 7070                                                       | RefSpotDate                                                          | LocalMktDate | N    | Defines the spot date in the 360T financial calender.<br>This value is used to clarify if both sides have the same<br>definition for a spot. |  |
|                                                            |                                                                      |              |      | Continued on next page                                                                                                                       |  |

| Tag    | Name                            | Type       | Req. | Description                                                                                |
|--------|---------------------------------|------------|------|--------------------------------------------------------------------------------------------|
| 7071   | ProductType                     | String     | Y    | Custom field defining the requested product.                                               |
|        |                                 |            |      | Possible values:                                                                           |
|        |                                 |            |      | • 'FX-STD': Spot, Forward, Swap, NDF and NDS                                               |
|        |                                 |            |      | • 'FX-BT': Block trade                                                                     |
|        |                                 |            |      | • 'Metals Outright': Metals Outright                                                       |
|        |                                 |            |      | • 'Metals Spread': Metals Spread                                                           |
|        |                                 |            |      | • 'Metals Quarterly Strip': Metals Quarterly Strip                                         |
|        |                                 |            |      | • 'MM': Loan/Deposit                                                                       |
| 7611   | ExecutionVenueType              | String     | N    | Used to determine the trading venue on which the re<br>quest will be processed.            |
|        |                                 |            |      | • '2' = OFF facility / OTC (default)                                                       |
|        |                                 |            |      | • '3' = MTF                                                                                |
|        |                                 |            |      | • '7' = SG RMO                                                                             |
| 29     | LastCapacity                    | int        | N    | Tradingcapacity - Values are:                                                              |
|        |                                 |            |      | • '1' = AOTC - Any other capacity                                                          |
|        |                                 |            |      | • '3' = MTCH - Matched principal                                                           |
|        |                                 |            |      | • '4' = DEAL - Dealing on own account                                                      |
| 7072   | DayCount                        | String     | C    | Defines the day count convention of the interest rate for                                  |
|        |                                 |            |      | Loan/Deposit requests (required field for this product).                                   |
|        |                                 |            |      | Possible values:                                                                           |
|        |                                 |            |      | • 1/1                                                                                      |
|        |                                 |            |      | • ACT/365F                                                                                 |
|        |                                 |            |      | • ACT/360                                                                                  |
|        |                                 |            |      | • ACT/ACT                                                                                  |
|        |                                 |            |      | • 30/360                                                                                   |
|        |                                 |            |      | • 30E/360                                                                                  |
|        |                                 |            |      | • BUS/252                                                                                  |
| 7075   | FixingReference                 | String     | N    | Fixing reference for NDF, NDS or NDF block (if prod<br>uct supported in this API version). |
| 7546   | NoCustomFields                  | NumInGroup | N    | Number of custom fields (group).                                                           |
| → 7547 | CustomFieldName                 | String     | C    | Name of this custom field (required when custom fields<br>are used).                       |
| → 7548 | CustomFieldValue                | String     | C    | Value of this custom field (required when custom fields<br>are used).                      |
|        | <messagefooter></messagefooter> |            | Y    |                                                                                            |
|        |                                 |            |      | Continued on next page                                                                     |

©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 30

| Tag | Name | Type | Req. | Description |
|-----|------|------|------|-------------|
|     |      |      |      |             |

Table 7.2: QuoteRequest message

### 7.3. QuoteRequestReject [AG] Chapter 7: Business Messages

## <span id="page-31-0"></span>**7.3 QuoteRequestReject [AG]**

### (360T → customer)

The QuoteRequestReject message is sent by the 360T to the customer to indicate that a QuoteRequest was rejected. In case of message-level issues and certain field validation error a BusinessMessageReject [j] may be sent instead.

<span id="page-31-1"></span>

| Tag                             | Name                         | Type       | Req. | Description                                                                         |
|---------------------------------|------------------------------|------------|------|-------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                              |            | Y    | MsgType <35> = AG                                                                   |
| 131                             | QuoteReqID                   | String     | Y    | Unique identifier for the rejected request                                          |
| 658                             | QuoteRequest<br>RejectReason | int        | Y    | Reason why the quote request from 360T was re<br>jected. Will always be '99': Other |
| 146                             | NoRelatedSym                 | NumInGroup | Y    | Number of related symbols. Always '1'                                               |
| →55                             | Symbol                       | String     | Y    | Contains the requested symbol.                                                      |
| →537                            | QuoteType                    | int        | Y    | Identifies the requested type of quote.                                             |
| →15                             | Currency                     | String     | N    | Contains the requested notional currency.                                           |
| 58                              | Text                         | String     | Y    | Reason why the request was rejected as text                                         |
| <messagefooter></messagefooter> |                              |            | Y    |                                                                                     |

Table 7.3: QuoteRequestReject message

## <span id="page-32-0"></span>**7.4 BusinessMessageReject [j]**

### (360T → customer)

A Business Message Reject can be sent from 360T to the customer to inform that a received message was semantically not correct and therefore couldn't get processed.

<span id="page-32-1"></span>

| Tag                             | Name                       | Type   | Req.             | Description                                                                                                          |
|---------------------------------|----------------------------|--------|------------------|----------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                            | Y      | MsgType <35> = j |                                                                                                                      |
| 45                              | RefSeqNum                  | SeqNum | Y                | MsgSeqNum of rejected message.                                                                                       |
| 372                             | RefMsgType                 | String | Y                | The MsgType of the FIX message being referenced.                                                                     |
| 379                             | BusinessRejectRefID String |        | N                | The value of the business-level 'ID' field on the mes<br>sage being referenced.                                      |
| 380                             | BusinessReject<br>Reason   | int    | Y                | Code to identify reason for a BusinessMessageReject<br>message:<br>• '0' = Other<br>• '3' = Unsupported message type |
|                                 |                            |        |                  | • '5' = Conditionally required field missing                                                                         |
| 58                              | Text                       | String | N                | Reason why business message can't be processed                                                                       |
| <messagefooter></messagefooter> |                            |        | Y                |                                                                                                                      |

Table 7.4: BusinessMessageReject message

## <span id="page-33-0"></span>**7.5 Quote [S]**

(360T → customer)

This message is sent from 360T to the customer to answer a quote request

<span id="page-33-1"></span>

| Tag   | Name                            | Type         | Req. | Description                                                                                                                                                            |
|-------|---------------------------------|--------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|       | <messageheader></messageheader> |              | Y    | MsgType <35> = S                                                                                                                                                       |
| 131   | QuoteReqID                      | String       | Y    | Unique ID which refers back to the customer's re<br>quest                                                                                                              |
| 117   | QuoteID                         | String       | Y    | Unique ID set by the quote's originator for each<br>quote. Its length would be not more than 15 char<br>acters longer than the length of the referenced<br>QuoteReqID. |
| 537   | QuoteType                       | int          | N    | Identifies the type of quote. Defaults to 1 (Trade<br>able) if missing.<br>• '0' = Indicative (no trading possible)                                                    |
|       |                                 |              |      | • '1' = Tradeable                                                                                                                                                      |
| 55    | Symbol                          | String       | Y    | Contains the requested symbol.                                                                                                                                         |
| 106   | Issuer                          | String       | Y    | Specifies the TEX name of the bank that sent this<br>quote or 'MarketData' if the quote request was a<br>market data request.                                          |
| 38    | OrderQty                        | Qty          | Y    | Requested size, for swaps of near leg. If the quote<br>type is indicative, this will be '0'                                                                            |
| 192   | OrderQty2                       | Qty          | N    | Requested size of far leg. Mandatory for swaps if<br>the quote type is 'tradeable'                                                                                     |
| 15    | Currency                        | Currency     | N    | Currency that the OrderQty<38> is expressed in.<br>Mandatory for tradeable quotes.                                                                                     |
| 1     | Account                         | String       | C    | Echoed<br>from<br>the<br>QuoteRequest<br>for<br>tradable<br>quotes.                                                                                                    |
| 555   | NoLegs                          | NumInGroup   | N    | Repeating group of leg instructions. Used only for<br>non-swap multi-leg instruments.                                                                                  |
| →600  | LegSymbol                       | String       | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2                                                                                                         |
| → 611 | LegMaturityDate                 | LocalMktDate | N    | Defines the Fixing Date for an NDF Block trade<br>leg.                                                                                                                 |
| →687  | LegQty                          | Qty          | Y    | The notional amount to be dealt on the leg.                                                                                                                            |
| →588  | LegSettlDate                    | LocalMktDate | N    | The settlement date for the leg.                                                                                                                                       |
| →681  | LegBidPx                        | Price        | N    | Defines the All-In-Bid-rate for the leg.                                                                                                                               |
| →684  | LegOfferPx                      | String       | N    | Defines the All-In-Offer-rate for the leg.                                                                                                                             |
|       |                                 |              |      | Continued on next page                                                                                                                                                 |

7.5. Quote [S] Chapter 7: Business Messages

| Tag    | Name                          | Type         | Req. | Description                                                                                                                                                                                                                                     |
|--------|-------------------------------|--------------|------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| →654   | LegRefID                      | String       | Y    | Unique reference id of the leg. Echoed from the<br>quote request.                                                                                                                                                                               |
| → 7652 | LegMidPx                      | String       | N    | The midprice for this leg                                                                                                                                                                                                                       |
| → 6050 | BidPx2                        | Price        | N    | Defines the far leg bid forward rate for an FX Swap.                                                                                                                                                                                            |
| → 6051 | OfferPx2                      | Price        | N    | Defines the far leg offer forward rate for an FX Swap.                                                                                                                                                                                          |
| 132    | BidPx                         | Price        | N    | Defines the bid forward rate for forwards.<br>For<br>swaps this field defines the composite near leg<br>rate to an offer rate in the far leg. So if OfferPx2<br>is set, this value must be set too.                                             |
|        |                               |              |      | For<br>a<br>Deposit,<br>this<br>field<br>defines<br>the<br>Interest<br>Rate that the provider (the borrower) will bid.<br>The interest rate may have up to 4 decimal places.                                                                    |
| 133    | OfferPx                       | Price        | N    | Defines the offer forward rate for forwards.<br>For<br>swaps this field defines the composite near leg<br>rate to a bid rate in the far leg. So if BidPx2 is set,<br>this value must be set too.                                                |
|        |                               |              |      | For<br>a<br>Loan,<br>this<br>field<br>defines<br>the<br>Interest<br>Rate that the provider (the lender) will offer. The<br>interest rate may have up to 4 decimal places.                                                                       |
| 188    | BidSpotRate                   | Price        | N    | Defines the bid reference spot rate.<br>For Swaps,<br>this value defines the reference spot rate of the<br>near leg rate to an offer rate in the far leg.<br>So<br>if OfferPx2 and BidPx are set, this value must be<br>set, too.               |
| 190    | OfferSpotRate                 | Price        | N    | Defines the offer reference spot rate. For Swaps<br>this value defines the reference spot rate of the<br>near leg to a bid rate in the far leg. So if BidPx2<br>and OfferPx are set, this value must be set, too.                               |
| 631    | MidPx                         | Price        | N    | MidPrice (near leg of Swaps)                                                                                                                                                                                                                    |
| 7650   | MidSpotRate                   | Price        | N    | Mid spot rate                                                                                                                                                                                                                                   |
| 7651   | MidPx2                        | Price        | N    | MidPrice of far leg (Swaps)                                                                                                                                                                                                                     |
| 7084   | BidInterestAtMaturity         | Price        | N    | Amount of interest (i.e.<br>lump-sum) at maturity to be<br>paid by the bank to the requester. Populated for Deposit<br>only.                                                                                                                    |
| 7085   | OfferInterestAtMaturity Price |              | N    | Amount of interest (i.e.<br>lump-sum) at maturity to be<br>paid by the client to the bank. Populated for Loan only.                                                                                                                             |
| 62     | ValidUntilTime                | UTCTimestamp | N    | The<br>time<br>UTC<br>with<br>format<br>'yyyyMMdd<br>HH:mm:ss' when the quote will expire.<br>If this<br>field is not set then the Quote will expire at the<br>end of RFS or the QuoteRequest's expire time.<br>Only sent for tradeable quotes. |
|        |                               |              |      | Continued on next page                                                                                                                                                                                                                          |

![](_page_35_Picture_0.jpeg)

### 7.5. Quote [S] Chapter 7: Business Messages

| Tag                             | Name         | Type         | Req. | Description                                                                                                                                                                                                                                                               |  |
|---------------------------------|--------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| 7071                            | ProductType  | String       | N    | Custom field defining the requested product.<br>Possible values:<br>• 'FX-STD': Spot, Forward, Swap, NDF and NDS<br>Note:<br>• This field is only populated for indicative quotes.<br>• Block trades and base metals products are not<br>supported for indicative quotes. |  |
| 9514                            | OptionPeriod | String       | N    | Option Period (for FX Time Option)                                                                                                                                                                                                                                        |  |
| 9515                            | OptionDate   | LocalMktDate | N    | Option Date (for FX Time Option)                                                                                                                                                                                                                                          |  |
| 7070                            | RefSpotDate  | LocalMktDate | N    | Defines the spot date in the 360T financial calender.<br>This value is used to clarify if both sides have the same<br>definition for a spot. Note: This field is only populated<br>for tradeable quotes.                                                                  |  |
| <messagefooter></messagefooter> |              |              | Y    |                                                                                                                                                                                                                                                                           |  |

Table 7.5: Quote message

## <span id="page-36-0"></span>**7.6 QuoteCancel [Z]**

The QuoteCancel message can be sent both from 360T to the customer and also from the customer to 360T.

### <span id="page-36-1"></span>**7.6.1 Customer** → **360T**

The customer sends a QuoteCancel message to cancel a previous QuoteRequest. The quote request message to be canceled is specified by the QuoteRequestID.

<span id="page-36-3"></span>

| Tag                             | Name            | Type   | Req.             | Description                                                                                                                                                                                                                                                                                                               |
|---------------------------------|-----------------|--------|------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                 | Y      | MsgType <35> = Z |                                                                                                                                                                                                                                                                                                                           |
| 117                             | QuoteID         | String | Y                | Always set to '0' as always the complete quote re<br>quest will be withdrawn.                                                                                                                                                                                                                                             |
| 131                             | QuoteReqID      | String | Y                | Unique ID that refers back to the original quote re<br>quest.                                                                                                                                                                                                                                                             |
| 298                             | QuoteCancelType | int    | Y                | Always set to '4': Cancel all quotes for the request.                                                                                                                                                                                                                                                                     |
| 7071                            | ProductType     | String | Y                | Custom field defining the requested product.<br>Possible values:<br>• 'FX-STD': Spot, Forward, Swap, NDF and NDS<br>• 'FX-BT': Block trade<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip':<br>Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit |
| <messagefooter></messagefooter> |                 | Y      |                  |                                                                                                                                                                                                                                                                                                                           |

Table 7.6: QuoteCancel message from customer to 360T

### <span id="page-36-2"></span>**7.6.2 Customer** ← **360T**

360T sends a QuoteCancel message to the customer to indicate that a previously sent quote is revoked. This quote is specified by the corresponding QuoteID field. If the value of QuoteID is 0, then the whole request is canceled. If the value of QuoteID is "MARKET-DATA", then this message is an indication that indicative quotes are temporarily not available. In this case the request is not canceled.

<span id="page-36-4"></span>

| Tag                             | Name                   | Type   | Req.             | Description                                                                                                                                                                                                  |  |
|---------------------------------|------------------------|--------|------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| <messageheader></messageheader> |                        | Y      | MsgType <35> = Z |                                                                                                                                                                                                              |  |
| 117                             | QuoteID                | String | Y                | ID<br>of<br>the<br>specific<br>quote<br>that<br>is<br>canceled.<br>'0'<br>means that the complete quote request is canceled.<br>'MARKET-DATA' means that indicative quotes are<br>temporarily not available. |  |
|                                 | Continued on next page |        |                  |                                                                                                                                                                                                              |  |

7.6. QuoteCancel [Z] Chapter 7: Business Messages

| Tag                             | Name            | Type   | Req. | Description                                                                                                                                                                                                                                                                                                                                                    |
|---------------------------------|-----------------|--------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 131                             | QuoteReqID      | String | Y    | Unique ID that refers back to the original quote re<br>quest.                                                                                                                                                                                                                                                                                                  |
| 298                             | QuoteCancelType | int    | Y    | Always set to '1': Cancel for Symbol.                                                                                                                                                                                                                                                                                                                          |
| 7071                            | ProductType     | String | Y    | Custom field defining the requested product.<br>Possible values:<br>• 'FX-STD': FX Spot, FX Forward and FX Swap<br>• 'FX-NDF': NDF<br>• 'FX-NDS': NDS<br>• 'FX-BT': Block trade<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip':<br>Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit |
| <messagefooter></messagefooter> |                 |        | Y    |                                                                                                                                                                                                                                                                                                                                                                |

Table 7.7: QuoteCancel message from 360T to customer

## <span id="page-38-0"></span>**7.7 New Order Single [D]**

### (Customer → 360T)

The customer sends a New Order Single message to execute a price. A NewOrderSingle message is limit-based with reference to the limit-rate of the referenced quote. 360T responds within 10 seconds with an Execution Report message to inform about the final status of the execution.

### Note: If you don't receive an ExecutionReport with state Filled or Rejected within 10 seconds, you must immediately contact 360T support to clarify the deal status.

<span id="page-38-1"></span>

| Tag                             | Name           | Type         | Req.             | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
|---------------------------------|----------------|--------------|------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                | Y            | MsgType <35> = D |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 11                              | ClOrdID        | String       | Y                | Unique identifier designated by the customer and sent<br>to 360T.                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 1                               | Account        | String       | Y                | If the sender is trading for another legal entity (Trade<br>As), this field defines the name of this entity. Other<br>wise this is the same value as in SenderCompID.                                                                                                                                                                                                                                                                                                                                                       |
| 64                              | SettlDate      | LocalMktDate | Y                | Trade settlement date (for swaps of near leg)                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| 14101                           | SplitSettlDate | LocalMktDate | N                | Split settlement date.<br>Must match the value in the<br>QuoteRequest <r>.</r>                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| 55                              | Symbol         | String       | Y                | Defines the currency pair for FX products, specified<br>using two 3-letter ISO 4217 codes separated by a<br>slash ('/') delimiter.<br>For Base Metals,<br>the Symbol<55> field contains<br>the chemical element symbol of the metal (e.g. "AL"<br>for aluminium, "CU" for copper etc.), apart from<br>"Iron Ore 100 ton" and "Iron Ore 100 ton", which use<br>"FE1" and "FE5" respectively.<br>For Money Market (MM) products, the Symbol<55><br>field defines the currency, specified using one 3-letter<br>ISO 4217 code. |
| 541                             | MaturityDate   | LocalMktDate | C                | Must be the same date as in the corresponding quote<br>request. Defines the Fixing Date for an NDF (or the<br>near leg for an NDS).<br>For Loan/Deposit products, the MaturityDate<541><br>field defines the end of the loan or deposit's term<br>(required for this product).                                                                                                                                                                                                                                              |
| 7541                            | MaturityDate2  | LocalMktDate | N                | Far leg maturity date if the order is an NDS. Must be the<br>same date as in the corresponding quote request.                                                                                                                                                                                                                                                                                                                                                                                                               |
| 106                             | Issuer         | String       | N                | Issuer of the original quote that is supposed to be ex<br>ecuted.                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Continued on next page          |                |              |                  |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

| Tag | Name         | Type         | Req. | Description                                                                                                                                      |
|-----|--------------|--------------|------|--------------------------------------------------------------------------------------------------------------------------------------------------|
| 54  | Side         | char         | Y    | Defines if the Market Taker is intending to buy or sell<br>the given symbol for FX and Base Metals products.                                     |
|     |              |              |      | • The Side<54> field defines the side of the first<br>currency in the Symbol<55> field (the base cur<br>rency) and not of the notional currency. |
|     |              |              |      | • For FX Swap and NDS products, the Side<54><br>field indicates the side of the far leg.                                                         |
|     |              |              |      | For Money Market (MM) products, the Side<54><br>field is mandatory and defines if the client wants to<br>lend or borrow money.                   |
|     |              |              |      | Possible values:                                                                                                                                 |
|     |              |              |      | • '1' = Buy                                                                                                                                      |
|     |              |              |      | • '2' = Sell                                                                                                                                     |
|     |              |              |      | • 'F' = Lend (MM Deposit only)                                                                                                                   |
|     |              |              |      | • 'G' = Borrow (MM Loan only)                                                                                                                    |
| 60  | TransactTime | UTCTimestamp | Y    | Time this order request was initiated/released by the<br>trader, trading system, or intermediary.                                                |
| 38  | OrderQty     | Qty          | Y    | Defines the notional amount in the notional currency,<br>for which the customer wants to execute.                                                |
| 40  | OrdType      | char         | Y    | Defines type of order. Possible values:                                                                                                          |
|     |              |              |      | • 'D' = Previously quoted                                                                                                                        |
|     |              |              |      | • '1' = Market order                                                                                                                             |
|     |              |              |      | • '2' = Limit order (only for FX Spot and For<br>ward)                                                                                           |
| 44  | Price        | Price        | C    | Price for which the spot, forward or near leg of a swap<br>has to be executed. If OrdType = '2' (Limit order), the<br>field is mandatory.        |
| 15  | Currency     | Currency     | Y    | Notional currency, in which the OrdQty <38> is de<br>fined.                                                                                      |
|     |              |              |      | Continued on next page                                                                                                                           |
|     |              |              |      |                                                                                                                                                  |

7.7. New Order Single [D] Chapter 7: Business Messages

| Tag                    | Name            | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                   |
|------------------------|-----------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 117                    | QuoteID         | String       | Y    | Mandatory field. For Limit and Market orders, it has<br>to be populated with QuoteRequestId <131> of the re<br>ferred quote request. For Previously Quoted orders, it<br>contains the QuoteID <117> of the referenced quote.<br>If Issuer <106> is not defined, then only the rate is<br>taken from the referenced quote, which would not<br>guarantee execution with the provider from whom it<br>came from. |
| 193                    | SettlDate2      | LocalMktDate | N    | Far settlement date.<br>If present, this must be inter<br>preted as a swap. In the 360T platform the tenor on<br>the far leg is relative to the near leg.                                                                                                                                                                                                                                                     |
| 14102                  | SplitSettlDate2 | LocalMktDate | N    | Far split settlement date.<br>Must match the value in the<br>QuoteRequest <r>.</r>                                                                                                                                                                                                                                                                                                                            |
| 192                    | OrderQty2       | Qty          | N    | Defines the notional amount in the notional currency,<br>for which the customer wants to execute. (FarLeg for<br>Swaps)                                                                                                                                                                                                                                                                                       |
| 7071                   | ProductType     | String       | C    | Custom field defining the requested product.<br>Possible values:<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip':<br>Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit<br>Note:<br>• Required for Money Market and Base Metals prod<br>ucts.                                                                                         |
| 7072                   | DayCount        | String       | C    | Defines the day count convention of the interest rate for<br>Loan/Deposit requests (required field for this product).<br>Possible values:<br>• 1/1<br>• ACT/365F<br>• ACT/360<br>• ACT/ACT<br>• 30/360<br>• 30E/360<br>• BUS/252                                                                                                                                                                              |
| 7075                   | FixingReference | String       | N    | Fixing reference for NDF, NDS or NDF block (if product<br>supported in this API version).                                                                                                                                                                                                                                                                                                                     |
| Continued on next page |                 |              |      |                                                                                                                                                                                                                                                                                                                                                                                                               |

7.7. New Order Single [D] Chapter 7: Business Messages

| Tag                             | Name       | Type         | Req. | Description                                                                                                                              |
|---------------------------------|------------|--------------|------|------------------------------------------------------------------------------------------------------------------------------------------|
| 9515                            | OptionDate | LocalMktDate | N    | Option Date, it is the end date of Time Option. Must be the<br>same date as in the corresponding quote request. (for FX<br>Time Option). |
| <messagefooter></messagefooter> |            |              | Y    |                                                                                                                                          |

Table 7.8: NewOrderSingle message

## <span id="page-42-0"></span>**7.8 New Order Multileg [AB]**

### (Customer → 360T)

The customer must use the NewOrderMultileg<AB> message to execute a block trade price. As with the New Order Single message, the NewOrderMultileg message is limit-based with reference to the limit-rate of the referenced quote. 360T responds within 10 seconds with an Execution Report message to inform about the final status of the execution.

### Note: If you don't receive an ExecutionReport with state Filled or Rejected within 10 seconds, you must immediately contact 360T support to clarify the deal status.

<span id="page-42-1"></span>

| Tag                             | Name            | Type         | Req.              | Description                                                                                                                                                                                                                                  |
|---------------------------------|-----------------|--------------|-------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                 | Y            | MsgType <35> = AB |                                                                                                                                                                                                                                              |
| 11                              | ClOrdID         | String       | Y                 | Unique identifier designated by the customer and sent<br>to 360T.                                                                                                                                                                            |
| 1                               | Account         | String       | Y                 | If the sender is trading for another legal entity (Trade<br>As), this field defines the name of this entity. Other<br>wise this is the same value as in SenderCompID. For<br>multi-allocations, this would be the parent entity.             |
| 54                              | Side            | char         | Y                 | Defines if the Market Taker is intending to buy or sell<br>the given symbol.2 Possible values:                                                                                                                                               |
|                                 |                 |              |                   | • '1' = Buy                                                                                                                                                                                                                                  |
|                                 |                 |              |                   | • '2' = Sell                                                                                                                                                                                                                                 |
|                                 |                 |              |                   | • 'B' = As Defined (for use with block trades)                                                                                                                                                                                               |
|                                 |                 |              |                   | For block trades, the Side<54> must either be set to<br>'B' = As Defined or its value must match the overall<br>side of the netted leg quantities (defined in each leg<br>using LegSide<624> and LegQty<687>).                               |
|                                 |                 |              |                   | For zero-netted block trades, if the customer specified<br>the top-level side to use in the QuoteRequest <r><br/>(either '1' = Buy or '2' = Sell), then the same side<br/>must be provided in the NewOrderMultileg<ab><br/>message.</ab></r> |
| 55                              | Symbol          | String       | Y                 | Contains the delivered currency pair in the format:<br>CC1/CC2.                                                                                                                                                                              |
| 106                             | Issuer          | String       | N                 | Issuer of the original quote that is supposed to be ex<br>ecuted.                                                                                                                                                                            |
| 555                             | NoLegs          | NumInGroup   | Y                 | Repeating group containing individual amounts on<br>legs.                                                                                                                                                                                    |
| → 600                           | LegSymbol       | String       | Y                 | Contains the delivered currency pair in the format:<br>CC1/CC2                                                                                                                                                                               |
| → 611                           | LegMaturityDate | LocalMktDate | N                 | Defines the Fixing Date for an NDF Block trade leg.                                                                                                                                                                                          |
| Continued on next page          |                 |              |                   |                                                                                                                                                                                                                                              |

<span id="page-42-2"></span><sup>2</sup>The Side<54> tag defines the direction of the Symbol, i.e. the side of the first currency in the symbol and not of the notional currency.

©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 43

7.8. New Order Multileg [AB] Chapter 7: Business Messages

| Tag                             | Name            | Type         | Req. | Description                                                                                                                                                                                                                                                                                  |
|---------------------------------|-----------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| → 624                           | LegSide         | char         | Y    | Defines if the customer wants to buy or sell the given<br>symbol on this leg. Possible values:                                                                                                                                                                                               |
|                                 |                 |              |      | • '1' = Buy                                                                                                                                                                                                                                                                                  |
|                                 |                 |              |      | • '2' = Sell                                                                                                                                                                                                                                                                                 |
| → 687                           | LegQty          | char         | Y    | The notional amount to be dealt on one leg                                                                                                                                                                                                                                                   |
| → 588                           | LegSettlDate    | LocalMktDate | Y    | Settlement date of one leg                                                                                                                                                                                                                                                                   |
| → 566                           | LegPrice        | Price        | Y    | Price to be executed for this leg.                                                                                                                                                                                                                                                           |
| → 654                           | LegRefID        | String       | Y    | Unique reference id of the leg from the QuoteRequest.                                                                                                                                                                                                                                        |
| 60                              | TransactTime    | UTCTimestamp | Y    | Time this order request was initiated/released by the<br>trader, trading system, or intermediary.                                                                                                                                                                                            |
| 38                              | OrderQty        | Qty          | Y    | Defines the netted notional amount in the notional<br>currency, for which the customer wants to execute.                                                                                                                                                                                     |
| 40                              | OrdType         | char         | Y    | Defines type of order. Possible values:                                                                                                                                                                                                                                                      |
|                                 |                 |              |      | • 'D' = Previously quoted                                                                                                                                                                                                                                                                    |
| 15                              | Currency        | Currency     | Y    | Notional currency, in which the OrdQty <38> is de<br>fined.                                                                                                                                                                                                                                  |
| 117                             | QuoteID         | String       | Y    | Mandatory field.<br>For Previously Quoted orders, it<br>contains the QuoteID <117> of the referenced quote.<br>If Issuer <106> is not defined, then only the rate is<br>taken from the referenced quote, which would not<br>guarantee execution with the provider from whom it<br>came from. |
| 7071                            | ProductType     | String       | Y    | Custom field defining the requested product.<br>Possible values:<br>• 'FX-BT': Block trade                                                                                                                                                                                                   |
| 7075                            | FixingReference | String       | N    | Fixing reference for NDF, NDS or NDF block (if product<br>supported in this API version).                                                                                                                                                                                                    |
| <messagefooter></messagefooter> |                 |              | Y    |                                                                                                                                                                                                                                                                                              |

Table 7.9: NewOrderMultileg message

## <span id="page-44-0"></span>**7.9 Execution Report [8]**

### (360T → customer)

When the customer sends a NewOrderSingle message, 360T responds with two different ExecutionReport messages: Immediately after receiving the message, a NewOrderSingle message is sent with OrdStatus '0' (New), to notify about the successful reception. After the order has been processed, 360T sends a second ExecutionReport message to inform about its final status.

The Regulatory ID group would contain TVTIC, UTI or USI IDs for the main trade or the allocation tickets, in case such exist.

### Note: If you don't receive an ExecutionReport with state Filled or Rejected within 10 seconds, you must immediately contact 360T support to clarify the deal status.

<span id="page-44-1"></span>

| Tag                             | Name          | Type       | Req. | Description                                                                                                                                                                                                                                                                                                                                                                |
|---------------------------------|---------------|------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |               |            | Y    | MsgType <35> = 8                                                                                                                                                                                                                                                                                                                                                           |
| 37                              | OrderID       | String     | Y    | Trade reference ID generated by 360T                                                                                                                                                                                                                                                                                                                                       |
| 11                              | ClOrdID       | String     | Y    | Unique<br>order<br>identifier<br>from<br>referenced<br>NewOrderSingle [D] message.                                                                                                                                                                                                                                                                                         |
| 453                             | NoPartyIDs    | NumInGroup | N    | Repeating group containing the participants of the<br>trade. As we have one provider and one requester this<br>will at least 2. If requested we can also send the en<br>tity for which the trade is exported with PartyRole 'In<br>terested Party'. This can be useful in a ITEX scenario<br>where both sides of an internal deal are exported to the<br>same FIX session. |
| → 448                           | PartyID       | String     | N    | 360T company name or MIC of the participant (re<br>quester or provider)                                                                                                                                                                                                                                                                                                    |
| → 447                           | PartyIDSource | char       | N    | It can contain one of the following values:<br>• 'D': Proprietary custom code<br>• 'G': MIC (for PartyRole=MTF/SI)<br>• 'N': LegalEntityIdentifier<br>• 'P': Short code identifier                                                                                                                                                                                         |
|                                 |               |            |      | Continued on next page                                                                                                                                                                                                                                                                                                                                                     |

| Tag   | Name                                                                      | Type   | Req. | Description                                                                                                                                                                                  |
|-------|---------------------------------------------------------------------------|--------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| → 452 | PartyRole                                                                 | int    | N    | Possible values:                                                                                                                                                                             |
|       |                                                                           |        |      | • '1' = 'Executing Firm' for the requester                                                                                                                                                   |
|       |                                                                           |        |      | • '4' = 'Clearing Firm'                                                                                                                                                                      |
|       |                                                                           |        |      | • '35' = 'Liquidity provider' for the provider side                                                                                                                                          |
|       |                                                                           |        |      | • '63' = Systematic Internaliser (SI) (the MIC of<br>the SI)                                                                                                                                 |
|       |                                                                           |        |      | • '64' = Multilateral Trading Facility (MTF) (the<br>MIC of the 360T MTF)                                                                                                                    |
|       |                                                                           |        |      | • '78' = 'Allocation Entity'                                                                                                                                                                 |
|       |                                                                           |        |      | • '116' = 'Reporting Party' - The LEI of reporting<br>party                                                                                                                                  |
|       |                                                                           |        |      | If a party has a LegalEntityIdentifier available, we'll<br>provide this as well by duplicating the group instance<br>and send the LEI with the same PartyRole and PartyID<br>Source<447>='N' |
|       | PartyID Repeating Group for Clearing Firm                                 |        |      |                                                                                                                                                                                              |
| → 448 | PartyID                                                                   | String | N    | Which Clearing house (DCO) this trade will be cleared<br>at.                                                                                                                                 |
| → 447 | PartyIDSource                                                             | char   | N    | 'D': Proprietary custom code.                                                                                                                                                                |
| → 452 | PartyRole                                                                 | int    | N    | '4' = Clearing Firm                                                                                                                                                                          |
|       | PartyID Repeating Group for ExecutionVenue = MTF                          |        |      |                                                                                                                                                                                              |
| → 448 | PartyID                                                                   | String | N    | MIC of 360T MTF                                                                                                                                                                              |
| → 447 | PartyIDSource                                                             | char   | N    | 'G': MIC                                                                                                                                                                                     |
| → 452 | PartyRole                                                                 | int    | N    | '64' = Multilateral Trading Facility (MTF)                                                                                                                                                   |
|       | PartyID Repeating Group for Requester(LEI)                                |        |      |                                                                                                                                                                                              |
| → 448 | PartyID                                                                   | String | N    | LEI of the requester                                                                                                                                                                         |
| → 447 | PartyIDSource                                                             | char   | N    | 'N': LEI                                                                                                                                                                                     |
| → 452 | PartyRole                                                                 | int    | N    | '1' = 'Executing Firm'                                                                                                                                                                       |
|       | PartyID Repeating Group for Requester(MIC) - in case requester is an SI   |        |      |                                                                                                                                                                                              |
| → 448 | PartyID                                                                   | String | N    | MIC of the requester                                                                                                                                                                         |
| → 447 | PartyIDSource                                                             | char   | N    | 'G': MIC                                                                                                                                                                                     |
| → 452 | PartyRole                                                                 | int    | N    | '1' = 'Executing Firm'                                                                                                                                                                       |
|       | PartyID Repeating Group for Provider(LEI)                                 |        |      |                                                                                                                                                                                              |
| → 448 | PartyID                                                                   | String | N    | LEI of the provider                                                                                                                                                                          |
| → 447 | PartyIDSource                                                             | char   | N    | 'N': LEI                                                                                                                                                                                     |
| → 452 | PartyRole                                                                 | int    | N    | '35' = 'Liquidity Provider'                                                                                                                                                                  |
|       | PartyID Repeating Group for Provider(MIC) - in case the provider is an SI |        |      |                                                                                                                                                                                              |
| → 448 | PartyID                                                                   | String | N    | MIC of the provider                                                                                                                                                                          |
|       |                                                                           |        |      | Continued on next page                                                                                                                                                                       |

©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 46

| Tag   | Name                                 | Type         | Req. | Description                                                                                                                                                                                                                                    |
|-------|--------------------------------------|--------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| → 447 | PartyIDSource                        | char         | N    | 'G': MIC                                                                                                                                                                                                                                       |
| → 452 | PartyRole                            | int          | N    | '35' = 'Liquidity Provider'                                                                                                                                                                                                                    |
|       | PartyID Repeating Group for Provider |              |      |                                                                                                                                                                                                                                                |
| → 448 | PartyID                              | String       | N    | 360T company name of provider                                                                                                                                                                                                                  |
| → 447 | PartyIDSource                        | char         | N    | 'D': Proprietary/Custom code                                                                                                                                                                                                                   |
| → 452 | PartyRole                            | int          | N    | '35' = 'Liquidity Provider'                                                                                                                                                                                                                    |
| 17    | ExecID                               | String       | Y    | Unique execution identifier assigned by 360T.                                                                                                                                                                                                  |
| 150   | ExecType                             | char         | Y    | Describes the current state of the order. Possible<br>values:                                                                                                                                                                                  |
|       |                                      |              |      | • '0' = New                                                                                                                                                                                                                                    |
|       |                                      |              |      | • '8' = Rejected                                                                                                                                                                                                                               |
|       |                                      |              |      | • 'F' = Trade                                                                                                                                                                                                                                  |
| 39    | OrdStatus                            | char         | Y    | Describes the current state of the order. Possible<br>values:                                                                                                                                                                                  |
|       |                                      |              |      | • '0' = New                                                                                                                                                                                                                                    |
|       |                                      |              |      | • '2' = Filled                                                                                                                                                                                                                                 |
|       |                                      |              |      | • '8' = Rejected                                                                                                                                                                                                                               |
| 1     | Account                              | String       | Y    | Legal entity of the issuer of this Execution report.<br>Refers back to the same value from the executed<br>quote.                                                                                                                              |
| 64    | SettlDate                            | LocalMktDate | N    | Settlement Date of the order. Near leg Date of a<br>swap. Not provided for block trades.                                                                                                                                                       |
| 14101 | SplitSettlDate                       | LocalMktDate | N    | Split settlement date. For swaps this is the near leg split<br>settlement date.                                                                                                                                                                |
| 55    | Symbol                               | String       | Y    | Contains the delivered symbol.                                                                                                                                                                                                                 |
| 48    | SecurityID                           | String       | C    | Contains the product level ISIN code.<br>The value of<br>this field is subject to availability. For leg level ISINS<br>for Swaps please refer to the group NoSecurityAltID<br><454>. This field will not be set to report for Block<br>trades. |
| 22    | SecurityIDSource                     | String       | C    | Should be '4'=ISIN if SecurityID<48> is set.                                                                                                                                                                                                   |
| 454   | NoSecurityAltID                      | NumInGroup   | C    | Should be 2 in case of Swap and NDS if ISINs are<br>available. The first group lists the ISIN of the near<br>leg and the second group - the ISIN of the far leg.                                                                               |
| → 455 | SecurityAltID                        | String       | C    | The ISIN of the respective leg.                                                                                                                                                                                                                |
| → 456 | SecurityAltIDSource                  | String       | C    | Should be '4'=ISIN if SecurityID<48> is set.                                                                                                                                                                                                   |
|       |                                      |              |      | Continued on next page                                                                                                                                                                                                                         |

| Tag  | Name          | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                          |
|------|---------------|--------------|------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 461  | CFICode       | String       | N    | CFI-Code of the traded instrument.                                                                                                                                                                                                                                                                                                   |
| 2891 | UPICode 3     | String       | N    | Unique Product Identifier (UPI) of the traded instru<br>ment (near leg for Swaps). When there are allocations,<br>this field contains the UPI of each allocation.                                                                                                                                                                    |
| 7891 | UPICode2 3    | String       | N    | Unique Product Identifier (UPI) of the traded instru<br>ment (far leg for Swaps). When there are allocations,<br>this field contains the UPI of each allocation.                                                                                                                                                                     |
| 541  | MaturityDate  | LocalMktDate | C    | Defines the Fixing Date for an NDF (or the near<br>leg for an NDS).                                                                                                                                                                                                                                                                  |
|      |               |              |      | For<br>Loan/Deposit<br>products,<br>the<br>Maturity<br>Date<541> field defines the end of the loan or<br>deposit's term.                                                                                                                                                                                                             |
| 7541 | MaturityDate2 | LocalMktDate | N    | Far leg Maturity date if the order was an NDS.                                                                                                                                                                                                                                                                                       |
| 54   | Side          | char         | Y    | Defines if the client has to bought or sold the<br>given symbol for FX and Base Metals products.<br>For Money Market (MM) products, the Side<54><br>field defines if the client has lent or borrowed<br>money.<br>Possible values:<br>• '1' = Buy<br>• '2' = Sell<br>• 'F' = Lend (MM Deposit only)<br>• 'G' = Borrow (MM Loan only) |
| 38   | OrderQty      | Qty          | Y    | Defines the notional amount in the notional cur<br>rency, for which the customer tried to execute.<br>Netted amount in case of a block trade.                                                                                                                                                                                        |
| 40   | OrdType       | char         | Y    | Depending on the value sent in the order request:<br>• 'D' = Previously quoted<br>• '1' = Market order<br>• '2' = Limit order                                                                                                                                                                                                        |
| 44   | Price         | Price        | Y    | Price which has been set by the customer in the<br>NewOrderSingle message. For block trades it will<br>be set to 0.                                                                                                                                                                                                                  |
|      |               |              |      | Continued on next page                                                                                                                                                                                                                                                                                                               |

<span id="page-47-0"></span><sup>3</sup>These fields are only available if support for sending UPI identifiers has been enabled for the FIX session. Please speak to your 360T representative if you require this feature.

©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 48

| Tag    | Name            | Type         | Req. | Description                                                                                                                                       |
|--------|-----------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------|
| 15     | Currency        | Currency     | Y    | Notional currency, in which the quantities are de<br>fined.                                                                                       |
| 32     | LastQty         | Price        | Y    | Amount that has been executed for a spot or for<br>ward or the near leg of a swap. For block trades it<br>will have the netted amount.            |
| 31     | LastPx          | Price        | Y    | Rate that has been executed for a spot or forward<br>or the near leg of a swap. For block trades it will<br>be set to 0.                          |
| 194    | LastSpotRate    | Price        | Y    | The reference spot rate.                                                                                                                          |
| 151    | LeavesQty       | Qty          | Y    | Always set to '0'.                                                                                                                                |
| 14     | CumQty          | Qty          | Y    | Has the value of the executed amount of a spot or<br>forward or of the near leg of a swap.<br>For block<br>trades it will have the netted amount. |
| 6      | AvgPx           | Price        | Y    | Always set to '0'.                                                                                                                                |
| 631    | MidPx           | Price        | N    | MidPrice (near leg of Swaps).                                                                                                                     |
| 7650   | MidSpotRate     | Price        | N    | Mid spot rate                                                                                                                                     |
| 7651   | MidPx2          | Price        | N    | MidPrice of far leg (Swaps)                                                                                                                       |
| 60     | TransactTime    | UTCTimestamp | Y    | Time when the trade was executed. This field is<br>set in millisecond precision.                                                                  |
| 58     | Text            | String       | N    | contains additional information                                                                                                                   |
| 193    | SettlDate2      | LocalMktDate | N    | Farleg Date of a swap. In the 360T platform the<br>tenor on the far leg is relative to the near leg.                                              |
| 14102  | SplitSettlDate2 | LocalMktDate | N    | Far split settlement date. If present, this must be inter<br>preted as a swap.                                                                    |
| 192    | OrderQty2       | Qty          | N    | Defines the notional amount in the notional cur<br>rency, which has been executed in the far leg for a<br>swap.                                   |
| 640    | Price2          | Price        | N    | Price which was set in the order for the far leg of<br>a Swap.                                                                                    |
| 78     | NoAllocs        | NumInGroup   | N    | Repeating group of allocations and related identi<br>fiers                                                                                        |
| → 79   | AllocAccount    | String       | N    | Entity for which an amount gets allocated. Echoed<br>back from the quote request.                                                                 |
| → 80   | AllocQty        | Qty          | N    | Amount allocated. Can be negative to indicate in<br>verse to the side given in field 54. Echoed back<br>from the quote request.                   |
| → 7605 | USIPrefix       | String       | N    | Unique Swap Identifier Prefix for allocation (near<br>leg for Swaps).                                                                             |
| → 7606 | USIID           | String       | N    | Unique Swap Identifier for allocation (near leg for<br>Swaps).                                                                                    |
|        |                 |              |      | Continued on next page                                                                                                                            |

| Tag                    | Name                                                                    | Type         | Req. | Description                                                                                  |
|------------------------|-------------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------|
| → 7607                 | USIPrefix2                                                              | String       | N    | Unique Swap Identifier Prefix for allocation (far<br>leg for Swaps only).                    |
| → 7608                 | USIID2                                                                  | String       | N    | Unique Swap Identifier for allocation (far leg for<br>Swaps).                                |
| → 7653                 | UTIID                                                                   | String       | N    | Unique Trade Identifier for allocation (near leg for<br>Swaps).                              |
| → 7654                 | UTIID2                                                                  | String       | N    | Unique Trade Identifier for allocation (far leg for<br>Swaps).                               |
| → 539                  | NoNestedPartyIDs                                                        | NumInGroup   | N    | Repeating group for MiFID properties of the allo<br>cation. One is accepted per allocation   |
| 7546                   | NoCustomFields                                                          | NumInGroup   | N    | Number of custom fields (group). These are only pro<br>vided if the order has been executed. |
| → 7547                 | CustomFieldName                                                         | String       | C    | Name of this custom field (required when custom fields<br>are used).                         |
| → 7548                 | CustomFieldValue                                                        | String       | C    | Value of this custom field (required when custom fields<br>are used).                        |
|                        | NestedPartyID Repeating Group for LEI of a TAS/TOB requester allocation |              |      |                                                                                              |
| →→ 524                 | NestedPartyID                                                           | String       | N    | LEI of a TAS/TOB requester allocation account                                                |
| →→ 525                 | NestedPartyIDSource                                                     | char         | N    | 'N': LEI                                                                                     |
| →→ 538                 | NestedPartyRole                                                         | int          | N    | '78' = 'Allocation Entity'                                                                   |
| 555                    | NoLegs                                                                  | NumInGroup   | N    | Repeating group of legs - only used to define<br>ISIN/CFI-Codes for Multileg-Products        |
| → 600                  | LegSymbol                                                               | String       | N    | Symbol of the QuoteRequest                                                                   |
| → 611                  | LegMaturityDate                                                         | LocalMktDate | N    | Defines the Fixing Date for an NDF Block trade<br>leg.                                       |
| → 602                  | LegSecurityID                                                           | String       | N    | ISIN of this leg                                                                             |
| → 603                  | LegSecurityIDSource String                                              |              | N    | '4'=ISIN                                                                                     |
| → 608                  | LegCFICode                                                              | String       | N    | CFI-Code of this leg.                                                                        |
| → 2893                 | LegUPICode 3                                                            | String       | N    | Unique Product Identifier (UPI) of this leg.                                                 |
| → 588                  | LegSettlDate                                                            | LocalMktDate | Y    | Settlement date of this leg.                                                                 |
| → 566                  | LegPrice                                                                | Price        | Y    | Execution price of this leg.                                                                 |
| → 654                  | LegRefID                                                                | String       | Y    | Unique<br>reference<br>id<br>of<br>the<br>leg<br>from<br>the<br>QuoteRequest.                |
| → 7652                 | LegMidPx                                                                | Price        | N    | This mid price for this leg.                                                                 |
| → 670                  | NoLegAllocs                                                             | NumInGroup   | Y    | Repeating group of allocations per leg. Only one<br>is accepted per leg.                     |
| →→ 671                 | AllocAccount                                                            | String       | Y    | Entity for which an amount gets allocated                                                    |
| →→ 673                 | AllocQty                                                                | Qty          | Y    | Amount allocated.                                                                            |
| Continued on next page |                                                                         |              |      |                                                                                              |

| Tag                                                                           | Name                                                       | Type       | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |  |  |
|-------------------------------------------------------------------------------|------------------------------------------------------------|------------|------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
| →→ 539                                                                        | NoNestedPartyIDs                                           | NumInGroup | N    | Repeating group for MiFID properties of the allocation.<br>One is accepted per allocation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |  |  |
| NestedPartyID Repeating Group for Investment Decision within Firm (Algorithm) |                                                            |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |
| →→ → 524                                                                      | NestedPartyID                                              | String     | N    | LEI of a TAS/TOB requester allocation account                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |  |  |
| →→ → 525                                                                      | NestedPartyIDSource                                        | char       | N    | 'N': LEI                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |  |  |
| →→ → 538                                                                      | NestedPartyRole                                            | int        | N    | '78' = 'Allocation Entity'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |  |  |
| 1907                                                                          | NoRegulatoryTradeIDs                                       | NumInGroup | N    | Repeating Group containing regulatory IDs.<br>In case of single-leg products with no allocations, one<br>group would be expected.<br>In the case of swaps with<br>no allocations, two or three groups would be sent - first<br>for the near leg, second for the far leg and optionally the<br>third one - product level ISIN. The third group is subject<br>to availability of the TVTIC code on product level.<br>In case of single-leg products with allocations, these<br>groups would match the number and order of legs of<br>the NoAllocs group. In case of a swap with allocations,<br>these groups would match the number of allocations<br>times two plus ontionally one. Namely: the first group<br>- regulatory code for allocation 1 of the near leg; the<br>second group - allocation 1 of the far leg; thrid group -<br>allocation 2 of the near leg; etc. After all the allocation<br>TVTIC there may optionally be a last group indicating<br>the TVTIC for product level for the swap. This value is<br>is subject to availability of the TVTIC code on product<br>level.<br>In case of swaps with allocations, the RegulatoryLe<br>gRefID<2411> will match the NoLegs group this Regu<br>latoryTradeID is refering to. The optional product-level<br>TVTIC won't have a RegulatoryLegRefID<2411> set. |  |  |
|                                                                               | RegulatoryTrade Repeating Group for TVTIC, USI, UTI or RTN |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |
| → 1903                                                                        | RegulatoryTradeID                                          | String     | N    | Regulatory ID                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |  |  |
| → 1906                                                                        | RegulatoryTradeIDType int                                  |            | N    | Regulatory ID type:                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |  |
|                                                                               |                                                            |            |      | • '0' = Unique Transaction Identifier (UTI) or<br>Unique Swap Identifier (USI)<br>• '5' = Trading venue transaction identification<br>code (TVTIC)<br>• '6' = Report Tracking Number (RTN) 4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |  |
| → 2411                                                                        | RegulatoryLegRefID                                         | String     | N    | This field will be missing in case of single-leg prod<br>ucts. In case of non-swap multileg products, this id will<br>match LegRefId of the respective leg it references.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |  |  |
| 2668                                                                          | NoTrdRegPublications                                       | NumInGroup | N    | Repeating Group containing Waivers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |  |
| TrdRegPublication Repeating Group for Waiver                                  |                                                            |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |
| Continued on next page                                                        |                                                            |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |

<span id="page-50-0"></span><sup>4</sup>These fields are only available if support for sending RTN identifiers has been enabled for the FIX session. Please speak to your 360T representative if you require this feature.

©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 51

| Tag                    | Name                        | Type   | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |  |
|------------------------|-----------------------------|--------|------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| → 2669                 | TrdRegPublicationType int   |        | N    | '0' = Pre-trade transparency waiver                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |
| → 2670                 | TrdRegPublication<br>Reason | int    | N    | • '0'='No preceding order in book as transaction<br>price set within average spread of a liquid instru<br>ment'<br>• '1'='No preceding order in book as transaction<br>price depends on system-set reference price for<br>an illiquid instrument'<br>• '2'='No preceding order in book as transac<br>tion price is for transaction subject to conditions<br>other than current market price'<br>• '3'='No public price for preceding order as pub<br>lic reference price was used for matching orders'<br>• '4'='No public price quoted as instrument is<br>illiquid'<br>• '5'='No public price quoted due to "Size"'<br>• '9'='No public price and/or size quoted as trans |  |
|                        |                             |        |      | action is "large in scale"'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |  |
| 828                    | TrdType                     | int    | N    | Post Trade Indicator '65' = TPAC (Package Trade)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |
| 6160                   | LastPx2                     | Price  | N    | Rate that has been executed for the far leg of a swap.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |
| 7071                   | ProductType                 | String | N    | Custom field defining the requested product.<br>Possible values:<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip': Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit<br>Note:<br>• Only populated for Base Metals products.                                                                                                                                                                                                                                                                                                                                                                         |  |
| 7075                   | FixingReference             | String | N    | Fixing reference for NDF, NDS or NDF block (if<br>product supported in this API version).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |  |
| 7653                   | UTIID                       | String | N    | Unique Trade Identifier for allocation (near leg for<br>Swaps). When there are allocations this identifies the<br>parent ticket.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |
| 7654                   | UTIID2                      | String | N    | Unique Trade Identifier for allocation (far leg for<br>Swaps). When there are allocations this identifies the<br>parent ticket.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |  |
| 7605                   | USIPrefix                   | String | N    | Unique Swap Identifier Prefix (near leg for Swaps).<br>When there are allocations this identifies the parent<br>ticket.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |
| Continued on next page |                             |        |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |  |

| Tag                             | Name         | Type         | Req. | Description                                                                                                                 |
|---------------------------------|--------------|--------------|------|-----------------------------------------------------------------------------------------------------------------------------|
| 7606                            | USIID        | String       | N    | Unique Swap Identifier (near leg for Swaps).<br>When<br>there are allocations this identifies the parent ticket.            |
| 7607                            | USIPrefix2   | String       | N    | Unique Swap Identifier Prefix (far leg for Swaps only).<br>When there are allocations this identifies the parent<br>ticket. |
| 7608                            | USIID2       | String       | N    | Unique Swap Identifier (far leg for Swaps). When there<br>are allocations this identifies the parent ticket.                |
| 9514                            | OptionPeriod | String       | N    | Option Period (for FX Time Option)                                                                                          |
| 9515                            | OptionDate   | LocalMktDate | N    | Option Date (for FX Time Option)                                                                                            |
| <messagefooter></messagefooter> |              |              | Y    |                                                                                                                             |

Table 7.10: ExecutionReport message

## <span id="page-53-0"></span>**7.10 SecurityDefinitionRequest [c]**

(Customer → 360T)

The SecurityDefinitionRequest message is used by the customer to request 360T financial calendar information (tenor symbols and value dates) for a particular currency pair. The currency pair must be specified in the Symbol field of the SecurityDefinitionRequest message.

In response to each SecurityDefinitionRequest sent by the client, 360T will reply with a SecurityDefinition message containing a list of tenor symbols and value dates for the given currency pair.

<span id="page-53-1"></span>

| Tag                             | Name                | Type   | Req. | Description                                                                                                                       |
|---------------------------------|---------------------|--------|------|-----------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                     |        | Y    | MsgType <35> = c                                                                                                                  |
| 320                             | SecurityReqID       | String | Y    | Unique identifier for this request. This field must not<br>be longer than 50 characters.                                          |
| 321                             | SecurityRequestType | int    | Y    | Defines the type of SecurityDefinitionRequest. Value<br>provided must be '3' = Request List Securities.                           |
| 55                              | Symbol              | String | Y    | The currency pair for which financial calendar infor<br>mation is being requested.<br>Must be provided in the<br>format: CC1/CC2. |
| <messagefooter></messagefooter> |                     |        | Y    |                                                                                                                                   |

Table 7.11: SecurityDefinitionRequest message

## <span id="page-54-0"></span>**7.11 SecurityDefinition [d]**

### (360T → customer)

This message is sent by 360T to the customer in response to a SecurityDefinitionRequest. The message contains a list of tenor symbols and value dates, stored in the Underlyings group.

Note: the value dates are defined for the current trade date, which is determined when the SecurityDefinitionRequest is received by 360T. The value dates are only valid until the value date roll-over time of the given currency pair.

<span id="page-54-1"></span>

| Tag                             | Name                   | Type         | Req. | Description                                                                                                                         |
|---------------------------------|------------------------|--------------|------|-------------------------------------------------------------------------------------------------------------------------------------|
| <messageheader></messageheader> |                        |              | Y    | MsgType <35> = d                                                                                                                    |
| 320                             | SecurityReqID          | String       | Y    | Unique identifier for original request.<br>This field must not be longer than 50 characters.                                        |
| 322                             | SecurityResponseID     | String       | Y    | Unique identifier for this response.<br>This field must not be longer than 50 characters.                                           |
| 323                             | SecurityResponseType   | int          | Y    | Defines the type of SecurityDefinitionResponse.<br>Value<br>will always be set to '4' = List of securities returned per<br>request. |
| 711                             | NoUnderlyings          | NumInGroup   | Y    | Number of FX value dates delivered in the group.                                                                                    |
| → 311                           | UnderlyingSymbol       | String       | Y    | The currency pair for which date information pertains.<br>Provided in the format: CC1/CC2.                                          |
| → 309                           | UnderlyingSecurityID   | String       | Y    | FX tenor short name.                                                                                                                |
| → 542                           | UnderlyingMaturityDate | LocalMktDate | Y    | Value date of FX tenor in the 360T financial calender.                                                                              |
| → 307                           | UnderlyingSecurityDesc | String       | Y    | FX tenor long name.                                                                                                                 |
| <messagefooter></messagefooter> |                        |              | Y    |                                                                                                                                     |

Table 7.12: SecurityDefinition message

The table below details the FX tenor symbols for which value dates will be provided in the SecurityDefinition message.

Note: the tenors included in the message may be subject to change in future, i.e. tenors may be added or removed without notice.

<span id="page-54-2"></span>

| Tenor<br>Short<br>Name | Tenor<br>Long<br>Name |
|------------------------|-----------------------|
| TD                     | TODAY                 |
| TM                     | TOMORROW              |
| SP                     | SPOT                  |
| SN                     | SPOTNEXT              |
| 1W                     | 1 WEEK                |
| 2W                     | 2 WEEKS               |
| 3W                     | 3 WEEKS               |
| 1M                     | 1 MONTH               |
| 2M                     | 2 MONTHS              |

| 3M  | 3 MONTHS  |
|-----|-----------|
| 4M  | 4 MONTHS  |
| 5M  | 5 MONTHS  |
| 6M  | 6 MONTHS  |
| 7M  | 7 MONTHS  |
| 8M  | 8 MONTHS  |
| 9M  | 9 MONTHS  |
| 1Y  | 1 YEAR    |
| 18M | 18 MONTHS |
| 2Y  | 2 YEARS   |
| 3Y  | 3 YEARS   |
| IM  | MAR IMM   |
| IJ  | JUN IMM   |
| IS  | SEP IMM   |
| ID  | DEC IMM   |

Table 7.13: FX tenors

## <span id="page-56-0"></span>**8 Example Messages**

## <span id="page-56-1"></span>**8.1 News [B]**

<span id="page-56-2"></span>

| Tag                           | Attribute   | Value          |
|-------------------------------|-------------|----------------|
| <message header=""></message> |             |                |
| 35                            | MsgType     | B              |
| 148                           | Headline    | Bank<br>Basket |
| 33                            | LinesOfText | 1              |
| 58                            | Text        | ACME           |

<Message Trailer>

An example New message sent from customer to 360T asking for available providers.

### Table 8.1: Example News message

This is an example News message sent from 360T to the customer. It states that the bank basket for "Fx Spot" trades consists of 5 providers. Each provider is sent in a single Text tag.

<span id="page-56-3"></span>

| Tag                            | Attribute                     | Value                 |  |
|--------------------------------|-------------------------------|-----------------------|--|
|                                | <message header=""></message> |                       |  |
| 35                             | MsgType                       | B                     |  |
| 148                            | Headline                      | Fx<br>Spot            |  |
| 33                             | LinesOfText                   | 5                     |  |
| 58                             | Text                          | BOAL.DEMO             |  |
| 58                             | Text                          | RBS.LND.DEMO          |  |
| 58                             | Text                          | SEB.FRA.DEMO          |  |
| 58                             | Text                          | CITIBANK.DEMO         |  |
| 58                             | Text                          | Barclays<br>BARX.DEMO |  |
| 215                            | NoRoutingIDs                  | 1                     |  |
| 216                            | RoutingType                   | 1                     |  |
| 217                            | RoutingID                     | ACME                  |  |
| <message trailer=""></message> |                               |                       |  |

Table 8.2: Example News message

## <span id="page-57-0"></span>**8.2 QuoteRequest [R]**

### <span id="page-57-1"></span>**8.2.1 Tradeable Quotes: Spot**

This is an example QuoteRequest message. The client wants to buy 1 million EUR against USD. As RefSpotDate is SettlDate this is request for SPOT quotes.

<span id="page-57-3"></span>

| Tag                            | Attribute                     | Value              |  |
|--------------------------------|-------------------------------|--------------------|--|
|                                | <message header=""></message> |                    |  |
| 35                             | MsgType                       | R                  |  |
| 131                            | QuoteReqID                    | MyQuoteReqID-12345 |  |
| 7070                           | RefSpotDate                   | ********           |  |
| 7071                           | ProductType                   | FX-STD             |  |
| 146                            | NoRelatedSym                  | 1                  |  |
| 55                             | Symbol                        | EUR/USD            |  |
| 537                            | QuoteType                     | 1                  |  |
| 54                             | Side                          | 1                  |  |
| 38                             | OrderQty                      | 1000000            |  |
| 64                             | SettlDate                     | ********           |  |
| 15                             | Currency                      | EUR                |  |
| 1                              | Account                       | ACME               |  |
| 126                            | ExpireTime                    | ********-12:13:55  |  |
| <message trailer=""></message> |                               |                    |  |

Table 8.3: Example QuoteRequest for Spot quotes

### <span id="page-57-2"></span>**8.2.2 Tradeable Quotes: Forward**

This is an example QuoteRequest message. The client wants to buy 1 million EUR against USD. Since the settlement date is different from RefSpotDate, this request is for forward quotes.

<span id="page-57-4"></span>

| Tag                           | Attribute    | Value                  |
|-------------------------------|--------------|------------------------|
| <message header=""></message> |              |                        |
| 35                            | MsgType      | R                      |
| 131                           | QuoteReqID   | MyQuoteReqID-12345     |
| 7070                          | RefSpotDate  | ********               |
| 7071                          | ProductType  | FX-STD                 |
| 146                           | NoRelatedSym | 1                      |
| 55                            | Symbol       | EUR/USD                |
| 537                           | QuoteType    | 1                      |
| 54                            | Side         | 1                      |
|                               |              | continued on next page |

### 8.2. QuoteRequest [R] Chapter 8: Example Messages

| Tag                            | Attribute  | Value             |
|--------------------------------|------------|-------------------|
| 38                             | OrderQty   | 1000000           |
| 64                             | SettlDate  | ********          |
| 15                             | Currency   | EUR               |
| 1                              | Account    | ACME              |
| 126                            | ExpireTime | ********-12:13:55 |
| <message trailer=""></message> |            |                   |

Table 8.4: Example QuoteRequest for Forward quotes

### <span id="page-58-0"></span>**8.2.3 Tradeable Quotes: FX Time Options**

This is an example QuoteRequest message. The client wants to buy 1 million EUR against USD. Since the OptionDate are supplied, this request is for FX Time Options quotes.

<span id="page-58-2"></span>

| Tag                            | Attribute    | Value                         |
|--------------------------------|--------------|-------------------------------|
|                                |              | <message header=""></message> |
| 35                             | MsgType      | R                             |
| 131                            | QuoteReqID   | MyQuoteReqID-12345            |
| 7070                           | RefSpotDate  | ********                      |
| 7071                           | ProductType  | FX-STD                        |
| 146                            | NoRelatedSym | 1                             |
| 55                             | Symbol       | EUR/USD                       |
| 537                            | QuoteType    | 1                             |
| 54                             | Side         | 1                             |
| 38                             | OrderQty     | 1000000                       |
| 64                             | SettlDate    | ********                      |
| 15                             | Currency     | EUR                           |
| 1                              | Account      | ACME                          |
| 126                            | ExpireTime   | ********-12:13:55             |
| 9515                           | OptionDate   | ********                      |
| <message trailer=""></message> |              |                               |

Table 8.5: Example QuoteRequest for Forward quotes

### <span id="page-58-1"></span>**8.2.4 Tradeable Quotes: Swap**

This is an example QuoteRequest message. The client wants to buy 1 million EUR against USD on the near leg and sell the same amount on the far leg. Swaps always have SettlDate2 and OrderQty2 tags set.

<span id="page-59-1"></span>

| Tag  | Attribute                     | Value                          |  |
|------|-------------------------------|--------------------------------|--|
|      | <message header=""></message> |                                |  |
| 35   | MsgType                       | R                              |  |
| 131  | QuoteReqID                    | MyQuoteReqID-12345             |  |
| 7070 | RefSpotDate                   | ********                       |  |
| 7071 | ProductType                   | FX-STD                         |  |
| 146  | NoRelatedSym                  | 1                              |  |
| 55   | Symbol                        | EUR/USD                        |  |
| 537  | QuoteType                     | 1                              |  |
| 54   | Side                          | 2                              |  |
| 38   | OrderQty                      | 1000000                        |  |
| 64   | SettlDate                     | ********                       |  |
| 193  | SettlDate2                    | ********                       |  |
| 192  | OrderQty2                     | 1000000                        |  |
| 15   | Currency                      | EUR                            |  |
| 1    | Account                       | ACME                           |  |
| 126  | ExpireTime                    | ********-12:13:55              |  |
|      |                               | <message trailer=""></message> |  |

### Table 8.6: Example QuoteRequest for Swap quotes

### <span id="page-59-0"></span>**8.2.5 Tradeable Quotes: NDF**

This is an example QuoteRequest message for a Non-Deliverable-Forward. The client wants to buy 1 million EUR against RUB. The Fixing date (sent in the field MaturityDate) is 2 days before the settlement date.

<span id="page-59-2"></span>

| Tag  | Attribute                     | Value                  |  |  |
|------|-------------------------------|------------------------|--|--|
|      | <message header=""></message> |                        |  |  |
| 35   | MsgType                       | R                      |  |  |
| 131  | QuoteReqID                    | MyQuoteReqID-12345     |  |  |
| 7070 | RefSpotDate                   | ********               |  |  |
| 7071 | ProductType                   | FX-STD                 |  |  |
| 146  | NoRelatedSym                  | 1                      |  |  |
| 55   | Symbol                        | EUR/RUB                |  |  |
| 541  | MaturityDate                  | 20110808               |  |  |
| 537  | QuoteType                     | 1                      |  |  |
| 54   | Side                          | 1                      |  |  |
| 38   | OrderQty                      | 1000000                |  |  |
| 64   | SettlDate                     | ********               |  |  |
|      |                               | continued on next page |  |  |

| Tag                            | Attribute  | Value             |
|--------------------------------|------------|-------------------|
| 15                             | Currency   | EUR               |
| 1                              | Account    | ACME              |
| 126                            | ExpireTime | ********-12:13:55 |
| <message trailer=""></message> |            |                   |

Table 8.7: Example QuoteRequest for NDF quotes

### <span id="page-60-0"></span>**8.2.6 Tradeable Quotes: NDS**

This is an example QuoteRequest message for a Non-Deliverable-Swap. The client wants to buy 1 million EUR against RUB on the near leg and sell the same amount in the far leg. The Fixing dates (sent in the MaturityDate fields) is 2 days before the settlement dates for both of the legs.

<span id="page-60-2"></span>

| Tag  | Attribute                     | Value                          |  |
|------|-------------------------------|--------------------------------|--|
|      | <message header=""></message> |                                |  |
| 35   | MsgType                       | R                              |  |
| 131  | QuoteReqID                    | MyQuoteReqID-12345             |  |
| 7070 | RefSpotDate                   | ********                       |  |
| 7071 | ProductType                   | FX-STD                         |  |
| 146  | NoRelatedSym                  | 1                              |  |
| 55   | Symbol                        | EUR/RUB                        |  |
| 541  | MaturityDate                  | 20110808                       |  |
| 7541 | MaturityDate                  | ********                       |  |
| 537  | QuoteType                     | 1                              |  |
| 54   | Side                          | 1                              |  |
| 38   | OrderQty                      | 1000000                        |  |
| 64   | SettlDate                     | ********                       |  |
| 193  | SettlDate2                    | ********                       |  |
| 192  | OrderQty2                     | 1000000                        |  |
| 15   | Currency                      | EUR                            |  |
| 1    | Account                       | ACME                           |  |
| 126  | ExpireTime                    | ********-12:13:55              |  |
|      |                               | <message trailer=""></message> |  |

Table 8.8: Example QuoteRequest for NDS quotes

### <span id="page-60-1"></span>**8.2.7 Tradeable Quotes: FX Block Trade**

<span id="page-61-1"></span>

| Tag  | Attribute       | Value                                |
|------|-----------------|--------------------------------------|
|      |                 | <message header=""></message>        |
| 8    | BeginString     | FIX.4.4                              |
| 9    | BodyLength      | 382                                  |
| 35   | MsgType         | R                                    |
| 131  | QuoteReqID      | 4c5c455c-a2cc-4eef-8387-53f74e384454 |
| 7070 | RefSpotDate     | ********                             |
| 7071 | ProductType     | FX-BT                                |
| 146  | NoRelatedSym    | 1                                    |
| 55   | Symbol          | USD/BRL                              |
| 537  | QuoteType       | 1                                    |
| 54   | Side            | 1                                    |
| 38   | OrderQty        | 11000                                |
| 64   | SettlDate       | ********                             |
| 15   | Currency        | USD                                  |
| 1    | Account         | GroupE                               |
| 126  | ExpireTime      | ********-11:38:03.142                |
| 555  | NoLegs          | 2                                    |
| 600  | LegSymbol       | USD/BRL                              |
| 624  | LegSide         | 1                                    |
| 687  | LegQty          | 5000                                 |
| 670  | NoLegAllocs     | 1                                    |
| 671  | LegAllocAccount | GroupE                               |
| 673  | LegAllocQty     | 5000                                 |
| 654  | LegRefID        | 39b7001a-4508-4365-b139-f77c6c858959 |
| 588  | LegSettlDate    | ********                             |
| 600  | LegSymbol       | USD/BRL                              |
| 624  | LegSide         | 1                                    |
| 687  | LegQty          | 6000                                 |
| 670  | NoLegAllocs     | 1                                    |
| 671  | LegAllocAccount | GroupE                               |
| 673  | LegAllocQty     | 6000                                 |
| 654  | LegRefID        | 53dcd8e7-092a-4ba1-bcc3-62fe3b815711 |
| 588  | LegSettlDate    | ********                             |

Table 8.9: Example QuoteRequest for FX Block Trade quotes

### <span id="page-61-0"></span>**8.2.8 Tradeable Quotes: NDF Block Trade**

<span id="page-62-1"></span>

| Tag  | Attribute       | Value                                |
|------|-----------------|--------------------------------------|
|      |                 | <message header=""></message>        |
| 35   | MsgType         | R                                    |
| 131  | QuoteReqID      | c63f07a1b8d4                         |
| 7070 | RefSpotDate     | ********                             |
| 7071 | ProductType     | FX-BT                                |
| 146  | NoRelatedSym    | 1                                    |
| 55   | Symbol          | USD/BRL                              |
| 106  | Issuer          | JPMORGAN.DEMO                        |
| 537  | QuoteType       | 1                                    |
| 54   | Side            | 1                                    |
| 38   | OrderQty        | 11000                                |
| 64   | SettlDate       | ********                             |
| 15   | Currency        | USD                                  |
| 1    | Account         | GroupE                               |
| 555  | NoLegs          | 2                                    |
| 600  | LegSymbol       | USD/BRL                              |
| 611  | LegMaturityDate | ********                             |
| 624  | LegSide         | 1                                    |
| 687  | LegQty          | 5000                                 |
| 670  | NoLegAllocs     | 1                                    |
| 671  | LegAllocAccount | GroupE                               |
| 673  | LegAllocQty     | 5000                                 |
| 654  | LegRefID        | e5b9f2d4-26fb-4c37-af32-52a6193b1097 |
| 588  | LegSettlDate    | ********                             |
| 600  | LegSymbol       | USD/BRL                              |
| 611  | LegMaturityDate | ********                             |
| 624  | LegSide         | 1                                    |
| 687  | LegQty          | 6000                                 |
| 670  | NoLegAllocs     | 1                                    |
| 671  | LegAllocAccount | GroupE                               |
| 673  | LegAllocQty     | 6000                                 |
| 654  | LegRefID        | 467dd1e3-8446-4b5a-b380-ebb04c8e13e2 |
| 588  | LegSettlDate    | ********                             |

Table 8.10: Example QuoteRequest for NDF Block Trade quotes

### <span id="page-62-0"></span>**8.2.9 Tradeable Quotes: Loan/Deposit**

<span id="page-63-1"></span>

| Tag  | Attribute                     | Value        |  |
|------|-------------------------------|--------------|--|
|      | <message header=""></message> |              |  |
| 35   | MsgType                       | R            |  |
| 131  | QuoteReqID                    | c63f07a1b8d4 |  |
| 7071 | ProductType                   | MM           |  |
| 7072 | DayCount                      | ACT/360      |  |
| 146  | NoRelatedSym                  | 1            |  |
| 55   | Symbol                        | EUR          |  |
| 537  | QuoteType                     | 1            |  |
| 54   | Side                          | G            |  |
| 38   | OrderQty                      | 11000        |  |
| 64   | SettlDate                     | ********     |  |
| 541  | MaturityDate                  | ********     |  |
| 15   | Currency                      | EUR          |  |
| 1    | Account                       | GroupE       |  |

Table 8.11: Example QuoteRequest for Loan/Deposit quotes

### <span id="page-63-0"></span>**8.2.10 Request for Market data (FORWARD)**

Quote Requests for Market data are simpler that those for tradeable quotes. QuoteType is '0', OrderQty is 0 and Currency is not needed. This market data feed will expire at the time specified in ExpireTime.

<span id="page-63-2"></span>

| Tag                            | Attribute                     | Value              |  |
|--------------------------------|-------------------------------|--------------------|--|
|                                | <message header=""></message> |                    |  |
| 35                             | MsgType                       | R                  |  |
| 131                            | QuoteReqID                    | MyQuoteReqID-12345 |  |
| 7070                           | RefSpotDate                   | ********           |  |
| 7071                           | ProductType                   | FX-STD             |  |
| 146                            | NoRelatedSym                  | 1                  |  |
| 55                             | Symbol                        | EUR/RUB            |  |
| 537                            | QuoteType                     | 0                  |  |
| 54                             | Side                          | 1                  |  |
| 38                             | OrderQty                      | 0                  |  |
| 64                             | SettlDate                     | ********           |  |
| 1                              | Account                       | ACME               |  |
| 126                            | ExpireTime                    | ********-18:00:00  |  |
| <message trailer=""></message> |                               |                    |  |
| continued on next page         |                               |                    |  |

8.3. QuoteRequestReject [AG] Chapter 8: Example Messages

|  | Tag | Attribute | Value |
|--|-----|-----------|-------|
|--|-----|-----------|-------|

Table 8.12: Example QuoteRequest for Spot Market data quotes

Market Data requests for other products are similar to this one. Note that for swap market data requests, OrderQty2 should be set to 0, too.

## <span id="page-64-0"></span>**8.3 QuoteRequestReject [AG]**

A QuoteRequestReject message might look like this:

<span id="page-64-2"></span>

| Tag                            | Attribute                | Value                      |
|--------------------------------|--------------------------|----------------------------|
| <message header=""></message>  |                          |                            |
| 35                             | QuoteReqID               | AG                         |
| 131                            | QuoteReqID               | MyQuoteReqID-12345         |
| 658                            | QuoteRequestRejectReason | 99                         |
| 146                            | NoRelatedSym             | 1                          |
| 55                             | Symbol                   | EUR/USD                    |
| 15                             | Currency                 | EUR                        |
| 7071                           | ProductType              | FX-STD                     |
| 58                             | Text                     | ExpireTime is in the past. |
| <message trailer=""></message> |                          |                            |

Table 8.13: Example BusinessMessageReject message

### <span id="page-64-1"></span>**8.4 BusinessMessageReject [j]**

A BusinessMessageReject message might look like this:

<span id="page-64-3"></span>

| Tag                            | Attribute            | Value                                   |
|--------------------------------|----------------------|-----------------------------------------|
| <message header=""></message>  |                      |                                         |
| 35                             | MsgType              | j                                       |
| 45                             | RefSeqNr             | 45                                      |
| 58                             | Text                 | Exception while processing the request. |
| 372                            | RefMsgType           | R                                       |
| 379                            | BusinessRejectRefID  | MyQuoteReqID-12345                      |
| 380                            | BusinessRejectReason | 0                                       |
| <message trailer=""></message> |                      |                                         |

Table 8.14: Example BusinessMessageReject message

## <span id="page-65-0"></span>**8.5 Quote [S]**

### <span id="page-65-1"></span>**8.5.1 Spot Quote - Buy**

<span id="page-65-3"></span>

| Tag                            | Attribute                     | Value                   |  |
|--------------------------------|-------------------------------|-------------------------|--|
|                                | <message header=""></message> |                         |  |
| 35                             | MsgType                       | S                       |  |
| 1                              | Account                       | ACME                    |  |
| 15                             | Currency                      | EUR                     |  |
| 38                             | OrderQty                      | 1000000                 |  |
| 55                             | Symbol                        | EUR/USD                 |  |
| 106                            | Issuer                        | Barclays BARX.DEMO      |  |
| 117                            | QuoteID                       | MyQuoteReq-98765-000001 |  |
| 131                            | QuoteReqID                    | MyQuoteReq-98765        |  |
| 190                            | OfferSpotRate                 | 1.4051                  |  |
| 7070                           | RefSpotDate                   | ********                |  |
| <message trailer=""></message> |                               |                         |  |

### Table 8.15: Example Spot quote

### <span id="page-65-2"></span>**8.5.2 Forward Quote - Two-Way**

Forward Quotes differ from Spot quotes by having OfferPx/BidPx set in addition to OfferSpotRate/BidSpotRate.

<span id="page-65-4"></span>

| Tag  | Attribute                     | Value                   |  |
|------|-------------------------------|-------------------------|--|
|      | <message header=""></message> |                         |  |
| 35   | MsgType                       | S                       |  |
| 1    | Account                       | ACME                    |  |
| 15   | Currency                      | EUR                     |  |
| 38   | OrderQty                      | 1000000                 |  |
| 55   | Symbol                        | EUR/USD                 |  |
| 106  | Issuer                        | Barclays BARX.DEMO      |  |
| 117  | QuoteID                       | MyQuoteReq-98765-000002 |  |
| 131  | QuoteReqID                    | MyQuoteReq-98765        |  |
| 132  | BidPx                         | 1.3995                  |  |
| 133  | OfferPx                       | 1.4070                  |  |
| 188  | BidSpotRate                   | 1.4007                  |  |
| 190  | OfferSpotRate                 | 1.4051                  |  |
| 7070 | RefSpotDate                   | ********                |  |
|      |                               | continued on next page  |  |

8.5. Quote [S] Chapter 8: Example Messages

| Tag                            | Attribute | Value |
|--------------------------------|-----------|-------|
| <message trailer=""></message> |           |       |

Table 8.16: Example Forward quote

### <span id="page-66-0"></span>**8.5.3 FX Time Options quote - Two-Way**

FX Time Options Quotes differ from Forward quotes by having OptionDate and optionally OptionPeriod in addition.

<span id="page-66-2"></span>

| Tag                            | Attribute     | Value                   |
|--------------------------------|---------------|-------------------------|
| <message header=""></message>  |               |                         |
| 35                             | MsgType       | S                       |
| 1                              | Account       | ACME                    |
| 15                             | Currency      | EUR                     |
| 38                             | OrderQty      | 1000000                 |
| 55                             | Symbol        | EUR/USD                 |
| 106                            | Issuer        | Barclays BARX.DEMO      |
| 117                            | QuoteID       | MyQuoteReq-98765-000003 |
| 131                            | QuoteReqID    | MyQuoteReq-98765        |
| 132                            | BidPx         | 1.3995                  |
| 133                            | OfferPx       | 1.4070                  |
| 188                            | BidSpotRate   | 1.4007                  |
| 190                            | OfferSpotRate | 1.4051                  |
| 7070                           | RefSpotDate   | ********                |
| 9515                           | OptionDate    | ********                |
| <message trailer=""></message> |               |                         |

### Table 8.17: Example Forward quote

### <span id="page-66-1"></span>**8.5.4 Swap Quote - Two-Way**

In Addition to Forward quotes, Swap quotes contain data for the far leg, too.

<span id="page-66-3"></span>

| Tag                           | Attribute | Value                  |
|-------------------------------|-----------|------------------------|
| <message header=""></message> |           |                        |
| 35                            | MsgType   | S                      |
| 1                             | Account   | ACME                   |
| 15                            | Currency  | EUR                    |
| 38                            | OrderQty  | 1000000                |
|                               |           | continued on next page |

| Tag                            | Attribute     | Value                   |
|--------------------------------|---------------|-------------------------|
| 55                             | Symbol        | EUR/USD                 |
| 106                            | Issuer        | Barclays BARX.DEMO      |
| 117                            | QuoteID       | MyQuoteReq-98765-000004 |
| 131                            | QuoteReqID    | MyQuoteReq-98765        |
| 132                            | BidPx         | 1.416989                |
| 133                            | OfferPx       | 1.1.417064              |
| 188                            | BidSpotRate   | 1.4172                  |
| 190                            | OfferSpotRate | 1.4173                  |
| 6050                           | BidPx2        | 1.4168577               |
| 6051                           | OfferPx       | 1.4169421               |
| 7070                           | RefSpotDate   | ********                |
| <message trailer=""></message> |               |                         |

Table 8.18: Example Forward quote

### <span id="page-67-0"></span>**8.5.5 NDF quote - Two-Way**

NDF quotes look exactly like Forward quotes.

### <span id="page-67-1"></span>**8.5.6 NDS quote - Two-Way**

NDS quotes look exactly like Swap quotes.

### <span id="page-67-2"></span>**8.5.7 Market Data quote - Forward, Two-Way**

MarketData quotes are marked by QuoteType '0'. They always have OrderQty set to 0 and the constant string "MarketData" as Issuer. Also the field Currency is not set.

<span id="page-67-3"></span>

| Tag                           | Attribute     | Value                   |
|-------------------------------|---------------|-------------------------|
| <message header=""></message> |               |                         |
| 35                            | MsgType       | S                       |
| 38                            | OrderQty      | 0                       |
| 55                            | Symbol        | EUR/USD                 |
| 106                           | Issuer        | MarketData              |
| 117                           | QuoteID       | MyQuoteReq-98765-000005 |
| 131                           | QuoteReqID    | MyQuoteReq-98765        |
| 132                           | BidPx         | 1.416989                |
| 133                           | OfferPx       | 1.1.417064              |
| 188                           | BidSpotRate   | 1.4172                  |
| 190                           | OfferSpotRate | 1.4173                  |
|                               |               | continued on next page  |

| Tag                            | Attribute   | Value    |
|--------------------------------|-------------|----------|
| 537                            | QuoteType   | 0        |
| 7070                           | RefSpotDate | ******** |
| <message trailer=""></message> |             |          |

Table 8.19: Example Forward Market data quote

## <span id="page-68-0"></span>**8.6 QuoteCancel [Z]**

### <span id="page-68-1"></span>**8.6.1 Customer** → **360**

<span id="page-68-5"></span>

| Tag                            | Attribute       | Value                    |
|--------------------------------|-----------------|--------------------------|
| <message header=""></message>  |                 |                          |
| 35                             | MsgType         | Z                        |
| 117                            | QuoteID         | 0                        |
| 131                            | QuoteReqID      | LO-EUR/JPY91312851372795 |
| 298                            | QuoteCancelType | 4                        |
| 7071                           | ProductType     | FX-STD                   |
| <message trailer=""></message> |                 |                          |

Table 8.20: Example QuoteCancel message from customer

### <span id="page-68-2"></span>**8.6.2 Customer** ← **360T**

<span id="page-68-6"></span>

| Tag                            | Attribute       | Value                                           |
|--------------------------------|-----------------|-------------------------------------------------|
| <message header=""></message>  |                 |                                                 |
| 35                             | MsgType         | Z                                               |
| 117                            | QuoteID         | fd8d67a1-8eae-4195-946b-d1306bcaa2c4-<br>000001 |
| 131                            | QuoteReqID      | fd8d67a1-8eae-4195-946b-d1306bcaa2c4            |
| 298                            | QuoteCancelType | 1                                               |
| 7071                           | ProductType     | FX-STD                                          |
| <message trailer=""></message> |                 |                                                 |

Table 8.21: Example QuoteCancel message from 360T

## <span id="page-68-3"></span>**8.7 NewOrderSingle [D]**

### <span id="page-68-4"></span>**8.7.1 Previously Quoted - Forward**

Customer wants to buy 100000 EUR against JPY as a forward on a quote from RBS.LND which he received earlier.

<span id="page-69-1"></span>**Tag Attribute Value** <Message Header> MsgType D Account ACME ClOrdID LO-0190CM00011208L0000002009 Currency EUR OrderQty 100000 OrdType D Side 1 Symbol EUR/JPY TransactTime ********-14:13:54.710 SettlDate ******** Issuer RBS.LND QuoteID q77t54rg[2364]a05l\$94

Table 8.22: Example NewOrderSingle message (forward)

<Message Trailer>

### <span id="page-69-0"></span>**8.7.2 Previously Quoted - FX Time Option**

Customer wants to buy 100000 EUR against JPY as a FX Time Option on a quote from RBS.LND which he received earlier.

<span id="page-69-2"></span>

| Tag                            | Attribute                     | Value                        |  |
|--------------------------------|-------------------------------|------------------------------|--|
|                                | <message header=""></message> |                              |  |
| 35                             | MsgType                       | D                            |  |
| 1                              | Account                       | ACME                         |  |
| 11                             | ClOrdID                       | LO-0190CM00011208L0000002009 |  |
| 15                             | Currency                      | EUR                          |  |
| 38                             | OrderQty                      | 100000                       |  |
| 40                             | OrdType                       | D                            |  |
| 54                             | Side                          | 1                            |  |
| 55                             | Symbol                        | EUR/JPY                      |  |
| 60                             | TransactTime                  | ********-14:13:54.710        |  |
| 64                             | SettlDate                     | ********                     |  |
| 106                            | Issuer                        | RBS.LND                      |  |
| 117                            | QuoteID                       | q77t54rg[2364]a05l\$94       |  |
| 9515                           | OptionDate                    | ********                     |  |
| <message trailer=""></message> |                               |                              |  |
|                                |                               | continued on next page       |  |

8.7. NewOrderSingle [D] Chapter 8: Example Messages

| Tag<br>Attribute<br>Value |
|---------------------------|
|---------------------------|

Table 8.23: Example NewOrderSingle message (forward)

### <span id="page-70-0"></span>**8.7.3 Previously Quoted - Swap**

Customer wants to buy EUR against JPY as a swap. On the near leg he sells 100000 EUR and buys them again on the far leg. Thus OrderQty2 and SettlDate2 must be set. Side is 1 as the client buys on the far leg.

<span id="page-70-2"></span>

| Tag                           | Attribute                      | Value                  |  |
|-------------------------------|--------------------------------|------------------------|--|
| <message header=""></message> |                                |                        |  |
| 35                            | MsgType                        | D                      |  |
| 1                             | Account                        | ACME                   |  |
| 11                            | ClOrdID                        | jhfsadf-fsa            |  |
| 15                            | Currency                       | EUR                    |  |
| 38                            | OrderQty                       | 100000                 |  |
| 40                            | OrdType                        | D                      |  |
| 54                            | Side                           | 1                      |  |
| 55                            | Symbol                         | EUR/JPY                |  |
| 60                            | TransactTime                   | ********-04:56:12.441  |  |
| 64                            | SettlDate                      | ********               |  |
| 106                           | Issuer                         | RBS.LND                |  |
| 117                           | QuoteID                        | q77t54rg[2364]a05l\$94 |  |
| 192                           | OrderQty2                      | 100000                 |  |
| 193                           | SettleDate2                    | 20110901               |  |
|                               | <message trailer=""></message> |                        |  |

Table 8.24: Example NewOrderSingle message (swap)

### <span id="page-70-1"></span>**8.7.4 Previously Quoted - NDF**

Customer wants to buy EUR against JPY as an NDF. Therefore the maturity date must be sent with the order message.

<span id="page-70-3"></span>

| Tag                           | Attribute | Value                  |  |
|-------------------------------|-----------|------------------------|--|
| <message header=""></message> |           |                        |  |
| 35                            | MsgType   | D                      |  |
| 1                             | Account   | ACME                   |  |
| 11                            | ClOrdID   | jhfsadf-fsa            |  |
| 15                            | Currency  | EUR                    |  |
|                               |           | continued on next page |  |

### 8.7. NewOrderSingle [D] Chapter 8: Example Messages

| Tag                            | Attribute    | Value                  |
|--------------------------------|--------------|------------------------|
| 38                             | OrderQty     | 100000                 |
| 40                             | OrdType      | D                      |
| 54                             | Side         | 1                      |
| 55                             | Symbol       | EUR/JPY                |
| 541                            | MaturityDate | ********               |
| 60                             | TransactTime | ********-08:11:33.071  |
| 64                             | SettlDate    | ********               |
| 106                            | Issuer       | RBS.LND                |
| 117                            | QuoteID      | q77t54rg[2364]a05l\$94 |
| <message trailer=""></message> |              |                        |

Table 8.25: Example NewOrderSingle message (NDF)

### <span id="page-71-0"></span>**8.7.5 Previously Quoted - NDS**

Customer wants to buy EUR against JPY as an NDS. Therefore the maturity dates must be sent with the order message.

<span id="page-71-1"></span>

| Tag                           | Attribute     | Value                          |  |
|-------------------------------|---------------|--------------------------------|--|
| <message header=""></message> |               |                                |  |
| 35                            | MsgType       | D                              |  |
| 1                             | Account       | ACME                           |  |
| 11                            | ClOrdID       | jhfsadf-fsa                    |  |
| 15                            | Currency      | EUR                            |  |
| 38                            | OrderQty      | 100000                         |  |
| 40                            | OrdType       | D                              |  |
| 54                            | Side          | 1                              |  |
| 55                            | Symbol        | EUR/RUB                        |  |
| 541                           | MaturityDate  | ********                       |  |
| 7541                          | MaturityDate2 | ********                       |  |
| 60                            | TransactTime  | ********-04:56:12.441          |  |
| 64                            | SettlDate     | ********                       |  |
| 106                           | Issuer        | RBS.LND                        |  |
| 117                           | QuoteID       | q77t54rg[2364]a05l\$94         |  |
| 192                           | OrderQty2     | 100000                         |  |
| 193                           | SettleDate2   | 20110901                       |  |
|                               |               | <message trailer=""></message> |  |
|                               |               | continued on next page         |  |

8.7. NewOrderSingle [D] Chapter 8: Example Messages

| Tag<br>Attribute | Value |
|------------------|-------|
|------------------|-------|

Table 8.26: Example NewOrderSingle message (swap)

### <span id="page-72-0"></span>**8.7.6 Market order - Forward**

Customer wants to buy 100000 EUR against JPY as a market order (OrdType 1). Instead of a QuoteID, the QuotRequestID is sent.

<span id="page-72-2"></span>

| Tag                            | Attribute                     | Value                        |  |  |
|--------------------------------|-------------------------------|------------------------------|--|--|
|                                | <message header=""></message> |                              |  |  |
| 35                             | MsgType                       | D                            |  |  |
| 1                              | Account                       | ACME                         |  |  |
| 11                             | ClOrdID                       | LO-0190CM00011208L0000002009 |  |  |
| 15                             | Currency                      | EUR                          |  |  |
| 38                             | OrderQty                      | 100000                       |  |  |
| 40                             | OrdType                       | 1                            |  |  |
| 54                             | Side                          | 1                            |  |  |
| 55                             | Symbol                        | EUR/JPY                      |  |  |
| 60                             | TransactTime                  | ********-14:13:54.710        |  |  |
| 64                             | SettlDate                     | ********                     |  |  |
| 117                            | QuoteID                       | 321423-43241-55432-12342     |  |  |
| <message trailer=""></message> |                               |                              |  |  |

Table 8.27: Example NewOrderSingle message (Market order)

### <span id="page-72-1"></span>**8.7.7 Limit order - Forward**

Customer wants to buy 100000 EUR against USD as a Limit order. The limit price is 1.4.

<span id="page-72-3"></span>

| Tag                           | Attribute | Value                        |  |
|-------------------------------|-----------|------------------------------|--|
| <message header=""></message> |           |                              |  |
| 35                            | MsgType   | D                            |  |
| 1                             | Account   | ACME                         |  |
| 11                            | ClOrdID   | LO-0190CM00011208L0000002009 |  |
| 15                            | Currency  | EUR                          |  |
| 38                            | OrderQty  | 100000                       |  |
| 40                            | OrdType   | 2                            |  |
| 44                            | Price     | 1.4                          |  |
| 54                            | Side      | 1                            |  |
|                               |           | continued on next page       |  |

| Tag                            | Attribute    | Value                    |
|--------------------------------|--------------|--------------------------|
| 55                             | Symbol       | EUR/USD                  |
| 60                             | TransactTime | ********-14:13:54.710    |
| 64                             | SettlDate    | ********                 |
| 117                            | QuoteID      | 321423-43241-55432-12342 |
| <message trailer=""></message> |              |                          |

Table 8.28: Example NewOrderSingle message (Limit order)

## <span id="page-73-0"></span>**8.8 NewOrderMultileg [AB]**

### <span id="page-73-1"></span>**8.8.1 Previously Quoted - FX Block Trade**

<span id="page-73-2"></span>

| Tag  | Attribute    | Value                                       |
|------|--------------|---------------------------------------------|
|      |              | <message header=""></message>               |
| 35   | MsgType      | AB                                          |
| 1    | Account      | GroupE                                      |
| 11   | ClOrdID      | b7gt95-v                                    |
| 15   | Currency     | USD                                         |
| 38   | OrderQty     | 11000                                       |
| 40   | OrdType      | D                                           |
| 54   | Side         | 1                                           |
| 55   | Symbol       | USD/BRL                                     |
| 60   | TransactTime | ********-11:37:05.635                       |
| 64   | SettlDate    | ********                                    |
| 106  | Issuer       | Morgan Stanley.TEST                         |
| 117  | QuoteID      | 4c5c455c-a2cc-4eef-8387-53f74e384454-000003 |
| 7071 | ProductType  | FX-BT                                       |
| 555  | NoLegs       | 2                                           |
| 600  | LegSymbol    | USD/BRL                                     |
| 624  | LegSide      | 1                                           |
| 687  | LegQty       | 5000.0                                      |
| 588  | LegSettlDate | ********                                    |
| 566  | LegPrice     | 5.1954                                      |
| 654  | LegRefID     | 39b7001a-4508-4365-b139-f77c6c858959        |
| 600  | LegSymbol    | USD/BRL                                     |
| 624  | LegSide      | 1                                           |
|      |              | continued on next page                      |

| Tag | Attribute    | Value                                |
|-----|--------------|--------------------------------------|
| 687 | LegQty       | 6000.0                               |
| 588 | LegSettlDate | ********                             |
| 566 | LegPrice     | 5.2194                               |
| 654 | LegRefID     | 53dcd8e7-092a-4ba1-bcc3-62fe3b815711 |

Table 8.29: Example NewOrderMultileg message (FX Block Trade)

### <span id="page-74-0"></span>**8.8.2 Market order - NDF Block Trade**

<span id="page-74-1"></span>

| Tag  | Attribute       | Value                                |
|------|-----------------|--------------------------------------|
|      |                 | <message header=""></message>        |
| 35   | MsgType         | AB                                   |
| 1    | Account         | GroupE                               |
| 11   | ClOrdID         | Test-snangarath-9dff46273b45         |
| 15   | Currency        | USD                                  |
| 38   | OrderQty        | 11000                                |
| 40   | OrdType         | D                                    |
| 54   | Side            | 1                                    |
| 55   | Symbol          | USD/BRL                              |
| 60   | TransactTime    | ********-11:37:05.635                |
| 64   | SettlDate       | ********                             |
| 106  | Issuer          | JPMORGAN.DEMO                        |
| 117  | QuoteID         | c63f07a1b8d4                         |
| 7071 | ProductType     | FX-BT                                |
| 555  | NoLegs          | 2                                    |
| 600  | LegSymbol       | USD/BRL                              |
| 611  | LegMaturityDate | ********                             |
| 624  | LegSide         | 1                                    |
| 687  | LegQty          | 5000                                 |
| 588  | LegSettlDate    | ********                             |
| 566  | LegPrice        | 5.1954                               |
| 654  | LegRefID        | e5b9f2d4-26fb-4c37-af32-52a6193b1097 |
| 600  | LegSymbol       | USD/BRL                              |
| 611  | LegMaturityDate | ********                             |
| 624  | LegSide         | 1                                    |
| 687  | LegQty          | 6000                                 |
|      |                 | continued on next page               |

| Tag | Attribute    | Value                                |
|-----|--------------|--------------------------------------|
| 588 | LegSettlDate | ********                             |
| 566 | LegPrice     | 5.2194                               |
| 654 | LegRefID     | 467dd1e3-8446-4b5a-b380-ebb04c8e13e2 |

Table 8.30: Example NewOrderMultileg message (NDF Block Trade)

## <span id="page-75-0"></span>**8.9 ExecutionReport [8]**

### <span id="page-75-1"></span>**8.9.1 Execution report - Order received**

This is an example execution report that is sent when 360T has received the order message. Note that the Price is still 0.

<span id="page-75-2"></span>

| Tag | Attribute                      | Value                                    |  |
|-----|--------------------------------|------------------------------------------|--|
|     | <message header=""></message>  |                                          |  |
| 35  | MsgType                        | 8                                        |  |
| 1   | Account                        | ACME                                     |  |
| 6   | AvgPx                          | 0                                        |  |
| 11  | ClOrdID                        | LO-0190CM00011208L0000002009             |  |
| 14  | CumQty                         | 0                                        |  |
| 15  | Currency                       | EUR                                      |  |
| 17  | ExecID                         | 0                                        |  |
| 32  | LastQty                        | 0                                        |  |
| 37  | OrderID                        | **********                               |  |
| 38  | OrderQty                       | 100000                                   |  |
| 39  | OrdStatus                      | 0                                        |  |
| 40  | OrdType                        | D                                        |  |
| 44  | Price                          | 0                                        |  |
| 54  | Side                           | 1                                        |  |
| 55  | Symbol                         | EUR/JPY                                  |  |
| 58  | Text                           | NewOrderSingle message has been received |  |
| 64  | SettlDate                      | ********                                 |  |
| 150 | ExecType                       | 0                                        |  |
| 151 | LeavesQty                      | 0                                        |  |
|     | <message trailer=""></message> |                                          |  |

Table 8.31: Example ExecutionReport message (confirmation of receipt)

### <span id="page-76-0"></span>**8.9.2 Execution report - Order executed**

Shortly after the received message, an execution message is sent to the customer with OrdStatus "Filled" and ExecType "Trade".

<span id="page-76-2"></span>

| Tag | Attribute                      | Value                        |  |  |
|-----|--------------------------------|------------------------------|--|--|
|     | <message header=""></message>  |                              |  |  |
| 35  | MsgType                        | 8                            |  |  |
| 1   | Account                        | ACME                         |  |  |
| 6   | AvgPx                          | 0                            |  |  |
| 11  | ClOrdID                        | LO-0190CM00011208L0000002009 |  |  |
| 14  | CumQty                         | 100000                       |  |  |
| 15  | Currency                       | EUR                          |  |  |
| 17  | ExecID                         | 9w3ffq-a0734-gr45t8ks-1dp    |  |  |
| 31  | LastPx                         | 108.85574                    |  |  |
| 32  | LastQty                        | 0                            |  |  |
| 37  | OrderID                        | **********                   |  |  |
| 38  | OrderQty                       | 100000                       |  |  |
| 39  | OrdStatus                      | 2                            |  |  |
| 40  | OrdType                        | D                            |  |  |
| 54  | Side                           | 1                            |  |  |
| 55  | Symbol                         | EUR/JPY                      |  |  |
| 60  | TransactTime                   | 20110805-16:10:32.804        |  |  |
| 64  | SettlDate                      | ********                     |  |  |
| 150 | ExecType                       | F                            |  |  |
| 151 | LeavesQty                      | 0                            |  |  |
| 194 | LastSpotRate                   | 104.23                       |  |  |
| 453 | NoPartyIDs                     | 1                            |  |  |
| 448 | PartyID                        | RBS.LND                      |  |  |
| 447 | PartyIDSource                  | D                            |  |  |
| 452 | PartyRole                      | 35                           |  |  |
|     | <message trailer=""></message> |                              |  |  |

Table 8.32: Example ExecutionReport message (execution confirmation)

## <span id="page-76-1"></span>**8.10 SecurityDefinitionRequest [c]**

An example SecurityDefinitionRequest message sent from customer to 360T asking for financial calender information for the EUR/USD currency pair.

<span id="page-77-1"></span>

| Tag                            | Attribute           | Value               |
|--------------------------------|---------------------|---------------------|
| <message header=""></message>  |                     |                     |
| 35                             | MsgType             | c                   |
| 320                            | SecurityReqID       | MySecDefReqID-12345 |
| 321                            | SecurityRequestType | 3                   |
| 55                             | Symbol              | EUR/USD             |
| <message trailer=""></message> |                     |                     |

Table 8.33: Example SecurityDefinitionRequest message

## <span id="page-77-0"></span>**8.11 SecurityDefinition [d]**

An example SecurityDefinition message sent from 360T to the customer. Note: the FX tenors featured in this example are not exhaustive - a smaller selection is used to reduce the length of the example message.

<span id="page-77-2"></span>

| Tag | Attribute                     | Value                         |  |
|-----|-------------------------------|-------------------------------|--|
|     | <message header=""></message> |                               |  |
| 35  | MsgType                       | d                             |  |
| 320 | SecurityReqID                 | MySecDefReqID-12345           |  |
| 322 | SecurityResponseID            | SD-aa9slo-a074c-j0m5o2fa-26n1 |  |
| 321 | SecurityRequestType           | 4                             |  |
| 711 | NoUnderlyings                 | 5                             |  |
| 311 | UnderlyingSymbol              | EUR/USD                       |  |
| 309 | UnderlyingSecurityID          | TD                            |  |
| 542 | UnderlyingMaturityDate        | 20170322                      |  |
| 307 | UnderlyingSecurityDesc        | TODAY                         |  |
| 311 | UnderlyingSymbol              | EUR/USD                       |  |
| 309 | UnderlyingSecurityID          | TM                            |  |
| 542 | UnderlyingMaturityDate        | 20170323                      |  |
| 307 | UnderlyingSecurityDesc        | TOMORROW                      |  |
| 311 | UnderlyingSymbol              | EUR/USD                       |  |
| 309 | UnderlyingSecurityID          | SP                            |  |
| 542 | UnderlyingMaturityDate        | 20170324                      |  |
| 307 | UnderlyingSecurityDesc        | SPOT                          |  |
| 311 | UnderlyingSymbol              | EUR/USD                       |  |
| 309 | UnderlyingSecurityID          | 1W                            |  |
| 542 | UnderlyingMaturityDate        | 20170331                      |  |
| 307 | UnderlyingSecurityDesc        | 1<br>WEEK                     |  |
|     |                               | continued on next page        |  |

| Tag                            | Attribute              | Value     |
|--------------------------------|------------------------|-----------|
| 311                            | UnderlyingSymbol       | EUR/USD   |
| 309                            | UnderlyingSecurityID   | 1Y        |
| 542                            | UnderlyingMaturityDate | 20180326  |
| 307                            | UnderlyingSecurityDesc | 1<br>YEAR |
| <message trailer=""></message> |                        |           |

Table 8.34: Example SecurityDefinition message

## <span id="page-79-0"></span>**9 Firewall Configuration**

For the connections we use the following IP addresses:

<span id="page-79-1"></span>

| Environment/<br>Production |                 | Integration |                |      |
|----------------------------|-----------------|-------------|----------------|------|
| Connection                 | IP              | Port        | IP             | Port |
| Internet<br>(plain)        | n/a             | n/a         | ************   | 7001 |
| Stunnel                    | *************   | 7001        | ************   | 7001 |
| Radianz                    | *************** | 7001        | ************** | 7001 |

Table 9.1: 360T IP addresses

## <span id="page-80-0"></span>**10 FIX Session Reset**

By default, a FIX session reset is performed according to the schedule defined in the following table.

<span id="page-80-1"></span>

| Setting             | Description                                                    | Default<br>Value |
|---------------------|----------------------------------------------------------------|------------------|
| Timezone            | Time zone to be used for the session schedule.                 | America/New York |
| Start Time          | Time of day that this FIX session becomes activated.           | 17:01:00         |
| End Time            | Time of day that this FIX session becomes deactivated.         | 17:00:00         |
| Start Day           | Starting day of week for the session.                          | Saturday         |
| End Day             | Ending day of week for the session.                            | Saturday         |
| Reset On Logon      | Sequence number is reset when recieving a logon request.       | Yes              |
| Reset On Logout     | Sequence number is reset to 1 after normal logout termination. | No               |
| Reset On Disconnect | Sequence number is reset to 1 after abnormal termination.      | Yes              |

Table 10.1: 360T FIX Session

## <span id="page-81-0"></span>**11 Appendix**

## <span id="page-81-1"></span>**11.1 CFI Codes**

CFI codes used are based on the ISO 10962 standard. Please note that with options, the defined CFI code is used to identify the type of the option (CALL/PUT) with relation to currency 1 as it's set in the 360T system. Due to ANNA-DSB normalization, the CFI code passed in this API may not match that of the ISIN provided.

<span id="page-81-3"></span>

| CFI<br>Code | Details                                                                                                                    |
|-------------|----------------------------------------------------------------------------------------------------------------------------|
| JFTXFP      | Fx Spot, Fx Forward and Fx Option with Delta Hedge Strategy, as well as spot and<br>forward legs of block trades and swaps |
| JFTXFC      | NDF products as well as legs of NDS                                                                                        |
| JFRXFP      | Fx Spot and Fx Forward with CNH                                                                                            |
| SFCXXP      | Fx Swap including forward swaps                                                                                            |
| SFCXXC      | NDS                                                                                                                        |
| HFTAVP      | Fx European Option with CALL on the first currency                                                                         |
| HFTDVP      | Fx European Option with PUT on the first currency                                                                          |
| HFTBVP      | Fx American Option with CALL on the first currency                                                                         |
| HFTEVP      | Fx American Option with PUT on the first currency                                                                          |
| MRCXXX      | Fx Option with Delta Hedge StrategyMoney and Market Deposit                                                                |

Table 11.1: Supported CFI codes.

## <span id="page-81-2"></span>**11.2 Fixing Sources for NDF/NDS**

<span id="page-81-4"></span>

| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                               |
|--------|-----------------------------------------------------------------------------------------------------|
| USDARS | 'ARS01', 'ARS02', 'EMTA (ARS05)'                                                                    |
| USDBRL | 'BRL01', 'BRL02', 'BRL03', 'BRL10', 'BRL11', 'PTAX (BRL09)', 'Pontos sobre<br>PTAX', 'NDF Asiatica' |
| USDCLP | 'CLP01', 'CLP02', 'CLP03', 'CLP04', 'CLP08', 'CLP09', 'CLPOBS (CLP10)'                              |
| USDCNY | 'SAEC (CNY01)'                                                                                      |
| USDCOP | 'COP01', 'COP TRM (COP02)', 'CO/COL3'                                                               |
| USDCRC | 'CRREB'                                                                                             |
| USDEGP | 'FEMF (EGP01)'                                                                                      |
| USDGHS | 'GHS TR (GHS03)'                                                                                    |
| USDIDR | 'JISDOR (IDR04)', 'ABSIRFIX01', 'INR01', 'RBIB'                                                     |
| USDINR | 'FBIL'                                                                                              |

| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                                                                                                                                                     |
|--------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| USDKES | 'KES TR (KES01)'                                                                                                                                                                                                          |
| USDKRW | 'KRW02', 'KFTC18'                                                                                                                                                                                                         |
| USDKZT | 'KZFXWA', 'KZT KASE (KZT01)'                                                                                                                                                                                              |
| USDMYR | 'MYR PPKM (MYR03)', 'MYR KL REF (MYR04)'                                                                                                                                                                                  |
| USDNGN | 'NGN NiFEX (NGN01)', 'NGN NAFEX (NGN03)'                                                                                                                                                                                  |
| USDPEN | 'PEN01', 'PEN02', 'PEN05'                                                                                                                                                                                                 |
| USDPHP | 'PHP01', 'PHP02', 'PHP03', 'PHP04', 'PDSPESO', 'PHP BAPPESO (PHP06)'                                                                                                                                                      |
| USDRSD | 'RSDFIX'                                                                                                                                                                                                                  |
| USDRUB | 'RUB01', 'RUB02', 'RUB MOEX (RUB05)'                                                                                                                                                                                      |
| USDTWD | 'TWD01', 'TWD02', 'TAIFX1 (TWD03)'                                                                                                                                                                                        |
| USDUAH | 'EMTAUAHFIX', 'EMTA UAH ISR (UAH02)'                                                                                                                                                                                      |
| USDVEF | 'VEB01'                                                                                                                                                                                                                   |
| USDVND | 'ABSIRFIX01'                                                                                                                                                                                                              |
| EURARS | 'EMTA (ARS05)-BFIX EUR L080', 'EMTA (ARS05)-BFIX EUR L130', 'EMTA<br>(ARS05)-BFIX EUR L160', 'EMTA (ARS05)-WMCo 8am LDN', 'EMTA (ARS05)-<br>WMCo 1pm LDN', 'EMTA (ARS05)-WMCo 4pm LDN'                                    |
| EURBRL | 'PTAX-BFIX EUR L080', 'PTAX-BFIX EUR L130', 'PTAX-BFIX EUR L160',<br>'PTAX-WMCo 8am LDN', 'PTAX-WMCo 1pm LDN', 'PTAX-WMCo 4pm LDN',<br>'NDF Asiatica', 'Pontos sobre PTAX'                                                |
| EURCLP | 'CLPOBS<br>(CLP10)-BFIX<br>EUR<br>L080',<br>'CLPOBS<br>(CLP10)-BFIX<br>EUR<br>L130',<br>'CLPOBS (CLP10)-BFIX EUR L160',<br>'CLPOBS (CLP10)-WMCo 8am LDN',<br>'CLPOBS (CLP10)-WMCo 1pm LDN', 'CLPOBS (CLP10)-WMCo 4pm LDN' |
| EURCNY | 'SAEC (CNY01)-BFIX EUR L080', 'SAEC (CNY01)-BFIX EUR L130', 'SAEC<br>(CNY01)-BFIX EUR L160', 'SAEC (CNY01)-WMCo 8am LDN', 'SAEC (CNY01)-<br>WMCo 1pm LDN', 'SAEC (CNY01)-WMCo 4pm LDN'                                    |
| EURCOP | 'TRM (COP02)-BFIX EUR L080',<br>'TRM (COP02)-BFIX EUR L130',<br>'TRM<br>(COP02)-BFIX EUR L160', 'TRM (COP02)-WMCo 8am LDN', 'TRM (COP02)-<br>WMCo 1pm LDN', 'TRM (COP02)-WMCo 4pm LDN'                                    |
| EURINR | 'FBIL', 'FBIL-BFIX EUR L080', 'FBIL-BFIX EUR L130', 'FBIL-BFIX EUR L160',<br>'FBIL-WMCo 8am LDN', 'FBIL-WMCo 1pm LDN', 'FBIL-WMCo 4pm LDN'                                                                                |
| EURIDR | 'JISDOR (IDR04)-BFIX EUR L080', 'JISDOR (IDR04)-BFIX EUR L130', 'JIS<br>DOR (IDR04)-BFIX EUR L160', 'JISDOR (IDR04)-WMCo 8am LDN', 'JISDOR<br>(IDR04)-WMCo 1pm LDN', 'JISDOR (IDR04)-WMCo 4pm LDN'                        |
| EURKRW | 'KFTC18-BFIX EUR L080', 'KFTC18-BFIX EUR L130', 'KFTC18-BFIX EUR<br>L160',<br>'KFTC18-WMCo 8am LDN', 'KFTC18-WMCo 1pm LDN', 'KFTC18-<br>WMCo 4pm LDN'                                                                     |
| EURKZT | 'KZFXWA-BFIX EUR L080', 'KZFXWA-BFIX EUR L130', 'KZFXWA-BFIX EUR<br>L160', 'KZFXWA-WMCo 8am LDN', 'KZFXWA-WMCo 1pm LDN', 'KZFXWA<br>WMCo 4pm LDN'                                                                         |

| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                                                                                                                                                  |
|--------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| EURMYR | 'ABSIRFIX01-BFIX EUR L080', 'ABSIRFIX01-BFIX EUR L130', 'ABSIRFIX01-<br>BFIX EUR L160', 'ABSIRFIX01-WMCo 8am LDN', 'ABSIRFIX01-WMCo 1pm<br>LDN', 'ABSIRFIX01-WMCo 4pm LDN'                                             |
| EURPEN | 'PEN05-BFIX EUR L080', 'PEN05-BFIX EUR L130', 'PEN05-BFIX EUR L160',<br>'PEN05-WMCo 8am LDN', 'PEN05-WMCo 1pm LDN', 'PEN05-WMCo 4pm LDN'                                                                               |
| EURPHP | 'PDSPESO-BFIX EUR L080', 'PDSPESO-BFIX EUR L130', 'PDSPESO-BFIX<br>EUR<br>L160',<br>'PDSPESO-WMCo<br>8am<br>LDN',<br>'PDSPESO-WMCo<br>1pm<br>LDN',<br>'PDSPESO-WMCo 4pm LDN'                                           |
| EURRUB | 'RUB MOEX (RUB05)-BFIX EUR L080', 'RUB MOEX (RUB05)-BFIX EUR<br>L130', 'RUB MOEX (RUB05)-BFIX EUR L160', 'RUB MOEX (RUB05)-WMCo<br>8am LDN', 'RUB MOEX (RUB05)-WMCo 1pm LDN', 'RUB MOEX (RUB05)-<br>WMCo 4pm LDN'      |
| EURTWD | 'TAIFX1<br>(TWD03)-BFIX<br>EUR<br>L080',<br>'TAIFX1<br>(TWD03)-BFIX<br>EUR L130',<br>'TAIFX1 (TWD03)-BFIX EUR L160',<br>'TAIFX1 (TWD03)-WMCo 8am LDN',<br>'TAIFX1 (TWD03)-WMCo 1pm LDN', 'TAIFX1 (TWD03)-WMCo 4pm LDN' |
| GBPARS | 'EMTA (ARS05)-WMCo 8am LDN', 'EMTA (ARS05)-WMCo 1pm LDN', 'EMTA<br>(ARS05)-WMCo 4pm LDN', 'EMTA (ARS05)-BFIX GBP L080'                                                                                                 |
| GBPBRL | 'PTAX-WMCo 8am LDN', 'PTAX-WMCo 1pm LDN', 'PTAX-WMCo 4pm LDN',<br>'PTAX-BFIX GBP L080', 'Pontos sobre PTAX', 'NDF Asiatica'                                                                                            |
| GBPCLP | 'CLPOBS-WMCo 8am LDN', 'CLPOBS-WMCo 1pm LDN', 'CLPOBS-WMCo<br>4pm LDN', 'CLOPBS-BFIX GBP L080'                                                                                                                         |
| GBPCNY | 'SAEC-WMCo 8am LDN', 'SAEC-WMCo 1pm LDN', 'SAEC-WMCo 4pm LDN',<br>'SAEC-BFIX GBP L080'                                                                                                                                 |
| GBPCOP | 'COP TRM-WMCo 8am LDN', 'COP TRM-WMCo 1pm LDN', 'COP TRM-WMCo<br>4pm LDN', 'COP TRM-BFIX GBP L080'                                                                                                                     |
| GBPEGP | 'FEMF-WMCo 8am LDN', 'FEMF-WMCo 1pm LDN', 'FEMF-WMCo 4pm LDN',<br>'FEMF-BFIX GBP L080'                                                                                                                                 |
| GBPIDR | 'JISDOR-WMCo 8am LDN', 'JISDOR-WMCo 1pm LDN', 'JISDOR-WMCo 4pm<br>LDN', 'JISDOR-BFIX GBP L080'                                                                                                                         |
| GBPINR | 'FBIL', 'FBIL-WMCo 8am LDN', 'FBIL-WMCo 1pm LDN', 'FBIL-WMCo 4pm<br>LDN', 'FIBL-BFIX GBP L080'                                                                                                                         |
| GBPKRW | 'KFTC18-WMCo 8am LDN', 'KFTC18-WMCo 1pm LDN', 'KFTC18-WMCo 4pm<br>LDN', 'KFTC18-BFIX GBP L080'                                                                                                                         |
| GBPKZT | 'KZFXWA-WMCo 8am LDN', 'KZFXWA-WMCo 1pm LDN', 'KZFXWA-WMCo<br>4pm LDN', 'KZFXWA-BFIX GBP L080'                                                                                                                         |
| GBPMYR | 'MYR PPKM-WMCo 8am LDN', 'MYR PPKM-WMCo 1pm LDN', 'MYR PPKM<br>WMCo 4pm LDN', 'MYR PPKM-BFIX GBP L080'                                                                                                                 |
| GBPPEN | 'PEN05-WMCo 8am LDN', 'PEN05-WMCo 1pm LDN', 'PEN05-WMCo 4pm<br>LDN', 'PEN05-BFIX GBP L080'                                                                                                                             |
| GBPPHP | 'PDSPESO-WMCo 8am LDN', 'PDSPESO-WMCo 1pm LDN', 'PDSPESO-WMCo<br>4pm LDN', 'PDSPESO-BFIX GBP L080'                                                                                                                     |
| GBPRSD | 'RSDFIX-WMCo 8am LDN', 'RSDFIX-WMCo 1pm LDN', 'RSDFIX-WMCo 4pm<br>LDN', 'RSDFIX-BFIX GBP L080'                                                                                                                         |

![](_page_84_Picture_0.jpeg)

| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                                                                     |
|--------|-------------------------------------------------------------------------------------------------------------------------------------------|
| GBPRUB | 'RUB MOEX (RUB05)-WMCo 8am LDN', 'RUB MOEX (RUB05)-WMCo 1pm<br>LDN', 'RUB MOEX (RUB05)-WMCo 4pm LDN', 'RUB MOEX (RUB05)-BFIX<br>GBP L080' |
| GBPTWD | 'TAIFX1-WMCo 8am LDN', 'TAIFX1-WMCo 1pm LDN', 'TAIFX1-WMCo 4pm<br>LDN', 'TAIFX1-BFIX GBP L080'                                            |
| GBPUAH | 'EMTAUAHFUIX-WMCo<br>8am<br>LDN',<br>'EMTAUAHFUIX-WMCo<br>1pm<br>LDN',<br>'EMTAUAHFUIX-WMCo 4pm LDN', 'EMTAUAHFUIX-BFIX GBP L080'         |
| GBPVES | 'VEB01-WMCo 8am LDN', 'VEB01-WMCo 1pm LDN', 'VEB01-WMCo 4pm<br>LDN', 'VEB01-BFIX GBP L080'                                                |
| GBPVND | 'ABSIRFIX01-WMCo<br>8am<br>LDN',<br>'ABSIRFIX01-WMCo<br>1pm<br>LDN',<br>'ABSIRFIX01-WMCo 4pm LDN', 'ABSIRFIX01-BFIX GBP L080'             |
| BRL*** | 'NDF Asiatica', 'Pontos sobre PTAX'                                                                                                       |

Table 11.2: Supported fixing source string for NDF/NDS.

## <span id="page-85-0"></span>**12 Version Log**

| Version | Date       | Comments                                                                                                                                                                                                                                                                                                                                                                                     |
|---------|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1.0     | 2010-09-27 | Initial Version                                                                                                                                                                                                                                                                                                                                                                              |
| 1.1     | 2010-11-11 | Fixed tag number for QuoteType in QuoteRequest.                                                                                                                                                                                                                                                                                                                                              |
| 1.2     | 2010-11-30 | Removed Direct Line connection option                                                                                                                                                                                                                                                                                                                                                        |
| 1.3     | 2010-12-15 | Added additional Fields (Prices/Dates) in all messages                                                                                                                                                                                                                                                                                                                                       |
| 1.4     | 2011-01-20 | Send individual News messages for each product, removed point fields in<br>Quote messages, added 'New' status for ExecutionReport message, added val<br>ues for ExecType field in ExecutionReport                                                                                                                                                                                            |
| 1.5     | 2011-02-03 | Added Limit and Market orders.                                                                                                                                                                                                                                                                                                                                                               |
| 1.6     | 2011-02-28 | Added NDFs.                                                                                                                                                                                                                                                                                                                                                                                  |
| 1.7     | 2011-05-25 | Noted that Password field is mandatory for Logon message, and removed un<br>used Username field                                                                                                                                                                                                                                                                                              |
| 1.8     | 2011-07-18 | multiple changes:<br>• clarified Side<54> for swaps<br>• added NDF references to several field descriptions<br>• added MaturityDate<541> to several messages<br>• QuoteRequestID<131> may be 50 characters in size<br>• ExpireTime<126> may not be more than 5 minutes in the future<br>• OrdType<40><br>in<br>Execution<br>report<br>reflects<br>the<br>one<br>sent<br>in<br>NewOrderSingle |
| 1.9     | 2011-07-28 | added MarketData functionality                                                                                                                                                                                                                                                                                                                                                               |
| 1.10    | 2011-08-09 | Added example messages                                                                                                                                                                                                                                                                                                                                                                       |
| 1.11    | 2011-08-11 | added note regarding responsibility for timeouts                                                                                                                                                                                                                                                                                                                                             |
| 1.12    | 2011-10-28 | added missing field to ExecutionReport, small layout changes,                                                                                                                                                                                                                                                                                                                                |
| 1.13    | 2012-04-26 | removed tag 40 (OrdType) from QuoteRequest                                                                                                                                                                                                                                                                                                                                                   |
| 1.14    | 2012-06-12 | Added Important disclaimer for API clients                                                                                                                                                                                                                                                                                                                                                   |
| 1.15    | 2012-10-18 | Renamed to RFS API                                                                                                                                                                                                                                                                                                                                                                           |
|         |            | continued on next page                                                                                                                                                                                                                                                                                                                                                                       |

| Version | Date       | Comments                                                                                                                         |
|---------|------------|----------------------------------------------------------------------------------------------------------------------------------|
| 3.0     | 2013-01-17 | multiple changes:                                                                                                                |
|         |            | • added BusinessRejectRefID in BusinessMessageReject message                                                                     |
|         |            | • Moved provider name to Parties component in ExecutionReport                                                                    |
|         |            | • Layout changes                                                                                                                 |
|         |            |                                                                                                                                  |
| 3.1     | 2013-05-08 | moved MaturityDate (tag 541) after Symbol (tag 55)                                                                               |
| 4.0     | 2013-07-08 | Multiple changes:                                                                                                                |
|         |            | • Pre-trade allocations can be set on the QuoteRequest message with<br>NoAllocs (tag 78) and NoLegAllocs (tag 670) nested groups |
|         |            | • ExecutionReport contains reference spot rate in field LastSpotRate (tag<br>194)                                                |
|         |            | • Support of NewOrderMultileg message as an alternative for FX Swap<br>orders.                                                   |
| 5.0     | 2013-07-08 | add Unique Swap Identifier fields and MidRates                                                                                   |
| 5.1     | 2013-08-07 | Updated description of tag 37 in ExecutionReport message and examples.                                                           |
| 5.2     | 2013-08-20 | RefSpotDate is no longer mandatory in QuoteRequest message                                                                       |
| 5.3     | 2013-08-27 | Fixed a bug in spec (Quote message contains RefSpotDate<7070> rather than<br>ProductType<7071>                                   |
| 6.0     | 2013-09-04 | Multiple changes:                                                                                                                |
|         |            | • The field Side in business messages can be configured in the session on<br>360T side to be relative to notional currency       |
|         |            | • QuoteCancel messages will be sent upon quote request expiration and as<br>confirmation of a client QuoteCancel message.        |
| 6.1     | 2014-03-19 | add Firewall configuration                                                                                                       |
| 7.0     | 2014-03-24 | Send<br>Bank<br>Basket<br>for<br>each<br>TAS(TradeAsEntity)<br>and<br>TOB(TradeOnBehalfEntity)                                   |
| 7.1     | 2014-03-24 | UTI fields and NDS support                                                                                                       |
| 7.2     | 2014-03-28 | Add support for FX Time Options                                                                                                  |
| 7.3     | 2014-06-04 | Update the location of OptionDate in QuoteRequest in relation to FX Time<br>Options                                              |
| 7.4     | 2014-06-17 | Timeout instructions                                                                                                             |
| 7.5     | 2014-09-08 | Add allocation USI/UTI in Execution Report                                                                                       |
| 7.6     | 2014-09-21 | Update QuoteCancel messages                                                                                                      |
| 7.7     | 2014-09-23 | Improved USI/UTI documentation                                                                                                   |
|         |            | continued on next page                                                                                                           |

Chapter 12: Version Log

| Version | Date       | Comments                                                                                                                                                                                                                                                                                                                                |
|---------|------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 7.8     | 2014-09-23 | Updated the sequence diagram                                                                                                                                                                                                                                                                                                            |
| 7.9     | 2015-03-04 | Update definition for 'SIDE'                                                                                                                                                                                                                                                                                                            |
| 7.10    | 2015-04-06 | Corrected QuoteType definition in Quote                                                                                                                                                                                                                                                                                                 |
| 7.11    | 2015-04-06 | Corrected field order in header and several example messages                                                                                                                                                                                                                                                                            |
| 7.12    | 2015-06-26 | Corrected NewOrderMultileg MsgType to 35=AB                                                                                                                                                                                                                                                                                             |
| 7.13    | 2016-02-01 | Updated the ExpireTime specification                                                                                                                                                                                                                                                                                                    |
| 7.14    | 2016-03-01 | Fix quote fields                                                                                                                                                                                                                                                                                                                        |
| 7.15    | 2017-03-24 | Added financial calender query messages (SecurityDefinitionRequest and<br>SecurityDefinition)                                                                                                                                                                                                                                           |
| 8.0     | 2017-09-19 | MIFID draft                                                                                                                                                                                                                                                                                                                             |
| 8.1     | 2018-01-19 | MIFID final                                                                                                                                                                                                                                                                                                                             |
| 9.0     | 2018-02-21 | Added support for block trades.                                                                                                                                                                                                                                                                                                         |
| 9.1     | 2018-03-09 | Corrections on the order of fields for block trades.                                                                                                                                                                                                                                                                                    |
| 9.2     | 2018-12-12 | Clarification on quote id usage in orders and quote id length in quote messages.                                                                                                                                                                                                                                                        |
| 10.0    | 2018-12-19 | Added<br>Clearing<br>Firm<br>to<br>QuoteRequest<br>and<br>ExecutionReport.<br>Updated<br>ISIN<br>and<br>regulatory<br>trade<br>id<br>structure<br>for<br>Swap<br>and<br>NDS<br>requests<br>in<br>ExecutionReport. ExecType<150> in ExecutionReport will be 'F' (Trade) for<br>executed trades. Updated structure of QuoteRequestReject. |
| 11.0    | 2019-10-21 | Updated how ISIN and TVTICs are sent for trades under MTF.                                                                                                                                                                                                                                                                              |
| 11.1    | 2019-12-06 | Updated <3> Reject message to include tags <371> RefTagID and <373> Ses<br>sionRejectReason.                                                                                                                                                                                                                                            |
| 11.2    | 2020-03-19 | Updated SettlDate2 description.                                                                                                                                                                                                                                                                                                         |
| 11.3    | 2020-06-11 | Added Trading Hours section that details the enforced MTF closing hours.                                                                                                                                                                                                                                                                |
| 12.0    | 2020-09-18 | Added NoCustomFields <7546> group to QuoteRequest and ExecutionReport.                                                                                                                                                                                                                                                                  |
| 12.1    | 2021-05-04 | Added missing ProductType<7071> field to Quote <s> message. Added miss<br/>ing RefMsgType&lt;372&gt; field to Reject&lt;3&gt; message.</s>                                                                                                                                                                                              |
| 12.2    | 2023-02-16 | Added required ProductType<7071> field to the NewOrderMultileg <ab><br/>message (field was already present but undocumented).</ab>                                                                                                                                                                                                      |
| 12.3    | 2023-02-21 | Added<br>LegMaturityDate<611><br>to<br>QuoteRequest <r><br/>and<br/>NewOrderMultileg<ab> messages for uploading NDF Block trades.</ab></r>                                                                                                                                                                                              |
| 12.4    | 2023-02-22 | Added Side<54> field value 'B' = As Defined for block trades.                                                                                                                                                                                                                                                                           |
| 12.5    | 2023-02-21 | Added example messages for FX Block trades and NDF Block trades.                                                                                                                                                                                                                                                                        |
| 12.6    | 2023-03-10 | Removing support for using NewOrderMultileg <ab> message to execute FX<br/>Swap quotes.</ab>                                                                                                                                                                                                                                            |
| 12.7    | 2023-06-14 | Adding support for Base Metals.                                                                                                                                                                                                                                                                                                         |
| 12.8    | 2023-10-30 | Adding support for Unique Product Identifier (UPI) and Reporting Tracking<br>Number (RTN) to the ExecutionReport<8> message.                                                                                                                                                                                                            |
| 12.9    | 2024-02-15 | Adding support for Loan/Deposit.                                                                                                                                                                                                                                                                                                        |
|         |            | continued on next page                                                                                                                                                                                                                                                                                                                  |

![](_page_88_Picture_0.jpeg)

Chapter 12: Version Log

| Version | Date       | Comments                                                                                                                                                                                                       |
|---------|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 12.10   | 2024-05-13 | Added support for the Singapore Regulated Market Operator (RMO) venue to<br>the ExecutionVenueType<7611> field.                                                                                                |
| 12.12   | 2024-06-04 | Adding FixingReference<7075> to QuoteRequest <r>, NewOrderSingle<d>,<br/>NewOrderMultileg<ab> and ExecutionReport&lt;8&gt; messages.</ab></d></r>                                                              |
| 12.11   | 2024-06-10 | Removing<br>obsolete<br>RegulatoryTradeIDs<1907><br>group<br>from<br>QuoteRequest <r> message.</r>                                                                                                             |
| 12.13   | 2024-06-27 | Reordering fields in QuoteRequest <r>,<br/>Quote<s>,<br/>NewOrderSingle<d>,<br/>NewOrderMultileg<ab> and ExecutionReport&lt;8&gt; messages according to<br/>their position in FIX dictionary.</ab></d></s></r> |
| 12.14   | 2024-10-15 | Adding<br>support<br>for<br>Split<br>Settlement<br>Date<br>to<br>NewOrderSingle <d><br/>and<br/>ExecutionReport&lt;8&gt; messages.</d>                                                                         |