# **USER GUIDE BRIDGE ADMINISTRATION**

![](_page_0_Picture_1.jpeg)

# **TEX MULTIDEALER TRADING SYSTEM**

USER GUIDE

BRIDGE ADMINISTRATION: REGULATORY DATA

© 360 TREASURY SYSTEMS AG, 2018 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION

| 1 |       | INTRODUCTION                  | 4  |
|---|-------|-------------------------------|----|
| 2 |       | GETTING STARTED<br>           | 4  |
| 3 |       | REGULATORY DATA               | 5  |
|   | 3.1   | INSTITUTION DETAILS           | 7  |
|   | 3.1.1 | Overview                      | 7  |
|   | 3.1.2 | Common entity details         | 8  |
|   | 3.1.3 | OTC entity details            | 9  |
|   | 3.1.4 | MTF entity details<br>        | 10 |
|   | 3.1.5 | Ex Ante Cost Disclosure       | 11 |
|   | 3.2   | USER DETAILS                  | 12 |
|   | 3.3   | AUTODEALER USER<br>           | 14 |
|   | 3.4   | SECURITY AND DATA PROTECTION  | 16 |
|   | 3.5   | DATA RETENTION PERIOD         | 16 |
| 4 |       | EXTERNAL MAPPING              | 16 |
| 5 |       | CREATE AN EXTERNAL INDIVIDUAL | 20 |
| 6 |       | CONTACTING 360T               | 25 |

## **TABLE OF FIGURES**

| Figure 1 Header Bar5                                                                  |  |
|---------------------------------------------------------------------------------------|--|
| Figure 2 Bridge Administration: Homepage. 5                                           |  |
| Figure 3 Regulatory Data Administration: Start page6                                  |  |
| Figure 4 Regulatory Data Administration: Live Audit Log7                              |  |
| Figure 5 Regulatory Data Administration: Overview. 7                                  |  |
| Figure 6 Regulatory Data Administration: Download/upload entities or users. 8         |  |
| Figure 7 Regulatory Data Administration: Common tab9                                  |  |
| Figure 8 Regulatory Data Administration: OTC tab10                                    |  |
| Figure 10 Regulatory Data Administration: MTF tab 11                                  |  |
| Figure 9 Ex Ante customer definition12                                                |  |
| Figure 11 Regulatory Data Administration: MTF user details13                          |  |
| Figure 12: Trading Capacity 14                                                        |  |
| Figure 13: AutoDealer user 15                                                         |  |
| Figure 14 360T MTF Identification of users behind API 16                              |  |
| Figure 15 Bridge Administration Homepage: External Mappings feature 17                |  |
| Figure 16 External Mapping: Create new configuration. 17                              |  |
| Figure 17 External Mapping: Adding external code 18                                   |  |
| Figure 18 External Mapping: Upload of external codes 19                               |  |
| Figure 19 External Mapping: Modification of external codes19                          |  |
| Figure 20 Bridge Administration: Help Wizard. 20                                      |  |
| Figure 21 Bridge Administration: Help Wizard Step 1 - Select an Institution. 21       |  |
| Figure 22 Bridge Administration: Help Wizard Step 2 – Individual details. 22          |  |
| Figure 23 Bridge Administration: Help Wizard Step 2 – Individual details completed23  |  |
| Figure 24 Bridge Administration: Help Wizard Step 3 – Individual details overview. 24 |  |
| Figure 25 Bridge Administration: External user24                                      |  |
|                                                                                       |  |

## **TABLE OF TABLES**

| Table 1 Field description of user details 14            |  |
|---------------------------------------------------------|--|
| Table 2: Field description of AutoDealer user details15 |  |
| Table 3: Field description of External user details23   |  |

## <span id="page-3-0"></span>**1 INTRODUCTION**

This user manual describes the Regulatory Data, External Mapping and Create External Individual features of the 360T Bridge Administration tool, the methodology of static data collection as required by MIFID II regulations and the way in which protection of confidential data is guaranteed.

The Regulatory Technical Standard 24 (RTS) outlines all record keeping obligations that are required to be fulfilled by an MTF (Multilateral Trading Facility). These requirements encompass storage of trade activity information enriched with user- and entity static details. Since the 3rd January 2018, 360T operates an MTF alongside its OTC trading platform and therefore fulfils the above-mentioned requirements.

The Bridge Administration tool is available only to users who have regulatory administrator rights. As part of the onboarding process, a sub-set of administrator users to whom the tool is to be accessible to has to be defined. These users are then responsible for entering the information as described in the following chapters. <NAME_EMAIL> or your customer relationship manager for setting up the administrator rights.

Please note: Institutions can only be enabled for the MTF by the 360T CAS team as part of the 360T MTF Onboarding process. The initial activation of individual users for the 360T MTF can only be done by the regulatory data administrator of the institution as 360T has no access to the confidential personal data of individual users which is required for MTF activation. Per default, all entities and users are OTC enabled (off MTF) for MiFID II relevant products: Forwards, Block, Swap, non-SEF NDF, non-SEF NDS and non-SEF Options.

## <span id="page-3-1"></span>**2 GETTING STARTED**

The Bridge Administration tool can be accessed via the menu option "Administration" in the screen header of the Bridge application. Several features may be visible in the left menu depending on user permissions.

|                                                                                                                                                                                                                           |                                                                                                                                                                                                                         |                                                                                                                                                                                                                               | $\vee$ Preferences<br>$\sim$ Administration                                                                                                                                                                                                                                        | $AA - D \times$<br>$\vee$ Help                           |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------|
| Sridge Administration                                                                                                                                                                                                     |                                                                                                                                                                                                                         |                                                                                                                                                                                                                               |                                                                                                                                                                                                                                                                                    | X                                                        |
| 117890<br>11/920<br>Spot // 21.11.2017<br>11793020<br>11/96070<br>4.020<br>4.07.0<br>1 Week // 28.11.2017<br>11807340<br>11310450<br>18340 18450<br>1 Month // 21 12 2017<br>11923700<br>11327200<br>134,700<br>135,200   | 132327<br>13336<br>Spot // 21.11.2017<br>13234800 13235800<br>2.100<br>2.200<br>1 1 Week // 28 11:2017<br>13242860<br>13243870<br>10.160<br>10.270<br>1 Month // 21.12.2017<br>13313200<br>13315100<br>80,400<br>81,400 | $0.89084$ $0.89112$<br>Spot // 21.11.2017<br>0.8909950 0.8912860<br>1.550<br>1.660<br>1 Week // 28.11.2017<br>0.8915380<br>0.8918300<br>6.980<br>7.100<br>1 Month // 21 12 2017<br>0.8955100<br>0.8958900<br>46.700<br>47.700 | $0.99184$ $0.99193$<br>Spot // 21.11.2017<br>0.9914280<br>15220<br>$-4.120$<br>$-4.080$<br>1 Week // 28.11.2017<br>0.9899720<br><b>Bas00890</b><br>$-18.680$<br>$-18.410$<br>1 Month // 21.12.2017<br>09780800<br>0.9782700<br>$-137.600$<br>$-136.600$<br>A Monthe // 33 06 2018: |                                                          |
| All (0) Executed (0) Cancelled (0) Expired (0) Rejected (0) Done (0) $\vert$ Q<br>Requester A Base Currency Counterpart Effective Date Effective Pe Far Leg Req RFS / Order<br>Ø.<br>TradeAsG.TreasurerA.TradeAsG // DEMO |                                                                                                                                                                                                                         | <b>SECT</b>                                                                                                                                                                                                                   | Maturity Date Maturity Per Near Leg Re Notional Am Parent                                                                                                                                                                                                                          | Product<br>Fr, 17. Nov 2017, 08:14:20 GMT // Connected @ |

<span id="page-4-1"></span>Figure 1 Header Bar

The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools and actions for the particular user.

A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.

|                     |                                         |                      |                              |                             | $\vee$ Preferences      | $\vee$ Administration                                | $\vee$ Help $\Box$ $\Diamond$ AA $ \Box$ X |  |
|---------------------|-----------------------------------------|----------------------|------------------------------|-----------------------------|-------------------------|------------------------------------------------------|--------------------------------------------|--|
|                     | <b>RFS REQUESTER</b>                    | <b>DEAL TRACKING</b> | <b>BRIDGE ADMINISTRATION</b> | $\boldsymbol{+}$            |                         |                                                      |                                            |  |
|                     | 合                                       |                      |                              | <b>Administration Start</b> |                         |                                                      |                                            |  |
|                     |                                         |                      | Configurations               |                             |                         |                                                      |                                            |  |
|                     |                                         |                      |                              |                             |                         |                                                      |                                            |  |
|                     |                                         |                      | <b>Regulatory Data</b>       |                             | <b>External Mapping</b> |                                                      |                                            |  |
|                     |                                         |                      | <b>Actions</b>               |                             |                         |                                                      |                                            |  |
|                     |                                         |                      |                              |                             |                         |                                                      |                                            |  |
|                     |                                         |                      | <b>Change Request</b>        |                             | Wizards                 |                                                      |                                            |  |
|                     |                                         |                      |                              |                             |                         |                                                      |                                            |  |
|                     |                                         |                      |                              |                             |                         |                                                      |                                            |  |
| $\frac{\phi}{\Box}$ |                                         |                      |                              |                             |                         |                                                      |                                            |  |
|                     | 1 TradeAsG.TreasurerA, TradeAsG // DEMO |                      |                              | <b>semp</b>                 |                         | Tue, 17. Jul 2018, 09:53:31 GMT // Connected [FFM] ● |                                            |  |

<span id="page-4-2"></span>Figure 2 Bridge Administration: Homepage.

## <span id="page-4-0"></span>**3 REGULATORY DATA**

The "Regulatory Data" quick link opens a navigation panel which contains an institution tree. The tree includes a list of all users and trade-as, trade-on-behalf or I-TEX entities configured under the main entity.

Individual user types can be identified by color: Treasurer (blue), Trader (red), Backoffice (yellow), AutoDealer (green), Hybrid (half red/blue), MT API (purple), External (orange). Both active and inactive users are displayed in the list. Active users will appear in full color. Disabled users (in the case of temporary leave) will appear grey. Deleted users will appear grey and with a strikethrough.

The selection of the individuals or institutions is done by single-click within the institution tree which opens a new form/sheet with the available details of that user or entity. The selected item is highlighted with a light grey color as an active task inside the taskbar.

| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                                                                                                                                                                                                                     | $+$                       |                                                                                                          | $\vee$ Preferences $\vee$ Administration $\vee$ Help |                                     | $AA - D \times$                                       |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|----------------------------------------------------------------------------------------------------------|------------------------------------------------------|-------------------------------------|-------------------------------------------------------|
| Q ※ L ▲ ■ L<br>$\overline{\left( \right. }%$<br>合<br>TradeAs<br>$\times$<br>⊖<br>$\sim \triangleq$ TradeAsG<br><sup> TradeAsG.TAS.B1</sup><br>$\Box$<br><sup> TradeAsG.TAS.B2</sup><br><sup> TradeAsG.TAS.B3</sup><br><sup> TradeAsG.TradeAsG</sup><br>TradeAsG.TreasurerA<br>TradeAsG.TreasurerB<br>TradeAsG.TreasurerC | Overview<br>Common<br>自土土 | <b>OTC</b><br><b>MTF</b><br>Institution tree<br>Name<br>TradeAsG.TAS.B2<br>Company's individuals<br>Name | <b>OTC</b><br>enabled<br><b>OTC</b>                  | <b>MTF</b><br>enabled<br><b>MTF</b> | $\text{and}\quad \mathbb{R}\rightarrow \mathbb{R}$    |
| ☆<br>$\mathbb C$<br>$\bigcirc$<br>TradeAsG.TreasurerA, TradeAsG // DEMO                                                                                                                                                                                                                                                  |                           | TradeAsG X   TradeAsG.TreasurerB X   TradeAsG.TAS.B1 X<br>TradeAsG.TAS.B2 X<br><b>EEUT</b>               |                                                      | Discard all changes                 | Save<br>Fr, 17. Nov 2017, 08:57:20 GMT // Connected · |

<span id="page-5-0"></span>Figure 3 Regulatory Data Administration: Start page.

A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:

- **Search** : A search field will open and the user can type in an alphanumeric value in order to find the desired institution or individual. The dynamic search can be limited to individuals or institutions if one of the corresponding "Show individuals" or "Show institutions" icon is additionally selected. The search field can be hidden by clicking on the search icon again.
- **Scroll from source** : This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel. Jumping to the selected tree item (active individual or institution in the taskbar) is possible when clicking scroll from source.
- **Show individuals** view toggle : Displays only individuals in the navigation panels. It can be activated in order to limit search results to individual users only.
- **Show institutions** view toggle : Displays only institutions in the navigation panels. It can be activated in order to limit search results to entities only.

The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.

Each entity or user tab has a **Live Audit Log** which tracks all unsaved changes. Individual unsaved changes can be reverted by clicking on the arrow icon. Clicking on the "Discard all changed" button will revert all unsaved changes.

| <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b>                | $^{+}$                                                  | $\vee$ Preferences $\vee$ Administration $\vee$ Help $A_A - \Box X$                       |
|---------------------------------------------------------------------|---------------------------------------------------------|-------------------------------------------------------------------------------------------|
| Q 带<br>117<br>K<br>合<br>$\land \equiv$ TradeAsG                     | orc<br><b>MTF</b><br>Overview<br>Common                 | $\Omega$<br>Live Audit Log<br>Event Name<br>Target                                        |
| $\mathcal{L}_{\mathcal{F}}$<br>盒 TradeAsG.TAS.B1<br>TradeAsG.TAS.B2 | <b>OTC Enabled</b><br>$\bigtriangledown$                | Systematic Internalizer<br>$\sqrt{2}$                                                     |
| 盒 TradeAsG.TAS.B3<br>$\Box$<br>TradeAsG.TradeAsG                    | $\sigma$<br>Systematic Internalizer                     | Systematic Internalizer<br>$\mathcal{L}$<br>Systematic Internalizer<br>$\checkmark$<br>40 |
|                                                                     | <b>Systematic Internalizer MIC</b>                      |                                                                                           |
|                                                                     | Field should be specified                               |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
|                                                                     |                                                         |                                                                                           |
| $\circ$<br>o                                                        |                                                         | Save<br><b>Discard all changes</b>                                                        |
| $\circ$                                                             | TradeAsGTAS B1 X   TradeAsGTreasurerA X  <br>TradeAsG X |                                                                                           |
| 1 D TradeAsG.TreasurerA, TradeAsG // DEMO                           | EECT                                                    | Fr, 17. Nov 2017, 10:10:57 GMT // Connected @                                             |

<span id="page-6-2"></span>Figure 4 Regulatory Data Administration: Live Audit Log.

## <span id="page-6-0"></span>**3.1 Institution details**

### <span id="page-6-1"></span>**3.1.1 Overview**

Selecting an entity from the institution tree displays an Overview tab with additional regulatory data tabs: Common, OTC and MTF. The overview tab shows which users and entities are OTC and MTF enabled for the MiFID II relevant products.

|                                                 | <b>BRIDGE ADMINISTRATION</b> |                          |            | $\vee$ Preferences $\vee$ Administration $\vee$ Help | $AA - B X$                                    |
|-------------------------------------------------|------------------------------|--------------------------|------------|------------------------------------------------------|-----------------------------------------------|
| <b>RFS REQUESTER</b>                            | $^{+}$                       |                          |            |                                                      |                                               |
| Q 第 1 直<br>合                                    | Common<br>K<br>Overview      | <b>OTC</b><br><b>MTF</b> |            |                                                      | $O$ $N$                                       |
| $\wedge \triangleq$ TradeAsG                    | 自立出                          |                          |            |                                                      |                                               |
| €<br>TradeAsG.TreasurerA<br>TradeAsG.TreasurerB |                              | <b>Institution tree</b>  |            |                                                      |                                               |
| TradeAsG.TreasurerC<br>目                        |                              | Name                     | <b>OTC</b> | <b>MTF</b>                                           |                                               |
|                                                 |                              | 盒 TradeAsG               | enabled    | enabled                                              |                                               |
|                                                 |                              | TradeAsG.TAS.B1          | enabled    | enabled                                              |                                               |
|                                                 |                              | TradeAsG.TAS.B2          | enabled    | enabled                                              |                                               |
|                                                 |                              | TradeAsG.TAS.B3          | enabled    | enabled                                              |                                               |
|                                                 |                              | TradeAsG.TradeAsG        | enabled    | enabled                                              |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              | Company's individuals    |            |                                                      |                                               |
|                                                 |                              | Name                     | OTC        | <b>MTF</b>                                           |                                               |
|                                                 |                              | TradeAsG.TreasurerA      | enabled    | disabled                                             |                                               |
|                                                 |                              | TradeAsG TreasurerB      | enabled    | disabled                                             |                                               |
|                                                 |                              | tradeAsG.TreasurerC      | enabled    | disabled                                             |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
| $\langle \rangle$                               |                              |                          |            |                                                      |                                               |
| $\mathbb C$                                     |                              |                          |            | Discard all changes                                  | Save                                          |
| $\heartsuit$                                    | TradeAsG X                   |                          |            |                                                      |                                               |
|                                                 |                              |                          |            |                                                      |                                               |
| TradeAsG.TreasurerA. TradeAsG // DEMO           |                              | FECT                     |            |                                                      | Fr, 17. Nov 2017, 09:43:18 GMT // Connected . |

<span id="page-6-3"></span>Figure 5 Regulatory Data Administration: Overview.

The admin user can download a list of all daughter institutions or individuals as a CSV file. This file can be used as an upload template to add missing information to users (upload from clipboard or from CSV file). Please note that only specific data fields (as specified in Chapter 3.2.1) can be changed using the CSV upload:

- "Nationality" (ISO 3166 1 alpha2 code)
- "Branch Country" (ISO 3166 1 alpha2 code)
- "National client Id 1", "National client Id 2", "National client Id 3" (as defined in Annex II Annex II of RTS 22)
- "Trading Capacity" (default trading capacity of the user)

|                                                      |                    |                                                               |                    | $\vee$ Preferences $\vee$ Administration $\vee$ Help | $AA - \Box X$                                                                                                                |
|------------------------------------------------------|--------------------|---------------------------------------------------------------|--------------------|------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------|
| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b> | $^{+}$             |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
| Q 發<br>上鱼<br>合                                       | Overview<br>Common | <b>OTC</b><br><b>MTF</b>                                      |                    |                                                      | $\textcolor{blue}{\textcolor{blue}{\textbf{M}}} \textcolor{blue}{\textcolor{blue}{\textbf{N}}} \textcolor{blue}{\mathbf{E}}$ |
| $\wedge \hat{m}$ TradeAsG                            | 圓മ±∎               |                                                               |                    |                                                      |                                                                                                                              |
| ⊙<br><sup> TradeAsG.TAS.B1</sup>                     |                    | <b>Institution tree</b>                                       |                    |                                                      |                                                                                                                              |
| <sup> TradeAsG.TAS.B2</sup>                          |                    |                                                               | <b>OTC</b>         | <b>MTF</b>                                           |                                                                                                                              |
| $\Box$<br><sup> TradeAsG.TAS.B3</sup>                |                    | Name                                                          |                    |                                                      |                                                                                                                              |
| <sup> TradeAsG.TradeAsG</sup><br>TradeAsG.TreasurerA |                    | $en TradeASG$<br><sup>1</sup> / <sub>10</sub> TradeAsG.TAS.B1 | enabled<br>enabled | enabled<br>enabled                                   |                                                                                                                              |
| TradeAsG.TreasurerB                                  |                    | TradeAsG.TAS.B2                                               | enabled            | enabled                                              |                                                                                                                              |
| TradeAsG.TreasurerC                                  |                    | TradeAsG.TAS.B3                                               | enabled            | enabled                                              |                                                                                                                              |
|                                                      |                    | <b><sup>1</sup></b> TradeAsG.TradeAsG                         | enabled            | enabled                                              |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    | Company's individuals                                         |                    |                                                      |                                                                                                                              |
|                                                      |                    | Name                                                          | <b>OTC</b>         | <b>MTF</b>                                           |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    | TradeAsG.TreasurerA<br>TradeAsG.TreasurerB                    | enabled<br>enabled | disabled<br>disabled                                 |                                                                                                                              |
|                                                      |                    | TradeAsG.TreasurerC                                           | enabled            | disabled                                             |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
|                                                      |                    |                                                               |                    |                                                      |                                                                                                                              |
| ☆                                                    |                    |                                                               |                    | Discard all changes                                  | Save                                                                                                                         |
| $\Box$                                               |                    |                                                               |                    |                                                      |                                                                                                                              |
| $\bigcirc$                                           | TradeAsG X         |                                                               |                    |                                                      |                                                                                                                              |
| 1 TradeAsG.TreasurerA, TradeAsG // DEMO              |                    | Franc                                                         |                    | Fr, 17. Nov 2017, 08:21:27 GMT // Connected ·        |                                                                                                                              |

<span id="page-7-1"></span>Figure 6 Regulatory Data Administration: Download/upload entities or users.

### <span id="page-7-0"></span>**3.1.2 Common entity details**

The Common tab shows two read-only fields: Investment Firm and LEI.

The Investment Firm flag indicates whether the entity is an investment firm covered by MIFID II Directive (RTS 22, Annex I, Table 2, Field 6). 360T's MTF has a transaction reporting obligation if the MTF participant is not an investment firm (e.g. a corporation). 360T uses this field to determine whether or not it has a transaction reporting obligation in relation to its capacity as an MTF.

The Legal Entity Identifier (LEI) is a 20-digit, alpha-numeric code based on the ISO 17442 standard developed by the International Organization for Standardization (ISO). It connects to key reference information that enables clear and unique identification of legal entities participating in financial transactions. The LEI number must be provided to 360T for each entity which should be enabled for the MTF and is part of the 360T MTF onboarding process.

| <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b>                            | $\, +$             |                                           |                      | $AA = \Box X$<br>$\vee$ Preferences $\vee$ Administration $\vee$ Help                                                        |
|---------------------------------------------------------------------------------|--------------------|-------------------------------------------|----------------------|------------------------------------------------------------------------------------------------------------------------------|
| Q 嵌<br>12E<br>$\overline{\left( \right. }%$<br>合<br>$\land \triangleq$ TradeAsG | Overview<br>Common | OTC<br><b>MTF</b>                         |                      | $\textcolor{blue}{\textcolor{blue}{\textbf{M}}} \textcolor{blue}{\textcolor{blue}{\textbf{N}}} \textcolor{blue}{\textbf{E}}$ |
| $\mathcal{G}$<br><sup> TradeAsG.TAS.B1</sup><br>TradeAsG.TAS.B2                 |                    | <b>Investment Firm</b>                    | $\sqrt{\phantom{a}}$ |                                                                                                                              |
| $\Box$<br>TradeAsG.TAS.B3<br>TradeAsG.TradeAsG                                  |                    | LEI                                       | 22345678901234567871 |                                                                                                                              |
|                                                                                 |                    |                                           |                      |                                                                                                                              |
|                                                                                 |                    |                                           |                      |                                                                                                                              |
|                                                                                 |                    |                                           |                      |                                                                                                                              |
|                                                                                 |                    |                                           |                      |                                                                                                                              |
|                                                                                 |                    |                                           |                      |                                                                                                                              |
|                                                                                 |                    |                                           |                      |                                                                                                                              |
|                                                                                 |                    |                                           |                      |                                                                                                                              |
| $\breve{\nabla}$                                                                |                    |                                           |                      |                                                                                                                              |
| $\overline{c}$<br>$\bigcirc$                                                    | TradeAsG X         | TradeAsG.TAS.B1 X   TradeAsG.TreasurerA X |                      | Discard all changes<br>Save                                                                                                  |
| TradeAsG.TreasurerA, TradeAsG // DEMO                                           |                    | <b>BECK</b>                               |                      | Fr, 17. Nov 2017, 10:05:23 GMT // Connected .                                                                                |

<span id="page-8-1"></span>Figure 7 Regulatory Data Administration: Common tab

### <span id="page-8-0"></span>**3.1.3 OTC entity details**

In the OTC tab the admin user can define if the entity is a Systemic Internalizer (SI) and add the Systemic Internalizer Market Identifier Code (SI MIC). The SI MIC is mandatory if the SI checkbox is selected.

The configured SI status is taken into consideration by the 360T OTC platform when a manual trader or an AutoDealer in a back-to-back scenario executes a transaction on an OTC bilateral basis. In case a trading API is utilized (e.g. Market Taker API), the SI status has to be provided via the API.

The 360T OTC platform forwards the SI status to the trade counterparty on trade execution given that this information is necessary for the determination of the trade party making the transaction public via an Approved Publication Arrangement (APA).

| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                   | $\boldsymbol{+}$   |                                                        |                  | $\vee$ Preferences $\vee$ Administration $\vee$ Help | $AA - B \times$                               |
|----------------------------------------------------------------------------------------|--------------------|--------------------------------------------------------|------------------|------------------------------------------------------|-----------------------------------------------|
| $1 \equiv$<br>Q 發<br>$\overline{\left( \right. }%$<br>合<br>$\land \triangleq$ TradeAsG | Overview<br>Common | <b>OTC</b><br><b>MTF</b>                               |                  |                                                      | $\circ \circ \cong$                           |
| $\mathcal{G}$<br><sup>1</sup> / <sub>10</sub> TradeAsG.TAS.B1<br>TradeAsG.TAS.B2       |                    | <b>OTC</b> Enabled                                     | $\sqrt{\bullet}$ |                                                      |                                               |
| $\Box$<br>TradeAsG.TAS.B3<br>TradeAsG.TradeAsG                                         |                    | Systematic Internalizer<br>Systematic Internalizer MIC | $\bullet$        |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
|                                                                                        |                    |                                                        |                  |                                                      |                                               |
| $\breve{\mathbf{Q}}$<br>$\overline{c}$                                                 |                    |                                                        |                  | Discard all changes                                  | Save                                          |
| $\bigcirc$<br>TradeAsG.TreasurerA, TradeAsG // DEMO                                    | TradeAsG X         | TradeAsG.TAS.B1 X TradeAsG.TreasurerA X<br>esch        |                  |                                                      | Fr, 17. Nov 2017, 10:09:25 GMT // Connected · |

<span id="page-9-1"></span>Figure 8 Regulatory Data Administration: OTC tab

#### <span id="page-9-0"></span>**3.1.4 MTF entity details**

The MTF tab allows admin users to define a default investment decision maker (IDM) at a legal entity level provided that this entity has been enabled for the MTF by 360T CAS. The IDM default value can be set either to "Trader" or to "Defined Individual" provided that this individual has been previously MTF enabled by the admin user (see Chapter 3.2).

When a trader intends to trade "on behalf of" different funds and selects them within the trading application, the IDM - as setup within the Bridge Administration tool at the legal entity level - will be displayed.

| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b> | $^{+}$                                                         | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\mid$ AA - $\Box$ X |
|------------------------------------------------------|----------------------------------------------------------------|---------------------------------------------------------------------------|
| Q ※ 工意<br>K<br>$\hat{\mathbf{n}}$<br>へ 童 TradeAsG    | OTC<br>MTF<br>Overview<br>Common                               | $\Omega \cap \overline{\mathbb{R}}$                                       |
| ⊙<br>TradeAsG.TAS.B1<br>TradeAsG.TAS.B2              | <b>MTF Enabled</b>                                             | $\overline{v}$ e                                                          |
| TradeAsGTAS B3<br>$\Box$<br>盒 TradeAsG.TradeAsG      | <b>Investment Decision:</b><br>Investment decision within firm | Trader<br>$\checkmark$                                                    |
|                                                      | <b>Defined Individual</b>                                      | $\checkmark$                                                              |
|                                                      |                                                                |                                                                           |
|                                                      |                                                                |                                                                           |
|                                                      |                                                                |                                                                           |
|                                                      |                                                                |                                                                           |
|                                                      |                                                                |                                                                           |
|                                                      |                                                                |                                                                           |
| $\circ$<br>$\mathbf{C}$                              |                                                                | Discard all changes<br>Save                                               |
| $\circ$<br>TradeAsG TreasurerA, TradeAsG // DEMO     | TradeAsG.TAS.B1 X   TradeAsG.TreasurerA X  <br>TradeAsG X      |                                                                           |

<span id="page-10-1"></span>Figure 9 Regulatory Data Administration: MTF tab

#### <span id="page-10-0"></span>**3.1.5 Ex Ante Cost Disclosure**

The "Ex Ante Cost Disclosure" functionality was designed for liquidity providers who are required to disclose "ex ante cost" information to their customers, as set out in MiFID II, Article 24(4).

Administrator users have to be permissioned by 360T CAS prior to be able to access the tab "Ex ANTE Disclosure" in the Bridge Administration tool. Subsequently, "I-TEX" and "TEX customers can be moved from the "Available" to the "Selected" selection area to whom the ex ante costs have to be disclosed with regards to the RFS trading workflow, refer to [Figure](#page-11-1)  [10 Ex Ante customer definition.](#page-11-1)

Further information on the details of the disclosed information can be found in the separate document "360T User Guide RFS Market Taker Bridge.pdf".

| $\boldsymbol{+}$<br><b>BRIDGE ADMINISTRATION</b>                                                                                                                                                                                                                                                                | 9                                                                                                                                                                                                                                                                                                                             | $\bullet$ $0 \leftarrow \bullet$ AA $-\bullet$ X |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------|
| 1 血<br>Q ●<br>ぐ<br>合<br>へ 血 BankA<br>$\mathcal{G}$<br>$\vee \frac{1}{111}$ BankClientA<br>$\vee$ $\frac{\hat{\mathbf{m}}}{\hat{\mathbf{m}}}$ BankClientB<br>BankA.AutoDealer<br>$\mathbf{B}^\star$<br>BankA.TraderA<br>BankA.TraderB<br>BankA.TraderC<br>BankA.TraderD<br>☆<br>Ď<br>$\Rightarrow$<br>$\bigcirc$ | $OTC$ MTF<br>Common<br><b>Ex ANTE Disclosure</b><br><b>Overview</b><br>Selected<br>Available<br>BankClientA<br>TradeAsB<br>$(\Sigma)$<br>TradeAsA<br>TradeAsB.TAS.B1<br>$\prec$<br>TradeAsB.TAS.B3<br>TradeAsA.TAS.A2<br>$(\otimes)$<br>$(\kappa)$<br><b>Discard All Changes</b><br><b>Create Change Request</b><br>BankA * X | $\Omega \curvearrowright \equiv$<br>Save         |
| <b>1</b> BankA.TraderA, BankA // DEMO                                                                                                                                                                                                                                                                           | EECH<br>Mo, 19. Nov 2018, 08:37:23 GMT // Connected [FFM] ●                                                                                                                                                                                                                                                                   | <b>DEMO</b>                                      |

<span id="page-11-1"></span>Figure 10 Ex Ante customer definition

## <span id="page-11-0"></span>**3.2 User details**

Pursuant to RTS 24, 360T has to record trade information enriched with user details related to the execution decision maker and investment decision maker. Admin users of 360T MTF participants are required to activate the individual users for the MTF and enter their personal details and default values for trader related dynamic fields within the Bridge administration tool. Please refer to [Figure 11](#page-12-0) and the corresponding field descriptions in [Table 1.](#page-13-2)

|                                                      |                                |                           |            | $\vee$ Preferences $\vee$ Administration $\vee$ Help | $AA = B \times$                                                                                                                                       |
|------------------------------------------------------|--------------------------------|---------------------------|------------|------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b> | $+$                            |                           |            |                                                      |                                                                                                                                                       |
| Q ※<br>$\ $ $\ $ $\ $<br>K                           | <b>OTC</b><br><b>MTF</b>       |                           |            |                                                      | $\textcolor{blue}{\textcolor{blue}{\textbf{M}}} \textcolor{blue}{\textcolor{blue}{\textbf{N}}} \textcolor{blue}{\mathsf{N}} \textcolor{blue}{\equiv}$ |
| <mark>⋒</mark><br>$\land \triangleq$ TradeAsG        |                                |                           |            |                                                      |                                                                                                                                                       |
| $\mathcal{G}$<br>TradeAsG.TreasurerA                 |                                | <b>MTF Enabled</b>        | $\bullet$  |                                                      |                                                                                                                                                       |
| TradeAsG.TreasurerB<br>TradeAsG.TreasurerC           |                                |                           |            |                                                      |                                                                                                                                                       |
| ⊟                                                    |                                | <b>Individual Details</b> |            |                                                      |                                                                                                                                                       |
|                                                      |                                | First Name                | TreasurerA |                                                      |                                                                                                                                                       |
|                                                      |                                | Last Name                 | TreasurerA |                                                      |                                                                                                                                                       |
|                                                      |                                | Nationality               |            | $\checkmark$                                         |                                                                                                                                                       |
|                                                      |                                | Country of the Branch     |            | $\checkmark$                                         |                                                                                                                                                       |
|                                                      |                                | <b>National Client ID</b> |            |                                                      |                                                                                                                                                       |
|                                                      |                                | <b>Trading Capacity</b>   |            |                                                      |                                                                                                                                                       |
|                                                      |                                | <b>Trading Capacity</b>   | NONE       | $\vee$                                               |                                                                                                                                                       |
|                                                      |                                |                           |            |                                                      |                                                                                                                                                       |
|                                                      |                                |                           |            |                                                      |                                                                                                                                                       |
|                                                      |                                |                           |            |                                                      |                                                                                                                                                       |
|                                                      |                                |                           |            |                                                      |                                                                                                                                                       |
|                                                      |                                |                           |            |                                                      |                                                                                                                                                       |
|                                                      |                                |                           |            |                                                      |                                                                                                                                                       |
|                                                      |                                |                           |            |                                                      |                                                                                                                                                       |
| $\breve{\nabla}$                                     |                                |                           |            | Discard all changes                                  | Save                                                                                                                                                  |
| $\mathbb C$                                          |                                |                           |            |                                                      |                                                                                                                                                       |
| $\bigcirc$                                           | TradeAsG * X TradeAsG.TAS.B1 X | TradeAsG.TreasurerA X     |            |                                                      |                                                                                                                                                       |
| TradeAsG.TreasurerA, TradeAsG // DEMO                |                                | <b>escript</b>            |            |                                                      | Fr, 17. Nov 2017, 10:34:48 GMT // Connected ·                                                                                                         |

#### <span id="page-12-0"></span>Figure 11 Regulatory Data Administration: MTF user details

| No. | Field Name                    | Reference                                             | Details                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
|-----|-------------------------------|-------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1   | First Name                    |                                                       | The first name is setup as part of the user on-boarding process by<br>360T CAS. This value is NOT editable and shown for reference<br>purposes only.                                                                                                                                                                                                                                                                                                                                                     |
| 2   | Last Name                     |                                                       | The last name is setup as part of the user on-boarding process by<br>360T CAS. This value is NOT editable and shown for reference<br>purposes only.                                                                                                                                                                                                                                                                                                                                                      |
| 3   | Nationality                   | RTS<br>22,<br>Annex I, Table<br>2, Field 57 &<br>59   | Both the physical 'investment decision maker' (idm) and the<br>'execution decision maker' (edm) have to be identified using a<br>concatenation of the ISO 3166-1 alpha-2 (2 letter country code) of<br>the nationality of the person, followed by the identifier listed in RTS<br>22, Annex II based on the nationality of the person. Please refer to<br>item No. 5 in this table.                                                                                                                      |
|     |                               | RTS<br>24,<br>Annex,<br>Table<br>2, Field 4 & 5       | NB: A physical edm assumes that trading is done via a GUI and not<br>through an automated electronic algorithm.                                                                                                                                                                                                                                                                                                                                                                                          |
| 4   | Country of the<br>Branch      | RTS<br>22,<br>Annex I, Table<br>2, Field 60           | Field used to identify the country of the branch of the investment<br>firm for the user. In the event that a user is not supervised by a<br>branch, this field should be populated with the country code of the<br>home Member State of the investment firm OR the country code of<br>the country where the firm has established its head office or<br>registered office.                                                                                                                                |
|     |                               |                                                       | NB: This field is only relevant if the entity is an investment firm.                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| 5   | National Client<br>ID         | RTS<br>22,<br>Annex I, Table<br>2, Field 57 &         | 360T, as an MTF, is required to collect the national client id of the<br>'investment decision maker' (idm) and the 'execution decision<br>maker' (edm).                                                                                                                                                                                                                                                                                                                                                  |
|     |                               | 59<br>RTS<br>24,<br>Annex,<br>Table<br>2, Field 4 & 5 | The fields shown in the configuration tool are derived from the<br>nationality of the user as defined in RTS 22, Annex II. The identifier<br>has to be assigned in accordance with the priority levels in Annex II.<br>When the configuration tool offers several entry fields for the<br>national client Id then the first field has to be used to define the<br>identifier. If the person does not have the first priority identifier, the<br>second priority identifier should be used, and so forth. |
|     |                               |                                                       | E.g. 1: idm A is a citizen of the United Kingdom with a national<br>insurance number "JE 12 34 56 b".                                                                                                                                                                                                                                                                                                                                                                                                    |
|     |                               |                                                       | In this case, idm A would need to enter JE123456b in the<br>configuration as the first priority identifier.                                                                                                                                                                                                                                                                                                                                                                                              |
|     |                               |                                                       | E.g. 2: idm B is a citizen of the United Kingdom but does not have a<br>national insurance number.                                                                                                                                                                                                                                                                                                                                                                                                       |
|     |                               |                                                       | In this case, idm B would need to provide the concatenation<br>(CONCAT) of the birthday, first name and second name in the<br>format as specified in RTS 22, Article 6. The length of the CONCAT<br>string has to be 18 characters.                                                                                                                                                                                                                                                                      |
|     |                               |                                                       | For example, 19800512JOHN#KING#                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| 6   | Trading<br>Capacity           | RTS<br>22,<br>Annex I, Table                          | A default value can be set on a trader by trader basis. The drop<br>down menu consists of the following three values:                                                                                                                                                                                                                                                                                                                                                                                    |
|     |                               | 2, Field 28                                           | - 'DEAL' – dealing on own account                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|     |                               |                                                       | - 'MTCH' – matched principal trading                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
|     | RTS<br>24,<br>Annex,<br>Table | - 'AOTC' – any other capacity                         |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
|     |                               | 2, Field 7                                            | When a trader opens a product definition window within 360T,<br>he/she will see the configured default value in the Trading Capacity<br>field<br>in a new tab called "MIFID".<br>The default value can be<br>overwritten in the product definition window by the trader. Refer to<br>the Figure 12.                                                                                                                                                                                                      |

<span id="page-13-2"></span>Table 1 Field description of user details

|                                        | Sell<br>$EUR \vee$<br>Notional<br>$\rightleftharpoons$ |
|----------------------------------------|--------------------------------------------------------|
|                                        | $USD \vee$ 0<br>Buy                                    |
|                                        | Date                                                   |
|                                        | Wed, 11.10.2017 (<br>1 Week<br>7 Days<br>$\vee$        |
|                                        |                                                        |
|                                        | Clear via EUREX CLEARING                               |
|                                        |                                                        |
|                                        |                                                        |
|                                        |                                                        |
| Provider List                          | Transactions<br>Comments<br>MIFID                      |
| <b>Complex Trade Component ID</b>      |                                                        |
|                                        |                                                        |
| <b>Trading Capacity</b>                | DEAL                                                   |
| <b>Investment Decision within Firm</b> | <b>DEAL</b>                                            |
|                                        | <b>MTCH</b>                                            |
|                                        | <b>AOTC</b><br><b>NONE</b>                             |

<span id="page-13-1"></span>Figure 12: Trading Capacity

The user details can be entered individually or via bulk upload. Please refer to chapter [3.1.1](#page-6-1) of this document for more information regarding mass upload of user details.

The individual can be MTF-enabled after all mandatory data has been entered.

## <span id="page-13-0"></span>**3.3 AutoDealer User**

In case an AutoDealer is used for MTF trading, the AutoDealer user has to be MTF enabled. Refer to [Figure 13](#page-14-0) and the corresponding field descriptions in [Table 2.](#page-14-1)

|                                                                                 |                              |                             | $\vee$ Preferences | $\vee$ Administration<br>$\vee$ Help          | $AA - D \times$          |
|---------------------------------------------------------------------------------|------------------------------|-----------------------------|--------------------|-----------------------------------------------|--------------------------|
| <b>TRADER WORKSHEET</b>                                                         | <b>BRIDGE ADMINISTRATION</b> | $+$                         |                    |                                               |                          |
| Q<br>上鱼<br>登<br>合<br>$\wedge \triangleq$ BankA                                  | OTC<br>K                     | <b>MTF</b>                  |                    |                                               | $\Omega \cap \mathbb{R}$ |
| $\mathcal{O}$<br>$\vee \triangleq$ BankClientA<br>$\vee \triangleq$ BankClientB |                              | MTF Enabled                 | $\bullet$          |                                               |                          |
| $\boxminus^*$<br>BankA.TraderA                                                  | BankA.AutoDealer             | <b>Trading Capacity</b>     |                    |                                               |                          |
| BankA.TraderB                                                                   |                              | <b>Trading Capacity B2B</b> | DEAL               |                                               | $\checkmark$             |
| BankA.TraderC<br>BankA.TraderD                                                  |                              | Trading Capacity PS         | DEAL               |                                               | $\checkmark$             |
|                                                                                 |                              | Algo IDs                    |                    |                                               |                          |
|                                                                                 |                              |                             |                    |                                               | Add Algo ID              |
|                                                                                 |                              |                             |                    |                                               |                          |
|                                                                                 |                              |                             |                    |                                               |                          |
|                                                                                 |                              |                             |                    |                                               |                          |
| $\breve{\nabla}$<br>$\mathbf{C}$                                                |                              |                             |                    | Discard all changes                           | Save                     |
| $\bigcirc$                                                                      |                              | BankA.AutoDealer * X        |                    |                                               |                          |
| <sup>1</sup> BankA.TraderA, BankA // DEMO                                       |                              | FECT                        |                    | Fr, 15. Dez 2017, 09:22:18 GMT // Connected · |                          |

<span id="page-14-0"></span>Figure 13: AutoDealer user

| No. | Field Name           | Details                                                                                                                                                                                                                                                                                                                                                        |
|-----|----------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1   | Trading Capacity B2B | Trading Capacity used for Back2Back trading.                                                                                                                                                                                                                                                                                                                   |
|     |                      | E.g.: Trading Capacity B2B is set to "DEAL".                                                                                                                                                                                                                                                                                                                   |
|     |                      | If a transaction is hedged on the MTF, 360T will record the Trading<br>Capacity of the MTF transaction as "DEAL".                                                                                                                                                                                                                                              |
| 2   | Trading Capacity PS  | A default value can be set for Market Maker APIs.                                                                                                                                                                                                                                                                                                              |
|     |                      | E.g.: Trading Capacity PS is set to "DEAL".                                                                                                                                                                                                                                                                                                                    |
|     |                      | If a bank does not provide the Trading Capacity via the Market<br>Maker API then the Trading Capacity of a MTF transaction will<br>default to the configured value "DEAL".                                                                                                                                                                                     |
| 3   | Algo IDs             | An Algorithmic (algo) trading strategy, identified by its algo ID, is<br>required to undergo conformance tests prior to being utilised on the<br>360T<br>MTF<br>trading<br>venue.<br>Algos<br>which<br>pass<br>the<br>relevant<br>conformance tests are then added by 360T to the Algo ID white-list<br>and are then tradable on the 360T MTF t trading venue. |
|     |                      | The Algo IDs are only applicable for Market Maker APIs.                                                                                                                                                                                                                                                                                                        |

<span id="page-14-1"></span>Table 2: Field description of AutoDealer user details

Please note that the autodealer routing must be reconfigured for the MTF trading in the Business Configuration Tool.

## <span id="page-15-0"></span>**3.4 Security and data protection**

Individual confidential data, including Nationality and National Client ID will be recorded and maintained within the 360T datacenters located in Frankfurt, in an encrypted format. As part of the MTF solution, 360T will roll out infrastructure which allows only dedicated internal reporting services to decrypt MTF data. An example of a decryption service would be an "Extract, Transform, Load" (ETL) process, responsible for the submission of transaction reports to an "approved reporting mechanism" (ARM). Reports will not be saved to persistent storage and will not be backed up.

Please note that the 360T Client Advisory Services (CAS) team has NO access to the confidential data of MTF customers. The responsibility lies with the MTF participant to assure that all data entered in the configuration tool is valid.

## <span id="page-15-1"></span>**3.5 Data retention period**

Data are stored according to regulatory and legal requirements, including requirements of the EU General Data Protection Regulation. They will be deleted once they are no longer necessary in relation to the purposes for which they were collected or otherwise processed and relevant reporting and archiving periods have elapsed.

## <span id="page-15-2"></span>**4 EXTERNAL MAPPING**

Every configured user has a 360T specific short-code. This short-code will be used during the trading workflow in order to identify the investment decision maker and execution decision maker.

Individual user details of manual traders who are using a proprietary trading system connected to 360T's MTF via an API are also required to be captured in the tool. Such manual traders are then identified by their 360T specific short-code via the API, as shown in [Figure 14.](#page-15-3) It is mandatory that user details are provided prior to the usage of the corresponding short-codes. In case a short-code is received in the execution report with unknown user details, 360T would reject the execution.

![](_page_15_Figure_10.jpeg)

<span id="page-15-3"></span>Figure 14 360T MTF Identification of users behind API

The External Mapping feature allows to map the above mentioned external codes with 360T short codes. Additionally, it provides a mapping functionality for those liquidity providers who do not intend to maintain 360T specific short-codes.

|                                | <b>RFS REQUESTER</b>                  | <b>BRIDGE ADMINISTRATION</b> | $+$             |      |                         | $\vee$ Preferences | $\vee$ Administration $\vee$ Help             |  | $AA = B \times$ |
|--------------------------------|---------------------------------------|------------------------------|-----------------|------|-------------------------|--------------------|-----------------------------------------------|--|-----------------|
|                                | 合                                     |                              |                 |      | Administration Start    |                    |                                               |  |                 |
|                                |                                       |                              | Configurations  |      |                         |                    |                                               |  |                 |
|                                |                                       |                              |                 |      | <b>CO</b>               |                    |                                               |  |                 |
|                                |                                       |                              | Regulatory Data |      | <b>External Mapping</b> |                    |                                               |  |                 |
|                                |                                       |                              |                 |      |                         |                    |                                               |  |                 |
|                                |                                       |                              |                 |      |                         |                    |                                               |  |                 |
|                                |                                       |                              |                 |      |                         |                    |                                               |  |                 |
|                                |                                       |                              |                 |      |                         |                    |                                               |  |                 |
| $\frac{\phi}{D}$<br>$\bigcirc$ |                                       |                              |                 |      |                         |                    |                                               |  |                 |
|                                | TradeAsG.TreasurerA, TradeAsG // DEMO |                              |                 | esch |                         |                    | Fr, 17. Nov 2017, 10:47:24 GMT // Connected · |  |                 |

<span id="page-16-0"></span>Figure 15 Bridge Administration Homepage: External Mappings feature

The External Mapping configuration can be accessed via the Bridge Administration homepage. Clicking on the shortcut will open the initial view with "Create New Configuration" button.

<span id="page-16-1"></span>![](_page_16_Picture_5.jpeg)

Figure 16 External Mapping: Create new configuration.

The admin user can either add external codes individually or mass upload multiple codes using a CSV template.

In order to add a code, the user has to select the "Add Code" icon, enter the external code and select the corresponding 360T short code of the individual.

|                                              |                                                                      |                         |                      |                        | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\parallel$ AA $ \Box \times$ |                                                                                                                                       |
|----------------------------------------------|----------------------------------------------------------------------|-------------------------|----------------------|------------------------|------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------|
|                                              | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                 | $+$                     |                      |                        |                                                                                    |                                                                                                                                       |
|                                              | $Q \otimes    \_ \equiv$<br>$\overline{\left\langle \right\rangle }$ | <b>External Mapping</b> |                      |                        |                                                                                    | $\textcolor{red}{\textbf{O}} \textcolor{red}{\textbf{O}} \textcolor{red}{\widehat{\textbf{C}}} \textcolor{red}{\widehat{\textbf{E}}}$ |
| 合                                            | $\land \triangleq$ TradeAsG                                          | ⊡∑∑⊡                    |                      |                        |                                                                                    |                                                                                                                                       |
| $\mathcal{G}$                                | <sup>1</sup> / <sub>10</sub> TradeAsG.TAS.B1                         |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              | TradeAsG.TAS.B2                                                      |                         | <b>External Code</b> | <b>Individual Name</b> |                                                                                    |                                                                                                                                       |
| $\qquad \qquad \textcircled{\scriptsize{1}}$ | TradeAsG.TAS.B3                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              | <sup>1</sup> / <sub>10</sub> TradeAsG.TradeAsG                       |                         |                      |                        | <b>Add Code</b>                                                                    |                                                                                                                                       |
| $\circ$                                      |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
| $\breve{\nabla}$                             |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
| $\mathbb C$                                  |                                                                      |                         |                      |                        | Discard all changes                                                                | Save                                                                                                                                  |
|                                              |                                                                      |                         |                      |                        |                                                                                    |                                                                                                                                       |
| $\circ$                                      |                                                                      | TradeAsG X              |                      |                        |                                                                                    |                                                                                                                                       |
|                                              | TradeAsG.TreasurerA, TradeAsG // DEMO                                |                         | <b>EECH</b>          |                        | Fr, 17. Nov 2017, 10:50:49 GMT // Connected ·                                      |                                                                                                                                       |

<span id="page-17-0"></span>Figure 17 External Mapping: Adding external code

In order to mass upload the external short codes, the admin user has to download a CSV template including a list of all available users and add the codes to this file. The file can then be uploaded and the list of mapped users will be immediately visible in the External Mapping tab. The uploaded mappings can be discarded or saved.

By default, only one mapping table can be created. In case several mapping tables for different interfaces are required, please contact [<EMAIL>](mailto:<EMAIL>) who can create additional tables.

| 簽<br>$\langle$<br>$\mathfrak{O} \, \curvearrowright \, \equiv$<br>$\alpha$<br><b>External Mapping</b><br><u> 1951   1951   1951   1951   1951   1951   1951   1951   1951   1951   1951   1951   1951   1951   1951   195</u><br>画<br>合<br>自工业<br>$\mathcal{G}$<br>$\sim \underline{\hat{m}}$ TradeAsG<br>$\mathbf{A}$<br>$\triangleq$ Individual Name<br><b>External Code</b><br><sup> TradeAsG.TAS.B1</sup><br>$\qquad \qquad \textcircled{\scriptsize{1}}$<br>$\mathbb{F} \times \mathbb{R}$<br>TradeAsG.TreasurerB<br>TradeAsG.Smith<br>FradeAsG.TAS.B2<br><sup> TradeAsG.TAS.B3</sup><br>TradeAsG.TreasurerA<br>TradeAsG.Mueller<br>F û<br>$\circ$<br>TradeAsG.TradeAsG<br>F û<br>TradeAsG.TreasurerC<br>TradeAsG.Kowalski<br>Add Code<br>$\vec{\zeta}$<br><b>Discard all changes</b><br>Save<br>$\overline{\mathbb{D}}$<br>$\bigcirc$<br>TradeAsG X<br>TradeAsG.TAS.B1 $\times$   TradeAsG.TradeAsG $\times$  <br>TradeAsG.TAS.B2 X | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b> | $+$ |             | $\vee$ Preferences $\vee$ Administration $\vee$ Help<br>$AA - B \times$ |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|-----|-------------|-------------------------------------------------------------------------|
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |                                                      |     |             |                                                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |                                                      |     |             |                                                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |                                                      |     |             |                                                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |                                                      |     |             |                                                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | TradeAsG.TreasurerA, TradeAsG // DEMO                |     | <b>EEUT</b> | Fr, 17. Nov 2017, 11:43:18 GMT // Connected ·                           |

<span id="page-18-0"></span>Figure 18 External Mapping: Upload of external codes

Each manually entered or uploaded mapping can be modified or deleted.

|                                                                                                                       |                                |                      |                     | $\vee$ Preferences $\vee$ Administration $\vee$ Help | $AA - \Box X$ |
|-----------------------------------------------------------------------------------------------------------------------|--------------------------------|----------------------|---------------------|------------------------------------------------------|---------------|
| <b>BRIDGE ADMINISTRATION</b><br>RFS REQUESTER                                                                         | $+$                            |                      |                     |                                                      |               |
| Q 泰   L<br>$\left\langle \right\rangle$<br>舎<br>$\land$ $\hat{\blacksquare}$ TradeAsG<br>$\odot$<br>直 TradeAsG.TAS.B1 | <b>External Mapping</b><br>自立出 |                      |                     |                                                      | $n \sim E$    |
| <sup> TradeAsG TAS B2</sup>                                                                                           |                                | <b>External Code</b> | Individual Name     |                                                      |               |
| $\Box$<br><sup> TradeAsGTAS B3</sup>                                                                                  |                                | TradeAsG Smith       | TradeAsG TreasurerB | <b>ZE</b>                                            |               |
| 童 TradeAsG.TradeAsG<br>$\circ$                                                                                        |                                | TradeAsG Mueller     | TradeAsG.TreasurerA | ジョ                                                   |               |
|                                                                                                                       |                                | TradeAsG.Kowalski    | TradeAsG.TreasurerC | ■ 全<br>$\vee$                                        |               |
|                                                                                                                       |                                |                      |                     | Add Code                                             |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
|                                                                                                                       |                                |                      |                     |                                                      |               |
| $\langle \rangle$                                                                                                     |                                |                      |                     |                                                      |               |
| $\mathbf C$                                                                                                           |                                |                      |                     | Discard all changes                                  | Save          |
| $\bigcirc$                                                                                                            | TradeAsG X                     |                      |                     |                                                      |               |
| TradeAsG.TreasurerB, TradeAsG // DEMO                                                                                 |                                | EECIN                |                     | Fr, 17 Nov 2017, 11:45:22 GMT // Connected @         |               |

<span id="page-18-1"></span>Figure 19 External Mapping: Modification of external codes

It is mandatory that user details are provided prior to the usage of the corresponding shortcodes.

## <span id="page-19-0"></span>**5 CREATE AN EXTERNAL INDIVIDUAL**

Section 3.2 describes that admin users of 360T MTF participants are required to enter the personal details of individual users within the Bridge administration tool.

In order to capture user-static details each individual acting as either an execution decision maker (EDM) or investment decision maker (IDM) on the 360T MTF must have a unique user ID.

A unique user ID is created for an individual depending on the user type:

- (1) **Platform users**: These individuals log in directly to the 360T GUI to access the MTF venue. Platform users receive a unique user ID from 360Ts Client Advisory Services team when access is requested for them via the New User Creation process.
- (2) **External users**: These individuals are not direct users and do not log in to the 360T GUI. They may still be identified as an EDM or IDM on the 360T MTF venue and/or access the venue via an API. Unique user IDs for these individuals are created by 360T MTF admin users via the "Create an External Individual" wizard.

The Create an External Individual wizard can be accessed via the "Wizards" quick link from the Bridge Administration homepage. The Wizards homepage may contain several types of Help Wizards to assist in performing quick actions.

![](_page_19_Picture_9.jpeg)

Figure 20 Bridge Administration: Help Wizard.

<span id="page-19-1"></span>Click Create an External Individual to open the Help Wizard Step 1 - Please select an Institution. In case your setup contains multiple entities the main TEX entity will appear first in the Institution Tree. Related entities (trade as, trade-on-behalf, ITEX etc.) will appear below the main TEX entity.

The user may be configured as an Execution Decision Maker (EDM) or Investment Decision Maker (IDM) of the selected institution. Therefore, care should be taken in selecting the correct entity.

Highlight the Institution and click Next.

|                          |                                              |                                         |                      |                         |                              |                              |                   | $\vee$ Preferences $\vee$ Administration $\vee$ Help |                                                      | $\Box$ AA - $\Box$ X |  |
|--------------------------|----------------------------------------------|-----------------------------------------|----------------------|-------------------------|------------------------------|------------------------------|-------------------|------------------------------------------------------|------------------------------------------------------|----------------------|--|
|                          |                                              | <b>RFS REQUESTER</b>                    | <b>DEAL TRACKING</b> |                         | <b>BRIDGE ADMINISTRATION</b> |                              | $+$               |                                                      |                                                      |                      |  |
|                          | 合                                            | $2$ $3$ $\nu$<br>$\vert 1 \vert$        |                      |                         |                              |                              | Create Individual |                                                      |                                                      |                      |  |
|                          | $\mathcal{G}$                                |                                         |                      |                         |                              | Please select an Institution |                   |                                                      |                                                      |                      |  |
|                          | $\qquad \qquad \textcircled{\scriptsize{1}}$ |                                         |                      |                         | Q Search Institution         |                              |                   |                                                      |                                                      |                      |  |
|                          | $\bullet$                                    |                                         |                      | $\vee \hat{m}$ TradeAsG |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          | ♬<br>$\vec{R}_\chi$                          |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
|                          |                                              |                                         |                      |                         |                              |                              |                   |                                                      |                                                      |                      |  |
| $\frac{1}{2}$<br>$\circ$ |                                              | Previous                                |                      |                         |                              |                              |                   |                                                      | Cancel                                               | <b>Next</b>          |  |
|                          |                                              | 1 TradeAsG.TreasurerA, TradeAsG // DEMO |                      |                         |                              | <b>EECT</b>                  |                   |                                                      | Wed, 18. Jul 2018, 08:54:14 GMT // Connected [FFM] . |                      |  |

<span id="page-20-0"></span>Figure 21 Bridge Administration: Help Wizard Step 1 - Select an Institution.

Pursuant to RTS 24, 360T has to record trade information enriched with user details related to the execution decision maker and investment decision maker. The majority of personal details and default values for trader-related dynamic fields are entered within the Regulatory Category of the Bridge administration tool.

As a first step, some basic information is required in order to create the user. Please refer to [Figure 22](#page-21-0) and the corresponding field descriptions in [Table 3.](#page-22-1)

|                             |                             |                                                                                                           |               |                                       |                                  | $\vee$ Preferences | $\vee$ Administration                                | $\vee$ Help | $\Box$ | $AA - CD \times$ |  |
|-----------------------------|-----------------------------|-----------------------------------------------------------------------------------------------------------|---------------|---------------------------------------|----------------------------------|--------------------|------------------------------------------------------|-------------|--------|------------------|--|
|                             |                             | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b>                                                              |               | <b>BRIDGE ADMINISTRATION</b>          | $+$                              |                    |                                                      |             |        |                  |  |
|                             | 合                           | $\left  \begin{array}{c} 1 \\ 2 \end{array} \right $ $\left  \begin{array}{c} 2 \\ 3 \end{array} \right $ |               |                                       | Create Individual                |                    |                                                      |             |        |                  |  |
|                             | $\mathcal{G}$               |                                                                                                           |               | Please fill in the Individual details |                                  |                    |                                                      |             |        |                  |  |
|                             | $\mathbf{\Theta}$           |                                                                                                           | Login Name *  |                                       | TradeAsG                         | $\checkmark$       |                                                      |             |        |                  |  |
|                             | $\circ$                     |                                                                                                           | Last Name*    |                                       | Last Name should be specified    |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           |               |                                       |                                  |                    |                                                      |             |        |                  |  |
|                             | 厚                           |                                                                                                           | First Name*   |                                       | First Name should be specified   |                    |                                                      |             |        |                  |  |
|                             | $\mathcal{L}_{\mathcal{K}}$ |                                                                                                           | Description   |                                       |                                  |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           | Email *       |                                       |                                  |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           |               |                                       | Email should be specified        |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           | Phone Number* |                                       |                                  |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           |               |                                       | Phone Number should be specified |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           | Fax Number    |                                       |                                  |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           | Salutation *  |                                       | $MR \vee$                        |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           | Country *     |                                       | Germany                          |                    | $\checkmark$                                         |             |        |                  |  |
|                             |                             |                                                                                                           |               |                                       |                                  |                    | * Mandatory                                          |             |        |                  |  |
|                             |                             |                                                                                                           |               |                                       |                                  |                    |                                                      |             |        |                  |  |
|                             |                             |                                                                                                           |               |                                       |                                  |                    |                                                      |             |        |                  |  |
| $\frac{Q}{D}$<br>$\bigcirc$ |                             | <b>Previous</b>                                                                                           |               |                                       |                                  |                    |                                                      |             | Cancel | Next             |  |
|                             |                             | 1/ TradeAsG.TreasurerA, TradeAsG // DEMO                                                                  |               |                                       | <b>esun</b>                      |                    | Wed, 18. Jul 2018, 08:58:20 GMT // Connected [FFM] · |             |        |                  |  |

<span id="page-21-0"></span>Figure 22 Bridge Administration: Help Wizard Step 2 – Individual details.

| No. | Field Name   | Details                                                                                                                                                                                                                                                                                                                                                                                          |  |  |  |
|-----|--------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|
| 1   | Login Name   | Every<br>configured<br>user<br>has<br>a<br>360T<br>specific<br>short-code<br>(PREFIX.Lastname). This short-code will be used during the trading<br>workflow in order to identify the investment decision maker (IDM)<br>and execution decision maker (EDM).<br>The PREFIX can be selected via the dropdown menu if more than<br>one prefix is configured for your entity. The Login Name will be |  |  |  |
|     |              | autogenerated based on the user's last name.                                                                                                                                                                                                                                                                                                                                                     |  |  |  |
| 2   | Last Name    | This field is mandatory and will be included in the reporting data<br>sent to the appropriate Regulatory Authority.                                                                                                                                                                                                                                                                              |  |  |  |
| 3   | First Name   | This field is mandatory and will be included in the reporting data<br>sent to the appropriate Regulatory Authority.                                                                                                                                                                                                                                                                              |  |  |  |
| 4   | Description  | This field is not mandatory. It is a free text field.                                                                                                                                                                                                                                                                                                                                            |  |  |  |
| 5   | Email        | This field is mandatory and must contain "@".                                                                                                                                                                                                                                                                                                                                                    |  |  |  |
|     |              | It is recommended, but not required, that the field contain the user's<br>professional email address. If the user is converted to a platforml<br>user 360T requires a valid value prior to release of access<br>credentials.                                                                                                                                                                     |  |  |  |
| 6   | Phone Number | This field is not mandatory.                                                                                                                                                                                                                                                                                                                                                                     |  |  |  |
|     |              | Any value entered must begin with "+" followed by a country code. It<br>is recommended, but not required, that the field contain the user's<br>professional phone number. If the user is converted to a platform<br>user 360T requires a valid value prior to release of access<br>credentials.                                                                                                  |  |  |  |
| 7   | Fax Number   | This field is not mandatory.                                                                                                                                                                                                                                                                                                                                                                     |  |  |  |
|     |              | Any value entered must begin with "+" followed by a country code.                                                                                                                                                                                                                                                                                                                                |  |  |  |

| 8 | Salutation | This field is mandatory. The field contains three possible values:<br>"MR", "MS" or "DR". "MR" is populated by default.                                                                                                                                                                      |  |  |
|---|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
| 9 | Country    | This field is mandatory. The country of the company is populated by<br>default.                                                                                                                                                                                                              |  |  |
|   |            | This field is for administrative purposes only and does not<br>correspond to the field "Country of the Branch" (see Regulatory<br>Category). A helpful warning will be provided if the Country and<br>Phone Number country code do not match. The warning will not<br>prevent user creation. |  |  |

<span id="page-22-1"></span>Table 3: Field description of External user details

#### Add values to the mandatory fields and click Next.

|                          |                                              |                              |                                       | $\Box$ AA = $\Box$ X<br>$\vee$ Preferences $\vee$ Administration $\vee$ Help |
|--------------------------|----------------------------------------------|------------------------------|---------------------------------------|------------------------------------------------------------------------------|
|                          | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b> | <b>BRIDGE ADMINISTRATION</b> | $+$                                   |                                                                              |
|                          | $\begin{pmatrix} 1 & 2 & 3 \end{pmatrix}$    |                              | Create Individual                     |                                                                              |
|                          | 合                                            |                              |                                       |                                                                              |
|                          | $\mathcal{G}$                                |                              | Please fill in the Individual details |                                                                              |
|                          | $\Box$                                       | Login Name *                 | Doe<br>TradeAsG<br>$\vee$             |                                                                              |
|                          |                                              | Last Name *                  | Doe                                   |                                                                              |
|                          | $\circ$                                      | First Name *                 | . .<br>John                           |                                                                              |
|                          |                                              | <b>Description</b>           |                                       |                                                                              |
|                          | 見                                            | Email *                      | <EMAIL>                    |                                                                              |
|                          | $\mathcal{D}_{\mathcal{K}}$                  | Phone Number*                |                                       |                                                                              |
|                          |                                              |                              |                                       |                                                                              |
|                          |                                              | <b>Fax Number</b>            |                                       |                                                                              |
|                          |                                              | Salutation *                 | $MR$ $\vee$                           |                                                                              |
|                          |                                              | Country *                    | Germany                               | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                     |
|                          |                                              |                              |                                       | * Mandatory                                                                  |
|                          |                                              |                              |                                       |                                                                              |
|                          |                                              |                              |                                       |                                                                              |
|                          |                                              |                              |                                       |                                                                              |
|                          |                                              |                              |                                       |                                                                              |
|                          |                                              |                              |                                       |                                                                              |
|                          |                                              |                              |                                       |                                                                              |
| $\frac{1}{2}$<br>$\circ$ | Previous                                     |                              |                                       | Cancel<br><b>Next</b>                                                        |
|                          | TradeAsG.TreasurerA, TradeAsG // DEMO        |                              | EECH                                  | Wed, 18. Jul 2018, 11:18:45 GMT // Connected [FFM] .                         |

<span id="page-22-0"></span>Figure 23 Bridge Administration: Help Wizard Step 2 – Individual details completed.

Before creating the user, carefully review the individual details for correctness. Click Create.

|         |                |                                                           |                              | $\vee$ Preferences                   | $\vee$ Administration | $A = B \times$<br>$\times$ Help                      |
|---------|----------------|-----------------------------------------------------------|------------------------------|--------------------------------------|-----------------------|------------------------------------------------------|
|         |                | RFS REQUESTER $\vee$<br><b>DEAL TRACKING</b>              | <b>BRIDGE ADMINISTRATION</b> | $^{+}$                               |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         | <mark>△</mark> | $\left  \frac{1}{2} \right $ $\left  \frac{3}{2} \right $ |                              | Create Individual                    |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         | $\mathcal{G}$  |                                                           |                              | Overview: Please review your changes |                       |                                                      |
|         | $\Box$         | <b>Individual Name</b>                                    | TradeAsG.Doe                 |                                      |                       |                                                      |
|         |                |                                                           | <b>Last Name</b>             | <b>Doe</b>                           |                       |                                                      |
|         | $\circ$        |                                                           | First Name                   | John                                 |                       |                                                      |
|         |                |                                                           | Description                  |                                      |                       |                                                      |
|         | 見              |                                                           | Email                        | <EMAIL>                   |                       |                                                      |
|         | 磙              |                                                           | <b>Phone Number</b>          |                                      |                       |                                                      |
|         |                |                                                           | <b>Fax Number</b>            |                                      |                       |                                                      |
|         |                |                                                           | Salutation                   | Mr.<br>External                      |                       |                                                      |
|         |                |                                                           | Position<br>Country          | Germany                              |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
|         |                |                                                           |                              |                                      |                       |                                                      |
| 0 Q     |                |                                                           |                              |                                      |                       |                                                      |
|         |                | <b>Previous</b>                                           |                              |                                      |                       | Create<br>Cancel                                     |
| $\circ$ |                |                                                           |                              |                                      |                       |                                                      |
|         |                | TradeAsG.TreasurerA, TradeAsG // DEMO                     |                              | escre                                |                       | Wed, 18. Jul 2018, 11:22:53 GMT // Connected [FFM] · |

<span id="page-23-0"></span>Figure 24 Bridge Administration: Help Wizard Step 3 – Individual details overview.

The new user will be created immediately and will be visible in the Regulatory Category and External Mapping Category. The user will appear with an orange icon.

<span id="page-23-1"></span>![](_page_23_Picture_5.jpeg)

Figure 25 Bridge Administration: External user.

## <span id="page-24-0"></span>**6 CONTACTING 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **United Arab Emirates**

*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

#### **EMEA Americas**

## **USA**

*360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900

#### **Middle East Asia Pacific**

#### **Singapore**

*360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756