![](_page_0_Picture_0.jpeg)

# **US<PERSON> GUIDE BRIDGE ADMINISTRATION COUNTERPART RELATIONSHIP MANAGEMENT**

# **TEX MULTIDEALER TRADING SYSTEM**

**User Guide 360T Bridge Administration: Counterpart Relationship Management for Market Maker**

Release 4.17 (March 2023)

© 360 Treasury Systems AG, 2023 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG

![](_page_1_Picture_0.jpeg)

# **CONTENTS**

| 1 | Introduction                                               | 4  |
|---|------------------------------------------------------------|----|
| 2 | Getting Started                                            | 4  |
| 3 | Counterpart Relationship Management                        | 6  |
|   | 3.1<br>Opening the counterpart relationship configuration  | 6  |
|   | 3.2<br>Notification about incoming requested relationships | 7  |
|   | 3.3<br>Accepting or rejecting a relationship               | 8  |
| 4 | Filter, search and sorting options                         | 11 |
|   | 4.1<br>Filter & search                                     | 11 |
|   | 4.2<br>Sort                                                | 12 |
| 5 | Download and upload counterparts                           | 13 |
|   | 5.1<br>Download counterparts                               | 13 |
|   | 5.2<br>Upload counterparts                                 | 13 |
| 6 | Audit log                                                  | 14 |
| 7 | Contacting 360T                                            | 19 |

![](_page_2_Picture_0.jpeg)

# **FIGURES**

| Figure 1 Header Bar4                                               |  |
|--------------------------------------------------------------------|--|
| Figure 2 Bridge Administration: Homepage<br>5                      |  |
| Figure 3 Quick Navigation Toolbar<br>5                             |  |
| Figure 4 Bridge Administration CRM<br>6                            |  |
| Figure 5 Opening Counterpart Relationships Configuration7          |  |
| Figure 6 Notifications7                                            |  |
| Figure 7 Taker relationships<br>8                                  |  |
| Figure 8 Trader Worksheet: Relationship not accepted warning10     |  |
| Figure 9 Accepting a relationship<br>10                            |  |
| Figure 10 Rejecting a relationship<br>11                           |  |
| Figure 11 Selecting Multiple Relationships<br>11                   |  |
| Figure 12 Searching relationships<br>12                            |  |
| Figure 13 Filtered relationships<br>12                             |  |
| Figure 14 Sorting relationships<br>12                              |  |
| Figure 15 Download counterparts13                                  |  |
| Figure 16 Relationships csv file13                                 |  |
| Figure 17 Upload counterparts<br>13                                |  |
| Figure 18 Updated relationships14                                  |  |
| Figure 19 Audit Log14                                              |  |
| Figure 20 Audit Log criteria selection15                           |  |
| Figure 21 Audit Log Category selection15                           |  |
| Figure 22 Audit Log Company selection<br>16                        |  |
| Figure 23 Audit Log CRM results16                                  |  |
| Figure 24 -<br>Audit Log Event Date -<br>pre defined periods<br>17 |  |
| Figure 25 -<br>Audit Log Event Date -<br>calendars17               |  |
| Figure 26 Audit Log<br>CRM results and details18                   |  |
| Figure 27 Audit Log change details<br>18                           |  |

![](_page_3_Picture_0.jpeg)

# **User Guide 360T Bridge Counterpart Relationship Management**

# <span id="page-3-0"></span>**1 Introduction**

This user manual describes the functionality of the Counterpart Relationship Management from a market maker's perspective.

The Counterpart Relationship Management allows market takers and market makers to manage their relationships. All changes made become effective immediately.

The Bridge Administration tool is available only to users who have administrator rights. As part of the onboarding process, a sub-set of administrator users to whom the tool is to be accessible must be defined. These users are then responsible for entering the information as described in the following chapters. Additionally, it is possible to grant read-only access to certain administrator users, however, only viewing and downloading CRM information will be possible with this role. <NAME_EMAIL> or your customer relationship manager for setting up the administrator rights.

# <span id="page-3-1"></span>**2 Getting Started**

The Bridge Administration tool can be accessed via the menu option "Administration" in the screen header of the Bridge application. Several features may be visible in the left menu depending on user permissions.

![](_page_3_Picture_9.jpeg)

**Figure 1 Header Bar**

<span id="page-3-2"></span>The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools and actions for the user.

![](_page_4_Picture_0.jpeg)

A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.

![](_page_4_Figure_3.jpeg)

<span id="page-4-0"></span>**Figure 2 Bridge Administration: Homepage**

![](_page_4_Figure_5.jpeg)

<span id="page-4-1"></span>**Figure 3 Quick Navigation Toolbar**

![](_page_5_Picture_0.jpeg)

# <span id="page-5-0"></span>**3 Counterpart Relationship Management**

The "CRM" quick link, shown in Figure 2 [Bridge Administration: Homepage,](#page-4-0) opens a navigation panel which contains an institution tree. The tree includes a list of all entities configured under the main entity.

![](_page_5_Picture_4.jpeg)

**Figure 4 Bridge Administration CRM**

## <span id="page-5-2"></span><span id="page-5-1"></span>**3.1 Opening the counterpart relationship configuration**

Clicking on the entity name in the quick navigation tool bar, shown in [Figure 3](#page-4-1) Quick [Navigation Toolbar,](#page-4-1) opens the counterpart relationships panels. Relationships with counterparts can be established for the different trading methods RFS, Orders, SEP, MmFund, ECP, TripartyRepo and MidMatch. For each of these products there is a different tab on the screen, as shown in Figure 5 [Opening Counterpart Relationships](#page-6-1)  [Configuration.](#page-6-1)

In "Taker Relationships" section, the market maker can review existing and incoming requested relationships as described ahead in chapter [3.3.](#page-7-0)

![](_page_6_Picture_0.jpeg)

|                         |                             | <b>BRIDGE ADMINISTRATION</b><br><b>TRADER WORKSHEET</b> | $+$                                           | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\Box$ $\mathbb{D}^1$ $\circledast$ AA $-\circledast \times$ |                                                           |    |
|-------------------------|-----------------------------|---------------------------------------------------------|-----------------------------------------------|-------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|----|
|                         |                             | $\rightarrow$<br>$\alpha$                               | RFS Orders SEP                                |                                                                                                                   |                                                           | 土土 |
|                         | 合                           | $\mathbf{\hat{m}}$ > BankP                              | Taker Relationships Auto Accept Configuration |                                                                                                                   |                                                           |    |
|                         | $\mathcal{L}_{\mathcal{T}}$ | $\underline{\hat{\pi}}$ BankP                           |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         | $\ensuremath{\boxdot}$      |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         | $\circledcirc$              |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         | $\overline{\text{vir}}$     |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         | $\overline{d}$              |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
| $\zeta_{\rm s}^{\rm H}$ |                             |                                                         |                                               |                                                                                                                   |                                                           |    |
| $\mathbb C$<br>$\circ$  |                             |                                                         |                                               |                                                                                                                   | Discard all Changes Construction                          |    |
| Θ                       |                             |                                                         | Bank $P \times$                               |                                                                                                                   |                                                           |    |
|                         |                             | 1 BankP.TraderA, BankP // DEMO                          | $\overline{\phantom{a}}$                      |                                                                                                                   | Fri, 23. Apr 2021, 13:47:55 GMT // Connected [FFM] • DEMO |    |

<span id="page-6-1"></span>**Figure 5 Opening Counterpart Relationships Configuration**

## <span id="page-6-0"></span>**3.2 Notification about incoming requested relationships**

In case a market taker requests a relationship with a market maker, the market maker's counterpart relationship management administrator will be notified about this request.

The notification is displayed by clicking on the bell icon present in the upper-right side of the screen as shown in Figure 6 [Notifications.](#page-6-2)

| Preferences | $\vee$ Administration | $\vee$ Help                                                             | ◑                                                                                                                                                                                             | Aа |   | 吊 |
|-------------|-----------------------|-------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----|---|---|
|             |                       |                                                                         | Notifications (0/1)                                                                                                                                                                           |    | ロ |   |
|             |                       | <b>Information</b><br>counterpart relationships.<br>4/23/21, 4:11:44 PM | There are changes in the company<br>Please review the changes in the<br>Counterpart Relationships tool.<br>Please confirm by saving the configuration<br>to avoid this message in the future. |    |   | 侕 |
|             |                       |                                                                         |                                                                                                                                                                                               |    |   |   |

## <span id="page-6-2"></span>**Figure 6 Notifications**

![](_page_7_Picture_0.jpeg)

If the administrator is not logged in with an open counterpart relationship management tool at the time of an incoming requested relationship, the notification will be available at the next login.

## <span id="page-7-0"></span>**3.3 Accepting or rejecting a relationship**

When a market taker requests a relationship with a market maker, the market maker's counterpart relationship management administrator will see a new entry on the Taker Relationships tab. This entry shows initially the Counterparty Status as "Accepted" and My Status as "Undefined", indicating that this relationship is yet to be accepted or rejected by the market maker. The relationship can be accepted or rejected either directly in the GUI or via csv upload (see chapter [5.2\)](#page-12-2).

Additionally, market maker can be enabled for Auto-Accept Configuration, which allows to automatically accept requests from daughter entities of selected requesters. Please contact [<EMAIL>](mailto:<EMAIL>) to enable this feature.

The refresh button displayed on the upper-left side of the screen can be used to update the statuses or new incoming requests while the administrator is logged in as shown in Figure 7 [Taker relationships.](#page-7-1)

|                                                                         | <b>TRADER WORKSHEET</b>                   | $+$<br><b>BRIDGE ADMINISTRATION</b>                               |                                    |                      |                                 |                                        | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\Diamond$ $\Diamond$ $\Diamond$ AA $ \ominus$ X |                                                           |
|-------------------------------------------------------------------------|-------------------------------------------|-------------------------------------------------------------------|------------------------------------|----------------------|---------------------------------|----------------------------------------|-------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|
| 合                                                                       | Q<br>$\hat{\mathsf{m}}$ > BankP           | RFS Orders SEP<br>$\rightarrow$<br><b>Taker Relationships (1)</b> | Auto Accept Configuration          |                      |                                 |                                        |                                                                                                       | 土土                                                        |
| $\sqrt{2}$<br>$\ensuremath{\boxdot}$                                    | <b>血 BankP</b>                            |                                                                   |                                    |                      | $\alpha$                        | $\rightarrow$                          |                                                                                                       |                                                           |
| $\circledast$                                                           |                                           | Counterpart<br>BankClientP                                        | <b>Legal Entity</b><br>BankClientP | Long Name<br>"Empty" | My Status<br><b>O</b> Undefined | <b>Counterparty Status</b><br>Accepted |                                                                                                       | $\checkmark$ $\times$                                     |
|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |
|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |
|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |
|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |
|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |
| $\zeta^{\star}_{\mathbf{z}^{\star}}$<br>$\mathbb C$<br>$\bigcirc$       |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       | Save<br>Discard all Changes                               |
| $\qquad \qquad \qquad \qquad \qquad \qquad \qquad \qquad \qquad \qquad$ |                                           | BankP $\times$                                                    |                                    |                      |                                 |                                        |                                                                                                       |                                                           |
|                                                                         | <sup>1</sup> BankP.TraderA, BankP // DEMO |                                                                   |                                    |                      | <b>EBCT</b>                     |                                        |                                                                                                       | Fri, 23. Apr 2021, 14:44:59 GMT // Connected [FFM] · DEMO |

## <span id="page-7-1"></span>**Figure 7 Taker relationships**

For counterpart relationships the following statuses exist:

- Initial status undefined displayed as
- Status accepted displayed as
- Status rejected displayed as

The initial status of a new relationship is undefined.

![](_page_8_Picture_0.jpeg)

![](_page_9_Picture_0.jpeg)

In case the administrator is not logged in while a relationship request comes in, and the counterpart sends a RFS request, it will be routed for manual pricing as shown in Figure 8 [Trader Worksheet: Relationship not accepted warning.](#page-9-0) The traders of the market maker company will see that the relationship with that requester has not been accepted. The trader should then contact the company's counterpart relationship management administrator in order to finalize the relationship. The trader can price the requests for quote of the company.

All other deal types can only be done after the market maker has explicitly accepted the relationship.

|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 | $\vee$ Preferences                                  | v Administration<br>$\vee$ Help                                                                  |                                                                                                                       |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|------------------------------|--------------------|-------------|---------|-------------------|---------|-----------------|-----------------------------------------------------|--------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                                                                                                                                                    | <b>TRADER WORKSHEET</b>                                                                                                                                                          |          | <b>BRIDGE ADMINISTRATION</b> |                    | $+$         |         |                   |         |                 |                                                     |                                                                                                  |                                                                                                                       |
|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     |                                                                                                  | 章 宣                                                                                                                   |
| $Q \pm$<br>Dealer Intervention (1)<br><b>Fixing Orders Intervention (0)</b><br>Market Orders Intervention (0)                                                                                                                                                      |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         | • Request Offer | $Q \equiv$                                          |                                                                                                  |                                                                                                                       |
|                                                                                                                                                                                                                                                                    | <b>FX Forward</b><br><b>Effective C</b><br>Reference $# \vee$ Timeout<br><b>Legal Entity</b><br><b>Currencies</b><br>Notional A<br>Type<br>Requester C<br>Requester A<br>Product |          |                              |                    |             |         |                   |         |                 |                                                     |                                                                                                  |                                                                                                                       |
|                                                                                                                                                                                                                                                                    | <b>RFS</b>                                                                                                                                                                       | ******** | 00:37                        | <b>BankClientP</b> | BankClientP | Two Way | <b>FX Forward</b> | EUR/USD |                 | 1,000.00 EUR   Tue, 04. Mi (D  <br>$ \times $ $\in$ |                                                                                                  | $\bigcirc$ 00:37<br>EUR / USD // 1,000.00 EUR                                                                         |
| Your relationship with 'BankClientP' trading entity(s) of 'BankClientP' does<br>not have the status ACCEPTED yet. You are able to trade but please inform<br>your counterpart relationship administrator or call 360T CAS in order to<br>finalize the relationship |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     |                                                                                                  |                                                                                                                       |
|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     | Bid<br>$.20549$ $\bigcirc$<br>⊜<br>Margin (pips)<br>۸<br>$0.0\,$ $\circledcirc$                  | Offer<br>$\bullet$ 1.20555 $\bullet$ $\circ$<br>Margin (pips)<br>$\bullet$<br>$0.0$ $\circledcirc$                    |
|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     | <b>Bid Points</b><br>04. May 21<br>1.620<br>Margin (pips)<br>⊝<br>$0.000$ (D)<br><b>Bid Rate</b> | Offer Points<br>$\bullet$<br>$1.650$ $\bigoplus$ $\bigoplus$<br>Margin (pips)<br>$\circ$<br>$0.000$ $@$<br>Offer Rate |
|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     | 1.2056520                                                                                        | 1.20E<br>150                                                                                                          |

<span id="page-9-0"></span>**Figure 8 Trader Worksheet: Relationship not accepted warning**

A relationship is accepted by clicking on the button on the right side of the Taker Relationships table as shown in Figure 9 [Accepting a relationship.](#page-9-1)

| RFS<br>Orders SEP              |                                  |                       |                                              |                            |                            | $\mathcal{F}\ \mathcal{F}$ |
|--------------------------------|----------------------------------|-----------------------|----------------------------------------------|----------------------------|----------------------------|----------------------------|
| <b>Taker Relationships (1)</b> | <b>Auto Accept Configuration</b> |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       | $\begin{array}{c} \n\boxed{Q} \n\end{array}$ | $\rightarrow)$             |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
| Counterpart                    | <b>Legal Entity</b>              | Long Name             | My Status                                    | <b>Counterparty Status</b> |                            | $\nabla \times$            |
| BankClientP                    | BankClientP                      | $\mathsf{``Empty"''}$ | $\bullet$ Accepted                           | Accepted                   |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            |                            |                            |
|                                |                                  |                       |                                              |                            | <b>Discard all Changes</b> | Save*                      |
|                                |                                  |                       |                                              |                            |                            |                            |

<span id="page-9-1"></span>**Figure 9 Accepting a relationship**

![](_page_10_Picture_0.jpeg)

A relationship is rejected by clicking on the button on the right side of the Taker Relationships table as shown in Figure 10 [Rejecting a relationship.](#page-10-2)

| <b>RFS</b><br>Orders<br><b>SEP</b> |                                  |           |                    |                            |                            | 土土                                   |
|------------------------------------|----------------------------------|-----------|--------------------|----------------------------|----------------------------|--------------------------------------|
| <b>Taker Relationships</b>         | <b>Auto Accept Configuration</b> |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           | Q)                 | $\rightarrow$              |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
| Counterpart                        | <b>Legal Entity</b>              | Long Name | My Status          | <b>Counterparty Status</b> |                            |                                      |
| BankClientP                        | BankClientP                      | "Empty"   | $\bullet$ Rejected | • Accepted                 |                            | $\overline{\mathbb{R}^{\mathbb{N}}}$ |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            |                            |                                      |
|                                    |                                  |           |                    |                            | <b>Discard all Changes</b> | Save*                                |

### <span id="page-10-2"></span>**Figure 10 Rejecting a relationship**

The change must be saved, by clicking on "Save" button to become effective.

After saving the change, the updated status become available to the market taker who has requested the relationship.

It is possible to accept or reject multiple relationships at the same time by doing CTRL+click or Shift+click as shown in Figure 11 [Selecting Multiple Relationships](#page-10-3).

| Counterpart  | <b>Legal Entity</b> | <b>Long Name</b> | My Status          | <b>Counterparty Status</b> |                              |
|--------------|---------------------|------------------|--------------------|----------------------------|------------------------------|
| <b>BankB</b> | BankB               | "Empty"          | • Accepted         | • Accepted                 | / X                          |
| BankC        | BankC               | "Empty"          | Accepted           | <b>O</b> Undefined         | VX                           |
| <b>BankD</b> | BankD               | "Empty"          | <b>O</b> Undefined | Undefined                  | <b>TXL</b><br>V.             |
| BankE        | BankE               | "Empty"          | <b>Undefined</b>   | <b>Undefined</b>           | $\checkmark$                 |
| <b>BankG</b> | <b>BankG</b>        | "Empty"          | <b>Undefined</b>   | Undefined                  | $\mathbb{R}^{\times}$<br>l v |
| BankH        | BankH               | BankH Plc        | <b>Undefined</b>   | <b>Undefined</b>           | $\checkmark$                 |
| Bankl        | Bankl               | "Empty"          | • Accepted         | <b>O</b> Undefined         | VX                           |
| BankJ        | BankJ               | "Empty"          | Accepted           | <b>O</b> Undefined         | $\vee$ X                     |
|              |                     |                  |                    |                            |                              |

<span id="page-10-3"></span>**Figure 11 Selecting Multiple Relationships**

# <span id="page-10-0"></span>**4 Filter, search and sorting options**

## <span id="page-10-1"></span>**4.1 Filter & search**

It is possible to search and filter a specific Taker or reduced list of Takers by typing the Counterpart or letters contained in the Counterpart and clicking on the arrow on the right, as shown in Figure 12 [Searching relationships.](#page-11-1) The search parameter is applied to the column Counterpart which refers to the Market Taker.

![](_page_11_Picture_0.jpeg)

| <b>RFS</b><br><b>SEP</b><br>Orders |                                  |                  |                    |                            | 土土                    |
|------------------------------------|----------------------------------|------------------|--------------------|----------------------------|-----------------------|
| <b>Taker Relationships (6)</b>     | <b>Auto Accept Configuration</b> |                  |                    |                            |                       |
|                                    |                                  |                  | $Q$ Client         | $\rightarrow$              |                       |
| Counterpart                        | <b>Legal Entity</b>              | <b>Long Name</b> | My Status          | <b>Counterparty Status</b> |                       |
| BankClientA                        | <b>BankClientA</b>               | "Empty"          | <b>O</b> Undefined | Accepted                   | $\sqrt{\chi}$         |
| <b>BankClientB</b>                 | <b>BankClientB</b>               | "Empty"          | <b>Undefined</b>   | Accepted                   | $\checkmark$ $\times$ |
| <b>BankClientC</b>                 | BankClientC                      | "Empty"          | <b>Undefined</b>   | Accepted                   | $\sqrt{\chi}$         |
| BankClientD                        | BankClientD                      | "Empty"          | <b>Undefined</b>   | Accepted                   | $\checkmark$ $\times$ |
| BankClientE                        | BankClientE                      | "Empty"          | <b>Undefined</b>   | Accepted                   | V X                   |
| BankClientP                        | BankClientP                      | "Empty"          | <b>Undefined</b>   | Accepted                   | $\checkmark$ $\times$ |

## <span id="page-11-1"></span>**Figure 12 Searching relationships**

The list of relationships is then filtered based on what was typed as shown in [Figure](#page-11-2)  13 [Filtered relationships.](#page-11-2)

| RFS<br><b>SEP</b><br>Orders    |                                  |                  |                      |                            | 土土                    |
|--------------------------------|----------------------------------|------------------|----------------------|----------------------------|-----------------------|
| <b>Taker Relationships (1)</b> | <b>Auto Accept Configuration</b> |                  |                      |                            |                       |
|                                |                                  |                  | $\mathbb{Q}$ ClientP | $\rightarrow$              |                       |
|                                |                                  |                  |                      |                            |                       |
|                                |                                  |                  |                      |                            |                       |
| Counterpart                    | <b>Legal Entity</b>              | <b>Long Name</b> | My Status            | <b>Counterparty Status</b> |                       |
| BankClientP                    | BankClientP                      | "Empty"          | <b>O</b> Undefined   | Accepted                   | $\checkmark$ $\times$ |
|                                |                                  |                  |                      |                            |                       |
|                                |                                  |                  |                      |                            |                       |

## <span id="page-11-2"></span>**Figure 13 Filtered relationships**

It is possible to go back to the original list display by clearing the search field and clicking on the arrow on the right.

## <span id="page-11-0"></span>**4.2 Sort**

The Taker Relationships table still offers sorting capabilities (ascending or descending ) by clicking on the column headers as shown in [Figure 14](#page-11-3) Sorting [relationships.](#page-11-3)

| Counterpart        | $\vee$ Legal Entity | <b>Long Name</b> | <b>My Status</b>   | <b>Counterparty Status</b> |                       |
|--------------------|---------------------|------------------|--------------------|----------------------------|-----------------------|
| BankClientP        | BankClientP         | "Empty"          | <b>O</b> Undefined | Accepted                   | $\vee$ X              |
| <b>BankClientE</b> | <b>BankClientE</b>  | "Empty"          | <b>Undefined</b>   | Accepted                   | $\vee$ X              |
| BankClientD        | BankClientD         | "Empty"          | <b>O</b> Undefined | Accepted                   | $\vee$ X              |
| <b>BankClientC</b> | <b>BankClientC</b>  | "Empty"          | <b>Undefined</b>   | Accepted                   | $\checkmark$ $\times$ |
| BankClientB        | <b>BankClientB</b>  | "Empty"          | <b>Undefined</b>   | Accepted                   | $\vee$ X              |
| <b>BankClientA</b> | <b>BankClientA</b>  | "Empty"          | <b>Undefined</b>   | Accepted                   | $\checkmark$ X        |

## <span id="page-11-3"></span>**Figure 14 Sorting relationships**

![](_page_12_Picture_0.jpeg)

# <span id="page-12-0"></span>**5 Download and upload counterparts**

## <span id="page-12-1"></span>**5.1 Download counterparts**

It is possible to download a csv file containing information about all the relationships defined (or undefined) for the market maker for all configured products by clicking on the symbol indicated in Figure 15 [Download counterparts.](#page-12-3)

<span id="page-12-3"></span>**Figure 15 Download counterparts**

After saving the csv file, it can be opened, edited and used as desired. A sample file is shown in Figure 16 [Relationships csv file.](#page-12-4)

|                                                        | File<br>Home              | Page Layout<br>Insert                                                        | Formulas                                                                     | Data<br>Review                                                        | View                                                                  | Help               |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    | <b>B</b> Share              | <b>□</b> Comments       |
|--------------------------------------------------------|---------------------------|------------------------------------------------------------------------------|------------------------------------------------------------------------------|-----------------------------------------------------------------------|-----------------------------------------------------------------------|--------------------|------------------|--------------------|-----------------|--------------------------------|---------------------------------------------------|----------------------|-------------------------|---|----------------------------------------------------|-------------------------------------------------------|--------------------------------|------------------------------------|-----------------------------|-------------------------|
|                                                        | X Cut<br>Ĥ<br>$\Box$ Copy | Calibri<br>$B$ $I$<br>Format Painter                                         | $~\vee$ 11<br>$\underline{\mathsf{U}}$ $\vee$ $\overline{\mathsf{H}}$ $\vee$ | $\vee$ A <sup><math>\circ</math></sup><br>$A^{\prime}$<br>$A \cdot A$ | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\frac{1}{2}$<br>퇴목 공 표 표 | 광 Wrap Text        | Merge & Center v | General<br>图 > % 9 |                 | $\frac{10}{68}$ $\frac{0}{10}$ | ш<br>Conditional<br>Formatting v Table v Styles v | 34<br>Format as Cell | $\mathbb{Z}$            | 钾 | 囯<br>₩<br>Insert Delete Format<br>$\sim$<br>$\sim$ | $\sqrt{2}$ Fill $\sim$<br>$\diamondsuit$ Clear $\sim$ | $\sum$ AutoSum<br>žy           | Sort & Find &<br>Filter Y Select Y | <b>Q</b><br>Analyze<br>Data | S<br>Sensitivity        |
|                                                        | Clipboard                 | $\overline{N}$                                                               | Font                                                                         | $\overline{5}$                                                        |                                                                       | Alignment          |                  | $\overline{u}$     | Number          | $\overline{2}$                 |                                                   | Styles               |                         |   | Cells                                              |                                                       | Editing                        |                                    | Analysis                    | Sensitivity             |
| 119                                                    |                           | $\mathbf{v}$ i $\times$<br>$f_x$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        | $\mathsf{A}$              | B                                                                            |                                                                              | D                                                                     |                                                                       |                    |                  |                    | $\vert G \vert$ | H                              |                                                   |                      |                         |   | K                                                  |                                                       |                                | м                                  |                             | N                       |
|                                                        | <b>MY ENTIT</b>           | <b>MY ENTITY LONG NAME</b>                                                   | <b>REQUESTER</b>                                                             | <b>REQUESTER LONG NAM</b>                                             |                                                                       | <b>EGAL ENTI</b>   | I FGAI-FNT       | Y LONG NAME        | LEI             | <b>RFS MY STATUS</b>           |                                                   | <b>RFS CP STATUS</b> | <b>Orders MY STATUS</b> |   |                                                    |                                                       | Orders CP STATUS SEP MY STATUS | <b>SEP CP STATUS</b>               |                             | <b>MmFund MY STATUS</b> |
|                                                        | <b>BankP</b>              | *Empty*                                                                      | BankA                                                                        | *Empty*                                                               |                                                                       | <b>BankClientA</b> | *Empty*          |                    |                 | Accepted                       | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        | <b>BankP</b>              | *Empty*                                                                      | <b>BankB</b>                                                                 | *Empty*                                                               |                                                                       | <b>BankClientB</b> | *Empty*          |                    |                 | <b>Undefined</b>               | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        | <b>BankP</b>              | *Empty*                                                                      | BankClientC *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | <b>Undefined</b>               | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        | <b>BankP</b>              | *Empty*                                                                      | BankClientD *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | Undefined                      | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        | 6 BankP                   | *Empty*                                                                      | BankClientE *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | <b>Undefined</b>               | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
| $\overline{7}$                                         | <b>BankP</b>              | *Empty*                                                                      | BankClientP *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | <b>Undefined</b>               | Accepted                                          |                      | Accepted                |   | Accepted                                           |                                                       |                                |                                    |                             |                         |
|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
| $\begin{array}{c}\n\cdot \\ 8 \\ 9 \\ 10\n\end{array}$ |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |
|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |

<span id="page-12-4"></span>**Figure 16 Relationships csv file**

## <span id="page-12-2"></span>**5.2 Upload counterparts**

Taker relationships may also be updated (accepted or rejected) by uploading a csv file as shown in Figure 17 [Upload counterparts.](#page-12-5)

| RFS <sup>1</sup><br>Orders SEP |                                  |  |
|--------------------------------|----------------------------------|--|
| <b>Taker Relationships (6)</b> | <b>Auto Accept Configuration</b> |  |

## <span id="page-12-5"></span>**Figure 17 Upload counterparts**

The template for csv upload must be the file downloaded from the "Download counterparts" functionality described in [5.1.](#page-12-1) The structure of the template must not be changed, otherwise the file fails to upload. This means that column headers must not be included or excluded. The changes must be limited to the content of the cells in columns labelled as "% MY STATUS". Ex: RFS MY STATUS, Orders MY STATUS, etc. They refer to the market maker status. The "% CP STATUS" columns (RFS CP STATUS, Orders CP STATUS, etc. refer to the market taker status. Changes in the content of these columns are not applied.

A sample edited file is shown in Figure 16 [Relationships csv file.](#page-12-4) The cell indicated in blue font was changed from Undefined to Accepted.

Only "Accepted" and "Rejected" statuses edited in correspondent "% MY STATUS" columns in csv file are reflected in the platform. Statuses modified to "Undefined" are not applied.

![](_page_13_Picture_0.jpeg)

| <b>RFS</b><br><b>SEP</b><br>Orders |                                  |           |                    |                            | 土土                    |
|------------------------------------|----------------------------------|-----------|--------------------|----------------------------|-----------------------|
| <b>Taker Relationships (6)</b>     | <b>Auto Accept Configuration</b> |           |                    |                            |                       |
|                                    |                                  |           |                    |                            |                       |
|                                    |                                  | Q         |                    | $\rightarrow$              |                       |
|                                    |                                  |           |                    |                            |                       |
| Counterpart                        | $\wedge$ Legal Entity            | Long Name | My Status          | <b>Counterparty Status</b> |                       |
|                                    |                                  |           |                    |                            |                       |
| BankClientA                        | BankClientA                      | "Empty"   | Accepted           | Accepted                   | $\mathcal{N} \times$  |
| <b>BankClientB</b>                 | <b>BankClientB</b>               | "Empty"   | <b>Undefined</b>   | Accepted                   | $\checkmark$ $\times$ |
|                                    |                                  |           |                    |                            |                       |
| BankClientC                        | BankClientC                      | "Empty"   | <b>O</b> Undefined | Accepted                   | $\sqrt{\chi}$         |
| BankClientD                        | <b>BankClientD</b>               | "Empty"   | <b>O</b> Undefined | Accepted                   | $\checkmark$ $\times$ |
| BankClientE                        | BankClientE                      | "Empty"   | <b>O</b> Undefined | Accepted                   | $\vee$ X              |
| <b>BankClientP</b>                 | BankClientP                      | "Empty"   | <b>Undefined</b>   | Accepted                   | $\sqrt{\chi}$         |

<span id="page-13-1"></span>**Figure 18 Updated relationships**

# <span id="page-13-0"></span>**6 Audit log**

The Audit Log tool is available in the Bridge Administration homepage to the administrators who have the corresponding rights. <NAME_EMAIL> or your customer relationship manager for setting up the audit log rights if needed.

![](_page_13_Picture_6.jpeg)

**Figure 19 Audit Log**

<span id="page-13-2"></span>Through the audit log functionality, it is possible to track all changes related to the CRM tool described in this user guide.

Firstly, corresponding category and company must be selected as shown in following Figures.

![](_page_14_Picture_0.jpeg)

|                                        |                                       | <b>BRIDGE ADMINISTRATION</b><br><b>TRADER WORKSHEET</b>  | $+$          |                       |                      | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\begin{array}{c ccc} & \blacktriangleright^1 & \circledast & \mathsf{A}\mathsf{A} & \multimap & \varnothing & \times \end{array}$ |                                                           |
|----------------------------------------|---------------------------------------|----------------------------------------------------------|--------------|-----------------------|----------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|
|                                        | 合<br>$\mathcal{G}$                    |                                                          |              |                       | Audit Log            |                                                                                                                                                                                         |                                                           |
|                                        | $\ensuremath{\boxdot}$                | Select Category<br>$\checkmark$<br>Select Company        | $\checkmark$ |                       |                      |                                                                                                                                                                                         | $\mathord{\hookrightarrow}$                               |
|                                        | $\circledbullet$<br>$\eta_{\rm 1D}^2$ | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\checkmark$ |              | Type to Filter Events | ○ ○ Change Request と |                                                                                                                                                                                         |                                                           |
|                                        | $\frac{1}{600}$                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
|                                        |                                       |                                                          |              |                       | No Category Selected |                                                                                                                                                                                         |                                                           |
|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
| $\frac{1}{2\sqrt{3}}$                  |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
| $\mathbb C$<br>$\overline{\circ}$<br>Đ |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |
|                                        |                                       | 1 BankP.TraderA, BankP // DEMO                           |              |                       | $\blacksquare$       |                                                                                                                                                                                         | Fri, 23. Apr 2021, 16:23:05 GMT // Connected [FFM] · DEMO |

<span id="page-14-0"></span>**Figure 20 Audit Log criteria selection**

| Select Category    |              |
|--------------------|--------------|
|                    |              |
| Institutions       |              |
| RegulatoryData     |              |
| <b>BankBaskets</b> |              |
| Counterparts       |              |
|                    |              |
|                    |              |
|                    |              |
|                    |              |
|                    |              |
|                    |              |
| <b>Select All</b>  | <b>Apply</b> |

<span id="page-14-1"></span>**Figure 21 Audit Log Category selection**

![](_page_15_Picture_0.jpeg)

| Select Company |
|----------------|
|                |
| <b>BankP</b>   |
|                |
|                |
|                |
|                |
|                |
|                |
| Apply          |

## <span id="page-15-0"></span>**Figure 22 Audit Log Company selection**

All related changes are then displayed as shown in Figure 23 [Audit Log CRM results.](#page-15-1)

|                          |                                                                      | <b>TRADER WORKSHEET</b>                                   | $\left  + \right $<br><b>BRIDGE ADMINISTRATION</b>      |                     |                       |                                                   |               | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\sum_{i=1}^{n}$ (i) AA - $\oplus \times$ |                      |  |
|--------------------------|----------------------------------------------------------------------|-----------------------------------------------------------|---------------------------------------------------------|---------------------|-----------------------|---------------------------------------------------|---------------|------------------------------------------------------------------------------------------------|----------------------|--|
|                          | 合                                                                    |                                                           | $\boxed{\smile}$ $\boxed{\smile}$ Counterparts $\times$ |                     |                       | <b>Audit Log</b>                                  |               |                                                                                                |                      |  |
|                          | $\mathcal{L}_{\mathcal{T}}$<br>$\qquad \qquad \boxdot \qquad \qquad$ | Select Category<br>O 0 Show all companies (Select Company |                                                         |                     | $\ominus$             |                                                   |               |                                                                                                |                      |  |
|                          | Œ                                                                    | Event Date (All                                           | $\frac{1}{2}$<br>$\checkmark$                           | Search <sup>3</sup> | Type to Filter Events | ○ O Change Request と                              |               |                                                                                                |                      |  |
|                          | $\frac{d}{d\theta}$                                                  | <b>DB Revision</b>                                        | Timestamp (GMT)                                         | Target              | Company               | <b>Event Name</b>                                 | Changed By    | Field                                                                                          | Value                |  |
|                          |                                                                      | 168348                                                    | 23.04.2021 / 16:18:08                                   | BankP               | BankP                 | Counterpart Relation Maker Status _ BankP.TraderA |               | <b>Maker Status</b>                                                                            | <b>ACCEPTED</b>      |  |
|                          | $\frac{1}{600}$                                                      | 168328                                                    | 23.04.2021 / 15:26:43                                   | BankClientP         | BankP                 | Auto accept enabled                               | BankP.TraderA | Old Maker Status                                                                               | <b>UNDEFINED</b>     |  |
|                          |                                                                      | 168310                                                    | 23.04.2021 / 13:44:48                                   | BankP               | BankP                 | Counterpart Relation Maker Status __   ********   |               | Taker<br>Maker                                                                                 | BankClientA<br>BankP |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               | <b>Negotiation Type</b>                                                                        | <b>RFS</b>           |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
| $\zeta_{\rm c}^{\rm sc}$ |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
| O                        |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
| $\bigcirc$               |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
| Θ                        |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |
|                          |                                                                      | <sup>1</sup> BankP.TraderA, BankP // DEMO                 |                                                         |                     |                       | $\blacksquare$                                    |               | Fri, 23. Apr 2021, 16:27:59 GMT // Connected [FFM] · DEMO                                      |                      |  |

## <span id="page-15-1"></span>**Figure 23 Audit Log CRM results**

The table of results contain the following data:

- DB Revision: internal ID which identifies each entry in the database
- Timestamp GMT: date and time in which the change was made
- Target/Name: impacted entity

![](_page_16_Picture_0.jpeg)

- Event Name: description of the change
- Changed by: name of the individual administrator who made de change. Please note that the name is not available for the changes made by a 360T administrator, but only those made by the market maker administrators.

It is also possibe to restrict the audit logs search to a specific period of time, using either the pre-defined periods or calendars available in the Event Date field as shown in Figure 24 - [Audit Log Event Date -](#page-16-0) pre defined periods and Figure 25 - [Audit Log](#page-16-1)  [Event Date -](#page-16-1) calendars.

| <b>Event Date</b>  | All             | ᄉ | $-111$<br>Ш     |
|--------------------|-----------------|---|-----------------|
| <b>DB Revision</b> | Today<br>7 days |   | Timestamp (GMT) |
|                    | 30 days         |   |                 |
|                    | 90 days         |   |                 |
|                    | 180 days        |   |                 |
|                    | 1 year          |   |                 |

<span id="page-16-0"></span>**Figure 24 - Audit Log Event Date - pre defined periods**

| G          | <b>Event Date</b><br>Custom |     |             |                      |                |             |          | ▽ 21.03.2021 - 28.03.2021 |                                          |                                          |                   |                   |             |       |            |
|------------|-----------------------------|-----|-------------|----------------------|----------------|-------------|----------|---------------------------|------------------------------------------|------------------------------------------|-------------------|-------------------|-------------|-------|------------|
| Sunday     |                             |     |             | <b>March 2021</b>    |                |             |          |                           |                                          |                                          | <b>April 2021</b> |                   |             |       | Sunday     |
| <b>MAR</b> | s                           | M   | $\top$      | w                    | $T$ F          |             | $\sim$ S | S                         | M                                        | $\top$                                   | w                 | - 1 -             | $-F$        | - S   | <b>MAR</b> |
|            |                             |     | $1 \t2 \t3$ |                      | $\overline{4}$ | 5 6         |          |                           |                                          |                                          |                   |                   | $1 \t2 \t3$ |       |            |
|            |                             | - 8 | 9           |                      |                | 10 11 12 13 |          | 4                         | 5 6                                      |                                          | $7^{\circ}$       | - 8               | 9           | 10    |            |
|            | 14                          |     |             | 15 16 17 18          |                | 19 20       |          |                           |                                          | 11 12 13 14                              |                   | $-15$             | - 16        | $-17$ |            |
|            |                             |     |             | 21 22 23 24 25 26 27 |                |             |          | 18                        | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                   | 21 22 23 24       |             |       |            |
|            | 28                          |     | 29 30 31    |                      |                |             |          |                           |                                          |                                          |                   | 25 26 27 28 29 30 |             |       |            |
| 2021       |                             |     |             |                      |                |             |          |                           |                                          |                                          |                   |                   |             |       | 2021       |

<span id="page-16-1"></span>**Figure 25 - Audit Log Event Date - calendars**

When the administrator clicks on any of the listed entries, the details of the change are shown in another table located at the right side of the screen as shown in [Figure](#page-17-1)  27 [Audit Log change details.](#page-17-1)

In the following example, the Maker Status was changed from UNDEFINED to ACCEPTED in the relationship with Taker BankClientA for Negotiation Type RFS.

Please note that the changes made via Upload counterparts functionality are displayed in the same way as the changes made directly in the Maker Relationships screen. The Audit Log does not differentiate them as the output is the same.

![](_page_17_Picture_0.jpeg)

|                                                  |                                                  | <b>TRADER WORKSHEET</b>                                                   | $\Box$<br><b>BRIDGE ADMINISTRATION</b>     |               |                       |                                               |               | $\vee$ Preferences $\vee$ Administration $\vee$ Help $\Box$ $\mathbb{D}^1$ (0 AA - $\varpi \times$ |                                                                     |
|--------------------------------------------------|--------------------------------------------------|---------------------------------------------------------------------------|--------------------------------------------|---------------|-----------------------|-----------------------------------------------|---------------|----------------------------------------------------------------------------------------------------|---------------------------------------------------------------------|
|                                                  | 合<br>$\mathcal{L}_{\mathcal{T}}$                 | Select Category                                                           | $\vee$   Counterparts $\times$             |               |                       | <b>Audit Log</b>                              |               |                                                                                                    | $\ominus$                                                           |
|                                                  | $\qquad \qquad \boxdot \qquad$<br>$\circledcirc$ | O 0 Show all companies (Select Company<br>Event Date (All<br>$\checkmark$ | $\vee$   (BankP $\times$ )<br>$\Box$ (iii) | Search        | Type to Filter Events | ○ 0 Change Request と                          |               |                                                                                                    |                                                                     |
|                                                  | $\overline{\text{tr}}$                           | <b>DB Revision</b>                                                        | Timestamp (GMT)                            | <b>Target</b> | Company               | <b>Event Name</b>                             | Changed By    | Field                                                                                              | Value                                                               |
|                                                  |                                                  | 168348                                                                    | 23.04.2021 / 16:18:08                      | BankP         | BankP                 | Counterpart Relation Maker Status             | BankP.TraderA | <b>Maker Status</b>                                                                                | <b>ACCEPTED</b>                                                     |
|                                                  | $\frac{1}{600}$                                  | 168328                                                                    | 23.04.2021 / 15:26:43                      | BankClientP   | BankP                 | Auto accept enabled                           | BankP.TraderA | <b>Old Maker Status</b>                                                                            | <b>UNDEFINED</b>                                                    |
|                                                  |                                                  | 168310                                                                    | 23.04.2021 / 13:44:48                      | BankP         | BankP                 | Counterpart Relation Maker Status    ******** |               | Taker                                                                                              | BankClientA                                                         |
|                                                  |                                                  |                                                                           |                                            |               |                       |                                               |               | Maker<br><b>Negotiation Type</b>                                                                   | BankP<br><b>RFS</b>                                                 |
| $\zeta_{\rm c}^{\rm sc}$<br>O<br>$\bigcirc$<br>œ |                                                  |                                                                           |                                            |               |                       |                                               |               |                                                                                                    |                                                                     |
|                                                  |                                                  | <b>1</b> BankP.TraderA, BankP // DEMO                                     |                                            |               |                       | $\blacksquare$ $\blacksquare$ $\blacksquare$  |               |                                                                                                    | Fri, 23. Apr 2021, 16:27:59 GMT // Connected [FFM] ·<br><b>DEMO</b> |

#### <span id="page-17-0"></span>**Figure 26 Audit Log CRM results and details**

| Field                   | Value              |  |  |  |  |
|-------------------------|--------------------|--|--|--|--|
| <b>Maker Status</b>     | <b>ACCEPTED</b>    |  |  |  |  |
| <b>Old Maker Status</b> | <b>UNDEFINED</b>   |  |  |  |  |
| <b>Taker</b>            | <b>BankClientA</b> |  |  |  |  |
| Maker                   | <b>BankP</b>       |  |  |  |  |
| <b>Negotiation Type</b> | <b>RFS</b>         |  |  |  |  |
|                         |                    |  |  |  |  |
|                         |                    |  |  |  |  |

#### <span id="page-17-1"></span>**Figure 27 Audit Log change details**

![](_page_18_Picture_0.jpeg)

# <span id="page-18-0"></span>**7 Contacting 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

Germany 360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29

### **Asia Pacific South Asia**

Singapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756

### **Middle East**

United Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134

### **EMEA Americas**

## USA

360 Trading Networks, Inc 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902

India ThreeSixty Trading Networks (India) Pvt Ltd Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437