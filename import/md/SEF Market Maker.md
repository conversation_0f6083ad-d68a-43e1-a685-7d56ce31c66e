# **USER GUIDE 360T SEF**

![](_page_0_Picture_1.jpeg)

# **TEX MULTIDEALER TRADING SYSTEM**

USER GUIDE 360T SWAP EXECUTION FACILITY FOR THE MARKET MAKER

© 360 TREASURY SYSTEMS AG, 2013 THIS FILE CONTAINS PROPRIETARY AND CONFID<PERSON><PERSON>AL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG

| 1 |              | INTRODUCTION                                                                                    | 4        |
|---|--------------|-------------------------------------------------------------------------------------------------|----------|
| 2 |              | DEFINITION OF SEF REPORTING PARAMETERS                                                          | 6        |
|   | 2.1<br>2.2   | SEF<br>REPOR<PERSON>D DATA<br><br>INDICATION OF COLLATERALIZATION                                     | 6<br>8   |
| 3 |              | TRADING NDF/NDS AND FX OPTIONS AS REQUEST FOR STREAMED QUOTES ON<br>THE SEF                     | 9        |
|   | 3.1          | NDF                                                                                             | 9        |
|   | 3.2          | NDS                                                                                             | 12       |
|   | 3.3<br>3.3.1 | FX<br>OPTIONS<br>Plain vanilla FX Option<br>                                                    | 13<br>14 |
|   | 3.3.2        | FX Risk Reversal / Zero Cost / Strategy option                                                  | 15       |
| 4 |              | PRICING IN THE TRADER WORKSHEET                                                                 | 16       |
|   | 4.1          | PRICING NDF                                                                                     | 16       |
|   | 4.2          | PRICING NDS                                                                                     | 19       |
|   | 4.3          | PRICING FX<br>OPTIONS<br>                                                                       | 23       |
|   | 4.3.1        | Plain vanilla FX option with Notional in quote currency and Premium in base currency<br>24      |          |
|   | 4.3.2        | Example 2: EUR/USD -<br>notional and premium in EUR<br>                                         | 26       |
|   | 4.3.3        | Example 3: Zero-Cost Strategy –<br>notional in USD –<br>Premium in EUR                          | 27       |
|   | 4.3.4        | Example 4: Risk Reversal Strategy -<br>Notional in USD -premium in EUR                          | 29       |
|   | 4.3.5        | Example 5: Bear Spread (Buy Put and Sell Put)                                                   | 30       |
|   | 4.3.6        | Example 6: EUR/USD Option –<br>Notional in USD –<br>premium in EUR with Separate<br>Delta Hedge | 31       |
|   |              |                                                                                                 |          |
| 5 |              | SEF ORDER BOOK<br>                                                                              | 35       |
| 6 |              | DEAL BLOTTER                                                                                    | 37       |
|   | 6.1          | REQUESTS<br>TODAY (REALTIME)                                                                    | 38       |
|   | 6.2          | ALL REQUESTS<br>                                                                                | 39       |
|   | 6.3          | EXECUTED REQUESTS<br>                                                                           | 39       |
|   | 6.4          | MATURITY SCHEDULE                                                                               | 40       |
| 7 |              | CONTACTING 360T                                                                                 | 41       |

## **TABLE OF FIGURES**

| Figure 1 NDF request from a SEF participant -<br>Product is displayed in green with SEF  |  |
|------------------------------------------------------------------------------------------|--|
| superscript<br>4                                                                         |  |
| Figure 2 Request details with (SEF) information5                                         |  |
| Figure 3 Administration of SEF Data<br>6                                                 |  |
| Figure 4 SEF Company data<br>7                                                           |  |
| Figure 5 SEF User data7                                                                  |  |
| Figure 6 NDF Product definition<br>10                                                    |  |
| Figure 7 NDF Transaction details<br>11                                                   |  |
| Figure 8 NDF Comments11                                                                  |  |
| Figure 9 NDS Product definition<br>12                                                    |  |
| Figure 10 Open FX Option product definition13                                            |  |
| Figure 11 Plain Vanilla Option product definition<br>14                                  |  |
| Figure 12 FX Zero Premium Risk Reversal Option Product Definition<br>15                  |  |
| Figure 13 NDF Incoming Request for<br>Quote16                                            |  |
| Figure 14 Offer screen for a NDF request for quote17                                     |  |
| Figure 15 NDF Deal confirmation ticket<br>18                                             |  |
| Figure 16 Incoming NDS request for quote19                                               |  |
| Figure 17 Offer screen for a NDS20                                                       |  |
| Figure 18 Offer screen for a forward starting NDS21                                      |  |
| Figure 19 Offer screen for a forward starting NDS from a two-way price request22         |  |
| Figure 20 NDS Deal confirmation ticket23                                                 |  |
| Figure 21 Pricing of an EUR/USD FX Option, USD notional, premium in EUR<br>25            |  |
| Figure 22 Pricing of a EUR/USD FX Option with premium in EUR<br>26                       |  |
| Figure 23 Pricing a zero-cost FX option28                                                |  |
| Figure 24 Pricing of a risk reversal FX option<br>29                                     |  |
| Figure 25 Pricing of an FX option strategy30                                             |  |
| Figure 26 Pricing of an FX Option with separate delta hedge<br>32                        |  |
| Figure 27 Deal confirmation for an FX Option with a separate Spot Delta hedge<br>33      |  |
| Figure 28 Deal confirmation for a separate delta hedge spot trade34                      |  |
| Figure 29 Access the SEF Order Book35                                                    |  |
| Figure 30 SEF Order Book<br>35                                                           |  |
| Figure 31 Create NDF Order36                                                             |  |
| Figure 32 Execute Order from the SEF order book36                                        |  |
| Figure 33 Competitive bidding including SEF Order Book quote (tradable and indicative)37 |  |
| Figure 34 Deal Blotter functions<br>38                                                   |  |
| Figure 35 Realtime Deal Blotter with pending request and executed trade38                |  |
| Figure 36 Realtime Deal Blotter with executed and not execute trades<br>38               |  |
| Figure 37 All Requests Deal Blotter39                                                    |  |
| Figure 38 Executed requests Deal Blotter<br>40                                           |  |
|                                                                                          |  |

## <span id="page-3-0"></span>**1 INTRODUCTION**

In July 2010, the Dodd-Frank Wall Street Reform and Consumer Protection Act ("Dodd-Frank" or the "Act") was signed into U.S. federal law. The Act made changes to the financial regulatory environment in America as a way of promoting financial stability by, among other things, improving accountability and transparency in the financial system. Within the Act, Title VII concerns regulation of the over-the-counter swaps markets, which requires certain OTC derivative and FX instruments to be traded on a regulated SEF.

**360 Trading Networks Inc. (360T Inc.) offers a SEF venue for such transactions captured under this definition.** At present, this applies to FX options and FX nondeliverable forward and Swap (NDF and NDS) instruments traded by persons identified as U.S. entities or which fall under the definitions within the Act. 360T Inc.'s SEF services will enable clients to comply with their own Dodd Frank regulatory obligations and will retain the ability for clients to trade with greater transparency and enhanced control at every stage of the trading lifecycle.

Once a client is considered by 360T to be SEF relevant, users can only trade NDF, NDS and FX Options on the SEF. U.S. clients and entities trading with U.S. clients are blocked from trading these instruments if not enabled on the SEF. The required forms have therefore to be complete as well as the administrative information within the 360T administration tool.

This user manual describes the functionality available for the Market Participant trading as Market Maker under the 360T Swap Execution Facility ("360T SEF"). This means on one side the administrative functions to complete in the system prior to being able to trade under the 360T SEF, as well as the actual trading of the transactions falling under the regulation.

The 360T SEF platform is embedded in the 360T trading platform, so that an entity defined as trading NDF/NDS and FX Options under the SEF can still trade all other products under the non SEF platform. This user manual only describes the SEF relevant part. Please refer to the general 360T user manuals for all non SEF business.

In the 360T trader user interface, SEF related items are either displayed in green color with "SEF" in superscript, or with the mention of "SEF" in brackets, as shown in the following screen captures

| <b>EX BETA Platform - Trader Worksheet</b> |                       |                                        |               |                           |                                               |     |          |                                                    |   |               |
|--------------------------------------------|-----------------------|----------------------------------------|---------------|---------------------------|-----------------------------------------------|-----|----------|----------------------------------------------------|---|---------------|
| System                                     | Deal Blotter<br>Tools | Administration                         | Help          |                           |                                               |     |          |                                                    |   |               |
| Timeout                                    | Counterpart           | Legal Entity                           | <b>Action</b> | Product                   | Currency1 Currency2                           |     | Notional | Maturity Date                                      |   | EffectiveDate |
|                                            | 360T.AMERICAS         |                                        | <b>Buys</b>   | <b>NDF</b> <sup>SEF</sup> | <b>USD</b>                                    | BRL |          | ,5,000,000 3 MONTHS Tue, 26. N 3 MONTHS Tue, 26. N |   |               |
|                                            |                       |                                        |               |                           |                                               |     |          |                                                    |   |               |
|                                            |                       |                                        |               |                           |                                               |     |          |                                                    |   |               |
|                                            |                       | Local Date: <b>22/08/2013 18:20:31</b> |               |                           |                                               |     |          |                                                    |   |               |
|                                            |                       | Trade Date: 22/08/2013 16:20:31        |               |                           |                                               |     |          |                                                    | E |               |
|                                            |                       |                                        |               |                           | User: CITIBANK.Trader1 Company: CITIBANK.TEST |     |          | Status: Connected                                  |   |               |

<span id="page-3-1"></span>Figure 1 NDF request from a SEF participant - Product is displayed in green with SEF superscript

| <b>IES TEX BETA Platform - Trader Worksheet</b>                                                             |                                                                                                                                                        |         |                                 |          |               |               |  |  |  |
|-------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------|---------|---------------------------------|----------|---------------|---------------|--|--|--|
| System Deal Blotter Tools<br>Administration Help                                                            |                                                                                                                                                        |         |                                 |          |               |               |  |  |  |
| Counterpart<br>Timeout                                                                                      | Legal Entity<br><b>Action</b>                                                                                                                          | Product | Currency1 Currency2             | Notional | Maturity Date | EffectiveDate |  |  |  |
|                                                                                                             |                                                                                                                                                        |         | Local Date: 22/08/2013 18:25:32 |          |               |               |  |  |  |
| <b>Requests</b><br>Offers                                                                                   |                                                                                                                                                        |         | Trade Date: 22/08/2013 16:25:32 |          | Minimize      | <b>364</b>    |  |  |  |
| <b>Product Definition</b><br><b>Transaction Details</b><br>Reference #<br>Counterparty Details<br>Requester | <b>Requests</b><br>10:55<br>3M<br>USD/BRL.<br><b>NDF</b>                                                                                               |         |                                 |          |               |               |  |  |  |
| Individual                                                                                                  | 360TUSA.Treasurer1                                                                                                                                     |         |                                 |          |               |               |  |  |  |
| <b>Product Details</b><br>Action<br>Notional amount<br>Settlement date<br>Fixing date<br>Fixing reference   | FX Non-deliverable Forward (SEF)<br><b>Client BUYS USD / SELLS BRL</b><br>5,000,000.00 USD<br>3 MONTHS Tue, 26, Nov 2013<br>Fri, 22. Nov 2013<br>BRL01 |         |                                 |          |               |               |  |  |  |

<span id="page-4-0"></span>Figure 2 Request details with (SEF) information

A Swap Dealer who has signed the 360T SEF participation agreement can trade NDF, NDS and FX Options both under the SEF and under the non SEF platform. This happens implicitly over configuration settings on the platform, so that you as user don't have to switch from SEF to non SEF.

Whether a trade is done under the SEF or not basically depends on the country of origin of the requesting entity and user and on the country of origin of the providing entity and user.

US requesters can trade only with counterparties which have also signed the SEF participation agreement and set up the SEF reporting parameters. Non US requesters can trade with US and non US counterparties.

For the Market Maker this means the following:

If you are a Swap Dealer defined with 360T as a US participant, all NDF, NDS and FX Options will be traded on the SEF. You will be able to execute trades with US and non US requesters under the condition that yourself and the counterparties have recorded their reporting parameters.

If you are a Swap Dealer defined with 360T as a non US participant, but you wish to execute trades with US counterparties on the SEF, you will have to record your SEF reporting parameters to be included in the bank basket of your clients. Trading with non US clients is off-SEF.

| Requester        | Provider         | SEF / OFF SEF |  |  |
|------------------|------------------|---------------|--|--|
| Is US Person     | Is US Person     | SEF           |  |  |
|                  | Is not US Person | SEF           |  |  |
| Is not US Person | Is US Person     | SEF           |  |  |
|                  | Is not US Person | OFF SEF       |  |  |

In summary, for all cases marked as SEF above, the following prerequisites must be fulfilled:

- Company has provided 360T with its LEI
- Company is SEF enabled by 360T
- SEF Reporting parameters have been keyed in both for Company and Traders.

## <span id="page-5-0"></span>**2 DEFINITION OF SEF REPORTING PARAMETERS**

Under the Dodd Frank Act regulation, required transaction creation data must be reported to a Swap Data Repository (SDR). In addition, data is reported to the National Futures Association (NFA) in order to conduct trade practice and market surveillance on behalf of the 360T SEF. The data transmitted to the SDR and the NFA are trade execution parameters as well as activity data in general about who is trading and what is trading. For this, some participant specific data needs to be completed by the company and users.

Once the SEF participation agreement has been signed with 360T Inc., users will have been named to 360T's support services and given the according Administration rights to complete required parameters.

The functionality "SEF Data" to enter the required data can then be found under the Administration menu.

| <b>EX TEX BETA Platform - Trader Worksheet</b> |                           |                                               |          |                   |               |  |  |  |  |
|------------------------------------------------|---------------------------|-----------------------------------------------|----------|-------------------|---------------|--|--|--|--|
| Deal Blotter Tools<br>System                   | Administration Help       |                                               |          |                   |               |  |  |  |  |
| Counterpart<br>Timeout                         | Change password           | Currency1 Currency2                           | Notional | Maturity Date     | EffectiveDate |  |  |  |  |
|                                                | Counterpart Relationships |                                               |          |                   |               |  |  |  |  |
|                                                | SEF Data                  |                                               |          |                   |               |  |  |  |  |
|                                                |                           |                                               |          |                   |               |  |  |  |  |
| Local Date: 22/08/2013 19:03:57                |                           |                                               |          |                   | 36 <b>m</b>   |  |  |  |  |
| Trade Date: 22/08/2013 17:03:57                |                           |                                               |          |                   |               |  |  |  |  |
|                                                |                           | User: CITIBANK.Trader1 Company: CITIBANK.TEST |          | Status: Connected |               |  |  |  |  |

<span id="page-5-2"></span>Figure 3 Administration of SEF Data

## <span id="page-5-1"></span>**2.1 SEF Reported Data**

Required fields are displayed in bold letters. As long as a field is shown in red color the data is incomplete and can't be saved.

Field definitions are shown at the bottom of the table.

Each company and user set up by 360T's services on the platform is already associated to a country. Depending on that information, some fields might not be required. For example, if you are a UK entity but have signed the SEF participation agreement in order to be able to trade with US participants, then you will not be required to enter a SSN.

| <b>UE SEF Reported Data</b>                                                                                                                                                                  |                                                                                                                                                                                                             | $\overline{\mathbf{x}}$                                                                                                                         |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>SEF Reported Data</b> Collateralization                                                                                                                                                   |                                                                                                                                                                                                             |                                                                                                                                                 |
| Reload Discard Changes Export As Excel<br>Save<br><b>⊟</b> -Details<br>E J SEB.DEMO<br>F == 9 SEBDEMO.AutoDealer<br>Fi- 9 SEBDEMO.Trader1<br>El- 9 SEBDEMO. Trader2<br>SEBDEMO.Trader3<br>田里 | 图화 ■ 학업<br>E<br>TaxID<br>Name<br><b>Address 1</b><br>Address 2<br>Address 3<br>City<br>State<br><b>Postal Code</b><br>Country<br>Phone<br>Fax<br>Email<br>Start Date<br>End Date<br>(Name)<br>(Description) | 4567<br>SEB.DEMO<br>Address 1<br><b>Los Angeles</b><br>CA<br>12345<br><b>United States of America</b><br>00164254556<br>info@seb<br>Nov 3, 2015 |
|                                                                                                                                                                                              |                                                                                                                                                                                                             |                                                                                                                                                 |
| 03/11/15 14:06:08 GMT                                                                                                                                                                        |                                                                                                                                                                                                             | User: SEBDEMO.Trader1, Company: SEB.DEMO COMO                                                                                                   |

<span id="page-6-0"></span>Figure 4 SEF Company data

| <b>SEF Reported Data</b> Collateralization                                                                                                                                     |                                                                                                                                                                                                                                        |                                                                                                                                                                                                       |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Save Reload Discard Changes Export As Excel                                                                                                                                    |                                                                                                                                                                                                                                        |                                                                                                                                                                                                       |
| <b>E</b> Details<br>E- I SEB.DEMO<br><b>E-</b> <i>I</i> SEBDEMO.AutoDealer<br>E - F SEBDEMO. Trader1<br>E- # SEBDEMO.Trader2<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>田- | 图斜 四 性<br>E<br>SSN<br>Date of birth<br><b>First Name</b><br><b>Last Name</b><br><b>Address 1</b><br>Address 2<br>Address 3<br>City<br>State<br><b>Postal Code</b><br>Country<br>Phone<br>Fax<br>Email<br><b>Start Date</b><br>End Date | 8484<br>Nov 16, 1976<br><b>Jim</b><br><b>SEBBANKER</b><br>Street 1<br><b>Los Angeles</b><br>CA<br>123456<br><b>United States of America</b><br>************<br><EMAIL><br>Oct 22, 2015 |
|                                                                                                                                                                                | <b>Trader Type</b><br><b>SEF Enabled</b>                                                                                                                                                                                               | <b>NATS</b><br><b>Company default</b>                                                                                                                                                                 |
|                                                                                                                                                                                | (Name)                                                                                                                                                                                                                                 |                                                                                                                                                                                                       |
|                                                                                                                                                                                | (Description)                                                                                                                                                                                                                          |                                                                                                                                                                                                       |

<span id="page-6-1"></span>Figure 5 SEF User data

Please note that if you are using an integrated price engine, the AutoDealer also needs SEF reporting data. It is assumed that it will be provided by the person responsible for the pricing system in the bank.

Once all data has been provided, your provider entity will automatically be included in the requester bank basket for an NDF, NDS or FX Option trade request of market takers having a counterparty relationship with you.

If the data is incomplete, the user will not be able to trade on the SEF.

After completion of the SEF User data form, a trader can be disabled from SEF trading by setting the value of the "SEF enabled" field to "false".

**NB:** Note that disablement of SEF trading only takes effect *after* the trader logs out and back into the application.

## <span id="page-7-0"></span>**2.2 Indication of Collateralization**

Pursuant to CFTC regulation part 45, an indication of collateralization has to be reported to the SDR for all bilaterally executed swaps on the SEF and *must* include the following values:

- "uncollateralized"
- "partially collateralized"
- "one-way collateralized"
- "fully collateralized"

360T requires Market Makers to designate collateralization indication on a client by client basis. As shown in the screenshot below, the corresponding configuration form is accessible via the "Collateralization" tab.

| <b>ILES SEF Reported Data</b>                                       |                                          |                  |                 |                   |
|---------------------------------------------------------------------|------------------------------------------|------------------|-----------------|-------------------|
| Collateralization<br>SEF Reported Data                              |                                          |                  |                 |                   |
| Reload<br>Discard Changes<br>Save                                   |                                          |                  |                 |                   |
| SEB.DEMO<br>F-<br>$\circ$                                           | Requester                                | Legal entity     | Provider        | Collateralization |
| O Defined<br>田<br>O Undefined<br><b>E</b><br>ஈ<br>⊙ All             | 360T.AMERICAS                            | 360T.AMERICAS    | SEB.DEMO        | Undefined         |
|                                                                     | 360T.AMERICAS                            | 360TUS.TOB.CTPY6 | <b>SEB.DEMO</b> | <b>Undefined</b>  |
|                                                                     | GroupA                                   | <b>GroupA</b>    | <b>SEB.DEMO</b> | <b>Undefined</b>  |
|                                                                     | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | <b>GroupB</b>    | <b>SEB.DEMO</b> | <b>Undefined</b>  |
|                                                                     | <b>GroupC</b>                            | <b>GroupC</b>    | <b>SEB.DEMO</b> | <b>Undefined</b>  |
|                                                                     | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | <b>GroupD</b>    | <b>SEB.DEMO</b> | <b>Undefined</b>  |
|                                                                     | <b>GroupF</b>                            | <b>GroupF</b>    | <b>SEB.DEMO</b> | <b>Undefined</b>  |
|                                                                     |                                          |                  |                 |                   |
| User: SEBDEMO.Trader1, Company: SEB.DEMO<br>$03/11/15$ 14:06:08 GMT |                                          |                  |                 |                   |

For clarification purposes, the configuration form is subdivided into three separate menus. Under the first menu point entitled "Defined", the administrator has access to the list of clients for which the collateralization designation has been already configured. The "Undefined" menu provides an overview of missing values and, finally, "All" displays a consolidated list of both "Defined" and "Undefined" data. The collateralized designation can either be set per client on an individual basis or by use of the bulk functionality via the right-hand side mouse button. Please refer to the below screenshot.

| <b>USE SEF Reported Data</b>             |                                                                                        |                                                                                        |                                           |  |                  |                                          |
|------------------------------------------|----------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------|-------------------------------------------|--|------------------|------------------------------------------|
| SEF Reported Data Collateralization      |                                                                                        |                                                                                        |                                           |  |                  |                                          |
| <b>Discard Changes</b><br>Reload<br>Save |                                                                                        |                                                                                        |                                           |  |                  |                                          |
| O SEB.DEMO<br> ⊣…                        | Requester                                                                              |                                                                                        | Legal entity                              |  | Provider         | Collateralization                        |
| O Defined<br>। मि                        | 360T.AMER                                                                              |                                                                                        | Reset all visible to Undefined            |  |                  | <b>Undefined</b>                         |
| O Undefined<br>F-                        | 360T.AME<br><b>GroupA</b><br><b>GroupB</b><br><b>GroupC</b><br><b>GroupD</b><br>GroupF |                                                                                        |                                           |  |                  | <b>Undefined</b>                         |
| <b>E</b> ⊢ O All                         |                                                                                        | Reset all visible to Uncollateralized<br>Reset all visible to Partially collateralized |                                           |  | <b>Undefined</b> |                                          |
|                                          |                                                                                        |                                                                                        |                                           |  | <b>Undefined</b> |                                          |
|                                          |                                                                                        |                                                                                        |                                           |  | <b>Undefined</b> |                                          |
|                                          |                                                                                        | Reset all visible to One-way collateralized                                            |                                           |  | <b>Undefined</b> |                                          |
|                                          |                                                                                        |                                                                                        | Reset all visible to Fully collateralized |  |                  | <b>Undefined</b>                         |
|                                          |                                                                                        |                                                                                        |                                           |  |                  |                                          |
| $03/11/1514:06:08$ GMT                   |                                                                                        |                                                                                        |                                           |  |                  | User: SEBDEMO.Trader1, Company: SEB.DEMO |

After selecting the "Save" button, the amended entries are automatically re-located into the different sub-menus.

## <span id="page-8-0"></span>**3 TRADING NDF/NDS AND FX OPTIONS AS REQUEST FOR STREAMED QUOTES ON THE SEF**

Once SEF specific reporting data is defined, NDF, NDS and FX Options requests from Requesters that have also completed the SEF on-boarding can be received and priced by you.

### <span id="page-8-1"></span>**3.1 NDF**

Non deliverable forwards are outright contracts for non-convertible foreign currencies, which are cash-settled by using the difference between the agreed forward rate and the spot rate on the fixing date, which is usually 2 working days before the settlement date.

The workflow supported in 360T's SEF only concerns the execution of the NDF. Once NDF will be subject to clearing, further information will be completed in this manual. The settlement of the NDF always takes place offline between the counterparties.

An FX Non deliverable forward (NDF) product definition is opened after selecting the product NDF in the product selection area and after clicking on a price in one of the FX Live Pricing.

The product definition screen will already include predefined data corresponding to the selection in the live pricing screen. For example, the currency pair and the effective date correspond to that selection.

The market taker enters an NDF with the following data.

<span id="page-9-0"></span>SEF highlighted providers are on the SEF.

|                                                                                                                                                                                                                                                                                                                                                     | Notional amount. Either currency<br>field can be used, depending on<br>the amount to be traded                                                                                                                               |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                                                                                                                                                                                                                                     | The minimum notional amount,<br>that can be entered, is the<br>currency's smallest monetary<br>unit.                                                                                                                         |
|                                                                                                                                                                                                                                                                                                                                                     | The upper limit is restricted to a<br>fixed digit length. 360T reserves<br>the right to amend the fixed digit<br>length whenever it is deemed to<br>be necessary.                                                            |
|                                                                                                                                                                                                                                                                                                                                                     | Value date of the trade                                                                                                                                                                                                      |
|                                                                                                                                                                                                                                                                                                                                                     | The Fixing Date will automatically<br>be populated by the system<br>based on the spot date<br>convention of the selected<br>currencies, generally Value date<br>– 2.<br>It can be changed to a date<br>before the value date |
|                                                                                                                                                                                                                                                                                                                                                     | Fixing reference available in                                                                                                                                                                                                |
|                                                                                                                                                                                                                                                                                                                                                     | drop-down list                                                                                                                                                                                                               |
|                                                                                                                                                                                                                                                                                                                                                     |                                                                                                                                                                                                                              |
|                                                                                                                                                                                                                                                                                                                                                     | The link to Regulatory<br>Disclosures leads to a page with<br>additional details from various                                                                                                                                |
| The request duration which is<br>Figure 6 NDF Product definition<br>set to 5 minutes by default for<br>NDFcan be extended/reduced.<br>An indicative quote is displayed if<br>available from 360T's market data<br>source                                                                                                                            | swap dealers and the EMTA. In<br>some cases a login is needed to<br>access the bank specific<br>disclosure pages                                                                                                             |
| The standard bank basket configured for the requester<br>company is displayed by default. It can be refined on<br>currency pair and user level by adding/removing<br>providers.<br>For Non US Requesters with bank relationships to both<br>US banks and non US banks, the US banks will be<br>highlighted in green. Only trades executed with such |                                                                                                                                                                                                                              |

| Two-way price<br>selection can also<br>be defined as<br>default     |
|---------------------------------------------------------------------|
| Allows to execute<br>with several<br>market makers<br>consecutively |
|                                                                     |

<span id="page-10-0"></span>Figure 7 NDF Transaction details

|  | Comments that will be<br>kept on the trade<br>ticket after execution.<br>The maximum length<br>of the comment is<br>5,000 characters    |
|--|-----------------------------------------------------------------------------------------------------------------------------------------|
|  | Check/Uncheck this                                                                                                                      |
|  | field depending on<br>whether the comment<br>is only for internal<br>requester use or to be<br>read by providers.<br>Commented requests |
|  | might be published for<br>manual trader<br>intervention on the<br>maker side                                                            |
|  |                                                                                                                                         |

<span id="page-10-1"></span>Figure 8 NDF Comments

## <span id="page-11-0"></span>**3.2 NDS**

Non-deliverable swaps traded on 360T SEF can be considered as two linked non-deliverable forwards with opposite actions.

The workflow supported in 360T's SEF only concerns the execution of the NDS. Once NDF will be subject to clearing, further information will be completed in this manual. The settlement of the NDS always takes place offline between the counterparties.

The product definition screen will already include predefined data corresponding to the selection in the live pricing screen. For example, the currency pair and the effective periods correspond to that selection.

<span id="page-11-1"></span>

|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |                                                                                                  | Notional amount: either<br>currency field can be                                                                                                                                                                                   |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |                                                                                                  | used, depending on the<br>amount to be traded<br>For uneven swaps,<br>amount can be                                                                                                                                                |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |                                                                                                  | overwritten                                                                                                                                                                                                                        |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |                                                                                                  | The Fixing Date will<br>automatically be<br>populated by the system<br>based on the spot date<br>convention of the<br>selected currencies,<br>generally Value date – 2.<br>It can be changed to a<br>date before the value<br>date |
| Figure 9 NDS Product definition<br>An indicative quote is displayed if<br>available from 360T's market data<br>source<br>The standard bank basket configured for the<br>requester company is displayed by default. It<br>can be refined on currency pair and user<br>level by adding/removing providers.<br>If you are a Non US Person with bank<br>relationships to both US banks and non US<br>banks, the US banks will be highlighted in<br>green. Only trades executed with such SEF<br>highlighted providers are on the SEF. | The request duration which is<br>set to 5 minutes by default for<br>NDS can be extended/reduced. | The link to Regulatory<br>Disclosures leads to a<br>page with additional<br>details from various swap<br>dealers and the EMTA.<br>You will in some cases<br>need a login to access the<br>bank specific disclosure<br>pages        |

Details about the tabs Transaction and Comments can be found in the section about NDF on page 9 of this manual.

## <span id="page-12-0"></span>**3.3 FX Options**

360T supports requests in first generation options, calls and puts and combinations of 2 options (zero premium or standard risk reversal or a request with two legs), European and American style.

Option pricing can be requested for live pricing or with a separate spot delta hedge.

FX Option product definition is accessed via the menu Transaction.

<span id="page-12-1"></span>![](_page_12_Picture_7.jpeg)

Figure 10 Open FX Option product definition

### <span id="page-13-0"></span>**3.3.1 Plain vanilla FX Option**

|                                                                                                                                                                                                                                             | Options can be<br>requested as live prices<br>or with an additional spot<br>delta hedge, in which<br>case a spot rate field is<br>provided in the product<br>definition |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                                                                                                                             | Standard expiry locations<br>and times are offered in<br>the dropdownl ist. Please<br>select depending on the<br>traded currency pair                                   |
|                                                                                                                                                                                                                                             | Notional and Premium<br>currency can be different                                                                                                                       |
|                                                                                                                                                                                                                                             | The Premium Value Date<br>is defaulted to Spot when<br>opening the product<br>definition. This date can<br>be changed                                                   |
|                                                                                                                                                                                                                                             | The Delivery Date is the<br>settlement date of the<br>option.                                                                                                           |
|                                                                                                                                                                                                                                             | The exercise date is<br>defaulted to 1 or 2<br>working days before the<br>Delivery date depending<br>on the spot date<br>convention of the<br>selected currency pair    |
| Figure 11 Plain Vanilla Option product definition<br>The link to Regulatory<br>Disclosures leads to a<br>page with additional<br>details from various swap<br>dealers and the EMTA.<br>You will in some cases<br>need a login to access the |                                                                                                                                                                         |

<span id="page-13-1"></span>Details about the tabs Transaction and Comments can be found in the section about NDF on page 9 of this manual.

bank specific disclosure

pages

#### <span id="page-14-0"></span>**3.3.2 FX Risk Reversal / Zero Cost / Strategy option**

In addition to plain vanilla, it is possible to request combinations of 2 options.

Pre-configured strategies to create a standard risk reversal or a zero-cost option are accessible over the menu Transaction.

| For a zero-premium<br>option request, select                        |
|---------------------------------------------------------------------|
| this option.                                                        |
| In this case, only the<br>Strike of the first option<br>is entered. |
|                                                                     |
| In case of a standard risk<br>reversal option, both                 |
| Strikes need to be                                                  |
| entered                                                             |
|                                                                     |
|                                                                     |
| Options can be                                                      |
| requested as live prices<br>or with an additional spot              |
| delta hedge, in which                                               |
| case an spot rate field is                                          |
| provided in the product<br>definition                               |
|                                                                     |
|                                                                     |
| Inform the market<br>makers on the                                  |
| settlement of the                                                   |
| premium amount                                                      |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |
|                                                                     |

<span id="page-14-1"></span>Figure 12 FX Zero Premium Risk Reversal Option Product Definition

To request a zero-premium option, the user requests the strike for the first option of the combination and the bank will quote the strike of the second option.

In case of a standard risk reversal, the user will request a given strike for both options and the bank will quote the premium, similar to plain vanilla options

Further freely configurable combinations of two plain vanilla options can be requested as Option Strategies. The product definition is similar to the risk reversal product definition, except that any combination of 2 options can be defined.

## <span id="page-15-0"></span>**4 PRICING IN THE TRADER WORKSHEET**

Once a product definition is complete, the requester clicks to Send to receive quote streams to the swap dealers defined in the provider list.

The following describes the workflow when the request is not routed to an automated pricing system but is published for manual pricing in 360T's trader worksheet.

A trade request routed for manual intervention shows up as a blinking line with its details, while a ringing sound notifies the trader of the incoming request. To provide a price, the trader clicks on the request line to get the quoting screen.

## <span id="page-15-1"></span>**4.1 Pricing NDF**

| <b>EX TEX BETA Platform - Trader Worksheet</b>    |                                  |                       |                                               |                   |                                                           |
|---------------------------------------------------|----------------------------------|-----------------------|-----------------------------------------------|-------------------|-----------------------------------------------------------|
| Deal Blotter<br>Administration<br>Tools<br>System | <b>Help</b>                      |                       |                                               |                   |                                                           |
| <b>Action</b><br>Counterpart<br>Timeout<br>.      | Currency1<br>Product             | Currency <sub>2</sub> | Notional                                      | Maturity Date     | EffectiveDate                                             |
| 360T.AMERICAS<br><b>Buys</b>                      | <b>NDF</b> <sup>SEF</sup><br>USD | BRL                   |                                               |                   | 3,000,000.00 3 MONTHS Wed, 27, Nov  3 MONTHS Wed, 27, Nov |
|                                                   |                                  |                       |                                               |                   |                                                           |
|                                                   |                                  |                       |                                               |                   |                                                           |
| Local Date: 23/08/2013 15:47:13                   |                                  |                       |                                               |                   |                                                           |
| Trade Date: 23/08/2013 13:47:13                   |                                  |                       |                                               |                   | 360                                                       |
|                                                   |                                  |                       | User: CITIBANK.Trader1 Company: CITIBANK.TEST | Status: Connected |                                                           |

<span id="page-15-2"></span>Figure 13 NDF Incoming Request for Quote

The price of a NDF is the forward rate, which is calculated by the provided spot rate and forward points.

![](_page_16_Picture_3.jpeg)

<span id="page-16-0"></span>In case the user requests a two-way price, both bid rate (on the left side) and offer rate (on the right side) have to be provided.

Click to Send instantly shows the provided price to the requester in his competitive bidding screen, together with prices provided by other requested market makers, as well as with executable and indicative orders from the SEF Order Book.

Once the price is sent to the requester, the buttons Cancel and Send are replaced by the buttons Interrupt and Requote. You can withdraw your quote by clicking Interrupt. By Requote, you can directly send a newly entered quote.

| - 1 - 1 - 1<br>m.<br>Requote<br>Interrupt<br>$\blacksquare$ As request time | Offer Timeout |  |
|-----------------------------------------------------------------------------|---------------|--|
|                                                                             |               |  |

In case the user executes your offered price, a deal confirmation ticket is displayed to the trader.

| <b>EX TEX BETA Platform - Deal Confirmation</b>                                                                                                                                 |                                                                                                                                                                                                                                                     |                    |                                                                    |                     |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|--------------------------------------------------------------------|---------------------|
| <b>secor</b><br><b>Deal Confirmation</b>                                                                                                                                        |                                                                                                                                                                                                                                                     |                    | Local Date: 23/08/2013 16:11:05<br>Trade Date: 23/08/2013 14:11:05 |                     |
| <b>Transaction Details</b><br>Reference #<br><b>Trade date</b><br>Counterparty Details<br>Requester<br>Trader                                                                   | 3946836<br>Fri, 23. Aug 2013 14:11:04 GMT<br><b>360T.AMERICAS</b><br>360TUSA.Treasurer1                                                                                                                                                             | Provider<br>Trader | <b>CITIBANK.TEST</b><br><b>CITIBANK.Trader1</b>                    |                     |
| Action<br>Notional amount<br>Opposite amount<br>Settlement date<br><b>Fixing date</b><br><b>Fixing reference</b><br>Spot rate<br>Forward points<br>Forward rate<br><b>PTMMM</b> | Product Details - FX Non-deliverable Forward (SEF)<br><b>Client BUYS USD / SELLS BRL</b><br>3,000,000.00 USD<br>7,344,900.00 BRL<br>3 MONTHS Wed, 27. Nov 2013<br>Mon, 25. Nov 2013<br><b>BRL01</b><br>2.40020<br>481.000<br>2.4483000<br>2.4545000 |                    |                                                                    |                     |
| Print / Export                                                                                                                                                                  |                                                                                                                                                                                                                                                     |                    | Print<br>Export                                                    | Close               |
|                                                                                                                                                                                 | User: CITIBANK.Trader1 Company: CITIBANK.TEST Status: Connected                                                                                                                                                                                     |                    |                                                                    | $0 \quad 0 \quad 0$ |

<span id="page-17-0"></span>Figure 15 NDF Deal confirmation ticket

In case the requester executes the trade with a competing market maker, the offer screen automatically closes with an according sound.

## <span id="page-18-0"></span>**4.2 Pricing NDS**

|         | TEX BETA Platform - Trader Worksheet   |                      |               |           |           |                                               |                                         |                   |                   |         |
|---------|----------------------------------------|----------------------|---------------|-----------|-----------|-----------------------------------------------|-----------------------------------------|-------------------|-------------------|---------|
| System  | Deal Blotter Tools Administration Help |                      |               |           |           |                                               |                                         |                   |                   |         |
| Timeout | Counterpart                            | <b>Action</b><br>. 1 | Product       | Currency1 | Currency2 | Notional                                      | Maturity Date                           |                   | EffectiveDate     |         |
| 04:55   | 360T.AMERICAS                          | Swap                 | <b>NDSSEF</b> | USD       | BRL       |                                               | 5,000,000.00 3 MONTHS Wed, 27, Nov SPOT |                   | Tue, 27. Aug 2013 |         |
|         |                                        |                      |               |           |           |                                               |                                         |                   |                   |         |
|         |                                        |                      |               |           |           |                                               |                                         |                   |                   |         |
|         | Local Date: 23/08/2013 16:14:27        |                      |               |           |           |                                               |                                         |                   |                   |         |
|         | Trade Date: 23/08/2013 14:14:27        |                      |               |           |           |                                               |                                         |                   | 260               |         |
|         |                                        |                      |               |           |           | User: CITIBANK.Trader1 Company: CITIBANK.TEST |                                         | Status: Connected |                   | $\circ$ |

<span id="page-18-1"></span>Figure 16 Incoming NDS request for quote

An NDS is priced by providing the spot rate and the swap points, potentially near and far points in case the request is a forward starting NDS.

In case a two-way request is made, the swap points have to be provided on the bid and on the offer side.

![](_page_19_Picture_4.jpeg)

<span id="page-19-0"></span>Far Rate calculated from the entered Spot rate and Forward Points. The swap points is the quote displayed to the requester. The Spot rate is also displayed for information

To comply with CFTC Rule 23.431(a)(3)(i) regarding delivery of the pre-trade mid-market mark (PTMMM), enter the mid swap points in this field

|                                   | <b>ISS</b> TEX BETA Platform - Trader Worksheet                 |                                                        |                                               |                               |                                          |
|-----------------------------------|-----------------------------------------------------------------|--------------------------------------------------------|-----------------------------------------------|-------------------------------|------------------------------------------|
| ∑ystem                            | Deal Blotter Tools Administration Help                          |                                                        |                                               |                               |                                          |
| Timeout<br>Counterpart            | Product<br>Action                                               | Currency1<br>Currency2                                 | Notional                                      | Maturity Date                 | EffectiveDate                            |
|                                   |                                                                 |                                                        |                                               |                               |                                          |
|                                   |                                                                 |                                                        |                                               |                               |                                          |
|                                   |                                                                 |                                                        |                                               |                               |                                          |
|                                   |                                                                 | Local Date: 23/08/2013 17:22:09                        |                                               | Minimize                      |                                          |
| Requests<br>Offers                |                                                                 | Trade Date: 23/08/2013 15:22:09                        |                                               |                               |                                          |
| <b>Product Definition</b>         |                                                                 |                                                        |                                               | <b>Request Timeout: 04:42</b> | <b>Requests</b><br>04:42<br><b>BR</b>    |
| <b>Transaction Details</b>        |                                                                 |                                                        |                                               |                               | <b>NDS</b><br><b>USD/BRL</b>             |
| Reference #                       | 3946857                                                         |                                                        |                                               |                               |                                          |
| Counterparty Details<br>Requester | 360T.AMERICAS                                                   |                                                        |                                               |                               |                                          |
| Individual                        | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                        |                                                        |                                               |                               |                                          |
| Product Details                   | FX Non-deliverable Swap (SEF)                                   |                                                        |                                               |                               |                                          |
| Near Leg                          | <b>Client BUYS USD / SELLS BRL</b><br>Notional 5,000,000.00 USD |                                                        |                                               |                               |                                          |
|                                   | Effective date SPOTNEXT Wed, 28. Aug 2013                       |                                                        |                                               |                               |                                          |
| Fixing date<br>Far Leg            | <b>Mon, 26. Aug 2013</b><br><b>Client SELLS USD / BUYS BRL</b>  |                                                        |                                               |                               |                                          |
| Notional                          | 5,000,000.00 USD                                                |                                                        |                                               |                               |                                          |
| Maturity date<br>Fixing date      | <b>BROKEN Thu, 05. Dec 2013</b><br>Tue, 03. Dec 2013            |                                                        |                                               |                               |                                          |
| Fixing reference BRL01            |                                                                 |                                                        |                                               |                               |                                          |
|                                   |                                                                 |                                                        |                                               | PTMMM Points                  | <b>Offers</b>                            |
|                                   |                                                                 |                                                        |                                               | 500,000                       |                                          |
|                                   |                                                                 |                                                        |                                               | Spot Rate                     |                                          |
|                                   |                                                                 |                                                        |                                               | 2.38070                       |                                          |
|                                   |                                                                 |                                                        | BidNear Points                                | Offer Near Points             |                                          |
|                                   |                                                                 |                                                        |                                               | 21.900<br>0.0                 |                                          |
|                                   |                                                                 |                                                        | BidFar Points                                 | Offer Par Points              |                                          |
|                                   |                                                                 |                                                        | 521.400                                       | O,<br>÷                       |                                          |
|                                   |                                                                 |                                                        | Bid Rate   Points                             | Points   Offer Rate           |                                          |
|                                   |                                                                 |                                                        | 2.4328400   499.500                           |                               |                                          |
|                                   |                                                                 |                                                        |                                               |                               |                                          |
|                                   |                                                                 | Offer Timeout                                          |                                               |                               |                                          |
|                                   |                                                                 | <mark>⊺</mark> 품₩ <mark>Ⅰ 준</mark> ™ <mark>99</mark> 준 |                                               | Send<br>Cancel                |                                          |
|                                   |                                                                 | ■ As request time                                      |                                               |                               |                                          |
|                                   |                                                                 |                                                        | User: CITIBANK.Trader1 Company: CITIBANK.TEST |                               | Status: CornectBaaznik (JIRA)<br>$\circ$ |
|                                   |                                                                 |                                                        |                                               |                               |                                          |

<span id="page-20-0"></span>Figure 18 Offer screen for a forward starting NDS

The swap rate of the forward starting swap is calculated by the difference between far points and near points

|                                                                    | 1995 TEX BETA Platform - Trader Worksheet                                                                                          |                                                                                          |                                                   |                                                                      |                                             |
|--------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------|---------------------------------------------------|----------------------------------------------------------------------|---------------------------------------------|
| System                                                             | Deal Blotter Tools Administration Help                                                                                             |                                                                                          |                                                   |                                                                      |                                             |
| Timeout<br>Counterpart                                             | <b>Action</b><br>Product                                                                                                           | Currency2<br>Currency1                                                                   | Notional                                          | Maturity Date                                                        | EffectiveDate                               |
|                                                                    |                                                                                                                                    |                                                                                          |                                                   |                                                                      |                                             |
| Requests<br>Offers                                                 |                                                                                                                                    | Local Date: 23/08/2013 17:20:14<br>Trade Date: 23/08/2013 15:20:14                       |                                                   | Minimize                                                             |                                             |
| <b>Product Definition</b>                                          |                                                                                                                                    |                                                                                          |                                                   |                                                                      | Requests                                    |
| <b>Transaction Details</b>                                         |                                                                                                                                    |                                                                                          |                                                   | Request Timeout: 04:39                                               | 04:39<br>BR<br><b>NDS</b><br><b>USD/BRL</b> |
| Reference #                                                        | 3946855                                                                                                                            |                                                                                          |                                                   |                                                                      |                                             |
| Counterparty Details<br>Requester<br>Individual                    | 360T.AMERICAS<br>360TUSA.Treasurer1                                                                                                |                                                                                          |                                                   |                                                                      |                                             |
| <b>Product Details</b><br>Near Leg                                 | FX Non-deliverable Swap (SEF)<br><b>Client USD / BRL</b><br>Notional 5,000,000.00 USD<br>Effective date SPOTNEXT Wed, 28. Aug 2013 |                                                                                          |                                                   |                                                                      |                                             |
| Fixing date<br>Far Leg<br>Notional<br>Maturity date<br>Fixing date | <b>Mon, 26. Aug 2013</b><br><b>Client USD / BRL</b><br>5,000,000.00 USD<br><b>BROKEN Thu, 05. Dec 2013</b><br>Tue, 03. Dec 2013    |                                                                                          |                                                   |                                                                      |                                             |
| Fixing reference BRL01                                             |                                                                                                                                    |                                                                                          |                                                   |                                                                      | <b>Offers</b>                               |
|                                                                    |                                                                                                                                    |                                                                                          |                                                   | PTMMM Points<br>500,000<br>Spot Rate<br>2.38040                      |                                             |
|                                                                    |                                                                                                                                    |                                                                                          | <b>BidNear Points</b><br>BidFar Points<br>505.933 | Offer Near Points<br>20.933<br>20.933<br>Offer Far Points<br>485.000 |                                             |
|                                                                    |                                                                                                                                    |                                                                                          | Bid Rate   Points                                 | Points   Offer Rate                                                  |                                             |
|                                                                    |                                                                                                                                    |                                                                                          |                                                   | 2.4309933   485.000 505.933   2.4289000                              |                                             |
|                                                                    |                                                                                                                                    | Offer Timeout<br>- 0 중 m 59 중 s<br>$\begin{array}{c} 0 \end{array}$<br>■ As request time |                                                   | <b>Send</b><br>Cancel                                                |                                             |
|                                                                    |                                                                                                                                    |                                                                                          | User: CITIBANK.Trader1 Company: CITIBANK.TEST     | Status: Connected                                                    | $\circ$ $\circ$ $\circ$                     |

<span id="page-21-0"></span>Figure 19 Offer screen for a forward starting NDS from a two-way price request

Once the price is sent to the requester, the buttons Cancel and Send are replaced by the buttons Interrupt and Requote. You can withdraw your quote by clicking Interrupt. By Requote, you can directly send a newly entered quote.

| Offer Timeout             |           |         |
|---------------------------|-----------|---------|
|                           |           |         |
| $\bigcup$ As request time | Interrupt | Requote |

In case the user executes your offered price, a deal confirmation ticket is displayed to the trader.

| <b>ES TEX BETA Platform - Deal Confirmation</b>                                                         |                                                                                                                                                                                                                      |                           | $-$ 10                                                             |
|---------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|--------------------------------------------------------------------|
| = M T                                                                                                   |                                                                                                                                                                                                                      |                           | Local Date: 23/08/2013 17:27:06<br>Trade Date: 23/08/2013 15:27:06 |
| <b>Deal Confirmation</b>                                                                                |                                                                                                                                                                                                                      |                           |                                                                    |
| <b>Transaction Details</b><br>Reference #<br>Trade date                                                 | 3946861<br>Fri, 23. Aug 2013 15:27:05 GMT                                                                                                                                                                            |                           |                                                                    |
| Counterparty Details<br>Requester<br><b>Trader</b>                                                      | <b>360T.AMERICAS</b><br>360TUSA.Treasurer1                                                                                                                                                                           | Provider<br><b>Trader</b> | <b>CITIBANK.TEST</b><br><b>CITIBANK.Trader1</b>                    |
| Near Leg<br>Notional<br>Opposite<br>Effective date<br><b>Fixing date</b><br>Spot rate<br>Points<br>Rate | Product Details - FX Non-deliverable Swap (SEF)<br><b>Client SELLS USD / BUYS BRL</b><br>5,000,000.00 USD<br>11,905,500.00 BRL<br><b>SPOT Tue, 27. Aug 2013</b><br>Fri, 23. Aug 2013<br>2.38110<br>0.00<br>2.3811000 |                           |                                                                    |
| Far Leg<br>Notional<br>Opposite<br>Maturity date<br><b>Fixing date</b><br>Spot rate<br>Points<br>Rate   | <b>Client BUYS USD / SELLS BRL</b><br>5,000,000.00 USD<br>12,148,072.00 BRL<br>3 MONTHS Wed, 27. Nov 2013<br>Mon, 25. Nov 2013<br>2.38110<br>485.144<br>2.4296144                                                    |                           |                                                                    |
| <b>Fixing reference</b><br><b>PTMMM</b>                                                                 | <b>BRL01</b><br>475.000                                                                                                                                                                                              |                           |                                                                    |
| Print / Export                                                                                          |                                                                                                                                                                                                                      |                           | Export<br>Print<br>Close                                           |
|                                                                                                         | User: CITIBANK.Trader1 Company: CITIBANK.TEST   Status: Connected                                                                                                                                                    |                           | 0000                                                               |

<span id="page-22-1"></span>Figure 20 NDS Deal confirmation ticket

Should the requester execute with another provider, the offer screen automatically closes with an according sound.

## <span id="page-22-0"></span>**4.3 Pricing FX Options**

| TEX BETA Platform - Trader Worksheet |                                                                                       |                      |                       |           |           |                                          |  |               |  |                   |  |
|--------------------------------------|---------------------------------------------------------------------------------------|----------------------|-----------------------|-----------|-----------|------------------------------------------|--|---------------|--|-------------------|--|
| System                               | Deal Blotter<br>Tools Administration Help                                             |                      |                       |           |           |                                          |  |               |  |                   |  |
| Timeout                              | Counterpart                                                                           | <b>Action</b><br>. 1 | Product               | Currency1 | Currency2 | Notional                                 |  | Maturity Date |  | EffectiveDate     |  |
| 04:53                                | 360T.AMERICAS                                                                         | <b>BUYS</b>          | Option <sup>SEF</sup> | EUR       | USD.      | 10,000,000.00 3 MONTHS Mon, 25, Nov SPOT |  |               |  | Tue, 27. Aug 2013 |  |
|                                      |                                                                                       |                      |                       |           |           |                                          |  |               |  |                   |  |
|                                      | Local Date: 23/08/2013 18:01:10<br>1360<br>Trade Date: 23/08/2013 16:01:10            |                      |                       |           |           |                                          |  |               |  |                   |  |
|                                      | $\circ$ $\circ$<br>Status: Connected<br>User: CITIBANK.Trader1 Company: CITIBANK.TEST |                      |                       |           |           |                                          |  |               |  |                   |  |

The price of an FX Option is the premium amount, which can be expressed in one of the two currencies of the underlying currency pair.

The market maker usually expresses the price of the option either in terms of the base currency per unit of the underlying currency (in pips), or in percent of the notional currency.

The quote is shown to the market taker in total premium amount with additional details of the price in pips of the premium currency and, in case that notional and premium currencies are the same, in % of this notional amount. If the taker requests an option price with separate delta hedge, he will also see the total notional of the spot hedge and as price indication also applied volatility in %.

On the following pages, a number of examples illustrate the different cases of requests for the different FX options or options combinations.

#### <span id="page-23-0"></span>**4.3.1 Plain vanilla FX option with Notional in quote currency and Premium in base currency**

The market taker requests quotes to buy a EUR/USD option with USD notional and premium in EUR.

Buy EUR Put / USD Call Notional USD 10 million Strike EUR/USD 1.30 Expiry 3 months Premium CCY EUR

As the option notional and option premium are in opposite currencies, the market maker is required to price this option in EUR pips of the USD notional.

For instance 15 EUR pips, corresponding to: 10,000,000 \* 0.0015 = 15,000 EUR

The total premium he is charging is expressed in red below the price input field.

|  |  |  | Price<br>in<br>premium<br>converted<br>the "Send" button. | pips<br>of<br>the<br>currency<br>is<br>into<br>total<br>premium amount, offered to<br>the requestor by pressing |
|--|--|--|-----------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------|
|  |  |  |                                                           |                                                                                                                 |
|  |  |  |                                                           |                                                                                                                 |

<span id="page-24-0"></span>Figure 21 Pricing of an EUR/USD FX Option, USD notional, premium in EUR

Once the trader has entered the price in pips, he clicks on the "Send" button to send the quote to the requesting market taker.

When clicking on "Cancel", the request will disappear and the taker will see no offer.

### <span id="page-25-0"></span>**4.3.2 Example 2: EUR/USD - notional and premium in EUR**

| Buy EUR Call / USD Put |                |  |  |  |  |  |  |
|------------------------|----------------|--|--|--|--|--|--|
| Notional               | EUR 10 million |  |  |  |  |  |  |
| Strike EUR/USD         | 1.30           |  |  |  |  |  |  |
| Maturity               | 3 months       |  |  |  |  |  |  |
| Premium CCY            | EUR            |  |  |  |  |  |  |

As the notional and the premium are expressed in the same currency, the market maker is requested to price this option in percentage of the notional.

|  |  | Price in percent<br>of the<br>notional<br>amount<br>is<br>converted<br>into<br>total<br>premium amount offered to |
|--|--|-------------------------------------------------------------------------------------------------------------------|
|  |  | the requestor                                                                                                     |
|  |  |                                                                                                                   |
|  |  |                                                                                                                   |

<span id="page-25-1"></span>Figure 22 Pricing of a EUR/USD FX Option with premium in EUR

### <span id="page-26-0"></span>**4.3.3 Example 3: Zero-Cost Strategy – notional in USD – Premium in EUR**

The requested zero cost option is a combination of a Put option and a Call option with the same notional and expiry date. The premium currency is EUR (Total of the premium amounts equals zero).

Buy EUR Put / USD Call Sell EUR Call / USD Put Notional USD 10 million Notional USD 10 million Strike EUR/USD 1,30 Maturity 3 months Premium CCY EUR (netted)

First option Second option

The market maker quotes the strike price for Option 2 and has to enter an indicative option premium in EUR pips of the USD notional. For instance 15 EUR pips

Corresponding to: 10.000.000 \* 0,0015 = 15.000 EUR

The option premium price is only an **indicative price** relevant for the two options of the zero cost structure as the treasurer needs a price of each individual option to be able to revaluate them during their life cycle.

<span id="page-27-0"></span>

|                                         |                                                              |  | Strike of option 2<br>(also in red) |
|-----------------------------------------|--------------------------------------------------------------|--|-------------------------------------|
|                                         |                                                              |  |                                     |
|                                         |                                                              |  |                                     |
|                                         |                                                              |  |                                     |
| Figure 23 Pricing a zero-cost FX option | Indicative premium of one<br>option. The second option       |  |                                     |
|                                         | premium is equal in amount<br>with opposite sign (in black). |  |                                     |

### <span id="page-28-0"></span>**4.3.4 Example 4: Risk Reversal Strategy - Notional in USD -premium in EUR**

The requested risk reversal option is a combination of a Put option and a Call option with same notional and expiry date, the notional expressed in USD and premium amounts expressed in EUR.

Buy EUR Put / USD Call Sell EUR Call / USD Put Notional USD 10 million Notional USD 10 million Strike EUR/USD 1.30 Strike EUR/USD 1.34 Maturity 3 months Premium CCY EUR netted

First option Second option

The market maker prices both options by entering both premiums in EUR pips.

<span id="page-28-1"></span>

|                                                |  |        |                                                                            |  | Premiums of the 2 options of<br>this risk reversal are netted               |
|------------------------------------------------|--|--------|----------------------------------------------------------------------------|--|-----------------------------------------------------------------------------|
|                                                |  |        |                                                                            |  |                                                                             |
|                                                |  |        |                                                                            |  | 1.53 EUR pips per<br>USD notional<br>corresponds to 1,530<br>EUR to receive |
| Figure 24 Pricing of a risk reversal FX option |  | to pay | 2.81 EUR pips per USD<br>notional corresponds to a<br>premium of 2,810 EUR |  |                                                                             |

#### <span id="page-29-0"></span>**4.3.5 Example 5: Bear Spread (Buy Put and Sell Put)**

The bear spread request, entered by the market user under Option Strategies, is a combination of two EUR/USD put options.

Buy EUR Put / USD Call Sell EUR Put / USD Call Notional USD 10 million Notional USD 10 million Strike EUR/USD 1,25 Strike EUR/USD 1,21 Maturity 3 months Premium CCY EUR not netted

First option Second option

The market maker prices each component of the option strategy separately similarly to two plain vanilla options in pips of the premium currency.

![](_page_29_Picture_9.jpeg)

<span id="page-29-1"></span>Figure 25 Pricing of an FX option strategy

When executing any of the combined option request, two separate confirmations with a comment referring to the second option are displayed.

#### <span id="page-30-0"></span>**4.3.6 Example 6: EUR/USD Option – Notional in USD – premium in EUR with Separate Delta Hedge**

The market taker requests quotes to buy a EUR/USD option with USD notional and premium in EUR.

Buy EUR Put / USD Call Notional USD 10 million Strike EUR/USD 1.33 Priced on Spot Level 1.3383 Expiry 3 months Premium CCY EUR

The market maker has to price this option in EUR pips of the USD notional.

For instance 22 EUR pips, corresponding to: 10,000,000 \* 0.0022 = 22,000 EUR

The total premium he is charging is expressed in red below the price input field.

The market maker has to add the total Notional of the Delta Hedge amount, in this request in USD as the requested notional was USD.

The market maker has to add as an **indicative** information the volatility on which he has calculated the pips price.

| Figure 26 Pricing of an FX Option with separate delta hedge |  |                                    |  |
|-------------------------------------------------------------|--|------------------------------------|--|
|                                                             |  | Input for Delta Notional Amount in |  |

<span id="page-31-0"></span>When executing the option, a confirmation for the option and for the separate delta hedge will be delivered.

Notional Currency

| <b>EX TEX BETA Platform - Deal Confirmation</b>                                                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                             |                                  |                                                                    |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------|--------------------------------------------------------------------|
| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                                                                                                                                                                                                                     |                                                                                                                                                                                                                                                                                                                             |                                  | Local Date: 23/08/2013 18:35:12<br>Trade Date: 23/08/2013 16:35:12 |
| <b>Deal Confirmation</b>                                                                                                                                                                                                                                                                                     |                                                                                                                                                                                                                                                                                                                             |                                  |                                                                    |
| <b>Transaction Details</b><br>Reference #<br><b>Trade date</b>                                                                                                                                                                                                                                               | 3946878<br>Fri, 23. Aug 2013 16:35:12 GMT                                                                                                                                                                                                                                                                                   |                                  |                                                                    |
| Counterparty Details<br>Requester<br>Trader                                                                                                                                                                                                                                                                  | <b>360T.AMERICAS</b><br>360TUSA.Treasurer1                                                                                                                                                                                                                                                                                  | <b>Provider</b><br><b>Trader</b> | <b>CITIBANK.TEST</b><br><b>CITIBANK.Trader1</b>                    |
| Product Details - FX Option with Delta Hedge (SEF)<br>Action<br>for<br>Premium amount<br>Notional amount<br>Opposite amount<br><b>Strike</b><br>Spot rate<br>Option type<br>Premium date<br>Exercise date<br>Delivery date<br><b>Expiry</b><br>Volatility<br>Delta Exchange Spot Transaction<br><b>PTMMM</b> | <b>Client BUYS EUR CALL on EUR / USD</b><br><b>22,0000 EUR Pips</b><br>22,000.00 EUR<br>10,000,000.00 USD<br>7,518,796.99 EUR<br>1.3300000<br>1.33830<br><b>CALL-EUROPEAN</b><br><b>SPOT Tue, 27. Aug 2013</b><br>Mon, 25. Nov 2013<br>3 MONTHS Wed, 27. Nov 2013<br>New York 10:00<br>12.8000%<br>3946880<br>20,000.00 EUR |                                  |                                                                    |
| Print / Export                                                                                                                                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                             |                                  |                                                                    |
|                                                                                                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                             | Export                           | Print<br>Close                                                     |
|                                                                                                                                                                                                                                                                                                              | User: CITIBANK.Trader1 Company: CITIBANK.TEST   Status: Connected                                                                                                                                                                                                                                                           |                                  | 0000                                                               |

<span id="page-32-0"></span>Figure 27 Deal confirmation for an FX Option with a separate Spot Delta hedge

| <b>EEX BETA Platform - Deal Confirmation</b>                                                              |                                                                                                        |                      | u.                                                                 |
|-----------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------|----------------------|--------------------------------------------------------------------|
| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                  |                                                                                                        |                      | Local Date: 23/08/2013 18:35:12<br>Trade Date: 23/08/2013 16:35:12 |
| <b>Deal Confirmation</b>                                                                                  |                                                                                                        |                      |                                                                    |
| <b>Transaction Details</b><br>Reference #<br><b>Trade date</b>                                            | 3946880<br>Fri, 23. Aug 2013 16:35:12 GMT                                                              |                      |                                                                    |
| <b>Counterparty Details</b><br>Requester<br>Trader                                                        | 360T.AMERICAS<br>360TUSA.Treasurer1                                                                    | Provider -<br>Trader | <b>CITIBANK.TEST</b><br><b>CITIBANK.Trader1</b>                    |
| Product Details - FX Spot (SEF)<br>Action<br>Notional amount<br>Opposite amount<br>Effective date<br>Rate | <b>Client SELLS EUR / BUYS USD</b><br>1,800,000.00 USD<br>1,344,989.91 EUR<br><b>BROKEN</b><br>1.33830 | Tue, 27. Aug 2013    |                                                                    |
| Delta Hedge of Option Transaction<br><b>PTMMM</b>                                                         | 3946878<br>20000.00000                                                                                 |                      |                                                                    |
|                                                                                                           |                                                                                                        |                      |                                                                    |
| Print / Export                                                                                            |                                                                                                        |                      |                                                                    |
|                                                                                                           |                                                                                                        | Export               | Print<br>Close                                                     |
|                                                                                                           | User: CITIBANK.Trader1 Company: CITIBANK.TEST   Status: Connected                                      |                      | $\circ$ $\circ$ $\circ$                                            |

<span id="page-33-0"></span>Figure 28 Deal confirmation for a separate delta hedge spot trade

Please note that the delta spot hedge notional will always be in the currency of the option notional requested.

Please note also that 360T does support actually ONLY requests for SPOT value delta hedges.

## <span id="page-34-0"></span>**5 SEF ORDER BOOK**

The SEF Order Book allows the SEF enabled users to place limit orders that can be viewed by all other users registered as SEF Participants with 360T.

| <b>EX TEX BETA Platform - Live Pricing</b> |                          |  |  |                      |        |  |  |  |
|--------------------------------------------|--------------------------|--|--|----------------------|--------|--|--|--|
| System                                     | Transaction Deal Blotter |  |  | Tools Administration | Help   |  |  |  |
|                                            |                          |  |  | -(0) Mute Sound      | Ctrl+M |  |  |  |
|                                            |                          |  |  | Order Management     |        |  |  |  |
|                                            |                          |  |  | SEE Order Book       |        |  |  |  |

<span id="page-34-1"></span>Figure 29 Access the SEF Order Book

All users can view all orders placed in the SEF Order Book. Execution is only possible between entities that have a counterparty relationship with each other.

Orders displayed in black can be executed. Orders displayed in grey with obfuscated Company name are only indicative, as they originate from market participants without a relationship to the entity looking at the order book.

Orders for NDF, NDS and FX Options are supported in the SEF Order Book.

|                                        | SEF Order Book    |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|----------------------------------------|-------------------|---------|-----------------------------------------|------------|--------------------------|----------|----------|-----|-------------------------------|-----------------------------------------|---------|--------------------------------------------------------------------------------|------------|----------|----------|-----------------------|
| Eile                                   | Tools<br>Edit     | Help    |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
| Q                                      |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        | Fx Option NDF NDS |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          | 圓                     |
| <b>BID</b>                             |                   |         |                                         |            |                          |          |          |     | ASK                           |                                         |         |                                                                                |            |          |          |                       |
| $\ldots$ $\blacktriangle$ <sup>1</sup> | Company           |         | Currency Notional Amount Notional       |            | Forward Date Fixing Date |          | Rate     |     | $S$ $\cdots$ $A$ <sup>1</sup> | Company                                 |         | Currency Notional Amount Notiona Forward Date Fixing Date                      |            |          |          | s,<br>Rate            |
| ≑                                      | **********        | USD/BRL | 1,000,000.00                            | <b>USD</b> | 26/09/13                 | 24/09/13 | 2.445709 |     | ≑…                            | **********                              | USD/BRL | 5,000,000.00                                                                   | <b>USD</b> | 26/11/13 | 22/11/13 | 2.503                 |
| $\equiv$                               | **********        | USD/BRL | 5,000,000.00                            | USD        | 26/11/13                 | 22/11/13 | 2.481    |     | $\div$                        | 360T.AMERICAS                           | USD/CNY | 1,000,000.00                                                                   | USD        | 26/11/13 | 22/11/13 | 6.5075                |
| ≑…                                     | US BANK, TEST     | USD/CNY | 3,000,000.00                            | USD        | 26/09/13                 | 24/09/13 | 6,1232   |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
|                                        |                   |         |                                         |            |                          |          |          |     |                               |                                         |         |                                                                                |            |          |          |                       |
| $\blacksquare$                         |                   |         | 4 2000000000000000000000000000000000000 |            |                          |          |          | ▶ Ⅲ |                               | 4 3000000000000000000000000000000000000 |         |                                                                                |            |          |          | $\blacktriangleright$ |
| $\Theta$                               |                   |         |                                         |            |                          |          |          |     |                               |                                         |         | User: USBANK.Trader1, Company: US BANK.TEST <b>CONTROLLER INTO THE CONTROL</b> |            |          |          |                       |

<span id="page-34-2"></span>Figure 30 SEF Order Book

A new order can be placed either by selecting New in the File menu, or by using the button in the toolbar.

| S New NDF (1)          |                                                           |  |  |  |  |  |  |  |
|------------------------|-----------------------------------------------------------|--|--|--|--|--|--|--|
| Sell<br>USD            | 1,000,000.00<br>CNY.                                      |  |  |  |  |  |  |  |
| Date:                  | $\frac{1}{26}$ -Nov-2013 $\frac{1}{2}$ $\Box$<br>3 MONTHS |  |  |  |  |  |  |  |
| Fixing Date:           | Fri 22-Nov-2013<br>Гο                                     |  |  |  |  |  |  |  |
| Fixing Reference:      | CNY01                                                     |  |  |  |  |  |  |  |
| Limits:                |                                                           |  |  |  |  |  |  |  |
| <b>Spot Rate:</b>      | 6,50000                                                   |  |  |  |  |  |  |  |
| <b>Forward Points:</b> | 75,000                                                    |  |  |  |  |  |  |  |
| <b>Forward Rate:</b>   | 6.5075000                                                 |  |  |  |  |  |  |  |
|                        |                                                           |  |  |  |  |  |  |  |
|                        | Close<br>Place                                            |  |  |  |  |  |  |  |

<span id="page-35-0"></span>Figure 31 Create NDF Order

360T does not automatically match orders. The execution remains under the control of the market participants. If an order is displayed as tradable (in black), the user can execute it without placing an opposite order in the user interface but by marking it, opening the context menu over the right mouse click and then selecting Execute; or by opening it with a doubleclick and then clicking the Execute button.

|                        | B-12203 NDF 5000000.00 USD [USD |
|------------------------|---------------------------------|
| Buy<br><b>USD</b>      | 5,000,000.00<br><b>BRL</b>      |
| Date:                  | 어머<br>26-Nov-2013<br>3 MONTHS   |
| Fixing Date:           | Ťō<br>Fri 22-Nov-2013           |
| Fixing Reference:      | BRL01                           |
| Limits:                |                                 |
| <b>Spot Rate:</b>      | 2.45100                         |
| <b>Forward Points:</b> | 300,000                         |
| <b>Forward Rate:</b>   | 2.4810000                       |
|                        |                                 |
|                        | Close<br>Execute                |

<span id="page-35-1"></span>Figure 32 Execute Order from the SEF order book

The SEF Order Book and the Request for Streamed quotes (RFS) process interact in the following way:

If a RFS matches one or several orders in terms of: Action, Currency Couple, Effective Date and Maturity Date, Fixing Reference/Expiry and the notional is equal to the amount in the Limit Order and also the notional currency is the same as in the limit order then the **best executable** limit rate will be displayed in the competitive bidding window. In addition the **best indicative** limit rate will also be displayed in the RFS window if it is better than or equal to the best executable limit rate. A rate is considered indicative (non executable), if no relationship to the company submitting the limit order exists.

|  |  | Quote provided from a limit<br>order in the SEF Order<br>Book from a provider with<br>whom a relationship exists |
|--|--|------------------------------------------------------------------------------------------------------------------|
|  |  |                                                                                                                  |
|  |  |                                                                                                                  |
|  |  |                                                                                                                  |
|  |  |                                                                                                                  |
|  |  | Indicative quote from the                                                                                        |
|  |  | SEF Order Book from a<br>provider with whom no                                                                   |
|  |  | relationship exists                                                                                              |
|  |  |                                                                                                                  |
|  |  |                                                                                                                  |
|  |  |                                                                                                                  |

<span id="page-36-1"></span>Figure 33 Competitive bidding including SEF Order Book quote (tradable and indicative)

Limit orders cannot be partially filled.

All orders placed in the SEF order book expire at the end of the trading day, i.e. at 5pm New York. A placed order can be canceled or amended by any user in the same company as the user who has entered the order.

To comply with the timing delay described in section 37.9 of the regulation, an order has to be 15 seconds in the system before a user can cancel it. Cancellation of an order can be achieved by double-clicking on a placed order within the order book. This re-opens the product definition screen in which the user can initiate the cancellation.

## <span id="page-36-0"></span>**6 DEAL BLOTTER**

The Deal Blotter shows the trade requests that were done. Depending on user settings, it can display only requests from the respective user or requests done by a group of traders or even of the complete provider company.

Several pre-filtered blotters are available.

|         | <b>EE TEX BETA Platform - Trader Worksheet</b> |               |         |                                                               |          |               |                 |
|---------|------------------------------------------------|---------------|---------|---------------------------------------------------------------|----------|---------------|-----------------|
| System  | Deal Blotter<br>Tools Administration           | Help          |         |                                                               |          |               |                 |
| Timeout | Requests today (realtime)                      | <b>Action</b> | Product | Currency1 Currency2                                           | Notional | Maturity Date | EffectiveDate   |
|         | All Requests<br>Executed Requests              |               |         |                                                               |          |               |                 |
|         | Executed Orders                                |               |         |                                                               |          |               |                 |
|         | Maturity Schedule                              | D1            |         |                                                               |          |               |                 |
|         |                                                |               |         | Liser: USBANK.Trader1 Company: US BANK.TEST Status: Connected |          |               | $\circ$ $\circ$ |

<span id="page-37-1"></span>Figure 34 Deal Blotter functions

## <span id="page-37-0"></span>**6.1 Requests today (realtime)**

The realtime Deal Blotter is automatically updated with any event related to the concerned user:

- when a request is received by the provider and under pricing
- when a trade is executed with the provider
- when a request was not executed with the provider. Reasons can be: executed with another provider, expired, request withdrawn by the requester.

It only displays trades of the current trading day.

|           |        | TEX BETA Platform - Deal Blotter (recent trades - realtime) |                 |            |                      |                |                          |        |             |                      |                                                              |                                          |                       |  | → |         |                     |
|-----------|--------|-------------------------------------------------------------|-----------------|------------|----------------------|----------------|--------------------------|--------|-------------|----------------------|--------------------------------------------------------------|------------------------------------------|-----------------------|--|---|---------|---------------------|
| File<br>确 |        | View: USBANKTrader1 Y Filters: All                          |                 |            | $\mathbf{I}$ AND All |                | $\overline{\phantom{a}}$ |        | Time: TODAY | 13.09.13 to 14.09.13 | $\vert$                                                      | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Export                |  |   |         |                     |
| Reference | Parent | User                                                        | Trade Date      | Status     | Legal Entity         | Counterpart    | Product                  | Action | Currency 1  | Currency 2           | Notional                                                     |                                          | <b>Effective Date</b> |  |   | Options |                     |
| 3949866   |        | USBANK.Trader1   Fri,13.Sep 2013                            |                 | <b>PEN</b> | US BANK TEST         | 360T.AMERICAS  | <b>NDF</b>               |        | <b>USD</b>  | <b>KRW</b>           | 10,000,000.00 KRW6 MONTHS Mon,17.Mar 20 6 MONTI View         |                                          |                       |  |   | ∸       | <b>a</b>            |
| 3949865   |        | USBANK.Trader1                                              | Fri.13.Sep 2013 | EXEC       | US BANK TEST         | 360T, AMERICAS | <b>NDF</b>               | Sell   | <b>USD</b>  | <b>BRL</b>           | 10.000.000.00 USD 3 MONTHS Tue.17.Dec 20 3 MONTI View        |                                          |                       |  |   | 피       | $\overline{\alpha}$ |
|           |        |                                                             |                 |            |                      |                |                          |        |             |                      |                                                              |                                          |                       |  |   |         |                     |
|           |        |                                                             |                 |            |                      |                |                          |        |             |                      |                                                              |                                          |                       |  |   | ==      |                     |
|           |        | Last updated on 13/09/2013 15:08:12                         |                 |            |                      |                |                          |        |             |                      | User: USBANK.Trader1 Company: US BANK.TEST Status: Connected |                                          |                       |  |   |         | 000                 |

<span id="page-37-2"></span>Figure 35 Realtime Deal Blotter with pending request and executed trade

|           |        | TEX BETA Platform - Deal Blotter (recent trades - realtime) |                   |               |                           |               |                          |            |             |                      |                                                              |                | →   | ⊡l⊠                       |  |
|-----------|--------|-------------------------------------------------------------|-------------------|---------------|---------------------------|---------------|--------------------------|------------|-------------|----------------------|--------------------------------------------------------------|----------------|-----|---------------------------|--|
| File      |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                |     |                           |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                |     |                           |  |
| 确         |        | View: USBANKTrader1 * Filters: All                          |                   |               | $\sqrt{2}$ AND $\sqrt{4}$ |               | $\overline{\phantom{a}}$ |            | Time: TODAY | $\vert \cdot \vert$  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                     | Export         |     |                           |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             | 13.09.13 to 14.09.13 |                                                              |                |     |                           |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                |     |                           |  |
| Reference | Parent | User                                                        | Trade Date        | <b>Status</b> | Legal Entity              | Counterpart   | Product                  | Action     | Currency 1  | Currency 2           | Notional                                                     | Effective Date |     | Options                   |  |
| 3949866   |        | USBANK.Trader1- Fri,13.Sep 2013-                            |                   | NOT EXEC      | US BANK TEST              | 360T.AMERICAS | <b>NDF</b>               | <b>Buv</b> | USD-        | <b>KRW</b>           | 10,000,000.00 KRW(6 MONTHS Mon,17.Mar 20 6 M View            |                |     | <u> </u><br>$  -$         |  |
| 3949865   |        | USBANK.Trader1                                              | Fri, 13. Sep 2013 | <b>EXEC</b>   | <b>US BANK TEST</b>       | 360T.AMERICAS | <b>NDF</b>               | Sell       | <b>USD</b>  | <b>BRL</b>           | 10,000,000.00 USD 3 MONTHS Tue,17.Dec 20 3 Mt View           |                |     | øГ<br>$\overline{\alpha}$ |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                |     |                           |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                |     |                           |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                |     |                           |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                |     |                           |  |
|           |        |                                                             |                   |               |                           |               |                          |            |             |                      |                                                              |                | e i |                           |  |
|           |        | Last updated on 13/09/2013 15:09:09                         |                   |               |                           |               |                          |            |             |                      | User: USBANK.Trader1 Company: US BANK.TEST Status: Connected |                |     | 000                       |  |

<span id="page-37-3"></span>Figure 36 Realtime Deal Blotter with executed and not execute trades

The main details of the trades are displayed in the table view. A double-click on a trade in the blotter opens the deal confirmation ticket with all details, which can then be printed or exported again.

In case the user is configured to view trades of other users, he can select them over the View drop-down list.

Over the two Filter fields, it is possible to restrict the view based on the selected criteria. Filters are available for the main terms of the trades, e.g. product or product group, counterpart, currencies, maturity and status.

## <span id="page-38-0"></span>**6.2 All Requests**

The All Requests blotter can additionally display orders of the past. It includes requests which were not executed, either because they were executed with another bank, or because they were cancelled by the requester, or because they expired, which is provided by the information in the Status column.

It is not updated automatically like the Realtime Deal Blotter. A click on the binoculars button updates it.

|                      |        | TEX BETA Platform - Deal Blotter                            |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              | $\Box$ o $\boxtimes$                            |
|----------------------|--------|-------------------------------------------------------------|------------------------------------------|---------------------|---------------------------------------------|----------------------------------|---------------------------------------|----------------------|----------------------|--------------------------|--------------------------|-------------------------------------------------------------------|--------------|-------------------------------------------------|
| File                 |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        | View: USBANK.Trader1   Filters: All                         |                                          |                     |                                             | $\overline{\phantom{a}}$ AND All |                                       | $\blacktriangledown$ | Time: 1 MONTH        |                          | $\overline{\phantom{a}}$ |                                                                   |              |                                                 |
| 44                   |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      | 13.08.13 to 14.09.13     |                          | 冎                                                                 |              | Export                                          |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
| Reference            | Parent | User                                                        | Trade Date                               | Status              |                                             | Legal Entity                     | Counterpart                           | Product              | Action               | Currency 1               | Currency 2               | Notional                                                          |              | Options                                         |
| 3949866              |        | USBANK.Trader1-                                             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                     |                                             |                                  | NOT EXEC US BANK TEST   360T AMERICAS | <b>NDF</b>           | <b>Buv</b>           | USD                      | <b>KRW</b>               | 10.000.000.00 K                                                   | View         | ΣΙ ακ                                           |
| 3949865              |        | USBANK.Trader1                                              | Fri,13.Sep 2013                          | EXEC                |                                             | US BANK TEST                     | 360T.AMERICAS                         | <b>NDF</b>           | Sell                 | <b>USD</b>               | <b>BRL</b>               | 10,000,000.00 L                                                   | View         | $\overline{\alpha}$<br>$\vert \cdot \vert$      |
| 3946901              |        | USBANK.Trader1                                              | Mon, 26. Aug 2013                        | NOT EXEC-           |                                             | US BANK TEST                     | 360T.AMERICAS                         | NDF                  | <b>Buv</b>           | <b>USD</b>               | <b>BRL</b>               | 15,000,000.00 {                                                   | View         | $\alpha$<br>$\mathbf{v}$                        |
| 3946898              |        | USBANK.Trader1                                              | Mon, 26. Aug 2013                        | <b>EXEC</b>         |                                             | US BANK TEST                     | 360T.AMERICAS                         | Spot                 | Buy                  | <b>EUR</b>               | <b>USD</b>               | 250,000.00 E                                                      | View         | ᅬ<br>$\alpha$                                   |
| 3946896              |        | USBANK.Trader1                                              | Mon, 26. Aug 2013                        | EXEC                |                                             | US BANK TEST                     | 360T.AMERICAS                         | Option               | Sell                 | <b>EUR</b>               | USD                      | 10,000,000.00 E                                                   | View         | $\mathbf{v}$<br>$\alpha$                        |
| 3946894              |        | USBANK.Trader1                                              | Mon, 26. Aug 2013                        | NOT EXEC            |                                             | US BANK TEST                     | 360T.AMERICAS                         | Option               | Sell                 | <b>EUR</b>               | USD                      | 10,000,000.00 E                                                   | View         | $\alpha$<br>$\mathbf{r}$                        |
| 3946030              |        | USBANK.Trader1                                              | Thu, 22. Aug 2013                        | NOT EXEC-           |                                             | US BANK TEST                     | 360T.AMERICAS                         | NDF                  | Sell-                | <b>USD</b>               | <b>DRL</b>               | 5,000,000.00                                                      | View         | $\alpha$<br>회                                   |
| 3946828              |        | USBANK.Trader1                                              | Thu, 22. Aug 2013                        | NOT EXEC            |                                             | US BANK TEST                     | 360T.AMERICAS                         | <b>NDF</b>           | Sell                 | USD                      | <b>BRL</b>               | 5,000,000.00                                                      | View         | $\overline{\alpha}$<br>$\mathbf{r}$             |
| 3946026              |        | USBANK.Trader1-                                             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | NOT EXEC-           |                                             | US BANK TEST                     | 360T.AMERICAS                         | NDF                  | Sell-                | <b>USD</b>               | <b>BRL</b>               | 5,000,000.00                                                      | View         | $\alpha$<br>$\mathbf{v}$                        |
| 3946822              |        | USBANK.Trader1                                              | Thu.22.Aug 2013                          | NOT EXEC            |                                             | US BANK TEST                     | 360T.AMERICAS                         | <b>NDF</b>           | Sell                 | <b>USD</b>               | <b>BRL</b>               | $5.000,000.00 -$                                                  | View         | $\alpha$<br>× 1                                 |
| 3946020              |        | USBANK.Trader1-                                             | Thu, 22. Aug 2013                        | NOT EXEC-           |                                             | US BANK TEST                     | 360T.AMERICAS                         | NDF                  | $\sim$               | <b>USD</b>               | <b>BRL</b>               | $-3,000,000.00$                                                   | View         | $\alpha$<br>$-1$                                |
| 3946817              |        | USBANK.Trader1                                              | Thu.22.Aug.2013                          | NOT EXEC            |                                             | US BANK TEST                     | 360T.AMERICAS                         | <b>NDF</b>           | Sell                 | USD                      | <b>BRL</b>               | $4.000.000.00 +$                                                  | View         | $\alpha$<br>$\mathbf{r}$                        |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          | $\blacktriangleright$                                             | View         | $\alpha$                                        |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        | Last updated on 13/09/2013 15:13:41                         |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          | User: USBANK.Trader1 Company: US BANK.TEST   Status: Connected    |              | $\circ$ $\circ$<br>la                           |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        | <b>EE TEX BETA Platform - Deal Blotter</b>                  |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              | $\rightarrow$<br>$\Box \Box$                    |
| File                 |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      |        | View: 360TUSA.Treasur Filters: All                          |                                          |                     | $\sim$ AND All                              |                                  | $\overline{\phantom{a}}$              | Time: 1 MONTH        |                      | $\blacktriangledown$     |                          | Export                                                            |              |                                                 |
| 鹋                    |        |                                                             |                                          |                     |                                             |                                  |                                       |                      | 13.08.13 to 14.09.13 |                          | 6                        |                                                                   |              |                                                 |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   |              |                                                 |
|                      | Parent | User                                                        | Trade Date                               | <b>Status</b>       |                                             | Counterpart                      |                                       | Product              | Action               |                          |                          | Notional                                                          |              | Options                                         |
| Reference<br>3946901 |        | -360TUSA Treasurer1   Mon. 26. Aug 2013                     |                                          | -CAN-               | Legal Entity<br>360T.AMERICAS 360TBANK.TEST |                                  |                                       | NDF                  | Sell                 | Currency 1<br><b>USD</b> | Currency 2<br><b>BRL</b> | 45,000,000.00 USD 3                                               | View         |                                                 |
| 3946901              |        | 360TUSA Treasurer1                                          | Mon, 26. Aug 2013                        | CAN                 | 360T.AMERICAS                               | CITIBANK-TEST                    |                                       | NDF                  | Sell                 | USD                      | <b>BRL</b>               | 15,000,000.00 USD 3                                               | View         | ⊠ ≪<br>$\alpha$<br>$\mathbf{v}$                 |
| 3946901              |        | 360TUSA Treasurer1 Mon.26.Aug 2013                          |                                          | -CAN-               | 360T.AMERICAS                               | GS.TEST                          |                                       | NDF                  | -Sell-               | <b>USD</b>               | <b>BRL</b>               | 45,000,000.00 USD 3                                               | View         | $\sim$<br>$\alpha$                              |
| 3946901              |        | 360TUSA.Treasurer1                                          | Mon, 26. Aug 2013                        | CAN                 | 360T.AMERICAS                               | CITIBANK.TEST                    |                                       | NDF                  | Sell                 | USD                      | <b>BRL</b>               | 15,000,000.00 USD 3                                               | View         | $\overline{z}$<br>$\alpha$                      |
| 3946901              |        | 360TUSA Treasurer1 Mon. 26 Aug 2013                         |                                          | CAN-                | 360T.AMERICAS                               | US BANK TEST                     |                                       | NDF                  | Sell-                | USD-                     | <b>BRL</b>               | 45,000,000.00 USD 3                                               | View         | $\alpha$<br>$\sim$                              |
| 3946899              |        | 360TUSA.Treasurer1                                          | Mon, 26. Aug 2013                        | CAN                 | 360T.AMERICAS                               | 360TBANK-TEST                    |                                       | NDF                  | Sell                 | <b>USD</b>               | <b>BRL</b>               | 15,000,000.00 USD                                                 | View         | $\overline{\phantom{a}}$<br>$\overline{\alpha}$ |
| 3946899              |        | 360TUSA Treasurer1 Mon.26.Aug 2013                          |                                          | -CAN                | 360T.AMERICAS                               | CITIBANK-TEST                    |                                       | NDF                  | Sell-                | USD-                     | <b>BRL</b>               | 45,000,000.00 USD 3                                               | view         | ⋥<br>$\alpha$                                   |
| 3946899              |        | 360TUSA Treasurer1                                          | Mon, 26. Aug 2013                        | CAN                 | 360T.AMERICAS                               | US BANK TEST                     |                                       | NDF                  | Sell                 | USD                      | <b>BRL</b>               | 15,000,000.00 USD 3                                               | view         | $\alpha$<br>피                                   |
| 3946099              |        | 360TUSA Treasurer1 Mon, 26 Aug 2013                         |                                          | CAN-                | 360T.AMERICAS                               | GS.TEST                          |                                       | NDF                  | Sell-                | <b>USD</b>               | <b>BRL</b>               | 45,000,000.00 USD 3                                               | View<br>View | $\alpha$<br>$-$                                 |
| 3946899              |        | 360TUSA.Treasurer1                                          | Mon, 26. Aug 2013                        | CAN                 | 360T.AMERICAS                               | CITIBANK.TEST                    |                                       | NDF                  | Sell                 | USD<br>EUR               | <b>BRL</b><br>USD        | 15,000,000.00 USD                                                 | Rollover     | $\alpha$                                        |
| 3946898<br>3946896   |        | 360TUSA.Treasurer1   Mon, 26.Aug 2013<br>360TUSA.Treasurer1 | Mon, 26. Aug 2013                        | EXEC<br><b>EXEC</b> | 360T.AMERICAS<br>360T.AMERICAS              | US BANK.TEST<br>US BANK.TEST     |                                       | Spot<br>Option       | Sell<br>Buy          | <b>EUR</b>               | <b>USD</b>               | 250,000.00 EUR BF<br>10,000,000.00 EUR                            | View         | 회<br>$\alpha$<br>⊣<br>$\alpha$                  |
| 3946894              |        | 360TUSA Treasurer1 Mon.26.Aug 2013                          |                                          | CAN                 | 360T.AMERICAS                               | 360TBANK TEST                    |                                       | Option               | <b>Buv</b>           | <b>EUR</b>               | <b>USD</b>               | 40.000.000.00 EUR                                                 | View         | $\alpha$<br>ᅬ                                   |
| 3946894              |        | 360TUSA Treasurer1                                          | Mon, 26. Aug 2013                        | <b>CAN</b>          | 360T.AMERICAS                               | GS.TEST                          |                                       | Option               | <b>Buy</b>           | <b>EUR</b>               | <b>USD</b>               | 10,000,000.00 EUR                                                 | View         | $\alpha$<br>$\sim$                              |
| 3946094              |        | 360TUSA Treasurer1 Mon.26.Aug 2013                          |                                          | CAN-                | 360T.AMERICAS                               | US BANK TEST                     |                                       | Option               | <b>Buy</b>           | <b>EUR</b>               | <b>USD</b>               | 40.000.000.00 EUR                                                 | View         | $\alpha$                                        |
| 3946894              |        | 360TUSA Treasurer1                                          | Mon, 26. Aug 201.                        | CAN                 | 360T.AMERICAS                               | CITIBANK.TEST                    |                                       | Option               | Buy                  | EUR                      | USD                      | 10,000,000.00 EUR                                                 | View         | $\alpha$<br>$-1$                                |
| 3046802              |        | 260THCA Trescurer1 Mon 26 Aug 2012                          |                                          | <b>CAN</b>          | PACIFIEST TRAP                              | <b>GROTBANK TEST</b>             |                                       | AIDE.                | -Brev                | HSD                      | DDL                      | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                          | View         | $\alpha$<br>$\overline{\phantom{a}}$            |
|                      |        |                                                             |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          |                                                                   | View         | $\sim$                                          |
|                      |        | Last updated on 13/09/2013 10:43:19                         |                                          |                     |                                             |                                  |                                       |                      |                      |                          |                          | User: 360TUSA.Treasurer1 Company: 360T.AMERICAS Status: Connected |              | $\circ$ $\circ$<br>$\sigma$                     |

<span id="page-38-2"></span>Figure 37 All Requests Deal Blotter

The filter criteria are the same as in the Realtime Deal Blotter.

## <span id="page-38-1"></span>**6.3 Executed Requests**

The Executed Requests blotter displays the trades of the day done by the user.

|                                                                                                     |                                     | <b>EX BETA Platform - Deal Blotter</b> |                   |        |                      |               |            |                                         |            |            |                                                       |  | →       |           |
|-----------------------------------------------------------------------------------------------------|-------------------------------------|----------------------------------------|-------------------|--------|----------------------|---------------|------------|-----------------------------------------|------------|------------|-------------------------------------------------------|--|---------|-----------|
| File                                                                                                |                                     |                                        |                   |        |                      |               |            |                                         |            |            |                                                       |  |         |           |
| 确                                                                                                   | View: USBANK.Trader1   Filters: All |                                        |                   |        | $\frac{1}{2}$ AND AI |               |            | $\overline{\phantom{a}}$<br>Time: TODAY |            |            | $\overline{\phantom{a}}$<br>冎<br>13.09.13 to 14.09.13 |  | Export  |           |
|                                                                                                     |                                     |                                        |                   |        |                      |               |            |                                         |            |            |                                                       |  |         |           |
| Reference                                                                                           | Parent                              | User                                   | Trade Date        | Status | Legal Entity         | Counterpart   | Product    | Action                                  | Currency 1 | Currency 2 | Notional                                              |  | Options |           |
| 3949865                                                                                             |                                     | USBANK.Trader1                         | Fri, 13. Sep 2013 | EXEC   | US BANK TEST         | 360T.AMERICAS | <b>NDF</b> | Sell                                    | <b>USD</b> | BRL        | 10,000,000.00 USD 3 N View                            |  | ᅬ       | <b>OK</b> |
|                                                                                                     |                                     |                                        |                   |        |                      |               |            |                                         |            |            | Þ                                                     |  |         |           |
|                                                                                                     |                                     |                                        |                   |        |                      |               |            |                                         |            |            |                                                       |  |         |           |
| User: USBANK.Trader1 Company: US BANK.TEST Status: Connected<br>Last updated on 13/09/2013 15:26:09 |                                     |                                        |                   |        |                      |               |            |                                         |            |            |                                                       |  |         | 000       |

<span id="page-39-1"></span>Figure 38 Executed requests Deal Blotter

The main details of the trades are displayed in the table view. A double-click on a trade in the blotter opens the deal confirmation ticket with all details, which can then be printed or exported again.

In case the user is configured to view trades of other users, he can select them over the View drop-down list and then click to the Binoculars button to refresh the view.

The Executed Requests blotter principally shows the trades of the day, but it is possible to extend the view by selecting a wider period over the Time fields.

Over the two Filter fields, it is possible to restrict the view based on the selected criteria. Filters are available for the main terms of the trades, e.g. product or product group, counterpart, currencies, maturity and status.

## <span id="page-39-0"></span>**6.4 Maturity Schedule**

The maturity schedule is a blotter which shows all trades with maturity date in the future.

The filter criteria are the same as in the Executed Requests blotter.

## <span id="page-40-0"></span>**7 CONTACTING 360T**

#### **Global Support**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **EMEA Americas**

#### **USA**

*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900

### **Asia Pacific**

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756