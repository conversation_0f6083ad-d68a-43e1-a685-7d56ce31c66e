![](_page_0_Picture_0.jpeg)

# 360T Limit REST API

API Version v2 Document Version 2.0.5

360 Treasury Systems AG Grüneburgweg 16-18 / Westend Carrée D-60322 Frankfurt am Main Tel: +49 69 900289 0 Fax: +49 69 900289 29 Email: <EMAIL> Commercial Register Frankfurt, No. HRB 49874 Executive Board: <PERSON>, <PERSON> Supervisory Board: <PERSON>

## **Table of Contents**

| 1 |                                                             | Introduction                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |                                                                                                          |  |  |  |
|---|-------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------|--|--|--|
| 2 | 2.1<br>2.2<br>2.3                                           | Communication<br>Secure Connection<br>.<br>Firewall Configuration<br>.<br>Availability<br>.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 4<br>4<br>4<br>4                                                                                         |  |  |  |
| 3 |                                                             | Security                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | 5                                                                                                        |  |  |  |
| 4 | 4.1<br>4.2<br>4.3<br>4.4                                    | Interacting With The Api<br>Status Codes<br>Making Requests<br>.<br>CORS<br>Swagger Specification<br>.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | 6<br>6<br>6<br>6<br>6                                                                                    |  |  |  |
| 5 |                                                             | REST Operations Overview                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | 7                                                                                                        |  |  |  |
| 6 | 6.1<br>6.2<br>6.3<br>6.4<br>6.5<br>6.6<br>6.7<br>6.8<br>6.9 | Risk Portfolio Rule<br>Risk Portfolio Rule Operations<br>6.1.1<br>Add a new Risk Portfolio Rule<br>6.1.2<br>List all Risk Portfolio Rules<br>.<br>6.1.3<br>Get Portfolio Rule<br>.<br>6.1.4<br>Update Risk Portfolio Rule<br>6.1.4.1<br>Partial Update<br>.<br>6.1.5<br>Update Limit value of a Config Rule<br>6.1.6<br>Update Limits of a Config Rule<br>.<br>6.1.7<br>Delete a Risk Portfolio Rule<br>.<br>Risk Portfolio Rule Model<br>.<br>Limit Value Model<br>.<br>Limits Row Model<br>.<br>Single or Group Dealer Model<br>.<br>Single or Group Counterpart Model<br>.<br>Single or Group Legal Entity Model<br>.<br>Single or Group Time Period Model<br>.<br>Single or Group Execution Method Model | 12<br>12<br>12<br>13<br>15<br>17<br>18<br>19<br>19<br>20<br>20<br>21<br>22<br>22<br>22<br>22<br>23<br>23 |  |  |  |
| 7 | 7.1                                                         | Active Rule<br>Active Rule Operations<br>7.1.1<br>List all Active Rules<br>.<br>7.1.2<br>List all Exceptional Limits of an Active Rule<br>.<br>7.1.3<br>Get Active Rule<br>7.1.4<br>Get Limit value of an Active Rule<br>.<br>7.1.5<br>Get Exceptional Limit value of an Active Rule for a given date<br>7.1.6<br>Get Utilization value of an Active Rule<br>.<br>7.1.7<br>Get Utilization value of an Active Rule for a given date<br>7.1.8<br>Update Limit value of an Active Rule<br>.                                                                                                                                                                                                                    | 24<br>24<br>25<br>26<br>26<br>27<br>27<br>28<br>28<br>28                                                 |  |  |  |

|   |     | 7.1.9<br>Update Limits of an Active Rule                                   | 29 |
|---|-----|----------------------------------------------------------------------------|----|
|   |     | 7.1.10<br>Set Exceptional Limit value of an Active Rule                    | 29 |
|   |     |                                                                            |    |
|   |     | 7.1.11<br>Set Exceptional Limits of an Active Rule<br>.                    | 30 |
|   |     | 7.1.12<br>Delete all Exceptional Limits of an Active Rule                  | 30 |
|   |     | 7.1.13<br>Delete Exceptional Limit of an Active Rule for a given date<br>. | 31 |
|   | 7.2 | Active Rule Model<br>.                                                     | 31 |
|   | 7.3 | Exceptional Limit Model                                                    | 32 |
|   |     |                                                                            |    |
|   | 7.4 | Exceptional Limit Row Model<br>.                                           | 32 |
|   | 7.5 | Utilizaton Value Model                                                     | 33 |
|   |     |                                                                            |    |
| 8 |     | Portfolio                                                                  | 34 |
|   | 8.1 | Portfolio Operations                                                       | 34 |
|   |     | 8.1.1<br>Add a Portfolio                                                   | 34 |
|   |     |                                                                            |    |
|   |     | 8.1.2<br>List All Portolios                                                | 35 |
|   |     | 8.1.3<br>Update a Portfolio<br>.                                           | 36 |
|   |     | 8.1.4<br>Get a Portfolio<br>.                                              | 37 |
|   |     | 8.1.5<br>Delete a Portfolio                                                | 37 |
|   | 8.2 | Portfolio Model                                                            | 38 |
|   |     |                                                                            |    |
|   | 8.3 | Portfolio Element Model<br>.                                               | 38 |
|   | 8.4 | Single Or Group Product Model<br>.                                         | 38 |
|   | 8.5 | Currency Couple Model<br>.                                                 | 39 |
|   | 8.6 | Single Or Group Currency Couple Model<br>.                                 | 39 |
|   |     |                                                                            |    |
| 9 |     | Algorithm                                                                  | 40 |
|   |     |                                                                            |    |
|   | 9.1 | Algorithm Operations                                                       | 40 |
|   |     |                                                                            |    |
|   |     | 9.1.1<br>Get Available Algorithms<br>.                                     | 40 |
|   | 9.2 | Algorithm Model<br>.                                                       | 40 |
|   |     |                                                                            |    |
|   |     | 10 Execution Method                                                        | 41 |
|   |     |                                                                            |    |
|   |     | 10.1 Execution Method Operation                                            | 41 |
|   |     | 10.1.1<br>Get Available Execution Methods<br>.                             | 41 |
|   |     | 10.2 ExecutionMethod Model<br>.                                            | 41 |
|   |     |                                                                            |    |
|   |     | 11 Execution Method Group                                                  | 42 |
|   |     | 11.1 Execution Method GroupOperations<br>.                                 | 42 |
|   |     | 11.1.1<br>Add a Execution Method Group<br>.                                | 42 |
|   |     |                                                                            |    |
|   |     | 11.1.2<br>List All Execution Method Group<br>.                             | 43 |
|   |     | 11.1.3<br>Update a Execution Method Group<br>.                             | 43 |
|   |     | 11.1.4<br>Add Execution Method to Execution Method Group<br>.              | 44 |
|   |     | 11.1.5<br>Get a Execution Method Group<br>.                                | 44 |
|   |     | 11.1.6<br>Delete a Execution Method Group                                  | 45 |
|   |     | 11.2 Execution Method GroupModel<br>.                                      | 45 |
|   |     |                                                                            |    |
|   |     | 12 Product Group                                                           | 46 |
|   |     | 12.1 Product Group Operations<br>.                                         | 46 |
|   |     |                                                                            |    |
|   |     | 12.1.1<br>Add a Product Group                                              | 46 |
|   |     | 12.1.2<br>List All Product Groups<br>.                                     | 47 |
|   |     | 12.1.3<br>Update a Product Group<br>.                                      | 47 |
|   |     | 12.1.4<br>Add a Product to Product Group                                   | 48 |
|   |     | 12.1.5<br>Get a Product Group<br>.                                         | 48 |

|         | 12.2 Product Group Model                             | 49 |  |  |  |
|---------|------------------------------------------------------|----|--|--|--|
|         | 13 Product<br>50                                     |    |  |  |  |
|         | 13.1 Product Operations<br>.                         | 50 |  |  |  |
|         | 13.1.1<br>Get Available Products                     | 50 |  |  |  |
|         | 13.2 Product Model<br>.                              | 50 |  |  |  |
|         |                                                      |    |  |  |  |
|         | 14 Currency Couple Group                             | 51 |  |  |  |
|         | 14.1 Currency Couple Group Operations<br>.           | 51 |  |  |  |
|         | 14.1.1<br>Add a Currency Couple Group                | 51 |  |  |  |
|         | 14.1.2<br>List All Currency Couple Groups<br>.       | 52 |  |  |  |
|         | 14.1.3<br>Update a Currency Couple Group<br>.        | 52 |  |  |  |
|         | 14.1.4<br>Get a Currency Couple Group<br>.           | 53 |  |  |  |
|         | 14.1.5<br>Delete a Currency Couple Group             | 54 |  |  |  |
|         | 14.2 Currency Couple Group Model                     | 54 |  |  |  |
|         | 14.3 Currency Couple Model<br>.                      | 54 |  |  |  |
|         |                                                      |    |  |  |  |
|         | 15 Iso Currency                                      | 56 |  |  |  |
|         | 15.1 Iso Currency Operations<br>.                    | 56 |  |  |  |
|         | 15.1.1<br>Get Available Iso Currencies               | 56 |  |  |  |
|         | 15.2 IsoCurrency Model<br>.                          | 56 |  |  |  |
|         |                                                      |    |  |  |  |
|         | 16 Time Period Group                                 | 57 |  |  |  |
|         | 16.1 Time Period Group Operations                    | 57 |  |  |  |
|         | 16.1.1<br>Add a Time Period Group<br>.               | 57 |  |  |  |
|         | 16.1.2<br>List All Time Period Group<br>.            | 58 |  |  |  |
|         | 16.1.3<br>Update a TimePeriod Group<br>.             | 58 |  |  |  |
|         | 16.1.4<br>Get a Time Period Group                    | 59 |  |  |  |
|         | 16.1.5<br>Delete a Time Period Group<br>.            | 59 |  |  |  |
|         | 16.2 Time Period Group Model<br>.                    | 60 |  |  |  |
|         | 16.3 Time Period Model<br>.                          | 60 |  |  |  |
| 17 Time |                                                      | 61 |  |  |  |
|         | 17.1 Time Operations<br>.                            | 61 |  |  |  |
|         | 17.1.1<br>List Available Times<br>.                  | 61 |  |  |  |
|         | 17.2 Time Model                                      | 61 |  |  |  |
|         |                                                      |    |  |  |  |
|         | 18 Dealer Group                                      | 63 |  |  |  |
|         | 18.1 Dealer Group Operations                         | 63 |  |  |  |
|         | 18.1.1<br>List All Dealer Groups                     | 63 |  |  |  |
|         | 18.1.2<br>Get a Dealer Group                         | 63 |  |  |  |
|         | 18.1.3<br>Add a Dealer Group<br>.                    | 64 |  |  |  |
|         | 18.1.4<br>Add a Dealer to Existing Dealer Group<br>. | 64 |  |  |  |
|         | 18.1.5<br>Update a Dealer Group                      | 65 |  |  |  |
|         | 18.1.6<br>Delete a Dealer Group<br>.                 | 65 |  |  |  |
|         | 18.2 Dealer Group Model<br>.                         | 66 |  |  |  |
|         |                                                      |    |  |  |  |
|         | 19 Dealer                                            | 67 |  |  |  |
|         | 19.1 Dealer Operations                               | 67 |  |  |  |
|         | 19.1.1<br>List Available Dealers<br>.                | 67 |  |  |  |
|         | 19.2 Dealer Model<br>.                               | 67 |  |  |  |

| 20 Counterpart Group                                                | 68       |
|---------------------------------------------------------------------|----------|
| 20.1 Counterpart Group Operations<br>.                              | 68       |
| 20.1.1<br>Add a Counterpart Group                                   | 68       |
| 20.1.2<br>List All Counterpart Groups<br>.                          | 69       |
| 20.1.3<br>Update a Counterpart Group                                | 69       |
| 20.1.4<br>Get a Counterpart Group                                   | 70       |
| 20.1.5<br>Delete a Counterpart Group<br>.                           | 70       |
| 20.2 Counterpart Group Model                                        | 71       |
|                                                                     |          |
| 21 Counterpart                                                      | 72       |
| 21.1 Counterpart Operations                                         | 72       |
| 21.1.1<br>List Available Counterparts<br>.                          | 72       |
| 21.2 Counterpart Model<br>.                                         | 72       |
|                                                                     |          |
| 22 Legal Entity Group                                               | 73       |
| 22.1 Legal Entity Group Operations                                  | 73       |
| 22.1.1<br>Add a Legal Entity Group<br>.                             | 73       |
| 22.1.2<br>List All Legal Entities Groups<br>.                       | 74       |
| 22.1.3<br>Update a Legal Entity Group                               | 74       |
| 22.1.4<br>Add a Legal Entity to Existing Legal Entity Group<br>.    | 75       |
| 22.1.5<br>Get a Legal Entity Group                                  | 75       |
| 22.1.6<br>Delete a Legal Entity Group<br>.                          | 75       |
| 22.2 Legal Entity Group Model<br>.                                  | 76       |
| 23 Legal Entity                                                     | 77       |
| 23.1 Legal Entity Operations                                        | 77       |
| 23.1.1<br>List Available Legal Entities                             | 77       |
| 23.2 Legal Entity Model<br>.                                        | 77       |
|                                                                     |          |
| 24 PFE Table                                                        | 78       |
| 24.1 PFE Table Operations                                           |          |
|                                                                     | 78       |
| 24.1.1<br>Add a Currency Couple to PFE Table<br>.                   |          |
| 24.1.2<br>List All PFE Entries<br>.                                 | 78<br>79 |
| 24.1.3<br>Update PFE Entry on PFE Table                             | 81       |
| 24.1.4<br>Update Default PFE Entry on PFE Table                     | 82       |
| 24.1.5<br>Delete a Currency Couple from PFE Table<br>.              | 83       |
| 24.1.6<br>Add a Tenor to PFE Table<br>.                             | 83       |
| 24.1.7<br>Delete a Tenor from PFE Table                             | 83       |
| 24.2 PFE Table Row Model<br>.                                       | 84       |
| 24.3 PFE Table Column Model<br>.                                    | 84       |
| 24.4 Tenor Model                                                    | 84       |
|                                                                     |          |
| 25 Error Handling                                                   | 85       |
| 25.1 Authentication                                                 | 85       |
| 26 Version Log                                                      | 86       |
|                                                                     |          |
| 27 Appendix                                                         | 87       |
| 27.1 How To Manage Risk Portfolio From Bridge Admin<br>.            |          |
| 27.1.1<br>Product Group<br>.                                        | 87<br>87 |
| 27.1.2<br>Currency Couple Group<br>.<br>27.1.3<br>Time Period Group | 87<br>88 |

#### Table of Contents Table of Contents

| 27.1.4 | Portfolio<br>.<br>88         |
|--------|------------------------------|
| 27.1.5 | Legal Entity Group<br>88     |
| 27.1.6 | Counterparts Group<br>89     |
| 27.1.7 | Execution Method Group<br>89 |
| 27.1.8 | Risk Portfolio Rule<br>89    |
| 27.1.9 | Active Rule<br>90            |
|        | 27.1.10 PFE<br>90            |

## <span id="page-6-0"></span>**1 Introduction**

This document presents the 360T Limit REST API which provides operations that allow clients to configure rules of the 360T Risk Portfolio. It contains an overview of the general workflow, as well as [screenshots](#page-91-1) that demonstrate how to configure the Risk Portfolio using the 360T Bridge Admin application.

Detailed specifications of the utilized JSON messages are also included. The API is implemented to meet the OpenAPI 2.0 standard.

The target audience of this document is the development team integrating their risk management system into the 360T Risk Portfolio.

## <span id="page-7-0"></span>**2 Communication**

All calls to the REST API should be made with a HTTP call over secure TCP socket.

The current specification allows these operations: querying, updating, deleting or adding any risk portfolio configuration parameters.

All access points in this document refer to the 360T development environment. The production environment differs only with its IP address. When acceptance tests are completed, the steps described in the following sections should be repeated in production as well, so that the client is granted access to the resources there. 360T integration and production environments are completely separate systems, but work in the exact same way to allow for comprehensive development and testing.

### <span id="page-7-1"></span>**2.1 Secure Connection**

Connections coming via Internet should be secured by establishing a HTTPS connection to the 360T data center. Certificates will be be provided by 360T integration team to the network team of the client.

### <span id="page-7-2"></span>**2.2 Firewall Configuration**

| Environment | Connection      | URL                                | IP                 | Port | Description                                                                |
|-------------|-----------------|------------------------------------|--------------------|------|----------------------------------------------------------------------------|
| INT         | Internet(plain) | https://apigateway<br>int.360t.com | ************       | 7060 | This server can be<br>used<br>for<br>testing<br>and development<br>purpose |
| PROD        | Internet(plain) | https://apigateway.360t.com        | ************* 7060 |      | This<br>server<br>is<br>used for produc<br>tion                            |

For the connections, 360T uses below IP addresses and ports.

### <span id="page-7-3"></span>**2.3 Availability**

The 360T Production environment is available between Sunday 6 pm America/New York and Friday 5 pm America/New York. Every day during the week the environment has a maintenance window between 5 and 6 pm America/New York during which the application may not be available. The non-availablity of the 360T platform does not mean that REST API will be unreachable from the client for the entire duration of the maintenance window. If the REST API is reachable during the maintenance window, the functionality of the API will not be available.

## <span id="page-8-0"></span>**3 Security**

The communication with the Limit REST API is over a secure HTTPS connection. The clients receive a private key from 360T that identifies them and grants them access to the 360T system. When starting to use the Limit REST API, the following steps need to be considered:

- 1. Client's firewall should be opened for outgoing calls to the IP addresses defined in Section 2.2.
- 2. 360T to provide a dedicated customer private key along with server certificates.
- 3. Client to add the private key in their application's keystore and server certificate in their truststore. This will allow client to validate 360T server and be authenticated to use the Limit REST API resources.

## <span id="page-9-0"></span>**4 Interacting With The Api**

### <span id="page-9-1"></span>**4.1 Status Codes**

- 200 OK Successful request
- 201 Created New object saved
- 400 Bad Request Returns JSON with the error message
- 401 Unauthorized Couldn't authenticate your request
- 403 Invalid scope User hasn't authorized necessary scope
- 404 Not Found No such object
- 406 Not Acceptable Not Acceptable request
- 409 Conflict Data Already Exists
- 429 Too Many Requests
- 500 Internal Server Error Something went wrong in api level
- 503 Service Unavailable The service is down for maintenance

### <span id="page-9-2"></span>**4.2 Making Requests**

As per RESTful design patterns, 360T Limit REST API implements following HTTP verbs:

- GET Read resources
- POST Create new resources
- PUT Modify existing resources
- DELETE Remove resources

When making requests, arguments can be passed as parameters or JSON with correct Content-Type header.

Using any HTTP operation on any resource requires authentication. For any unauthenticated request would therefore fail with 401 error code. For more details on authentication, please refer to section 3.

### <span id="page-9-3"></span>**4.3 CORS**

360T Limit REST API does not support cross-origin HTTP requests which is commonly referred as CORS.

### <span id="page-9-4"></span>**4.4 Swagger Specification**

Swagger Specification is an API description format for REST APIs. 360T Limit REST API Swagger Specification in JSON format can be fetched via the following request.

```
GET https://apigateway-int.360t.com:7060/limitapi/v2/
```

## <span id="page-10-0"></span>**5 REST Operations Overview**

The following table shows the summary of the REST operations that can be done.

<span id="page-10-1"></span>

| Resource            | Operation                    | Description                                                                                                  | Http<br>Method/Path                       |
|---------------------|------------------------------|--------------------------------------------------------------------------------------------------------------|-------------------------------------------|
| Risk Portfolio Rule |                              |                                                                                                              |                                           |
|                     | List                         | List all Risk Portfolio Rules                                                                                | GET /configRule                           |
|                     | Get                          | Fetch a single Risk Portfolio<br>Rule by given name                                                          | GET /configRule/{name}                    |
|                     | Add                          | Add a Risk Portfolio Rule                                                                                    | POST /configRule                          |
|                     | Update                       | Update a Risk Portfolio Rule<br>by given name                                                                | PUT /configRule/{name}                    |
|                     | Update Limit                 | Update Limit of a Risk Port<br>folio<br>Rule<br>by<br>given<br>Rule<br>name                                  | PUT /configRule/{name}/limit              |
|                     | Update Limits                | Batch<br>operation<br>to<br>update<br>Limits<br>of<br>Risk<br>Portfolio<br>Rules by given Rule names         | PUT /configRule/limits                    |
|                     | Delete                       | Delete a Risk Portfolio Rule<br>by given name                                                                | DELETE /configRule/{name}                 |
| Active Rule         |                              |                                                                                                              |                                           |
|                     | List                         | List all Active Rules                                                                                        | GET /activeRule                           |
|                     | List                         | List Exceptional Limits of<br>an<br>Active<br>Rule<br>by<br>given<br>Rule name                               | GET /activeRule/{name}/limit/all          |
|                     | Get                          | Fetch a single Active Rule<br>by given Rule name                                                             | GET /activeRule/{name}                    |
|                     | Get Limit                    | Get Limit of an Active Rule<br>by given Rule name                                                            | GET /activeRule/{name}/limit              |
|                     | Get<br>Excep<br>tional Limit | Get Exceptional Limit of an<br>Active Rule by given Rule<br>name and date (in a format<br>"yyyyMMdd")        | GET /activeRule/{name}/limit/{date}       |
|                     | Get<br>Utiliza<br>tion       | Get Utilization of an Active<br>Rule by given Rule name                                                      | GET /activeRule/{name}/utilization        |
|                     | Get<br>Utiliza<br>tion       | Get Utilization of an Ac<br>tive<br>Rule<br>by<br>given<br>Rule<br>name and date (in a format<br>"yyyyMMdd") | GET /activeRule/{name}/utilization/{date} |
|                     | Update Limit                 | Update Limit of an Active<br>Rule by given Rule name                                                         | PUT /activeRule/{name}/limit              |

|                              | Update Limits                     | Batch<br>operation<br>to<br>update<br>Limits of Active Rules by<br>given Rule names                                                       | PUT /activeRule/limits                 |
|------------------------------|-----------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|
|                              | Set<br>Excep<br>tional Limit      | Set Exceptional Limit of an<br>Active Rule by given Rule<br>name and date (in a format<br>"yyyyMMdd")                                     | PUT /activeRule/{name}/limit/{date}    |
|                              | Set<br>Excep<br>tional Limits     | Batch operation to set Ex<br>ceptional Limits of Active<br>Rules by given Rule names<br>and<br>dates<br>(in<br>a<br>format<br>"yyyyMMdd") | PUT /activeRule/limit/all              |
|                              | Delete Excep<br>tional Limits     | Delete all Exceptional Lim<br>its<br>of<br>an<br>Active<br>Rule<br>by<br>given Rule name                                                  | DELETE /activeRule/{name}/limit/all    |
|                              | Delete Excep<br>tional Limit      | Delete Exceptional Limit of<br>an<br>Active<br>Rule<br>by<br>given<br>Rule name and date (in a for<br>mat "yyyyMMdd")                     | DELETE /activeRule/{name}/limit/{date} |
| Algorithm                    |                                   |                                                                                                                                           |                                        |
|                              | List                              | List available Algorithms                                                                                                                 | GET /algo                              |
| Execution<br>Method<br>Group |                                   |                                                                                                                                           |                                        |
|                              | List                              | List all Execution Method<br>Group                                                                                                        | GET /executionMethodGroup              |
|                              | Find One                          | List<br>a<br>Execution<br>Method<br>Groupby given name                                                                                    | GET /executionMethodGroup/{name}       |
|                              | Add                               | Add<br>a<br>Execution<br>Method<br>Group                                                                                                  | POST /executionMethodGroup             |
|                              | Add<br>New<br>Execution<br>Method | Add a Currency to specific<br>Execution Method Group                                                                                      | POST /executionMethodGroup/{name}      |
|                              | Update                            | Update<br>given<br>Execution<br>Method<br>Groupby<br>given<br>name                                                                        | PUT /executionMethodGroup/{name}       |
|                              | Delete                            | Delete a Execution Method<br>Groupby given name                                                                                           | DELETE /executionMethodGroup/{name}    |
| Execution Method             |                                   |                                                                                                                                           |                                        |
|                              | List                              | List<br>available<br>Execution<br>Methods                                                                                                 | GET /executionMethod                   |
| Portfolio                    |                                   |                                                                                                                                           |                                        |
|                              | List                              | List All Portfolios                                                                                                                       | GET /portfolio                         |

|                             | Find One               | Get<br>a<br>Portfolio<br>by<br>given<br>name                        | GET /portfolio/{name}              |  |
|-----------------------------|------------------------|---------------------------------------------------------------------|------------------------------------|--|
|                             | Add                    | Add a new Portfolios                                                | POST /portfolio                    |  |
|                             | Update                 | Update a Portfolio                                                  | PUT /portfolio/{name}              |  |
|                             | Delete                 | Delete a Portfolio                                                  | DELETE /portfolio/{name}           |  |
| Product Group               |                        |                                                                     |                                    |  |
|                             | List                   | List All Product Groups                                             | GET /productGroup                  |  |
|                             | Find One               | Get<br>a<br>Product<br>Group<br>by<br>given name                    | GET /productGroup/{name}           |  |
|                             | Add                    | Add a new Product Group                                             | POST /productGroup                 |  |
|                             | Add Product            | Add a Product to specific<br>Product Group                          | POST /productGroup/{name}          |  |
|                             | Update                 | Update a Product Group                                              | PUT /productGroup/{name}           |  |
|                             | Delete                 | Delete a Product Group                                              | DELETE /productGroup/{name}        |  |
| FX Product                  |                        |                                                                     |                                    |  |
|                             | List                   | List available Products                                             | GET /product                       |  |
| Currency<br>Couple<br>Group |                        |                                                                     |                                    |  |
|                             | List                   | List<br>All<br>Currency<br>Couple<br>Groups                         | GET /currencyCoupleGroup           |  |
|                             | Find One               | Get<br>a<br>Currency<br>Couple<br>Group by given name               | GET /currencyCoupleGroup/{name}    |  |
|                             | Add                    | Add a new Currency Couple<br>Group                                  | POST /currencyCoupleGroup          |  |
|                             | Add Currency<br>Couple | Add a Currency Couple to<br>specific<br>Currency<br>Couple<br>Group | POST /currencyCoupleGroup/{name}   |  |
|                             | Update                 | Update a Currency Couple<br>Group                                   | PUT /currencyCoupleGroup/{name}    |  |
|                             | Delete                 | Delete a Currency Couple<br>Group                                   | DELETE /currencyCoupleGroup/{name} |  |
| Iso Currency                |                        |                                                                     |                                    |  |
|                             | List                   | List available ISO Curren<br>cies                                   | GET /isoCurrency                   |  |
| Time Period Group           |                        |                                                                     |                                    |  |
|                             | List                   | List All Time Period Groups                                         | GET /timePeriodGroup               |  |
|                             | Find One               | Get a Time Period Group by<br>given name                            | GET /timePeriodGroup/{name}        |  |
|                             | Add                    | Add<br>a<br>new<br>Time<br>Period<br>Group                          | POST /timePeriodGroup              |  |

| Add             |     | Add a new currency couple<br>to PFE Table           | POST /pfe                                                             |
|-----------------|-----|-----------------------------------------------------|-----------------------------------------------------------------------|
| Add<br>tenor    | PFE | Add a PFE tenor                                     | POST /pfe/tenor                                                       |
| Update          |     | Update PFE Entry                                    | PUT /pfe                                                              |
| Delete          |     | Delete<br>a<br>currency<br>couple<br>from PFE Table | DELETE<br>/pfe?baseCurrency={currency1}<br>&quoteCurrency={currency2} |
| Delete<br>tenor | PFE | Delete a PFE tenor                                  | PUT /pfe/tenor/{tenor}                                                |

## <span id="page-15-0"></span>**6 Risk Portfolio Rule**

Risk portfolio rule is set of parameters that can be defined by client to set their limits on. It includes counterpart, time period, currency pair, product, algorithm, limit and execution method. For an overview please see the [screenshot](#page-93-2) that demonstrates how to define Risk Portfolio Rule from Bridge Admin application.

### <span id="page-15-1"></span>**6.1 Risk Portfolio Rule Operations**

| Operation     | Http<br>Method | Description                                                                     | Resource<br>path         |
|---------------|----------------|---------------------------------------------------------------------------------|--------------------------|
| List          | GET            | List all Risk Portfolio Rules                                                   | /configRule              |
| Get           | GET            | Fetch a single Risk Portfolio Rule by given name                                | /configRule/{name}       |
| Add           | POST           | Add a Risk Portfolio Rule                                                       | /configRule              |
| Update        | PUT            | Update a Risk Portfolio Rule by given name                                      | /configRule/{name}       |
| Update Limit  | PUT            | Update Limit of a Risk Portfolio Rule by given<br>Rule name                     | /configRule/{name}/limit |
| Update Limits | PUT            | Batch operation to update Limits of Risk Portfolio<br>Rules by given Rule names | /configRule/limits       |
| Delete        | DELETE         | Delete a Risk Portfolio Rule by given name                                      | /configRule/{name}       |

The followings are the operations which can be done on Risk Portfolio Rule.

Please note that any change (update, delete or add) done on a risk portfolio rule will be valid after day rollover which happens every day at New York 5 PM.

### <span id="page-15-2"></span>**6.1.1 Add a new Risk Portfolio Rule**

To add a new Risk Portfolio Rule into 360T's Limit Monitor System, client's application needs to post all the required parameters of a risk portfolio rule. To see the required parameters, please see the [Risk Portfolio Rule](#page-23-1) [Model](#page-23-1) table.

Risk portfolio rule identifier (parameter 'name') is used in endpoints which is explained in details in relevant sub-sections for each operation.

Please note, parameters of a risk portfolio rule can also have null values. Sending the parameters except Algorithm with null value would mean that the value can be any of all available values (for Limit it would mean 0). Sending Algorithm with null value would mean that "NET\_DAILY\_SETTLEMENT\_LIMIT " will be used as default.

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/configRule

### Example Request

16

```
{
   "name": "RiskPortfolioRule-Test",
   "active": true,
   "algo": {
      "name": "Net Daily Settlement Limit"
   },
   "singleOrGroupDealer": {
      "dealers": [
         {
            "name": "MT.Treasurer1"
         }
      ],
      "name": "DealerGrp-Test"
   },
   "singleOrGroupCounterpart": {
      "institutionGroup": {
         "name": "CounterpartGrp-Test"
      },
      "isSingleInstitution": false
   },
   "singleOrGroupLegalEntity": {
      "singleInstitution": {
         "name": "360T.INT_LIMAPI.TEST"
      },
      "isSingleInstitution": true
   },
   "limit": 50000,
   "portfolio": {
      "name": "Portfolio-Test"
   },
   "singleOrGroupTimePeriod": {
      "timePeriodGroup": {
         "name": "TimePeriodGrp-Test"
      },
      "isSingleTimePeriod": false
   },
   "executionMethod": {
      "name": "SEP"
   }
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-16-0"></span>**6.1.2 List all Risk Portfolio Rules**

List all [Risk Portfolio Rules.](#page-23-1)

HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/configRule

#### Example Response

[

```
{
   "active": true,
   "algo": {
      "name": "Net Daily Settlement Limit"
   },
   "singleOrGroupDealer": {
         "dealers": [
            {
               "name": "MT.Treasurer1"
            }
         ],
         "name": "DealerGrp-Test"
   },
   "singleOrGroupCounterpart": {
      "institutionGroup": {
         "id": "9037612",
         "name": "RiskPortfolioRule-Test",
         "institutions": [
            {
               "id": "MT.Bank3",
               "name": "MT.Bank3"
            }
         ]
      },
      "isSingleInstitution": false
   },
   "name": "RiskPortfolioRule-Test",
   "id": "7077188",
   "singleOrGroupLegalEntity": {
      "singleInstitution": {
         "id": "360T.INT_LIMAPI.TEST",
         "name": "360T.INT_LIMAPI.TEST"
      },
      "isSingleInstitution": true
   },
   "limit": 1000,
   "portfolio": {
      "id": "2014231",
      "name": "Portfolio-Test",
      "portfolioElement": {
         "currencyCoupleGroup": {
            "singleCurrencyCouple": {
               "baseCurrency": {
                   "isoCode": "***"
               },
               "quoteCurrency": {
                   "isoCode": "***"
               }
            },
```

18

```
"isSingleCurrencyCouple": true
         },
         "productGroup": {
             "productGroup": {
                "id": "1883301",
                "name": "ProductGrp-Test",
                "products": [
                   {
                      "name": "Fx Forward"
                   },
                   {
                      "name": "Fx Spot"
                   }
                ]
             },
             "isSingleProduct": false
         }
      }
   },
   "singleOrGroupTimePeriod": {
      "timePeriodGroup": {
         "id": "5754708",
         "name": "TimePeriodGrp-Test",
         "timePeriod": {
             "from": {
                "longName": "TODAY",
                "shortName": "TD"
             },
             "to": {
                "longName": "UNLIMITED",
                "shortName": "UL"
             }
         }
      },
      "isSingleTimePeriod": false
   },
   "executionMethod": {
      "name": "SEP"
   }
}
```

### <span id="page-18-0"></span>**6.1.3 Get Portfolio Rule**

Fetch a single [Risk Portfolio Rule](#page-23-1) by name.

#### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test
```

#### Example Response

{

]

```
"active": true,
"algo": {
   "name": "Net Daily Settlement Limit"
},
"singleOrGroupDealer": {
      "dealers": [
         {
            "name": "MT.Treasurer1"
         }
      ],
      "name": "DealerGrp-Test"
},
"singleOrGroupCounterpart": {
   "institutionGroup": {
      "id": "9037612",
      "name": "CounterpartGrp-Test",
      "institutions": [
         {
            "id": "MT.Bank3",
            "name": "MT.Bank3"
         }
      ]
   },
   "isSingleInstitution": false
},
"name": "RiskPortfolioRule-Test",
"id": "7077188",
"singleOrGroupLegalEntity": {
   "singleInstitution": {
      "id": "360T.INT_LIMAPI.TEST",
      "name": "360T.INT_LIMAPI.TEST"
   },
   "isSingleInstitution": true
},
"limit": 1000,
"portfolio": {
   "id": "2014231",
   "name": "Portfolio-Test",
   "portfolioElement": {
      "currencyCoupleGroup": {
         "singleCurrencyCouple": {
            "baseCurrency": {
               "isoCode": "***"
            },
            "quoteCurrency": {
               "isoCode": "***"
            }
         },
         "isSingleCurrencyCouple": true
      },
      "productGroup": {
         "productGroup": {
            "id": "1883301",
            "name": "ProductGrp-Test",
            "products": [
```

```
{
                   "name": "Fx Forward"
                },
                {
                   "name": "Fx Spot"
                }
             ]
         },
         "isSingleProduct": false
      }
   }
},
"singleOrGroupTimePeriod": {
   "timePeriodGroup": {
      "id": "5754708",
      "name": "TimePeriodGrp-Test",
      "timePeriod": {
         "from": {
             "longName": "TODAY",
             "shortName": "TD"
         },
         "to": {
             "longName": "UNLIMITED",
             "shortName": "UL"
         }
      }
   },
   "isSingleTimePeriod": false
},
"executionMethod": {
   "name": "SEP"
}
```

### <span id="page-20-0"></span>**6.1.4 Update Risk Portfolio Rule**

Update [Risk Portfolio Rule](#page-23-1) by name.

#### HTTP Request

}

{

```
PUT
   https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test
```

#### Example Request

```
"active": true,
"algo": {
   "name": "Net Daily Settlement Limit"
},
"singleOrGroupDealer": {
      "dealers": [
         {
            "name": "MT.Treasurer1"
```

```
}
      ],
      "name": "DealerGrp-Test"
},
"singleOrGroupLegalEntity": {
   "singleInstitution": {
      "name": "360T.INT_LIMAPI.TEST"
   },
   "isSingleInstitution": true
},
"limit": 12000,
"portfolio": {
   "name": "Portfolio-Test"
},
"singleOrGroupTimePeriod": {
   "timePeriodGroup": {
      "name": "TimePeriodGrp-Test"
   },
   "isSingleTimePeriod": false
},
"executionMethod": {
   "name": "SEP"
}
```

#### Example Response

}

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-21-0"></span>**6.1.4.1 Partial Update**

1. Update only [Risk Portfolio Rule'](#page-23-1)s limit by name. This allows clients to update only limit of the rule.

#### HTTP Request

```
PUT
   https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test
```

Example Request

```
{
   "limit":1200000000
}
```

#### Example Response

{

}

```
"code": 200,
"message": "Response"
```

#### 2. Deactivate [Risk Portfolio Rule](#page-23-1) by name.

This allows client to activate or deactivate the rule.

#### HTTP Request

```
PUT
   https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test
```

#### Example Request

```
{
   "active": false
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-22-0"></span>**6.1.5 Update Limit value of a Config Rule**

Update [Limit'](#page-25-0)s value by [Risk Portfolio Rule'](#page-23-1)s name. HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test/limit

#### Example Request

{

}

```
"value": 11111
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-22-1"></span>**6.1.6 Update Limits of a Config Rule**

Batch operation to update [Limits](#page-25-1) by [Risk Portfolio Rule'](#page-23-1) names. HTTP Request

PUT

```
https://apigateway-int.360t.com:7060/limitapi/v2/configRule/limits
```

#### Example Request

```
[
   {
      "ruleName": "RiskPortfolioRule-Test",
      "limit": 11111
   },
   {
      "ruleName": "RiskPortfolioRule-Test",
      "limit": 22222
   },
   {
      "ruleName": "RiskPortfolioRule-Test-2",
      "limit": 33333
   }
]
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-23-0"></span>**6.1.7 Delete a Risk Portfolio Rule**

Delete [Risk Portfolio Rule](#page-23-1) by name.

#### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test
```

#### Example Response

{

}

```
"code": 200,
"message": "Response"
```

# <span id="page-23-1"></span>**6.2 Risk Portfolio Rule Model**

| Field<br>Name | Type   | Description                            | Example |
|---------------|--------|----------------------------------------|---------|
| id            | String | Unique and unmodifiable Id of Risk     | 7077188 |
|               |        | Portfolio Rule.<br>360T generates this |         |
|               |        | Id once a new Risk Portfolio Rule is   |         |
|               |        | added.                                 |         |

| name                                | String                                      | Unique Name of Risk Portfolio Rule.<br>This field is used in Risk Portfolio<br>Rule's endpoints. This field is required<br>when adding a new Risk Portfolio Rule.<br>The value of this field must not be mod<br>ified.<br>This field will default to 'id'<br>parameter's value if no user value pro<br>vided originally. | RiskPortfolioRule<br>Test |
|-------------------------------------|---------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|
| algo                                | Algorithm Model                             | Any<br>Algorithm<br>which<br>is<br>fetched<br>from<br>Get<br>Available<br>Algorithms<br>can<br>be<br>used<br>to<br>define<br>Risk<br>Port<br>folio<br>Rule.<br>If<br>not<br>provided,<br>NET_DAILY_SETTLEMENT_LIMIT<br>will be used as default                                                                           |                           |
| singleOrGroupDealer                 | Single<br>or<br>Group<br>Dealer Model       | Any Dealer Group which is fetched<br>from List All Dealer Groups can be<br>used.<br>If not provided, 'any' will be<br>used as default                                                                                                                                                                                    |                           |
| singleOrGroupCounterpart            | Single<br>or<br>Group<br>Counterpart Model  | Any<br>Counterpart<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Counterpart<br>Groups can be used.<br>If not provided,<br>'any' will be used as default                                                                                                                                                  |                           |
| singleOrGroupLegalEntity            | Single or Group Le<br>gal Entity Model      | Any<br>Legal<br>Entity<br>Group<br>which<br>is<br>fetched from List All Legal Entities<br>Groups can be used.<br>If not provided,<br>'any' will be used as default                                                                                                                                                       |                           |
| portfolio                           | Portfolio Model                             | Any Portfolio which is fetched from<br>List All Portolios can be used.<br>If not<br>provided, 'any' will be used as default                                                                                                                                                                                              |                           |
| singleOrGroupTimePeriod             | Single<br>or<br>Group<br>Time Period Model  | Any<br>Time<br>Period<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Time<br>Period<br>Group can be used.<br>If not provided,<br>'any' will be used as default                                                                                                                                             |                           |
| singleOrGroupExecutionMethod Single | or<br>Group<br>Execution<br>Method<br>Model | Execution method refers to how the<br>trade is executed and is part of risk<br>portfolio model to allow defining lim<br>its differently based on how trade is ne<br>gotiated and executed.If not provided,<br>'any' will be used as default                                                                              |                           |
| limit                               | Long                                        | Amount that needs to be used as limit.<br>If not provided, '0' will be used as de<br>fault                                                                                                                                                                                                                               | 1000000                   |

| active | Boolean | Flag that shows if the rule is enabled.     | true |
|--------|---------|---------------------------------------------|------|
|        |         | If the rule is just added and active flag   |      |
|        |         | is true, the rule will be activated with    |      |
|        |         | day rollover and will be appeared in ac     |      |
|        |         | tive rules. If not provided, 'true' will be |      |
|        |         | used as default                             |      |

### <span id="page-25-0"></span>**6.3 Limit Value Model**

| Field<br>Name | Type | Description                           | Example |
|---------------|------|---------------------------------------|---------|
| value         | Long | Amount that needs to be used as limit | 1000000 |

### <span id="page-25-1"></span>**6.4 Limits Row Model**

| Field<br>Name | Type   | Description                                                                                                                                                                                   | Example         |
|---------------|--------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|
| ruleName      | String | Unique Name of Active Rule.<br>This<br>field is used in Active Rule's endpoints.<br>This field is required when adding a<br>new Active Rule. The value of this field<br>must not be modified. | ActiveRule-Test |
| limit         | Long   | Amount that needs to be used as excep<br>tional limit                                                                                                                                         | 1000000         |

### <span id="page-25-2"></span>**6.5 Single or Group Dealer Model**

| Field<br>Name  | Type               | Description                                                                                                                                                                            | Example |
|----------------|--------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|
| singleDealer   | Dealer Model       | Any which is fetched from List Avail<br>able Dealers can be used                                                                                                                       |         |
| dealerGroup    | Dealer Group Model | Any Dealer Group which is fetched<br>from List All Dealer Groups can be<br>used                                                                                                        |         |
| isSingleDealer | Boolean            | Flag represents if singleDealer is pro<br>vided.<br>If isSingleDealer is true, then<br>singleDealer must be provided. If isS<br>ingleDealer is false, dealerGroup must<br>be provided. |         |

### <span id="page-25-3"></span>**6.6 Single or Group Counterpart Model**

| Field<br>Name     | Type              | Description                             | Example |
|-------------------|-------------------|-----------------------------------------|---------|
| singleInstitution | Counterpart Model | Any Counterpart which is fetched from   |         |
|                   |                   | List Available Counterparts can be used |         |

| institutionGroup    | Counterpart<br>Group<br>Model | Any<br>Counterpart<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Counterpart<br>Groups can be used                                                                                                        |  |
|---------------------|-------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| isSingleInstitution | Boolean                       | Flag represents if singleInstitution is<br>provided. If isSingleInstitution is true,<br>then<br>singleInstitution<br>must<br>be<br>pro<br>vided. If isSingleInstitution is false, in<br>stitutionGroup must be provided. |  |

### <span id="page-26-0"></span>**6.7 Single or Group Legal Entity Model**

| Field<br>Name       | Type                              | Description                                                                                                                                                                                                              | Example |
|---------------------|-----------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|
| singleInstitution   | Legal Entity Model                | Any Legal Entity which is fetched from<br>List Available Legal Entities can be<br>used                                                                                                                                   |         |
| institutionGroup    | Legal<br>Entity<br>Group<br>Model | Any<br>Legal<br>Entity<br>Group<br>which<br>is<br>fetched from List All Legal Entities<br>Groups can be used                                                                                                             |         |
| isSingleInstitution | Boolean                           | Flag represents if singleInstitution is<br>provided. If isSingleInstitution is true,<br>then<br>singleInstitution<br>must<br>be<br>pro<br>vided. If isSingleInstitution is false, in<br>stitutionGroup must be provided. |         |

### <span id="page-26-1"></span>**6.8 Single or Group Time Period Model**

| Field<br>Name      | Type                             | Description                                                                                                                                                                                                                 | Example |
|--------------------|----------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|
| singleTimePeriod   | Time Period Model                | Please see more details in Time Period<br>Model                                                                                                                                                                             |         |
| timePeriodGroup    | Time<br>Period<br>Group<br>Model | Any<br>Time<br>Period<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Time<br>Period<br>Group can be used                                                                                                      |         |
| isSingleTimePeriod | Boolean                          | Flag represents if singleTimePeriod is<br>provided.<br>If<br>isSingleTimePeriod<br>is<br>true, then singleTimePeriod must be<br>provided.<br>If<br>isSingleTimePeriod<br>is<br>false, timePeriodGroup must be pro<br>vided. | false   |

### <span id="page-26-2"></span>**6.9 Single or Group Execution Method Model**

| Field<br>Name | Type | Description | Example |
|---------------|------|-------------|---------|
|---------------|------|-------------|---------|

![](_page_27_Picture_0.jpeg)

| executionMethod       | ExecutionMethod<br>Model          | Any<br>Execution<br>Method<br>which<br>is<br>fetched from Get Available Execution<br>Methods can be used                                                                                                                                             |  |
|-----------------------|-----------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| executionMethodGroup  | Execution<br>Method<br>GroupModel | Any Execution Method Group which<br>is<br>fetched<br>from<br>List<br>All<br>Execution<br>Method Group can be used                                                                                                                                    |  |
| singleExecutionMethod | Boolean                           | Flag<br>represents<br>if<br>singleExecution<br>Method is provided.<br>If singleExecu<br>tionMethod is true, then singleExecu<br>tionMethod must be provided.<br>If sin<br>gleExecutionMethod is false,<br>execu<br>tionMethodGroup must be provided. |  |

## <span id="page-28-0"></span>**7 Active Rule**

Acive Rule operations allow clients to list the current active risk portfolio rules and their corresponding 'Utilization' amount. For an overview please see the [screenshot](#page-94-0) that demonstrates Active Rules view from Bridge Admin application.

## <span id="page-28-1"></span>**7.1 Active Rule Operations**

| Operation                        | Http<br>Method | Description                                                                                                               | resource<br>path                      |
|----------------------------------|----------------|---------------------------------------------------------------------------------------------------------------------------|---------------------------------------|
| List                             | GET            | List all Active Rules                                                                                                     | /activeRule                           |
| List                             | GET            | List Exceptional Limits of an Ac<br>tive Rule by given Rule name                                                          | /activeRule/{name}/limit/all          |
| Get                              | GET            | Fetch a single Active Rule by given<br>Rule name                                                                          | /activeRule/{name}                    |
| Get Limit                        | GET            | Get Limit of an Active Rule by<br>given Rule name                                                                         | /activeRule/{name}/limit              |
| Get<br>Exceptional<br>Limit      | GET            | Get Exceptional Limit of an Active<br>Rule by given Rule name and date<br>(in a format "yyyyMMdd")                        | /activeRule/{name}/limit/{date}       |
| Get Utilization                  | GET            | Get Utilization of an Active Rule by<br>given Rule name                                                                   | /activeRule/{name}/utilization        |
| Get Utilization                  | GET            | Get Utilization of an Active Rule by<br>given Rule name and date (in a for<br>mat "yyyyMMdd")                             | /activeRule/{name}/utilization/{date} |
| Update Limit                     | PUT            | Update Limit of an Active Rule by<br>given Rule name                                                                      | /activeRule/{name}/limit              |
| Update Limits                    | PUT            | Batch operation to update Limits of<br>Active Rules by given Rule names                                                   | /activeRule/limits                    |
| Set<br>Exceptional<br>Limit      | PUT            | Set Exceptional Limit of an Active<br>Rule by given Rule name and date<br>(in a format "yyyyMMdd")                        | /activeRule/{name}/limit/{date}       |
| Set<br>Exceptional<br>Limits     | PUT            | Batch operation to set Exceptional<br>Limits of Active Rules by given<br>Rule names and dates (in a format<br>"yyyyMMdd") | /activeRule/limit/all                 |
| Delete<br>Excep<br>tional Limits | DELETE         | Delete all Exceptional Limits of an<br>Active Rule by given Rule name                                                     | /activeRule/{name}/limit/all          |

The followings are the operations which can be done on Active Rules.

| Delete       | Excep | DELETE | Delete Exceptional Limit of an Ac | /activeRule/{name}/limit/{date} |
|--------------|-------|--------|-----------------------------------|---------------------------------|
| tional Limit |       |        | tive Rule by given Rule name and  |                                 |
|              |       |        | date (in a format "yyyyMMdd")     |                                 |

### <span id="page-29-0"></span>**7.1.1 List all Active Rules**

List all [Active Rules.](#page-35-1) HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/activeRule

### Example Response

[

```
{
   "algo": {
      "name": "Net Daily Settlement Limit"
   },
   "name": "ActiveRule-Test",
   "id": "5268107",
   "singleOrGroupDealer": {
         "dealers": [
            {
                "name": "MT.Treasurer1"
            }
         ],
         "name": "DealerGrp-Test"
   },
   "singleOrGroupLegalEntity": {
      "singleInstitution": {
         "id": "360T.INT_LIMAPI.TEST",
         "name": "360T.INT_LIMAPI.TEST"
      },
      "isSingleInstitution": true
   },
   "limit": 11111,
   "singleOrGroupTimePeriod": {
      "singleTimePeriod": {
         "from": {
            "longName": "SPOT",
            "shortName": "SP"
         },
         "to": {
            "longName": "1 WEEK",
            "shortName": "1W"
         }
      },
      "isSingleTimePeriod": true
   },
   "executionMethod": {
      "name": "SEP"
   }
   "utilization": 0
```

]

}

### <span id="page-30-0"></span>**7.1.2 List all Exceptional Limits of an Active Rule**

List all [Exceptional Limits](#page-36-0) by [Active Rule'](#page-35-1)s name. HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/all

### Example Response

```
[
   {
      "date": "20210520",
      "limit": 11111
   },
   {
      "date": "20210521",
      "limit": 22222
   }
]
```

### <span id="page-30-1"></span>**7.1.3 Get Active Rule**

Fetch a single [Active Rule](#page-35-1) by name. HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test
```

```
{
   "algo": {
      "name": "Net Daily Settlement Limit"
   },
   "name": "ActiveRule-Test",
   "id": "5268107",
   "singleOrGroupDealer": {
         "dealers": [
            {
               "name": "MT.Treasurer1"
            }
         ],
         "name": "DealerGrp-Test"
   },
   "singleOrGroupLegalEntity": {
      "singleInstitution": {
         "id": "360T.INT_LIMAPI.TEST",
```

```
"name": "360T.INT_LIMAPI.TEST"
   },
   "isSingleInstitution": true
},
"limit": 11111,
"singleOrGroupTimePeriod": {
   "singleTimePeriod": {
      "from": {
         "longName": "SPOT",
         "shortName": "SP"
      },
      "to": {
         "longName": "1 WEEK",
         "shortName": "1W"
      }
   },
   "isSingleTimePeriod": true
},
"executionMethod": {
   "name": "SEP"
}
"utilization": 0
```

### <span id="page-31-0"></span>**7.1.4 Get Limit value of an Active Rule**

Get [Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name. HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit
```

#### Example Response

}

{

}

{

}

```
"value": 11111
```

#### <span id="page-31-1"></span>**7.1.5 Get Exceptional Limit value of an Active Rule for a given date**

Get [Exceptional Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name and date in a format "yyyyMMdd". HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520
```

```
"value": 11111
```

### <span id="page-32-0"></span>**7.1.6 Get Utilization value of an Active Rule**

Get [Utilizaton'](#page-37-0)s value by [Active Rule'](#page-35-1)s name. HTTP Request

```
GET
```

{

}

https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/utilization

### Example Response

```
"value": 11111
```

### <span id="page-32-1"></span>**7.1.7 Get Utilization value of an Active Rule for a given date**

Get [Utilizaton'](#page-37-0)s value by [Active Rule'](#page-35-1)s name and date in a format "yyyyMMdd". HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/utilization/20210520
```

#### Example Response

```
{
   "value": 11111
}
```

### <span id="page-32-2"></span>**7.1.8 Update Limit value of an Active Rule**

Update [Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name.

IMPORTANT: Please note that the change done on active limit is valid until the day rollover at New York 5 PM. After day rollover, the generic limit defined on Risk Portfolio rule will be valid. HTTP Request

PUT

https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit

### Example Request

```
"value": 11111
```

}

{

{

```
"code": 200,
"message": "Response"
```

}

### <span id="page-33-0"></span>**7.1.9 Update Limits of an Active Rule**

Batch operation to update [Limits](#page-25-1) by [Active Rules'](#page-35-1) names. HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limits

### Example Request

```
[
   {
      "ruleName": "ActiveRule-Test",
      "limit": 11111
   },
   {
      "ruleName": "ActiveRule-Test",
      "limit": 22222
   },
   {
      "ruleName": "ActiveRule-Test-2",
      "limit": 33333
   }
]
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-33-1"></span>**7.1.10 Set Exceptional Limit value of an Active Rule**

Set [Exceptional Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name and date in a format "yyyyMMdd". HTTP Request

```
PUT
```

```
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520
```

#### Example Request

{

}

```
"value": 11111
```

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-34-0"></span>**7.1.11 Set Exceptional Limits of an Active Rule**

Batch operation to set [Exceptional Limits](#page-36-1) by [Active Rules'](#page-35-1) names and dates in a format "yyyyMMdd". HTTP Request

PUT

```
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limit/all
```

### Example Request

```
[
   {
      "ruleName": "ActiveRule-Test",
      "date": "20210520",
      "limit": 11111
   },
   {
      "ruleName": "ActiveRule-Test",
      "date": "20210521",
      "limit": 22222
   },
   {
      "ruleName": "ActiveRule-Test-2",
      "date": "20210520",
      "limit": 33333
   }
]
```

### Example Response

{ "code": 200, "message": "Response" }

### <span id="page-34-1"></span>**7.1.12 Delete all Exceptional Limits of an Active Rule**

Delete all [Exceptional Limits](#page-36-0) by [Active Rule'](#page-35-1)s name. HTTP Request

DELETE https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/all

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-35-0"></span>**7.1.13 Delete Exceptional Limit of an Active Rule for a given date**

Delete [Exceptional Limit](#page-25-0) by [Active Rule'](#page-35-1)s name and date in a format "yyyyMMdd". HTTP Request

DELETE

```
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-35-1"></span>**7.2 Active Rule Model**

| Field<br>Name            | Type                                  | Description                                                                                                                                                                                        | Example                                     |
|--------------------------|---------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------|
| id                       | String                                | Unique and unmodifiable Id of Ac<br>tive Rule.<br>360T generates this Id<br>once an existing Risk Portfolio Rule<br>is rolled over                                                                 | fxpgp3-c0a815a5-<br>k5zby2j6-l7_act20200210 |
| name                     | String                                | Unique Name of Active Rule. This<br>field is used in Active Rule's end<br>points. This field is required when<br>adding a new Active Rule.<br>The<br>value of this field must not be mod<br>ified. | ActiveRule-Test                             |
| algo                     | Algorithm Model                       | Algorithm which is defined in an<br>existing Risk Portfolio Rule                                                                                                                                   |                                             |
| singleOrGroupDealer      | Single or Group Dealer<br>Model       | Dealer Group or Dealer which is<br>defined in an existing Risk Portfo<br>lio Rule                                                                                                                  |                                             |
| singleOrGroupCounterpart | Single or Group Coun<br>terpart Model | Counterpart Group or Counterpart<br>which is defined in an existing Risk<br>Portfolio Rule                                                                                                         |                                             |

![](_page_36_Picture_0.jpeg)

| singleOrGroupLegalEntity                           | Single or Group Legal<br>Entity Model | Legal Entity Group or Legal Entity<br>which is defined in an existing Risk<br>Portfolio Rule                                                                                                                                                  |         |
|----------------------------------------------------|---------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|
| portfolio                                          | Portfolio Model                       | Portfolio which is defined in an ex<br>isting Risk Portfolio Rule                                                                                                                                                                             |         |
| singleOrGroupTimePeriod                            | Time<br>Period<br>Group<br>Model      | Time Period Group or Time Period<br>which is defined in an existing Risk<br>Portfolio Rule                                                                                                                                                    |         |
| singleOrGroupExecutionMethod Single or Group Execu | tion Method Model                     | Execution method refers to how the<br>trade is executed and is part of<br>risk portfolio model to allow defin<br>ing limits differently based on how<br>trade is negotiated and executed.If<br>not provided, 'any' will be used as<br>default |         |
| limit                                              | Long                                  | Amount that needs to be used as<br>limit                                                                                                                                                                                                      | 1000000 |
| utilization                                        | Long                                  | Amount that is used so far                                                                                                                                                                                                                    | 10000   |

### <span id="page-36-0"></span>**7.3 Exceptional Limit Model**

| Field<br>Name | Type   | Description                                                                 | Example  |
|---------------|--------|-----------------------------------------------------------------------------|----------|
| date          | String | Date<br>in<br>a<br>format<br>"yyyyMMdd"<br>to<br>which the limit is applied | 20210520 |
| limit         | Long   | Amount that needs to be used as excep<br>tional limit                       | 1000000  |

### <span id="page-36-1"></span>**7.4 Exceptional Limit Row Model**

| Field<br>Name | Type   | Description                                                                                                                                                                                   | Example         |
|---------------|--------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|
| ruleName      | String | Unique Name of Active Rule.<br>This<br>field is used in Active Rule's endpoints.<br>This field is required when adding a<br>new Active Rule. The value of this field<br>must not be modified. | ActiveRule-Test |
| date          | String | Date<br>in<br>a<br>format<br>"yyyyMMdd"<br>to<br>which the limit is applied                                                                                                                   | 20210520        |
| limit         | Long   | Amount that needs to be used as excep<br>tional limit                                                                                                                                         | 1000000         |

### <span id="page-37-0"></span>**7.5 Utilizaton Value Model**

| Field<br>Name | Type | Description                | Example |
|---------------|------|----------------------------|---------|
| value         | Long | Amount that is used so far | 1000000 |

## <span id="page-38-0"></span>**8 Portfolio**

Portfolio consists of [Product Group](#page-50-1) and Currency Couple Group. For an overview please see the [screenshot](#page-92-1) that demonstrates how to define Portfolio from Bridge Admin application.

### <span id="page-38-1"></span>**8.1 Portfolio Operations**

The followings are the operations which can be done on Portfolio.

| Operation | Http<br>Method | Description                                                     | resource<br>path  |
|-----------|----------------|-----------------------------------------------------------------|-------------------|
| List      | GET            | List all Portfolios                                             | /portfolio        |
| Find One  | GET            | List a Portfolio by given name                                  | /portfolio/{name} |
| Add       | POST           | Add a Portfolio                                                 | /portfolio        |
| Update    | PUT            | Update given Portfolio. Update works<br>according to given name | /portfolio        |
| Delete    | DELETE         | Delete a Portfolio by given name                                | /portfolio/{name} |

### <span id="page-38-2"></span>**8.1.1 Add a Portfolio**

Add a new [Portfolio](#page-42-0)

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/portfolio

### Example Request

```
{
   "name": "Portfolio-Test",
   "portfolioElement": {
      "currencyCoupleGroup": {
         "singleCurrencyCouple": {
            "baseCurrency": {
               "isoCode": "EUR"
            },
            "quoteCurrency": {
               "isoCode": "***"
            }
         },
         "isSingleCurrencyCouple": true
      },
      "productGroup": {
         "productGroup": {
            "name": "ProductGrp-Test",
            "products": [
```

### 8.1. Portfolio Operations Portfolio

```
{
                    "name": "Fx Forward"
                 },
                 {
                    "name": "Fx Spot"
                 }
             ]
          },
          "isSingleProduct": false
      }
   }
}
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-39-0"></span>**8.1.2 List All Portolios**

List all [Portfolios.](#page-42-0)

### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/portfolio

### Example Response

[

```
{
   "id": "2014231",
   "name": "Portfolio-Test",
   "portfolioElement": {
      "singleOrGroupCurrencyCouple": {
         "singleCurrencyCouple": {
            "baseCurrency": {
                "isoCode": "***"
            },
            "quoteCurrency": {
                "isoCode": "***"
            }
         },
         "isSingleCurrencyCouple": true
      },
      "singleOrGroupProduct": {
         "productGroup": {
            "id": "1883301",
            "name": "ProductGrp-Test",
            "products": [
                {
                   "name": "Fx Forward"
                },
```

### 8.1. Portfolio Operations Portfolio

```
{
                         "name": "Fx Spot"
                     }
                 ]
              },
              "isSingleProduct": false
          }
       }
   }
]
```

### <span id="page-40-0"></span>**8.1.3 Update a Portfolio**

Update existing [Portfolio.](#page-42-0)

### HTTP Request

```
PUT https://apigateway-int.360t.com:7060/limitapi/v2/portfolio/Portfolio-Test
```

### Example Request

```
{
   "name": "Portfolio-Test",
   "portfolioElement": {
      "currencyCoupleGroup": {
         "singleCurrencyCouple": {
             "baseCurrency": {
                "isoCode": "***"
             },
             "quoteCurrency": {
                "isoCode": "USD"
             }
         },
         "isSingleCurrencyCouple": true
      },
      "productGroup": {
         "productGroup": {
             "name": "ProductGrp-Test",
             "products": [
                {
                   "name": "Fx Forward"
                },
                {
                   "name": "Fx Spot"
                }
             ]
         },
         "isSingleProduct": false
      }
   }
}
```

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-41-0"></span>**8.1.4 Get a Portfolio**

Get a [Portfolio](#page-42-0) by name.

#### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/portfolio/Portfolio-Test
```

#### Example Response

```
{
   "id": "2014231",
   "name": "Portfolio-Test",
   "portfolioElement": {
      "currencyCoupleGroup": {
         "singleCurrencyCouple": {
             "baseCurrency": {
                "isoCode": "***"
             },
             "quoteCurrency": {
                "isoCode": "***"
             }
         },
         "isSingleCurrencyCouple": true
      },
      "productGroup": {
         "productGroup": {
             "id": "1883301",
             "name": "ProductGrp-Test",
             "products": [
                {
                   "name": "Fx Forward"
                },
                {
                   "name": "Fx Spot"
                }
             ]
         },
         "isSingleProduct": false
      }
   }
}
```

### <span id="page-41-1"></span>**8.1.5 Delete a Portfolio**

Remove a Portfolio by name.

### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/portfolio/Portfolio-Test
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-42-0"></span>**8.2 Portfolio Model**

| Field<br>Name    | Type                          | Description                                                                                                                                                                                                                                                                 | Example     |
|------------------|-------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|
| id               | String                        | Unique and unmodifiable Id of Portfo<br>lio. 360T generates this Id once a new<br>Portfolio is added.                                                                                                                                                                       | 2014231     |
| name             | String                        | The name must be unique.<br>It is bet<br>ter to define logical names to easily<br>recognize which portfolio elements this<br>group has. This field is required when<br>adding a new or updating the existing<br>Portfolio. The value of this field must<br>not be modified. | Portfolio 1 |
| portfolioElement | Portfolio<br>Element<br>Model | List of Portfolio Element can be speci<br>fied                                                                                                                                                                                                                              |             |

### <span id="page-42-1"></span>**8.3 Portfolio Element Model**

| Field<br>Name               | Type                                      | Description                                                       | Example |
|-----------------------------|-------------------------------------------|-------------------------------------------------------------------|---------|
| singleOrGroupProduct        | Single Or Group Product Model             | A<br>single<br>Product<br>or<br>Product<br>Group can be specified |         |
| singleOrGroupCurrencyCouple | Single Or Group Currency Cou<br>ple Model | A Currency Couple or Currency<br>Couple Group can be specified    |         |

### <span id="page-42-2"></span>**8.4 Single Or Group Product Model**

| Field<br>Name | Type                      | Description                                                                       | Example |
|---------------|---------------------------|-----------------------------------------------------------------------------------|---------|
| singleProduct | Product Model             | Any Product which is fetched from Get<br>Available Products can be used           |         |
| productGroup  | Product<br>Group<br>Model | Any Product Group which is fetched<br>from List All Product Groups can be<br>used |         |

| isSingleProduct | Boolean | Flag represents if singleProduct is pro<br>vided. If isSingleProduct is true, then<br>singleProduct must be provided.<br>If |  |
|-----------------|---------|-----------------------------------------------------------------------------------------------------------------------------|--|
|                 |         | isSingleProduct is false, productGroup                                                                                      |  |
|                 |         | must be provided.                                                                                                           |  |

### <span id="page-43-0"></span>**8.5 Currency Couple Model**

| Field<br>Name | Type                            | Description                                                                                            | Example |
|---------------|---------------------------------|--------------------------------------------------------------------------------------------------------|---------|
| baseCurrency  | IsoCurrency Model               | Base currency represents currenyc1 in<br>Trade.<br>E.g EUR/USD, EUR is base<br>currency in this case   |         |
|               | quoteCurrency IsoCurrency Model | Quote currency represents currency2 in<br>Trade.<br>E.g EUR/USD, USD is quote<br>currency in this case |         |

### <span id="page-43-1"></span>**8.6 Single Or Group Currency Couple Model**

| Field<br>Name          | Type                              | Description                                                                                                                                                                                                                            | Example |
|------------------------|-----------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|
| singleCurrencyCouple   | Currency<br>Couple<br>Model       | Please see Currency Couple Model<br>for more details                                                                                                                                                                                   |         |
| currencyCoupleGroup    | Currency<br>Couple<br>Group Model | Any Currency Couple Group which<br>is fetched from List All Currency<br>Couple Groups can be used                                                                                                                                      |         |
| isSingleCurrencyCouple | Boolean                           | Flag represents if singleCurrency<br>Couple is provided. If isSingleCur<br>rencyCouple is true,<br>then single<br>CurrencyCouple must be provided.<br>If isSingleCurrencyCouple is false,<br>currencyCoupleGroup must be pro<br>vided. |         |

## <span id="page-44-0"></span>**9 Algorithm**

Algorithm refers to the parameter of any risk portfolio rule which would determine how to calculate the utilization amount of portfolio. Only GET operation can be used and it returns available Algorithms.

### <span id="page-44-1"></span>**9.1 Algorithm Operations**

### <span id="page-44-2"></span>**9.1.1 Get Available Algorithms**

List available [Algorithms](#page-44-3).

### HTTP Request

[

]

```
GET https://apigateway-int.360t.com:7060/limitapi/v2/algo
```

### Example Response

```
{
 "name": "Net Daily Settlement Limit"
},
{
 "name": "Gross Daily Settlement Limit"
},
{
 "name": "Aggregate Gross Settlement Limit"
},
{
 "name": "Aggregate Net Settlement Limit"
},
{
 "name": "Potential Future Exposure"
},
{
 "name": "Externally Managed Limit"
},
{
 "name": "Daily Net Trading Limit"
}
```

### <span id="page-44-3"></span>**9.2 Algorithm Model**

| Field<br>Name | Type   | Description       | Example                   |
|---------------|--------|-------------------|---------------------------|
| name          | String | Name of Algorithm | POTENTIAL_FUTURE_EXPOSURE |

## <span id="page-45-0"></span>**10 Execution Method**

Execution method refers to how the trade is executed and is part of risk portfolio model to allow defining limits differently based on how trade is negotiated and executed.

### <span id="page-45-1"></span>**10.1 Execution Method Operation**

### <span id="page-45-2"></span>**10.1.1 Get Available Execution Methods**

List available [Execution Methods](#page-45-3) in 360T system.

### HTTP Request

```
GET https://apigateway-int.360t.com:7060/limitapi/v2/executionMethod
```

### Example Response

```
[
   {
      "name": "SEP"
   },
   {
      "name": "RFS"
   },
   {
      "name": "OMT"
   },
   {
      "name": "MidMatch"
   },
   {
      "name": "HST Engine"
   },
   {
      "name": "HST OrderBook"
   },
   {
      "name": "GTX CLOB"
   }
]
```

### <span id="page-45-3"></span>**10.2 ExecutionMethod Model**

| Field<br>Name | Type   | Description              | Example |
|---------------|--------|--------------------------|---------|
| name          | String | Name of execution method | SEP     |

## <span id="page-46-0"></span>**11 Execution Method Group**

Execution Method Group is used to group execution methods. For an overview please see the [screenshot](#page-93-1) that demonstrates how to define Execution Method Groups from Bridge Admin application.

### <span id="page-46-1"></span>**11.1 Execution Method GroupOperations**

| Operation                         | Http<br>Method | Description                                                         | resource<br>path                    |
|-----------------------------------|----------------|---------------------------------------------------------------------|-------------------------------------|
| List                              | GET            | List<br>all<br>Execution<br>Method Group                            | GET /executionMethodGroup           |
| Find One                          | GET            | List<br>a<br>Execution<br>Method<br>Groupby<br>given name           | GET /executionMethodGroup/{name}    |
| Add                               | POST           | Add<br>a<br>Execution<br>Method Group                               | POST /executionMethodGroup          |
| Add<br>New<br>Execution<br>Method | POST           | Add<br>a<br>Currency<br>to<br>specific<br>Execution<br>Method Group | POST /executionMethodGroup/{name}   |
| Update                            | PUT            | Update<br>given<br>Ex<br>ecution<br>Method<br>Groupby given name    | PUT /executionMethodGroup/{name}    |
| Delete                            | DELETE         | Delete<br>a<br>Execution<br>Method<br>Groupby<br>given name         | DELETE /executionMethodGroup/{name} |

The followings are the operations which can be done on Execution Method Group.

### <span id="page-46-2"></span>**11.1.1 Add a Execution Method Group**

Add a new [Execution Method Group](#page-49-1) to 360T system.

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup

### Example Response

{

```
"executionMethods": [
   {
      "name": "RFS"
   },
   {
      "name": "OMT"
```

} ], "name": "Grp-Test"

#### Example Response

}

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-47-0"></span>**11.1.2 List All Execution Method Group**

List all [Execution Method Groups.](#page-49-1)

### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup

### Example Response

```
[
   {
      "executionMethods": [
          {
             "name": "RFS"
          },
          {
             "name": "OMT"
          }
      ],
      "id": "7227449",
      "name": "Grp-Test"
   },
   {
      "executionMethods": [],
      "id": "3490945",
      "name": "ExecutionMethodGrp-Test"
   }
]
```

### <span id="page-47-1"></span>**11.1.3 Update a Execution Method Group**

Update given [Execution Method Group.](#page-49-1)

### HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test

Example Request

```
{
      "executionMethods": [
          {
             "name": "RFS"
          },
          {
             "name": "OMT"
          }
      ],
      "name": "Grp-Test"
}
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-48-0"></span>**11.1.4 Add Execution Method to Execution Method Group**

Add new [Execution Method](#page-45-3) to existing Execution Method Group.

### HTTP Request

```
POST
https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test
```

#### Example Request

```
{
 "name": "SEP"
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-48-1"></span>**11.1.5 Get a Execution Method Group**

Get a [Execution Method Group](#page-49-1) by name.

#### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test
```

```
{
   "executionMethods": [
      {
          "name": "RFS"
      },
      {
          "name": "OMT"
      }
   ],
   "id": "7227449",
   "name": "Grp-Test"
```

}

### <span id="page-49-0"></span>**11.1.6 Delete a Execution Method Group**

Delete Execution Method Group by name.

### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-49-1"></span>**11.2 Execution Method GroupModel**

| Field<br>Name    | Type                                                    | Description                                                                                                                                                                                                                                                                           | Example                  |
|------------------|---------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------|
| id               | String                                                  | Unique and unmodifiable Id of Execu<br>tion Method Group.<br>360T generates<br>this Id once a new Execution Method<br>Group is added.                                                                                                                                                 | lov5ea-7f011-k8ag9hay-18 |
| name             | String                                                  | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which Execution Methodss this<br>group has. This field is required when<br>adding a new or updating the existing<br>Execution Method Group. The value of<br>this field must not be modified. | Streaming                |
| executionMethods | List <executionmethod<br>Model&gt;</executionmethod<br> | List of Execution Methods which are<br>fetched from Get Available Execution<br>Methods can be used                                                                                                                                                                                    |                          |

## <span id="page-50-0"></span>**12 Product Group**

Product Group consists of several Fx Product. Product Group is used to define [Portfolio.](#page-50-1) And Portfolio is used to define [Risk Portfolio Rule.](#page-23-1) For an overview please see the [screenshot](#page-91-2) that demonstrates how to define Product Group from Bridge Admin application.

### <span id="page-50-1"></span>**12.1 Product Group Operations**

The followings are the operations which can be done on Product Group.

| Operation | Http<br>Method | Description                                                            | resource<br>path     |
|-----------|----------------|------------------------------------------------------------------------|----------------------|
| List      | GET            | List all product groups                                                | /productGroup        |
| Find One  | GET            | List a Product Group by given name                                     | /productGroup/{name} |
| Add       | POST           | Add a Product Group                                                    | /productGroup        |
| Update    | PUT            | Update given product group.<br>Update<br>works according to given name | /productGroup/{name} |
| Delete    | DELETE         | Delete a Product Group by given name                                   | /productGroup/{name} |

### <span id="page-50-2"></span>**12.1.1 Add a Product Group**

Add a new [Product Group](#page-53-1) to 360T Risk Portfolio System.

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/productGroup

### Example Request

```
[
   {
      "name": "ProductGrp-Test",
      "products": [
          {
             "name": "Fx Forward"
          },
          {
             "name": "NDF"
          }
      ]
   }
]
```

### Example Response

{

```
"code": 200,
   "message": "Response"
}
```

### <span id="page-51-0"></span>**12.1.2 List All Product Groups**

List all [Product Groups.](#page-53-1)

#### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/productGroup

#### Example Response

```
[
   {
      "id": "1883301",
      "name": "ProductGrp-Test",
      "products": [
          {
             "name": "Fx Forward"
          },
          {
             "name": "Fx Spot"
          }
      ]
   }
]
```

#### <span id="page-51-1"></span>**12.1.3 Update a Product Group**

Update given [Product Group.](#page-53-1)

#### HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test

### Example Request

```
{
   "name": "ProductGrp-Test",
   "products": [
      {
          "name": "Fx Forward"
      },
      {
          "name": "Fx Swap"
      }
   ]
}
```

#### Example Response

#### 52

©360 Treasury Systems AG, 2019, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-52-0"></span>**12.1.4 Add a Product to Product Group**

Add a new [Product](#page-54-3) to existing Product Group.

#### HTTP Request

```
POST
https://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test
```

#### Example Request

```
{
 "name": "Block-Trade"
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-52-1"></span>**12.1.5 Get a Product Group**

Get a [Product Group](#page-53-1) by name.

#### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test
```

```
{
   "id": "1883301",
   "name": "ProductGrp-Test",
   "products": [
      {
          "name": "Fx Forward"
      },
      {
          "name": "Fx Spot"
      }
   ]
}
```

### <span id="page-53-0"></span>**12.1.6 Delete a Product Group**

Delete Product Group by name.

### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-53-1"></span>**12.2 Product Group Model**

| Field<br>Name | Type                                    | Description                                                                                                                                                                                                                                                        | Example       |
|---------------|-----------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------|
| id            | String                                  | Unique and unmodifiable Id of Product<br>Group.<br>360T generates this Id once a<br>new Product Group is added.                                                                                                                                                    | 1883301       |
| name          | String                                  | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which products this group has.<br>This field is required when adding a<br>new or updating the existing Product<br>Group. The value of this field must not<br>be modified. | Main Products |
| products      | List <product<br>Model&gt;</product<br> | A Product or List of Product which are<br>fetched from Get Available Products<br>can be used                                                                                                                                                                       |               |

## <span id="page-54-0"></span>**13 Product**

This chapter explains the Product model and operations. Product refers to the product type of any trade intention such as FX Spot, FX Swap etc. and is part of 'Portfolio' paramater of a risk portfolio rule. Only GET operation can be used which would return the all available products.

### <span id="page-54-1"></span>**13.1 Product Operations**

### <span id="page-54-2"></span>**13.1.1 Get Available Products**

List all supported [Products.](#page-54-3)

### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/product

### Example Response

```
[
   {
      "name": "Fx Spot"
   },
   {
      "name": "Fx Forward"
   },
   {
      "name": "Fx Swap"
   },
   {
      "name": "NDF"
   },
   {
      "name": "NDS"
   },
   {
      "name": "Block-Trade"
   },
   {
      "name": "Fx Time Option"
   }
]
```

### <span id="page-54-3"></span>**13.2 Product Model**

| Field<br>Name | Type   | Description     | Example |
|---------------|--------|-----------------|---------|
| name          | String | Name of Product | Fx Swap |

## <span id="page-55-0"></span>**14 Currency Couple Group**

Currency Couple Group is a component of a 'Portfolio' which is a required parameter of any risk portfolio rule. A currency couple group can contain multiple currency couple. In order to add a currency couple into a group, clients can either use the single currency group that they previously defined in 360T's Limit Monitor System or add currency pairs customly by using Iso Currencies. For an overview please see the [screenshot](#page-91-3) that demonstrates how to define Currency Couple Group from Bridge Admin application.

### <span id="page-55-1"></span>**14.1 Currency Couple Group Operations**

| Operation              | Http<br>Method | Description                                                         | resource<br>path                   |
|------------------------|----------------|---------------------------------------------------------------------|------------------------------------|
| List                   | GET            | List<br>all<br>Currency<br>Couple<br>Groups                         | GET /currencyCoupleGroup           |
| Find One               | GET            | List<br>a<br>Currency<br>Couple<br>Group by given name              | GET /currencyCoupleGroup/{name}    |
| Add                    | POST           | Add<br>a<br>Currency<br>Couple<br>Group                             | POST /currencyCoupleGroup          |
| Add Currency<br>Couple | POST           | Add a Currency Couple to<br>specific<br>Currency<br>Couple<br>Group | POST /currencyCoupleGroup/{name}   |
| Update                 | PUT            | Update given Currency Cou<br>ple Group by given name                | PUT /currencyCoupleGroup/{name}    |
| Delete                 | DELETE         | Delete a Currency Couple<br>Group by given name                     | DELETE /currencyCoupleGroup/{name} |

The followings are the operations which can be done on Currency Couple Group.

### <span id="page-55-2"></span>**14.1.1 Add a Currency Couple Group**

Add a new [Currency Couple Group](#page-58-1) to 360T system.

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup

### Example Request

{

```
"currencyCouples": [
   {
      "baseCurrency": {
         "isoCode": "GBP"
      },
      "quoteCurrency": {
```

56

```
"isoCode": "CHF"
          }
      }
   ],
   "name": "CcyCoupleGrp-Test"
}
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-56-0"></span>**14.1.2 List All Currency Couple Groups**

List all [Currency Couple Groups.](#page-58-1)

### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup

### Example Response

```
[
   {
      "currencyCouples": [
          {
             "baseCurrency": {
                "isoCode": "AUD"
             },
             "quoteCurrency": {
                "isoCode": "CAD"
             }
          },
          {
             "baseCurrency": {
                "isoCode": "GBP"
             },
             "quoteCurrency": {
                "isoCode": "CHF"
             }
          }
      ],
      "id": "9214996",
      "name": "CcyCoupleGrp-Test"
   }
]
```

### <span id="page-56-1"></span>**14.1.3 Update a Currency Couple Group**

Update given [Currency Couple Group.](#page-58-1)

#### HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup/CcyCoupleGrp-Test

#### Example Request

```
{
   "currencyCouples": [
      {
         "baseCurrency": {
             "isoCode": "GBP"
         },
         "quoteCurrency": {
             "isoCode": "CHF"
         }
      }
   ],
   "name": "CcyCoupleGrp-Test"
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-57-0"></span>**14.1.4 Get a Currency Couple Group**

Get a [Currency Couple Group](#page-58-1) by name.

#### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup/CcyCoupleGrp-Test
```

```
{
   "currencyCouples": [
      {
         "baseCurrency": {
             "isoCode": "AUD"
         },
         "quoteCurrency": {
             "isoCode": "CAD"
         }
      },
      {
         "baseCurrency": {
             "isoCode": "GBP"
         },
         "quoteCurrency": {
```

```
"isoCode": "CHF"
         }
      }
   ],
   "id": "9214996",
   "name": "CcyCoupleGrp-Test"
}
```

### <span id="page-58-0"></span>**14.1.5 Delete a Currency Couple Group**

Delete Currency Couple Group by name.

### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup/CcyCoupleGrp-Test
```

Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-58-1"></span>**14.2 Currency Couple Group Model**

| Field<br>Name   | Type                                                  | Description                                                                                                                                                                                                                                                                        | Example        |
|-----------------|-------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|
| id              | String                                                | Unique and unmodifiable Id of Cur<br>rency Couple Group.<br>360T generates<br>this Id once a new Currency Couple<br>Group is added.                                                                                                                                                | 9214996        |
| name            | String                                                | The name must be unique. It is better<br>to define logical names to easily recog<br>nize which currency couples this group<br>has. This field is required when adding<br>a new or updating the existing Currency<br>Couple Group. The value of this field<br>must not be modified. | G10 currencies |
| currencyCouples | List <currency<br>Cou<br/>ple Model&gt;</currency<br> | Currency Couple Group can contain<br>list of currency couple                                                                                                                                                                                                                       |                |

### <span id="page-58-2"></span>**14.3 Currency Couple Model**

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| Name  |      |             |         |

| baseCurrency | IsoCurrency Model               | Base currency represents currenyc1 in<br>Trade.<br>E.g EUR/USD, EUR is base<br>currency in this case   |  |
|--------------|---------------------------------|--------------------------------------------------------------------------------------------------------|--|
|              | quoteCurrency IsoCurrency Model | Quote currency represents currency2 in<br>Trade.<br>E.g EUR/USD, USD is quote<br>currency in this case |  |

## <span id="page-60-0"></span>**15 Iso Currency**

Supported Iso Currencies by 360T. Only GET method can be used and it returns available currencies in 360T system.

### <span id="page-60-1"></span>**15.1 Iso Currency Operations**

### <span id="page-60-2"></span>**15.1.1 Get Available Iso Currencies**

List available [Iso Currencies](#page-60-3) in 360T system.

### HTTP Request

```
GET https://apigateway-int.360t.com:7060/limitapi/v2/isoCurrency
```

### Example Response

```
[
 {
   "isoCode": "***"
 },
 {
   "isoCode": "EUR"
 },
 {
   "isoCode": "USD"
 },
 {
   "isoCode": "GBP"
 },
 {
   "isoCode": "JPY"
 }
]
```

### <span id="page-60-3"></span>**15.2 IsoCurrency Model**

| Field<br>Name | Type   | Description          | Example |
|---------------|--------|----------------------|---------|
| isoCode       | String | ISO code of currency | EUR     |

## <span id="page-61-0"></span>**16 Time Period Group**

Time Period Group is used to define a period of time. E.g from TODAY to 1 MONTH later. Time Period is part of a Risk Portfolio Rule to determine which trade intentions to be captured based on the effective date of the deals. For an overview please see the [screenshot](#page-92-0) that demonstrates how to define Time Period Group from Bridge Admin application.

### <span id="page-61-1"></span>**16.1 Time Period Group Operations**

| Operation | Http<br>Method | Description                                     | resource<br>path        |
|-----------|----------------|-------------------------------------------------|-------------------------|
| List      | GET            | List all Time Period Groups                     | /timePeriodGroup        |
| Find One  | GET            | List a Time Period Group by given<br>name       | /timePeriodGroup/{name} |
| Add       | POST           | Add a Time Period Group                         | /timePeriodGroup        |
| Update    | PUT            | Update given Time Period Group by<br>given name | /timePeriodGroup/{name} |
| Delete    | DELETE         | Delete a Time Period Group by given<br>name     | /timePeriodGroup/{name} |

The followings are the operations which can be done on Time Period Group.

### <span id="page-61-2"></span>**16.1.1 Add a Time Period Group**

Add a new [Time Period Group](#page-64-0) to 360T system.

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/timePeriod

### Example Request

```
[
   {
      "name": "TimePeriodGrp-Test",
      "timePeriod": {
         "from": {
             "longName": "TOMORROW",
             "shortName": "TM"
         },
         "to": {
             "longName": "1 WEEK",
             "shortName": "1W"
         }
      }
   }
```

]

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-62-0"></span>**16.1.2 List All Time Period Group**

List all [Time Period Group.](#page-64-0)

#### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/timePeriodGroup

#### Example Response

```
[
   {
      "id": "5754708",
      "name": "TimePeriodGrp-Test",
      "timePeriod": {
         "from": {
             "longName": "TODAY",
             "shortName": "TD"
         },
         "to": {
             "longName": "UNLIMITED",
             "shortName": "UL"
         }
      }
   }
]
```

#### <span id="page-62-1"></span>**16.1.3 Update a TimePeriod Group**

Update given [Time Period Group.](#page-64-0)

#### HTTP Request

```
PUT
   https://apigateway-int.360t.com:7060/limitapi/v2/timePeriod/TimePeriodGrp-Test
```

#### Example Request

{

```
"name": "TimePeriodGrp-Test",
"timePeriod": {
   "from": {
      "longName": "TODAY",
      "shortName": "TD"
```

63

```
},
      "to": {
          "longName": "UNLIMITED",
          "shortName": "UL"
      }
   }
}
```

Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-63-0"></span>**16.1.4 Get a Time Period Group**

Get a [Time Period Group](#page-64-0) by given time period group Id.

#### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/timePeriodGroup/TimePeriodGrp-Test

#### Example Response

```
{
   "id": "5754708",
   "name": "TimePeriodGrp-Test",
   "timePeriod": {
      "from": {
         "longName": "TODAY",
         "shortName": "TD"
      },
      "to": {
         "longName": "UNLIMITED",
         "shortName": "UL"
      }
   }
}
```

### <span id="page-63-1"></span>**16.1.5 Delete a Time Period Group**

Delete Time Period Group by given time period group Id.

#### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/timePeriodGroup/TimePeriodGrp-Test
```

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-64-0"></span>**16.2 Time Period Group Model**

| Field<br>Name | Type              | Description                                                                                                                                                                                                                                                                 | Example    |
|---------------|-------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|
| id            | String            | Unique and unmodifiable Id of Time<br>Period Group.<br>360T generates this<br>Id once a new Time Period Group is<br>added.                                                                                                                                                  | 5754708    |
| name          | String            | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which time periods this group<br>has. This field is required when adding<br>a new or updating the existing Time Pe<br>riod Group. The value of this field must<br>not be modified. | TodTomSpot |
| timePeriod    | Time Period Model | Time Period                                                                                                                                                                                                                                                                 |            |

### <span id="page-64-1"></span>**16.3 Time Period Model**

| Field<br>Name | Type       | Description                                                                                                                                                                         | Example |
|---------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|
| from          | Time Model | This specifies the start time. Start time<br>and end time defines Time Period. E.g<br>Start Time:<br>TODAY, End Time:<br>1<br>MONTH. That means time from today<br>to 1 Month later |         |
| to            | Time Model | This specifies the end time. Start time<br>and end time defines Time Period. E.g<br>Start Time:<br>TODAY, End Time:<br>1<br>MONTH. That means time from today<br>to 1 Month later   |         |

## <span id="page-65-0"></span>**17 Time**

It is possible to use pre-defined tenors in 360T Risk Portfolio. By using GET operation as described below, all available pre-defined tenors can be listed.

### <span id="page-65-1"></span>**17.1 Time Operations**

### <span id="page-65-2"></span>**17.1.1 List Available Times**

List all available [Time Model](#page-65-3) in 360T system.

### HTTP Request

[

GET https://apigateway-int.360t.com:7060/limitapi/v2/time

### Example Response

```
{
   "longName": "TODAY",
   "shortName": "TD"
},
{
   "longName": "TOMORROW",
   "shortName": "TM"
},
{
   "longName": "SPOT",
   "shortName": "SP"
},
{
   "longName": "SPOTNEXT",
   "shortName": "SN"
},
{
   "longName": "1 WEEK",
   "shortName": "1W"
},
{
   "longName": "2 WEEKS",
   "shortName": "2W"
}
```

### <span id="page-65-3"></span>**17.2 Time Model**

]

| Field<br>Type<br>Description<br>Example<br>Name |
|-------------------------------------------------|
|-------------------------------------------------|

| shortName | String | Short name of Time Period | TD    |
|-----------|--------|---------------------------|-------|
| longName  | String | Long name of Time Period  | TODAY |

## <span id="page-67-0"></span>**18 Dealer Group**

You can group several Dealers under one Dealer Group, then defined Dealer Group is used to create rules.

### <span id="page-67-1"></span>**18.1 Dealer Group Operations**

The followings are the operations which can be done on Dealer Group.

| Operation  | Http<br>Method | Description                                 | resource<br>path    |
|------------|----------------|---------------------------------------------|---------------------|
| List       | GET            | List all dealer groups                      | /dealerGroup        |
| Find One   | GET            | List a Dealer Group by given name           | /dealerGroup/{name} |
| Add        | POST           | Add a Dealer Group                          | /dealerGroup        |
| Add dealer | POST           | Add Dealer to Dealer Group by given<br>name | /dealerGroup/{name} |
| Update     | PUT            | Update a Dealer Group by given name         | /dealerGroup/{name} |
| Delete     | DELETE         | Delete a Dealer Group by given name         | /dealerGroup/{name} |

### <span id="page-67-2"></span>**18.1.1 List All Dealer Groups**

List all [Dealer Group.](#page-70-0)

### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup

### Example Request

```
[
   {
      "id": "123456",
      "dealers": [
          {
             "id": "MT.Treasurer1",
             "name": "MT.Treasurer1"
          }
      ],
      "name": "DealerGrp-Test"
   }
]
```

### <span id="page-67-3"></span>**18.1.2 Get a Dealer Group**

[Dealer Group](#page-70-0) by name.

#### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test
```

#### Example Response

```
{
   "id": "123456",
   "dealers": [
      {
         "id": "MT.Treasurer1",
         "name": "MT.Treasurer1"
      }
   ],
   "name": "DealerGrp-Test"
}
```

#### <span id="page-68-0"></span>**18.1.3 Add a Dealer Group**

Add a new [Dealer Group](#page-70-0) to 360T system.

#### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup

#### Example Request

```
{
   "dealers": [
      {
          "name": "MT.Treasurer1"
      }
   ],
   "name": "DealerGrp-Test"
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-68-1"></span>**18.1.4 Add a Dealer to Existing Dealer Group**

Add a new [Dealer](#page-71-3) to existing Dealer Group.

#### HTTP Request

```
POST https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test
```

#### Example Request

```
{
   "dealer": {
      "name": "MT.Treasurer1"
   }
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-69-0"></span>**18.1.5 Update a Dealer Group**

Update given [Dealer Group.](#page-70-0)

#### HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test

#### Example Request

```
{
   "dealers": [
      {
          "name": "MT.Treasurer1"
      }
   ],
   "name": "DealerGrp-Test"
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-69-1"></span>**18.1.6 Delete a Dealer Group**

Delete [Dealer Group](#page-70-0) by name.

#### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test
```

#### Example Response

```
{
```

"code": 200,

### 18.2. Dealer Group Model Dealer Group

```
"message": "Response"
```

}

## <span id="page-70-0"></span>**18.2 Dealer Group Model**

| Field<br>Name | Type                            | Description                                                                                                                                                                                                                                                              | Example        |
|---------------|---------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|
| id            | String                          | Unique and unmodifiable Id of Dealer<br>Group.<br>360T generates this Id once a<br>new Dealer Group is added.                                                                                                                                                            | 123456         |
| name          | String                          | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which legal entitiess this group<br>has. This field is required when adding<br>a new or updating the existing Dealer<br>Group. The value of this field must not<br>be modified. | DealerGrp-Test |
| dealers       | List <dealer model=""></dealer> | Group can contain a list of Dealers                                                                                                                                                                                                                                      |                |

## <span id="page-71-0"></span>**19 Dealer**

Dealer is an essential parameter of a risk portfolio rule which includes all dealer users plus the dealer groups that are pre-defined by client via Dealer Group operations.

### <span id="page-71-1"></span>**19.1 Dealer Operations**

### <span id="page-71-2"></span>**19.1.1 List Available Dealers**

List available [Dealers](#page-71-3) of the client.

### HTTP Request

```
GET https://apigateway-int.360t.com:7060/limitapi/v2/dealer
```

### Example Response

```
[
   {
      "id": "MT.Treasurer1",
      "name": "MT.Treasurer1"
   }
]
```

### <span id="page-71-3"></span>**19.2 Dealer Model**

| Field<br>Name | Type   | Description                                                                  | Example       |
|---------------|--------|------------------------------------------------------------------------------|---------------|
| id            | String | Unique ID of Dealer in 360T system.<br>360T uses name of Dealer as unique Id | MT.Treasurer1 |
| name          | String | Name of Dealer                                                               | MT.Treasurer1 |

## <span id="page-72-0"></span>**20 Counterpart Group**

Counterpart refers to the 360T system name of the entities clients have permissioned trading relationship with. It is an essential parameter of a Risk Portfolio rule which determines to check the limit of based on the counterpart who client intends to trade. Counterpart group allows clients to group their counterparts so that they can define a risk portfolio rule which captures the risk exposure of multiple counterparts.

Please note that this parameter will also allow clients to map 360T system names of counterparts with their internal systems by creating counterpart group with their internal naming and add the corresponding counterpart.

For an overview please see the [screenshot](#page-93-0) that demonstrates how to define Counterpart Group from Bridge Admin application.

### <span id="page-72-1"></span>**20.1 Counterpart Group Operations**

| Operation | Http<br>Method | Description                                              | resource<br>path          |
|-----------|----------------|----------------------------------------------------------|---------------------------|
| List      | GET            | List all Counterpart Groups                              | /counterpartsGroup        |
| Find One  | GET            | List<br>a<br>Counterpart<br>Group<br>by<br>given<br>name | /counterpartsGroup/{name} |
| Add       | POST           | Add a Counterpart Group                                  | /counterpartsGroup        |
| Update    | PUT            | Update Counterpart Group by given<br>name                | /counterpartsGroup/{name} |
| Delete    | DELETE         | Delete a Counterpart Group by given<br>name              | /counterpartsGroup/{name} |

The followings are the operations which can be done on Counterpart Group.

### <span id="page-72-2"></span>**20.1.1 Add a Counterpart Group**

Add a new [Counterpart Group](#page-75-0) to 360T system.

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup

### Example Request

{

```
"name": "CounterpartGrp-Test",
"institutions": [
   {
      "name": "MT.Bank3"
   },
   {
```

"name": "MT.Bank4" } ] }

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-73-0"></span>**20.1.2 List All Counterpart Groups**

List all [Counterpart Group.](#page-75-0)

#### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup

#### Example Response

```
[
   {
      "id": "9037612",
      "institutions": [
          {
             "id": "MT.Bank3",
             "name": "MT.Bank3"
          },
          {
             "id": "MT.Bank4",
             "name": "MT.Bank4"
          }
      ],
      "name": "CounterpartGrp-Test"
   }
]
```

#### <span id="page-73-1"></span>**20.1.3 Update a Counterpart Group**

The followings can be updated with this operation.

• Counterpart list of Counterpart Group (deleting or adding institution)

#### HTTP Request

Put

https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup/CounterpartGrp-Test

Example Request

```
{
   "name": "CounterpartGrp-Test",
   "institutions": [
      {
          "name": "MT.Bank3"
      },
      {
          "name": "MT.Bank5"
      }
   ]
}
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-74-0"></span>**20.1.4 Get a Counterpart Group**

Get a [Counterpart Group](#page-75-0) by name.

#### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup/CounterpartGrp-Test
```

#### Example Response

```
{
   "id": "9037612",
   "institutions": [
      {
         "id": "MT.Bank3",
         "name": "MT.Bank3"
      },
      {
         "id": "MT.Bank4",
         "name": "MT.Bank4"
      }
   ],
   "name": "CounterpartGrp-Test"
}
```

#### <span id="page-74-1"></span>**20.1.5 Delete a Counterpart Group**

Delete Counterpart Group by name.

#### HTTP Request

DELETE

https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup/CounterpartGrp-Test

#### Example Response

```
{
   "code": 200,
   "message": "Response"
```

}

### <span id="page-75-0"></span>**20.2 Counterpart Group Model**

| Field<br>Name | Type                                            | Description                                                                                                                                                                                                                                                                 | Example            |
|---------------|-------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|
| id            | String                                          | Unique and unmodifiable Id of Coun<br>terpart Group.<br>360T generates this<br>Id once a new Counterpart Group is<br>added.                                                                                                                                                 | 9037612            |
| name          | String                                          | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which counterparts this group<br>has. This field is required when adding<br>a new or updating the existing Counter<br>part Group. The value of this field must<br>not be modified. | Counterpart Group1 |
| institutions  | List <counterpart<br>Model&gt;</counterpart<br> | Group can contain a list of Counterpart.                                                                                                                                                                                                                                    |                    |

## <span id="page-76-0"></span>**21 Counterpart**

Counterpart is an essential parameter of a risk portfolio rule and include all requester and/or provider companies a credit entity has trading relationship with plus the counterpart groups that are pre-defined by client via Counterpart Group operations.

### <span id="page-76-1"></span>**21.1 Counterpart Operations**

### <span id="page-76-2"></span>**21.1.1 List Available Counterparts**

List available [Counterparts](#page-76-3) of the client.

### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/counterpart

### Example Response

```
[
   {
      "id": "MT.Bank3",
      "name": "MT.Bank3"
   }
]
```

### <span id="page-76-3"></span>**21.2 Counterpart Model**

| Field<br>Name | Type   | Description                                                                                | Example   |
|---------------|--------|--------------------------------------------------------------------------------------------|-----------|
| id            | String | Unique ID of Counterpart in 360T sys<br>tem. 360T uses name of Counterpart as<br>unique Id | BANK.TEST |
| name          | String | Name of counterpart entity                                                                 | BANK.TEST |

## <span id="page-77-0"></span>**22 Legal Entity Group**

You can group several legal entities under one Legal Entity Group, then defined Legal Entity Group is used to create rules. For an overview please see the [screenshot](#page-92-2) that demonstrates how to define Legal Entity Group from Bridge Admin application.

### <span id="page-77-1"></span>**22.1 Legal Entity Group Operations**

| Operation | Http<br>Method | Description                                  | resource<br>path           |
|-----------|----------------|----------------------------------------------|----------------------------|
| List      | GET            | List all product groups                      | /legalEntitiesGroup        |
| Find One  | GET            | List a Legal Entity Group by given<br>name   | /legalEntitiesGroup/{name} |
| Add       | POST           | Add a Legal Entity Group                     | /legalEntitiesGroup        |
| Update    | PUT            | Update a Legal Entity Group by given<br>name | /legalEntitiesGroup/{name} |
| Delete    | DELETE         | Delete a Legal Entity Group by given<br>name | /legalEntitiesGroup/{name} |

The followings are the operations which can be done on Legal Entity Group.

### <span id="page-77-2"></span>**22.1.1 Add a Legal Entity Group**

Add a new Legal Entity Group to 360T system.

### HTTP Request

```
POST https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup
```

### Example Request

```
{
   "institutions": [
      {
         "name": "360T.INT_LIMAPI.TEST"
      }
   ],
   "name": "LegalEntityGrp-Test"
}
```

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-78-0"></span>**22.1.2 List All Legal Entities Groups**

List all Legal Entity Group.

#### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup

#### Example Request

```
[
   {
      "id": "4684994",
      "institutions": [
         {
             "id": "360T.INT_LIMAPI.TEST",
             "name": "360T.INT_LIMAPI.TEST"
         }
      ],
      "name": "LegalEntityGrp-Test"
   }
]
```

#### <span id="page-78-1"></span>**22.1.3 Update a Legal Entity Group**

Update given Legal Entity Group.

#### HTTP Request

```
PUT
   https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test
```

#### Example Request

```
{
   "institutions": [
      {
         "name": "360T.INT_LIMAPI.TEST"
      }
   ],
   "name": "LegalEntityGrp-Test"
}
```

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-79-0"></span>**22.1.4 Add a Legal Entity to Existing Legal Entity Group**

Add a new Institution to existing Legal Entity Group.

#### HTTP Request

POST

{

}

https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test

### Example Request

"name": "360T.INT\_LIMAPI.TEST"

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-79-1"></span>**22.1.5 Get a Legal Entity Group**

Legal Entity Group by name.

### HTTP Request

```
GET
https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test
```

### Example Response

```
{
   "id": "4684994",
   "institutions": [
      {
         "id": "360T.INT_LIMAPI.TEST",
         "name": "360T.INT_LIMAPI.TEST"
      }
   ],
   "name": "LegalEntityGrp-Test"
}
```

### <span id="page-79-2"></span>**22.1.6 Delete a Legal Entity Group**

Delete Legal Entity Group by name.

### HTTP Request

```
DELETE
https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test
```

### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-80-0"></span>**22.2 Legal Entity Group Model**

| Field<br>Name | Type                                           | Description                                                                                                                                                                                                                                                                       | Example             |
|---------------|------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|
| id            | String                                         | Unique and unmodifiable Id of Coun<br>terpart Group.<br>360T generates this<br>Id once a new Counterpart Group is<br>added.                                                                                                                                                       | 4684994             |
| name          | String                                         | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which legal entitiess this group<br>has. This field is required when adding<br>a new or updating the existing Legal<br>Entity Group.<br>The value of this field<br>must not be modified. | LegalEntityGrp-Test |
| institutions  | List <legal<br>Entity<br/>Model&gt;</legal<br> | Group can contain a list of Legal Entity                                                                                                                                                                                                                                          |                     |

## <span id="page-81-0"></span>**23 Legal Entity**

Legal entity refers to the available legal entities of the client (credit entity). Legal Entity is another parameter of risk portfolio rule which allows clients to differentiate their risk portfolios based on which of their legal entity they are trading for.

### <span id="page-81-1"></span>**23.1 Legal Entity Operations**

The followings are the operations which can be done on Legal Entity.

| Operation | Http<br>Method | Description                                        | resource<br>path |
|-----------|----------------|----------------------------------------------------|------------------|
| List      | GET            | List all available Legal Entities of the<br>client | /legalEntity     |

### <span id="page-81-2"></span>**23.1.1 List Available Legal Entities**

List available Institution in 360T system.

### HTTP Request

```
GET https://apigateway-int.360t.com:7060/limitapi/v2/legalEntity
```

### Example Response

```
[
   {
      "id": "360T.INT_LIMAPI.TEST",
      "name": "360T.INT_LIMAPI.TEST"
   }
]
```

### <span id="page-81-3"></span>**23.2 Legal Entity Model**

| Field<br>Name | Type   | Description                                                                                     | Example   |
|---------------|--------|-------------------------------------------------------------------------------------------------|-----------|
| id            | String | Unique ID of Legal Entity in 360T sys<br>tem.<br>360T uses name of Legal Entity<br>as unique Id | BANK.TEST |
| name          | String | Name of Institution                                                                             | BANK.TEST |

## <span id="page-82-0"></span>**24 PFE Table**

PFE Table can be used to define the PFE factors per currency pair and tenor. For an overview please see the [screenshot](#page-94-1) that demonstrates how to define PFE from Bridge Admin application.

### <span id="page-82-1"></span>**24.1 PFE Table Operations**

**Operation Http Method Description resource path** List GET List All PFE entries /pfe Add POST Add a new currency couple to PFE Table /pfe Add Tenor POST Add a PFE tenor /pfe/tenor Update PUT Update PFE entry /pfe Delete DELETE Delete a currency couple from PFE Table /pfe?baseCurrency= {currency1} &quoteCurrency= {currency2} Delete Tenor DELETE Delete a PFE tenor /pfe/tenor/{tenor}

The followings are the operations which can be done on PFE Table.

### <span id="page-82-2"></span>**24.1.1 Add a Currency Couple to PFE Table**

Add a new currency to [PFE Table.](#page-88-0) "Default" represents default tenor. If default tenor is not specified, factor for default tenor will be 1 as default.

### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/pfe

### Example Request

{

```
"baseCurrency": {
   "isoCode": "EUR"
},
"quoteCurrency": {
   "isoCode": "GBP"
},
"factors": [
   {
      "factor": 0.4,
      "tenor": {
         "tenor": "6"
```

```
}
   },
   {
      "factor": 1,
      "tenor": {
          "tenor": "8"
      }
   },
   {
      "factor": 1.6,
      "tenor": {
          "tenor": "20"
      }
   },
   {
      "factor": 3,
      "tenor": {
          "tenor": "Default"
      }
   }
]
```

### Example Response

}

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-83-0"></span>**24.1.2 List All PFE Entries**

List all [PFE Entries.](#page-88-0) "\*\*\*" will represent default currency.

### HTTP Request

GET https://apigateway-int.360t.com:7060/limitapi/v2/pfe

```
[
   {
      "baseCurrency": {
          "isoCode": "***"
      },
      "quoteCurrency": {
          "isoCode": "***"
      },
      "factors": [
          {
             "factor": 1,
             "tenor": {
                "tenor": "6"
             }
```

```
},
      {
          "factor": 1,
          "tenor": {
             "tenor": "8"
          }
      },
      {
          "factor": 1,
          "tenor": {
             "tenor": "20"
          }
      },
      {
          "factor": 1,
          "tenor": {
             "tenor": "Default"
          }
      }
   ]
},
{
   "baseCurrency": {
      "isoCode": "CAD"
   },
   "quoteCurrency": {
      "isoCode": "AUD"
   },
   "factors": [
      {
          "factor": 1,
          "tenor": {
             "tenor": "6"
          }
      },
      {
          "factor": 1,
          "tenor": {
             "tenor": "8"
          }
      },
      {
          "factor": 1,
          "tenor": {
             "tenor": "20"
          }
      },
      {
          "factor": 1,
          "tenor": {
             "tenor": "Default"
          }
      }
   ]
}
```

]

### <span id="page-85-0"></span>**24.1.3 Update PFE Entry on PFE Table**

Update given [PFE Entry.](#page-88-0)

#### HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/pfe

#### Example Request

```
{
      "baseCurrency": {
          "isoCode": "CAD"
      },
      "quoteCurrency": {
          "isoCode": "AUD"
      },
      "factors": [
          {
             "factor": 0.4,
             "tenor": {
                 "tenor": "6"
             }
          },
          {
             "factor": 1,
             "tenor": {
                 "tenor": "8"
             }
          },
          {
             "factor": 1.6,
             "tenor": {
                 "tenor": "20"
             }
          },
          {
             "factor": 3,
             "tenor": {
                 "tenor": "Default"
             }
          }
      ]
}
```

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-86-0"></span>**24.1.4 Update Default PFE Entry on PFE Table**

In order to update default currency couple, "\*\*\*" needs to be sent as isoCode.

#### HTTP Request

PUT https://apigateway-int.360t.com:7060/limitapi/v2/pfe

#### Example Request

```
{
      "baseCurrency": {
          "isoCode": "***"
      },
      "quoteCurrency": {
          "isoCode": "***"
      },
      "factors": [
          {
             "factor": 1,
             "tenor": {
                 "tenor": "6"
             }
          },
          {
             "factor": 1.5,
             "tenor": {
                 "tenor": "8"
             }
          },
          {
             "factor": 1.2,
             "tenor": {
                 "tenor": "20"
             }
          },
          {
             "factor": 2,
             "tenor": {
                 "tenor": "Default"
             }
          }
      ]
}
```

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-87-0"></span>**24.1.5 Delete a Currency Couple from PFE Table**

Delete a currency [PFE Entry](#page-88-0) from PFE Table by id. Default configuration cannot be deleted. Therefore if you try to make a request as following "v1/pfe?baseCurrency=\*\*\*quoteCurrency=\*\*\*", you will receive an error message.

#### HTTP Request

```
DELETE
   https://apigateway-int.360t.com:7060/limitapi/v2/pfe?baseCurrency=CAD&quoteCurrency=AUD
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-87-1"></span>**24.1.6 Add a Tenor to PFE Table**

Add a tenor to PFE Table. Default tenor cannot be added. Only Integer value is accepted.

#### HTTP Request

POST https://apigateway-int.360t.com:7060/limitapi/v2/pfe/tenor

#### Example Request

```
{
 "tenor": 4
}
```

#### Example Response

```
{
   "code": 200,
   "message": "Response"
}
```

#### <span id="page-87-2"></span>**24.1.7 Delete a Tenor from PFE Table**

Delete a tenor from PFE Table.

#### HTTP Request

DELETE https://apigateway-int.360t.com:7060/limitapi/v2/pfe/tenor/5

```
{
   "code": 200,
   "message": "Response"
}
```

### <span id="page-88-0"></span>**24.2 PFE Table Row Model**

| Field<br>Name | Type                                             | Description                                                                          | Example |
|---------------|--------------------------------------------------|--------------------------------------------------------------------------------------|---------|
| baseCurrency  | IsoCurrency Model                                | Any IsoCurrency which is fetched from<br>Get Available Iso Currencies can be<br>used |         |
|               | quoteCurrency IsoCurrency Model                  | Any IsoCurrency which is fetched from<br>Get Available Iso Currencies can be<br>used |         |
| factors       | List <pfe col<br="" table="">umn Model&gt;</pfe> | List of factors refers to tenor-factor<br>couple                                     |         |

### <span id="page-88-1"></span>**24.3 PFE Table Column Model**

| Field<br>Name | Type        | Description                                                   | Example |
|---------------|-------------|---------------------------------------------------------------|---------|
| tenor         | Tenor Model | Tenor value which refers to number of<br>actual calendar days |         |
| factor        | Integer     | Factor value                                                  | 0.35    |

### <span id="page-88-2"></span>**24.4 Tenor Model**

| Field<br>Name | Type   | Description                                                                                                                | Example      |
|---------------|--------|----------------------------------------------------------------------------------------------------------------------------|--------------|
| tenor         | String | Tenor value which refers to number of<br>actual calendar days or this value can<br>be "Default" to represent default tenor | 2 or Default |

## <span id="page-89-0"></span>**25 Error Handling**

### <span id="page-89-1"></span>**25.1 Authentication**

Each client is provided with a set of private key and client certificate. These should be used when submitting requests to the 360T Limit REST API access point. Thus the requester will be authorized to use certain resources. If such a client certificate is not provided, the services will return HTTP 401.

## <span id="page-90-0"></span>**26 Version Log**

| Version | Date       | Comments                                                                                                                                                                                                                 |
|---------|------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 0.1     | 30.04.2019 | Initial draft.                                                                                                                                                                                                           |
| 0.2     | 30.04.2019 | Added new status code 409.                                                                                                                                                                                               |
| 0.2     | 19.06.2019 | Updated ConfigRule Model.<br>Updated Portfolio Model.                                                                                                                                                                    |
| 0.3     | 28.11.2019 | Added PFE Overview to overview list.<br>Added PFE Operations.<br>Updated Bridge Admin screenshots.                                                                                                                       |
| 0.3     | 10.12.2019 | Added "how to update default configurations" to PFE Operations.                                                                                                                                                          |
| 0.3     | 23.12.2019 | Updated Time Period Group Model.<br>Updated examples under Time Period Group Operations.                                                                                                                                 |
| 0.3     | 30.12.2019 | Updated Portfolio Model.<br>Updated examples under Portfolio Operations.<br>Updated definitions under PFE Overview.<br>Updated definitions under PFE Operations.<br>Added test environment (INT) information to Servers. |
| 0.4     | 30.12.2019 | Added new section Active Rule.                                                                                                                                                                                           |
| 0.4     | 28.03.2020 | Updated INT server.<br>Updated base url.                                                                                                                                                                                 |
| 1.0     | 18.09.2020 | New version has been created.                                                                                                                                                                                            |
| 2.0     | 19.09.2020 | New version has been created.<br>Changed unique identifier of group from id to name.<br>Added Execution Method Operations.                                                                                               |
| 2.0     | 21.10.2020 | Added Execution Method Group Operations.                                                                                                                                                                                 |
| 2.0.1   | 27.05.2021 | Removing the single currency group.                                                                                                                                                                                      |
| 2.0.2   | 28.05.2021 | Updated Risk Portfolio Rule, Active Rule and REST Operations Overview sections.                                                                                                                                          |
| 2.0.3   | 14.07.2021 | Updated JSON examples in Currency Couple Group Operations.                                                                                                                                                               |
| 2.0.4   | 09.11.2021 | Added Exceptional Limits to Active Rule.                                                                                                                                                                                 |
| 2.0.5   | 29.12.2021 | Added new sections: Dealer Operations, Dealer Group Operations.<br>Added Single or Group Dealer to Config Rule and Active Rule.                                                                                          |

Table 26.1: Version history

## <span id="page-91-0"></span>**27 Appendix**

### <span id="page-91-1"></span>**27.1 How To Manage Risk Portfolio From Bridge Admin**

<span id="page-91-2"></span>**27.1.1 Product Group**

![](_page_91_Picture_4.jpeg)

**27.1.2 Currency Couple Group**

<span id="page-91-3"></span>![](_page_91_Figure_6.jpeg)

### <span id="page-92-0"></span>**27.1.3 Time Period Group**

![](_page_92_Picture_4.jpeg)

### <span id="page-92-1"></span>**27.1.4 Portfolio**

![](_page_92_Picture_6.jpeg)

### <span id="page-92-2"></span>**27.1.5 Legal Entity Group**

![](_page_92_Picture_8.jpeg)

### <span id="page-93-0"></span>**27.1.6 Counterparts Group**

![](_page_93_Picture_4.jpeg)

### <span id="page-93-1"></span>**27.1.7 Execution Method Group**

![](_page_93_Picture_6.jpeg)

### <span id="page-93-2"></span>**27.1.8 Risk Portfolio Rule**

![](_page_93_Picture_8.jpeg)

![](_page_94_Picture_0.jpeg)

### <span id="page-94-0"></span>**27.1.9 Active Rule**

![](_page_94_Figure_4.jpeg)

### <span id="page-94-1"></span>**27.1.10 PFE**

![](_page_94_Picture_6.jpeg)