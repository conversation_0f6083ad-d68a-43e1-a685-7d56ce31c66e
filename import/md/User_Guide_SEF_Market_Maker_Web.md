# **USER GUIDE 360T SEF**

![](_page_0_Picture_2.jpeg)

# **TEX MULTIDEALER TRADING SYSTEM**

### USER GUIDE 360T SWAP EXECUTION FACILITY FOR THE MARKET MAKER

© 360 TREASURY SYSTEMS AG, 2015

## **1 INTRODUCTION**

In July 2010, the Dodd-Frank Wall Street Reform and Consumer Protection Act ("Dodd-Frank" or the "Act") was signed into U.S. federal law. The Act made changes to the financial regulatory environment in America as a way of promoting financial stability by, among other things, improving accountability and transparency in the financial system. Within the Act, Title VII concerns regulation of the over-the-counter swaps markets, which requires certain OTC derivative and FX instruments to be traded on a regulated SEF

. **360 Trading Networks Inc. (360T Inc.) offers a SEF venue for such transactions captured under this definition.** At present, this applies to FX options and FX nondeliverable forward and Swap (NDF and NDS) instruments traded by persons identified as U.S. entities or which fall under the definitions within the Act. 360T Inc.'s SEF services will enable clients to comply with their own Dodd Frank regulatory obligations and will retain the ability for clients to trade with greater transparency and enhanced control at every stage of the trading lifecycle.

Once a client is considered by 360T to be SEF relevant, users can only trade NDF, NDS and FX Options on the SEF. U.S. clients and entities trading with U.S. clients are blocked from trading these instruments if not enabled on the SEF. The required forms have therefore to be complete as well as the administrative information within the 360T administration tool.

This user manual describes the functionality available for the Market Participant trading as Market Maker under the 360T Swap Execution Facility ("360T SEF"). This means on one side the administrative functions to complete in the system prior to being able to trade under the 360T SEF, as well as the actual trading of the transactions falling under the regulation.

The 360T SEF platform is embedded in the 360T trading platform, so that an entity defined as trading NDF/NDS and FX Options under the SEF can still trade all other products under the non SEF platform. This user manual only describes the SEF relevant part. Please refer to the general 360T user manuals for all non SEF business.

In the 360T trader user interface, SEF related items are either displayed in green color with "SEF" in superscript, or with the mention of "SEF" in brackets, as shown in the following screen captures.

A Swap Dealer who has signed the 360T SEF participation agreement can trade NDF, NDS and FX Options both under the SEF and under the non SEF platform. This happens implicitly over configuration settings on the platform, so that you as user don't have to switch from SEF to non SEF.

Whether a trade is done under the SEF or not basically depends on the country of origin of the requesting entity and user and on the country of origin of the providing entity and user. US requesters can trade only with counterparties which have also signed the SEF participation agreement and set up the SEF reporting parameters. Non US requesters can trade with US and non US counterparties.

For the Market Maker this means the following:

If you are a Swap Dealer defined with 360T as a US participant, all NDF, NDS and FX Options will be traded on the SEF. You will be able to execute trades with US and non US requesters under the condition that you and the counterparties have recorded their reporting parameters.

If you are a Swap Dealer defined with 360T as a non US participant, but you wish to execute trades with US counterparties on the SEF, you will have to record your SEF reporting parameters to be included in the bank basket of your clients. Trading with non US clients is off-SEF.

| Requester        | Provider         | SEF / OFF SEF |
|------------------|------------------|---------------|
| Is US Person     | Is US Person     | SEF           |
|                  | Is not US Person | SEF           |
| Is not US Person | Is US Person     | SEF           |
|                  | Is not US Person | OFF SEF       |

For all cases marked as SEF above, the following prerequisites must be fulfilled:

- Company has provided 360T with its LEI
- Company is SEF enabled by 360T
- SEF Reporting parameters have been keyed in both for Company and Traders

# **2 SEF REPORTING PARAMETERS**

Under the Dodd Frank Act regulation, required transaction creation data must be reported to a Swap Data Repository (SDR). In addition, data is reported to the National Futures Association (NFA) in order to conduct trade practice and market surveillance on behalf of the 360T SEF. The data transmitted to the SDR and the NFA are trade execution parameters as well as activity data in general about who is trading and what is traded. For this, some participant specific data needs to be completed by the company and users.

Once the SEF participation agreement has been signed with 360T Inc., users will have been named to 360T's support services and given the according Administration rights to complete required parameters.

The functionality "SEF Data" to enter the required data can then be found under the Administration menu.

Required fields are displayed in bold letters. As long as a field is shown in red colour the data is incomplete and can't be saved.

Each company and user set up by 360T's services on the platform is already associated to a country. Depending on that information, some fields might not be required. For example, if you are a UK entity but have signed the SEF participation agreement in order to be able to trade with US participants, then you will not be required to enter a SSN.

Please note that if you are using an integrated price engine, the AutoDealer also needs SEF reporting data. It is assumed that it will be provided by the person responsible for the pricing system in the bank.

Once all data has been provided, your provider entity will automatically be included in the requester bank basket for an NDF, NDS or FX Option trade request of market takers having a counterparty relationship with you.

If the data is incomplete, the user will not be able to trade on the SEF.

After completion of the SEF User data form, a trader can be disabled from SEF trading by setting the value of the "SEF enabled" field to "false".

**NB**: Note that disablement of SEF trading only takes effect after the trader logs out and back into the application.

#### Indication of Collateralization:

Pursuant to CFTC regulation part 45, an indication of collateralization has to be reported to the SDR for all bilaterally executed swaps on the SEF and *must* include the following values:

- "uncollateralized"
- "partially collateralized"
- "one-way collateralized"
- "fully collateralized"

360T requires Market Makers to designate collateralization indication on a client by client basis. The corresponding configuration form is accessible via the "Collateralization" tab.

For clarification purposes, the configuration form is subdivided into three separate menus. Under the first menu point entitled "Defined", the administrator has access to the list of clients for which the collateralization designation has been already configured. The "Undefined" menu provides an overview of missing values and, finally, "All" displays a consolidated list of both "Defined" and "Undefined" data. The collateralized designation can either be set per client on an individual basis or by use of the bulk functionality via the right-hand side mouse button.

After selecting the "Save" button, the amended entries are automatically re-located into the different sub-menus.

# **3 TRADING NDF/NDS AND FX OPTIONS AS REQUEST FOR QUOTE ON THE SEF**

Once SEF specific reporting data is defined, NDF, NDS and FX Options requests from Requesters that have also completed the SEF on-boarding can be received and priced by you.

### **3.1 NDF**

Non deliverable forwards are outright contracts for non-convertible foreign currencies, which are cash-settled by using the difference between the agreed forward rate and the spot rate on the fixing date, which is usually 2 working days before the settlement date.

The workflow supported in 360T's SEF only concerns the execution of the NDF. Once NDF will be subject to clearing, further information will be completed in this manual. The settlement of the NDF always takes place offline between the counterparties.

On the market taker side, an FX Non deliverable forward (NDF) product definition is opened after selecting the product NDF in the product selection area and after clicking on a price in one of the FX Live Pricing.

The product definition screen will already include predefined data corresponding to the selection in the live pricing screen. For example, the currency pair and the effective date correspond to that selection.

The market taker enters an NDF with the desired data, i.e. notional amount, tenor and or maturity date, fixing date and fixing reference.

#### **3.2 NDS**

Non-deliverable swaps traded on 360T SEF can be considered as two linked non-deliverable forwards with opposite actions.

The workflow supported in 360T's SEF only concerns the execution of the NDS. Once NDF will be subject to clearing, further information will be completed in this manual. The settlement of the NDS always takes place offline between the counterparties.

The product definition screen will already include predefined data corresponding to the selection in the live pricing screen. For example, the currency pair and the effective periods correspond to that selection.

The market taker enters an NDS with the desired data, i.e. notional amount, tenor and or maturity date, fixing date and fixing reference.

In order to effect a transaction, a user must specify the relevant currencies and the notional amount to be traded. The minimum notional amount that can be specified is the relevant currency's smallest monetary unit. The maximum notional amount is restricted to a fixed digit length determined by 360T based upon credit and market risk considerations. 360T may change these parameters at any time. Users can access these parameters through the Trading System.

The Fixing Date will automatically be populated by the Trading System based on the spot date convention of the relevant currencies, and is generally two days before the value date. The Trading System will automatically send the requester's RFQ to the liquidity providers selected in advance by the requester on a disclosed basis. There is no minimum or maximum number of liquidity providers to whom RFQs may be sent.

Different liquidity providers may be selected for different currencies. A requester can override this functionality if desired in order to select different liquidity providers on a trade by trade basis. Notification of execution is sent to be counterparties to a transaction, but is not sent to all users of the Trading System.

#### **3.3 FX Options**

360T supports requests in first generation options, calls and puts and combinations of 2 options (zero premium or standard risk reversal or a request with two legs), European and American style.

Option pricing can be requested for live pricing or with a separate spot delta hedge.

The premium value date is defaulted to the spot date for the currency in which the premium is to be paid, but can be changed by the user. The exercise date is automatically generated by the Trading System and is 1 or 2 business days before the relevant delivery expiration date, depending on the spot date convention for the relevant currency pair.

The minimum notional amount that can be specified is the relevant currency's smallest monetary unit. The maximum notional amount is restricted to a fixed digit length determined by 360T based upon credit and market risk considerations. 360T may change these parameters at any time. Users can access these parameters through the Trading System.

The Trading System will automatically send the requester's RFQ to the liquidity providers selected in advance by the requester on a disclosed basis. There is no minimum or maximum number of liquidity providers to whom RFQs may be sent.

Different providers may be selected for different currencies. A requester can override this functionality if desired in order to select different liquidity providers on a trade by trade basis. Notification of execution is sent to the counterparties to a transaction, but is not sent to all users of the Trading System.

#### **3.3.1 Plain Vanilla FX Option / FX Risk Reversal / Zero Cost / Strategy option**

In addition to plain vanilla, it is possible to request combinations of 2 options.

Pre-configured strategies to create a standard risk reversal or a zero-cost option are accessible over the menu Transaction.

To request a zero-premium option, the user requests the strike for the first option of the combination and the bank will quote the strike of the second option.

In case of a standard risk reversal, the user will request a given strike for both options and the bank will quote the premium, similar to plain vanilla options.

Further freely configurable combinations of two plain vanilla options can be requested as Option Strategies. The product definition is similar to the risk reversal product definition, except that any combination of 2 options can be defined.

# **4 PRICING IN THE TRADER WORKSHEET**

Once a product definition is complete, the requester clicks to Send to receive quote streams to the swap dealers defined in the provider list.

The following describes the workflow when the request is not routed to an automated pricing system but is published for manual pricing in 360T's trader worksheet.

A trade request routed for manual intervention shows up as a blinking line with its details, while a ringing sound notifies the trader of the incoming request. To provide a price, the trader clicks on the request line to get the quoting screen.

#### **4.1 Pricing NDF**

The price of a NDF is the forward rate, which is calculated by the provided spot rate and forward points.

In case the user requests a two-way price, both bid rate (on the left side) and offer rate (on the right side) have to be provided.

Click to Send instantly shows the provided price to the requester in his competitive bidding screen, together with prices provided by other requested market makers, as well as with executable and indicative orders from the SEF Order Book.

Once the price is sent to the requester, the buttons Cancel and Send are replaced by the buttons Interrupt and Requote. You can withdraw your quote by clicking Interrupt. By Requote, you can directly send a newly entered quote.

In case the user executes your offered price, a deal confirmation ticket is displayed to the trader.

In case the requester executes the trade with a competing market maker, the offer screen automatically closes with an according sound.

### **4.2 Pricing NDS**

An NDS is priced by providing the spot rate and the swap points, potentially near and far points in case the request is a forward starting NDS.

In case a two-way request is made, the swap points have to be provided on the bid and on the offer side.

Once the price is sent to the requester, the buttons Cancel and Send are replaced by the buttons Interrupt and Requote. You can withdraw your quote by clicking Interrupt. By Requote, you can directly send a newly entered quote.

In case the user executes your offered price, a deal confirmation ticket is displayed to the trader.

Should the requester execute with another provider, the offer screen automatically closes with an according sound.

#### **4.3 Pricing Options**

The price of an FX Option is the premium amount, which can be expressed in one of the two currencies of the underlying currency pair.

The market maker usually expresses the price of the option either in terms of the base currency per unit of the underlying currency (in pips), or in percent of the notional currency.

The quote is shown to the market taker in total premium amount with additional details of the price in pips of the premium currency and, in case that notional and premium currencies are the same, in % of this notional amount. If the taker requests an option price with separate delta hedge, he will also see the total notional of the spot hedge and as price indication also applied volatility in %.

## **5 SPECIAL EXECUTION MODES**

As described previously, 2 additional special execution modes are possible. For simple FX products it is the multiple execution mode. For money market products it is the Up-to request mode usually combined with a multiple execution.

#### **5.1 Allow Multiple Executions**

The objective of the multiple execution mode is to split a large trade amount to several banks. The request will then be executed in a row with several of the pricing banks.

In case the option Allow multiple execution was selected on the Transaction tab of the product definition, once the first bid is executed, the competitive bidding screen remains open, while the remaining banks continue to price. Further executions are then possible, until the user clicks on Finish, which then stops the request to the remaining banks.

#### **5.2 Rollover and Early Settlement of Trades**

All dealblotters offer the possibility to rollover specific past trades.

NDF trades can be rolled over via the context menu in the dealblotter using the right mouse click or over the menu on the right of the dealblotter.

Rolling over an NDF automatically generates a new NDS based on the parameters of the rolled deal: near date of the NDS corresponds to the far date of the original deal; the same notional is assumed but can be modified. The request is then not only sent to the bank of the original rolled transaction, but simultaneously to other market makers as a new request for quote.

# **6 SEF ORDER BOOK**

The SEF Order Book allows the SEF enabled users to place limit orders that can be viewed by all other users registered as SEF Participants with 360T.

All users can view all orders placed in the SEF Order Book. Execution is only possible between entities that have a counterparty relationship with each other.

Orders displayed in black can be executed. Orders displayed in grey with obfuscated Company name are only indicative, as they originate from market participants without a relationship to the entity looking at the order book.

Orders for NDF, NDS and FX Options are supported in the SEF Order Book. In order to place an order on the SEF Order Book, a user must specify the relevant product, currencies and the notional amount to be traded. The minimum notional amount that can be specified is the relevant currency's smallest monetary unit. The maximum notional amount is restricted to a fixed digit length determined by 360T based upon credit and market risk considerations. 360T may change these parameters at any time. Users can access these parameters through the Trading System.

A new order can be placed either by selecting New in the File menu, or by using the button

in the toolbar.

360T does not automatically match orders. The execution remains under the control of the market participants. If an order is displayed as tradable (in black), the user can execute it without placing an opposite order in the user interface but by marking it, opening the context menu over the right mouse click and then selecting Execute; or by opening it with a doubleclick and then clicking the Execute button.

The SEF Order Book and the Request for Streamed quotes (RFS) process interact in the following way:

If a RFS matches one or several orders in terms of: Action, Currency Couple, Effective Date and Maturity Date, Fixing Reference/Expiry and the notional is equal to the amount in the Limit Order and also the notional currency is the same as in the limit order then the **best executable** limit rate will be displayed in the competitive bidding window. In addition the **best indicative** limit rate will also be displayed in the RFS window if it is better than or equal to the best executable limit rate. A rate is considered indicative (non-executable), if no relationship to the company submitting the limit order exists.

Limit orders cannot be partially filled.

All orders placed in the SEF order book expire at the end of the trading day, i.e. at 5pm New York. A placed order can be canceled or amended by any user in the same company as the user who has entered the order.

# **7 CONTACTING 360T**

#### **Global Client Advisory Services**

Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>

#### **Germany**

*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29

#### **Middle East Asia Pacific**

#### **United Arab Emirates**

*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134

#### **EMEA Americas**

**USA**

*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900

**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756