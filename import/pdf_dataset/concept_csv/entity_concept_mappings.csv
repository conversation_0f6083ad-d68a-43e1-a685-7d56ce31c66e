entity,concept
RFS Market Taker API FIX Rules of Engagement API,API_Systems
API Version 12.14,API_Systems
RFS Market Taker API FIX Rules of Engagement API,FIX_Protocol
RFS Market Taker API FIX Rules of Engagement API,Market_Systems
FIX Protocol Version 4.4,FIX_Protocol
FIX services,FIX_Protocol
detailed specifications of utilized FIX messages,FIX_Protocol
API,API_Systems
FIX 4.4 standards,FIX_Protocol
Products currently supported by API subsection,API_Systems
Market data subsection,Market_Systems
Important Disclaimer for API Clients section,API_Systems
FIX Engine Compatibility Testing subsection,FIX_Protocol
FIX Protocol Levels subsection,FIX_Protocol
FIX Session Reset,FIX_Protocol
Trading Hours subsection,Trading_Operations
Utilization of the FIX Protocol section,FIX_Protocol
The RFS Market Taker API implements FIX Protocol Version 4.4.,API_Systems
RFS Market Taker API,API_Systems
The RFS Market Taker API implements FIX Protocol Version 4.4.,FIX_Protocol
The RFS Market Taker API implements FIX Protocol Version 4.4.,Market_Systems
RFS Market Taker API,Market_Systems
This document provides an overview of the FIX services.,FIX_Protocol
This document contains detailed specifications of the utilized FIX messages.,FIX_Protocol
utilized FIX messages,FIX_Protocol
The API is implemented to meet FIX 4.4 standards.,API_Systems
The API,API_Systems
The API is implemented to meet FIX 4.4 standards.,FIX_Protocol
The purpose of this document is to provide an overview of the FIX services offered by 360 Treasury Systems for FX products.,FIX_Protocol
The document contains detailed specifications of the utilized FIX messages.,FIX_Protocol
Market data (FORWARD),Market_Systems
"Market Data quote - Forward, Two-Way",Market_Systems
Market order - Forward,Market_Systems
Market order - NDF Block Trade,Market_Systems
Fixing Sources for NDF/NDS,FIX_Protocol
Standard Header for FIX Messages,FIX_Protocol
Standard Footer for FIX Messages,FIX_Protocol
A FIX session is reset.,FIX_Protocol
FIX Session,FIX_Protocol
Sources are fixed for NDF and NDS.,FIX_Protocol
Spot Market data quotes,Market_Systems
Forward Market data quote,Market_Systems
Market order,Market_Systems
fixing source string,FIX_Protocol
360T's FIX offering,FIX_Protocol
FIX connection,FIX_Protocol
client's FIX engine,FIX_Protocol
FIX RFS Market Taker service,FIX_Protocol
FIX RFS Market Taker service,Market_Systems
360T's FIX offering acts as an intermediary.,FIX_Protocol
Request For Quote is available through a FIX connection.,FIX_Protocol
Sub-second latency is provided from a client's FIX engine to the 360T FX marketplace.,FIX_Protocol
The FIX RFS Market Taker service can be used to request market data.,FIX_Protocol
The FIX RFS Market Taker service can be used to request market data.,Market_Systems
The client acknowledges that 360T is not a counterparty to the trade and is neither responsible for the clients nor any third party's use of any information transmitted through the API nor is able to negotiate the details of any trade.,API_Systems
The client has to ensure that every execution request sent to the API receives a timely and definitive response from the API.,API_Systems
360T's FIX offering acts as an intermediary connecting Banks and other market participants.,FIX_Protocol
The aim is to provide sub-second latency from a client's FIX engine to the 360T FX marketplace.,FIX_Protocol
"When executing trades over the API, the client has to consider that the counterparties to each trade are the client and the respective provider.",API_Systems
360T is neither responsible for the clients nor any third party's use of any information transmitted through the API.,API_Systems
reconciling API trades with providers,API_Systems
reconciling API trade with Deal Blotter trade,API_Systems
client connection to 360T FIX services,FIX_Protocol
FIX engine connection process,FIX_Protocol
FIX engine tests,FIX_Protocol
The client is solely responsible for reconciling trades executed over the API with the respective providers and for contacting the providers directly in the event of any issues relating to specific trades.,API_Systems
The 360T Technical Support Help will be able to assist if a specific trade cannot be found in the Deal Blotter or if a specific trade initiated over the API needs to be reconciled with a trade shown in the Deal Blotter.,API_Systems
"Once interest has been established in connecting a client to 360T's FIX services, a project manager will be assigned.",FIX_Protocol
360T's FIX services,FIX_Protocol
"The project manager will manage the process of connecting the FIX engines of the two companies, performing FIX engine tests, and bringing the connection live.",FIX_Protocol
FIX engines,FIX_Protocol
A specific trade initiated over the API needs to be reconciled with a trade shown in the Deal Blotter.,API_Systems
Interest has been established in connecting a client to 360T's FIX services.,FIX_Protocol
The project manager will manage the process of connecting the FIX engines of the two companies.,FIX_Protocol
The project manager will perform FIX engine tests.,FIX_Protocol
FIX engine,FIX_Protocol
testing against FIX engine,FIX_Protocol
FIX,FIX_Protocol
FIX version 4.4,FIX_Protocol
transport layer FIXT.1.1,FIX_Protocol
application layer FIX.5.0,FIX_Protocol
official FIX specification,FIX_Protocol
http://www.fixprotocol.org,FIX_Protocol
We offer clients the ability to test against our FIX engine by connecting over the Internet.,FIX_Protocol
FIX is the communications protocol 360T and its clients will both have to support to fully communicate with each other.,FIX_Protocol
360T systems are compliant with FIX version 4.4.,FIX_Protocol
"In addition, some systems support transport layer FIXT.1.1 and application layer FIX.5.0.",FIX_Protocol
"The official FIX specification, available on [http://www.fixprotocol.org,](http://www.fixprotocol.org) should be consulted for in-depth descriptions.",FIX_Protocol
Some systems support transport layer FIXT.1.1 and application layer FIX.5.0.,FIX_Protocol
client disconnection from FIX session(s),FIX_Protocol
functionality of the API,API_Systems
FIX standard,FIX_Protocol
The non-availability of the 360T platform does not mean that the client will be disconnected from their FIX session(s) for the entire duration of the maintenance window.,FIX_Protocol
FIX session(s),FIX_Protocol
"If the client remains connected during the maintenance window, the functionality of the API will not be available.",API_Systems
Trading hours are defined in the MTF Rulebook.,Trading_Operations
"This includes cases when the fix connection goes down due to network connectivity or issues either on 360T or on client's side, when the client disconnects shortly after sending an order or any other cases that prevent 360T to send a execution report to the client at all or within the agreed timeout period.",FIX_Protocol
fix connection,FIX_Protocol
The following administrative system messages are supported as prescribed by the FIX standard.,FIX_Protocol
The business messages are used as prescribed by the FIX standard and interpreted in a way that all business relevant actions for RFS can be communicated.,FIX_Protocol
The API functionality is not available.,API_Systems
The fix connection goes down.,FIX_Protocol
the FIX API,API_Systems
the FIX API,FIX_Protocol
Utilization of the FIX Protocol,FIX_Protocol
FIX.4.4,FIX_Protocol
The general workflow for trading through the FIX API is shown in the following diagram 5.4.,API_Systems
The general workflow for trading through the FIX API is shown in the following diagram 5.4.,FIX_Protocol
FIX API,API_Systems
FIX API,FIX_Protocol
FIX Messages,FIX_Protocol
internal API version,API_Systems
Standard FIX value,FIX_Protocol
last field in every FIX message,FIX_Protocol
Either end of a FIX connection has not sent any data for HeartBtInt <108> seconds.,FIX_Protocol
Either end of a FIX connection will transmit a Heartbeat message.,FIX_Protocol
tag number of FIX field being referenced,FIX_Protocol
MsgType of FIX message being referenced,FIX_Protocol
termination of a FIX session,FIX_Protocol
The Logout message initiates or confirms the termination of a FIX session.,FIX_Protocol
FIX session,FIX_Protocol
currency for Money Market products,Market_Systems
Fixing Date for NDF,FIX_Protocol
far leg Fixing Date for NDS,FIX_Protocol
Market Taker's position,Market_Systems
"For Money Market products, the Symbol field defines the currency, specified using one 3-letter ISO 4217 code.",Market_Systems
Money Market products,Market_Systems
MaturityDate defines the Fixing Date for an NDF or the near leg for an NDS.,FIX_Protocol
Fixing Date,FIX_Protocol
MaturityDate2 defines the far leg Fixing Date for an NDS.,FIX_Protocol
far leg Fixing Date,FIX_Protocol
Trading will not be possible on indicative quotes.,Trading_Operations
Trading,Trading_Operations
Side defines if the Market Taker is in.,Market_Systems
Market Taker,Market_Systems
"For Money Market products, Symbol defines the currency.",Market_Systems
MaturityDate defines the Fixing Date for an NDF.,FIX_Protocol
Market data is requested (QuoteType = '0').,Market_Systems
Money Market (MM) products,Market_Systems
"For Money Market (MM) products, the Side<54> field is mandatory and defines if the client wants to lend or borrow money.",Market_Systems
Market data is requested.,Market_Systems
Fixing Date for NDF Block trade leg,FIX_Protocol
Both sides of an internal deal are exported to the same FIX session in an ITEX scenario.,FIX_Protocol
Tradingcapacity,Trading_Operations
FixingReference,FIX_Protocol
"FixingReference is a fixing reference for NDF, NDS, or NDF block.",FIX_Protocol
API version,API_Systems
MsgType of referenced FIX message,FIX_Protocol
'MarketData',Market_Systems
Market Taker's intention to buy or sell symbol,Market_Systems
"For Money Market products, the Symbol field defines the currency.",Market_Systems
Side defines if the Market Taker is intending to buy the given symbol for FX products.,Market_Systems
Side defines if the Market Taker is intending to sell the given symbol for FX products.,Market_Systems
Side defines if the Market Taker is intending to buy the given symbol for Base Metals products.,Market_Systems
Side defines if the Market Taker is intending to sell the given symbol for Base Metals products.,Market_Systems
"For Money Market products, the Side field is mandatory.",Market_Systems
"For Money Market products, the Side field defines if the client wants to lend money.",Market_Systems
"For Money Market products, the Side field defines if the client wants to borrow money.",Market_Systems
Market orders,Market_Systems
The value '1' represents a Market order.,Market_Systems
QuoteID must be populated with QuoteRequestId for Market orders.,Market_Systems
ProductType is required for Money Market products.,Market_Systems
The order is a Limit or Market order.,Market_Systems
The product is a Money Market or Base Metals product.,Market_Systems
Market Taker intention,Market_Systems
The Side field defines if the Market Taker is intending to buy or sell the given symbol.,Market_Systems
LegMaturityDate defines the Fixing Date for an NDF Block trade leg.,FIX_Protocol
this API version,API_Systems
Multilateral Trading Facility (MTF),Trading_Operations
Multilateral Trading Facility (MTF) ('64'),Trading_Operations
The value '64' represents a Multilateral Trading Facility (MTF).,Trading_Operations
Multilateral Trading Facility,Trading_Operations
PartyRole (452) is an integer field indicating '64' for 'Multilateral Trading Facility (MTF)'.,Trading_Operations
Fixing Date for an NDF,FIX_Protocol
client's action (for Money Market products),Market_Systems
The Side<54> field defines if the client has lent or borrowed money for Money Market products.,Market_Systems
These fields are available if support for sending UPI identifiers has been enabled for the FIX session.,FIX_Protocol
USIPrefix,FIX_Protocol
Unique Swap Identifier Prefix,FIX_Protocol
USIPrefix2,FIX_Protocol
USIPrefix is a Unique Swap Identifier Prefix for allocation.,FIX_Protocol
USIPrefix2 is a Unique Swap Identifier Prefix for allocation.,FIX_Protocol
Trading venue transaction identification code (TVTIC),Trading_Operations
support is enabled for FIX session,FIX_Protocol
These fields are only available if support for sending RTN identifiers has been enabled for the FIX session.,FIX_Protocol
Fixing reference,FIX_Protocol
"Fixing reference is for NDF, NDS, or NDF block.",FIX_Protocol
A product is supported in this API version.,API_Systems
USIPrefix is a Unique Swap Identifier Prefix.,FIX_Protocol
USIPrefix is for the near leg for Swaps.,FIX_Protocol
USIPrefix identifies the parent ticket when there are allocations.,FIX_Protocol
USIPrefix2 is a Unique Swap Identifier Prefix.,FIX_Protocol
USIPrefix2 is for the far leg for Swaps only.,FIX_Protocol
USIPrefix2 identifies the parent ticket when there are allocations.,FIX_Protocol
Fixing date,FIX_Protocol
Fixing dates,FIX_Protocol
The Fixing date is 2 days before the settlement date.,FIX_Protocol
The Fixing dates are 2 days before the settlement dates for both of the legs.,FIX_Protocol
The Fixing date (sent in the field MaturityDate) is 2 days before the settlement date.,FIX_Protocol
The Fixing dates (sent in the MaturityDate fields) is 2 days before the settlement dates for both of the legs.,FIX_Protocol
Quote Requests for Market data,Market_Systems
QuoteType for Market data requests,Market_Systems
OrderQty for Market data requests,Market_Systems
Currency for Market data requests,Market_Systems
Market data feed,Market_Systems
QuoteRequest for Spot Market data quotes,Market_Systems
MsgType in Spot Market data QuoteRequest,Market_Systems
QuoteReqID in Spot Market data QuoteRequest,Market_Systems
RefSpotDate in Spot Market data QuoteRequest,Market_Systems
ProductType in Spot Market data QuoteRequest,Market_Systems
NoRelatedSym in Spot Market data QuoteRequest,Market_Systems
Symbol in Spot Market data QuoteRequest,Market_Systems
QuoteType in Spot Market data QuoteRequest,Market_Systems
Side in Spot Market data QuoteRequest,Market_Systems
OrderQty in Spot Market data QuoteRequest,Market_Systems
SettlDate in Spot Market data QuoteRequest,Market_Systems
Account in Spot Market data QuoteRequest,Market_Systems
ExpireTime in Spot Market data QuoteRequest,Market_Systems
Market Data requests for other products,Market_Systems
Quote Requests for Market data are simpler than those for tradeable quotes.,Market_Systems
Market Data requests for other products are similar to this one.,Market_Systems
Market Data requests,Market_Systems
MarketData quotes are marked by QuoteType '0'.,Market_Systems
MarketData quotes,Market_Systems
MarketData quotes always have OrderQty set to 0.,Market_Systems
"MarketData quotes always have the constant string ""MarketData"" as Issuer.",Market_Systems
"constant string ""MarketData""",Market_Systems
The field Currency is not set for MarketData quotes.,Market_Systems
"MarketData quotes always have OrderQty set to 0 and the constant string ""MarketData"" as Issuer.",Market_Systems
FIX session reset,FIX_Protocol
360T FIX Session,FIX_Protocol
CFI code in API,API_Systems
Market Deposit,Market_Systems
A FIX session reset is performed according to the schedule defined in the following table.,FIX_Protocol
The CFI code passed in this API may not match that of the ISIN provided due to ANNA-DSB normalization.,API_Systems
The CFI code passed in this API may not match that of the ISIN provided.,API_Systems
ABSIRFIX01,FIX_Protocol
RSDFIX,FIX_Protocol
EMTAUAHFIX,FIX_Protocol
EMTA (ARS05)-BFIX EUR L080,FIX_Protocol
EMTA (ARS05)-BFIX EUR L130,FIX_Protocol
EMTA (ARS05)-BFIX EUR L160,FIX_Protocol
PTAX-BFIX EUR L080,FIX_Protocol
PTAX-BFIX EUR L130,FIX_Protocol
PTAX-BFIX EUR L160,FIX_Protocol
CLPOBS (CLP10)-BFIX EUR L080,FIX_Protocol
CLPOBS (CLP10)-BFIX EUR L130,FIX_Protocol
CLPOBS (CLP10)-BFIX EUR L160,FIX_Protocol
SAEC (CNY01)-BFIX EUR L080,FIX_Protocol
SAEC (CNY01)-BFIX EUR L130,FIX_Protocol
SAEC (CNY01)-BFIX EUR L160,FIX_Protocol
TRM (COP02)-BFIX EUR L080,FIX_Protocol
TRM (COP02)-BFIX EUR L130,FIX_Protocol
TRM (COP02)-BFIX EUR L160,FIX_Protocol
FBIL-BFIX EUR L080,FIX_Protocol
FBIL-BFIX EUR L130,FIX_Protocol
FBIL-BFIX EUR L160,FIX_Protocol
JISDOR (IDR04)-BFIX EUR L080,FIX_Protocol
JISDOR (IDR04)-BFIX EUR L130,FIX_Protocol
JISDOR (IDR04)-BFIX EUR L160,FIX_Protocol
KFTC18-BFIX EUR L080,FIX_Protocol
KFTC18-BFIX,FIX_Protocol
ABSIRFIX01-BFIX EUR L080,FIX_Protocol
ABSIRFIX01-BFIX EUR L130,FIX_Protocol
ABSIRFIX01-BFIX EUR L160,FIX_Protocol
ABSIRFIX01-WMCo 8am LDN,FIX_Protocol
ABSIRFIX01-WMCo 1pm LDN,FIX_Protocol
ABSIRFIX01-WMCo 4pm LDN,FIX_Protocol
PEN05-BFIX EUR L080,FIX_Protocol
PEN05-BFIX EUR L130,FIX_Protocol
PEN05-BFIX EUR L160,FIX_Protocol
PDSPESO-BFIX EUR L080,FIX_Protocol
PDSPESO-BFIX EUR L130,FIX_Protocol
PDSPESO-BFIX EUR L160,FIX_Protocol
RUB MOEX (RUB05)-BFIX EUR L080,FIX_Protocol
RUB MOEX (RUB05)-BFIX EUR L130,FIX_Protocol
RUB MOEX (RUB05)-BFIX EUR L160,FIX_Protocol
TAIFX1 (TWD03)-BFIX EUR L080,FIX_Protocol
TAIFX1 (TWD03)-BFIX EUR L130,FIX_Protocol
TAIFX1 (TWD03)-BFIX EUR L160,FIX_Protocol
EMTA (ARS05)-BFIX GBP L080,FIX_Protocol
PTAX-BFIX GBP L080,FIX_Protocol
CLOPBS-BFIX GBP L080,FIX_Protocol
SAEC-BFIX GBP L080,FIX_Protocol
COP TRM-BFIX GBP L080,FIX_Protocol
FEMF-BFIX GBP L080,FIX_Protocol
JISDOR-BFIX GBP L080,FIX_Protocol
FIBL-BFIX GBP L080,FIX_Protocol
KFTC18-BFIX GBP L080,FIX_Protocol
KZFXWA-BFIX GBP L080,FIX_Protocol
MYR PPKM-BFIX GBP L080,FIX_Protocol
PEN05-BFIX GBP L080,FIX_Protocol
PDSPESO-BFIX GBP L080,FIX_Protocol
RSDFIX-WMCo 8am LDN,FIX_Protocol
RSDFIX-WMCo 1pm LDN,FIX_Protocol
RSDFIX-WMCo 4pm LDN,FIX_Protocol
RSDFIX-BFIX GBP L080,FIX_Protocol
EURMYR has a list of supported fixing source values.,FIX_Protocol
EURPEN has a list of supported fixing source values.,FIX_Protocol
EURPHP has a list of supported fixing source values.,FIX_Protocol
EURRUB has a list of supported fixing source values.,FIX_Protocol
EURTWD has a list of supported fixing source values.,FIX_Protocol
GBPARS has a list of supported fixing source values.,FIX_Protocol
GBPBRL has a list of supported fixing source values.,FIX_Protocol
GBPCLP has a list of supported fixing source values.,FIX_Protocol
GBPCNY has a list of supported fixing source values.,FIX_Protocol
GBPCOP has a list of supported fixing source values.,FIX_Protocol
GBPEGP has a list of supported fixing source values.,FIX_Protocol
GBPIDR has a list of supported fixing source values.,FIX_Protocol
GBPINR has a list of supported fixing source values.,FIX_Protocol
GBPKRW has a list of supported fixing source values.,FIX_Protocol
GBPKZT has a list of supported fixing source values.,FIX_Protocol
GBPMYR has a list of supported fixing source values.,FIX_Protocol
GBPPEN has a list of supported fixing source values.,FIX_Protocol
GBPPHP has a list of supported fixing source values.,FIX_Protocol
GBPRSD has a list of supported fixing source values.,FIX_Protocol
supported fixing source strings,FIX_Protocol
Supported fixing source strings,FIX_Protocol
RUB MOEX fixing source strings,FIX_Protocol
TAIFX1 fixing source strings,FIX_Protocol
EMTAUAHFUIX fixing source strings,FIX_Protocol
VEB01 fixing source strings,FIX_Protocol
ABSIRFIX01 fixing source strings,FIX_Protocol
GBPRUB supports specific fixing source strings.,FIX_Protocol
RUB MOEX (RUB05)-BFIX GBP L080,FIX_Protocol
GBPTWD supports specific fixing source strings.,FIX_Protocol
TAIFX1-BFIX GBP L080,FIX_Protocol
GBPUAH supports specific fixing source strings.,FIX_Protocol
EMTAUAHFUIX-BFIX GBP L080,FIX_Protocol
GBPVES supports specific fixing source strings.,FIX_Protocol
VEB01-BFIX GBP L080,FIX_Protocol
GBPVND supports specific fixing source strings.,FIX_Protocol
ABSIRFIX01-BFIX GBP L080,FIX_Protocol
BRL*** supports specific fixing source strings.,FIX_Protocol
Table 11.2 describes supported fixing source strings for NDF/NDS.,FIX_Protocol
fixing source strings,FIX_Protocol
Version 1.1 fixed the tag number for QuoteType in QuoteRequest.,FIX_Protocol
Version 1.5 added Limit and Market orders.,Market_Systems
Version 1.9 added MarketData functionality.,Market_Systems
MarketData functionality,Market_Systems
Version 1.14 added an Important disclaimer for API clients.,API_Systems
API clients,API_Systems
The tag number for QuoteType in QuoteRequest was fixed.,FIX_Protocol
Limit and Market orders were added.,Market_Systems
MarketData functionality was added.,Market_Systems
An important disclaimer for API clients was added.,API_Systems
RFS API,API_Systems
A bug was fixed in the spec where Quote message contains RefSpotDate (7070) rather than ProductType (7071).,FIX_Protocol
Quote fields were fixed.,FIX_Protocol
A Trading Hours section was added that details the enforced MTF closing hours.,Trading_Operations
Trading Hours section,Trading_Operations
Support for the Singapore Regulated Market Operator (RMO) venue was added to the ExecutionVenueType field.,Market_Systems
Singapore Regulated Market Operator (RMO) venue,Market_Systems
FIX dictionary position,FIX_Protocol
"Fields were reordered in QuoteRequest <r>, Quote<s>, NewOrderSingle<d>, NewOrderMultileg<ab>, and ExecutionReport&lt;8&gt; messages according to their position in the FIX dictionary.",FIX_Protocol
FIX dictionary,FIX_Protocol
"Reordering fields in QuoteRequest, Quote, NewOrderSingle, NewOrderMultileg and ExecutionReport messages according to their position in FIX dictionary.",FIX_Protocol
Market Maker,Market_Systems
Market taker,Market_Systems
Market maker's counterpart relationship management administrator,Market_Systems
Market maker can be enabled for Auto-Accept Configuration.,Market_Systems
Market Maker Company,Market_Systems
Trading Relationship,Trading_Operations
Accepted Trading Relationship,Trading_Operations
BankClientP Trading Entity,Trading_Operations
360 Trading Networks LLC,Trading_Operations
"360 Trading Networks, Inc",Trading_Operations
ThreeSixty Trading Networks (India) Pvt Ltd,Trading_Operations
User Guide 360T Bridge: Order Book for Market Taker,Market_Systems
Market Order,Market_Systems
Fixing Order,FIX_Protocol
FIX Upload,FIX_Protocol
Market Orders are described.,Market_Systems
Fixing Orders are described.,FIX_Protocol
FIX files are uploaded.,FIX_Protocol
FIX file,FIX_Protocol
The user learns about fixing orders.,FIX_Protocol
The user learns about FIX upload.,FIX_Protocol
Partial Execution of a Large Market Order,Market_Systems
A Market Order is displayed.,Market_Systems
A Fixing Order is displayed.,FIX_Protocol
Partial Execution of a Large Market Order occurs.,Market_Systems
Large Market Order,Market_Systems
Supported Trading Workflow,Trading_Operations
360T User Guide RFS Market Taker Bridge,Market_Systems
Users can refer to 360T User Guide RFS Market Taker Bridge for more information on other Bridge features and the basic principles of the user interface.,Market_Systems
Market,Market_Systems
Market Takers choose from several options when placing orders to buy or sell FX OTC products.,Market_Systems
Market Takers,Market_Systems
Market Takers instruct a market maker to buy or sell with these orders.,Market_Systems
Market Rate,Market_Systems
Scotia Capital.TEST,API_Systems
Multilateral-Trading-Facility,Trading_Operations
Trading Capacity,Trading_Operations
broker FIX integration,FIX_Protocol
Market orders are the only type of trade orders which can be negotiated as a multidealer Request-For-Stream (RFS) request with multiple liquidity providers.,Market_Systems
Market orders can be placed as an order against a single liquidity provider depending on the underlying instrument.,Market_Systems
360T operates a Multilateral-Trading-Facility (MTF).,Trading_Operations
Multilateral-Trading-Facility (MTF),Trading_Operations
A Multilateral-Trading-Facility (MTF) is based on the RFS workflow.,Trading_Operations
"The underlying fields ""Trading Capacity"" and ""Investment Decision"" are mandatory and must be filled in if the MTF field has been flagged.",Trading_Operations
Clients can route orders to their Futures brokers for execution via broker FIX integration to 360T Bridge.,FIX_Protocol
360T operates a Multilateral-Trading-Facility (MTF) which is based on the RFS workflow.,Trading_Operations
"The underlying fields ""Trading Capacity"" and ""Investment Decision"" are mandatory and must be filled in.",Trading_Operations
Market Rate field,Market_Systems
Fixing Time,FIX_Protocol
Fixing Reference,FIX_Protocol
Fixing Authority,FIX_Protocol
Customized Fixing Date,FIX_Protocol
Fixing Authorities,FIX_Protocol
different fixing dates,FIX_Protocol
different fixing times,FIX_Protocol
Desired Fixing,FIX_Protocol
Custom Fixing,FIX_Protocol
A fixing order is an intraday order.,FIX_Protocol
fixing order,FIX_Protocol
A fixing order is used to buy a product at a specific rate.,FIX_Protocol
A fixing order is used to sell a product at a specific rate.,FIX_Protocol
fixing time,FIX_Protocol
The fixing reference can be set to a fixing authority.,FIX_Protocol
fixing reference,FIX_Protocol
fixing authority,FIX_Protocol
The fixing reference can be set to a customized fixing date.,FIX_Protocol
customized fixing date,FIX_Protocol
The ECB is a possible fixing authority.,FIX_Protocol
Reuters EBS is a possible fixing authority.,FIX_Protocol
WM/Reuters Intraday is a possible fixing authority.,FIX_Protocol
WM/Reuters Closing is a possible fixing authority.,FIX_Protocol
WM/Reuters Australia is a possible fixing authority.,FIX_Protocol
Bank of Canada Noon is a possible fixing authority.,FIX_Protocol
Different fixing authorities have different fixing dates.,FIX_Protocol
fixing authorities,FIX_Protocol
fixing dates,FIX_Protocol
Different fixing authorities have different times.,FIX_Protocol
A desired fixing is not included in the drop-down list.,FIX_Protocol
desired fixing,FIX_Protocol
"You can select ""Custom Fixing"".",FIX_Protocol
A fixing order is an intraday order to buy or sell a product at a specific time.,FIX_Protocol
The fixing reference can be set to a fixing authority or a customized fixing date.,FIX_Protocol
"Possible fixing authorities include ECB, Reuters EBS, WM/Reuters Intraday, WM/Reuters Closing, WM/Reuters Australia, and Bank of Canada Noon.",FIX_Protocol
Different fixing authorities have different fixing dates and times.,FIX_Protocol
"If a desired fixing is not in the drop-down list, you can select ""Custom Fixing"" and then define the required date and time.",FIX_Protocol
Market Swap Points,Market_Systems
Fixed (23),FIX_Protocol
LPs per Request (Fixed),FIX_Protocol
Gap between requests (Fixed),FIX_Protocol
Market Rate / Market Swap Points,Market_Systems
LPs per Request (Fixed ≥3),FIX_Protocol
Fixed number (LPs per Request),FIX_Protocol
Market Rate and Market Swap Points are an indicative feed of the prevailing market in that CCY and product.,Market_Systems
The fixed number must be greater than or equal to 3.,FIX_Protocol
fixed number,FIX_Protocol
"If 8 providers are clicked, the fixed number must be between 4 and 8.",FIX_Protocol
The LPs per Request option is set to Fixed (≥3).,FIX_Protocol
A fixed number is chosen for LPs per Request.,FIX_Protocol
The fixed number must be between 50-100% of the number of providers selected.,FIX_Protocol
The fixed number must be between 4 and 8.,FIX_Protocol
Fixed number of LPs per Request,FIX_Protocol
Fixed (≥3) LPs per Request,FIX_Protocol
Market Order (EMSO-1797),Market_Systems
A Market Order with reference EMSO-1795 was initialized by GroupH to buy EUR/USD FX Spot for 7.00 EUR.,Market_Systems
"A Market Order with reference EMSO-1795 was initialized by GroupH to buy EUR/USD FX Spot for 12,000.00 EUR.",Market_Systems
"A Market Order with reference EMSO-1795 was initialized by GroupH to sell EUR/USD FX Spot for 1,000,000.0 USD.",Market_Systems
"A Market Order with reference EMSO-1784 was initialized by GroupH to buy EUR/USD FX Spot for 1,000,000.0 EUR.",Market_Systems
"A Market Order with reference EMSO-1702 was initialized by GroupH to buy EUR/USD FX Spot for 1,000,000.0 EUR.",Market_Systems
"A Market Order with reference EMSO-1655 was initialized by GroupH to buy EUR/USD FX Spot for 7,006.00 EUR.",Market_Systems
"A Market Order with reference EMSO-1655 was initialized by GroupH to buy EUR/USD FX Spot for 4,009.00 EUR.",Market_Systems
A Market Order with reference EMSO-1869 was accepted by GroupH to buy EUR/INR FX Spot for 60.00 EUR with GTC expiry.,Market_Systems
A Market Order with reference EMSO-1868 was accepted by GroupH to sell EUR/GBP FX Fo MIF for 333.00 EUR with GTD expiry.,Market_Systems
Market Order EMSO-1868 was accepted for selling EUR/GBP.,Market_Systems
Market Order EMSO-1868 was accepted for selling EUR/CHF.,Market_Systems
Market Order EMSO-1868 was accepted for selling EUR/USD.,Market_Systems
Market Order EMSO-1859 was accepted for selling CAD/JPY.,Market_Systems
Market Order EMSO-1859 was accepted for buying CAD/JPY.,Market_Systems
Market Order EMSO-1842 is pending for selling EUR/GBP.,Market_Systems
Market Order EMSO-1835 is pending for selling EUR/USD.,Market_Systems
Market Order EMSO-1816 is pending for selling EUR/USD.,Market_Systems
Market Order EMSO-1816 was accepted for buying EUR/USD.,Market_Systems
Fixing Order EMSO-1317488,FIX_Protocol
Market Order EMSO-1312547,Market_Systems
Market Order EMSO-1312545,Market_Systems
Market Order EMSO-1312275,Market_Systems
Fixing Order EMSO-1312268,FIX_Protocol
Market Order EMSO-1312267,Market_Systems
Market Order EMSO-1312250,Market_Systems
Fixing Order EMSO-1317166,FIX_Protocol
A Fixing Order with ID EMSO-1317488 was initialized.,FIX_Protocol
A Market Order with ID EMSO-1312547 was initialized.,Market_Systems
A Market Order with ID EMSO-1312545 was initialized.,Market_Systems
"A Market Order with ID EMSO-1312275 for FxSpot was initialized to buy EUR and sell USD with a notional of 1,231.00 EUR.",Market_Systems
An OCO Order with ID EMSO-131227 was initialized with a Market Rate of 1.21147.,Market_Systems
A Fixing Order with ID EMSO-1312268 was initialized.,FIX_Protocol
A Market Order with ID EMSO-1312267 was initialized.,Market_Systems
A Market Order with ID EMSO-1312250 was initialized.,Market_Systems
A Fixing Order with ID EMSO-1317166 became active and was accepted with a Market Rate of 1.21151.,FIX_Protocol
A Fixing Order with ID EMSO-1317166 became active and was accepted with a Market Rate of 1.21151.,Market_Systems
A Fixing Order with ID EMSO-1317165 became active and was accepted.,FIX_Protocol
A Market Order with ID EMSO-1316214 became active and was accepted.,Market_Systems
Market Order EMSO-1312560,Market_Systems
Market Order EMSO-1312179,Market_Systems
Fixing Order EMSO-14674,FIX_Protocol
Market Order EMSO-1655,Market_Systems
Market Order EMSO-1316954,Market_Systems
Market Order EMSO-1299773,Market_Systems
Market Order EMSO-1798,Market_Systems
Market Order EMSO-1463,Market_Systems
Market Order EMSO-1869,Market_Systems
Market Order EMSO-1868,Market_Systems
Market Order EMSO-1859,Market_Systems
Market Order EMSO-1842,Market_Systems
Market Order EMSO-1835,Market_Systems
Market Order EMSO-1816,Market_Systems
Market Order EMSO-1816 is in a pending state.,Market_Systems
Market Order EMSO-1816 is in an accepted state.,Market_Systems
Market Order EMSO-1842 is in a pending state.,Market_Systems
"Market Order EMSO-1842 will expire on August 15, 2019.",Market_Systems
Fixing Order EMSO-1317165,FIX_Protocol
Market Order EMSO-1316214,Market_Systems
Market Order EMSO-1313543,Market_Systems
Fixing Order EMSO-1317166 is active and accepted by GroupE for a buy of USD/JPY.,FIX_Protocol
Fixing Order EMSO-1317165 is active and accepted by GroupE for a buy of GBP/USD.,FIX_Protocol
Market Order EMSO-1316214 is active and accepted by GroupE for a buy of EUR/CHF.,Market_Systems
Market Order EMSO-1313543 is active and accepted by GroupE for a buy of EUR/USD.,Market_Systems
A Market Order for FxForward was placed by GroupE and HSBC.DEMO.,Market_Systems
"A Market Order EMSO-45996 is pending with DB.DEMO, HSBC.DEMO, and JPM.GTX.",Market_Systems
same fixing reference,FIX_Protocol
Orders have the same fixing reference for NDF.,FIX_Protocol
Market Order EMSO-14673,Market_Systems
Market Order EMSO-14660,Market_Systems
Market Orders,Market_Systems
Market Orders consisting of different custom field values can be grouped.,Market_Systems
Market orders consisting of different expiry types (or expiry dates) can be grouped.,Market_Systems
Market orders with different pre-selected liquidity providers can only be grouped if they have at least one common bank.,Market_Systems
Market orders have at least one common bank.,Market_Systems
Market orders with different pre-selected liquidity providers can be grouped.,Market_Systems
Please note that it is possible to configure automatic placement of orders uploaded via an API.,API_Systems
valid Trading Date,Trading_Operations
Fixing,FIX_Protocol
Fixing reference for Fixing Orders,FIX_Protocol
Fixing reference for NDF market orders,FIX_Protocol
Effective Date should be a valid Trading Date.,Trading_Operations
Trading Date,Trading_Operations
Fixing is the fixing reference for Fixing Orders and for NDF market orders.,FIX_Protocol
Fixing Orders,FIX_Protocol
Fixing is not mandatory.,FIX_Protocol
Pre-agreed fixing,FIX_Protocol
Fixing Date 1,FIX_Protocol
Fixing Date 2,FIX_Protocol
fixed values,FIX_Protocol
Trading Capacity reflects the capacity in which the MTF member trades.,Trading_Operations
"Possible Custom Fields can be mandatory, have fixed values, or be text, dates, numbers, or boolean.",FIX_Protocol
Market Ord,Market_Systems
Fixing field,FIX_Protocol
no fixing orders required,FIX_Protocol
Fixing Date 1 field,FIX_Protocol
Fixing Date 2 field,FIX_Protocol
Trading Capacity field,Trading_Operations
XML API,API_Systems
The provider upload for XML API and FIX API is also supported.,API_Systems
The provider upload for XML API and FIX API is also supported.,FIX_Protocol
Market Order for FundE.2,Market_Systems
Market Order for FundE.1,Market_Systems
"document ""360T Order XML API""",API_Systems
Financial Information eXchange (FIX) protocol,FIX_Protocol
"document ""360T Order Market Taker FIX API""",API_Systems
"document ""360T Order Market Taker FIX API""",FIX_Protocol
"document ""360T Order Market Taker FIX API""",Market_Systems
"Users can refer to the document ""360T Order XML API"" for more information on the XML upload interface.",API_Systems
"A ""Financial Information eXchange"" (FIX) protocol can be utilized to upload orders into the Order Book tool.",FIX_Protocol
"Users can refer to the document ""360T Order Market Taker FIX API"" for more information.",API_Systems
"Users can refer to the document ""360T Order Market Taker FIX API"" for more information.",FIX_Protocol
"Users can refer to the document ""360T Order Market Taker FIX API"" for more information.",Market_Systems
"Market Order EMSO-2505 was accepted and is active for GroupH to buy 4,000.00 EUR/USD FX Spot with GTC expiry.",Market_Systems
Market Order EMSO-2505,Market_Systems
"Market Order EMSO-2504 was accepted and is active for GroupH to buy 10,000.00 EUR/USD FX Spot with GTC expiry.",Market_Systems
Market Order EMSO-2504,Market_Systems
"Market Order EMSO-2496. was accepted and is active for GroupH to buy 777,777.00 EUR/USD FX Spot with GTC expiry.",Market_Systems
Market Order EMSO-2496.,Market_Systems
"Market Order EMSO-2073 was accepted and is active for GroupH to buy 6,002.00 EUR/USD FX Spot with GTC expiry.",Market_Systems
Market Order EMSO-2073,Market_Systems
"Market Order EMSO-1914 was accepted and is active for GroupH to sell 10,000,000 EUR/USD FX Fo. MIT with GTC expiry.",Market_Systems
Market Order EMSO-1914,Market_Systems
"Market Order EMSO-1914 was accepted and is active for GroupH to buy 10,000,000 EUR/USD FX Fo MIF with GTC expiry.",Market_Systems
Market Order EMSO-1914 was accepted and is active for GroupH to buy 100.00 EUR/USD FX Fo MIF with GTC expiry.,Market_Systems
Market Order EMSO-1914 was accepted and is active for GroupH to sell 100.00 EUR/USD FX Fo MIF with GTC expiry.,Market_Systems
"360 Trading Networks LLC is located at Dubai International Financial Centre, Liberty House, Level 8, App. 810C, P.O. Box 482036, Dubai, United Arab Emirates.",Trading_Operations
The phone number for 360 Trading Networks LLC is +971 4 431 5134.,Trading_Operations
"360 Trading Networks, Inc. is located at 521 Fifth Avenue, 38th Floor, New York, NY 10175, USA.",Trading_Operations
"The phone number for 360 Trading Networks, Inc. is ****** 776 2900.",Trading_Operations
"The fax number for 360 Trading Networks, Inc. is ****** 776 2902.",Trading_Operations
"ThreeSixty Trading Networks (India) Pvt Ltd is located at Suite 9, Vatika Business Centre, Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India.",Trading_Operations
The phone number for ThreeSixty Trading Networks (India) Pvt Ltd is +91 22 4077 1437.,Trading_Operations
Market Maker Cockpit (MMC),Market_Systems
Additional and Fixed Spread,FIX_Protocol
Market Maker Cockpit Overview,Market_Systems
The Market Maker Cockpit is utilized.,Market_Systems
The Market Maker Cockpit is overviewed.,Market_Systems
Market Maker Cockpit,Market_Systems
The Market Maker Cockpit (MMC) allows automated risk management and pre-set actions with subsequent positions created by customer flows.,Market_Systems
The Market Maker Cockpit (MMC) allows automated risk management.,Market_Systems
The Market Maker Cockpit (MMC) allows pre-set actions.,Market_Systems
The Market Maker Cockpit (MMC) allows pre-set actions with these positions.,Market_Systems
use of 360T's Market Maker Cockpit (MMC),Market_Systems
Clients shall be solely responsible for the use of 360T's Market Maker Cockpit.,Market_Systems
360T's Market Maker Cockpit,Market_Systems
The Market Maker Cockpit consists four major components.,Market_Systems
The reference rate will be derived via price finding if not a fixed rate.,FIX_Protocol
fixed rate,FIX_Protocol
"If not a fixed rate, the reference rate will be derived via price finding.",FIX_Protocol
Market makers,Market_Systems
FIX API's,API_Systems
FIX API's,FIX_Protocol
"The outbound price is forwarded to clients via RFS, SST, and FIX API's.",API_Systems
"The outbound price is forwarded to clients via RFS, SST, and FIX API's.",FIX_Protocol
"The Market Maker Cockpit provides an overview of currency pairs being set up, current positions including profit and loss and various blotters to monitor client and hedge orders.",Market_Systems
"The outbound price is forwarded to clients via RFS, SST, and FIX API's",API_Systems
"The outbound price is forwarded to clients via RFS, SST, and FIX API's",FIX_Protocol
Trading Platform,Trading_Operations
Market Maker Cockpit start,Market_Systems
Market Maker Cockpit exit,Market_Systems
Pricing fixed spread,FIX_Protocol
Fixed spread,FIX_Protocol
Fixed outbound spread,FIX_Protocol
Fixed spreads,FIX_Protocol
defined fixed spread,FIX_Protocol
"Users can configure pricing additional or fixed spread, manual skew, and cutoff rules for managed instruments.",FIX_Protocol
fixed spread,FIX_Protocol
Fixed spreads are calculated around the mid-price of Inbound.,FIX_Protocol
"The system applies the defined fixed spread or the inbound spread if it is wider, when chosen.",FIX_Protocol
A fixed spread is chosen.,FIX_Protocol
"The system applies the defined fixed spread or, if wider, the inbound spread.",FIX_Protocol
"checkbox ""Allow Spread less than Market""",Market_Systems
"fixed spread of ""0""",FIX_Protocol
"The checkbox ""Allow Spread less than Market"" can be selected.",Market_Systems
"A fixed spread of ""0"" means that the inbound spread is used.",FIX_Protocol
Fix Core Price strategy,FIX_Protocol
Fix Core Price,FIX_Protocol
Fix Core Prices,FIX_Protocol
Fix Core Price will directly define a bid and ask outbound price for the selected channel.,FIX_Protocol
Fix Core Price can be used for pegged currencies.,FIX_Protocol
Markets are fast moving.,Market_Systems
Spread less than Market,Market_Systems
Market Best Bid,Market_Systems
All changes to the Market Maker Cockpit are stored in audit logs.,Market_Systems
Changes occur to the Market Maker Cockpit.,Market_Systems
360 Trading Networks LLC provides contact details for the United Arab Emirates.,Trading_Operations
"360 Trading Networks, Inc provides contact details for the USA.",Trading_Operations
FIXED AMOUNT BUTTONS,FIX_Protocol
Fixed Amount Buttons,FIX_Protocol
fixed amount buttons,FIX_Protocol
Standard fixed amount buttons,FIX_Protocol
View 'Fixed Amounts',FIX_Protocol
Standard fixed amount buttons can be removed by unchecking the View Fixed Amounts to gain screen space.,FIX_Protocol
standard fixed amount buttons,FIX_Protocol
View Fixed Amounts,FIX_Protocol
The View 'Fixed Amounts' is unchecked.,FIX_Protocol
The standard fixed amount buttons can be removed.,FIX_Protocol
The standard fixed amount buttons are not needed.,FIX_Protocol
The standard fixed amount buttons can be removed by unchecking the View 'Fixed Amounts'.,FIX_Protocol
suffix of Execution ID,FIX_Protocol
The RFS Boost is now fixed for the chosen tier until disabled by the user.,FIX_Protocol
"The suffix of the Execution ID in the table either contains an S (= stream, for example SO-10042173-S1)) or a R (= RFS Boost, for example SO-10042174-R1).",FIX_Protocol
suffix,FIX_Protocol
The suffix of the Execution ID in the table contains an S.,FIX_Protocol
The suffix of the Execution ID in the table contains an R.,FIX_Protocol
"The following resting order types can be uploaded: Limit Orders, Market Orders, Stop Orders, OCO Orders.",Market_Systems
Market prices are above the limit rate for an extended period.,Market_Systems
The header of the report shows Market or Limit execution for a given notional.,Market_Systems
Market execution,Market_Systems
Points To Market,Market_Systems
Percentage To Market,Market_Systems
API field,API_Systems
The Trading NDF/NDS and FX Options section is presented.,Trading_Operations
Trading NDF is discussed.,Trading_Operations
Trading NDS is discussed.,Trading_Operations
Trading FX Options is discussed.,Trading_Operations
360 Trading Networks Inc.,Trading_Operations
360 Trading Networks Inc. offers a SEF venue for transactions.,Trading_Operations
360 Trading Networks Inc. offers a SEF venue for such transactions.,Trading_Operations
Market Participant,Market_Systems
A Market Participant trades as a Market Maker under the 360T Swap Execution Facility.,Market_Systems
Trading under the 360T SEF becomes possible.,Trading_Operations
A Market Participant trades as Market Maker under the 360T Swap Execution Facility.,Market_Systems
"November 22, 2013 fixing date",FIX_Protocol
BRL01 fixing reference,FIX_Protocol
Trading with non US clients,Trading_Operations
Trading happens implicitly over configuration settings on the platform.,Trading_Operations
Trading with non US clients is off-SEF.,Trading_Operations
Market Makers,Market_Systems
360T requires Market Makers to designate collateralization indication on a client by client basis.,Market_Systems
360T requires Market Makers to designate collateralization indication.,Market_Systems
fixing date,FIX_Protocol
fixed digit length,FIX_Protocol
Non deliverable forwards are cash-settled by using the difference between the agreed forward rate and the spot rate on the fixing date.,FIX_Protocol
The upper limit is restricted to a fixed digit length.,FIX_Protocol
360T reserves the right to amend the fixed digit length whenever it is deemed to be necessary.,FIX_Protocol
The Fixing Date will automatically be populated by the system based on the spot date convention of the selected currencies.,FIX_Protocol
The Fixing Date can be changed to a date before the value date.,FIX_Protocol
The fixing date occurs.,FIX_Protocol
The Fixing Date is automatically populated by the system.,FIX_Protocol
The Fixing Date will be automatically populated by the system based on the spot date convention of the selected currencies.,FIX_Protocol
The Fixing Date,FIX_Protocol
The Fixing Date will automatically be populated by the system.,FIX_Protocol
"The near leg fixing date is Mon, 26. Aug 2013.",FIX_Protocol
near leg fixing date,FIX_Protocol
"The far leg fixing date is Tue, 03. Dec 2013.",FIX_Protocol
far leg fixing date,FIX_Protocol
The fixing reference is BRL01.,FIX_Protocol
"The fixing date for the Near Leg is Mon, 26. Aug 2013.",FIX_Protocol
"The fixing date for the Far Leg is Tue, 03. Dec 2013.",FIX_Protocol
"Fixing date Mon, 26. Aug 2013",FIX_Protocol
"Fixing date Tue, 03. Dec 2013",FIX_Protocol
Fixing reference BRL01,FIX_Protocol
Market maker,Market_Systems
"Action, Currency Couple, Effective Date, Maturity Date, Fixing Reference/Expiry",FIX_Protocol
Fixing Reference/Expiry,FIX_Protocol
"A Market Order (EMSO-2311) was placed to buy 6,699 EUR.",Market_Systems
"A Market Order (EMSO-2579) was placed to buy 10,800 EUR.",Market_Systems
Market Order FX Spot,Market_Systems
A Market Order FX Spot transaction was recorded.,Market_Systems
"Multilateral Trading Facility (""360T MTF"")",Trading_Operations
Market makers return streams of quotes.,Market_Systems
Market makers allow execution on the preferred quote.,Market_Systems
"As of January 3rd 2018, 360T operates a fully MiFID II compliant Multilateral Trading Facility (360T MTF) alongside the OTC trading platform.",Trading_Operations
MiFID II compliant Multilateral Trading Facility (360T MTF),Trading_Operations
360T operates a fully MiFID II compliant Multilateral Trading Facility.,Trading_Operations
360T is using a market data source feed from FIS (MarketMap).,Market_Systems
FIS (MarketMap),Market_Systems
360T makes no representation or warranty to users regarding the service of FIS (MarketMap).,Market_Systems
service of FIS (MarketMap),Market_Systems
360T does its best to provide the TWS with the FIS (MarketMap) service.,Market_Systems
FIS (MarketMap) service,Market_Systems
360T will not have any responsibility to maintain the service of FIS (MarketMap).,Market_Systems
"360T will not have any responsibility to supply any corrections, updates or releases in connection with the service of FIS (MarketMap).",Market_Systems
360T shall not have any liability to the Counterparty for the service of FIS (MarketMap).,Market_Systems
"360T is not liable for damages relating to users of the Bridge RFS Live Pricing by the use of, or inability to use, the service of FIS (MarketMap).",Market_Systems
360T makes no representation or warranty to users to the service of FIS (MarketMap).,Market_Systems
"360T will not have any responsibility to maintain the service of FIS (MarketMap) or to supply any corrections, updates or releases in connection therewith.",Market_Systems
"360T shall not have any liability to the Counterparty for the accuracy, reliability, performance, completeness, timeliness, continued availability or otherwise, of the service of FIS (MarketMap).",Market_Systems
"360T is not liable for special, indirect, exemplary, incidental, punitive or consequential damages relating to users of the Bridge RFS Live Pricing by the use of, or inability to use, the service of FIS (MarketMap).",Market_Systems
Money Market Loans,Market_Systems
The Up-to Request mode is available for Money Market Loans and Deposits.,Market_Systems
The product is Money Market Loans or Deposits.,Market_Systems
fixed forward rate,FIX_Protocol
Trading Capacity fields,Trading_Operations
Trading Capacity drop down menu,Trading_Operations
Trading Capacity and Investment Decision fields are mandatory.,Trading_Operations
The Trading Capacity drop down menu consists of specific values.,Trading_Operations
The Trading Capacity drop down menu consists of the following values: - 'DEAL' – dealing on own account - 'MTCH' – matched principal trading - 'AOTC' – any other capacity - 'NONE' – if none of the above values has been defined as default during the MTF onboarding process,Trading_Operations
fixing rate,FIX_Protocol
An FX Non-deliverable Forward is a contract between two counterparts that fixes the rate of exchange that applies on a notional FX forward transaction.,FIX_Protocol
"The counterparts do not exchange the notional amount at maturity of the trade, but they pay each other a cash amount corresponding to the difference between the FX NDF rate and an agreed FX spot rate at the contract's maturity, the fixing rate.",FIX_Protocol
Pre-Agreed Fixing,FIX_Protocol
The fixing reference can be entered or selected.,FIX_Protocol
"360T advises to use ""Pre-Agreed Fixing"".",FIX_Protocol
"""Pre-Agreed Fixing"" was agreed bilaterally with providers.",FIX_Protocol
The fixing reference can be entered or selected from the drop-down list if available.,FIX_Protocol
Money Market Request,Market_Systems
Market Standards,Market_Systems
Money Market Product Definition,Market_Systems
Money Market Product,Market_Systems
Money Market Deposit,Market_Systems
Money Market Loan,Market_Systems
Money Market Products,Market_Systems
"GroupH placed a Market Order to buy 1,010.00 EUR of FX Spot from Credit Suiss at 1.11750 EUR on August 16.",Market_Systems
"GroupH placed a Market Order to buy 1,052.00 EUR of FX Spot from Credit Suiss at 1.11750 EUR on August 16.",Market_Systems
GroupH placed a Market Order to buy 250.00 EUR of FX Spot from 360TBANK at 1.10157 EUR on August 16.,Market_Systems
original deal interest to new deal capital,API_Systems
capital and interest components,API_Systems
The interest of the original deal is automatically added to the capital of the new deal.,API_Systems
capital,API_Systems
A modification of the notional is displayed separately to have a distinct view of the capital and interest components of the new notional in case of compound interest.,API_Systems
There is a distinct view of the capital and interest components of the new notional.,API_Systems
ACT/365 (FIXED),FIX_Protocol
ACT/365 (FIXED) convention,FIX_Protocol
The ACT/365 (FIXED) convention calculates the actual number of days in the calculation period divided by 365 for payment.,FIX_Protocol
Custom Fields can have fixed values.,FIX_Protocol
The document provides an overview of the Market Maker Cockpit.,Market_Systems
The Market Maker Cockpit operates.,Market_Systems
The Market Maker Cockpit is logged in and started.,Market_Systems
The Market Maker Cockpit is exited.,Market_Systems
Login and Start the Market Maker Cockpit,Market_Systems
Exit the Market Maker Cockpit,Market_Systems
The Market Maker Cockpit (MMC) allows automated risk management and pre-set actions creating positions from customer flows.,Market_Systems
Clients are solely responsible for the use of 360T's Market Maker Cockpit (MMC).,Market_Systems
360T's Market Maker Cockpit (MMC),Market_Systems
The Market Maker Cockpit consists of four major components.,Market_Systems
The Market Maker Cockpit allows automated risk management and creates positions.,Market_Systems
The Market Maker Cockpit allows automated risk management.,Market_Systems
Market Maker Cockpit owner,Market_Systems
Any incoming Spot requests can be routed to Market Maker Cockpit.,Market_Systems
Any incoming Spot requests can be priced by Market Maker Cockpit.,Market_Systems
The reference rate is not a fixed rate.,FIX_Protocol
defined Market Data Source,Market_Systems
Spot components of the outright rates are received from Outbound quotes and forward points are received from the defined Market Data Source.,Market_Systems
Market Data Source,Market_Systems
"The Market Maker Cockpit provides an overview of currency pairs, current positions, profit and loss, and various blotters to monitor client and hedge orders.",Market_Systems
"Pricing uses additional or fixed spread, manual skew, slippage and cutoff rules.",FIX_Protocol
Fix Pips,FIX_Protocol
fixed outbound spread,FIX_Protocol
spread less than Market,Market_Systems
"Fixed outbound spread is defined in terms of PIPS, independent of the inbound spread.",FIX_Protocol
The system applies the defined fixed spread or the inbound spread if it is wider.,FIX_Protocol
"If wished, select the checkbox ""Allow Spread less than Market"".",Market_Systems
Markets are fast moving,Market_Systems
"For all requests priced by the MMC, a PTMM (Pre-Trade Mid-Market) rate is published across all FX derivative products and available to the client at the time of quoting as well as post execution.",Market_Systems
PTMM (Pre-Trade Mid-Market) rate,Market_Systems
MMC Position Upload API,API_Systems
"By default, blotter tracks the position due to client orders and hedge orders booked via MMC as well as any manual position update done via Trader on the application and/or automated position updates triggered via MMC Position Upload API.",API_Systems
RFS Market Taker API FIX Rules of Engagement,API_Systems
API Version 13.15,API_Systems
RFS Market Taker API FIX Rules of Engagement,FIX_Protocol
RFS Market Taker API FIX Rules of Engagement,Market_Systems
overview of FIX services,FIX_Protocol
Products currently supported by API,API_Systems
Market data,Market_Systems
Important Disclaimer for API Clients,API_Systems
FIX Engine Compatibility Testing,FIX_Protocol
FIX Protocol Levels,FIX_Protocol
Trading Hours,Trading_Operations
The RFS Market Taker API has version 13.15.,API_Systems
The RFS Market Taker API has version 13.15.,Market_Systems
The API implements FIX Protocol Version 4.4.,API_Systems
The API implements FIX Protocol Version 4.4.,FIX_Protocol
The purpose of this document is to provide an overview of the FIX services.,FIX_Protocol
360 Treasury Systems offers FIX services for FX products.,FIX_Protocol
This document provides an overview of FIX services.,FIX_Protocol
Request for Market data (FORWARD),Market_Systems
Sources for NDF and NDS are fixed.,FIX_Protocol
API Clients,API_Systems
The FIX RFS Market Taker service can also be used to request market data for all the products mentioned above.,FIX_Protocol
The FIX RFS Market Taker service can also be used to request market data for all the products mentioned above.,Market_Systems
The client acknowledges that 360T is not a counterparty to the trade and is neither responsible for the clients nor any third partys use of any information transmitted through the API nor is able to negotiate the details of any trade.,API_Systems
This raises the following issues for clients of the API.,API_Systems
client connection to 360T's FIX services,FIX_Protocol
The client is solely responsible for reconciling trades executed over the API with the respective providers.,API_Systems
A project manager will be assigned once interest has been established in connecting a client to 360T's FIX services.,FIX_Protocol
FIX protocol,FIX_Protocol
360T and its clients will both have to support FIX.,FIX_Protocol
The official FIX specification should be consulted.,FIX_Protocol
FIX session(s) during maintenance window,FIX_Protocol
functionality of API,API_Systems
The non-availability of the 360T platform does not mean the client will be disconnected from their FIX session(s) for the entire duration of the maintenance window.,FIX_Protocol
The functionality of the API will not be available.,API_Systems
Trading hours are between 7 AM Monday (New Zealand/Auckland) and 5 PM Friday (America/New York).,Trading_Operations
The FIX connection goes down due to network connectivity or issues on 360T or the client's side.,FIX_Protocol
Administrative system messages are supported as prescribed by the FIX standard.,FIX_Protocol
Business messages are used as prescribed by the FIX standard.,FIX_Protocol
The fix connection goes down due to network connectivity or issues either on 360T or on client's side.,FIX_Protocol
General workflow (for FIX API trading),API_Systems
General workflow (for FIX API trading),FIX_Protocol
every FIX message,FIX_Protocol
FIX connection end,FIX_Protocol
Either end of a FIX connection,FIX_Protocol
When either end of a FIX connection has not sent any data for HeartBtInt <108> seconds.,FIX_Protocol
tag number of FIX field,FIX_Protocol
FIX field,FIX_Protocol
MsgType of FIX message,FIX_Protocol
FIX message,FIX_Protocol
FIX session termination,FIX_Protocol
A FIX session terminates.,FIX_Protocol
"For Money Market (MM) products, the Symbol <55> field defines the currency.",Market_Systems
MaturityDate <541> defines the Fixing Date for an NDF.,FIX_Protocol
MaturityDate2 <7541> defines the far leg Fixing Date for an NDS.,FIX_Protocol
Trading is not possible on indicative quotes.,Trading_Operations
Side defines if the Market Taker is intending to.,Market_Systems
Trading will not be possible on those quotes.,Trading_Operations
The product is a Money Market product.,Market_Systems
The MaturityDate field defines the Fixing Date.,FIX_Protocol
The MaturityDate2 field defines the far leg Fixing Date.,FIX_Protocol
The Side<54> field is mandatory for Money Market (MM) products.,Market_Systems
same FIX session,FIX_Protocol
This is useful in an ITEX scenario where both sides of an internal deal are exported to the same FIX session.,FIX_Protocol
FixingReference (7075),FIX_Protocol
LastCapacity represents Tradingcapacity.,Trading_Operations
"FixingReference is a fixing reference for NDF, NDS or NDF block.",FIX_Protocol
The product must be supported in this API version.,API_Systems
Issuer specifies 'MarketData' if the quote request was a market data request.,Market_Systems
The Issuer specifies 'MarketData'.,Market_Systems
The Symbol<55> field defines the currency for Money Market products.,Market_Systems
The MaturityDate defines the Fixing Date for an NDF.,FIX_Protocol
Market Taker's intention,Market_Systems
Money Market (MM) Products,Market_Systems
Trading System,Trading_Operations
The Side<54> field defines if the client wants to lend or borrow money for Money Market (MM) products.,Market_Systems
QuoteID has to be populated with QuoteRequestId <131> of the referred quote request for Limit and Market orders.,Market_Systems
ProductType is required for Money Market and Base Metals products.,Market_Systems
Side defines if the Market Taker is intending to buy or sell the given symbol.,Market_Systems
"PartyRole has various possible values including Executing Firm, Clearing Firm, Liquidity provider, Systematic Internaliser, Multilateral Trading Facility, Execution Venue, Allocation Entity, and Reporting Party.",Trading_Operations
PartyRole is '64' for Multilateral Trading Facility (MTF).,Trading_Operations
It can be useful in an ITEX scenario where both sides of an internal deal are exported to the same FIX session.,FIX_Protocol
The Side<54> field defines if the client has lent or borrowed money for Money Market (MM) products.,Market_Systems
These fields are only available if support for sending UPI identifiers has been enabled for the FIX session.,FIX_Protocol
Support for sending UPI identifiers has been enabled for the FIX session.,FIX_Protocol
Fixing Date for an NDF Block trade leg,FIX_Protocol
"FixingReference provides a fixing reference for NDF, NDS or NDF block.",FIX_Protocol
Support for sending RTN identifiers has been enabled for the FIX session.,FIX_Protocol
The Fixing date is sent in the field MaturityDate.,FIX_Protocol
An example QuoteRequest for Spot Market data quotes is shown.,Market_Systems
NewOrderSingle message (Market order),Market_Systems
A FIX session reset is performed according to a defined schedule.,FIX_Protocol
The CFI code passed in this API may not match the ISIN provided.,API_Systems
The document presents a table of fixing sources for NDF and NDS.,FIX_Protocol
The document provides fixing sources for NDF and NDS.,FIX_Protocol
The SAEC (CNY01)-WMCo 8am LDN fixing is supported.,FIX_Protocol
The SAEC (CNY01)-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The SAEC (CNY01)-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The TRM (COP02)-WMCo 8am LDN fixing is supported.,FIX_Protocol
The TRM (COP02)-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The TRM (COP02)-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The FBIL-WMCo 8am LDN fixing is supported.,FIX_Protocol
The FBIL-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The FBIL-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The JISDOR (IDR04)-WMCo 8am LDN fixing is supported.,FIX_Protocol
The JISDOR (IDR04)-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The JISDOR (IDR04)-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The KFTC18-WMCo 8am LDN fixing is supported.,FIX_Protocol
The KFTC18-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The KFTC18-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The KZFXWA-WMCo 8am LDN fixing is supported.,FIX_Protocol
The KZFXWA-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The KZFXWA-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The ABSIRFIX01-WMCo 8am LDN fixing is supported.,FIX_Protocol
The ABSIRFIX01-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The ABSIRFIX01-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The PEN05-WMCo 8am LDN fixing is supported.,FIX_Protocol
The PEN05-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The PEN05-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The PDSPESO-WMCo 8am LDN fixing is supported.,FIX_Protocol
The PDSPESO-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The PDSPESO-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The RUB MOEX (RUB05)-WMCo 8am LDN fixing is supported.,FIX_Protocol
The RUB MOEX (RUB05)-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The RUB MOEX (RUB05)-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The TAIFX1 (TWD03)-WMCo 8am LDN fixing is supported.,FIX_Protocol
The TAIFX1 (TWD03)-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The TAIFX1 (TWD03)-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The EMTA (ARS05)-WMCo 8am LDN fixing is supported.,FIX_Protocol
The EMTA (ARS05)-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The EMTA (ARS05)-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The PTAX-WMCo 8am LDN fixing is supported.,FIX_Protocol
The PTAX-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The PTAX-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The CLPOBS-WMCo 8am LDN fixing is supported.,FIX_Protocol
The CLPOBS-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The CLPOBS-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The SAEC-WMCo 8am LDN fixing is supported.,FIX_Protocol
The SAEC-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The SAEC-WMCo 4pm LDN fixing is supported.,FIX_Protocol
The COP TRM-WMCo 8am LDN fixing is supported.,FIX_Protocol
The COP TRM-WMCo 1pm LDN fixing is supported.,FIX_Protocol
The COP TRM-WMCo 4pm LDN fixing is supported.,FIX_Protocol
Added Limit and Market orders.,Market_Systems
Added MarketData functionality.,Market_Systems
Added Important disclaimer for API clients.,API_Systems
Renamed to RFS API.,API_Systems
Fixed a bug in spec where Quote message contains RefSpotDate<7070> rather than ProductType<7071>.,FIX_Protocol
A Trading Hours section that details the enforced MTF closing hours was added.,Trading_Operations
USIPrefix<7605>,FIX_Protocol
USI Prefix,FIX_Protocol
USIPrefix2<7607>,FIX_Protocol
FixingReference<7075>,FIX_Protocol
Support for the Singapore Regulated Market Operator (RMO) venue was added to the ExecutionVenueType<7611> field.,Market_Systems
"FixingReference<7075> was added to QuoteRequest, NewOrderSingle, NewOrderMultileg, and ExecutionReport<8> messages.",FIX_Protocol
"FixingReference was added to QuoteRequest, NewOrderSingle, NewOrderMultileg and ExecutionReport messages.",FIX_Protocol
requesting relationships with Market Makers,Market_Systems
notification of Market Maker decisions,Market_Systems
Trading Limits tab,Trading_Operations
Trading limits are configured.,Trading_Operations
Trading Limits,Trading_Operations
Trading limits are defined.,Trading_Operations
Figure 18 shows the Trading Limits tab.,Trading_Operations
Trading limits are discussed.,Trading_Operations
Trading Methods,Trading_Operations
Trading Method,Trading_Operations
Market taker company,Market_Systems
Bank Baskets Trading Limits,Trading_Operations
Trading limits,Trading_Operations
setting up Trading Limits,Trading_Operations
Trading limits can be defined to restrict trading.,Trading_Operations
The configuration of Limit Rates is the basis to set up Trading and Settlement Limits.,Trading_Operations
Trading is restricted.,Trading_Operations
Trading and Settlement Limits are set up.,Trading_Operations
Bank Baskets Trading Limits section,Trading_Operations
"Market rates can be uploaded by clicking on the ""Update Rates"" button.",Market_Systems
Market rates,Market_Systems
Configuration functionality is available through the tab Trading Limits.,Trading_Operations
"Once defined, these product groups are available for all different types of limits, meaning Trading Limits, Settlement Limits and also for Autodealer Intraday Notional Limits if the company has an Autodealer.",Trading_Operations
Daily Trading Limit,Trading_Operations
A product can be a member of several groups for Trading Limits.,Trading_Operations
individual Daily Trading Limit,Trading_Operations
Both the individual Daily Trading Limit and the total limit of TOTAL LIMIT ALL INDIVIDUALS are checked to allow the execution of a transaction.,Trading_Operations
The individual Daily Trading Limit and the total limit of * TOTAL LIMIT ALL INDIVIDUALS * are checked.,Trading_Operations
"$1,000,000 Daily Trading Limit",Trading_Operations
10.000 Daily Trading Limit,Trading_Operations
"100,000 Daily Trading Limit",Trading_Operations
"1,000,000 Daily Trading Limit",Trading_Operations
UNLIMITED Daily Trading Limit,Trading_Operations
360 Trading Networks LLC is located in the United Arab Emirates.,Trading_Operations
"360 Trading Networks, Inc is located in the USA.",Trading_Operations
Daily Net Trading Limit,Trading_Operations
Daily Gross Trading Limit,Trading_Operations
Utilization changes arise due to API updates.,API_Systems
API updates,API_Systems
"Trades are initiated through different 360T applications such as Bridge, SST or EMS, as well as taker API interfaces.",API_Systems
taker API interfaces,API_Systems
Trading Limits profile,Trading_Operations
"The ""Trading Limits"" profile grants access to part of the configuration parameters.",Trading_Operations
"Trading Limits"" profile",Trading_Operations
Market participants want to allocate limits in a granular fashion.,Market_Systems
API users,API_Systems
API types of users,API_Systems
TODAY-1 MONTH Maily Net Trading Limit,Trading_Operations
TODAY-1 MONTH Daily Net Trading Limit,Trading_Operations
360TMMC.API,API_Systems
Trading activities,Trading_Operations
Daily Net Trading Limit algorithm allows clients to limit the net open position for the,Trading_Operations
Daily Net Trading Limit algorithm,Trading_Operations
Daily Gross Trading Limit algorithm,Trading_Operations
Daily Gross Trading Limit algorithm can be used to limit the total intraday trading volume for the current trading day.,Trading_Operations
"Potential Future Exposure, Net Daily Settlement Limit, Gross Daily Settlement Limit, Aggregate Net Settlement Limit, Aggregate Gross Settlement Limit, Daily Net Trading Limit",Trading_Operations
Daily Net Trading Limit algorithm limits the net amount of trades done on a single trading day.,Trading_Operations
Daily Net Trading Limit nets all the cashflow per currency account for all value dates across the credit horizon.,Trading_Operations
The Daily Net Trading Limit algorithm only nets the cashflows generated within the current trading day.,Trading_Operations
The Daily Net Trading Limit algorithm does not net all outstanding transactions.,Trading_Operations
Daily Net Trading Algorithm,Trading_Operations
Gross Trading Limit algorithm,Trading_Operations
Daily Gross Trading Algorithm,Trading_Operations
Daily Gross Trading algorithm,Trading_Operations
Gross Trading Limit algorithm limits the total gross volume of trades done on a single trading day.,Trading_Operations
Daily Gross Trading Limit sums the notional amount converted to company currency for all value dates across the credit horizon for current trading date.,Trading_Operations
"For Swap/NDS, Daily Gross Trading Algorithm only considers the largest amount of both of the legs.",Trading_Operations
Daily Net and Gross Trading algorithms,Trading_Operations
360T's Limits Monitor allows authorized users to reset the limit usage calculation for Daily Net and Gross Trading algorithms.,Trading_Operations
Daily Net Trading algorithms,Trading_Operations
Gross Trading algorithms,Trading_Operations
API connection,API_Systems
Customers with an API connection to 360T are not automatically informed when an order is still pending.,API_Systems
Clients who have an API connection to the Limits Monitor are able to send their own risk exposures either by overwriting what 360T calculates or by sending the incremental changes.,API_Systems
Limits Monitor displays the risk exposure changes arisen due to the updates done through API.,API_Systems
Value Date is sent by clients via API for DSL algorithms.,API_Systems
Currency is sent by client via API for net algorithms since Limits Monitor expects to receive currency exposure to be able to calculate available limit per each currency separately.,API_Systems
Exposure displayed in this table is the amount of change in the exposure for that respective value date and/or currency when API sends a new update.,API_Systems
"In case API sends an overwrite, Limits Monitor is calculating the incremental change and saving this as a delta exposure.",API_Systems
Clients with an API connection to 360T are not automatically informed when an order is pending.,API_Systems
Limits Monitor compensates for exposure updates sent through the API.,API_Systems
Currency is sent by client via API for net algorithms.,API_Systems
API sends a new update.,API_Systems
API sends an overwrite.,API_Systems
"Daily Gross Trading Limit of (1,000,000,000)",Trading_Operations
the API,API_Systems
"It contains an overview of the general workflow, as well as detailed specifications of the utilized FIX messages.",FIX_Protocol
Market Data quote,Market_Systems
Market order NDF Block Trade,Market_Systems
clients of API,API_Systems
360T is neither responsible for the clients use of any information transmitted through the API.,API_Systems
360T is neither responsible for any third party's use of any information transmitted through the API.,API_Systems
connecting FIX engines,FIX_Protocol
performing FIX engine tests,FIX_Protocol
The client is solely responsible for reconciling trades executed over the API with providers.,API_Systems
The 360T Technical Support Help will assist if a specific trade initiated over the API needs to be reconciled with a trade shown in the Deal Blotter.,API_Systems
A specific trade initiated over the API needs to be reconciled.,API_Systems
The project manager will manage the process of connecting the FIX engines.,FIX_Protocol
The project manager manages the process of connecting the FIX engines.,FIX_Protocol
The project manager performs FIX engine tests.,FIX_Protocol
The official FIX specification should be consulted for in-depth descriptions.,FIX_Protocol
client from FIX session(s),FIX_Protocol
API functionality,API_Systems
"When either end of a FIX connection has not sent any data for HeartBtInt <108> seconds, it will transmit a Heartbeat message.",FIX_Protocol
"For Money Market products, the Symbol 55 field defines the currency.",Market_Systems
The Side<54> field is mandatory for Money Market (MM) products and defines if the client wants to lend or borrow money.,Market_Systems
Tradingcapacity can be AOTC when its value is '1'.,Trading_Operations
Tradingcapacity can be MTCH when its value is '3'.,Trading_Operations
FixingReference is used.,FIX_Protocol
mandatory for Money Market products,Market_Systems
Trading system,Trading_Operations
'64' = Multilateral Trading Facility (MTF),Trading_Operations
"PartyRole has several possible values including Executing Firm, Clearing Firm, Liquidity provider, Systematic Internaliser, Multilateral Trading Facility, Execution Venue, Allocation Entity, and Reporting Party.",Trading_Operations
PartyRole '64' represents a Multilateral Trading Facility (MTF).,Trading_Operations
MaturityDate defines the Fixing Date for the near leg of an NDS.,FIX_Protocol
The Side<54> field defines if the client lent money for Money Market (MM) products.,Market_Systems
The Side<54> field defines if the client borrowed money for Money Market (MM) products.,Market_Systems
UPICode 3 and UPICode2 3 are only available if support for sending UPI identifiers has been enabled for the FIX session.,FIX_Protocol
The product is a Money Market (MM) product,Market_Systems
Support for sending UPI identifiers has been enabled for the FIX session,FIX_Protocol
Support for sending RTN identifiers enabled for FIX session,FIX_Protocol
This API version,API_Systems
An example QuoteRequest for Spot Market data quotes is presented.,Market_Systems
Spot Market data,Market_Systems
Quote Requests for Market data are simpler that those for tradeable quotes.,Market_Systems
MarketData quotes always have OrderQty set to 0 and the constant string 'MarketData' as Issuer.,Market_Systems
Forward Market data quote example,Market_Systems
API CFI code,API_Systems
BFIX EUR L130,FIX_Protocol
BFIX EUR L160,FIX_Protocol
BFIX EUR L080,FIX_Protocol
The SAEC (CNY01) BFIX EUR L130 fixing is available.,FIX_Protocol
The SAEC (CNY01) BFIX EUR L160 fixing is available.,FIX_Protocol
The SAEC (CNY01) WMCo 8am LDN fixing is available.,FIX_Protocol
The SAEC (CNY01) WMCo 1pm LDN fixing is available.,FIX_Protocol
The SAEC (CNY01) WMCo 4pm LDN fixing is available.,FIX_Protocol
The TRM (COP02) BFIX EUR L080 fixing is available.,FIX_Protocol
The TRM (COP02) BFIX EUR L130 fixing is available.,FIX_Protocol
The TRM (COP02) BFIX EUR L160 fixing is available.,FIX_Protocol
The TRM (COP02) WMCo 8am LDN fixing is available.,FIX_Protocol
The TRM (COP02) WMCo 1pm LDN fixing is available.,FIX_Protocol
List of supported fixing source values column,FIX_Protocol
The tag number for QuoteType in QuoteRequest was fixed,FIX_Protocol
Defining Fixing Reference Groups,FIX_Protocol
Money Market Time Period Groups,Market_Systems
Fixing Reference Groups,FIX_Protocol
Users can define Fixing Reference Groups.,FIX_Protocol
Money Market Time Period Groups are displayed.,Market_Systems
Fixing Reference Groups are displayed.,FIX_Protocol
Market Link Algorithm is defined.,Market_Systems
Market Link Algorithm,Market_Systems
A Trading Venue is selected.,Trading_Operations
Trading Venue,Trading_Operations
fixed amount,FIX_Protocol
Fixed amount margin,FIX_Protocol
This margin can be expressed as a fixed amount in the home currency.,FIX_Protocol
The fixed amount margin is added to the opposite amount.,FIX_Protocol
fixed amount margin,FIX_Protocol
The fixed amount margin is deducted from the opposite amount.,FIX_Protocol
The fixed amount margin is converted into a markup in pips of the effective rate.,FIX_Protocol
The fixed amount margin is converted into a markdown in pips of the effective rate.,FIX_Protocol
The fixed amount margin might have no impact due to the defined precision of the exchange rate and large notional amount of the trade.,FIX_Protocol
The fixed amount margin might lead to an abnormal effective rate due to the defined precision of the exchange rate and small notional amount of the trade.,FIX_Protocol
"This margin can be expressed in pips, percent, or a fixed amount.",FIX_Protocol
The fixed amount margin is added or deducted from the opposite amount and then converted.,FIX_Protocol
The fixed amount margin might have no impact or lead to an abnormal effective rate.,FIX_Protocol
Fixed Amount,FIX_Protocol
"The margin is expressed in pips, percent, or as a fixed amount in the home currency.",FIX_Protocol
"The Bid Spot Margin is expressed in pips, percent, or as a fixed amount.",FIX_Protocol
"The Offer Spot Margin is expressed in pips, percent, or as a fixed amount.",FIX_Protocol
fixed amount in home currency,FIX_Protocol
"The margin can be defined in terms of a) pips based on intervals of 3 decimal places, b) in percent with a maximum of 3 decimal places or c) as a fixed amount in the home currency.",FIX_Protocol
"The margin is expressed in pips based on intervals of one decimal place, in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.",FIX_Protocol
Fix Bid Price,FIX_Protocol
Fix Bid Price for FX Forward instruments,FIX_Protocol
Fix Offer Price,FIX_Protocol
Fix Offer Price for FX Forward instruments,FIX_Protocol
The margin is expressed in pips based on intervals of one decimal place in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.,FIX_Protocol
Fix Bid Price for FX Forward instruments refers to the Forward rate.,FIX_Protocol
Fix Offer Price for FX Forward instruments refers to the Forward rate.,FIX_Protocol
Money Market (MM) instruments,Market_Systems
Money Market instruments,Market_Systems
Period Groups are required specifically for Money Market instruments.,Market_Systems
The default time period group covers all possible maturities related to Money Market instruments.,Market_Systems
MM Period Groups can be used if Period Groups are required specifically for Money Market instruments.,Market_Systems
default fixing reference group,FIX_Protocol
all fixing references,FIX_Protocol
Groups of Fixing References,FIX_Protocol
Fixing References,FIX_Protocol
new Fixing Reference Group,FIX_Protocol
Available Fixing References,FIX_Protocol
Selected Fixing References,FIX_Protocol
configured Fixing Reference Groups,FIX_Protocol
Fixing Reference drop down menu,FIX_Protocol
Market Link routing,Market_Systems
route option Market Link,Market_Systems
The Fixing Reference Groups feature can be used.,FIX_Protocol
Fixing Reference Groups feature,FIX_Protocol
The default fixing reference group encompasses all fixing references.,FIX_Protocol
fixing references,FIX_Protocol
*** / *** Pre- Agreed Fixing,FIX_Protocol
Groups of Fixing References can be created.,FIX_Protocol
Groups of Fixing References can be removed.,FIX_Protocol
Groups of Fixing References can be renamed.,FIX_Protocol
A new Fixing Reference Group was created.,FIX_Protocol
Fixing Reference Group,FIX_Protocol
Fixing References can be assigned to this group.,FIX_Protocol
"Fixing References can be moved from ""Available Fixing References"" to ""Selected Fixing References"".",FIX_Protocol
"The configured Fixing Reference Groups are available in the ""Fixing Reference"" drop down menu.",FIX_Protocol
Configured Fixing Reference Groups,FIX_Protocol
"RFS Algorithm Groups are used for the route option ""Market Link"".",Market_Systems
"Fixing References can be assigned to this group by moving them from ""Available Fixing References"" to ""Selected Fixing References"" using the arrow buttons.",FIX_Protocol
Fixing Reference Groups are configured.,FIX_Protocol
"The configured Fixing Reference Groups are then available in the ""Fixing Reference"" drop down menu when defining the rules and rules templates for NDF and NDS.",FIX_Protocol
Market Link Algorithm definition,Market_Systems
"Market Link Algorithm ""Exotics""",Market_Systems
Market link providers,Market_Systems
"The Market Link Algorithm ""Exotics"" is used in a rule or a rule template.",Market_Systems
Trading Venue parameter,Trading_Operations
Market Link,Market_Systems
parameter 'Trading Venue',Trading_Operations
"The ""Trading Venue"" parameter offers the possibility to differentiate routing of a price request.",Trading_Operations
"parameter ""Trading Venue""",Trading_Operations
The 'Trading Venue' parameter is displayed.,Trading_Operations
The 'Trading Venue' parameter offers routing differentiation.,Trading_Operations
The 'Trading Venue' parameter is available for derivative products supported by the 360T MTF venue.,Trading_Operations
Selecting a Trading Venue,Trading_Operations
360T Market Maker Cockpit,Market_Systems
Market Link rule,Market_Systems
Market Link Provider,Market_Systems
Market Link Trading Venue,Market_Systems
Market Link Trading Venue,Trading_Operations
"The price basis is the 360T price feed from the 360T Market Maker Cockpit, Infotec, or a provider individual price feed through an adapter interface to the provider's pricing system.",Market_Systems
"This rule is used in combination with the selections under Market Link Provider, Market Link Trading Venue and Market Link Algorithm.",Market_Systems
"This rule is used in combination with the selections under Market Link Provider, Market Link Trading Venue and Market Link Algorithm.",Trading_Operations
This rule is only available for the product FX Forward and is a combination of Market Link and Pricing Server.,Market_Systems
"The price basis is either the 360T price feed from the 360T Market Maker Cockpit, Infotec (market data), or a provider individual price feed through an adapter interface to the provider's pricing system.",Market_Systems
Market Link strategy,Market_Systems
The Time Option Forward rule is a Market Link strategy.,Market_Systems
A Market Link strategy generates two standard FX Forward requests back-to-back.,Market_Systems
Market Link Provider is used to define the bank basket.,Market_Systems
Market Link Trading Venue is used to define execution location for the linked back-to-back request.,Market_Systems
Market Link Trading Venue is used to define execution location for the linked back-to-back request.,Trading_Operations
Market Link Algorithm allows selecting the Algorithm group.,Market_Systems
functionality for Market Participant trading as Market Taker,Market_Systems
This user manual describes functionality for the Market Participant trading as Market Taker under the 360T Swap Execution Facility.,Market_Systems
The difference between the agreed forward rate and the spot rate on the fixing date is used for settlement.,FIX_Protocol
User enters or selects the fixing reference from the drop-down list if available.,FIX_Protocol
"The Fixing Date will automatically be populated by the system based on the spot date convention of the selected currencies, generally Value date – 2.",FIX_Protocol
Enter or select the fixing reference from the drop-down list if available,FIX_Protocol
The Fixing Date is automatically populated by the system,FIX_Protocol
The fixing reference can be entered or selected from the drop down list.,FIX_Protocol
Fixing Date Fri 22-Nov-2013,FIX_Protocol
Fixing Reference CNY01,FIX_Protocol
Market participants,Market_Systems
Fixing Reference BRL01,FIX_Protocol
"A RFS matches one or several orders in terms of Action, Currency Couple, Effective Date, Maturity Date, Fixing Reference/Expiry.",FIX_Protocol
Trading Capacity and Investment Decision,Trading_Operations
Back2back Trading Capacities,Trading_Operations
360T MTF Identification of users behind API,API_Systems
360T MTF identifies users behind the API.,API_Systems
360T operates two separate Multi-Lateral Trading Facilities (MTFs).,Trading_Operations
Multi-Lateral Trading Facilities (MTFs),Trading_Operations
Trading on 360T MTF is not possible until the necessary data is entered into 360T's systems.,Trading_Operations
Trading on 360T MTF is possible.,Trading_Operations
"The ""Trading Capacity"" as well as the ""Investment decision within firm"" must be set in the ""Company MTF Details"" section.",Trading_Operations
"In the 'Company MTF Details' section, the 'Trading Capacity' as well as the 'Investment decision within firm' must be set.",Trading_Operations
Trading Capacity drop-down menu,Trading_Operations
Trading Capacity 'DEAL' is used,Trading_Operations
Trading Capacity 'AOTC' or 'MTCH' is used,Trading_Operations
The drop-down menu for Trading Capacity consists of three values.,Trading_Operations
The Investment decision within firm field is applicable if the Trading Capacity 'DEAL' is used.,Trading_Operations
Trading Capacity 'DEAL',Trading_Operations
The Investment decision within firm field can be set to NONE when Trading Capacity 'AOTC' or 'MTCH' is used.,Trading_Operations
Trading Capacity 'AOTC',Trading_Operations
Trading Capacity 'MTCH',Trading_Operations
"The ""Investment decision within firm"" field is only applicable if the Trading Capacity ""DEAL"" is used.",Trading_Operations
"When the Trading Capacity ""AOTC"" or ""MTCH"" is used, the ""Investment decision within firm"" field can be set to NONE.",Trading_Operations
Prefixes,FIX_Protocol
AutoDealer/API users,API_Systems
The process must be completed for all individual and AutoDealer/API users who require MTF trading.,API_Systems
The process of enabling users for MTF trading is completed for all individual and AutoDealer/API users who require MTF trading.,API_Systems
Trading Capacity PS,Trading_Operations
Trading Capacity via Market Maker API,API_Systems
Trading Capacity via Market Maker API,Market_Systems
Trading Capacity via Market Maker API,Trading_Operations
MTF transaction Trading Capacity,Trading_Operations
Trading Capacity B2B,Trading_Operations
The Trading Capacity PS value is non-editable.,Trading_Operations
"The Trading Capacity PS value is always set to ""DEAL"".",Trading_Operations
A bank does not provide the Trading Capacity via the Market Maker API.,API_Systems
A bank does not provide the Trading Capacity via the Market Maker API.,Market_Systems
A bank does not provide the Trading Capacity via the Market Maker API.,Trading_Operations
Market Maker API,API_Systems
Market Maker API,Market_Systems
"The Trading Capacity of an MTF transaction will automatically default to the value ""DEAL"".",Trading_Operations
"Trading Capacity B2B is set to ""DEAL"".",Trading_Operations
"Trading Capacity of MTF transaction as ""DEAL""",Trading_Operations
different Trading Capacity B2B,Trading_Operations
"If the transaction is hedged on MTF, 360T will record the Trading Capacity of the MTF transaction as ""DEAL"".",Trading_Operations
"If the Trading Capacity was set to ""AOTC"" or ""MTCH"", then the client must have a valid LEI configured on our systems.",Trading_Operations
It is feasible to set a different Trading Capacity B2B for a given client.,Trading_Operations
Trading Capacity AOTC,Trading_Operations
"360T records the Trading Capacity of the MTF transaction as ""DEAL"".",Trading_Operations
The client has no LEI and the Trading Capacity AOTC or MTCH cannot be used.,Trading_Operations
Market Taker APIs,API_Systems
Market Taker APIs,Market_Systems
Market Maker APIs,API_Systems
Market Maker APIs,Market_Systems
360T MTF via API,API_Systems
The Algo IDs are only applicable for Market Taker APIs or Market Maker APIs.,API_Systems
The Algo IDs are only applicable for Market Taker APIs or Market Maker APIs.,Market_Systems
Individual user details of manual traders who are using a proprietary trading system connected to 360T MTF via an API are also required to be captured in the tool.,API_Systems
"Such manual traders are then identified by their 360T specific short code via the API, as shown in [Figure 19.](#page-20-0)",API_Systems
Such manual traders are then identified by their 360T specific short code via the API.,API_Systems
venue via API,API_Systems
External users may be identified as an EDM or IDM on the 360T MTF venue or access the venue via an API.,API_Systems
PREFIX.Lastname,FIX_Protocol
PREFIX,FIX_Protocol
prefix,FIX_Protocol
The PREFIX can be selected via the dropdown menu.,FIX_Protocol
More than one prefix is configured for an entity.,FIX_Protocol
Prefixes to names,FIX_Protocol
Prefixes to surnames,FIX_Protocol
"Prefixes to names that denote titles, position, profession, or academic qualifications, such as Dr., must be removed.",FIX_Protocol
"Prefixes to surnames not on the specified list or prefixes attached to names like McDonald, MacChrystal, O'Brian, and O'Neal should not be removed.",FIX_Protocol
"The specified list of prefixes is not case sensitive and includes 'am', 'auf', 'auf dem', 'aus der', 'd', and 'da'.",FIX_Protocol
"Prefixes denote titles, position, profession or academic qualifications.",FIX_Protocol
These prefixes are to be removed.,FIX_Protocol
A prefix is not included in the below list or is attached to a name.,FIX_Protocol
The prefix should not be removed.,FIX_Protocol
Prefixes are processed.,FIX_Protocol
capital letters,API_Systems
Prefix,FIX_Protocol
Lower case letters must be transformed to capital letters.,API_Systems
A prefix was removed.,FIX_Protocol
Order Market Taker XML API,API_Systems
Order Market Taker XML API,Market_Systems
Order Market Taker XML API Rules of Engagement,API_Systems
Order Market Taker XML API Rules of Engagement,Market_Systems
The document is titled Order Market Taker XML API Rules of Engagement.,API_Systems
The document is titled Order Market Taker XML API Rules of Engagement.,Market_Systems
The API Version is 2.1.12.,API_Systems
API Version,API_Systems
The Fixing References subsection is on page 16.,FIX_Protocol
Fixing References subsection,FIX_Protocol
calls to Order XML API,API_Systems
communication with Order XML API,API_Systems
The Order Market Taker XML API allows customers to upload orders into different applications on the 360T platform.,API_Systems
The Order Market Taker XML API allows customers to upload orders into different applications on the 360T platform.,Market_Systems
All calls to the Order XML API should be made with a HTTP call over secure TCP socket.,API_Systems
Order XML API,API_Systems
The communication with the Order XML API is done through a secure HTTPS connection.,API_Systems
development environment of Order XML API,API_Systems
urn:360t:orderapi:tradeIntentionSubmit,API_Systems
IP ************ port 6445 is the location of the development environment of the Order XML API.,API_Systems
The Order XML API can use REST or SOAP interface for communication.,API_Systems
This is the location of the development environment of the Order XML API and your system should be able to access it.,API_Systems
"Provide 360T with the IP address or range of addresses of your system or proxy, through which you'll be accessing the Order XML API.",API_Systems
fixedFeeType,FIX_Protocol
fixedFeeCurrency,FIX_Protocol
fixedFeeValue,FIX_Protocol
fixedFeeCustodian,FIX_Protocol
fixedFeeCustodianCheck,FIX_Protocol
fixingOrderDate,FIX_Protocol
date for a fixing order,FIX_Protocol
missing fixingOrderDate,FIX_Protocol
fixingOrderReference is NONE,FIX_Protocol
fixingOrderReference,FIX_Protocol
fixingOrderDate is missing,FIX_Protocol
fixingOrderDate when NONE is selected,FIX_Protocol
An intention can be executed as an RFQ call or a market order if the fixing order date is missing.,FIX_Protocol
fixing order date,FIX_Protocol
Time should be specified if fixingOrderReference is NONE.,FIX_Protocol
The fixing reference field is ignored if fixingOrderDate is missing.,FIX_Protocol
fixing reference field,FIX_Protocol
Times defined for the selected mnemonic will be added to the date specified in fixingOrderDate.,FIX_Protocol
The time is taken from fixingOrderDate if NONE is selected.,FIX_Protocol
The fixingOrderReference is NONE.,FIX_Protocol
Time should be specified for the fixing order date.,FIX_Protocol
The fixingOrderDate is missing.,FIX_Protocol
The fixingOrderReference field is ignored.,FIX_Protocol
A mnemonic is selected for fixingOrderReference.,FIX_Protocol
The times defined for the selected mnemonic will be added to the date specified in fixingOrderDate.,FIX_Protocol
NONE is selected for fixingOrderReference.,FIX_Protocol
The time would be taken from fixingOrderDate.,FIX_Protocol
360T's EU Multilateral Trading Facility,Trading_Operations
Regulated Market Operator,Market_Systems
fixingDate,FIX_Protocol
fixingReference,FIX_Protocol
fixingDate specifies the fixing date in YYYY-MM-DD format.,FIX_Protocol
fixingReference specifies the fixing reference.,FIX_Protocol
fixing,FIX_Protocol
Fixing references,FIX_Protocol
API Mnemonic,API_Systems
Daily fixing time,FIX_Protocol
"When creating a fixing order, the fields fixingDate and the fixing should be provided.",FIX_Protocol
Fixing needs to be one of the listed values.,FIX_Protocol
Fixing needs to be one of the specified values.,FIX_Protocol
urn:360t:orderapi:tradeIntentionStatus,API_Systems
Trade Intention Cancellation API,API_Systems
urn:360t:orderapi:tradeIntentionCancel,API_Systems
Trade Intention Accepting API,API_Systems
urn:360t:orderapi:tradeIntentionAccept,API_Systems
Fetching Resulting Trades API,API_Systems
A cancel request can be sent using this API for a trade intention that meets certain conditions.,API_Systems
A trade intention can be Accepted using this API if it is in Delivered or Sent state.,API_Systems
A cancel request could be sent using this API.,API_Systems
It can be Accepted using this API.,API_Systems
urn:360t:orderapi:tradeIntentionTrades,API_Systems
urn:360t:orderapi:tradeIntentionStatusTrades,API_Systems
The SOAP action for trade query is urn:360t:orderapi:tradeIntentionTrades.,API_Systems
The SOAP action for the combined request is urn:360t:orderapi:tradeIntentionStatusTrades.,API_Systems
urn:360t:orderapi:tradeIntentionAmend,API_Systems
urn:360t:orderapi:tradeIntentionWithdraw/?id=123456&id=123456a,API_Systems
360T Order XML API access points,API_Systems
The Amend request is urn:360t:orderapi:tradeIntentionAmend.,API_Systems
The SOAP action for the Withdraw request is urn:360t:orderapi:tradeIntentionWithdraw/?id=123456&id=123456a.,API_Systems
These should be used when submitting requests to the 360T Order XML API access points.,API_Systems
fixing sources for NDF/NDS,FIX_Protocol
Fixing sources for NDF/NDS,FIX_Protocol
ARS01 fixing source,FIX_Protocol
ARS02 fixing source,FIX_Protocol
EMTA (ARS05) fixing source,FIX_Protocol
BRL01 fixing source,FIX_Protocol
BRL02 fixing source,FIX_Protocol
BRL03 fixing source,FIX_Protocol
BRL10 fixing source,FIX_Protocol
BRL11 fixing source,FIX_Protocol
PTAX (BRL09) fixing source,FIX_Protocol
KFTC18-BFIX EUR L130,FIX_Protocol
KFTC18-BFIX EUR L160,FIX_Protocol
KZFXWA-BFIX EUR L080,FIX_Protocol
KZFXWA-BFIX EUR L130,FIX_Protocol
KZFXWA-BFIX EUR L160,FIX_Protocol
Supported fixing source string for NDF/NDS,FIX_Protocol
RSDFIX-WMCo,FIX_Protocol
RSDFIX BFIX GBP L080,FIX_Protocol
ABSIRFIX01-WMCo,FIX_Protocol
Money and Market Deposit,Market_Systems
this API,API_Systems
"Due to ANNA-DSB normalization, the CFI code in this API may not match the ISIN provided.",API_Systems
Order API Actions,API_Systems
360T Order API Services,API_Systems
XML MT Order API Actions,API_Systems
"""urn:360t:orderapi:tradeIntentionSubmit""",API_Systems
"""urn:360t:orderapi:tradeIntentionAccept""",API_Systems
"""urn:360t:orderapi:tradeIntentionCancel""",API_Systems
"""urn:360t:orderapi:tradeIntentionStatus""",API_Systems
"""urn:360t:orderapi:tradeIntentionTrades""",API_Systems
"""urn:360t:orderapi:tradeIntentionStatusTrades""",API_Systems
Order XML API Connectivity,API_Systems
Fixing and stop orders,FIX_Protocol
BNA fixing reference,FIX_Protocol
Fixing and stop orders were implemented.,FIX_Protocol
BNA fixing reference was added.,FIX_Protocol
new Order API,API_Systems
Order API,API_Systems
available fixing references,FIX_Protocol
fixing orders,FIX_Protocol
Moved to new Order API added timeInforce,API_Systems
Added WM/Reuters fixing reference,FIX_Protocol
WM/Reuters fixing reference,FIX_Protocol
Updating available fixing references for fixing orders.,FIX_Protocol
Added support for uploading orders to Singapore Regulated Market Operator (RMO) venue.,Market_Systems
User Creation Wizard Trading Type Selection,Trading_Operations
Trading rights,Trading_Operations
User Creation Wizard: Trading Type Selection,Trading_Operations
Trading Type,Trading_Operations
Prefixes Overview,FIX_Protocol
Create/Delete prefix,FIX_Protocol
Creating or deleting a prefix requires a 360T CR.,FIX_Protocol
360 Trading Networks LLC can be reached by phone.,Trading_Operations
"360 Trading Networks, Inc can be reached by phone.",Trading_Operations
"360 Trading Networks, Inc can be reached by fax.",Trading_Operations
ThreeSixty Trading Networks (India) Pvt Ltd is located in India.,Trading_Operations
ThreeSixty Trading Networks (India) Pvt Ltd can be reached by phone.,Trading_Operations
This user manual describes the functionality available for the Market Participant trading as Market Taker under the 360T Swap Execution Facility.,Market_Systems
Market surveillance,Market_Systems
The spot rate is on the fixing date.,FIX_Protocol
The fixing date is usually 2 working days before the settlement date.,FIX_Protocol
The maximum notional amount is restricted to a fixed digit length.,FIX_Protocol
The fixed digit length is determined by 306T.,FIX_Protocol
Access is through the Trading System.,Trading_Operations
The Fixing Date will automatically be populated by the Trading System.,FIX_Protocol
The Fixing Date will automatically be populated by the Trading System.,Trading_Operations
The Fixing Date is generally two days before the value date.,FIX_Protocol
The Trading System will automatically send the requester's RFQ to the liquidity providers.,Trading_Operations
all users of Trading System,Trading_Operations
The Trading System will automatically send the requester's RFQ to the liquidity providers selected in advance by the requester on a disclosed basis.,Trading_Operations
Notification of execution is not sent to all users of the Trading System.,Trading_Operations
The exercise date is automatically generated by the Trading System.,Trading_Operations
The maximum notional amount is restricted to a fixed digit length determined by 360T.,FIX_Protocol
Users can access these parameters through the Trading System.,Trading_Operations
The exercise date is automatically generated by the Trading System,Trading_Operations
The Trading System sends the requester's RFQ to liquidity providers,Trading_Operations
2 special execution modes are possible in the Trading System.,Trading_Operations
Market participants without relationship,Market_Systems
Fixed digit length,FIX_Protocol
The fixed digit length is determined by 360T based upon credit and market risk considerations.,FIX_Protocol
360 Trading Networks LLC has a location in the United Arab Emirates.,Trading_Operations
360 Trading Networks LLC provides a phone number.,Trading_Operations
"360 Trading Networks, Inc has a location in the USA.",Trading_Operations
"360 Trading Networks, Inc provides a phone number.",Trading_Operations
The system supports additional and fixed spreads.,FIX_Protocol
fixed spreads,FIX_Protocol
The Market Maker Cockpit (MMC) is utilized.,Market_Systems
The Market Maker Cockpit overview is presented.,Market_Systems
Use of 360T's Market Maker Cockpit (MMC),Market_Systems
Clients shall be solely responsible for the use of 360T's Market Maker Cockpit (MMC).,Market_Systems
The Market Maker Cockpit allows automated risk management and pre-set actions.,Market_Systems
The outbound price is forwarded to clients via FIX API's.,API_Systems
The outbound price is forwarded to clients via FIX API's.,FIX_Protocol
Market Maker Cockpit owner can price incoming requests for following products.,Market_Systems
Any incoming Spot requests negotiated as RFS can be routed to Market Maker Cockpit.,Market_Systems
Any incoming Spot requests negotiated as RFS can be priced by Market Maker Cockpit.,Market_Systems
Any incoming Spot requests negotiated as SEP can be routed to Market Maker Cockpit.,Market_Systems
Any incoming Spot requests negotiated as SEP can be priced by Market Maker Cockpit.,Market_Systems
Forward points are received from the defined Market Data Source.,Market_Systems
"The Market Maker Cockpit provides an overview of currency pairs being set up, current positions including profit and loss, and various blotters to monitor client (requester) and hedge orders.",Market_Systems
Market Maker Cockpit login,Market_Systems
"Checkbox ""Allow Spread less than Market""",Market_Systems
"Fixed spread of ""0""",FIX_Protocol
The system applies the defined fixed spread or the inbound spread if wider.,FIX_Protocol
Users can select the checkbox 'Allow Spread less than Market'.,Market_Systems
checkbox 'Allow Spread less than Market',Market_Systems
A fixed spread of '0' means that the inbound spread is used.,FIX_Protocol
The Fixed spread mode is chosen.,FIX_Protocol
The user selects the checkbox 'Allow Spread less than Market'.,Market_Systems
A fixed spread of '0' is set.,FIX_Protocol
Allow Spread less than Market,Market_Systems
A PTMM (Pre-Trade Mid-Market) rate is published across all FX derivative products and available to the client at the time of quoting as well as post execution for all requests priced by the MMC.,Market_Systems
Market Best Ask,Market_Systems
All changes to the Market Maker Cockpit or any action by a user or triggered by the engine are stored in audit logs.,Market_Systems
FIXED COSTS / FIXED FEES,FIX_Protocol
Mark to Market Alert compliance,Market_Systems
Fixed costs and fixed fees are present.,FIX_Protocol
Fixed Costs,FIX_Protocol
Fixed Fees,FIX_Protocol
A Mark to Market Alert is issued.,Market_Systems
Mark to Market Alert,Market_Systems
Fixing sources are defined for NDF/NDS.,FIX_Protocol
Fixing Sources,FIX_Protocol
fixed costs rules,FIX_Protocol
fixed fee definition,FIX_Protocol
Mark to Market Alert configuration,Market_Systems
The Mark to Market Alert configuration is displayed.,Market_Systems
fixed fees,FIX_Protocol
A deal ticket with fixed fees is displayed.,FIX_Protocol
API Upload method,API_Systems
Fixed Fees enablement,FIX_Protocol
Fixed Costs tab,FIX_Protocol
"The main tabs are Workflow Rules, Portfolio Rules, Fixed Costs, Views, Compliance, Amend Settings, User Administration, Audit log and Change Request.",FIX_Protocol
"The tabs Portfolio Rules, Fixed Costs, Compliance and Amend Settings depend on the company configuration.",FIX_Protocol
"The tabs Portfolio Rules, Fixed Costs, Compliance and Amend Settings are only available if activated.",FIX_Protocol
API Users,API_Systems
Selected Users (API),API_Systems
All Users (API),API_Systems
Fixed Costs (Step 10),FIX_Protocol
The problems can be fixed.,FIX_Protocol
Fixing orders,FIX_Protocol
"The example in Figure 13 shows a restriction for OCO, fixing, algo and limit orders.",FIX_Protocol
Market order strategy,Market_Systems
"This view shows all approved spot, forward or swap orders with order strategy = Market.",Market_Systems
Auto-commit staging portfolio parameter (Fixing Date-based),FIX_Protocol
time before Fixing Date / Time reached,FIX_Protocol
Auto-commit staging portfolio before a specified amount of time before the Fixing Date / Time,FIX_Protocol
The configured time before the Fixing Date / Time is reached.,FIX_Protocol
Fixing Date / Time,FIX_Protocol
This configuration is developed for fixing orders.,FIX_Protocol
Orders in the portfolio will be committed at 3:55pm for the WMR Closing London fixing at 4pm GMT.,FIX_Protocol
WMR Closing London fixing,FIX_Protocol
The spot risk is placed as fixing order against the winning bank of the forward risk.,FIX_Protocol
Fixed Costs feature enabled,FIX_Protocol
The Fixed Costs tab is visible to EMS setups.,FIX_Protocol
Users do not see the Fixed Costs tab.,FIX_Protocol
The Fixed Costs feature is enabled.,FIX_Protocol
The Fixed Costs tab is visible.,FIX_Protocol
The Fixed Costs option is deactivated.,FIX_Protocol
The Fixed Costs tab is not visible.,FIX_Protocol
You do not see the Fixed Costs tab.,FIX_Protocol
fixed costs,FIX_Protocol
Fixed fees per ticket,FIX_Protocol
Fixed fees per million,FIX_Protocol
Fixed Fee configuration,FIX_Protocol
"One can define how the fixed costs should be applied in the ""Action"" column.",FIX_Protocol
Fixed Fee rules,FIX_Protocol
configured Fixed Costs rules,FIX_Protocol
uploading different fixed fees,FIX_Protocol
different fixed fees,FIX_Protocol
Fixed Fee configuration options,FIX_Protocol
Fixed costs,FIX_Protocol
fixed fee sorting,FIX_Protocol
The Fixed Fee rules work from top to bottom.,FIX_Protocol
The configured Fixed Costs rules can be overwritten by uploading different fixed fees for a single order.,FIX_Protocol
Fixed Fee configuration options cannot be done by the EMS administrator but only by 360T CAS Team.,FIX_Protocol
It should be decided for the initial EMS setup if the Fixed costs should be calculated in the total costs when sorting quotes in the RFS Dashboard and for the best execution algorithm for Auto Execution.,FIX_Protocol
The fixed fee sorting for blocks should be on full amount or on the small / large side.,FIX_Protocol
Fixed Fee rules work from top to bottom.,FIX_Protocol
Different fixed fees are uploaded for a single order.,FIX_Protocol
The configured Fixed Costs rules can be overwritten.,FIX_Protocol
Mark to Market Alert settings,Market_Systems
Mark to Market Alert section,Market_Systems
Fixing orders tolerance setting,FIX_Protocol
Fixing orders tolerance,FIX_Protocol
fixing orders tolerance check,FIX_Protocol
WM Fixings,FIX_Protocol
A separate reference pricing source can be defined for fixing orders.,FIX_Protocol
This source will send hourly reference prices for WM Fixings.,FIX_Protocol
"For fixing orders, a separate reference pricing source can be defined.",FIX_Protocol
fixed fee,FIX_Protocol
"Due to the inclusion of fixed fees, it can however mean that for that order the fixed fee paid by dealing away from the custodian (no fee) is greater than the spread benefit gained.",FIX_Protocol
The Small Side Alert requires a configuration or the upload of a custodian and fixed fees.,FIX_Protocol
The Large Side Alert requires a configuration or the upload of a custodian and fixed fees.,FIX_Protocol
Mark to Market,Market_Systems
Mark to Market amount,Market_Systems
The Mark to Market is the financial difference in home currency between the order creation snapshot and the current market rate.,Market_Systems
An alert can be configured when the Figure 48: Configuration for the Start Rate Out of tolerance Mark to Market amount is outside the configured boundaries.,Market_Systems
Figure 51 shows an example of the configured alert for the Mark to Market.,Market_Systems
The Mark to Market amount is outside the configured boundaries.,Market_Systems
An alert can be configured when the Mark to Market amount is outside the configured boundaries.,Market_Systems
An alert can be configured for the Mark to Market.,Market_Systems
fixed,FIX_Protocol
FIX ORDER API,API_Systems
FIX ORDER API,FIX_Protocol
XML ORDER API,API_Systems
The dates of the order are fix.,FIX_Protocol
The feature also applies to the FIX ORDER API and XML ORDER API.,API_Systems
The feature also applies to the FIX ORDER API and XML ORDER API.,FIX_Protocol
Fix,FIX_Protocol
"In case of incorrect configuration changes by the administrator, the fix for the problem needed to be applied manually.",FIX_Protocol
fix,FIX_Protocol
The fix for the problem needed to be applied manually.,FIX_Protocol
Fixing Orders can be created via manual creation.,FIX_Protocol
Fixing Orders can be created via order upload.,FIX_Protocol
Orders can be uploaded through 360T Order APIs.,API_Systems
360T Order APIs,API_Systems
FIX format,FIX_Protocol
automatic upload via API,API_Systems
"At present EMS support the csv format for a manual and automatic file upload, the manual order upload by copy from the clipboard, the FIX format, and the XML format for the automatic upload via an API.",API_Systems
"At present EMS support the csv format for a manual and automatic file upload, the manual order upload by copy from the clipboard, the FIX format, and the XML format for the automatic upload via an API.",FIX_Protocol
'CF' column name prefix,FIX_Protocol
'CF' name prefix,FIX_Protocol
This name prefix can be omitted in EMS.,FIX_Protocol
name prefix,FIX_Protocol
CF Fixing date 1,FIX_Protocol
custom fixing,FIX_Protocol
CF Fixing date 2,FIX_Protocol
CF Fixing,FIX_Protocol
Currency specific fixing references,FIX_Protocol
"default fixing reference ""Pre-Agreed Fixing""",FIX_Protocol
BFIX,FIX_Protocol
CF Fixing reference,FIX_Protocol
CNHFIX,FIX_Protocol
CF Fixing date 1 must be a valid trading day.,FIX_Protocol
A field is also used for Fixing Orders where it needs to be provided for the custom fixing.,FIX_Protocol
CF Fixing date 2 must be a valid trading day.,FIX_Protocol
CF Fixing is used for Currency specific fixing references.,FIX_Protocol
CF Fixing is used for NDF and NDS.,FIX_Protocol
If no value is provided EMS falls back on the default fixing reference 'Pre-Agreed Fixing'.,FIX_Protocol
default fixing reference,FIX_Protocol
A field is also used for Fixing Orders.,FIX_Protocol
Trading capacity,Trading_Operations
Trading capacity is not provided,Trading_Operations
Trading capacity is not provided.,Trading_Operations
A Forward Fixing Order example is defined with specific parameters.,FIX_Protocol
Fixing Order example,FIX_Protocol
A Forward Fixing Order example with custom Fixing is defined.,FIX_Protocol
Fixing Order example - custom Fixing,FIX_Protocol
"Fixed Fee, Tolerance & Auto Execution example",FIX_Protocol
A custom fixing occurred at 10:00 on 16.12.2019.,FIX_Protocol
In case one or more orders are reported as invalid you can decide to stop the uploading process and fix the invalid orders first before re-uploading them or you can decide to at least upload the valid orders.,FIX_Protocol
You fix the invalid orders.,FIX_Protocol
"""XML Order API"" (v2.1)",API_Systems
API methods,API_Systems
orders uploaded with API,API_Systems
XML Order API,API_Systems
automated upload via XML Order API,API_Systems
methods for FIX,FIX_Protocol
"""FIX ORDER API"" (v.9.7)",API_Systems
FIX upload,FIX_Protocol
"""FIX ORDER API"" (v.9.7)",FIX_Protocol
API methods for FIX,API_Systems
API methods for FIX,FIX_Protocol
automated upload via FIX Order API,API_Systems
automated upload via FIX Order API,FIX_Protocol
"Users should refer to the separate document ""XML Order API"" (v2.1) for more information on the XML upload.",API_Systems
The API methods can only be used on orders which have been uploaded with the API.,API_Systems
Manually created orders cannot be used with the API methods.,API_Systems
Orders that have been uploaded via csv file cannot be used with the API methods.,API_Systems
The XML Order API is fully MiFIDII compliant.,API_Systems
Users interested in the automated upload via the XML Order API should get in contact with their 360T account manager.,API_Systems
EMS also supports the FIX format for the upload of orders.,FIX_Protocol
"Users should refer to the separate document ""FIX ORDER API"" (v.9.7) for more information on the FIX upload.",API_Systems
"Users should refer to the separate document ""FIX ORDER API"" (v.9.7) for more information on the FIX upload.",FIX_Protocol
The FIX ORDER API is fully MiFIDII compliant.,API_Systems
The FIX ORDER API is fully MiFIDII compliant.,FIX_Protocol
Users interested in the automated upload via the FIX Order API should get in contact with their 360T account manager.,API_Systems
Users interested in the automated upload via the FIX Order API should get in contact with their 360T account manager.,FIX_Protocol
FIX Order API,API_Systems
FIX Order API,FIX_Protocol
You should refer to the separate document 'XML Order API' (v2.1).,API_Systems
Orders have been uploaded with the API.,API_Systems
The API methods can be used on these orders.,API_Systems
The API methods cannot be used with these orders.,API_Systems
You are interested in automated upload via the XML Order API.,API_Systems
You should refer to the separate document 'FIX ORDER API' (v.9.7).,API_Systems
More information on the FIX upload is desired.,FIX_Protocol
You should refer to the separate document 'FIX ORDER API' (v.9.7).,FIX_Protocol
You are interested in automated upload via the FIX Order API.,API_Systems
You are interested in automated upload via the FIX Order API.,FIX_Protocol
MYRFIX2-ECB37,FIX_Protocol
NDF and NDS orders require a fixing reference which depends on the selected currency pair.,FIX_Protocol
The table below shows all supported fixing references.,FIX_Protocol
supported fixing references,FIX_Protocol
CNE-EMTA (RUB03)-BFIX EUR L080,FIX_Protocol
CNE-EMTA (RUB03)-BFIX EUR L130,FIX_Protocol
CNE-EMTA (RUB03)-BFIX EUR L160,FIX_Protocol
The currency pair EURRUB uses the following fixing references.,FIX_Protocol
The currency pair EURTWD uses the following fixing references.,FIX_Protocol
The currency pair GBPARS uses the following fixing references.,FIX_Protocol
The currency pair GBPBRL uses the following fixing references.,FIX_Protocol
The currency pair GBPCLP uses the following fixing references.,FIX_Protocol
The currency pair GBPCNY uses the following fixing references.,FIX_Protocol
order ID with suffix '/x',FIX_Protocol
Suffix '/x',FIX_Protocol
"The amend action automatically cancels the original order and creates a new order which receives the same order id plus a suffix ""/x"".",FIX_Protocol
"suffix ""/x""",FIX_Protocol
"A new order is created which receives the same order id plus a suffix ""/x"".",FIX_Protocol
Trading actions,Trading_Operations
Fix Fee information,FIX_Protocol
Trading actions are only possible on the parent level of the block order.,Trading_Operations
"Custom Field values, Auto Execution parameters, Fix Fee information, and EDM / Original Order Uploader are copied over.",FIX_Protocol
XML order API,API_Systems
FIX order API,API_Systems
FIX order API,FIX_Protocol
Orders that are uploaded via the XML or FIX order API can be automatically blocked in EMS by using a custom field and the respective setting in the EMS configuration.,API_Systems
Orders that are uploaded via the XML or FIX order API can be automatically blocked in EMS by using a custom field and the respective setting in the EMS configuration.,FIX_Protocol
Orders are uploaded via XML or FIX order API using a custom field and EMS configuration.,API_Systems
Orders are uploaded via XML or FIX order API using a custom field and EMS configuration.,FIX_Protocol
"If fixed fees have been configured in the setup, it can be defined if they should be included in the quote calculation for the sorting for each product.",FIX_Protocol
"For block orders with fixed fees, the sorting can be chosen to be based on Full Amount, Small Side or Large Side.",FIX_Protocol
Fixed fees have been configured in the setup.,FIX_Protocol
You can define if fixed fees should be included in the quote calculation for sorting.,FIX_Protocol
Block orders have fixed fees.,FIX_Protocol
"In case the fixed fees are activated, and a custodian bank has been defined, the RFS dashboard can show if the custodian bank is quoting or not.",FIX_Protocol
The fixed fees are activated.,FIX_Protocol
Market lao,Market_Systems
The order type is Market.,Market_Systems
The expiry is Market lao.,Market_Systems
It is only available for Market Orders.,Market_Systems
"Other order types such as Limit, Stop, Fixing, Algo or OCO orders are not supported in this request type.",FIX_Protocol
The request type is only available for Market Orders.,Market_Systems
Order type Market Order,Market_Systems
example where fixed fees have been activated,FIX_Protocol
Fixed fees,FIX_Protocol
fixed fee rules,FIX_Protocol
source of fixed fees configuration,FIX_Protocol
Deal ticket with fixed fees,FIX_Protocol
The deal confirmation receives an additional tab when fixed costs are activated and configured by the EMS administrator.,FIX_Protocol
Figure 126 shows an example where fixed fees have been activated.,FIX_Protocol
Fixed fees can be applied to a trade by fixed fee rules.,FIX_Protocol
Fixed fees can be applied to a trade by uploading them with the order.,FIX_Protocol
The deal ticket shows the source to indicate how the fixed fees have been configured.,FIX_Protocol
The fixed fees have been configured by the rules in the EMS administration in the shown example.,FIX_Protocol
Fixed fees were applied to a deal confirmation.,FIX_Protocol
Fixed costs have been activated and configured by the EMS administrator.,FIX_Protocol
Fixed fees can be applied to a trade either by fixed fee rules or by uploading the fixed fees with the order.,FIX_Protocol
"In the shown example, the fixed fees have been configured by the rules in the EMS administration.",FIX_Protocol
Only orders of following type can be organized into portfolios: - Spot - Forward - Swap - NDS - NDF - Fixing Order,FIX_Protocol
"The Currency Pair Risk Netting view supports orders of type: Spot, Forward, NDF, Even Swaps, Uneven Swaps, Fixing Orders.",FIX_Protocol
Spot risk is executed as a fixing order.,FIX_Protocol
The bank executed the fixing order.,FIX_Protocol
API method,API_Systems
An email is sent when an order has been cancelled by a user in EMS or by the API method.,API_Systems
An order has been cancelled by the API method.,API_Systems
order amended by API method,API_Systems
This email is sent when an order has been amended by a user in EMS or by the API method.,API_Systems
An order has been cancelled by a user in EMS or by the API method,API_Systems
An order has been cancelled by a user in EMS or by the API method.,API_Systems
An order has been amended by a user in EMS or by the API method,API_Systems
An order has been amended by a user in EMS or by the API method.,API_Systems
360T DIGITEC D3.HybridMarketDataServer,Market_Systems
An order was created by 360T DIGITEC D3.HybridMarketDataServer.,Market_Systems
An order was approved by 360T DIGITEC D3.HybridMarketDataServer.,Market_Systems
Negotiation started via 360T DIGITEC D3.HybridMarketDataServer.,Market_Systems
"You may need to add the ""Trading Venue"" column in the deal blotter.",Trading_Operations
Trading Venue column,Trading_Operations
"Fixing date of Mon, 30. Jul 2018",FIX_Protocol
"Fixing date of Mon, 20. Aug 2018",FIX_Protocol
The 360T MTF refers to the Multilateral Trading Facility (as defined in Article 4(1)(22) of MiFID II) operated by 360T.,Trading_Operations
"Figure 164 shows the additional MiFID tab where the Complex Trade Component ID, Trading Capacity and the Investment decision maker can be filled in.",Trading_Operations
Trading limits are no longer checked for large Block Orders.,Trading_Operations
Fixing Orders product,FIX_Protocol
connection to 360T FIX ORDER API,API_Systems
connection to 360T FIX ORDER API,FIX_Protocol
Trading on SEF platform,Trading_Operations
Trading on MTF platform,Trading_Operations
upload of fixed fees,FIX_Protocol
Fixing Orders were added as a new product.,FIX_Protocol
A connection to 360T FIX ORDER API was added.,API_Systems
A connection to 360T FIX ORDER API was added.,FIX_Protocol
360T FIX ORDER API,API_Systems
360T FIX ORDER API,FIX_Protocol
Fixed Fees were added.,FIX_Protocol
Trading on SEF platform was added.,Trading_Operations
Trading on MTF platform was added.,Trading_Operations
Upload of fixed fees was added.,FIX_Protocol
Issues in CSV file description are fixed.,FIX_Protocol
Fixing references are updated.,FIX_Protocol
Fixing orders are available in portfolio views.,FIX_Protocol
Portfolios are auto-committed for fixing orders.,FIX_Protocol
"360 Trading Networks LLC is located at Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai.",Trading_Operations
360 Trading Networks LLC has a phone number +971 4 491 5134.,Trading_Operations
"360 Trading Networks, Inc is located at 708 Third Avenue, 7th Floor New York, NY 10017.",Trading_Operations
"360 Trading Networks, Inc has a phone number ****** 776 2900.",Trading_Operations
"360 Trading Networks, Inc has a fax number ****** 776 2902.",Trading_Operations
TWS Market Maker,Market_Systems
Money Market Loan / Deposit,Market_Systems
TWS Market Maker (BRIDGE) was processed page-by-page.,Market_Systems
TWS Market Maker (BRIDGE),Market_Systems
The TWS Market Maker (BRIDGE) document was processed page-by-page.,Market_Systems
Market orders are discussed.,Market_Systems
Fixing orders are discussed.,FIX_Protocol
The Fixing Orders tab is available.,FIX_Protocol
Fixing Orders tab,FIX_Protocol
Pricing Money Market Deposit,Market_Systems
Money Market Deposit is being priced.,Market_Systems
Market Orders are triggered.,Market_Systems
A Fixing Order is triggered.,FIX_Protocol
Multi-selection of Fixing Orders occurs.,FIX_Protocol
Multiple Fixing Orders are executed in bulk.,FIX_Protocol
Market Orders hedge with RFS.,Market_Systems
Fixing Orders are displayed.,FIX_Protocol
A Fixing Order is executed.,FIX_Protocol
Multiple fixing orders are selected.,FIX_Protocol
Multiple fixing orders are executed in bulk.,FIX_Protocol
Market orders are triggered.,Market_Systems
Market Orders Intervention,Market_Systems
Fixing Orders Intervention,FIX_Protocol
"The user guide describes the feature to receive and manage limit, stop, market, fixing, OCO, and If-then orders.",FIX_Protocol
"This area consists of Dealer Intervention, Market Orders Intervention and Fixing Order Intervention.",FIX_Protocol
"This area consists of Dealer Intervention, Market Orders Intervention and Fixing Order Intervention.",Market_Systems
Fixing Order Intervention,FIX_Protocol
"The bottom is split into several tabs and can be organized to view Offers and Active orders, Accepted orders, Automatically processed requests and orders, Hedged Orders, Active/Passive orders and finally Fixing orders.",FIX_Protocol
Fixing Orders (0),FIX_Protocol
There are zero fixing orders.,FIX_Protocol
0 fixing orders exist.,FIX_Protocol
Market Order 0-11039816,Market_Systems
Market Order 0-11039781,Market_Systems
Market Order 0-11039735,Market_Systems
Market Order 0-11039816 was placed by SubsidiaryH.,Market_Systems
Market Order 0-11039816 was accepted.,Market_Systems
Market Order 0-11039781 was placed by SubsidiaryH.,Market_Systems
Market Order 0-11039781 was accepted.,Market_Systems
Market Order 0-11039735 was placed by GroupH.,Market_Systems
Market Order 0-11039735 was accepted.,Market_Systems
A Market Order is placed with reference PO-111744 and type GTC.,Market_Systems
There are zero Market Orders Intervention.,Market_Systems
There is one Fixing Orders Intervention.,FIX_Protocol
An FxForward Fixing Order was placed.,FIX_Protocol
An FxSpot Market Order was placed.,Market_Systems
Market makers have a chance to receive incoming requests.,Market_Systems
Market makers have a chance to receive placed orders.,Market_Systems
Market makers have a chance to revisit the credit lines of the counterpart before making a decision to fulfil the request.,Market_Systems
Market makers have a chance to revisit the credit lines of the counterpart before making a decision to fulfil the order.,Market_Systems
Market makers have a chance to receive incoming requests and revisit credit lines before making a decision.,Market_Systems
Market makers revisit the credit lines of the counterpart.,Market_Systems
Market makers make a decision to fulfil the request or order.,Market_Systems
Market Orders Intervention tab,Market_Systems
Percentage to Market,Market_Systems
Zero fixing orders exist.,FIX_Protocol
Two fixing orders interventions occurred.,FIX_Protocol
No active or passive fixing orders were present.,FIX_Protocol
fully MiFID II compliant Multilateral Trading Facility,Trading_Operations
"360T operates a MiFID II compliant Multilateral Trading Facility, an OTC trading platform, and a SEF trading venue as of January 3rd 2018.",Trading_Operations
MiFID II compliant Multilateral Trading Facility,Trading_Operations
default Trading Capacity,Trading_Operations
The default Trading Capacity and Investment Decision Maker within Firm is changed.,Trading_Operations
Fixing Orders count,FIX_Protocol
Trading Capacity and Investment Decision fields are mandatory for MTF trades.,Trading_Operations
Trading Capacity fields are mandatory for MTF trades.,Trading_Operations
'NONE' is a value in the Trading Capacity drop down menu.,Trading_Operations
Fixing date and Fixing Reference are additional trade parameters to the delivered forward.,FIX_Protocol
Fixing Date (Near Leg),FIX_Protocol
Fixing Date (Far Leg),FIX_Protocol
Market Rate 1.12275,Market_Systems
Market Order Intervention view,Market_Systems
Market Rate 0.84343,Market_Systems
The stop order becomes a Market Order when a specified price is reached.,Market_Systems
Accepting a market order will display it in the Market Order Intervention view.,Market_Systems
The stop order becomes a Market Order.,Market_Systems
The order displays in the Market Order Intervention view.,Market_Systems
Fixing order,FIX_Protocol
Fixing authority,FIX_Protocol
selecting a fixing benchmark reference,FIX_Protocol
Fixing benchmark reference,FIX_Protocol
different fixing dates and times,FIX_Protocol
A fixing order allows buying or selling a product at a specific rate and time.,FIX_Protocol
A fixing authority is established by choosing a fixing benchmark reference.,FIX_Protocol
fixing benchmark reference,FIX_Protocol
"Examples of fixing references include WM/Reuters Intraday, WM/Reuters Closing, and Bank of Canada Noon.",FIX_Protocol
The requestor selects from the available fixing references.,FIX_Protocol
Fixing references are associated with distinct dates and times.,FIX_Protocol
fixing times,FIX_Protocol
A wished fixing might not be present in the dropdown list.,FIX_Protocol
wished fixing,FIX_Protocol
The requester can select Custom Fixing.,FIX_Protocol
The requestor chooses a fixing reference.,FIX_Protocol
A fixing authority is set.,FIX_Protocol
A wished fixing is not included in the dropdown list.,FIX_Protocol
The requester selects Custom Fixing.,FIX_Protocol
API-based integration,API_Systems
"If an API-based integration was implemented, the TWS will be used as a fallback user interface.",API_Systems
Orders accepted via an API-based integration can be viewed.,API_Systems
An API-based integration is implemented into the liquidity provider's Order Management System,API_Systems
Orders are accepted via an API-based integration into the liquidity provider's Order Management System,API_Systems
Fixing time of a fixing order,FIX_Protocol
Triggered market or fixing orders,FIX_Protocol
Market rate,Market_Systems
Market Order Intervention tab,Market_Systems
Fixing Order Intervention tab,FIX_Protocol
accepted Market Orders,Market_Systems
Accepted Market Orders,Market_Systems
fixing time of a fixing order,FIX_Protocol
Triggered market or fixing orders will be highlighted by violet dot.,FIX_Protocol
The Market Orders Intervention tab shows accepted Market Orders.,Market_Systems
The Market Orders Intervention tab,Market_Systems
The fixing time of a fixing order was reached.,FIX_Protocol
Triggered market or fixing orders will be highlighted by violet dot placed next to the order type in the furthest left column of the Active order table.,FIX_Protocol
"The order will appear, depending on the type of order either in the Market Order Intervention tab (for Market Orders) or in the Fixing Order Intervention tab or in the Dealer Intervention tab (for all other order types).",FIX_Protocol
"The order will appear, depending on the type of order either in the Market Order Intervention tab (for Market Orders) or in the Fixing Order Intervention tab or in the Dealer Intervention tab (for all other order types).",Market_Systems
"The order will disappear from the Offered/Accepted, Active/Passing and Fixing Orders tab.",FIX_Protocol
Triggered Market Orders,Market_Systems
Fixing Orders Intervention tab,FIX_Protocol
triggered fixing orders,FIX_Protocol
Fixing Reference column,FIX_Protocol
Fixing Date column,FIX_Protocol
date of the fixing,FIX_Protocol
Triggered Fixing Order,FIX_Protocol
multiple fixing orders,FIX_Protocol
Fixing Order PO-1061644-OCC,FIX_Protocol
Fixing Order PO-1061643-OCC,FIX_Protocol
Fixing Orders (total),FIX_Protocol
Offered/Active Fixing Orders,FIX_Protocol
Accepted Fixing Orders,FIX_Protocol
Automatically Processed Fixing Orders,FIX_Protocol
Active/Passive Fixing Orders,FIX_Protocol
Fixing Order (bottom example),FIX_Protocol
The Fixing Orders Intervention tab displays triggered fixing orders according to the sorting criteria defined.,FIX_Protocol
The table consists of a Fixing Reference and a Fixing Date column in addition to common order columns.,FIX_Protocol
Fixing Reference displays the respective fixing authority.,FIX_Protocol
The Fixing Date displays the date of the fixing.,FIX_Protocol
"The fixing order can be executed by clicking ""Execute"" after selecting the order.",FIX_Protocol
The user can select multiple fixing orders and price them in one step.,FIX_Protocol
Fixing Reference displays the fixing authority.,FIX_Protocol
Fixing Date displays the date of the fixing.,FIX_Protocol
A fixing order can be executed.,FIX_Protocol
Multiple fixing orders can be selected.,FIX_Protocol
Multiple fixing orders can be priced in one step.,FIX_Protocol
The table has a Fixing Reference and a Fixing Date column.,FIX_Protocol
The tab displays triggered fixing orders.,FIX_Protocol
Multi-selection of Fixing Orders,FIX_Protocol
Bulk execution of Fixing Orders,FIX_Protocol
The selected fixing orders can be executed in bulk with the same fixing rate.,FIX_Protocol
selected fixing orders,FIX_Protocol
Accepted Market Orders which can be hedged by creating RFS request can be found in the Market Order Intervention tab.,Market_Systems
Accepted Market Orders are found in the Market Order Intervention tab.,Market_Systems
"BankClientM placed a Market Order (PO-127793) to buy FX Forward EUR/USD for 3,000,000.00 on July 26.",Market_Systems
BankClientM placed a Market Order (PO-127546) to buy FX Spot EUR/USD on July 19.,Market_Systems
Two orders are fixing orders.,FIX_Protocol
Market Orders Interventions tab,Market_Systems
Market Order PO-1714873-DDN is accepted.,Market_Systems
Market Order PO-1714872-DDN is accepted.,Market_Systems
Market Order PO-1714871-DDN is accepted.,Market_Systems
Market Order PO-1714870-DDN is accepted.,Market_Systems
Market Order PO-1714869-DDN is accepted.,Market_Systems
Zero fixing orders require intervention.,FIX_Protocol
Market order linking,Market_Systems
"For market orders, linking will be done within the Market Order Intervention tab.",Market_Systems
It is done within the Market Order Intervention tab using the same command with the right mouse click.,Market_Systems
Market Order PO-127546,Market_Systems
Market Order PO-118574,Market_Systems
Market Order EMSO-2746125,Market_Systems
Market Order EMSO-2746122,Market_Systems
Market Order EMSO-2734766,Market_Systems
A Market Order was initialized for EUR/USD FX Spot by GroupH.,Market_Systems
The Market Order is pending rejection.,Market_Systems
The Market Order is pending cancellation.,Market_Systems
A Market Order was sent for EUR/USD FX Spot with a Forward Date by GroupH.,Market_Systems
The Market Order was initialized.,Market_Systems
A Market Order was sent by GroupH.,Market_Systems
The Market Order was accepted.,Market_Systems
Market Order EMSO-2746125 is active for EUR/USD FX Forward by GroupH.,Market_Systems
Market Order EMSO-2746125 was accepted.,Market_Systems
Market Order EMSO-2746125 has partial fill progress.,Market_Systems
Market Order EMSO-2746122 is active for EUR/USD FX Spot by GroupH.,Market_Systems
Market Order EMSO-2746122 is pending cancellation.,Market_Systems
Market Order EMSO-2734766 is active for EUR/USD by GroupH.,Market_Systems
Market Order EMSO-2734766 was accepted.,Market_Systems
Market Order PO-118574 was placed by SubsidiaryH to buy FX Spot EUR/USD for 123.456.00 on May 5th and was accepted.,Market_Systems
Market Order PO-118399 was placed by SubsidiaryH to buy FX Spot EUR/USD for 987.654.00 on May 5th and was accepted.,Market_Systems
Market Order PO-118399,Market_Systems
"Market Order 0-11040530 was placed by SubsidiaryH to sell FX Spot EUR/USD for 1,000.00 EUR on May 5th and was accepted.",Market_Systems
Market Order 0-11040530,Market_Systems
Market Order 0-11039807 was placed by SubsidiaryH to buy FX Spot EUR/INR for 50.000.00 on May 5th and was accepted.,Market_Systems
Market Order 0-11039807,Market_Systems
Market Order PO-1213787-UNN was placed by SubsidiaryH to buy FX Forward EUR/USD for 1.255.00 EUR with a 1-week maturity on May 12th and was accepted.,Market_Systems
Market Order PO-1213787-UNN,Market_Systems
Market Order PO-1213784-UNN was placed by SubsidiaryH to buy FX Forward EUR/USD for 4.879.00 EUR with a 1-month maturity on June 5th and is pending cancellation.,Market_Systems
Market Order PO-1213784-UNN,Market_Systems
A Market Order was initialized by GroupH to buy FX Spot EUR/USD for 8.0 and is pending rejection.,Market_Systems
Fixing Order PO-1277894- GTC,FIX_Protocol
1.18293 Market Rate,Market_Systems
Fixing Order PO-1277893- GTC,FIX_Protocol
same Fixing Reference,FIX_Protocol
same Fixing Date,FIX_Protocol
Effective Period of Fixing Orders,FIX_Protocol
Market Orders Intervention (4),Market_Systems
Fixing Orders Intervention (0),FIX_Protocol
Orders have same Fixing Reference,FIX_Protocol
Orders have same Fixing Date,FIX_Protocol
One fixing orders intervention occurred.,FIX_Protocol
Fixing orders are listed.,FIX_Protocol
No fixing orders intervention occurred.,FIX_Protocol
maintaining service of FIS (MarketMap),Market_Systems
"accuracy, reliability, performance, completeness, timeliness, continued availability of service of FIS (MarketMap)",Market_Systems
"delays, omissions or interruptions in delivery of service of FIS (MarketMap)",Market_Systems
use or inability to use service of FIS (MarketMap),Market_Systems
360T is using a market data source feed from FIS (MarketMap) which is linked to the TWS.,Market_Systems
"360T makes no representation or warranty to users to the service of FIS (MarketMap), express or implied, including any implied warranty or merchantability or fitness for a particular purpose, any warranty regarding the ability or inability to use, or, as applicable, the result of, the service of FIS (MarketMap) with respect to their accuracy, reliability, performance, completeness, timeliness, continued availability or otherwise.",Market_Systems
"360T does its best to provide the TWS with the FIS (MarketMap) service but will not have any responsibility to maintain the service of FIS (MarketMap) or to supply any corrections, updates or releases in connection therewith.",Market_Systems
"360T shall not have any liability, direct or indirect, contingent or otherwise, to the Counterparty, for the accuracy, reliability, performance, completeness, timeliness, continued availability or otherwise, of the service of FIS (MarketMap), or for delays or omissions therein or interruptions on the delivery thereof.",Market_Systems
"360T is not liable for special, indirect, exemplary, incidental, punitive or consequential damages, whether foreseeable or not, relating to users of the TWS by the use of, or inability to use, the service of FIS (MarketMap).",Market_Systems
use of the service of FIS (MarketMap),Market_Systems
inability to use the service of FIS (MarketMap),Market_Systems
Market Order PO-111614,Market_Systems
"A Market-Order PO-127793 was placed by BankClientM to buy FX-Forward EUR/USD for 3,000,000.00.",Market_Systems
Market-Order PO-127793,Market_Systems
Fixing Orders category,FIX_Protocol
The Accepted Orders tab shows all accepted orders with the exception of Fixing Orders.,FIX_Protocol
Fixing orders are displayed separately under a Fixing Orders tab.,FIX_Protocol
Fixing date and fixing authority are only displayed in the Fixing Orders tab.,FIX_Protocol
Once triggered the fixing orders move to the Fixing Orders Intervention tab.,FIX_Protocol
The fixing orders are highlighted with a violet coloured dot placed in the RFS/Order column.,FIX_Protocol
It is possible to execute the orders already in the Fixing Orders Intervention tab.,FIX_Protocol
"Fixing orders can be executed using the right mouse button and the ""Go to Execution"" command.",FIX_Protocol
A direct click on the Fixing Order slides in the Execution window on the right side of the screen.,FIX_Protocol
Fixing orders are displayed separately under a Fixing Orders tab,FIX_Protocol
Fixing orders are triggered,FIX_Protocol
the fixing orders move to the Fixing Orders Intervention tab,FIX_Protocol
fixing orders are highlighted with a violet coloured dot placed in the RFS/Order column,FIX_Protocol
The fixing orders move to the Fixing Orders Intervention tab,FIX_Protocol
A direct click is made on the Fixing Order,FIX_Protocol
Execute Fixing Order,FIX_Protocol
Execution of fixing orders,FIX_Protocol
The execution of several similar fixing orders at the same time will be supported again in a future version.,FIX_Protocol
"A Market Order Non Deliverable deal with reference 0-11018366 was placed by GroupH to buy 15,002.00 USD against BRL from Credit Suiss, effective on Wednesday, July 17.",Market_Systems
"The Market Order for Non Deliver. (0-11018366) became effective on Wednesday, July 17.",Market_Systems
"Users should refer to ""360T User Guide RFS Market Taker Bridge"" for further information.",Market_Systems
"GroupE and HSBC.DEMO placed a Fixing Order for FxForward to buy 45,331.00 A. for 23,121,000 AUD against IDR with a 1 Month maturity on Thu, 19, Apr.",FIX_Protocol
"GroupE and HSBC.DEMO placed a Fixing Order for FxForward to buy 46,000.00 A. for 12,009,400 AUD against IDR with a 1 Month maturity on Thu, 19. Apr.",FIX_Protocol
"GroupE and HSBC.DEMO placed a Fixing Order for FxForward to buy 46,000.00 A. for 10,841,877 AUD against IDR with a 1 Month maturity on Thu, 19. Apr.",FIX_Protocol
"GroupE and HSBC.DEMO placed a Fixing Order for FxForward to buy 45,000.00 A. for 84.34100 AUD against JPY on Thu, 22. Mar.",FIX_Protocol
An FxForward Fixing Order to buy AUD for IDR was placed (Ref: 0-10941173).,FIX_Protocol
An FxForward Fixing Order to buy AUD for IDR was placed (Ref: 0-10941168).,FIX_Protocol
An FxForward Fixing Order to buy AUD for JPY was placed (Ref: 0-10938006).,FIX_Protocol
"360 Trading Networks, Inc provides a fax number.",Trading_Operations
ThreeSixty Trading Networks (India) Pvt Ltd provides a phone number.,Trading_Operations
360T cannot send Proprietary or Market Maker FX Futures trades at this time.,Market_Systems
Market Maker FX Futures trades,Market_Systems
360T cannot be used at this point to send Proprietary or Market Maker FX Futures trades,Market_Systems
FX Futures Market Data,Market_Systems
"Only for ""Total or Partial Fill at Rate"" and ""Fill at Best"" execution types, FX Futures Market Data is visible.",Market_Systems
FX Futures Market Data is visible.,Market_Systems
Market data comes in from Eurex.,Market_Systems
Lock Market Data Stream icon,Market_Systems
"This is done by toggling the yellow icon between the ""Open Floating View"" icon and the ""Lock Market Data Stream"" icon on the top right of the currency pair.",Market_Systems
"The yellow icon is toggled between the ""Open Floating View"" icon and the ""Lock Market Data Stream"" icon on the top right of the currency pair.",Market_Systems
Figure 12 Trading Capacity,Trading_Operations
Figure 14 360T MTF Identification of users behind API,API_Systems
360T MTF identifies users behind API.,API_Systems
MTF (Multilateral Trading Facility),Trading_Operations
MT API,API_Systems
Systemic Internalizer Market Identifier Code,Market_Systems
trading API,API_Systems
In the OTC tab the admin user can define if the entity is a Systemic Internalizer (SI) and add the Systemic Internalizer Market Identifier Code (SI MIC).,Market_Systems
Systemic Internalizer Market Identifier Code (SI MIC),Market_Systems
"In case a trading API is utilized (e.g. Market Taker API), the SI status has to be provided via the API.",API_Systems
"In case a trading API is utilized (e.g. Market Taker API), the SI status has to be provided via the API.",Market_Systems
Market Taker API,API_Systems
Market Taker API,Market_Systems
A trading API is utilized.,API_Systems
The SI status must be provided via the API.,API_Systems
The SI status is provided via the API.,API_Systems
360T User Guide RFS Market Taker Bridge.pdf,Market_Systems
Further information on the details of the disclosed information can be found in the document 360T User Guide RFS Market Taker Bridge.pdf.,Market_Systems
"A trader will see the configured default value in the Trading Capacity field in a new tab called ""MIFID"" when opening a product definition window within 360T.",Trading_Operations
The trader will see the configured default value in the Trading Capacity field in a new tab called MIFID.,Trading_Operations
The trader sees the configured default value in the Trading Capacity field.,Trading_Operations
Trading Capacity is discussed.,Trading_Operations
Figure 12 provides a visual representation of Trading Capacity.,Trading_Operations
Trading Capacity B2B is used for Back2Back trading.,Trading_Operations
A default value can be set for Market Maker APIs.,API_Systems
A default value can be set for Market Maker APIs.,Market_Systems
"Trading Capacity PS is set to ""DEAL"".",Trading_Operations
"The Trading Capacity of a MTF transaction defaults to the configured value ""DEAL"".",Trading_Operations
Algo IDs are applicable for Market Maker APIs.,API_Systems
Algo IDs are applicable for Market Maker APIs.,Market_Systems
Individual user details of manual traders who are using a proprietary trading system connected to 360T's MTF via an API are also required to be captured in the tool.,API_Systems
"Such manual traders are then identified by their 360T specific short-code via the API, as shown in [Figure 14.](#page-15-3).",API_Systems
Such manual traders are then identified by their 360T specific short-code via the API.,API_Systems
External users may still be identified as an EDM or IDM on the 360T MTF venue and/or access the venue via an API.,API_Systems
External users may access the venue via an API,API_Systems
DD.MM.YYYY HH:MM:SS for fixing orders with custom fixing,FIX_Protocol
default fixing reference 'Pre-Agreed Fixing',FIX_Protocol
absence of CF Fixing value,FIX_Protocol
"BFIX, WMR Intraday London, WMR Closing London, WMR Australia, WMR New York, BOC Noon Rate, LBMA AM, LBMA PM, TKFE, CNHFIX, ECB, Custom Fixing",FIX_Protocol
The CF Fixing date 1 must be a valid trading day.,FIX_Protocol
The CF Fixing date 1 field is used for NDF and NDS where it must be provided.,FIX_Protocol
CF Fixing date 1 field,FIX_Protocol
The CF Fixing date 1 field is also used for Fixing Orders where it needs to be provided for the custom fixing.,FIX_Protocol
The CF Fixing date 2 must be a valid trading day.,FIX_Protocol
The CF Fixing date 2 field is only used for NDS and must be provided.,FIX_Protocol
CF Fixing date 2 field,FIX_Protocol
Currency specific fixing references are used for NDF and NDS.,FIX_Protocol
"EMS falls back on the default fixing reference ""Pre-Agreed Fixing"" if no value is provided.",FIX_Protocol
The CF Fixing field is also used for Fixing Orders.,FIX_Protocol
CF Fixing field,FIX_Protocol
The fixing date 1 field is used for NDF and NDS.,FIX_Protocol
The fixing date 1 field must be provided.,FIX_Protocol
The fixing date 1 field is used for custom fixing orders.,FIX_Protocol
The fixing date 1 field needs to be provided.,FIX_Protocol
The fixing date 2 field is used for NDS.,FIX_Protocol
The fixing date 2 field must be provided.,FIX_Protocol
No value is provided for CF Fixing.,FIX_Protocol
EMS falls back on the default fixing reference 'Pre-Agreed Fixing'.,FIX_Protocol
ACT_365_FIXED (ACT/365 (FIXED)),FIX_Protocol
Trading capacity is a MiFID II field.,Trading_Operations
Trading capacity is a non-mandatory field.,Trading_Operations
"If Trading capacity is not provided, the company default value is used.",Trading_Operations
NDF and NDS orders require a fixing reference.,FIX_Protocol
The table displays supported fixing references.,FIX_Protocol
Specific fixing references are used for the EURPEN currency pair.,FIX_Protocol
'PEN05-BFIX EUR L080',FIX_Protocol
'PEN05-BFIX EUR L130',FIX_Protocol
'PEN05-BFIX EUR L160',FIX_Protocol
Specific fixing references are used for the EURPHP currency pair.,FIX_Protocol
'PDSPESO-BFIX EUR L080',FIX_Protocol
'PDSPESO-BFIX EUR L130',FIX_Protocol
'PDSPESO-BFIX EUR L160',FIX_Protocol
Specific fixing references are used for the EURRUB currency pair.,FIX_Protocol
'CNE-EMTA (RUB03)-BFIX EUR L080',FIX_Protocol
'CNE-EMTA (RUB03)-BFIX EUR L130',FIX_Protocol
'CNE-EMTA (RUB03)-BFIX EUR L160',FIX_Protocol
Specific fixing references are used for the EURTWD currency pair.,FIX_Protocol
'TAIFX1 (TWD03)-BFIX EUR L080',FIX_Protocol
'TAIFX1 (TWD03)-BFIX EUR L130',FIX_Protocol
'TAIFX1 (TWD03)-BFIX EUR L160',FIX_Protocol
EURPEN is supported by 'PEN05-BFIX EUR L080'.,FIX_Protocol
EURPEN is supported by 'PEN05-BFIX EUR L130'.,FIX_Protocol
EURPHP is supported by 'PDSPESO-BFIX EUR L080'.,FIX_Protocol
EURPHP is supported by 'PDSPESO-BFIX EUR L130'.,FIX_Protocol
"360 Trading Networks LLC's address is Dubai International Financial Centre, Liberty House, Level 8, App. 810C, P.O. Box 482036, Dubai.",Trading_Operations
360 Trading Networks LLC's phone number is +971 4 491 5134.,Trading_Operations
"360 Trading Networks, Inc's address is 708 Third Avenue, 7th Floor, New York, NY 10017.",Trading_Operations
"360 Trading Networks, Inc's phone number is ****** 776 2900.",Trading_Operations
"360 Trading Networks, Inc's fax number is ****** 776 2902.",Trading_Operations
"360 Trading Networks LLC is located in Dubai, United Arab Emirates.",Trading_Operations
"360 Trading Networks Inc. is located in New York, USA.",Trading_Operations
360T Limit REST API,API_Systems
API Version v2,API_Systems
Interacting With The Api,API_Systems
Interacting with the API is explained.,API_Systems
API availability considerations are addressed.,API_Systems
Effective communication with the API is established.,API_Systems
The API can be interacted with effectively.,API_Systems
OpenAPI 2.0 standard,API_Systems
REST API calls,API_Systems
This document presents the 360T Limit REST API.,API_Systems
The 360T Limit REST API provides operations.,API_Systems
The API is implemented.,API_Systems
The API meets the OpenAPI 2.0 standard.,API_Systems
All calls to the REST API should be made with a HTTP call.,API_Systems
REST API,API_Systems
This document presents the 360T Limit REST API which provides operations that allow clients to configure rules of the 360T Risk Portfolio.,API_Systems
The API is implemented to meet the OpenAPI 2.0 standard.,API_Systems
All calls to the REST API should be made with a HTTP call over secure TCP socket.,API_Systems
https://apigatewayint.360t.com URL,API_Systems
https://apigatewayint.360t.com server,API_Systems
https://apigateway.360t.com URL,API_Systems
https://apigateway.360t.com server,API_Systems
REST API unreachable from client,API_Systems
Functionality of API,API_Systems
Communication with Limit REST API,API_Systems
Limit REST API,API_Systems
Limit REST API resources,API_Systems
something went wrong in API level,API_Systems
The non-availablity of the 360T platform does not mean that REST API will be unreachable from the client for the entire duration of the maintenance window.,API_Systems
"If the REST API is reachable during the maintenance window, the functionality of the API will not be available.",API_Systems
The communication with the Limit REST API is over a secure HTTPS connection.,API_Systems
This will allow client to validate 360T server and be authenticated to use the Limit REST API resources.,API_Systems
The REST API is reachable during the maintenance window.,API_Systems
Client can validate 360T server and be authenticated to use the Limit REST API resources.,API_Systems
API description format,API_Systems
REST APIs,API_Systems
360T Limit REST API Swagger Specification,API_Systems
360T Limit REST API implements HTTP verbs.,API_Systems
360T Limit REST API does not support cross-origin HTTP requests.,API_Systems
Swagger Specification is an API description format.,API_Systems
The 360T Limit REST API Swagger Specification can be fetched in JSON format via a request.,API_Systems
Swagger Specification is an API description format for REST APIs.,API_Systems
360T Limit REST API Swagger Specification in JSON format can be fetched.,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/configRule,API_Systems
singleOrGroupLegalEntity single institution name: 360T.INT_LIMAPI.TEST,API_Systems
An HTTP POST request is made to https://apigateway-int.360t.com:7060/limitapi/v2/configRule.,API_Systems
An HTTP GET request is made to https://apigateway-int.360t.com:7060/limitapi/v2/configRule.,API_Systems
360T.INT_LIMAPI.TEST Legal Entity,API_Systems
API Gateway Endpoint,API_Systems
API endpoint,API_Systems
360T.INT_LIMAPI.TEST,API_Systems
apigateway-int.360t.com,API_Systems
limitapi,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limits,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limit/all,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/all,API_Systems
HTTP Request PUT https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit,API_Systems
limitapi/v2/activeRule/ActiveRule-Test/limit endpoint,API_Systems
HTTP Request PUT https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limits,API_Systems
limitapi/v2/activeRule/limits endpoint,API_Systems
HTTP Request PUT https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520,API_Systems
limitapi/v2/activeRule/ActiveRule-Test/limit/20210520 endpoint,API_Systems
HTTP Request PUT https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limit/all,API_Systems
limitapi/v2/activeRule/limit/all endpoint,API_Systems
HTTP Request DELETE https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/all,API_Systems
limitapi/v2/activeRule/ActiveRule-Test/limit/all endpoint,API_Systems
HTTP Request DELETE https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/portfolio,API_Systems
A POST request is made to https://apigateway-int.360t.com:7060/limitapi/v2/portfolio.,API_Systems
A GET request is made to https://apigateway-int.360t.com:7060/limitapi/v2/portfolio.,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/portfolio/Portfolio-Test,API_Systems
/limitapi/v2/algo endpoint,API_Systems
/limitapi/v2/executionMethod endpoint,API_Systems
POST /executionMethodGroup API endpoint,API_Systems
GET /executionMethodGroup API endpoint,API_Systems
PUT /executionMethodGroup/{name} API endpoint,API_Systems
POST /executionMethodGroup/{name} API endpoint,API_Systems
GET /executionMethodGroup/{name} API endpoint,API_Systems
DELETE /executionMethodGroup/{name} API endpoint,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/productGroup,API_Systems
API Response,API_Systems
API endpoint `https://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test`,API_Systems
/limitapi/v2/product endpoint,API_Systems
/limitapi/v2/currencyCoupleGroup endpoint,API_Systems
GET /limitapi/v2/currencyCoupleGroup,API_Systems
PUT /limitapi/v2/currencyCoupleGroup/{name},API_Systems
Standard API Response,API_Systems
GET /limitapi/v2/currencyCoupleGroup/{name},API_Systems
DELETE /limitapi/v2/currencyCoupleGroup/{name},API_Systems
isoCurrency API endpoint,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/isoCurrency,API_Systems
timePeriod API endpoint,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/timePeriod,API_Systems
/limitapi/v2/time endpoint,API_Systems
/limitapi/v2/dealerGroup endpoint,API_Systems
API Gateway,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/dealer,API_Systems
Permissioned Trading Relationship,Trading_Operations
https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup,API_Systems
A PUT request is sent to the Counterpart Group API endpoint.,API_Systems
Counterpart Group API endpoint,API_Systems
A GET request is sent to the Counterpart Group API endpoint.,API_Systems
A DELETE request is sent to the Counterpart Group API endpoint.,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/counterpart,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup,API_Systems
"Institution ""360T.INT_LIMAPI.TEST""",API_Systems
"Institution ID ""360T.INT_LIMAPI.TEST""",API_Systems
"Institution Name ""360T.INT_LIMAPI.TEST""",API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test,API_Systems
"""360T.INT_LIMAPI.TEST""",API_Systems
"institution ID ""360T.INT_LIMAPI.TEST""",API_Systems
"institution name ""360T.INT_LIMAPI.TEST""",API_Systems
Legal Entity API,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/legalEntity,API_Systems
https://apigateway-int.360t.com:7060/limitapi/v2/pfe,API_Systems
An HTTP GET request is made to the legalEntity API.,API_Systems
legalEntity API,API_Systems
An example response is provided for the legalEntity API.,API_Systems
An HTTP POST request is made to the /pfe API for adding a currency couple.,API_Systems
/pfe API,API_Systems
An HTTP GET request is made to the /pfe API for listing entries.,API_Systems
An HTTP GET request is sent to the legal entity API.,API_Systems
An HTTP POST request is sent to the PFE API endpoint.,API_Systems
An HTTP GET request is sent to the PFE API endpoint.,API_Systems
A PUT HTTP request is sent to the limitapi/v2/pfe endpoint.,API_Systems
limitapi/v2/pfe endpoint,API_Systems
A DELETE HTTP request is sent to the limitapi/v2/pfe endpoint with specific currency parameters.,API_Systems
A POST HTTP request is sent to the limitapi/v2/pfe/tenor endpoint.,API_Systems
limitapi/v2/pfe/tenor endpoint,API_Systems
A DELETE HTTP request is sent to the limitapi/v2/pfe/tenor/5 endpoint.,API_Systems
limitapi/v2/pfe/tenor/5 endpoint,API_Systems
360T Limit REST API access point,API_Systems
Private key and client certificate should be used for submitting requests to the 360T Limit REST API access point.,API_Systems
These should be used when submitting requests to the 360T Limit REST API access point.,API_Systems
Definition of Market Link Algorithm.,Market_Systems
Selecting a Trading Venue.,Trading_Operations
Figure 26 Market Link Algorithm definition.,Market_Systems
Figure 27 Fixing Reference Groups.,FIX_Protocol
Figure 36 Selecting a Trading Venue.,Trading_Operations
"The available rule parameters in the ADS Configuration Groups include Requester Groups, Negotiation Groups, Product Groups, Activation Period Groups, MM Currency Groups, Currency Couple Groups, Notional Amount Groups, Time Period Groups, RFS Algorithm Groups, Fixing Reference Groups, and Order Strategy Groups.",FIX_Protocol
"Trading venue OTC. EU MTF, UK MTF DEFAULT",Trading_Operations
"Trading venue OTC, EU MTF, UK MTF",Trading_Operations
Trading venue OTC,Trading_Operations
The ADS facilitates the option to forward incoming requests to a group of Liquidity Providers (referred to as Market Link routing) or to single provider in case of Orders.,Market_Systems
Market Link RFS group membership,Market_Systems
Market Link RFS group,Market_Systems
Time periods for Money Market (MM) instruments,Market_Systems
Time periods specific for Money Market instruments cannot be combined with FX specific time periods within one Time Period Group.,Market_Systems
Fixing Reference conditions,FIX_Protocol
"""Fixing Reference"" drop down menu",FIX_Protocol
"If the Market Link Algorithm ""Exotics"" is used in a pricing rule, a request will be sent to the market link providers and waits for quotes from at least 3 different providers within the first 5 seconds before starting to stream the best quote to the requester.",Market_Systems
"In case NDF and NDS requests are priced, inter alia, based on Fixing Reference conditions then the Fixing Reference Groups feature can be used.",FIX_Protocol
The Fixing Reference Groups are only relevant for ADS RFS or Order Rules and are not applicable to ADS SEP Rules.,FIX_Protocol
"Groups of Fixing References can be created, removed, and renamed similarly to other ADS Configuration Groups.",FIX_Protocol
"The default value ""Any"" set during rule creation encompasses all fixing references.",FIX_Protocol
A list of all available Fixing References with group membership information can be downloaded as a CSV file.,FIX_Protocol
available Fixing References,FIX_Protocol
"Currently available order strategies are Market Orders, Limit Orders, Stop Orders, Fixing Orders, Loop Orders, If-Done Orders, OCO Orders, ALGO Orders and Call Orders.",FIX_Protocol
"Currently available order strategies are Market Orders, Limit Orders, Stop Orders, Fixing Orders, Loop Orders, If-Done Orders, OCO Orders, ALGO Orders and Call Orders.",Market_Systems
"The Market Link Algorithm ""Exotics"" is used in a pricing rule.",Market_Systems
NDF and NDS requests are priced based on Fixing Reference conditions.,FIX_Protocol
"Groups of Fixing References can be created, removed, and renamed.",FIX_Protocol
"The default value ""Any"" encompasses all fixing references.",FIX_Protocol
"A request goes to the Manual Routing Group configured for the rule if the route is set to Market Link, pricing is unavailable, and a republish timeout is configured in the ADS instance.",Market_Systems
The route is set to Market Link and for any reason pricing is not available and a republish timeout is configured in ADS instance.,Market_Systems
"percent, pips, or a fixed amount",FIX_Protocol
a fixed amount in the home currency,FIX_Protocol
"This margin can be expressed in pips, in percent, or a fixed amount in the home currency.",FIX_Protocol
Variable allows setting fix bid and fix ask prices for Spot or Forward.,FIX_Protocol
fix bid prices,FIX_Protocol
fix ask prices,FIX_Protocol
Market Link routes,Market_Systems
"When either ""Pips"", ""Percent"" or ""Fixed Amount"" is selected from the ""Margin Type"" field, the margins for spots, forwards, swaps, futures, interest rate products and commodities can be entered.",FIX_Protocol
"Either ""Pips"", ""Percent"" or ""Fixed Amount"" is selected from the ""Margin Type"" field.",FIX_Protocol
Market Link routing rule,Market_Systems
The margin is expressed in pips or as a fixed amount.,FIX_Protocol
"""Market Link""",Market_Systems
"The routing rules are ""Pricing Server"" or ""Market Link""",Market_Systems
"The margin can be defined in terms of pips, percent, or a fixed amount.",FIX_Protocol
"The margin can be defined in pips, percent, or as a fixed amount.",FIX_Protocol
Bid Commodity Margin is available for Fixed amount and Percentage margin.,FIX_Protocol
Fixed amount,FIX_Protocol
Ask Commodity Margin is available for Fixed amount and Percentage margin.,FIX_Protocol
parameter Trading Venue,Trading_Operations
Default value for Trading Venue,Trading_Operations
Trading Venue field,Trading_Operations
The parameter Trading Venue offers the possibility to differentiate the routing of a price request depending on the venue where it was originated.,Trading_Operations
The parameter Trading Venue,Trading_Operations
"The parameter Trading Venue offers the possibility to differentiate the routing of a price request depending on the venue (EU MTF, UK MTF or OTC) where it was originated.",Trading_Operations
Fixing Reference field,FIX_Protocol
Fixing Reference can be defined as routing condition for NDF and NDS products for RFS and Orders negotiations.,FIX_Protocol
"The price basis is either the 360T price feed from the 360T Market Maker Cockpit, Infotec, or a provider individual price feed through an adapter interface to the provider's pricing system.",Market_Systems
"The Market Link rule is used in combination with the selections under Market Link Provider, Market Link Trading Venue and Market Link Algorithm.",Market_Systems
"The Market Link rule is used in combination with the selections under Market Link Provider, Market Link Trading Venue and Market Link Algorithm.",Trading_Operations
The Forward Strip rule is only available for the RFS negotiation and product FX Forward and is a combination of Market Link and Pricing Server.,Market_Systems
The Market Order is forwarded to a group of providers using a request for quote.,Market_Systems
Market Link Provider group,Market_Systems
Market Link (ML) Trading Venue,Market_Systems
Market Link (ML) Trading Venue,Trading_Operations
Market Link (ML) Trading Venue's default value,Market_Systems
Market Link (ML) Trading Venue's default value,Trading_Operations
Market Link (ML) Trading Venue field,Market_Systems
Market Link (ML) Trading Venue field,Trading_Operations
Market Link Algorithm's default value,Market_Systems
Market Link Algorithm field,Market_Systems
maximum notional limit of liquidity in Market Link route for SEP negotiation,Market_Systems
"The Market Link Provider group must contain only one provider, or a single provider must be selected in the Provider column.",Market_Systems
Market Link (ML) Trading Venue is used to define whether the linked back-to-back request in the context of a market link route should be executed on a specific 360T venue.,Market_Systems
Market Link (ML) Trading Venue is used to define whether the linked back-to-back request in the context of a market link route should be executed on a specific 360T venue.,Trading_Operations
Market Link Algorithm allows selecting the Algorithm group with regards to the duration of the request and number of quotes to be considered.,Market_Systems
Liquidity Limit defines the maximum limit (notional in Home Currency) of liquidity available in the Market Link route in case of SEP negotiation.,Market_Systems
Market Link route,Market_Systems
The Market Link Provider group must contain only one provider.,Market_Systems
Spot Fixing Order,FIX_Protocol
MarketLink,Market_Systems
"A Fixing Order for a Spot product allows Manual Intervention, Pricing Server, Order to Order, and No Pricing, but does not allow MarketLink or Order to RFS.",FIX_Protocol
"A Fixing Order for a Spot product allows Manual Intervention, Pricing Server, Order to Order, and No Pricing, but does not allow MarketLink or Order to RFS.",Market_Systems
"An If Done Order for a Forward product allows Manual Intervention, Pricing Server, and Order to Order, but does not allow MarketLink, Order to RFS, or No Pricing.",Market_Systems
"An If Done Order for a Spot product allows Manual Intervention, Pricing Server, and Order to Order, but does not allow MarketLink, Order to RFS, or No Pricing.",Market_Systems
"A Limit Order for a Forward product allows Manual Intervention, Pricing Server, Order to Order, and No Pricing, but does not allow MarketLink or Order to RFS.",Market_Systems
"A Limit Order for a Future product allows Manual Intervention, Pricing Server, Order to Order, and No Pricing, but does not allow MarketLink or Order to RFS.",Market_Systems
"A Limit Order for a Spot product allows Manual Intervention, Pricing Server, Order to Order, and No Pricing, but does not allow MarketLink or Order to RFS.",Market_Systems
"A Limit Order for a Swap product allows Manual Intervention, Pricing Server, and Order to Order, but does not allow MarketLink, Order to RFS, or No Pricing.",Market_Systems
"A Loop Order for a Forward product allows Manual Intervention and Order to Order, but does not allow Pricing Server, MarketLink, Order to RFS, or No Pricing.",Market_Systems
"A Loop Order for a Spot product allows Manual Intervention and Order to Order, but does not allow Pricing Server, MarketLink, Order to RFS, or No Pricing.",Market_Systems
"A Market Order for a Forward product allows Manual Intervention, Pricing Server, Order to RFS, Order to Order, and No Pricing, but does not allow MarketLink.",Market_Systems
"A Market Order for a Future product allows Manual Intervention, Pricing Server, Order to RFS, Order to Order, and No Pricing, but does not allow MarketLink.",Market_Systems
"A Market Order for a Spot product allows Manual Intervention, Pricing Server, Order to RFS, Order to Order, and No Pricing, but does not allow MarketLink.",Market_Systems
"A Market Order for a Swap product allows Manual Intervention, Pricing Server, Order to RFS, Order to Order, and No Pricing, but does not allow MarketLink.",Market_Systems
"A Market Order for an NDF product allows Manual Intervention, Pricing Server, Order to RFS, Order to Order, and No Pricing, but does not allow MarketLink.",Market_Systems
"An OCO order for a Forward product allows Manual Intervention, Pricing Server, Order to Order, and No Pricing, but does not allow MarketLink or Order to RFS.",Market_Systems
"An OCO order for a Spot product allows Manual Intervention, Pricing Server, Order to Order, and No Pricing, but does not allow MarketLink or Order to RFS.",Market_Systems
A Spot Order uses a Market Order strategy.,Market_Systems
MarketLink is not available for that negotiation.,Market_Systems
Fixed Amount margin,FIX_Protocol
A Fixed Amount margin will not be available for any rule containing SEP negotiation.,FIX_Protocol
A Fixed Amount margin will not be available for that rule.,FIX_Protocol
Orders Forward Fixing Order,FIX_Protocol
Orders Forward Market Order,Market_Systems
Orders Spot Fixing Order,FIX_Protocol
Orders Spot Market Order,Market_Systems
Orders Swap Market Order,Market_Systems
Orders NDF Market Order,Market_Systems
"RFS supports NDF with Pip, %, Hybrid, Fixed amount, and Annual % margin types.",FIX_Protocol
"RFS supports NDS with Pip, %, Hybrid, and Fixed amount margin types.",FIX_Protocol
"RFS supports Loan with Pip, %, Fixed amount, and Annual % margin types.",FIX_Protocol
"RFS supports Deposit with Pip, %, Fixed amount, and Annual % margin types.",FIX_Protocol
RFS supports Commodity Asia Swap with % and Fixed amount margin types.,FIX_Protocol
RFS supports Commodity Bullet Swap with % and Fixed amount margin types.,FIX_Protocol
"RFS supports Future with Pip, %, and Fixed amount margin types.",FIX_Protocol
"Forward Orders support Fixing Order with Pip, %, and Hybrid margin types.",FIX_Protocol
"Forward Orders support Market Order with Pip, %, and Hybrid margin types.",Market_Systems
"Spot Orders support Fixing Order with Pip, %, and Hybrid margin types.",FIX_Protocol
"Spot Orders support Market Order with Pip, %, and Hybrid margin types.",Market_Systems
"Swap Orders support Market Order with Pip, %, and Hybrid margin types.",Market_Systems
"NDF Orders support Market Order with Pip, %, and Hybrid margin types.",Market_Systems
"Future Orders support Limit Order with Pip, %, Hybrid, and Fixed amount margin types.",FIX_Protocol
Basis Fixed Amount,FIX_Protocol
Market link trading venue,Market_Systems
Trading venue,Trading_Operations
Routing Rule 7081 Fixing reference was updated to Any.,FIX_Protocol
Routing Rule 7081 Market link trading venue was updated to OTC.,Market_Systems
Routing Rule 7081 Trading venue was updated to Any.,Trading_Operations
"360 Trading Networks LLC is located in Dubai, United Arab Emirates, and can be contacted by phone.",Trading_Operations
"360 Trading Networks, Inc is located in New York, USA, and can be contacted by phone and fax.",Trading_Operations
"ThreeSixty Trading Networks (India) Pvt Ltd is located in Mumbai, India, and can be contacted by phone.",Trading_Operations
API user type,API_Systems
A Daily Gross Trading Limit is applied.,Trading_Operations
Permissioned Trading Relationships,Trading_Operations
Trading Limits Profile,Trading_Operations
Daily Gross Trading Limit Types,Trading_Operations
Specific Segments of Trading Portfolio,Trading_Operations
"""Trading Limits"" profile",Trading_Operations
This user guide describes the functionalities provided for the Trading Limits profile.,Trading_Operations
The Trading Limits profile can be administrated via Risk Portfolio in the Bridge Administration panel.,Trading_Operations
Trading limits can be defined for many parameters.,Trading_Operations
These trades are also initiated through taker API interfaces.,API_Systems
The 'Trading Limits' profile grants access to part of the configuration parameters.,Trading_Operations
Limits Monitor can be administrated manually via Risk Portfolio module within the Bridge Administration tool as well as automatically via API.,API_Systems
"As well as created groups, all dealers who can trade (Trader, Treasurer, Hybrid and API types of users) and are active will appear as available value for this parameter.",API_Systems
IV Daily Net Trading Limit,Trading_Operations
Maily Net Trading Limit,Trading_Operations
Daily Cross Trading Limit,Trading_Operations
Two different algorithms are available for Trading Limits Profile.,Trading_Operations
The Daily Gross Trading Limit algorithm can limit the total intraday trading volume for the current trading day.,Trading_Operations
Trading Limit profile,Trading_Operations
Only the gross notional amount for the current date trades are accounted for Trading Limit profile.,Trading_Operations
Daily Gross Trading Limit algorithm limits the total gross volume of trades done on a single trading day.,Trading_Operations
"For Swap/NDS, Daily Gross Trading Algorithm only considers the larger amount of both of the legs.",Trading_Operations
"Swap/NDS, Daily Gross Trading Algorithm considers the largest amount of both legs.",Trading_Operations
360T`s Limits Monitor allows authorized users to reset the limit usage calculation for Daily Net and Gross Trading algorithms.,Trading_Operations
Market Rate Counterparts,Market_Systems
"ThreeSixty Trading Networks (India) Pvt Ltd is located at Level 8, Vibgyor Towers, G Block, C-62, Bandra Kurla Complex, Mumbai – 400 051, India.",Trading_Operations
ThreeSixty Trading Networks (India) Pvt Ltd has the phone number +91 22 4090 7165.,Trading_Operations
ThreeSixty Trading Networks (India) Pvt Ltd has the fax number +91 22 4090 7070.,Trading_Operations
User Guide 360T Swap Execution Facility for the Market Maker,Market_Systems
functionality for Market Participant trading as Market Maker,Market_Systems
This user manual describes the functionality available for the Market Participant trading as Market Maker under the 360T Swap Execution Facility.,Market_Systems
Trading is marked as SEF.,Trading_Operations
"Non deliverable forwards are outright contracts for non-convertible foreign currencies, which are cash-settled by using the difference between the agreed forward rate and the spot rate on the fixing date, which is usually 2 working days before the settlement date.",FIX_Protocol
all users of the Trading System,Trading_Operations
The maximum notional amount is restricted to a fixed digit length determined by 360T based upon credit and market risk considerations.,FIX_Protocol
The Fixing Date will automatically be populated by the Trading System based on the spot date convention of the relevant currencies.,FIX_Protocol
The Fixing Date will automatically be populated by the Trading System based on the spot date convention of the relevant currencies.,Trading_Operations
The Fixing Date is automatically populated by the Trading System.,FIX_Protocol
The Fixing Date is automatically populated by the Trading System.,Trading_Operations
The Fixing Date is populated.,FIX_Protocol
The Trading System automatically sends the requester's RFQ to the liquidity providers.,Trading_Operations
all Trading System users,Trading_Operations
"Notification of execution is sent to the counterparties to a transaction, but is not sent to all users of the Trading System.",Trading_Operations
The fixed digit length is determined by 360T.,FIX_Protocol
The fixed digit length is based upon credit and market risk considerations.,FIX_Protocol
"A RFS matches one or several orders in terms of Action, Currency Couple, Effective Date and Maturity Date, Fixing Reference/Expiry.",FIX_Protocol
"A RFS matches one or several orders in terms of Action, Currency Couple, Effective Date, Maturity Date, Fixing Reference/Expiry, notional amount, and notional currency.",FIX_Protocol
Market Maker Order API,API_Systems
Market Maker Order API,Market_Systems
API Version 7.0,API_Systems
FIX Protocol Version FIX.4.4,FIX_Protocol
FIX Market Maker Order API,API_Systems
FIX Market Maker Order API,FIX_Protocol
FIX Market Maker Order API,Market_Systems
FIX Business Messages,FIX_Protocol
The 360T Market Maker Order API implements FIX Protocol Version FIX.4.4.,API_Systems
360T Market Maker Order API,API_Systems
The 360T Market Maker Order API implements FIX Protocol Version FIX.4.4.,FIX_Protocol
The 360T Market Maker Order API implements FIX Protocol Version FIX.4.4.,Market_Systems
360T Market Maker Order API,Market_Systems
This document provides an overview of the FIX Market Maker Order API.,API_Systems
This document provides an overview of the FIX Market Maker Order API.,FIX_Protocol
This document provides an overview of the FIX Market Maker Order API.,Market_Systems
The FIX Market Maker Order API is offered by 360 Treasury Systems.,API_Systems
The FIX Market Maker Order API is offered by 360 Treasury Systems.,FIX_Protocol
The FIX Market Maker Order API is offered by 360 Treasury Systems.,Market_Systems
The document provides an overview of the FIX Market Maker Order API.,API_Systems
The document provides an overview of the FIX Market Maker Order API.,FIX_Protocol
The document provides an overview of the FIX Market Maker Order API.,Market_Systems
The document contains an overview of the general workflow and detailed specifications of utilized FIX messages.,FIX_Protocol
"If required by the API, 360T will provide a password.",API_Systems
A FIX session reset is performed for established connections according to the schedule detailed in appendix [10] [FIX Session Reset].,FIX_Protocol
FIX is the communications protocol 360T and its clients will both have to support.,FIX_Protocol
A password is required by the API.,API_Systems
A FIX session reset is performed.,FIX_Protocol
Fix session details,FIX_Protocol
"Fix session details like start time, end time, reset rules, ping intervals should be agreed upon during interface development.",FIX_Protocol
Either end of a FIX connection has not sent any data for HeartBtInt seconds.,FIX_Protocol
termination of FIX session,FIX_Protocol
The Logout message initiates the termination of a FIX session.,FIX_Protocol
The Logout message confirms the termination of a FIX session.,FIX_Protocol
FixingReference (Tag 7075),FIX_Protocol
FixingDate (Tag 7543),FIX_Protocol
"Possible values for the order type are Market, Limit, and Stop.",Market_Systems
'5' is the Trading venue transaction identifier.,Trading_Operations
Trading venue transaction identifier,Trading_Operations
Market (1),Market_Systems
"Possible values for OrdType are Market, Limit, and Stop.",Market_Systems
'1' (Market),Market_Systems
"For OCO orders, OrdType can be Market, Limit, or Stop.",Market_Systems
TradingCapacity,Trading_Operations
TradingCapacity should always be 'DEAL'.,Trading_Operations
TradingCapacity is required if ExecutionVenueType is '3'.,Trading_Operations
TradingCapacity is required.,Trading_Operations
MsgType <35> of FIX message,FIX_Protocol
A FIX session reset is performed according to the schedule defined in the following table by default.,FIX_Protocol
Market orders support,Market_Systems
FixingReference to the NewOrderMessage,FIX_Protocol
FixingDate to the NewOrderMessage,FIX_Protocol
Market orders support was added.,Market_Systems
FixingReference and FixingDate were added to the NewOrderMessage.,FIX_Protocol
FixingDate,FIX_Protocol
Version 1.1 added Market orders support.,Market_Systems
Version 6.1 added FixingReference and FixingDate to the NewOrderMessage.,FIX_Protocol
GCT Money Market Configuration,Market_Systems
TEX Multidealer Trading System,Trading_Operations
configuration of Money Market (MM) products,Market_Systems
Money Market Funds,Market_Systems
It describes the configuration of Money Market (MM) products.,Market_Systems
"It describes the configuration of Money Market (MM) products, the setting of counterpart relationships and user rights.",Market_Systems
Different Money Market products require specific configurations in the GCT before they can be used by the requester and provider side.,Market_Systems
Different Money Market products require specific configurations in the GCT.,Market_Systems
Market Type,Market_Systems
fixed length,FIX_Protocol
Market Type defines if it is a primary or secondary paper.,Market_Systems
"Isin is an international identifier, only required for secondary papers, has fixed length and ID validation.",FIX_Protocol
"Cusip is a US identifier, only required for secondary papers, has fixed length and ID validation.",FIX_Protocol
"360T Id is a unique 360T ID, generated automatically by the system, has fixed length and ID validation.",FIX_Protocol
360 Trading Networks,Trading_Operations
360 Trading Networks LLC is located in Dubai International Financial Centre.,Trading_Operations
The phone number for 360 Trading Networks LLC is +971 4 491 5134.,Trading_Operations
"360 Trading Networks, Inc is located at 708 Third Avenue in New York.",Trading_Operations
"The phone number for 360 Trading Networks, Inc is ****** 776 2900.",Trading_Operations
"The fax number for 360 Trading Networks, Inc is ****** 776 2902.",Trading_Operations
"field ""Trading Type""",Trading_Operations
selection of Trading Type,Trading_Operations
"The field ""Trading Type"" allows the specification of three available trading permissions.",Trading_Operations
The selection of the Trading Type depends on the particular set-up.,Trading_Operations
selection of the Trading Type,Trading_Operations
TEX Multidealer Trading System User Guide,Trading_Operations
creation of new prefix,FIX_Protocol
A new prefix can be created.,FIX_Protocol
User Trading Rights,Trading_Operations
Trading Rights,Trading_Operations
The Individual Details tab contains the following fields and information for TEX users: - Login Name (PREFIX.Lastname) - Last Name - First Name - Description (free text field) - Email - Phone Number - Fax Number - Salutation - Position (Front Office with trading rights; Back Offic,FIX_Protocol
"360 Trading Networks LLC in the United Arab Emirates is located at Dubai International Financial Centre, Liberty House, Level 8, App. 810C, P.O. Box 482036 Dubai, with a phone number of +971 4 431 5134.",Trading_Operations
"360 Trading Networks, Inc in the USA is located at 521 Fifth Avenue, 38th Floor, New York, NY 10175, with a phone number of ****** 776 2900.",Trading_Operations
