#!/usr/bin/env python3
"""
<PERSON>ript to generate missing embedding CSV files using a smaller, more memory-efficient model.
"""

import csv
import gc
import torch
from pathlib import Path
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import Sen<PERSON><PERSON><PERSON>mbedding

def setup_small_embedding_model():
    """Setup a smaller embedding model that won't cause memory issues."""
    print("📦 Setting up memory-efficient embedding model...")
    
    # Use a much smaller model
    device = "mps" if torch.backends.mps.is_available() else "cpu"
    print(f"Using device: {device}")
    
    try:
        # Use all-MiniLM-L6-v2 which is much smaller (22MB vs 7GB)
        small_model = SentenceTransformer('all-MiniLM-L6-v2', device=device)
        sentence_encoder = SentenceEmbedding(small_model)
        
        print(f"✅ Successfully loaded all-MiniLM-L6-v2")
        print(f"Model dimension: {small_model.get_sentence_embedding_dimension()}")
        
        return sentence_encoder
        
    except Exception as e:
        print(f"❌ Error loading embedding model: {str(e)}")
        return None

def clear_memory():
    """Clear GPU/MPS memory."""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    elif torch.backends.mps.is_available():
        torch.mps.empty_cache()

def generate_node_embeddings_small(input_file: Path, output_file: Path, sentence_encoder):
    """Generate node embeddings CSV file."""
    print(f"📊 Generating node embeddings...")
    print(f"  Input:  {input_file}")
    print(f"  Output: {output_file}")
    
    with open(input_file, "r", encoding='utf-8') as csvfile_node:
        with open(output_file, "w", newline='', encoding='utf-8') as csvfile_node_emb:
            reader_node = csv.reader(csvfile_node)
            writer_node = csv.writer(csvfile_node_emb)
            
            # Write header
            writer_node.writerow(["name:ID", "type", "file_id", "concepts", "synsets", "embedding:STRING", ":LABEL"])
            
            # Process in batches
            batch_size = 1024
            batch_nodes = []
            batch_rows = []
            processed_count = 0
            
            for row in reader_node:
                if row[0] == "name:ID":
                    continue
                    
                batch_nodes.append(row[0])
                batch_rows.append(row)
                
                if len(batch_nodes) == batch_size:
                    # Generate embeddings for batch
                    node_embeddings = sentence_encoder.encode(batch_nodes, batch_size=batch_size, show_progress_bar=False)
                    node_embedding_dict = dict(zip(batch_nodes, node_embeddings))
                    
                    # Write batch to file
                    for row in batch_rows:
                        new_row = [row[0], row[1], "", row[2], row[3], node_embedding_dict[row[0]].tolist(), row[4]]
                        writer_node.writerow(new_row)
                    
                    processed_count += len(batch_nodes)
                    print(f"    Processed {processed_count} nodes...")
                    
                    # Clear memory
                    del node_embeddings, node_embedding_dict
                    clear_memory()
                    
                    batch_nodes = []
                    batch_rows = []
            
            # Process remaining nodes
            if len(batch_nodes) > 0:
                node_embeddings = sentence_encoder.encode(batch_nodes, batch_size=batch_size, show_progress_bar=False)
                node_embedding_dict = dict(zip(batch_nodes, node_embeddings))
                
                for row in batch_rows:
                    new_row = [row[0], row[1], "", row[2], row[3], node_embedding_dict[row[0]].tolist(), row[4]]
                    writer_node.writerow(new_row)
                
                processed_count += len(batch_nodes)
                clear_memory()
            
    print(f"  ✅ Generated embeddings for {processed_count} nodes")

def generate_text_embeddings_small(input_file: Path, output_file: Path, sentence_encoder):
    """Generate text node embeddings CSV file."""
    print(f"📊 Generating text embeddings...")
    print(f"  Input:  {input_file}")
    print(f"  Output: {output_file}")
    
    batch_size = 512
    processed_count = 0
    
    with open(input_file, "r", encoding='utf-8') as csvfile_text_node:
        with open(output_file, "w", newline='', encoding='utf-8') as csvfile_text_node_emb:
            reader_text_node = csv.reader(csvfile_text_node)
            writer_text_node = csv.writer(csvfile_text_node_emb)
            
            # Write header
            writer_text_node.writerow(["text_id:ID", "original_text", ":LABEL", "embedding:STRING"])
            
            batch_text_nodes = []
            batch_rows = []
            
            for row in reader_text_node:
                if row[0] == "text_id:ID":
                    continue
                
                batch_text_nodes.append(row[1])  # original_text is in column 1
                batch_rows.append(row)
                
                if len(batch_text_nodes) == batch_size:
                    # Generate embeddings for batch
                    text_node_embeddings = sentence_encoder.encode(batch_text_nodes, batch_size=batch_size, show_progress_bar=False)
                    text_node_embedding_dict = dict(zip(batch_text_nodes, text_node_embeddings))
                    
                    # Write batch to file
                    for row in batch_rows:
                        embedding = text_node_embedding_dict[row[1]].tolist()
                        new_row = [row[0], row[1], row[2], embedding]
                        writer_text_node.writerow(new_row)
                    
                    processed_count += len(batch_text_nodes)
                    print(f"    Processed {processed_count} text nodes...")
                    
                    # Clear memory after each batch
                    del text_node_embeddings, text_node_embedding_dict
                    clear_memory()
                    
                    batch_text_nodes = []
                    batch_rows = []
            
            # Process remaining text nodes
            if len(batch_text_nodes) > 0:
                text_node_embeddings = sentence_encoder.encode(batch_text_nodes, batch_size=batch_size, show_progress_bar=False)
                text_node_embedding_dict = dict(zip(batch_text_nodes, text_node_embeddings))
                
                for row in batch_rows:
                    embedding = text_node_embedding_dict[row[1]].tolist()
                    new_row = [row[0], row[1], row[2], embedding]
                    writer_text_node.writerow(new_row)
                
                processed_count += len(batch_text_nodes)
                clear_memory()
            
    print(f"  ✅ Generated embeddings for {processed_count} text nodes")

def generate_edge_embeddings_small(input_file: Path, output_file: Path, sentence_encoder):
    """Generate edge embeddings CSV file."""
    print(f"📊 Generating edge embeddings...")
    print(f"  Input:  {input_file}")
    print(f"  Output: {output_file}")
    
    batch_size = 512
    processed_count = 0
    
    with open(input_file, "r", encoding='utf-8') as csvfile_edge:
        with open(output_file, "w", newline='', encoding='utf-8') as csvfile_edge_emb:
            reader_edge = csv.reader(csvfile_edge)
            writer_edge = csv.writer(csvfile_edge_emb)
            
            # Write header
            writer_edge.writerow([":START_ID", ":END_ID", "relation", "file_id", "concepts", "synsets", "embedding:STRING", ":TYPE"])
            
            batch_edges = []
            batch_rows = []
            
            for row in reader_edge:
                if row[0] == ":START_ID":
                    continue
                
                # Create edge string: "start_id relation end_id"
                edge_string = " ".join([row[0], row[2], row[1]])
                batch_edges.append(edge_string)
                batch_rows.append(row)
                
                if len(batch_edges) == batch_size:
                    # Generate embeddings for batch
                    edge_embeddings = sentence_encoder.encode(batch_edges, batch_size=batch_size, show_progress_bar=False)
                    edge_embedding_dict = dict(zip(batch_edges, edge_embeddings))
                    
                    # Write batch to file
                    for row in batch_rows:
                        edge_string = " ".join([row[0], row[2], row[1]])
                        new_row = [row[0], row[1], row[2], "", row[3], row[4], edge_embedding_dict[edge_string].tolist(), row[5]]
                        writer_edge.writerow(new_row)
                    
                    processed_count += len(batch_edges)
                    print(f"    Processed {processed_count} edges...")
                    
                    # Clear memory after each batch
                    del edge_embeddings, edge_embedding_dict
                    clear_memory()
                    
                    batch_edges = []
                    batch_rows = []
            
            # Process remaining edges
            if len(batch_edges) > 0:
                edge_embeddings = sentence_encoder.encode(batch_edges, batch_size=batch_size, show_progress_bar=False)
                edge_embedding_dict = dict(zip(batch_edges, edge_embeddings))
                
                for row in batch_rows:
                    edge_string = " ".join([row[0], row[2], row[1]])
                    new_row = [row[0], row[1], row[2], "", row[3], row[4], edge_embedding_dict[edge_string].tolist(), row[5]]
                    writer_edge.writerow(new_row)
                
                processed_count += len(batch_edges)
                clear_memory()
            
    print(f"  ✅ Generated embeddings for {processed_count} edges")

def main():
    """Main function to generate missing embedding files."""
    
    base_dir = Path("import/pdf_dataset")
    
    print("🚀 Generating missing embedding CSV files (Small Model)")
    print("=" * 70)
    
    # Setup embedding model
    sentence_encoder = setup_small_embedding_model()
    if not sentence_encoder:
        return False
    
    triples_csv_dir = base_dir / "triples_csv"
    concept_csv_dir = base_dir / "concept_csv"
    
    # Clean up existing broken files first
    broken_files = [
        triples_csv_dir / "triple_nodes__from_json_with_emb.csv",
        triples_csv_dir / "text_nodes__from_json_with_emb.csv", 
        triples_csv_dir / "triple_edges__from_json_with_concept_with_emb.csv"
    ]
    
    for file in broken_files:
        if file.exists():
            file.unlink()
            print(f"🗑️ Removed broken file: {file}")
    
    # Generate all embedding files
    embedding_tasks = [
        {
            'input': triples_csv_dir / "triple_nodes__from_json_without_emb.csv",
            'output': triples_csv_dir / "triple_nodes__from_json_with_emb.csv",
            'function': generate_node_embeddings_small,
            'name': 'Node Embeddings'
        },
        {
            'input': triples_csv_dir / "text_nodes__from_json.csv", 
            'output': triples_csv_dir / "text_nodes__from_json_with_emb.csv",
            'function': generate_text_embeddings_small,
            'name': 'Text Node Embeddings'
        },
        {
            'input': concept_csv_dir / "triple_edges__from_json_with_concept.csv",
            'output': triples_csv_dir / "triple_edges__from_json_with_concept_with_emb.csv", 
            'function': generate_edge_embeddings_small,
            'name': 'Edge Embeddings'
        }
    ]
    
    for i, task in enumerate(embedding_tasks, 1):
        print(f"\n🔧 Task {i}/3: {task['name']}")
        
        if not task['input'].exists():
            print(f"  ❌ Input file not found: {task['input']}")
            continue
            
        try:
            task['function'](task['input'], task['output'], sentence_encoder)
            print(f"  ✅ Successfully generated {task['name']}")
            
        except Exception as e:
            print(f"  ❌ Error generating {task['name']}: {str(e)}")
            continue
    
    print(f"\n✅ All embedding generation tasks completed!")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Successfully generated missing embedding files!")
    else:
        print("\n❌ Failed to generate missing embedding files!")