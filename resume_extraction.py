#!/usr/bin/env python3
"""
Resume Extraction - Simple script to continue extraction from where it left off.

This is a convenient wrapper around the resume extraction pipeline that uses
sensible defaults for your specific extraction file.

Usage:
    python resume_extraction.py                    # Continue with default settings
    python resume_extraction.py --analyze          # Just analyze, don't extract
    python resume_extraction.py --new-file         # Create new output file instead of appending
"""

import os
import sys
from resume_extraction_pipeline import run_resume_extraction_pipeline
from resume_extraction_analyzer import ResumeExtractionAnalyzer


def main():
    """Simple interface to resume your extraction."""
    
    # Default paths for your specific extraction
    EXISTING_OUTPUT = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    DATA_DIRECTORY = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data"
    
    print("🔄 Resume PDF Knowledge Graph Extraction")
    print("=" * 50)
    print("This script will continue your extraction from exactly where it left off.")
    print()
    
    # Check if files exist
    if not os.path.exists(EXISTING_OUTPUT):
        print(f"❌ Existing output file not found:")
        print(f"   {EXISTING_OUTPUT}")
        print()
        print("Please ensure your extraction output file exists at the expected location.")
        sys.exit(1)
    
    if not os.path.exists(DATA_DIRECTORY):
        print(f"❌ Data directory not found:")
        print(f"   {DATA_DIRECTORY}")
        print()
        print("Please ensure your input data directory exists.")
        sys.exit(1)
    
    # Parse simple command line arguments
    analyze_only = "--analyze" in sys.argv
    new_file_mode = "--new-file" in sys.argv
    
    if analyze_only:
        print("🔍 ANALYSIS MODE - Just analyzing resume point")
        print("-" * 40)
        
        try:
            # Run analysis only
            analyzer = ResumeExtractionAnalyzer(EXISTING_OUTPUT, DATA_DIRECTORY)
            resume_point = analyzer.calculate_resume_point(batch_size=16)
            analyzer.print_resume_analysis(resume_point)
            
            print("\n📋 To continue extraction, run:")
            print("   python resume_extraction.py")
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            sys.exit(1)
    
    else:
        print("🚀 EXTRACTION MODE - Continuing from where you left off")
        print("-" * 40)
        
        output_mode = "new" if new_file_mode else "append"
        if new_file_mode:
            print("📝 Will create new output file (--new-file mode)")
        else:
            print("📝 Will append to existing output file (default mode)")
        print()
        
        try:
            # Run the resume extraction
            success = run_resume_extraction_pipeline(
                existing_output_file=EXISTING_OUTPUT,
                data_directory=DATA_DIRECTORY,
                dataset_name="pdf_dataset",
                batch_size=16,
                filename_pattern="from_json",
                output_mode=output_mode
            )
            
            if success:
                print("\n🎉 SUCCESS! Your extraction has been resumed and completed.")
                print()
                print("📋 Next Steps:")
                print("1. Your knowledge graph extraction is now complete")
                print("2. Continue with the rest of your pipeline:")
                print("   - CSV conversion")
                print("   - Concept generation") 
                print("   - Neo4j import")
                print("   - Vector embeddings")
                
            else:
                print("\n❌ Resume extraction failed. Check the error messages above.")
                sys.exit(1)
                
        except KeyboardInterrupt:
            print("\n⏹️ Extraction interrupted by user.")
            sys.exit(1)
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")
            sys.exit(1)


def show_help():
    """Show help information."""
    print("🔄 Resume Extraction - Help")
    print("=" * 30)
    print()
    print("This script continues your PDF knowledge graph extraction from exactly")
    print("where it left off, using your existing Gemini-2.5 Flash output file.")
    print()
    print("Usage:")
    print("  python resume_extraction.py                # Continue extraction (default)")
    print("  python resume_extraction.py --analyze      # Just analyze resume point")
    print("  python resume_extraction.py --new-file     # Create new output file")
    print("  python resume_extraction.py --help         # Show this help")
    print()
    print("Files used:")
    print("  Input:  example_data/ (JSON files from PDF processing)")
    print("  Resume: import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json")
    print("  Output: Same file (append mode) or new timestamped file (new-file mode)")
    print()
    print("The script will:")
    print("1. Analyze your existing output to find the exact resume point")
    print("2. Skip all already-processed chunks automatically")
    print("3. Continue processing from where extraction stopped")
    print("4. Add resume markers to track the continuation")
    print()


if __name__ == "__main__":
    if "--help" in sys.argv or "-h" in sys.argv:
        show_help()
    else:
        main()