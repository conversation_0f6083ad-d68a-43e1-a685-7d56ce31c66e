# Robust PDF Knowledge Graph Extraction Pipeline

An enhanced version of the AutoSchemaKG pipeline with comprehensive **resumability**, **error handling**, and **progress tracking** for long-running knowledge graph extraction operations.

## 🚀 Key Features

### ✅ Resume Functionality
- **Automatic Checkpoints**: State saved every 5 minutes during processing
- **Failure Recovery**: Resume from any failure point without losing progress
- **Document Tracking**: <PERSON><PERSON> already processed documents automatically
- **Stage-Level Resumption**: Continue from the last completed pipeline stage

### ✅ Enhanced Error Handling
- **Automatic Retry**: Intelligent retry logic with exponential backoff
- **Circuit Breaker**: Prevents cascading failures for persistent issues
- **Error Categorization**: Smart handling of API limits, timeouts, network issues
- **Email Notifications**: Optional alerts for critical errors (configurable)

### ✅ Real-Time Progress Tracking
- **Individual File Progress**: Track processing of each PDF file separately
- **ETA Calculations**: Estimated time remaining based on processing history
- **Multi-Level Logging**: Console output + detailed file logs + error logs
- **Memory Monitoring**: Track memory usage and detect potential issues

### ✅ Robust State Management
- **Persistent State**: Pipeline state survives restarts and crashes
- **Processing Statistics**: Detailed metrics on success rates and performance
- **Stage Tracking**: Monitor completion of each pipeline stage
- **Recovery Options**: Multiple ways to handle and recover from failures

## 📦 Installation

The robust pipeline uses the same dependencies as the original AutoSchemaKG pipeline:

```bash
# Install the main package
pip install -e .

# For NV-embed-v2 support (if needed)
pip install -e .[nvembed]
```

## 🎯 Quick Start

### Basic Usage (with resume capability)
```bash
python pdf_kg_extraction_pipeline_robust.py
```

### Force restart from beginning
```bash
python pdf_kg_extraction_pipeline_robust.py --force-restart
```

### Retry only failed operations
```bash
python pdf_kg_extraction_pipeline_robust.py --retry-failed
```

### Tier 1 optimized (for API token limits)
```bash
python pdf_kg_extraction_pipeline_robust.py --tier1
```

## 📖 Detailed Usage

### Command Line Options

```bash
python pdf_kg_extraction_pipeline_robust.py [OPTIONS]

Options:
  -d, --dataset DATASET       Dataset name for output directory (default: pdf_dataset)
  -p, --pattern PATTERN       Filename pattern to match input files (default: all)
  -f, --force-restart         Ignore existing state and start fresh
  -r, --retry-failed          Retry previously failed operations
  -t, --tier1                 Use Tier 1 optimized settings for token limits
  -n, --notifications         Enable email notifications for critical errors
  -e, --email EMAIL           Email address for notifications (requires -n)
  -m, --max-retries RETRIES   Maximum retry attempts (default: 3)
  --examples                  Show usage examples and exit
  --help                      Show help message and exit
```

### Usage Examples

#### 1. Standard Processing with Resume
```bash
# Run with automatic resume capability
python pdf_kg_extraction_pipeline_robust.py

# If the pipeline fails, simply run the same command again
# It will automatically resume from where it left off
```

#### 2. Custom Dataset and Configuration
```bash
# Process with custom dataset name and Tier 1 optimization
python pdf_kg_extraction_pipeline_robust.py \
    --dataset financial_reports \
    --tier1 \
    --max-retries 5
```

#### 3. Error Recovery Scenarios
```bash
# Force complete restart (ignores all previous progress)
python pdf_kg_extraction_pipeline_robust.py --force-restart

# Retry only operations that previously failed
python pdf_kg_extraction_pipeline_robust.py --retry-failed

# Increase retry attempts for problematic operations
python pdf_kg_extraction_pipeline_robust.py --max-retries 10
```

#### 4. Email Notifications
```bash
# Enable email alerts for critical errors
python pdf_kg_extraction_pipeline_robust.py \
    --notifications \
    --email <EMAIL>
```

## 🏗️ Pipeline Architecture

### Core Components

1. **RobustKGExtractor**: Enhanced wrapper around the original KnowledgeGraphExtractor
2. **PipelineStateManager**: Handles checkpoint creation, loading, and state persistence
3. **ProgressTracker**: Real-time progress monitoring with comprehensive logging
4. **ErrorHandler**: Centralized error management with retry logic and recovery strategies

### Processing Stages

The robust pipeline processes documents through these stages with enhanced error handling:

1. **Initialization** - Setup models and validate prerequisites
2. **Triple Extraction** - Extract entities, relations, and events from text
3. **CSV Conversion** - Convert JSON results to CSV format
4. **Concept Generation** - Generate higher-level concepts from triples
5. **Concept CSV Creation** - Merge concept data with extracted triples
6. **Numeric ID Addition** - Add numeric identifiers for Neo4j import
7. **GraphML Conversion** - Convert to NetworkX-compatible format
8. **Embedding Generation** - Create vector embeddings for semantic search
9. **FAISS Index Creation** - Build fast similarity search indexes

## 📊 Monitoring and Logging

### Log Files

The pipeline creates detailed log files for monitoring:

- `pipeline_log_{dataset}_{timestamp}.log` - Detailed processing logs
- `pipeline_errors_{dataset}_{timestamp}.log` - Error-specific logs
- `pipeline_state_{dataset}.json` - Current pipeline state (checkpoint file)

### Progress Information

During processing, you'll see real-time updates including:

```
📊 Progress Update:
   Overall: 45.2% (19/42 files)
   Status: ✅ 18 completed, ⚠️ 1 failed, 🔄 1 processing
   ETA: 2.3h
   Memory: 1.2GB (peak: 1.5GB)
   🔄 technical_manual.json: 67.3% (145/215 docs) - ETA: 15.2m
```

### Error Tracking

Comprehensive error information:

```
🚨 Error Summary:
   Total errors: 12
   Resolved errors: 10  
   Unresolved errors: 2
   Recent errors (1h): 3
   Recovery success rate: 83.3%
```

## 🔧 Configuration

### Tier 1 vs Standard Mode

**Standard Mode** (default):
- Batch size: 8 triples, 16 concepts
- Max tokens: 8192
- Workers: 2
- Optimized for speed and quality

**Tier 1 Mode** (`--tier1`):
- Batch size: 1 triple, 5 concepts  
- Max tokens: 8192
- Workers: 1
- Optimized for API token limits

### Error Handling Configuration

The pipeline automatically configures error handling:

- **Retry Attempts**: 3 by default (configurable with `--max-retries`)
- **Backoff Strategy**: Exponential with jitter (2s, 4s, 8s, ...)
- **Circuit Breaker**: Opens after 5 failures, retries after 5 minutes
- **Rate Limiting**: Automatic detection and handling of API rate limits

## 🔄 Resume and Recovery

### Automatic Resume

The pipeline automatically detects existing progress:

```
📋 Existing pipeline state found - checking resumability
✅ Pipeline can be resumed from previous state
📊 Found 156 previously processed documents
🔄 Resuming from stage: concept_generation
```

### Manual Recovery Options

1. **Resume Normal** - Continue from last checkpoint
2. **Force Restart** - Start completely fresh (`--force-restart`)
3. **Retry Failed** - Only retry previously failed operations (`--retry-failed`)

### State Files

- `pipeline_state_{dataset}.json` - Main state file with progress tracking
- `pipeline_state_{dataset}.json.backup` - Automatic backup of previous state

## 🧪 Testing

Test the enhanced pipeline components:

```bash
# Run component tests
python test_robust_pipeline.py

# Show pipeline features
python test_robust_pipeline.py --features
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### Pipeline Fails with API Errors
```bash
# Use Tier 1 mode for token limit issues
python pdf_kg_extraction_pipeline_robust.py --tier1

# Increase retry attempts for transient issues
python pdf_kg_extraction_pipeline_robust.py --max-retries 10
```

#### Memory Issues
```bash
# Use smaller batch sizes (Tier 1 mode)
python pdf_kg_extraction_pipeline_robust.py --tier1
```

#### Corrupted State File
```bash
# Force restart to create fresh state
python pdf_kg_extraction_pipeline_robust.py --force-restart
```

#### Stuck on Specific Stage
```bash
# Retry only failed operations
python pdf_kg_extraction_pipeline_robust.py --retry-failed
```

### Getting Help

1. **Check Prerequisites**: Run the pipeline once to validate setup
2. **Review Logs**: Check the detailed log files for error messages
3. **State Information**: Examine `pipeline_state_{dataset}.json` for current status
4. **Component Tests**: Run `python test_robust_pipeline.py` to verify components

## 📈 Performance Considerations

### Recommended Settings

**For 37 PDF files (10+ hour processing)**:
- Use standard mode for best performance
- Enable email notifications for long runs
- Monitor memory usage in logs
- Set max retries to 5 for stability

**For API rate limiting issues**:
- Use `--tier1` mode
- Reduce workers to 1
- Increase retry attempts
- Monitor circuit breaker status

### Expected Processing Times

- **Standard Mode**: ~6-10 hours for 37 PDFs
- **Tier 1 Mode**: ~10-15 hours for 37 PDFs (more stable)
- **Resume Time**: <5 minutes to detect and continue from checkpoints

## 🎯 Next Steps After Pipeline Completion

1. **Review Generated Files**: Check the `import/{dataset}/` directory
2. **Import to Neo4j**: Use the generated `neo4j_import_commands.txt`
3. **Validate Results**: Test knowledge graph queries
4. **Monitor Performance**: Review processing statistics and error logs

## 🤝 Integration with Original Pipeline

The robust pipeline is fully compatible with the original AutoSchemaKG workflow:

- Same input format (JSON files in `example_data/`)
- Same output structure (CSV, GraphML, embeddings)
- Same Neo4j import process
- Enhanced with resumability and error handling

You can switch between the original and robust pipelines seamlessly:

```bash
# Original pipeline
python pdf_kg_extraction_pipeline.py

# Robust pipeline (enhanced)
python pdf_kg_extraction_pipeline_robust.py
```