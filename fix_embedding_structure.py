#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix the embedding directory structure for pdf_dataset.
This script creates the missing concept-enhanced CSV files and fixes the directory structure.
"""

import csv
import os
import shutil
from pathlib import Path

def load_entity_concept_mappings(mappings_file):
    """Load entity to concept mappings from CSV file."""
    mappings = {}
    with open(mappings_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            entity = row['entity']
            concept = row['concept']
            if entity not in mappings:
                mappings[entity] = []
            mappings[entity].append(concept)
    return mappings

def create_concept_enhanced_edges(input_edges_file, output_edges_file, entity_concept_mappings):
    """Create concept-enhanced edges file."""
    print(f"Creating concept-enhanced edges file: {output_edges_file}")
    
    with open(input_edges_file, 'r', encoding='utf-8') as infile:
        with open(output_edges_file, 'w', newline='', encoding='utf-8') as outfile:
            reader = csv.DictReader(infile)
            
            # Write header with proper format
            fieldnames = [':START_ID', ':END_ID', 'relation', 'concepts', 'synsets', ':TYPE']
            writer = csv.DictWriter(outfile, fieldnames=fieldnames)
            writer.writeheader()
            
            processed_count = 0
            for row in reader:
                start_id = row[':START_ID']
                end_id = row[':END_ID']
                
                # Get concepts for start and end entities
                start_concepts = entity_concept_mappings.get(start_id, [])
                end_concepts = entity_concept_mappings.get(end_id, [])
                
                # Combine concepts from both entities
                all_concepts = list(set(start_concepts + end_concepts))
                
                # Create the enhanced row
                enhanced_row = {
                    ':START_ID': start_id,
                    ':END_ID': end_id,
                    'relation': row['relation'],
                    'concepts': str(all_concepts) if all_concepts else '[]',
                    'synsets': row.get('synsets', '[]'),
                    ':TYPE': row[':TYPE']
                }
                
                writer.writerow(enhanced_row)
                processed_count += 1
                
                if processed_count % 1000 == 0:
                    print(f"  Processed {processed_count} edges...")
    
    print(f"  ✅ Created concept-enhanced edges file with {processed_count} edges")

def main():
    """Main function to fix the embedding structure."""
    base_dir = Path("import/pdf_dataset")
    
    print("🔧 Fixing embedding directory structure for pdf_dataset")
    print("=" * 60)
    
    # Check if base directory exists
    if not base_dir.exists():
        print(f"❌ Base directory {base_dir} not found!")
        return False
    
    # Load entity-concept mappings
    mappings_file = base_dir / "concept_csv" / "entity_concept_mappings.csv"
    if not mappings_file.exists():
        print(f"❌ Entity-concept mappings file not found: {mappings_file}")
        return False
    
    print(f"📖 Loading entity-concept mappings from {mappings_file}")
    entity_concept_mappings = load_entity_concept_mappings(mappings_file)
    print(f"  ✅ Loaded mappings for {len(entity_concept_mappings)} entities")
    
    # Create concept-enhanced edges file
    input_edges = base_dir / "triples_csv" / "triple_edges__from_json_without_emb.csv"
    output_edges = base_dir / "concept_csv" / "triple_edges__from_json_with_concept.csv"
    
    if not input_edges.exists():
        print(f"❌ Input edges file not found: {input_edges}")
        return False
    
    # Ensure concept_csv directory exists
    output_edges.parent.mkdir(exist_ok=True)
    
    # Create the concept-enhanced edges file
    create_concept_enhanced_edges(input_edges, output_edges, entity_concept_mappings)
    
    print("\n✅ Phase 1 completed: Created concept-enhanced edges file")
    print(f"  📁 Created: {output_edges}")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Successfully fixed concept CSV structure!")
    else:
        print("\n❌ Failed to fix concept CSV structure!")