"""
ProgressTracker - Real-time progress monitoring and logging for knowledge graph extraction pipeline.

Provides comprehensive progress visualization, ETA calculations, and multi-level logging
for long-running pipeline operations.
"""

import logging
import sys
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import threading


@dataclass
class FileProgress:
    """Track progress for individual file processing."""
    filename: str
    status: str  # 'pending', 'processing', 'completed', 'failed'
    current_stage: str
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    documents_processed: int = 0
    documents_total: int = 0
    current_document: str = ""
    stages_completed: List[str] = None
    error_message: str = ""
    
    def __post_init__(self):
        if self.stages_completed is None:
            self.stages_completed = []
    
    @property
    def duration(self) -> float:
        """Calculate processing duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return time.time() - self.start_time
        return 0.0
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage for document processing."""
        if self.documents_total == 0:
            return 0.0
        return (self.documents_processed / self.documents_total) * 100
    
    @property
    def eta_seconds(self) -> float:
        """Estimate time remaining for this file."""
        if self.documents_processed == 0 or self.duration == 0:
            return 0.0
        
        avg_time_per_doc = self.duration / self.documents_processed
        remaining_docs = self.documents_total - self.documents_processed
        return avg_time_per_doc * remaining_docs


class ProgressTracker:
    """Advanced progress tracking and logging for pipeline operations."""
    
    def __init__(self, dataset_name: str, total_files: int, log_level: str = "INFO"):
        """
        Initialize the progress tracker.
        
        Args:
            dataset_name: Name of the dataset being processed
            total_files: Total number of files to process
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        self.dataset_name = dataset_name
        self.total_files = total_files
        self.start_time = time.time()
        self.file_progress: Dict[str, FileProgress] = {}
        self.current_stage = ""
        self.stages_completed: List[str] = []
        self.pipeline_status = "initializing"
        
        # Performance tracking
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.initial_memory
        
        # Progress display
        self.last_progress_update = 0
        self.progress_update_interval = 5  # seconds
        self.console_width = 80
        
        # Setup logging
        self._setup_logging(log_level)
        
        # Thread safety
        self._lock = threading.Lock()
        
        self.logger.info(f"🚀 Progress tracking initialized for dataset: {dataset_name}")
        self.logger.info(f"📊 Total files to process: {total_files}")
    
    def _setup_logging(self, log_level: str):
        """Setup comprehensive logging with multiple handlers."""
        # Create logger
        self.logger = logging.getLogger(f"pipeline_{self.dataset_name}")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # Console handler (INFO and above)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler for detailed logs (DEBUG and above)
        log_filename = f"pipeline_log_{self.dataset_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(file_handler)
        
        # Error file handler (WARNING and above)
        error_log_filename = f"pipeline_errors_{self.dataset_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        error_handler = logging.FileHandler(error_log_filename, encoding='utf-8')
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(error_handler)
        
        self.logger.info(f"📝 Logging setup complete - Logs: {log_filename}, Errors: {error_log_filename}")
    
    def start_stage(self, stage_name: str, description: str = ""):
        """Start tracking a new pipeline stage."""
        with self._lock:
            self.current_stage = stage_name
            self.pipeline_status = "processing"
            
            self.logger.info("=" * 60)
            self.logger.info(f"🎯 Starting Stage: {stage_name}")
            if description:
                self.logger.info(f"📋 Description: {description}")
            self.logger.info("=" * 60)
            
            self._update_memory_tracking()
    
    def complete_stage(self, stage_name: str):
        """Mark a stage as completed."""
        with self._lock:
            if stage_name not in self.stages_completed:
                self.stages_completed.append(stage_name)
            
            self.logger.info(f"✅ Stage completed: {stage_name}")
            self._update_memory_tracking()
    
    def start_file_processing(self, filename: str, total_documents: int = 0):
        """Start tracking processing for a specific file."""
        with self._lock:
            if filename not in self.file_progress:
                self.file_progress[filename] = FileProgress(
                    filename=filename,
                    status='processing',
                    current_stage=self.current_stage,
                    start_time=time.time(),
                    documents_total=total_documents
                )
            else:
                progress = self.file_progress[filename]
                progress.status = 'processing'
                progress.current_stage = self.current_stage
                if not progress.start_time:
                    progress.start_time = time.time()
                if total_documents > 0:
                    progress.documents_total = total_documents
            
            self.logger.info(f"📁 Processing file: {filename}")
            if total_documents > 0:
                self.logger.info(f"📄 Documents to process: {total_documents}")
    
    def update_file_progress(self, filename: str, documents_processed: int, current_document: str = ""):
        """Update progress for a file being processed."""
        with self._lock:
            if filename in self.file_progress:
                progress = self.file_progress[filename]
                progress.documents_processed = documents_processed
                progress.current_document = current_document
                
                # Log progress periodically
                current_time = time.time()
                if current_time - self.last_progress_update > self.progress_update_interval:
                    self._display_progress_update()
                    self.last_progress_update = current_time
    
    def complete_file_processing(self, filename: str, documents_processed: int = 0):
        """Mark a file as completed."""
        with self._lock:
            if filename in self.file_progress:
                progress = self.file_progress[filename]
                progress.status = 'completed' 
                progress.end_time = time.time()
                progress.current_stage = self.current_stage
                
                if documents_processed > 0:
                    progress.documents_processed = documents_processed
                
                if self.current_stage not in progress.stages_completed:
                    progress.stages_completed.append(self.current_stage)
                
                duration = progress.duration
                self.logger.info(f"✅ File completed: {filename}")
                self.logger.info(f"⏱️  Processing time: {self._format_duration(duration)}")
                if progress.documents_processed > 0:
                    self.logger.info(f"📄 Documents processed: {progress.documents_processed}")
                    avg_time = duration / progress.documents_processed
                    self.logger.info(f"📊 Average time per document: {avg_time:.2f}s")
    
    def fail_file_processing(self, filename: str, error_message: str = ""):
        """Mark a file as failed."""
        with self._lock:
            if filename in self.file_progress:
                progress = self.file_progress[filename]
                progress.status = 'failed'
                progress.end_time = time.time()
                progress.error_message = error_message
                
                self.logger.error(f"❌ File failed: {filename}")
                if error_message:
                    self.logger.error(f"💥 Error: {error_message}")
    
    def _display_progress_update(self):
        """Display comprehensive progress update."""
        try:
            # Calculate overall statistics
            completed_files = len([p for p in self.file_progress.values() if p.status == 'completed'])
            failed_files = len([p for p in self.file_progress.values() if p.status == 'failed'])
            processing_files = len([p for p in self.file_progress.values() if p.status == 'processing'])
            
            overall_progress = (completed_files / self.total_files) * 100 if self.total_files > 0 else 0
            
            # Calculate ETA
            eta_str = self._calculate_eta()
            
            # Memory usage
            current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            if current_memory > self.peak_memory:
                self.peak_memory = current_memory
            
            # Display progress summary
            self.logger.info("📊 Progress Update:")
            self.logger.info(f"   Overall: {overall_progress:.1f}% ({completed_files}/{self.total_files} files)")
            self.logger.info(f"   Status: ✅ {completed_files} completed, ⚠️ {failed_files} failed, 🔄 {processing_files} processing")
            self.logger.info(f"   ETA: {eta_str}")
            self.logger.info(f"   Memory: {current_memory:.1f}MB (peak: {self.peak_memory:.1f}MB)")
            
            # Display currently processing files
            for filename, progress in self.file_progress.items():
                if progress.status == 'processing':
                    if progress.documents_total > 0:
                        doc_progress = progress.progress_percentage
                        eta_file = self._format_duration(progress.eta_seconds)
                        self.logger.info(f"   🔄 {filename}: {doc_progress:.1f}% ({progress.documents_processed}/{progress.documents_total} docs) - ETA: {eta_file}")
                    else:
                        duration = self._format_duration(progress.duration)
                        self.logger.info(f"   🔄 {filename}: processing for {duration}")
            
        except Exception as e:
            self.logger.error(f"Error displaying progress update: {e}")
    
    def _calculate_eta(self) -> str:
        """Calculate estimated time to completion."""
        try:
            completed_files = [p for p in self.file_progress.values() if p.status == 'completed']
            
            if not completed_files:
                return "calculating..."
            
            # Calculate average processing time per file
            total_processing_time = sum(p.duration for p in completed_files)
            avg_time_per_file = total_processing_time / len(completed_files)
            
            # Estimate remaining time
            remaining_files = self.total_files - len(completed_files)
            estimated_remaining_seconds = avg_time_per_file * remaining_files
            
            return self._format_duration(estimated_remaining_seconds)
            
        except Exception:
            return "unknown"
    
    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}m"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}h"
    
    def _update_memory_tracking(self):
        """Update memory usage tracking."""
        try:
            current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            if current_memory > self.peak_memory:
                self.peak_memory = current_memory
        except Exception:
            pass
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get comprehensive processing summary."""
        with self._lock:
            completed_files = [p for p in self.file_progress.values() if p.status == 'completed']
            failed_files = [p for p in self.file_progress.values() if p.status == 'failed']
            processing_files = [p for p in self.file_progress.values() if p.status == 'processing']
            
            total_processing_time = time.time() - self.start_time
            
            summary = {
                'dataset_name': self.dataset_name,
                'pipeline_status': self.pipeline_status,
                'current_stage': self.current_stage,
                'stages_completed': self.stages_completed.copy(),
                'total_files': self.total_files,
                'completed_files': len(completed_files),
                'failed_files': len(failed_files),
                'processing_files': len(processing_files),
                'overall_progress_percentage': (len(completed_files) / self.total_files) * 100 if self.total_files > 0 else 0,
                'total_processing_time': total_processing_time,
                'estimated_time_remaining': self._calculate_eta(),
                'memory_usage': {
                    'initial_mb': self.initial_memory,
                    'current_mb': self.process.memory_info().rss / 1024 / 1024,
                    'peak_mb': self.peak_memory
                },
                'file_details': {}
            }
            
            # Add detailed file information
            for filename, progress in self.file_progress.items():
                summary['file_details'][filename] = {
                    'status': progress.status,
                    'current_stage': progress.current_stage,
                    'duration': progress.duration,
                    'documents_processed': progress.documents_processed,
                    'documents_total': progress.documents_total,
                    'progress_percentage': progress.progress_percentage,
                    'stages_completed': progress.stages_completed.copy(),
                    'error_message': progress.error_message
                }
            
            return summary
    
    def display_final_summary(self):
        """Display final processing summary."""
        summary = self.get_processing_summary()
        
        self.logger.info("🎉 Pipeline Processing Complete!")
        self.logger.info("=" * 60)
        self.logger.info(f"📊 Final Summary for {self.dataset_name}:")
        self.logger.info(f"   Total files: {summary['total_files']}")
        self.logger.info(f"   ✅ Completed: {summary['completed_files']}")
        self.logger.info(f"   ❌ Failed: {summary['failed_files']}")
        self.logger.info(f"   📈 Success rate: {(summary['completed_files']/summary['total_files']*100):.1f}%")
        self.logger.info(f"   ⏱️  Total time: {self._format_duration(summary['total_processing_time'])}")
        self.logger.info(f"   💾 Peak memory: {summary['memory_usage']['peak_mb']:.1f}MB")
        
        # Show failed files if any
        if summary['failed_files'] > 0:
            self.logger.warning("⚠️  Failed files:")
            for filename, details in summary['file_details'].items():
                if details['status'] == 'failed':
                    self.logger.warning(f"   ❌ {filename}: {details['error_message']}")
        
        self.logger.info("=" * 60)
    
    def create_progress_bar(self, current: int, total: int, width: int = 50) -> str:
        """Create a visual progress bar."""
        if total == 0:
            return "[" + "─" * width + "] 0%"
        
        progress = current / total
        filled = int(width * progress)
        
        bar = "█" * filled + "─" * (width - filled)
        percentage = progress * 100
        
        return f"[{bar}] {percentage:.1f}%"
    
    def log_debug(self, message: str):
        """Log debug message."""
        self.logger.debug(message)
    
    def log_info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """Log warning message."""
        self.logger.warning(message)
    
    def log_error(self, message: str):
        """Log error message.""" 
        self.logger.error(message)
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - display final summary."""
        if exc_type is None:
            self.display_final_summary()
        else:
            self.logger.error(f"Pipeline failed with exception: {exc_type.__name__}: {exc_val}")
        return False