#!/usr/bin/env python3
"""
Concept Generation Recovery Script
This script runs concept generation on existing triple extraction results using full context Gemini API.
"""

from atlas_rag.kg_construction.triple_extraction import KnowledgeGraphExtractor
from atlas_rag.kg_construction.triple_config import ProcessingConfig
from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from configparser import ConfigParser
import os

def create_full_context_config(dataset_name="360t_guide_direct_api_v2"):
    """
    Create processing configuration optimized for full 1M token context.
    Uses original optimized values now that token limitations are removed.
    
    Args:
        dataset_name (str): Name of the existing dataset directory
        
    Returns:
        ProcessingConfig: Full context optimized processing parameters
    """
    
    # Load configuration
    config = ConfigParser()
    config.read('config.ini')
    
    model_name = config['settings']['LLM_MODEL']
    
    print(f"🔧 Creating full context processing configuration for {dataset_name}")
    print(f"Model: {model_name}")
    print(f"Rate-limit optimized configuration: Smaller batches to avoid API limits")
    
    # Create full context optimized configuration using restored defaults
    processing_config = ProcessingConfig(
        # Model configuration
        model_path=model_name,
        
        # Data paths - target existing dataset
        data_directory="example_data",
        filename_pattern="",
        output_directory=f"import/{dataset_name}",
        
        # Batch sizes - rate-limit aware for concept generation
        batch_size_triple=16,       # Restored from 2 to 16 (8x increase)
        batch_size_concept=8,       # Reduced from 64 to 8 for rate limit management
        
        # Processing control
        total_shards_triple=1,
        current_shard_triple=0,
        total_shards_concept=1,
        current_shard_concept=0,
        
        # Performance settings - rate-limit optimized
        max_new_tokens=2048,        # Reduced from 8192 to 2048 for concept generation
        max_workers=1,              # Reduced to 1 to avoid rate limits
        
        # Processing options
        use_8bit=False,
        debug_mode=False,
        resume_from=0,
        record=True,                # Enable recording for monitoring
        remove_doc_spaces=True
    )
    
    print("✅ Rate-limit optimized processing configuration created with following settings:")
    print(f"  - Batch size (triples): {processing_config.batch_size_triple}")
    print(f"  - Batch size (concepts): {processing_config.batch_size_concept} (reduced for rate limits)")
    print(f"  - Max tokens: {processing_config.max_new_tokens} (reduced for rate limits)")
    print(f"  - Max workers: {processing_config.max_workers} (sequential processing)")
    print(f"  - Output directory: {processing_config.output_directory}")
    
    return processing_config

def verify_existing_data(dataset_name="360t_guide_direct_api_v2"):
    """
    Verify that existing extraction data is available for concept generation.
    
    Args:
        dataset_name (str): Name of the dataset directory
        
    Returns:
        tuple: (bool, list) - (data_exists, list_of_found_files)
    """
    
    print(f"🔍 Verifying existing extraction data for {dataset_name}...")
    
    base_dir = f"import/{dataset_name}"
    extraction_dir = f"{base_dir}/kg_extraction"
    
    if not os.path.exists(extraction_dir):
        print(f"❌ Extraction directory {extraction_dir} does not exist")
        return False, []
    
    # Check for extraction JSON files
    json_files = [f for f in os.listdir(extraction_dir) if f.endswith('.json')]
    
    if len(json_files) == 0:
        print(f"❌ No extraction JSON files found in {extraction_dir}")
        return False, []
    
    print(f"✅ Found {len(json_files)} extraction files:")
    for file in json_files:
        file_path = os.path.join(extraction_dir, file)
        file_size = os.path.getsize(file_path)
        print(f"  - {file} ({file_size:,} bytes)")
    
    return True, json_files

def run_concept_generation():
    """
    Run concept generation on existing triple extraction results.
    """
    
    dataset_name = "360t_guide_direct_api_v2"
    
    print("🚀 Starting concept generation recovery...")
    print(f"Dataset: {dataset_name}")
    print(f"Using: Direct Gemini API with full 1M token context")
    print()
    
    # Verify existing extraction data
    data_exists, extraction_files = verify_existing_data(dataset_name)
    if not data_exists:
        print("❌ Cannot proceed: No existing extraction data found")
        print("💡 Please run triple extraction first")
        return False
    
    # Setup direct Gemini API
    print("🔌 Setting up direct Gemini API connection...")
    try:
        llm_generator = setup_gemini_llm_generator_direct()
        print("✅ Direct Gemini API connection established")
    except Exception as e:
        print(f"❌ Failed to setup Gemini API: {str(e)}")
        return False
    
    # Create full context configuration
    processing_config = create_full_context_config(dataset_name)
    
    # Initialize knowledge graph extractor
    print("🏗️ Initializing knowledge graph extractor...")
    kg_extractor = KnowledgeGraphExtractor(
        model=llm_generator,
        config=processing_config
    )
    print("✅ Knowledge graph extractor initialized")
    
    try:
        # Step 1: Convert existing JSON to CSV (if not already done)
        print("\\n1️⃣ Processing existing extraction JSON to CSV...")
        kg_extractor.convert_json_to_csv()
        print("✅ JSON to CSV conversion completed")
        
        # Step 2: Generate concepts from extracted triples
        print("\\n2️⃣ Generating concepts from extracted triples...")
        print("🔥 Using full context (1M tokens) with optimized batch sizes")
        kg_extractor.generate_concept_csv_temp(language='en')
        print("✅ Concept generation completed")
        
        # Step 3: Create final concept CSV files
        print("\\n3️⃣ Creating final concept CSV files...")
        kg_extractor.create_concept_csv()
        print("✅ Concept CSV files created")
        
        print("\\n🎉 Concept generation recovery completed successfully!")
        
        # Show results
        concept_dir = f"import/{dataset_name}/concepts"
        concept_csv_dir = f"import/{dataset_name}/concept_csv"
        
        if os.path.exists(concept_dir):
            concept_files = os.listdir(concept_dir)
            print(f"\\n📊 Generated concept files in {concept_dir}:")
            for file in concept_files:
                if file.endswith('.csv'):
                    file_path = os.path.join(concept_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"  - {file} ({file_size:,} bytes)")
        
        if os.path.exists(concept_csv_dir):
            concept_csv_files = os.listdir(concept_csv_dir)
            print(f"\\n📊 Generated concept CSV files in {concept_csv_dir}:")
            for file in concept_csv_files:
                if file.endswith('.csv'):
                    file_path = os.path.join(concept_csv_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"  - {file} ({file_size:,} bytes)")
        
        return True
        
    except Exception as e:
        print(f"\\n❌ Concept generation failed: {str(e)}")
        print("\\n🔧 Troubleshooting tips:")
        print("1. Check Gemini API key in config.ini")
        print("2. Verify existing extraction JSON files are valid")
        print("3. Check network connectivity")
        print("4. Monitor API rate limits")
        return False

if __name__ == "__main__":
    print("🎯 AutoSchemaKG Concept Generation Recovery")
    print("Leveraging full 1M token context with direct Gemini API")
    print("=" * 60)
    
    success = run_concept_generation()
    
    if success:
        print("\\n✅ Concept generation recovery completed successfully!")
        print("\\n🎯 Next steps:")
        print("1. Re-generate GraphML with concepts included")
        print("2. Import complete knowledge graph into Neo4j")
        print("3. Verify concept nodes and edges in the final graph")
    else:
        print("\\n❌ Concept generation recovery failed")
        print("Please check the error messages above and try again")