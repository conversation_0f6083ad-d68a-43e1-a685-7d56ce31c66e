"""
Resume Extraction Pipeline - Continues knowledge graph extraction from exact stopping point.

This module implements a precise resume mechanism that continues extraction from exactly 
where the previous run stopped, using the existing Gemini-2.5 Flash output file.
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Import existing components
from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from setup_processing_config import create_processing_config
from atlas_rag.kg_construction.triple_extraction import KnowledgeGraphExtractor, CustomDataLoader, DatasetProcessor
from atlas_rag.kg_construction.triple_config import ProcessingConfig
from atlas_rag.llm_generator import LLMGenerator

# Import our resume analyzer
from resume_extraction_analyzer import ResumeExtractionAnalyzer, ResumePoint


class ResumeKnowledgeGraphExtractor(KnowledgeGraphExtractor):
    """Enhanced KG extractor with precise resume capability."""
    
    def __init__(self, model: LLMGenerator, config: ProcessingConfig, resume_point: ResumePoint = None):
        """
        Initialize the resume-capable extractor.
        
        Args:
            model: LLM generator for extraction
            config: Processing configuration
            resume_point: Information about where to resume from
        """
        super().__init__(model, config)
        self.resume_point = resume_point
        self.batches_to_skip = resume_point.batch_resume_index if resume_point else 0
        self.output_file_mode = "append"  # or "new"
        
        if resume_point:
            print(f"🔄 Resume mode enabled - skipping first {self.batches_to_skip} batches")
    
    def create_output_filename(self, resume_mode: bool = False) -> str:
        """Create output filename, handling resume scenarios."""
        if resume_mode and self.resume_point:
            # Check if we want to append to existing file or create new one
            if self.output_file_mode == "append":
                # Use the existing output file for continuation
                existing_file = f"{self.config.output_directory}/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
                if os.path.exists(existing_file):
                    print(f"📝 Continuing extraction in existing file: {os.path.basename(existing_file)}")
                    return existing_file
            
            # Create new file with resume timestamp
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            model_name_safe = self.config.model_path.replace("/", "_")
            
            filename = (f"{model_name_safe}_{self.config.filename_pattern}_resume_"
                       f"{timestamp}_{self.config.current_shard_triple + 1}_in_{self.config.total_shards_triple}.json")
        else:
            # Use original filename logic
            return super().create_output_filename()
        
        extraction_dir = os.path.join(self.config.output_directory, "kg_extraction")
        os.makedirs(extraction_dir, exist_ok=True)
        
        return os.path.join(extraction_dir, filename)
    
    def run_extraction(self):
        """Run extraction with resume capability."""
        import torch
        from datasets import load_dataset
        
        # Setup
        os.makedirs(self.config.output_directory+'/kg_extraction', exist_ok=True)
        dataset = self.load_dataset()
        
        if self.config.debug_mode:
            print("Debug mode: Processing only 20 samples")
        
        # Create data processor and loader
        processor = DatasetProcessor(self.config)
        data_loader = CustomDataLoader(dataset["train"], processor)
        
        # Determine output file and mode
        output_file = self.create_output_filename(resume_mode=True)
        file_mode = "a" if (self.output_file_mode == "append" and os.path.exists(output_file)) else "w"
        
        print(f"🤖 Model: {self.config.model_path}")
        print(f"📄 Output file: {output_file}")
        print(f"📝 File mode: {'Append (resume)' if file_mode == 'a' else 'New file'}")
        
        if self.resume_point:
            print(f"🔄 Resume info: Skipping {self.batches_to_skip} batches")
            print(f"   Total chunks already processed: {self.resume_point.total_chunks_processed}")
            print(f"   Characters already processed: {self.resume_point.total_characters_processed:,}")
        
        batch_counter = 0
        total_batches = len(data_loader)
        batches_processed = 0
        
        with torch.no_grad():
            with open(output_file, file_mode, encoding='utf-8') as output_stream:
                # Write resume marker if appending
                if file_mode == "a":
                    resume_marker = {
                        "_resume_marker": True,
                        "_resume_timestamp": datetime.now().isoformat(),
                        "_resume_batch_index": self.batches_to_skip,
                        "_resume_chunks_skipped": self.resume_point.total_chunks_processed if self.resume_point else 0
                    }
                    output_stream.write(json.dumps(resume_marker, ensure_ascii=False) + "\n")
                    output_stream.flush()
                
                for batch in data_loader:
                    batch_counter += 1
                    
                    # Skip batches if resuming
                    if batch_counter <= self.batches_to_skip:
                        if batch_counter % 10 == 0 or batch_counter == self.batches_to_skip:
                            print(f"⏭️  Skipping batch {batch_counter}/{self.batches_to_skip} (resume mode)")
                        continue
                    
                    # Process this batch (we're past the resume point)
                    batches_processed += 1
                    messages_dict, batch_ids, batch_texts, batch_metadata = batch
                    
                    # Process all three stages
                    stage1_results = self.process_stage(messages_dict['stage_1'], 1)
                    stage2_results = self.process_stage(messages_dict['stage_2'], 2)
                    stage3_results = self.process_stage(messages_dict['stage_3'], 3)
                    
                    # Combine results
                    batch_data = (batch_ids, batch_texts, batch_metadata)
                    stage_outputs = (stage1_results, stage2_results, stage3_results)
                    
                    # Count extracted triplets for this batch
                    total_triplets = 0
                    total_events = 0
                    for i in range(len(batch_ids)):
                        entity_relations = stage_outputs[0][1][i]  # entity_relation_dict
                        event_entities = stage_outputs[1][1][i]    # event_entity_relation_dict  
                        event_relations = stage_outputs[2][1][i]   # event_relation_dict
                        
                        total_triplets += len(entity_relations) + len(event_relations)
                        total_events += len(event_entities)
                    
                    # Enhanced progress logging with resume context
                    progress_percent = (batch_counter / total_batches) * 100 if total_batches > 0 else 0
                    resume_info = f"(resumed, +{batches_processed} new)" if self.batches_to_skip > 0 else ""
                    
                    print(f"📊 Batch {batch_counter}/{total_batches} ({progress_percent:.1f}%) {resume_info}")
                    print(f"   Extracted {total_triplets} triplets, {total_events} events from {len(batch_ids)} chunks")
                    
                    for i in range(len(batch_ids)):
                        result = self.prepare_result_dict(batch_data, stage_outputs, i)
                        
                        if self.config.debug_mode:
                            self.debug_print_result(result)
   
                        output_stream.write(json.dumps(result, ensure_ascii=False) + "\n")
                        output_stream.flush()
                
                # Write completion marker
                completion_marker = {
                    "_completion_marker": True,
                    "_completion_timestamp": datetime.now().isoformat(),
                    "_total_batches_processed": batches_processed,
                    "_resume_mode": self.batches_to_skip > 0
                }
                output_stream.write(json.dumps(completion_marker, ensure_ascii=False) + "\n")
                
        print(f"✅ Resume extraction completed!")
        print(f"   New batches processed: {batches_processed}")
        print(f"   Output file: {output_file}")


def run_resume_extraction_pipeline(existing_output_file: str,
                                  data_directory: str,
                                  dataset_name: str = "pdf_dataset",
                                  batch_size: int = 16,
                                  filename_pattern: str = "from_json",
                                  output_mode: str = "append") -> bool:
    """
    Run the resume extraction pipeline.
    
    Args:
        existing_output_file: Path to existing extraction output
        data_directory: Directory containing input JSON files
        dataset_name: Name for the dataset
        batch_size: Batch size for processing
        filename_pattern: Pattern for file naming
        output_mode: "append" to continue existing file, "new" for new file
        
    Returns:
        True if successful, False otherwise
    """
    
    print("🔄 Resume Extraction Pipeline")
    print("=" * 60)
    print(f"Existing output: {os.path.basename(existing_output_file)}")
    print(f"Data directory: {data_directory}")
    print(f"Dataset: {dataset_name}")
    print(f"Batch size: {batch_size}")
    print(f"Output mode: {output_mode}")
    print()
    
    start_time = time.time()
    
    try:
        # Step 1: Analyze existing output to determine resume point
        print("1️⃣ Analyzing Existing Output...")
        print("-" * 30)
        
        analyzer = ResumeExtractionAnalyzer(existing_output_file, data_directory)
        resume_point = analyzer.calculate_resume_point(batch_size=batch_size)
        analyzer.print_resume_analysis(resume_point)
        
        # Check if resume is needed
        if resume_point.current_file == "None":
            print("ℹ️  All files appear to be completed. No resume needed.")
            return True
        
        # Step 2: Initialize models
        print("2️⃣ Initializing Models...")
        print("-" * 30)
        
        llm_generator = setup_gemini_llm_generator_direct()
        print(f"✅ LLM initialized: {llm_generator.model_name}")
        
        # Step 3: Setup configuration for resume
        print("3️⃣ Setting up Resume Configuration...")
        print("-" * 30)

        # Create processing config matching the original extraction
        processing_config = create_processing_config(dataset_name, filename_pattern)

        # Override data directory, filename pattern, and batch size to match resume analysis
        processing_config.data_directory = data_directory
        processing_config.filename_pattern = ""  # Match all JSON files, not just those with specific pattern
        processing_config.batch_size_triple = batch_size
        
        print(f"✅ Configuration prepared for resume")
        print(f"   Data directory: {processing_config.data_directory}")
        print(f"   Batch size: {processing_config.batch_size_triple}")
        print(f"   Max tokens: {processing_config.max_new_tokens}")
        print(f"   Output directory: {processing_config.output_directory}")
        
        # Step 4: Create resume extractor
        print("4️⃣ Creating Resume Extractor...")
        print("-" * 30)
        
        resume_extractor = ResumeKnowledgeGraphExtractor(
            model=llm_generator,
            config=processing_config,
            resume_point=resume_point
        )
        
        # Set output mode
        resume_extractor.output_file_mode = output_mode
        
        print(f"✅ Resume extractor created")
        print(f"   Will skip {resume_point.batch_resume_index} batches")
        print(f"   Resume from: {resume_point.resume_recommendation}")
        
        # Step 5: Run resume extraction
        print("5️⃣ Running Resume Extraction...")
        print("-" * 30)
        print("🎯 This will:")
        print(f"   - Skip {resume_point.total_chunks_processed} already processed chunks")
        print(f"   - Continue from: {resume_point.current_file}")
        print(f"   - Process remaining files: {len(resume_point.files_remaining)}")
        print()
        
        extraction_start = time.time()
        resume_extractor.run_extraction()
        extraction_time = time.time() - extraction_start
        
        total_time = time.time() - start_time
        
        print("\n🎉 RESUME EXTRACTION COMPLETED!")
        print("=" * 60)
        print(f"Extraction time: {extraction_time/60:.1f} minutes")
        print(f"Total time: {total_time/60:.1f} minutes")
        print(f"Resume successful: Continued from batch {resume_point.batch_resume_index}")
        
        # Step 6: Verify output
        output_file = resume_extractor.create_output_filename(resume_mode=True)
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"Output file size: {file_size:.1f} MB")
            
            # Count total lines in output file
            with open(output_file, 'r', encoding='utf-8') as f:
                total_lines = sum(1 for line in f if line.strip())
            print(f"Total output lines: {total_lines:,}")
        
        print("\n📋 Next Steps:")
        print("1. Your extraction has been resumed and completed")
        print("2. Continue with CSV conversion: convert_json_to_csv()")
        print("3. Then proceed with concept generation and remaining pipeline stages")
        
        return True
        
    except Exception as e:
        total_time = time.time() - start_time
        print(f"\n❌ Resume extraction failed: {str(e)}")
        print("=" * 60)
        print(f"Execution time: {total_time/60:.1f} minutes")
        
        import traceback
        traceback.print_exc()
        
        print("\n🔧 Troubleshooting:")
        print("1. Verify the existing output file exists and is valid")
        print("2. Check that the data directory contains the expected JSON files")
        print("3. Ensure your API keys are configured correctly")
        print("4. Try running the resume analyzer separately to debug the issue")
        
        return False


def main():
    """Main entry point for resume extraction."""
    parser = argparse.ArgumentParser(
        description='Resume Knowledge Graph Extraction Pipeline',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --existing_output /path/to/output.json --data_dir /path/to/data
  %(prog)s --existing_output output.json --data_dir example_data --batch_size 16
  %(prog)s --existing_output output.json --data_dir example_data --output_mode new
        """
    )
    
    parser.add_argument('--existing_output', '-o',
                       default='/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json',
                       help='Path to existing extraction output file')
    
    parser.add_argument('--data_dir', '-d',
                       default='/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data',
                       help='Directory containing input JSON files')
    
    parser.add_argument('--dataset_name', '-n',
                       default='pdf_dataset',
                       help='Dataset name for output directory')
    
    parser.add_argument('--batch_size', '-b',
                       type=int,
                       default=16,
                       help='Batch size for processing (default: 16)')
    
    parser.add_argument('--filename_pattern', '-p',
                       default='from_json',
                       help='Filename pattern for output files')
    
    parser.add_argument('--output_mode', '-m',
                       choices=['append', 'new'],
                       default='append',
                       help='Output mode: append to existing file or create new file')
    
    parser.add_argument('--analyze_only', '-a',
                       action='store_true',
                       help='Only analyze resume point, do not run extraction')
    
    args = parser.parse_args()
    
    print("🔄 Resume Knowledge Graph Extraction Pipeline")
    print("Continues extraction from exactly where it left off")
    print()
    
    # Validate inputs
    if not os.path.exists(args.existing_output):
        print(f"❌ Existing output file not found: {args.existing_output}")
        sys.exit(1)
    
    if not os.path.exists(args.data_dir):
        print(f"❌ Data directory not found: {args.data_dir}")
        sys.exit(1)
    
    if args.analyze_only:
        # Just run the analysis
        print("🔍 Running resume point analysis only...")
        analyzer = ResumeExtractionAnalyzer(args.existing_output, args.data_dir)
        resume_point = analyzer.calculate_resume_point(batch_size=args.batch_size)
        analyzer.print_resume_analysis(resume_point)
        return
    
    try:
        # Run the resume pipeline
        success = run_resume_extraction_pipeline(
            existing_output_file=args.existing_output,
            data_directory=args.data_dir,
            dataset_name=args.dataset_name,
            batch_size=args.batch_size,
            filename_pattern=args.filename_pattern,
            output_mode=args.output_mode
        )
        
        if success:
            print("\n🎉 Resume extraction completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Resume extraction failed.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ Resume extraction interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()