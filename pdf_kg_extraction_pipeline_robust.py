"""
Robust PDF Knowledge Graph Extraction Pipeline

Enhanced version of the PDF KG extraction pipeline with comprehensive resumability,
error handling, and progress tracking capabilities.

Features:
- Resume functionality from any failure point
- Comprehensive error handling with retry logic
- Real-time progress tracking with individual file monitoring  
- State management with checkpoint/restore
- Command-line arguments for flexible operation modes
"""

import os
import sys
import argparse
import time
import json
import glob
from datetime import datetime
from typing import Optional, Callable, Any
from configparser import ConfigParser

import requests  # lightweight dependency for Ollama HTTP calls

from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from setup_embedding_model import setup_qwen_embedding_model
from setup_processing_config import create_processing_config, create_output_directories

from robust_kg_extractor import RobustKGExtractor
from error_handler import ErrorConfig
from pipeline_state_manager import PipelineStateManager
from progress_tracker import ProgressTracker


class _OllamaGenerator:
    """
    Minimal local Ollama adapter exposing the same attributes/methods used by RobustKGExtractor:
      - model_name: str
      - generate_response(prompt:str, system:Optional[str], temperature:float, top_p:float,
                 max_tokens:int|None, stop:list[str]|None, json_mode:bool) -> dict with {'text', 'usage', 'raw'}
      - stream_response(prompt, system, on_delta: Callable[[str], None], ...) -> dict with {'text','usage','raw'}

    Note: Some call sites (e.g., RobustKGExtractor) expect method names generate_response/stream_response.
    """
    def __init__(self, model: str | None = None, base_url: str | None = None, timeout_s: int = 120):
        self.base_url = (base_url or os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")).rstrip("/")
        self.model_name = model or os.getenv("OLLAMA_MODEL", "qwen3:30b-a3b-instruct-2507-q4_K_M")
        self.timeout_s = timeout_s

    def _payload(
        self,
        prompt: str,
        system: str | None,
        temperature: float | None,
        top_p: float | None,
        max_tokens: int | None,
        stop: list[str] | None,
        json_mode: bool,
        stream: bool,
    ) -> dict[str, Any]:
        options: dict[str, Any] = {}
        if temperature is not None:
            options["temperature"] = float(temperature)
        if top_p is not None:
            options["top_p"] = float(top_p)
        if max_tokens is not None:
            options["num_predict"] = int(max_tokens)
        if stop:
            options["stop"] = stop
        payload: dict[str, Any] = {
            "model": self.model_name,
            "prompt": prompt,
            "system": system or "",
            "stream": stream,
            "options": options,
        }
        if json_mode:
            # Hint Ollama to return valid JSON
            payload["format"] = "json"
        return payload

    # Backward-compat shim: if upstream still calls generate(), delegate to generate_response()
    def generate(
        self,
        prompt: str,
        system: str | None = None,
        temperature: float = 0.2,
        top_p: float = 0.95,
        max_tokens: int | None = None,
        stop: list[str] | None = None,
        json_mode: bool = True,
        **kwargs,
    ) -> dict:
        return self.generate_response(
            prompt=prompt,
            system=system,
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens,
            stop=stop,
            json_mode=json_mode,
            **kwargs,
        )

    def generate_response(
        self,
        batch_messages,
        temperature: float = 0.2,
        top_p: float = 0.95,
        max_new_tokens: int | None = None,
        stop: list[str] | None = None,
        response_format: dict | None = None,
        return_text_only: bool = True,
        stream: bool | None = None,
        **kwargs,
    ):
        """
        Match LLMGenerator interface: accepts batch_messages (list of message lists)
        and returns text or (text, usage) based on return_text_only flag.
        """
        # Handle both single message list and batch of message lists
        is_batch = isinstance(batch_messages[0], list)
        if not is_batch:
            batch_messages = [batch_messages]
        
        results = []
        json_mode = response_format and response_format.get("type") == "json_object"
        
        for messages in batch_messages:
            # Convert OpenAI message format to single prompt
            prompt = ""
            system = ""
            
            for msg in messages:
                if msg["role"] == "system":
                    system = msg["content"]
                elif msg["role"] == "user":
                    prompt += msg["content"] + "\n"
                elif msg["role"] == "assistant":
                    prompt += msg["content"] + "\n"
            
            prompt = prompt.strip()
            
            url = f"{self.base_url}/api/generate"
            payload = self._payload(prompt, system, temperature, top_p, max_new_tokens, stop, json_mode, stream=False)
            
            try:
                r = requests.post(url, json=payload, timeout=self.timeout_s)
                if r.status_code == 404:
                    raise RuntimeError(f"Ollama model not found: {self.model_name}")
                r.raise_for_status()
            except requests.Timeout:
                raise TimeoutError("Ollama request timed out")
            except requests.RequestException as e:
                raise RuntimeError(f"Ollama transport error: {e}")
            
            data = r.json()
            text = data.get("response", "")
            usage = {
                "prompt_tokens": int(data.get("prompt_eval_count", 0) or 0),
                "completion_tokens": int(data.get("eval_count", 0) or 0),
            }
            
            if return_text_only:
                results.append(text)
            else:
                results.append((text, usage))
        
        # Return single result if input wasn't a batch
        return results[0] if not is_batch else results

    # Backward-compat shim: if upstream still calls generate_stream(), delegate to stream_response()
    def generate_stream(
        self,
        prompt: str,
        system: str | None,
        on_delta: Callable[[str], None],
        temperature: float = 0.2,
        top_p: float = 0.95,
        max_tokens: int | None = None,
        stop: list[str] | None = None,
        json_mode: bool = True,
        **kwargs,
    ) -> dict:
        return self.stream_response(
            prompt=prompt,
            system=system,
            on_delta=on_delta,
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens,
            stop=stop,
            json_mode=json_mode,
            **kwargs,
        )

    def stream_response(
        self,
        prompt: str,
        system: str | None,
        on_delta: Callable[[str], None],
        temperature: float = 0.2,
        top_p: float = 0.95,
        max_tokens: int | None = None,
        stop: list[str] | None = None,
        json_mode: bool = True,
        return_text_only: bool | None = None,
        stream: bool | None = None,
    ) -> dict:
        """
        Added optional kwargs return_text_only and stream to match upstream signatures. Ignored here.
        """
        url = f"{self.base_url}/api/generate"
        payload = self._payload(prompt, system, temperature, top_p, max_tokens, stop, json_mode, stream=True)
        text_parts: list[str] = []
        prompt_tokens = 0
        completion_tokens = 0
        try:
            with requests.post(url, json=payload, timeout=self.timeout_s, stream=True) as r:
                if r.status_code == 404:
                    raise RuntimeError(f"Ollama model not found: {self.model_name}")
                r.raise_for_status()
                for line in r.iter_lines():
                    if not line:
                        continue
                    try:
                        chunk = json.loads(line.decode("utf-8"))
                    except Exception:
                        continue
                    if "response" in chunk:
                        delta = chunk["response"]
                        text_parts.append(delta)
                        try:
                            on_delta(delta)
                        except Exception:
                            # Do not break streaming if the callback fails
                            pass
                    if chunk.get("done"):
                        prompt_tokens = int(chunk.get("prompt_eval_count", 0) or 0)
                        completion_tokens = int(chunk.get("eval_count", 0) or 0)
                        break
        except requests.Timeout:
            raise TimeoutError("Ollama stream timed out")
        except requests.RequestException as e:
            raise RuntimeError(f"Ollama stream transport error: {e}")
        text = "".join(text_parts)
        usage = {"prompt_tokens": prompt_tokens, "completion_tokens": completion_tokens}
        return {"text": text, "usage": usage, "raw": None}


def _setup_llm_generator():
    """
    Lightweight backend selector:
      - Reads from config.ini first, then environment variables
      - Default: Gemini via existing setup_gemini_llm_generator_direct()
      - When LLM_BACKEND=ollama: use local Ollama HTTP adapter
    """
    # Read configuration from config.ini first, fallback to environment variables
    config = ConfigParser()
    config.read('config.ini')
    
    # Get backend preference
    backend = "gemini"  # default
    if config.has_section('settings') and config.has_option('settings', 'LLM_BACKEND'):
        backend = config.get('settings', 'LLM_BACKEND').lower()
    else:
        backend = os.getenv("LLM_BACKEND", "gemini").lower()
    
    if backend == "ollama":
        # Get Ollama configuration from config.ini first, then environment
        if config.has_section('settings'):
            base = config.get('settings', 'OLLAMA_BASE_URL', fallback=None)
            model = config.get('settings', 'OLLAMA_MODEL', fallback=None)
        else:
            base = None
            model = None
            
        # Fallback to environment variables if not in config.ini
        if not base:
            base = os.getenv("OLLAMA_HOST", os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"))
        if not model:
            model = os.getenv("OLLAMA_MODEL", "qwen3:30b-a3b-instruct-2507-q4_K_M")
            
        print(f"🧪 Using Ollama backend locally: {model} @ {base}")
        print(f"📝 Configuration source: {'config.ini' if config.has_section('settings') else 'environment variables'}")
        return _OllamaGenerator(model=model, base_url=base)
    
    # default path remains Gemini to keep risk minimal
    print(f"🚀 Using Gemini backend")
    return setup_gemini_llm_generator_direct()


def create_robust_processing_config(dataset_name: str = "pdf_dataset", 
                                   filename_pattern: str = "",
                                   tier1_mode: bool = False,
                                   performance_mode: str = "medium") -> 'ProcessingConfig':
    """
    Create processing configuration optimized for robust pipeline operation.
    
    Args:
        dataset_name: Name for the output dataset directory
        filename_pattern: Pattern to match input files
        tier1_mode: Use Tier 1 optimized settings for token limits
        performance_mode: Performance optimization level (low/medium/high/max)
        
    Returns:
        ProcessingConfig: Configured processing parameters
    """
    from setup_processing_config import create_processing_config_tier1, create_processing_config
    
    # Performance mode settings
    performance_settings = {
        'low': {'batch_size_concept': 16, 'max_workers': 2},
        'medium': {'batch_size_concept': 64, 'max_workers': 8},
        'high': {'batch_size_concept': 128, 'max_workers': 16},
        'max': {'batch_size_concept': 256, 'max_workers': 32}
    }
    
    if tier1_mode:
        print("🔧 Using Tier 1 optimized configuration for token limits")
        config = create_processing_config_tier1(dataset_name, filename_pattern)
        # Apply tier1 conservative settings regardless of performance mode
        config.batch_size_concept = 5  # Even smaller for reliability
        config.max_workers = 1  # Single worker to avoid rate limits
        print(f"   Tier1 override: batch_size_concept=5, max_workers=1")
    else:
        print(f"🔧 Using {performance_mode} performance configuration")
        config = create_processing_config(dataset_name, filename_pattern)
        
        # Apply performance mode settings
        settings = performance_settings[performance_mode]
        config.batch_size_concept = settings['batch_size_concept']
        config.max_workers = settings['max_workers']
        
        # Keep conservative triple extraction settings for stability
        config.batch_size_triple = 8   # Smaller batches for stability
        
        print(f"   Performance settings: batch_size_concept={config.batch_size_concept}, max_workers={config.max_workers}")
        
        # Show performance hints
        if performance_mode == 'max':
            print("   💡 MAX mode: Ensure your system has sufficient CPU/memory")
            print("   💡 Recommended for: Local Ollama models with powerful hardware")
        elif performance_mode == 'high':
            print("   💡 HIGH mode: Good balance for local models")
        elif performance_mode == 'low':
            print("   💡 LOW mode: Conservative for API rate limits")
    
    return config


def create_error_config(enable_notifications: bool = False,
                       notification_email: str = "",
                       max_retries: int = 3) -> ErrorConfig:
    """
    Create error handling configuration.
    
    Args:
        enable_notifications: Whether to enable email notifications
        notification_email: Email address for notifications
        max_retries: Maximum number of retry attempts
        
    Returns:
        ErrorConfig: Configured error handling parameters
    """
    return ErrorConfig(
        max_retries=max_retries,
        base_delay=2.0,          # Start with 2-second delays
        max_delay=300.0,         # Max 5-minute delays
        backoff_multiplier=2.0,  # Exponential backoff
        jitter=True,             # Add randomness to prevent thundering herd
        circuit_breaker_threshold=5,  # Open circuit after 5 failures
        circuit_breaker_timeout=300.0,  # Try again after 5 minutes
        enable_notifications=enable_notifications,
        notification_email=notification_email
    )


def run_post_processing_pipeline(dataset_name: str = "pdf_dataset",
                               filename_pattern: str = "",
                               tier1_mode: bool = False,
                               performance_mode: str = "medium",
                               enable_notifications: bool = False,
                               notification_email: str = "",
                               max_retries: int = 3) -> bool:
    """
    Run the post-processing pipeline starting from completed triple extraction.
    
    This function continues the pipeline after triple extraction is complete, running:
    1. CSV conversion (convert_json_to_csv)
    2. Concept generation (generate_concept_csv)
    3. Concept CSV creation (create_concept_csv)
    4. Numeric ID addition (add_numeric_id)
    5. GraphML conversion (convert_to_graphml)
    6. Embedding generation (compute_kg_embedding)
    7. FAISS index creation (create_faiss_index)
    
    Args:
        dataset_name: Name for the dataset
        filename_pattern: Pattern to match input files
        tier1_mode: Use Tier 1 optimized settings for token limits
        enable_notifications: Enable email notifications for errors
        notification_email: Email address for notifications
        max_retries: Maximum number of retry attempts
        
    Returns:
        True if post-processing completed successfully, False otherwise
    """
    
    print("🔄 Starting Post-Processing Pipeline")
    print("=== Continue from Completed Triple Extraction ===")
    print(f"Dataset: {dataset_name}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Mode: {'Tier 1 Optimized' if tier1_mode else 'Standard'}")
    print()
    
    start_time = time.time()
    
    try:
        # Step 1: Initialize models
        print("1️⃣ Initializing Models...")
        print("-" * 30)
        
        llm_generator = _setup_llm_generator()
        sentence_encoder = setup_qwen_embedding_model()
        
        print("✅ Models initialized successfully")
        print(f"   LLM: {llm_generator.model_name}")
        print(f"   Embedding: {sentence_encoder.model_name}")
        print()
        
        # Step 2: Setup configuration
        print("2️⃣ Setting up Configuration...")
        print("-" * 30)
        
        create_output_directories(dataset_name)
        processing_config = create_robust_processing_config(
            dataset_name, filename_pattern, tier1_mode, performance_mode
        )
        
        error_config = create_error_config(
            enable_notifications=enable_notifications,
            notification_email=notification_email,
            max_retries=max_retries
        )
        
        print("✅ Configuration setup complete")
        print(f"   Output directory: {processing_config.output_directory}")
        print()
        
        # Step 3: Initialize robust extractor
        print("3️⃣ Initializing Robust Extractor...")
        print("-" * 30)
        
        robust_extractor = RobustKGExtractor(
            model=llm_generator,
            config=processing_config,
            resume_enabled=True,
            error_config=error_config,
            progress_log_level="INFO"
        )
        
        print("✅ Robust extractor initialized")
        print()
        
        # Step 4: Run post-processing stages
        print("4️⃣ Running Post-Processing Stages...")
        print("-" * 30)
        print("🎯 Starting from completed triple extraction")
        print("   1. CSV conversion")
        print("   2. Concept generation")
        print("   3. Concept CSV creation")
        print("   4. Numeric ID addition")
        print("   5. GraphML conversion")
        print("   6. Embedding generation")
        print("   7. FAISS index creation")
        print()
        
        post_processing_start = time.time()
        
        # Run individual post-processing stages
        stages = [
            ("CSV Conversion", lambda: robust_extractor.convert_json_to_csv(retry_failed=False)),
            ("Concept Generation", lambda: robust_extractor.generate_concept_csv(retry_failed=False)),
            ("Concept CSV Creation", lambda: robust_extractor.create_concept_csv(retry_failed=False)),
            ("Numeric ID Addition", lambda: robust_extractor.add_numeric_id(retry_failed=False)),
            ("GraphML Conversion", lambda: robust_extractor.convert_to_graphml(retry_failed=False)),
            ("Embedding Generation", lambda: robust_extractor.compute_kg_embedding(sentence_encoder, retry_failed=False)),
            ("FAISS Index Creation", lambda: robust_extractor.create_faiss_index(retry_failed=False))
        ]
        
        for stage_name, stage_func in stages:
            print(f"▶️ Starting: {stage_name}")
            if not stage_func():
                print(f"❌ Failed: {stage_name}")
                return False
            print(f"✅ Completed: {stage_name}")
            print()
        
        post_processing_time = time.time() - post_processing_start
        total_time = time.time() - start_time
        
        print("🎉 POST-PROCESSING PIPELINE COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"Post-processing time: {post_processing_time/60:.1f} minutes")
        print(f"Total execution time: {total_time/60:.1f} minutes")
        print(f"Dataset location: {processing_config.output_directory}")
        
        # Display processing summary
        summary = robust_extractor.get_pipeline_summary()
        print(f"\n📊 Processing Summary:")
        print(f"   Files processed: {summary['input_files_count']}")
        print(f"   Success rate: {summary['progress_tracker']['overall_progress']:.1f}%")
        print(f"   Total errors: {summary['error_handler']['total_errors']}")
        print(f"   Resolved errors: {summary['error_handler']['resolved_errors']}")
        
        print("\n📋 Next Steps:")
        print("1. Review the generated files in the dataset directory")
        print("2. Stop your Neo4j autoschemakg database")
        print("3. Run the Neo4j import command (check neo4j_import_commands.txt)")
        print("4. Start your Neo4j autoschemakg database")
        print("5. Test the knowledge graph with RAG queries")
        
        return True
        
    except Exception as e:
        total_time = time.time() - start_time
        print(f"\n💥 POST-PROCESSING PIPELINE FAILED: {str(e)}")
        print("=" * 60)
        print(f"Execution time: {total_time/60:.1f} minutes")
        print()
        print("🔧 Troubleshooting tips:")
        print("1. Check that triple extraction completed successfully")
        print("2. Verify output files are in example_data/ directory")
        print("3. Check your API keys in config.ini")
        print("4. Ensure internet connection for API calls")
        
        return False


def run_csv_conversion(dataset_name: str = "pdf_dataset",
                      filename_pattern: str = "",
                      tier1_mode: bool = False,
                      performance_mode: str = "medium") -> bool:
    """
    Run only the CSV conversion step from JSON to CSV format.
    
    Args:
        dataset_name: Name for the dataset
        filename_pattern: Pattern to match input files
        tier1_mode: Use Tier 1 optimized settings
        
    Returns:
        True if CSV conversion completed successfully, False otherwise
    """
    
    print("📄 Running CSV Conversion Only")
    print("=== Convert JSON to CSV Format ===")
    print(f"Dataset: {dataset_name}")
    print()
    
    try:
        # Initialize models and configuration
        llm_generator = _setup_llm_generator()
        processing_config = create_robust_processing_config(dataset_name, filename_pattern, tier1_mode, performance_mode)
        error_config = create_error_config()
        
        robust_extractor = RobustKGExtractor(
            model=llm_generator,
            config=processing_config,
            resume_enabled=True,
            error_config=error_config,
            progress_log_level="INFO"
        )
        
        print("▶️ Starting CSV conversion...")
        success = robust_extractor.convert_json_to_csv(retry_failed=False)
        
        if success:
            print("✅ CSV conversion completed successfully!")
            print(f"   Files saved to: {processing_config.output_directory}")
            return True
        else:
            print("❌ CSV conversion failed")
            return False
            
    except Exception as e:
        print(f"💥 CSV conversion failed: {str(e)}")
        return False


def run_concept_generation(dataset_name: str = "pdf_dataset",
                          filename_pattern: str = "",
                          tier1_mode: bool = False,
                          performance_mode: str = "medium") -> bool:
    """
    Run concept generation and CSV creation steps.
    
    Args:
        dataset_name: Name for the dataset
        filename_pattern: Pattern to match input files
        tier1_mode: Use Tier 1 optimized settings
        
    Returns:
        True if concept generation completed successfully, False otherwise
    """
    
    print("🧠 Running Concept Generation")
    print("=== Generate Concepts from Triples ===")
    print(f"Dataset: {dataset_name}")
    print()
    
    try:
        # Initialize models and configuration
        llm_generator = _setup_llm_generator()
        processing_config = create_robust_processing_config(dataset_name, filename_pattern, tier1_mode, performance_mode)
        error_config = create_error_config()
        
        robust_extractor = RobustKGExtractor(
            model=llm_generator,
            config=processing_config,
            resume_enabled=True,
            error_config=error_config,
            progress_log_level="INFO"
        )
        
        # Run concept generation stages
        stages = [
            ("Concept Generation", lambda: robust_extractor.generate_concept_csv(retry_failed=False)),
            ("Concept CSV Creation", lambda: robust_extractor.create_concept_csv(retry_failed=False))
        ]
        
        for stage_name, stage_func in stages:
            print(f"▶️ Starting: {stage_name}")
            if not stage_func():
                print(f"❌ Failed: {stage_name}")
                return False
            print(f"✅ Completed: {stage_name}")
        
        print("\n🎉 Concept generation completed successfully!")
        print(f"   Files saved to: {processing_config.output_directory}")
        return True
        
    except Exception as e:
        print(f"💥 Concept generation failed: {str(e)}")
        return False


def run_embeddings_and_indexes(dataset_name: str = "pdf_dataset",
                               filename_pattern: str = "",
                               tier1_mode: bool = False,
                               performance_mode: str = "medium") -> bool:
    """
    Run embeddings generation and FAISS index creation.
    
    Args:
        dataset_name: Name for the dataset
        filename_pattern: Pattern to match input files
        tier1_mode: Use Tier 1 optimized settings
        
    Returns:
        True if embeddings and indexes completed successfully, False otherwise
    """
    
    print("🔍 Running Embeddings & Index Creation")
    print("=== Generate Embeddings and FAISS Indexes ===")
    print(f"Dataset: {dataset_name}")
    print()
    
    try:
        # Initialize models and configuration
        llm_generator = _setup_llm_generator()
        sentence_encoder = setup_qwen_embedding_model()
        processing_config = create_robust_processing_config(dataset_name, filename_pattern, tier1_mode, performance_mode)
        error_config = create_error_config()
        
        robust_extractor = RobustKGExtractor(
            model=llm_generator,
            config=processing_config,
            resume_enabled=True,
            error_config=error_config,
            progress_log_level="INFO"
        )
        
        # Run embedding and indexing stages
        stages = [
            ("Numeric ID Addition", lambda: robust_extractor.add_numeric_id(retry_failed=False)),
            ("GraphML Conversion", lambda: robust_extractor.convert_to_graphml(retry_failed=False)),
            ("Embedding Generation", lambda: robust_extractor.compute_kg_embedding(sentence_encoder, retry_failed=False)),
            ("FAISS Index Creation", lambda: robust_extractor.create_faiss_index(retry_failed=False))
        ]
        
        for stage_name, stage_func in stages:
            print(f"▶️ Starting: {stage_name}")
            if not stage_func():
                print(f"❌ Failed: {stage_name}")
                return False
            print(f"✅ Completed: {stage_name}")
        
        print("\n🎉 Embeddings and indexes completed successfully!")
        print(f"   Files saved to: {processing_config.output_directory}")
        return True
        
    except Exception as e:
        print(f"💥 Embeddings and indexes failed: {str(e)}")
        return False


def run_robust_knowledge_extraction_pipeline(dataset_name: str = "pdf_dataset",
                                            filename_pattern: str = "",
                                            force_restart: bool = False,
                                            retry_failed: bool = False,
                                            tier1_mode: bool = False,
                                            performance_mode: str = "medium",
                                            enable_notifications: bool = False,
                                            notification_email: str = "",
                                            max_retries: int = 3) -> bool:
    """
    Run the robust knowledge graph extraction pipeline.
    
    Args:
        dataset_name: Name for the dataset
        filename_pattern: Pattern to match input files
        force_restart: Ignore existing state and start fresh
        retry_failed: Retry previously failed operations
        tier1_mode: Use Tier 1 optimized settings for token limits
        enable_notifications: Enable email notifications for errors
        notification_email: Email address for notifications
        max_retries: Maximum number of retry attempts
        
    Returns:
        True if pipeline completed successfully, False otherwise
    """
    
    print("🚀 Starting Robust AutoSchemaKG Pipeline")
    print("=" * 60)
    print(f"Dataset: {dataset_name}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Mode: {'Tier 1 Optimized' if tier1_mode else 'Standard'}")
    print(f"Force Restart: {force_restart}")
    print(f"Retry Failed: {retry_failed}")
    print()
    
    start_time = time.time()
    
    try:
        # Step 1: Initialize models
        print("1️⃣ Initializing Models...")
        print("-" * 30)
        
        llm_generator = _setup_llm_generator()
        sentence_encoder = setup_qwen_embedding_model()
        
        print("✅ Models initialized successfully")
        print(f"   LLM: {llm_generator.model_name}")
        print(f"   Embedding: {sentence_encoder.model_name}")
        print()
        
        # Step 2: Setup configuration
        print("2️⃣ Setting up Enhanced Configuration...")
        print("-" * 30)
        
        create_output_directories(dataset_name)
        processing_config = create_robust_processing_config(
            dataset_name, filename_pattern, tier1_mode, performance_mode
        )
        
        error_config = create_error_config(
            enable_notifications=enable_notifications,
            notification_email=notification_email,
            max_retries=max_retries
        )
        
        print("✅ Configuration setup complete")
        print(f"   Batch size (triples): {processing_config.batch_size_triple}")
        print(f"   Batch size (concepts): {processing_config.batch_size_concept}")
        print(f"   Max retries: {error_config.max_retries}")
        print(f"   Output directory: {processing_config.output_directory}")
        print()
        
        # Step 3: Initialize robust extractor
        print("3️⃣ Initializing Robust Knowledge Graph Extractor...")
        print("-" * 30)
        
        robust_extractor = RobustKGExtractor(
            model=llm_generator,
            config=processing_config,
            resume_enabled=True,
            error_config=error_config,
            progress_log_level="INFO"
        )
        
        print("✅ Robust extractor initialized successfully")
        print("   Features enabled:")
        print("   - ✅ Resume functionality")
        print("   - ✅ Comprehensive error handling")
        print("   - ✅ Real-time progress tracking")
        print("   - ✅ State management with checkpoints")
        print()
        
        # Step 4: Run complete robust pipeline
        print("4️⃣ Running Complete Robust Pipeline...")
        print("-" * 30)
        print("🎯 This enhanced pipeline will:")
        print("   - Resume from any previous failure point")
        print("   - Provide detailed progress tracking")
        print("   - Automatically retry failed operations")
        print("   - Save checkpoints every 5 minutes")
        print("   - Handle API rate limits gracefully")
        print()
        
        pipeline_start = time.time()
        
        success = robust_extractor.run_complete_pipeline(
            sentence_encoder=sentence_encoder,
            force_restart=force_restart,
            retry_failed=retry_failed
        )
        
        pipeline_time = time.time() - pipeline_start
        total_time = time.time() - start_time
        
        if success:
            print("\n🎉 ROBUST PIPELINE COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"Pipeline processing time: {pipeline_time/60:.1f} minutes")
            print(f"Total execution time: {total_time/60:.1f} minutes")
            print(f"Dataset location: {processing_config.output_directory}")
            
            # Display processing summary
            summary = robust_extractor.get_pipeline_summary()
            print(f"\n📊 Processing Summary:")
            print(f"   Files processed: {summary['input_files_count']}")
            print(f"   Success rate: {summary['progress_tracker']['overall_progress']:.1f}%")
            print(f"   Total errors: {summary['error_handler']['total_errors']}")
            print(f"   Resolved errors: {summary['error_handler']['resolved_errors']}")
            
            print("\n📋 Next Steps:")
            print("1. Review the generated files in the dataset directory")
            print("2. Stop your Neo4j autoschemakg database")
            print("3. Run the Neo4j import command (check neo4j_import_commands.txt)")
            print("4. Start your Neo4j autoschemakg database")
            print("5. Test the knowledge graph with RAG queries")
            
            return True
            
        else:
            print("\n❌ ROBUST PIPELINE FAILED")
            print("=" * 60)
            print(f"Total execution time: {total_time/60:.1f} minutes")
            
            # Display error summary
            summary = robust_extractor.get_pipeline_summary()
            print(f"\n🚨 Error Summary:")
            print(f"   Total errors: {summary['error_handler']['total_errors']}")
            print(f"   Unresolved errors: {summary['error_handler']['unresolved_errors']}")
            print(f"   Last stage: {summary['current_stage']}")
            
            print("\n🔧 Recovery Options:")
            print("1. Run with --retry-failed to retry failed operations")
            print("2. Check the detailed error logs for specific issues")
            print("3. Run with --force-restart to start completely fresh")
            print("4. Verify your API keys and network connectivity")
            
            return False

    except Exception as e:
        total_time = time.time() - start_time
        print("\n💥 ROBUST PIPELINE CRASHED")
        print("=" * 60)
        print(f"Error: {str(e)}")
        print(f"Total execution time before crash: {total_time/60:.1f} minutes")
        return False


def run_concept_csv_creation(dataset_name: str = "pdf_dataset",
                             filename_pattern: str = "",
                             tier1_mode: bool = False,
                             performance_mode: str = "medium") -> bool:
    """
    Run concept CSV creation step independently.
    
    Args:
        dataset_name: Name for the dataset
        filename_pattern: Pattern to match input files
        tier1_mode: Use Tier 1 optimized settings
        performance_mode: Performance optimization level
        
    Returns:
        True if concept CSV creation completed successfully, False otherwise
    """
    
    print("📋 Running Concept CSV Creation")
    print("=== Create Concept CSV Files ===")
    print(f"Dataset: {dataset_name}")
    print()
    
    try:
        # Initialize models and configuration
        llm_generator = _setup_llm_generator()
        processing_config = create_robust_processing_config(dataset_name, filename_pattern, tier1_mode, performance_mode)
        error_config = create_error_config()
        
        robust_extractor = RobustKGExtractor(
            model=llm_generator,
            config=processing_config,
            resume_enabled=True,
            error_config=error_config,
            progress_log_level="INFO"
        )
        
        # Run only the concept CSV creation stage
        print("▶️ Starting: Concept CSV Creation")
        success = robust_extractor.create_concept_csv(retry_failed=False)
        
        if success:
            print("✅ Concept CSV creation completed successfully!")
            print(f"   Files saved to: {processing_config.output_directory}")
            return True
        else:
            print("❌ Concept CSV creation failed")
            return False

    except Exception as e:
        print(f"💥 Concept CSV creation failed: {str(e)}")
        return False

def generate_import_summary(dataset_name: str):
    """
    Generate a summary of files created and Neo4j import commands.
    """
    import_dir = f"import/{dataset_name}"
    
    # Count files
    csv_files = []
    index_files = []
    
    for root, dirs, files in os.walk(import_dir):
        for file in files:
            if file.endswith('.csv'):
                csv_files.append(os.path.join(root, file))
            elif file.endswith('.index'):
                index_files.append(os.path.join(root, file))
    
    print(f"\n📊 Import Summary for {dataset_name}:")
    print(f"  - CSV files created: {len(csv_files)}")
    print(f"  - FAISS indexes created: {len(index_files)}")
    
    # Generate Neo4j import command
    neo4j_command = generate_neo4j_import_command(dataset_name, csv_files)
    
    # Save import commands to file
    with open("neo4j_import_commands.txt", "w") as f:
        f.write("Neo4j Import Commands for AutoSchemaKG (Robust Pipeline)\n")
        f.write("=" * 60 + "\n\n")
        f.write("IMPORTANT: Stop your autoschemakg database before running this command!\n\n")
        f.write("Command to run in your Neo4j installation directory:\n")
        f.write("-" * 50 + "\n")
        f.write(neo4j_command)
        f.write("\n\nAfter import completes, start your autoschemakg database again.\n")
        f.write("\nGenerated by Robust PDF KG Extraction Pipeline\n")
        f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print("✅ Neo4j import commands saved to neo4j_import_commands.txt")


def generate_neo4j_import_command(dataset_name: str, csv_files: list) -> str:
    """Generate the Neo4j admin import command."""
    # Categorize CSV files
    node_files = []
    relationship_files = []
    
    for file in csv_files:
        if 'nodes' in file or 'node' in file:
            node_files.append(file)
        elif 'edges' in file or 'edge' in file:
            relationship_files.append(file)
    
    # Build command
    command_parts = [
        "neo4j-admin database import full autoschemakg"
    ]
    
    # Add node files
    for node_file in node_files:
        command_parts.append(f"    --nodes ./{node_file}")
    
    # Add relationship files  
    for rel_file in relationship_files:
        command_parts.append(f"    --relationships ./{rel_file}")
    
    # Add options
    command_parts.extend([
        "    --overwrite-destination",
        "    --multiline-fields=true", 
        "    --id-type=string",
        "    --verbose",
        "    --skip-bad-relationships=true"
    ])
    
    return " \\\n".join(command_parts)


def detect_pipeline_stages(dataset_name: str = "pdf_dataset") -> dict:
    """
    Detect which pipeline stages have been completed.
    
    Args:
        dataset_name: Name of the dataset to check
        
    Returns:
        Dictionary with stage completion status
    """
    
    stages = {
        'triple_extraction': False,
        'csv_conversion': False,
        'concept_generation': False,
        'concept_csv': False,
        'numeric_ids': False,
        'graphml': False,
        'embeddings': False,
        'faiss_indexes': False
    }
    
    output_dir = f"import/{dataset_name}"
    
    # Check for triple extraction JSON files
    json_files = glob.glob("example_data/*.json")
    if json_files:
        # Check if any have extraction results
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'Triples' in data or 'Events' in data:
                        stages['triple_extraction'] = True
                        break
            except:
                continue
    
    # Check for CSV files
    if os.path.exists(f"{output_dir}/triples_csv"):
        csv_files = glob.glob(f"{output_dir}/triples_csv/*.csv")
        if csv_files:
            stages['csv_conversion'] = True
    
    # Check for concept files
    if os.path.exists(f"{output_dir}/concept_csv"):
        concept_files = glob.glob(f"{output_dir}/concept_csv/*.csv")
        if concept_files:
            stages['concept_generation'] = True
            stages['concept_csv'] = True
    
    # Check for numeric ID files
    if os.path.exists(f"{output_dir}/kg_csv_id"):
        id_files = glob.glob(f"{output_dir}/kg_csv_id/*.csv")
        if id_files:
            stages['numeric_ids'] = True
    
    # Check for GraphML files
    if os.path.exists(f"{output_dir}/kg_graphml"):
        graphml_files = glob.glob(f"{output_dir}/kg_graphml/*.graphml")
        if graphml_files:
            stages['graphml'] = True
    
    # Check for embedding files
    if os.path.exists(f"{output_dir}/kg_embedding"):
        embedding_files = glob.glob(f"{output_dir}/kg_embedding/*.npy")
        if embedding_files:
            stages['embeddings'] = True
    
    # Check for FAISS index files
    if os.path.exists(f"{output_dir}/kg_faiss_index"):
        index_files = glob.glob(f"{output_dir}/kg_faiss_index/*.index")
        if index_files:
            stages['faiss_indexes'] = True
    
    return stages


def show_pipeline_status(dataset_name: str = "pdf_dataset"):
    """Show current pipeline status and recommend next steps."""
    
    print(f"📊 Pipeline Status for Dataset: {dataset_name}")
    print("=" * 50)
    
    stages = detect_pipeline_stages(dataset_name)
    
    stage_names = {
        'triple_extraction': '1️⃣ Triple Extraction',
        'csv_conversion': '2️⃣ CSV Conversion', 
        'concept_generation': '3️⃣ Concept Generation',
        'concept_csv': '4️⃣ Concept CSV Creation',
        'numeric_ids': '5️⃣ Numeric ID Addition',
        'graphml': '6️⃣ GraphML Conversion',
        'embeddings': '7️⃣ Embedding Generation',
        'faiss_indexes': '8️⃣ FAISS Index Creation'
    }
    
    completed_stages = []
    pending_stages = []
    
    for stage_key, stage_name in stage_names.items():
        if stages[stage_key]:
            print(f"✅ {stage_name} - Completed")
            completed_stages.append(stage_key)
        else:
            print(f"⏳ {stage_name} - Pending")
            pending_stages.append(stage_key)
    
    print()
    
    # Recommend next steps
    if not stages['triple_extraction']:
        print("🚀 Recommendation: Start with full pipeline")
        print("   python pdf_kg_extraction_pipeline_robust.py")
    elif stages['triple_extraction'] and not stages['csv_conversion']:
        print("🔄 Recommendation: Continue with post-processing")
        print("   python pdf_kg_extraction_pipeline_robust.py --continue-from-extraction")
    elif stages['csv_conversion'] and not stages['concept_generation']:
        print("🧠 Recommendation: Run concept generation")
        print("   python pdf_kg_extraction_pipeline_robust.py --stage concepts")
    elif stages['concept_csv'] and not stages['embeddings']:
        print("🔍 Recommendation: Run embeddings and indexes")
        print("   python pdf_kg_extraction_pipeline_robust.py --stage embeddings")
    elif all(stages.values()):
        print("🎉 All stages completed! Ready for Neo4j import")
        print("   Check neo4j_import_commands.txt for import instructions")
    else:
        print("🔄 Recommendation: Continue with remaining post-processing")
        print("   python pdf_kg_extraction_pipeline_robust.py --continue-from-extraction")
    
    print()
    progress = (len(completed_stages) / len(stages)) * 100
    print(f"📈 Overall Progress: {progress:.1f}% ({len(completed_stages)}/{len(stages)} stages)")


def validate_prerequisites():
    """Validate that all prerequisites are met before running the pipeline."""
    print("🔍 Validating Prerequisites...")
    print("-" * 30)
    
    # Check config file
    if not os.path.exists('config.ini'):
        print("❌ config.ini not found")
        return False
    print("✅ config.ini found")
    
    # Check input directory
    if not os.path.exists('example_data'):
        print("❌ example_data directory not found")
        print("💡 Create this directory and place your JSON files there")
        return False
    
    # Check for input files
    input_files = [f for f in os.listdir('example_data') if f.endswith(('.json', '.txt'))]
    if len(input_files) == 0:
        print("❌ No input files found in example_data/")
        print("💡 Place your processed PDF files (JSON format) in example_data/")
        return False
    
    print(f"✅ Found {len(input_files)} input files")
    
    # Check import directory exists
    os.makedirs('import', exist_ok=True)
    print("✅ Import directory ready")
    
    print("✅ All prerequisites validated\n")
    return True


def show_usage_examples():
    """Display comprehensive usage examples for the robust pipeline."""
    print("\n" + "="*80)
    print("🎯 COMPREHENSIVE USAGE EXAMPLES")
    print("   Robust PDF Knowledge Graph Extraction Pipeline")
    print("="*80)
    
    print("\n📚 QUICK START SCENARIOS")
    print("-" * 40)
    print("🆕 First time running the pipeline:")
    print("   python pdf_kg_extraction_pipeline_robust.py --tier1")
    print("   python pdf_kg_extraction_pipeline_robust.py --notifications --email <EMAIL>")
    print()
    print("✅ After successful triple extraction (MOST COMMON):")
    print("   python pdf_kg_extraction_pipeline_robust.py --continue-from-extraction")
    print("   python pdf_kg_extraction_pipeline_robust.py -c  # Short form")
    print()
    print("🔍 Check current progress:")
    print("   python pdf_kg_extraction_pipeline_robust.py --status")
    
    print("\n🔄 RESUME & RECOVERY OPERATIONS")
    print("-" * 40)
    print("🚀 Standard resume (automatic detection):")
    print("   python pdf_kg_extraction_pipeline_robust.py")
    print()
    print("🔄 Continue from specific point:")
    print("   python pdf_kg_extraction_pipeline_robust.py --continue-from-extraction")
    print()
    print("⚠️  Retry only failed operations:")
    print("   python pdf_kg_extraction_pipeline_robust.py --retry-failed")
    print()
    print("🗑️  Fresh start (DESTRUCTIVE - removes all progress):")
    print("   python pdf_kg_extraction_pipeline_robust.py --force-restart")
    
    print("\n🎛️  INDIVIDUAL STAGE EXECUTION")
    print("-" * 40)
    print("📄 CSV conversion only:")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage csv")
    print()
    print("🧠 Concept generation only:")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage concepts")
    print()
    print("🔍 Embeddings and indexes only:")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage embeddings")
    print()
    print("⚡ All post-processing stages:")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage post-process")
    
    print("\n⚙️  CONFIGURATION & OPTIMIZATION")
    print("-" * 40)
    print("💻 Tier 1 API limits (free/limited accounts):")
    print("   python pdf_kg_extraction_pipeline_robust.py --tier1")
    print()
    print("📁 Custom dataset name:")
    print("   python pdf_kg_extraction_pipeline_robust.py --dataset financial_reports_2024")
    print()
    print("🔍 Filter input files by pattern:")
    print("   python pdf_kg_extraction_pipeline_robust.py --pattern \"annual_report*\"")
    print("   python pdf_kg_extraction_pipeline_robust.py --pattern \"*2024*.json\"")
    print()
    print("🔧 Increase retry attempts for reliability:")
    print("   python pdf_kg_extraction_pipeline_robust.py --max-retries 5")
    
    print("\n📧 MONITORING & NOTIFICATIONS")
    print("-" * 40)
    print("📧 Enable email notifications:")
    print("   python pdf_kg_extraction_pipeline_robust.py --notifications --email <EMAIL>")
    print()
    print("📊 Monitor with status checks:")
    print("   python pdf_kg_extraction_pipeline_robust.py --status")
    print("   # Run this anytime to check progress")
    
    print("\n🏭 PRODUCTION & ENTERPRISE SCENARIOS")
    print("-" * 40)
    print("🏢 High-reliability production setup:")
    print("   python pdf_kg_extraction_pipeline_robust.py \\\\")
    print("     --tier1 \\\\")
    print("     --notifications --email <EMAIL> \\\\")
    print("     --max-retries 5 \\\\")
    print("     --dataset quarterly_reports_2024")
    print()
    print("🔄 Automated recovery after interruption:")
    print("   python pdf_kg_extraction_pipeline_robust.py \\\\")
    print("     --continue-from-extraction \\\\")
    print("     --notifications --email <EMAIL>")
    print()
    print("📊 Batch processing with monitoring:")
    print("   python pdf_kg_extraction_pipeline_robust.py \\\\")
    print("     --dataset legal_documents \\\\")
    print("     --pattern \"contract_*.json\" \\\\")
    print("     --tier1 \\\\")
    print("     --notifications --email <EMAIL>")
    
    print("\n🚨 TROUBLESHOOTING & DEBUGGING")
    print("-" * 40)
    print("🔍 Debug specific pipeline stage:")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage csv")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage concepts")
    print()
    print("📊 Check what's completed vs pending:")
    print("   python pdf_kg_extraction_pipeline_robust.py --status")
    print()
    print("🔄 Retry with increased resilience:")
    print("   python pdf_kg_extraction_pipeline_robust.py --retry-failed --max-retries 7")
    print()
    print("🗑️  Nuclear option (complete restart):")
    print("   python pdf_kg_extraction_pipeline_robust.py --force-restart --tier1")
    
    print("\n🔗 COMMAND CHAINING")
    print("-" * 40)
    print("⛓️  Sequential stage execution:")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage csv && \\\\")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage concepts && \\\\")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage embeddings")
    print()
    print("📊 Status checks between operations:")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage csv")
    print("   python pdf_kg_extraction_pipeline_robust.py --status")
    print("   python pdf_kg_extraction_pipeline_robust.py --stage concepts")
    
    print("\n💡 WORKFLOW RECOMMENDATIONS")
    print("-" * 40)
    print("1️⃣  Initial Setup & First Run:")
    print("   python pdf_kg_extraction_pipeline_robust.py --tier1 --notifications --email <EMAIL>")
    print()
    print("2️⃣  After Triple Extraction Completes:")
    print("   python pdf_kg_extraction_pipeline_robust.py --continue-from-extraction")
    print()
    print("3️⃣  If Pipeline Gets Interrupted:")
    print("   python pdf_kg_extraction_pipeline_robust.py --status  # Check progress")
    print("   python pdf_kg_extraction_pipeline_robust.py  # Auto-resume")
    print()
    print("4️⃣  For Production Deployments:")
    print("   python pdf_kg_extraction_pipeline_robust.py --tier1 --max-retries 5 --notifications --email <EMAIL>")
    print()
    print("5️⃣  For Development/Testing:")
    print("   python pdf_kg_extraction_pipeline_robust.py --dataset test_data --max-retries 1")
    
    print("\n📖 ADDITIONAL RESOURCES")
    print("-" * 40)
    print("📚 Full help: python pdf_kg_extraction_pipeline_robust.py --help")
    print("📊 Status:    python pdf_kg_extraction_pipeline_robust.py --status")
    print("📋 Examples:  python pdf_kg_extraction_pipeline_robust.py --examples")
    print("📖 Docs:      See CLAUDE.md in project root directory")
    print()
    print("="*80)


def main():
    """Main entry point for the robust pipeline."""
    parser = argparse.ArgumentParser(
        description="""
Robust PDF Knowledge Graph Extraction Pipeline
============================================

An enhanced, production-ready pipeline for converting PDF documents into interactive knowledge graphs
with comprehensive resumability, error handling, and progress tracking capabilities.

PIPELINE STAGES:
1. Triple Extraction    - Extract entities, relations, and events from text
2. CSV Conversion      - Convert JSON results to CSV format for Neo4j
3. Concept Generation  - Generate high-level concepts from extracted triples
4. Concept CSV         - Create concept mapping files
5. Numeric ID Addition - Add numeric identifiers for graph nodes
6. GraphML Conversion  - Convert to NetworkX-compatible GraphML format
7. Embedding Generation- Create vector embeddings for semantic search
8. FAISS Index Creation- Build fast similarity search indexes

OPERATION MODES:
• Full Pipeline:       Complete end-to-end processing from PDFs to Neo4j-ready files
• Resume Mode:         Continue from any previous failure point automatically
• Stage Mode:          Execute individual pipeline stages independently
• Status Mode:         Check current progress and get recommendations
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
USAGE EXAMPLES:
==============

Basic Operations:
  %(prog)s                          # Run complete pipeline with auto-resume
  %(prog)s --help                   # Show this detailed help message

Resume & Recovery:
  %(prog)s -c                       # Continue from completed triple extraction
  %(prog)s --continue-from-extraction  # Same as above (full form)
  %(prog)s --retry-failed           # Retry only previously failed operations
  %(prog)s --force-restart          # Ignore existing progress, start fresh

Individual Stages:
  %(prog)s --stage csv              # Convert JSON extraction to CSV format
  %(prog)s --stage concepts         # Generate concepts from existing triples
  %(prog)s --stage embeddings       # Create embeddings and FAISS indexes
  %(prog)s --stage post-process     # Run all post-processing steps

Configuration & Optimization:
  %(prog)s --tier1                  # Use Tier 1 optimized token limits
  %(prog)s --dataset my_data        # Specify custom dataset name
  %(prog)s --pattern "2024*"        # Filter input files by pattern

Monitoring & Notifications:
  %(prog)s --status                 # Check pipeline progress and next steps
  %(prog)s --notifications --email <EMAIL>  # Enable email alerts
  %(prog)s --max-retries 5          # Increase retry attempts for reliability

Advanced Usage:
  %(prog)s --tier1 --notifications --email <EMAIL> --max-retries 5
  %(prog)s --dataset financial_docs --pattern "annual_report*" --continue-from-extraction

WORKFLOW RECOMMENDATIONS:
=========================
1. Initial Run:     %(prog)s --tier1 --notifications --email <EMAIL>
2. After Interrupt: %(prog)s --continue-from-extraction
3. Stage-by-Stage:  %(prog)s --stage csv && %(prog)s --stage concepts
4. Check Progress:  %(prog)s --status
5. Fresh Start:     %(prog)s --force-restart

For detailed documentation, see CLAUDE.md in the project root directory.
        """
    )
    
    parser.add_argument('--dataset', '-d', 
                       default='pdf_dataset',
                       metavar='NAME',
                       help="""Dataset name for output directory (default: pdf_dataset)
                       
                       Creates directory structure: import/{DATASET}/
                       All pipeline outputs will be organized under this directory.
                       Use descriptive names like 'financial_reports_2024' or 'legal_documents'.
                       
                       Example: --dataset medical_papers""")
    
    parser.add_argument('--pattern', '-p',
                       default='',
                       metavar='PATTERN',
                       help="""Filename pattern to match input files (default: all files)
                       
                       Filters input files using glob patterns. Useful for processing
                       specific subsets of documents or file naming conventions.
                       
                       Examples:
                         --pattern "annual_report*"    # Files starting with 'annual_report'
                         --pattern "*2024*"            # Files containing '2024'
                         --pattern "financial_*.json"  # Specific JSON files""")
    
    parser.add_argument('--force-restart', '-f',
                       action='store_true',
                       help="""Ignore existing state and start fresh (DESTRUCTIVE)
                       
                       WARNING: This will delete all existing pipeline progress and start
                       from the beginning. Use when you want to completely reprocess data
                       or when the existing state is corrupted.
                       
                       • Clears all checkpoints and progress tracking
                       • Removes existing output files
                       • Restarts from triple extraction
                       
                       Use with caution in production environments.""")
    
    parser.add_argument('--retry-failed', '-r',
                       action='store_true',
                       help="""Retry previously failed operations only
                       
                       Attempts to reprocess only the operations that failed in previous
                       runs, skipping successfully completed work. More efficient than
                       --force-restart when dealing with partial failures.
                       
                       • Preserves completed work
                       • Retries failed batches with exponential backoff
                       • Useful for recovering from network/API issues
                       • Safe to run multiple times""")
    
    parser.add_argument('--tier1', '-t',
                       action='store_true',
                       help="""Use Tier 1 optimized settings for token limits
                       
                       Applies conservative configuration optimized for Tier 1 API limits:
                       • Smaller batch sizes (5 for concepts vs 16 default)
                       • Single worker process to avoid rate limits
                       • Reduced token limits per request
                       • Longer delays between API calls
                       
                       Recommended for:
                       • Free or limited API tiers
                       • Rate-limited environments
                       • Large-scale processing with strict quotas""")
    
    parser.add_argument('--notifications', '-n',
                       action='store_true',
                       help="""Enable email notifications for critical errors
                       
                       Sends email alerts for:
                       • Pipeline failures and critical errors
                       • Completion notifications for long-running operations
                       • Error summaries with troubleshooting suggestions
                       
                       Requires --email parameter to specify recipient address.
                       
                       Useful for:
                       • Production deployments
                       • Long-running batch processing
                       • Monitoring unattended operations
                       • DevOps automation workflows""")
    
    parser.add_argument('--email', '-e',
                       default='',
                       metavar='EMAIL',
                       help="""Email address for notifications (requires --notifications)
                       
                       Specifies the recipient email address for pipeline notifications.
                       Must be used together with --notifications flag.
                       
                       Examples:
                         --notifications --email <EMAIL>
                         --notifications --email <EMAIL>
                       
                       Supports standard email formats and can be configured for
                       team distribution lists or monitoring systems.""")
    
    parser.add_argument('--max-retries', '-m',
                       type=int,
                       default=3,
                       metavar='N',
                       help="""Maximum number of retry attempts per operation (default: 3)
                       
                       Controls retry behavior for failed operations:
                       • API calls that timeout or return errors
                       • File processing operations that fail
                       • Network connectivity issues
                       
                       Retry mechanism uses exponential backoff with jitter to avoid
                       overwhelming services during recovery.
                       
                       Recommended values:
                         1-2   Fast failure for development/testing
                         3-5   Standard production environments (default: 3)
                         5-10  High-reliability, network-unstable environments
                         
                       Higher values increase total processing time but improve
                       success rates in unreliable environments.""")
    
    parser.add_argument('--continue-from-extraction', '-c',
                       action='store_true',
                       help="""Continue from completed triple extraction (MOST COMMON)
                       
                       Starts post-processing from existing extraction results, running:
                       1. CSV conversion (JSON → CSV)
                       2. Concept generation 
                       3. Concept CSV creation
                       4. Numeric ID addition
                       5. GraphML conversion
                       6. Embedding generation
                       7. FAISS index creation
                       
                       Perfect for:
                       • After successful triple extraction completion
                       • When extraction is done but post-processing failed
                       • Resuming interrupted workflows
                       
                       This is the most commonly used option after extraction.""")
    
    parser.add_argument('--stage', '-s',
                       choices=['csv', 'concepts', 'concept-csv', 'embeddings', 'post-process'],
                       metavar='STAGE',
                       help="""Run specific pipeline stage independently
                       
                       AVAILABLE STAGES:
                       
                       csv           Convert JSON extraction results to CSV format
                                    • Processes extraction JSON files
                                    • Creates node/edge CSV files for Neo4j
                                    • Required before concept generation
                       
                       concepts      Generate high-level concepts from triples
                                    • Analyzes extracted entities and relations
                                    • Creates concept hierarchies and mappings
                                    • Generates concept CSV files
                       
                       concept-csv   Create concept CSV files from existing concept outputs
                                    • Processes concept generation outputs only
                                    • Creates concept mapping CSV files
                                    • Requires that concept generation previously completed
                       
                       concept-csv   Create concept CSV files from existing concept outputs
                                    • Processes concept generation outputs only
                                    • Creates concept mapping CSV files
                                    • Requires that concept generation previously completed
                       
                       concept-csv   Create concept CSV files from existing concept outputs
                                    • Processes concept generation outputs only
                                    • Creates concept mapping CSV files
                                    • Requires that concept generation previously completed
                       
                       concept-csv   Create concept CSV files from existing concept outputs
                                    • Processes concept generation outputs only
                                    • Creates concept mapping CSV files
                                    • Requires that concept generation previously completed
                       
                       embeddings    Create vector embeddings and search indexes
                                    • Generates semantic embeddings for entities
                                    • Creates FAISS indexes for similarity search
                                    • Includes GraphML conversion and numeric IDs
                       
                       post-process  Run ALL post-processing stages (recommended)
                                    • Equivalent to --continue-from-extraction
                                    • Runs csv → concepts → embeddings pipeline
                                    • Most efficient for complete post-processing
                       
                       Use for granular control or debugging specific stages.""")
    
    parser.add_argument('--status',
                       action='store_true',
                       help="""Show current pipeline status and recommendations (READ-ONLY)
                       
                       Analyzes existing files and directories to determine:
                       • Which pipeline stages have been completed
                       • Current progress percentage
                       • File sizes and output statistics
                       • Recommended next steps and commands
                       
                       Provides intelligence about:
                       • Whether extraction has completed successfully
                       • Which post-processing steps are remaining
                       • Optimal commands to continue processing
                       
                       Safe to run at any time - makes no changes to files or state.
                       Excellent for understanding current progress after interruptions.""")
    
    parser.add_argument('--examples',
                       action='store_true',
                       help="""Show detailed usage examples and exit (READ-ONLY)
                       
                       Displays comprehensive examples covering:
                       • Common workflows and use cases
                       • Best practices for different scenarios
                       • Command combinations for specific needs
                       • Troubleshooting and recovery procedures
                       
                       Includes examples for:
                       • Initial pipeline runs
                       • Resume and recovery operations
                       • Stage-by-stage processing
                       • Production deployment patterns
                       • Integration with automation systems
                       
                       Use this to learn optimal usage patterns for your specific needs.""")
    
    parser.add_argument('--performance', '-P',
                       choices=['low', 'medium', 'high', 'max'],
                       default='medium',
                       metavar='MODE',
                       help="""Performance optimization mode (default: medium)
                       
                       PERFORMANCE MODES:
                       
                       low           Conservative settings for stability
                                    • batch_size_concept: 16
                                    • max_workers: 2
                                    • Best for: API rate limits, debugging
                       
                       medium        Balanced performance and reliability
                                    • batch_size_concept: 64
                                    • max_workers: 8
                                    • Best for: Most users, default setting
                       
                       high          High throughput processing
                                    • batch_size_concept: 128
                                    • max_workers: 16
                                    • Best for: Local models, powerful hardware
                       
                       max           Maximum parallel processing
                                    • batch_size_concept: 256
                                    • max_workers: 32
                                    • Best for: Local Ollama, high-end systems""")
    
    args = parser.parse_args()
    
    if args.examples:
        show_usage_examples()
        return
    
    if args.status:
        show_pipeline_status(args.dataset)
        return
    
    print("🎯 Robust AutoSchemaKG Pipeline")
    print("Enhanced with resumability, error handling, and progress tracking")
    print("From PDF Processing to Interactive Knowledge Graph Q&A")
    print()
    
    # Validate prerequisites
    if not validate_prerequisites():
        print("❌ Prerequisites not met. Please address the issues above.")
        sys.exit(1)
    
    # Validate email configuration
    if args.notifications and not args.email:
        print("❌ Email address required when --notifications is enabled")
        print("Use --email <EMAIL>")
        sys.exit(1)
    
    try:
        # Determine which pipeline to run based on arguments
        if args.continue_from_extraction or args.stage == 'post-process':
            # Run post-processing pipeline
            success = run_post_processing_pipeline(
                dataset_name=args.dataset,
                filename_pattern=args.pattern,
                tier1_mode=args.tier1,
                performance_mode=args.performance,
                enable_notifications=args.notifications,
                notification_email=args.email,
                max_retries=args.max_retries
            )
        elif args.stage == 'csv':
            # Run CSV conversion only
            success = run_csv_conversion(
                dataset_name=args.dataset,
                filename_pattern=args.pattern,
                tier1_mode=args.tier1,
                performance_mode=args.performance
            )
        elif args.stage == 'concepts':
            # Run concept generation only
            success = run_concept_generation(
                dataset_name=args.dataset,
                filename_pattern=args.pattern,
                tier1_mode=args.tier1,
                performance_mode=args.performance
            )
        elif args.stage == 'embeddings':
            # Run embeddings and indexes only
            success = run_embeddings_and_indexes(
                dataset_name=args.dataset,
                filename_pattern=args.pattern,
                tier1_mode=args.tier1,
                performance_mode=args.performance
            )
        else:
            # Run the full robust pipeline
            success = run_robust_knowledge_extraction_pipeline(
                dataset_name=args.dataset,
                filename_pattern=args.pattern,
                force_restart=args.force_restart,
                retry_failed=args.retry_failed,
                tier1_mode=args.tier1,
                performance_mode=args.performance,
                enable_notifications=args.notifications,
                notification_email=args.email,
                max_retries=args.max_retries
            )
        
        if success:
            # Generate import summary
            generate_import_summary(args.dataset)
            print("\n🎉 Pipeline completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Pipeline failed. Check logs for details.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()