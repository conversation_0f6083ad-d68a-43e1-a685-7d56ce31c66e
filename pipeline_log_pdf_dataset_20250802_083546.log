2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250802_083546.log, Errors: pipeline_errors_pdf_dataset_20250802_083546.log
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | 🚀 Progress tracking initialized for dataset: pdf_dataset
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | 📊 Total files to process: 0
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | 🛡️ <PERSON><PERSON>rHandler initialized with robust recovery strategies
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | 🎯 Starting Stage: csv_conversion
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | 📋 Description: Converting JSON to CSV format
2025-08-02 08:35:46 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: parsing_error
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset |    Message: 'original_text'
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'csv_conversion', 'operation': 'csv_conversion'}
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset | 💥 All retries exhausted for csv_conversion
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: parsing_error
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset |    Message: 'original_text'
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'csv_conversion', 'operation': 'convert_json_to_csv'}
2025-08-02 08:35:46 | ERROR    | pipeline_pdf_dataset | CSV conversion failed: 'original_text'
