[{"id": "1", "text": "![](_page_0_Picture_0.jpeg)\n\n# **USER GUIDE 360T Bridge Administration**\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\n**User Guide 360T Bridge Administration: Change Requests**\n\nRelease 4.16 (November 2022)\n\n© 360 Treasury Systems AG, 2022 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG\n\n![](_page_1_Picture_0.jpeg)\n\n# **CONTENTS**\n\n| 1 | Introduction                                                                                                                | 4                          |\n|---|-----------------------------------------------------------------------------------------------------------------------------|----------------------------|\n| 2 | Getting Started                                                                                                             | 4                          |\n| 3 | Common Change Requests                                                                                                      | 5                          |\n|   | 3.1<br>Create a new user                                                                                                    | 6                          |\n|   | 3.2<br>Remove an existing user                                                                                              | 14                         |\n| 4 | Change Request Features                                                                                                     | 17                         |\n|   | 4.1<br>Search and filter                                                                                                    | 18                         |\n|   | 4.1.1<br>Header<br>4.1.2<br>Select Category<br>4.1.3<br>Select Company<br>4.1.4<br>Filter by CR id<br>4.2<br>Column Headers | 18<br>20<br>22<br>24<br>25 |\n|   | 4.3<br>Status                                                                                                               | 25                         |\n|   | 4.3.1<br>Number of approvals<br>4.3.2<br>Status Types<br>4.4<br>Action Icons                                                | 26<br>26<br>26             |\n| 5 | Appendix I                                                                                                                  | 28                         |\n| 6 | Contacting 360T                                                                                                             | 30                         |\n\n![](_page_2_Picture_0.jpeg)\n\n# **FIGURES**\n\n| Figure 1 Header Bar5                                                 |  |\n|----------------------------------------------------------------------|--|\n| Figure 2 Bridge Administration: Homepage<br>5                        |  |\n| Figure 3 Bridge Administration: Company Details6                     |  |\n| Figure 4 Bridge Administration: Users<br>7                           |  |\n| Figure 5 Bridge Administration: User Creation Wizard8                |  |\n| Figure 6 User Creation Wizard: Trading Type Selection9               |  |\n| Figure 7 User Creation Wizard: Deal Tracking Membership<br>10        |  |\n| Figure 8 User Creation Wizard: Deal Tracking Viewer Membership<br>11 |  |\n| Figure 9 User Creation Wizard: Optional Admin Rights<br>12           |  |\n| Figure 10 User Creation Wizard: Overview12                           |  |\n| Figure 11 Bridge Administration: Change Request<br>13                |  |\n| Figure 12 Bridge Administration: Entity selection14                  |  |\n| Figure 13 Bridge Administration: User Deletion<br>15                 |  |\n| Figure 14 Bridge Administration: Change Request for User Deletion16  |  |\n| Figure 15 Bridge Administration: Change Request Quick Link17         |  |\n| Figure 16 Change Requests: Main Page18                               |  |\n| Figure 17 Change Requests: Header<br>19                              |  |\n| Figure 18 Change Requests: Show all toggle19                         |  |\n| Figure 19 Change Requests: Select Category20                         |  |\n| Figure 20 Change Requests: Select Category list<br>20                |  |\n| Figure 21 Change Requests: Apply Category21                          |  |\n| Figure 22 Change Requests: Applied Category21                        |  |\n| Figure 23 Change Requests: Select Company22                          |  |\n| Figure 24 Change Requests: Select Company22                          |  |\n| Figure 25 Change Requests: Apply Company23                           |  |\n| Figure 26 Change Requests: Applied Company23                         |  |\n| Figure 27 Change Requests: Filter by CR id<br>24                     |  |\n| Figure 28 Change Requests: Applied filter by CR id25                 |  |\n| Figure 29 Change Requests: Action icons27                            |  |\n| Figure 30 Change Requests: Show Summary pop-up28                     |  |\n\n![](_page_3_Picture_0.jpeg)\n\n# **User Guide 360T Bridge Administration – Change Requests**\n\n# <span id=\"page-3-0\"></span>**1 Introduction**\n\nThis user manual describes the Change Request feature of the 360T Bridge Administration tool. Clients are able to make changes within various Configurations (i.e., Institution) via \"change request\" workflows.\n\nEvery change to a configuration requires a Change Request (CR). For example, a client admin can request the creation of a new user or removal of an old user.\n\nDepending on the requested change, the following workflows are possible:\n\n- Request > Approval > Complete\n- Request > Approval > Request sent to 360T to be completed\n\nRequested changes can be reviewed and managed within the Change Request feature.\n\n### **Please note:**\n\nOnly users with corresponding user rights are able to access the tool and perform certain tasks.\n\nA user with an \"**Administrator**\" role is able to create requests. A user with a \"**Supervisor**\" role is able to approve requests. A user may have both roles, but cannot approve his/her own requests.\n\n<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.\n\n# <span id=\"page-3-1\"></span>**2 Getting Started**\n\nThe Change Request feature is found within the Bridge Administration tool. Bridge Administration can be accessed either via the menu option \"Administration\" in the screen header of the Bridge application or as a standalone feature from your starter applet.\n\n![](_page_4_Picture_0.jpeg)\n\n**Figure 1 Header Bar**\n\n<span id=\"page-4-1\"></span>The Bridge Administration feature opens to a homepage with available shortcuts to different categories of configuration tools and actions for the particular user.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\n![](_page_4_Picture_4.jpeg)\n\n**Figure** 2 **Bridge Administration: Homepage**\n\n# <span id=\"page-4-2\"></span><span id=\"page-4-0\"></span>**3 Common Change Requests**\n\nIn the Institution Configuration, Change Requests (CRs) can be created to make changes to the following:\n\nUsers:\n\n• Access (add new / remove users)\n\n![](_page_5_Picture_0.jpeg)\n\n- Individual details (phone number, email address etc.)\n- Position (front/back office)\n- Trading rights (trade-as or trade-on-behalf companies assigned to a user)\n- Deal Tracking rights (visibility of trades for each user)\n\nCompanies:\n\n- Status (active / inactive)\n- Company details (Phone number, Fax number)\n- Related entities (trade-as, trade-on-behalf or ITEX etc.)\n\nInstructions for completing common CRs can be found in the following sections. A comprehensive list of all possible changes and workflows is provided in Appendix I.\n\n### <span id=\"page-5-0\"></span>**3.1 Create a new user**\n\nCreating a TEX user requires an Administrator to create a Change Request, a Supervisor (different user) to approve the Change Request and 360T to apply the final changes.\n\nAdministrator User:\n\n![](_page_5_Picture_14.jpeg)\n\n1. Click on the entity of the user.\n\n<span id=\"page-5-1\"></span>**Figure 3 Bridge Administration: Company Details**\n\n![](_page_6_Picture_0.jpeg)\n\n2. Click on Users.\n\n|                                 | RFS REQUESTER $\\vee$<br><b>ORDER MANAGEMENT</b>      | <b>BRIDGE ADMINISTRATION</b> | $^{+}$                                                 |              |                                                                         | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\qquad \\qquad \\bigcirc$ (1) AA $ \\Box$ X |                                                        |\n|---------------------------------|------------------------------------------------------|------------------------------|--------------------------------------------------------|--------------|-------------------------------------------------------------------------|------------------------------------------------------------------------------------------------|--------------------------------------------------------|\n| 合                               | Q 發<br>上 童<br>≺                                      |                              | Company Details   Users (5)   Deal Tracking Groups (1) | Daughter (5) | <b>ITEX</b>   Legal Entities   Legal Entity Groups (2)   TAS/TOB Groups |                                                                                                | $\\text{and}\\quad \\mathbb{R}\\text{,}\\mathbb{R}\\text{,}$ |\n|                                 | $\\land \\overline{\\text{m}}$ TradeAsE                 | 自工也                          |                                                        |              |                                                                         |                                                                                                |                                                        |\n| $\\mathcal{G}$                   | $\\underline{\\widehat{\\mathbf{m}}}$ TradeAsE.ITEX.E1  |                              | Users (5)                                              |              | <b>Status</b>                                                           |                                                                                                |                                                        |\n|                                 | TradeAsE.TAS.E1                                      |                              | TradeAsE BackOfficeA                                   |              | <b>Active</b>                                                           | $\\overline{\\mathbf{C}}$<br>Iû                                                                  |                                                        |\n| 甌                               | $\\underline{\\widehat{\\mathbf{m}}}$ TradeAsE.TAS.E2   |                              | TradeAsE.TreasurerA                                    |              | Active                                                                  | û<br>$\\sim$                                                                                    |                                                        |\n|                                 | TradeAsE.TAS.E3                                      |                              | TradeAsE.TreasurerB                                    |              | <b>Active</b>                                                           | û<br>$\\sim$                                                                                    |                                                        |\n| tip                             | $\\underline{\\widehat{\\mathbf{m}}}$ TradeAsE.TradeAsE |                              | TradeAsE.TreasurerC                                    |              | Active                                                                  | û<br>$\\sim$                                                                                    |                                                        |\n|                                 | TradeAsE.BackOfficeA                                 |                              | FradeAsE.TreasurerD                                    |              | <b>Deleted</b>                                                          | $\\bigcirc$ $\\varphi$                                                                           |                                                        |\n|                                 | TradeAsE.TreasurerA                                  |                              |                                                        |              |                                                                         | <b>Create User</b>                                                                             |                                                        |\n|                                 | TradeAsE.TreasurerB                                  |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 | TradeAsE.TreasurerC                                  |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 | FradeAsE.TreasurerD                                  |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n| $\\ddot{\\Omega}$                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n| $\\frac{\\mathbf{C}}{\\mathbf{C}}$ |                                                      | Create Change Request        |                                                        |              |                                                                         | Discard All Changes                                                                            | Save                                                   |\n|                                 |                                                      |                              |                                                        |              |                                                                         |                                                                                                |                                                        |\n| Θ                               |                                                      | TradeAsE $\\times$            |                                                        |              |                                                                         |                                                                                                |                                                        |\n|                                 | TradeAsE.TreasurerB, TradeAsE // INT                 |                              | $\\Box$                                                 |              |                                                                         | Thu, 21. Mar 2019, 11:39:32 GMT // Connected [FFM] ·                                           | INT                                                    |\n\n<span id=\"page-6-0\"></span>**Figure 4 Bridge Administration: Users**\n\n- 3. Click on Create User .\n- 4. A Help Wizard will appear.\n  - a. Enter the user's information.\n\n![](_page_7_Picture_0.jpeg)\n\n| $2$ $3$ $4$ $5$ $6$ $\\sqrt{ }$<br>$\\mathbf{1}$ |               | Create an Individual                  |                       |\n|------------------------------------------------|---------------|---------------------------------------|-----------------------|\n|                                                |               | Please fill in the Individual details |                       |\n|                                                |               |                                       |                       |\n|                                                | Login Name*   | TradeAsE<br>$\\checkmark$              |                       |\n|                                                | Last Name*    | Last Name should be specified         |                       |\n|                                                | First Name*   | First Name should be specified        |                       |\n|                                                | Description   |                                       |                       |\n|                                                | Email *       | Email should be specified             |                       |\n|                                                | Phone Number* | Phone Number should be specified      |                       |\n|                                                | Fax Number    |                                       |                       |\n|                                                | Salutation *  | $(MR \\vee)$                           |                       |\n| Previous                                       | Position'     | O Front Office                        | <b>Next</b><br>Cancel |\n\n<span id=\"page-7-0\"></span>**Figure 5 Bridge Administration: User Creation Wizard**\n\nb. Indicate if the user should be a Front or Back Office user.\n\n| c.<br>Click Next |  |\n|------------------|--|\n\nd. Indicate if the user trades for the main entity-only (Plain TEX User). Alternatively, if requests are sent by the user in the name of related entities using 360T's Trade-As or Trade-on-Behalf functionality then please indicate if the user has trade-as (Trade-as User) or trade-onbehalf (Trade-on-behalf User) rights.\n\n![](_page_8_Picture_0.jpeg)\n\n| <b>Trading Type</b> |               | Trade-as User        | ∧ |\n|---------------------|---------------|----------------------|---|\n|                     |               | Trade-as User        |   |\n|                     | Available     | Trade-on-behalf User |   |\n| TradeAsE.TAS        |               | Plain TEX User       |   |\n|                     | TradeAsE.TAS2 |                      |   |\n|                     |               |                      |   |\n|                     |               |                      |   |\n|                     |               | $\\gg$                |   |\n|                     |               | $\\ll$                |   |\n|                     |               |                      |   |\n\n<span id=\"page-8-0\"></span>**Figure 6 User Creation Wizard: Trading Type Selection**\n\n**Please note**: This will depend on your particular setup. \"Plain TEX User\" is the default option. In case of trade-as or trade-on-behalf rights the legal entities can be assigned immediately to the user by moving a Legal Entity Group from Available to Selected as seen above.\n\n- e. Click Next .\n- f. If needed, add the user as a Member of a Deal Tracking group by moving the group from Available to Selected.\n\n|          |                                                   |                       | ור       |        | <b>DEAIANIE DAIN</b><br><b>GROUP</b> |  |\n|----------|---------------------------------------------------|-----------------------|----------|--------|--------------------------------------|--|\n| $3 -$    | $5 \\t{5}$ $\\t{6}$<br>4                            | Create an Individual  |          |        |                                      |  |\n|          | Configure Deal Tracking membership and viewership |                       |          |        |                                      |  |\n|          | <b>Deal Tracking Members</b>                      |                       |          |        |                                      |  |\n|          | Available<br>TradeAsE.Deals                       |                       | Selected |        |                                      |  |\n|          |                                                   | $\\mathbf{Z}$<br>$\\lt$ |          |        |                                      |  |\n|          |                                                   |                       |          |        |                                      |  |\n|          |                                                   | $\\gg$<br>$\\ll$        |          |        |                                      |  |\n|          |                                                   |                       |          |        |                                      |  |\n|          |                                                   |                       |          |        |                                      |  |\n|          |                                                   |                       |          |        |                                      |  |\n| Previous |                                                   |                       |          | Cancel | <b>Next</b>                          |  |\n\n<span id=\"page-9-0\"></span>**Figure 7 User Creation Wizard: Deal Tracking Membership**\n\n**Please note**: Viewers of a Deal Tracking group can see the Members' executed trades. Back Office users do not need to be added as \"Members\" to a Deal Tracking group as they do not trade.\n\n- g. Click Next .\n- h. If needed, add the user as a Viewer of a Deal Tracking group by moving the group from Available to Selected.\n\n![](_page_10_Picture_0.jpeg)\n\n| $3 \\choose 4 \\choose 5 \\choose 6 \\times$          | Create an Individual                     |          |             |\n|---------------------------------------------------|------------------------------------------|----------|-------------|\n| Configure Deal Tracking membership and viewership |                                          |          |             |\n| <b>Deal Tracking Viewers</b>                      |                                          |          |             |\n| Available                                         |                                          | Selected |             |\n| TradeAsE.Deals                                    | $\\rightarrow$                            |          |             |\n|                                                   | $\\,$ $\\,$ $\\,$                           |          |             |\n|                                                   | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |          |             |\n|                                                   | $\\ll$                                    |          |             |\n|                                                   |                                          |          |             |\n|                                                   |                                          |          |             |\n|                                                   |                                          |          |             |\n| Previous                                          |                                          | Cancel   | <b>Next</b> |\n\n<span id=\"page-10-0\"></span>**Figure 8 User Creation Wizard: Deal Tracking Viewer Membership**\n\n**Please note**: Viewers of a Deal Tracking group can see the Members' executed trades.\n\n- i. Click Next .\n- j. Indicate if the user should send Requests (Treasurer) or send Prices (Trader) and define the desired administrative rights.\n\n|                                                                                                                                                                                                                                               |                                      | 3C 1                 |              | DEUIJUNE DUNJ<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |  |\n|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------|----------------------|--------------|-----------------------------------------------------------|--|\n| $\\left  \\begin{array}{c} 1 \\\\ 2 \\end{array} \\right\\rangle$ $\\left  \\begin{array}{c} 2 \\\\ 3 \\end{array} \\right\\rangle$ $\\left  \\begin{array}{c} 4 \\\\ 5 \\end{array} \\right\\rangle$ 5 $\\left  \\begin{array}{c} 6 \\\\ 5 \\end{array} \\right\\rangle$ |                                      | Create an Individual |              |                                                           |  |\n|                                                                                                                                                                                                                                               | Send Prices                          | $\\frac{1}{\\sqrt{2}}$ |              |                                                           |  |\n|                                                                                                                                                                                                                                               | Send Requests                        |                      |              |                                                           |  |\n|                                                                                                                                                                                                                                               | General: Optional admin rights       |                      |              |                                                           |  |\n|                                                                                                                                                                                                                                               | Counterpart Relationships            | <b>NONE</b>          | $\\checkmark$ |                                                           |  |\n|                                                                                                                                                                                                                                               | Vantage                              | <b>NONE</b>          | $\\checkmark$ |                                                           |  |\n|                                                                                                                                                                                                                                               | Send Prices: Optional admin rights   |                      |              |                                                           |  |\n|                                                                                                                                                                                                                                               | Requester Limits                     | <b>NONE</b>          | $\\checkmark$ |                                                           |  |\n|                                                                                                                                                                                                                                               | <b>Destination Rules</b>             | <b>NONE</b>          | $\\checkmark$ |                                                           |  |\n|                                                                                                                                                                                                                                               | Send Requests: Optional admin rights |                      |              |                                                           |  |\n|                                                                                                                                                                                                                                               | <b>Bank Baskets</b>                  | <b>NONE</b>          | $\\checkmark$ |                                                           |  |\n|                                                                                                                                                                                                                                               | <b>Treasurer Limits</b>              | <b>NONE</b>          | $\\checkmark$ |                                                           |  |\n|                                                                                                                                                                                                                                               | Provider Limits                      | <b>NONE</b>          | $\\checkmark$ |                                                           |  |\n| <b>Previous</b>                                                                                                                                                                                                                               |                                      |                      |              | <b>Next</b><br>Cancel                                     |  |\n\n<span id=\"page-11-0\"></span>**Figure 9 User Creation Wizard: Optional Admin Rights**\n\n- k. Click Next .\n- l. Review your changes.\n\n|                 | 6 <sup>1</sup><br>5 <sub>1</sub><br>$\\sqrt{}$ | Create an Individual                 |                  |\n|-----------------|-----------------------------------------------|--------------------------------------|------------------|\n|                 |                                               | Overview: Please review your changes |                  |\n|                 |                                               |                                      |                  |\n|                 | Individual Name                               | TradeAsE.Doe                         |                  |\n|                 | Last Name                                     | <b>Doe</b>                           |                  |\n|                 | First Name                                    | John                                 |                  |\n|                 | Description                                   |                                      |                  |\n|                 | Email                                         | <EMAIL>                 |                  |\n|                 | Phone Number                                  | +49 123 456 789                      |                  |\n|                 | Fax Number                                    |                                      |                  |\n|                 | Salutation                                    | Mr.                                  |                  |\n|                 | Position                                      | Hybrid                               |                  |\n|                 | Country                                       | Germany                              |                  |\n|                 |                                               |                                      |                  |\n|                 | Trading Type                                  | Plain TEX User                       |                  |\n|                 | TAS/TOB Groups                                |                                      |                  |\n|                 | <b>Deal Tracking Member</b>                   |                                      |                  |\n| <b>Previous</b> | <b>Deal Tracking Viewer</b>                   |                                      | Create<br>Cancel |\n\n<span id=\"page-11-1\"></span>**Figure 10 User Creation Wizard: Overview**\n\n![](_page_12_Picture_0.jpeg)\n\nm. Click Create ..\n\n5. Add a comment.\n\n| Your text here |  |  |\n|----------------|--|--|\n|                |  |  |\n|                |  |  |\n\n6. Click OK.\n\nSupervisor user (different user):\n\n1. Open Change Requests using the Change Request quick link.\n\n| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                              | $\\boldsymbol{+}$<br><b>BRIDGE ADMINISTRATION</b> |                             |                        | $\\checkmark$ Preferences $\\checkmark$ Administration $\\checkmark$ Help $\\hat{\\varphi}$ ( $\\hat{\\varphi}$ AA $ \\hat{\\varphi}$ $\\times$ |\n|------------------------------------------------------------------------------|--------------------------------------------------|-----------------------------|------------------------|---------------------------------------------------------------------------------------------------------------------------------------|\n| 合                                                                            | Configurations                                   | <b>Administration Start</b> |                        |                                                                                                                                       |\n|                                                                              |                                                  |                             |                        |                                                                                                                                       |\n|                                                                              | Institution<br><b>Actions</b>                    | <b>Bank Baskets</b>         |                        |                                                                                                                                       |\n|                                                                              |                                                  |                             |                        |                                                                                                                                       |\n|                                                                              | <b>Change Request</b>                            | Wizards                     | <b>Evaluator Tools</b> |                                                                                                                                       |\n|                                                                              |                                                  |                             |                        |                                                                                                                                       |\n|                                                                              |                                                  |                             |                        |                                                                                                                                       |\n|                                                                              |                                                  |                             |                        |                                                                                                                                       |\n| $\\begin{array}{c c} \\hline \\textbf{B} & \\textbf{O} & \\textbf{B} \\end{array}$ |                                                  |                             |                        |                                                                                                                                       |\n| 1 TradeAsE.TreasurerA, TradeAsE // INT                                       |                                                  | $\\equiv$                    |                        | Wed, 20. Mar 2019, 10:45:51 GMT // Connected [FFM] $\\bullet$ NT                                                                       |\n\n<span id=\"page-12-0\"></span>**Figure 11 Bridge Administration: Change Request**\n\n- 2. Click Show all .\n- 3. Click Approve .\n- 4. Click OK .\n\n![](_page_13_Picture_0.jpeg)\n\nThe CR will be sent to 360T to apply final changes. For any questions, please contact our CAS team and reference the CR-number in the \"Id\" column.\n\n### <span id=\"page-13-0\"></span>**3.2 Remove an existing user**\n\nRemoving a TEX user requires an Administrator to create a Change Request and a Supervisor (different user) to approve the change request.\n\nAdministrator User:\n\n1. Click on the entity of the user.\n\n|                                                                                                                                                                                                                                                             |                                                   |                                                                                                                  |                                                                                                                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\qquad \\qquad \\oplus$ ( $\\qquad \\oplus$ AA $ \\boxtimes \\times$ |\n|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------|------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------|\n| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                             | <b>BRIDGE ADMINISTRATION</b>                      | $+$                                                                                                              |                                                                                                                            |                                                                                                                      |\n| Q 楽   1 血<br>≺<br><mark>@</mark><br>$\\sim \\frac{1}{111}$ TradeAsE                                                                                                                                                                                           | <b>Prefixes</b><br><b>Overview</b>                | Internal trades only                                                                                             | Company Details Users (5) Deal Tracking Groups (1) Daughter (5) ITEX Legal Entities Legal Entity Groups (2) TAS/TOB Groups | のの目                                                                                                                  |\n| $\\mathcal{G}$<br>TradeAsE.ITEX.E1<br>$\\underline{\\widehat{\\mathbf{m}}}$ TradeAsE.TAS.E1<br>TradeAsE.TAS.E2<br>甄<br>TradeAsE.TAS.E3<br>tip<br>TradeAsE.TradeAsE<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC |                                                   | Company Name *<br>Description<br><b>Phone Number</b><br>Fax Number<br>Country *<br>Currency                      | TradeAsE<br>Germany<br><b>EUR</b>                                                                                          | M)<br>$\\vee$                                                                                                         |\n| FradeAsE.TreasurerD                                                                                                                                                                                                                                         |                                                   | LEI<br>Status *<br><b>Provider Role</b><br><b>Requestor Role</b><br><b>Prime Broker</b><br>High Frequency Trader | Institution active<br>O Disabled<br>$\\vee$ Enabled<br>Disabled<br>O Disabled                                               | $\\checkmark$ )                                                                                                       |\n| $\\frac{1}{2}$<br>$\\bigoplus$                                                                                                                                                                                                                                | <b>Create Change Request</b><br>TradeAsE $\\times$ |                                                                                                                  |                                                                                                                            | Discard All Changes<br>Save                                                                                          |\n| 1/ TradeAsE.TreasurerB, TradeAsE // INT                                                                                                                                                                                                                     |                                                   | $\\Box \\Box \\Box$                                                                                                 |                                                                                                                            | Thu, 21. Mar 2019, 11:38:39 GMT // Connected [FFM] · INT                                                             |\n\n<span id=\"page-13-1\"></span>**Figure 12 Bridge Administration: Entity selection**\n\n2. Click on Users.\n\n![](_page_14_Picture_0.jpeg)\n\n| RFS REQUESTER $\\vee$<br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                         | <b>BRIDGE ADMINISTRATION</b>               | $\\, +$                                                                                                                        | $\\vee$ Preferences                                                                    | $\\vee$ Administration<br>$\\vee$ Help<br>$\\overline{a}$                                                                                       | $\\bullet$ AA - $\\bullet$ X                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |\n|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Q 嶽<br>1 童<br>≺<br><mark>⋒</mark>                                                                                                                                                                                                                                                                                       |                                            | Company Details   Users (5)   Deal Tracking Groups (1)   Daughter (5)   ITEX   Legal Entities   Legal Entity Groups (2)       |                                                                                       | <b>TAS/TOB Groups</b>                                                                                                                        | $\\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and}\\quad \\text{and$ |\n| $\\lambda \\mathbf{m}$ TradeAsE<br>$\\mathcal{G}$<br>$\\underline{\\widehat{\\mathbf{m}}}$ TradeAsE.ITEX.E1<br>TradeAsE.TAS.E1<br>$m$ TradeAsE.TAS.E2<br>龜<br>TradeAsE.TAS.E3<br>tip<br>TradeAsE.TradeAsE<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD | 自立也                                        | Users (5)<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD | <b>Status</b><br><b>Active</b><br>Active<br><b>Active</b><br>Active<br><b>Deleted</b> | I û<br>$\\overline{\\mathcal{C}}$<br>û<br>$\\sim$ 0<br>û<br>$\\sim$ 0<br>會<br>$\\sqrt{\\phantom{a}}$<br>$\\bigcirc$ $\\varphi$<br><b>Create User</b> |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |\n| $\\frac{1}{2}$<br>$\\bigcirc$<br>$\\bigoplus$<br>TradeAsE.TreasurerB, TradeAsE // INT                                                                                                                                                                                                                                      | Create Change Request<br>TradeAsE $\\times$ | $\\equiv$ $\\equiv$ $\\equiv$ $\\equiv$                                                                                           |                                                                                       | Discard All Changes<br>Thu, 21. Mar 2019, 11:39:32 GMT // Connected [FFM] · INT                                                              | Save                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |\n\n<span id=\"page-14-0\"></span>**Figure 13 Bridge Administration: User Deletion**\n\n- 3. Decide if the user must be removed permanently (deleted) or temporarily (inactive). Both will be removed and cannot login. Deleting a user removes the deal tracking viewership and TAS/TOB rights. Making a user inactive retains these settings. The personal data of deleted and inactive users will be removed after their regulatory retention time has expired.\n  - a. Permanent / Delete: Click on the trash can for the desired user.\n  - b. Temporary / Inactive: Click on the green check mark / toggle .\n- 4. Confirm your action.\n- 5. Click \"Create Change Request\" .\n\n6. Add a comment.\n\n![](_page_15_Picture_0.jpeg)\n\n| Your text here |  |\n|----------------|--|\n|                |  |\n|                |  |\n|                |  |\n\n7. Click OK.\n\nSupervisor user (different user):\n\n1. Open Change Requests using the Change Request quick link.\n\n| 合<br><b>Administration Start</b><br><b>Configurations</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |\n| <b>Institution</b><br><b>Bank Baskets</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |\n| <b>Actions</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |\n| <b>Change Request</b><br>Wizards<br><b>Evaluator Tools</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |  |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |\n| $\\begin{array}{ c c c c c }\\hline \\textbf{0} & \\textbf{0} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\hline \\textbf{1} & \\textbf{0}\\h$<br>1/ TradeAsE.TreasurerA, TradeAsE // INT<br>$\\equiv$<br>Wed, 20. Mar 2019, 10:45:51 GMT // Connected [FFM] @ INT |  |\n\n<span id=\"page-15-0\"></span>**Figure 14 Bridge Administration: Change Request for User Deletion**\n\n- 2. Click Show all . 3. Click Approve .\n- 4. Click OK .\n\n![](_page_16_Picture_0.jpeg)\n\nThe user is now removed and displayed as greyed out (if removed) or greyed out and struck through (if deleted) after refreshing the view. A refresh is done by clicking on\n\nthe refresh icon in the list on the left-hand side: .\n\n# <span id=\"page-16-0\"></span>**4 Change Request Features**\n\nClicking on the change request quick link opens the Change Request page.\n\n![](_page_16_Figure_6.jpeg)\n\n<span id=\"page-16-1\"></span>**Figure 15 Bridge Administration: Change Request Quick Link**\n\nThe page contains search and filtering elements, a table of requested changes and action icons.\n\n![](_page_17_Picture_0.jpeg)\n\n|                                                                                 |                    | <b>RFS REQUESTER</b>                                         | <b>ORDER MANAGEMENT</b>     | $\\, +$<br><b>BRIDGE ADMINISTRATION</b> |                         |                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help | $\\sum^2$ 0 AA - D X                                         |\n|---------------------------------------------------------------------------------|--------------------|--------------------------------------------------------------|-----------------------------|----------------------------------------|-------------------------|----------------------------|------------------------------------------------------|-------------------------------------------------------------|\n|                                                                                 | 合<br>$\\mathcal{G}$ | Select Category                                              | $\\checkmark$                |                                        | Change Requests (2/2)   |                            |                                                      | <b>D</b> Show all                                           |\n|                                                                                 | 龜<br>ú             | Select Company<br>Filter by CR id<br>$\\operatorname{\\sf Id}$ | $\\checkmark$<br>Institution | <b>Description</b>                     | Submitted at            | Submitted by               | <b>Status</b>                                        | $\\triangleq$                                                |\n|                                                                                 |                    | $CR-221$                                                     | TradeAsE                    | New Trade As Group                     | Mar 20, 2019 3:58:18 PM | <b>TradeAsE.TreasurerA</b> | 0/1                                                  | ◙▩◞▮◗▤                                                      |\n|                                                                                 | 見                  | CR-222                                                       | TradeAsE                    | Offboarding entity                     | Mar 20, 2019 3:59:03 PM | TradeAsE.TreasurerA        | 0/1                                                  | $\\circ \\mathbb{E} \\times \\mathbb{E} \\rightarrow \\mathbb{E}$ |\n| $\\frac{\\mathbf{B}}{\\mathbf{B}} \\mid \\mathbf{C} \\mid \\mathbf{D} \\mid \\mathbf{B}$ | $z_{\\rm A}$        |                                                              |                             |                                        |                         |                            |                                                      |                                                             |\n|                                                                                 |                    | TradeAsE.TreasurerA, TradeAsE // INT                         |                             |                                        | $\\Box$                  |                            |                                                      | Wed, 20. Mar 2019, 15:02:23 GMT // Connected [FFM] · INT    |\n\n<span id=\"page-17-2\"></span>**Figure 16 Change Requests: Main Page**\n\nChange requests initiated from various configuration categories and for various companies within a particular setup may be contained in the table.\n\n## <span id=\"page-17-0\"></span>**4.1 Search and filter**\n\n### <span id=\"page-17-1\"></span>**4.1.1 Header**\n\nThe Change Request header indicates the number of Change Requests which are currently visible out of the total contained in the table.\n\n![](_page_18_Picture_0.jpeg)\n\n![](_page_18_Picture_2.jpeg)\n\n#### <span id=\"page-18-0\"></span>**Figure 17 Change Requests: Header**\n\nTo show all Change Requests click the \"Show all\" toggle in the top right corner.\n\n|                                                                                                                                                                                                                                                                                                                                                                                                                    |                    | <b>RFS REQUESTER</b>                 | <b>ORDER MANAGEMENT</b> | $+$<br><b>BRIDGE ADMINISTRATION</b>      |                                                    |                                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\searrow$ ( $\\triangleright$ A $\\wedge$ $\\Box$ X |                                                                                                                 |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|--------------------------------------|-------------------------|------------------------------------------|----------------------------------------------------|--------------------------------------------|--------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------|\n|                                                                                                                                                                                                                                                                                                                                                                                                                    | 合<br>$\\mathcal{G}$ | Select Category                      | $\\checkmark$            |                                          | Change Requests $(2/2)$                            |                                            |                                                                                                        | VO) Show all                                                                                                    |\n|                                                                                                                                                                                                                                                                                                                                                                                                                    | 龜<br>tó            | Select Company<br>Filter by CR id    | $\\checkmark$            |                                          |                                                    | <b>Submitted by</b>                        |                                                                                                        | $\\ominus$                                                                                                       |\n|                                                                                                                                                                                                                                                                                                                                                                                                                    |                    | <b>Id</b>                            | <b>Institution</b>      | <b>Description</b>                       | Submitted at                                       |                                            | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                               |                                                                                                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                    |                    | $CR-221$<br>$CR-222$                 | TradeAsE<br>TradeAsE    | New Trade As Group<br>Offboarding entity | Mar 20, 2019 3:58:18 PM<br>Mar 20, 2019 3:59:03 PM | TradeAsE.TreasurerA<br>TradeAsE.TreasurerA | 0/1<br>0/1                                                                                             | $\\bullet \\mid \\triangleright \\mid \\equiv$<br> ⊗ ⊠ √<br>$\\parallel$ $\\parallel$ $\\parallel$ $\\parallel$<br> ●⊠ √ |\n|                                                                                                                                                                                                                                                                                                                                                                                                                    | 見                  |                                      |                         |                                          |                                                    |                                            |                                                                                                        |                                                                                                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                    | $\\ddot{\\alpha}$    |                                      |                         |                                          |                                                    |                                            |                                                                                                        |                                                                                                                 |\n| $\\begin{tabular}{ c c c c c c } \\hline \\rule{0pt}{3ex} & \\rule{0pt}{3ex} & \\rule{0pt}{3ex} \\\\ \\hline \\rule{0pt}{3ex} & \\rule{0pt}{3ex} & \\rule{0pt}{3ex} \\\\ \\rule{0pt}{3ex} & \\rule{0pt}{3ex} & \\rule{0pt}{3ex} \\\\ \\rule{0pt}{3ex} & \\rule{0pt}{3ex} & \\rule{0pt}{3ex} \\\\ \\rule{0pt}{3ex} & \\rule{0pt}{3ex} & \\rule{0pt}{3ex} \\\\ \\rule{0pt}{3ex} & \\rule{0pt}{3ex} & \\rule{0pt}{3ex} \\\\ \\rule{0pt}{3ex} & \\rule{0$ |                    |                                      |                         |                                          |                                                    |                                            |                                                                                                        |                                                                                                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                    |                    | TradeAsE.TreasurerA, TradeAsE // INT |                         |                                          | $\\equiv$ $\\equiv$ $\\equiv$ $\\equiv$                |                                            | Wed, 20. Mar 2019, 15:02:23 GMT // Connected [FFM] @ INT                                               |                                                                                                                 |\n\n<span id=\"page-18-1\"></span>**Figure 18 Change Requests: Show all toggle**\n\n![](_page_19_Picture_0.jpeg)\n\n### <span id=\"page-19-0\"></span>**4.1.2 Select Category**\n\nThe Select Category drop down list allows a user to view Change Requests from specific configurations.\n\n|                                                                                 |                          | <b>RFS REQUESTER</b>                     | <b>ORDER MANAGEMENT</b> | $+$<br><b>BRIDGE ADMINISTRATION</b> |                            |                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help<br>$\\mathbb{R}^2$ | $\\bullet$ AA - $\\bullet$ X                             |\n|---------------------------------------------------------------------------------|--------------------------|------------------------------------------|-------------------------|-------------------------------------|----------------------------|----------------------------|------------------------------------------------------------------------|--------------------------------------------------------|\n|                                                                                 | 合<br>$\\mathcal{G}$       | <b>Select Category</b>                   | $\\checkmark$            |                                     | Change Requests (2/2)      |                            |                                                                        | <b>VO</b> Show all                                     |\n|                                                                                 | 马<br>to                  | <b>Select Company</b><br>Filter by CR id | $\\checkmark$            |                                     |                            |                            |                                                                        | $\\triangleq$                                           |\n|                                                                                 |                          | <b>Id</b>                                | Institution             | <b>Description</b>                  | Submitted at               | Submitted by               | <b>Status</b>                                                          |                                                        |\n|                                                                                 |                          | $CR-221$                                 | TradeAsE                | New Trade As Group                  | Mar 20, 2019 3:58:18 PM    | <b>TradeAsE.TreasurerA</b> | 0/1                                                                    | ● ⊠   √  <br>$\\bullet \\mid \\triangleright \\mid \\equiv$ |\n|                                                                                 | $\\bigoplus$              | CR-222                                   | TradeAsE                | Offboarding entity                  | Mar 20, 2019 3:59:03 PM    | TradeAsE.TreasurerA        | 0/1                                                                    | $\\circledast$                                          |\n| $\\frac{\\mathbf{B}}{\\mathbf{B}} \\mid \\mathbf{C} \\mid \\mathbf{D} \\mid \\mathbf{B}$ | $\\overline{\\mathcal{L}}$ |                                          |                         |                                     |                            |                            |                                                                        |                                                        |\n|                                                                                 |                          | TradeAsE.TreasurerA, TradeAsE // INT     |                         |                                     | $\\equiv$ $\\equiv$ $\\equiv$ |                            | Wed, 20. Mar 2019, 15:02:23 GMT // Connected [FFM] ·                   | INT                                                    |\n|                                                                                 |                          |                                          |                         |                                     |                            |                            |                                                                        |                                                        |\n\n### <span id=\"page-19-1\"></span>**Figure 19 Change Requests: Select Category**\n\nThe available categories are determined by a user's individual administrative rights.\n\n![](_page_19_Picture_7.jpeg)\n\n<span id=\"page-19-2\"></span>**Figure 20 Change Requests: Select Category list**\n\n![](_page_20_Picture_0.jpeg)\n\nTo show change requests from a specific category, select the category and click Apply.\n\n| <b>Select Category</b>             | $\\checkmark$ |\n|------------------------------------|--------------|\n|                                    |              |\n| Institutions<br><b>BankBaskets</b> |              |\n|                                    |              |\n|                                    |              |\n|                                    |              |\n|                                    |              |\n|                                    |              |\n| <b>Select All</b>                  | Apply        |\n\n<span id=\"page-20-0\"></span>**Figure 21 Change Requests: Apply Category**\n\nApplied configuration categories will appear next to the drop down list. Individual categories can be removed by clicking X.\n\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\ $       | $\\mathbb{D}^2$ O AA - $\\Box$ X                                                                        |\n|---------------------------------------------------------------------------------|-------------------------|--------------------------------------|----------------------------------------|-------------------------------------|-------------------------|---------------------|-----------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|\n|                                                                                 |                         | <b>RFS REQUESTER</b>                 | <b>ORDER MANAGEMENT</b>                | $+$<br><b>BRIDGE ADMINISTRATION</b> |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 | <mark>⋒</mark>          |                                      |                                        |                                     |                         |                     |                                                                 | V Show all                                                                                            |\n|                                                                                 |                         |                                      |                                        |                                     | Change Requests (2/2)   |                     |                                                                 |                                                                                                       |\n|                                                                                 | $\\mathcal{G}$           | <b>Select Category</b>               | Institutions $\\times$<br>$\\smallsmile$ |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         | Select Company                       | $\\checkmark$                           |                                     |                         |                     |                                                                 | $\\mathord{\\ominus}$                                                                                   |\n|                                                                                 | 马                       | Filter by CR id                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 | $\\frac{1}{2}$           | Id.                                  | Institution                            | <b>Description</b>                  | Submitted at            | Submitted by        | <b>Status</b>                                                   |                                                                                                       |\n|                                                                                 |                         | $CR-221$                             | TradeAsE                               | New Trade As Group                  | Mar 20, 2019 3:58:18 PM | TradeAsE.TreasurerA | 0/1                                                             | $\\circ \\mathbb{E}\\left[\\mathbb{E}\\right] \\cup \\mathbb{E}\\left[\\mathbb{E}\\right] \\subseteq \\mathbb{E}$ |\n|                                                                                 |                         | $CR-222$                             | TradeAsE                               | Offboarding entity                  | Mar 20, 2019 3:59:03 PM | TradeAsE.TreasurerA | 0/1                                                             | $\\circ \\mathbb{E} \\setminus \\mathbb{E} \\triangleright \\mathbb{E}$                                     |\n|                                                                                 | 厚                       |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 | $\\overline{\\mathbf{z}}$ |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n| $\\frac{\\mathbf{B}}{\\mathbf{B}} \\mid \\mathbf{C} \\mid \\mathbf{D} \\mid \\mathbf{B}$ |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n|                                                                                 |                         | TradeAsE.TreasurerA, TradeAsE // INT |                                        |                                     | asch                    |                     | Wed, 20. Mar 2019, 15:43:33 GMT // Connected [FFM] <sup>.</sup> |                                                                                                       |\n|                                                                                 |                         |                                      |                                        |                                     |                         |                     |                                                                 |                                                                                                       |\n\n<span id=\"page-20-1\"></span>**Figure 22 Change Requests: Applied Category**\n\n![](_page_21_Picture_0.jpeg)\n\n### <span id=\"page-21-0\"></span>**4.1.3 Select Company**\n\nA change may be requested for the main entity in a setup or any related entity (tradeas, trade-on-behalf, ITEX or other administrated entity). The Select Company drop down list allows a user to view Change Requests from specific companies.\n\n|                                                                                                |                          | <b>RFS REQUESTER</b>                 | <b>ORDER MANAGEMENT</b> | $+$<br><b>BRIDGE ADMINISTRATION</b> |                         |                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help     | $\\mathbb{D}^2$ O AA - $\\mathbb{D} \\times$                         |\n|------------------------------------------------------------------------------------------------|--------------------------|--------------------------------------|-------------------------|-------------------------------------|-------------------------|----------------------------|----------------------------------------------------------|-------------------------------------------------------------------|\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                | <mark>⋒</mark>           |                                      |                         |                                     | Change Requests (2/2)   |                            |                                                          | <b>VO</b> Show all                                                |\n|                                                                                                | $\\mathcal{G}$            | Select Category                      | $\\checkmark$            |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                | 马                        | Select Company                       | $\\checkmark$            |                                     |                         |                            |                                                          | $\\ominus$                                                         |\n|                                                                                                | ú                        | Filter by CR id<br><b>Id</b>         | <b>Institution</b>      | <b>Description</b>                  | <b>Submitted at</b>     | Submitted by               | <b>Status</b>                                            |                                                                   |\n|                                                                                                |                          | $CR-221$                             | TradeAsE                | New Trade As Group                  | Mar 20, 2019 3:58:18 PM | TradeAsE.TreasurerA        | 0/1                                                      | ◙▩◞▮◗▤                                                            |\n|                                                                                                |                          | CR-222                               | TradeAsE                | Offboarding entity                  | Mar 20, 2019 3:59:03 PM | <b>TradeAsE.TreasurerA</b> | 0/1                                                      | $\\circ \\mathbb{E} \\setminus \\mathbb{E} \\triangleright \\mathbb{E}$ |\n|                                                                                                | $\\overline{\\nabla}$      |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                | $\\overline{\\mathcal{R}}$ |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n| $\\frac{\\partial}{\\partial t} \\mid \\mathbf{C} \\mid \\mathbf{C} \\mid \\frac{\\partial}{\\partial t}$ |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          |                                      |                         |                                     |                         |                            |                                                          |                                                                   |\n|                                                                                                |                          | TradeAsE.TreasurerA, TradeAsE // INT |                         |                                     | $\\blacksquare$          |                            | Wed, 20. Mar 2019, 15:02:23 GMT // Connected [FFM] @ INT |                                                                   |\n\n#### <span id=\"page-21-1\"></span>**Figure 23 Change Requests: Select Company**\n\nThe available companies are determined by the entity relationship structure.\n\n![](_page_21_Figure_7.jpeg)\n\n#### <span id=\"page-21-2\"></span>**Figure 24 Change Requests: Select Company**\n\n![](_page_22_Picture_0.jpeg)\n\nTo show Change Requests from a specific company, select the company and click Apply.\n\n![](_page_22_Picture_3.jpeg)\n\n#### <span id=\"page-22-0\"></span>**Figure 25 Change Requests: Apply Company**\n\nApplied company selections will appear next to the drop-down list. Individual companies can be removed by clicking X.\n\n|                                                                                 | <b>RFS REQUESTER</b>                               | <b>ORDER MANAGEMENT</b>                                 | $\\, +$<br><b>BRIDGE ADMINISTRATION</b>   |                                                    |                                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help | $\\mathbb{D}^2$ O AA - $\\mathbb{D}$ X                                                                                                |\n|---------------------------------------------------------------------------------|----------------------------------------------------|---------------------------------------------------------|------------------------------------------|----------------------------------------------------|--------------------------------------------|------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------|\n| 合                                                                               | $\\mathcal{G}$<br>Select Category                   | Institutions $\\times$<br>$\\checkmark$                   |                                          | Change Requests (2/2)                              |                                            |                                                      | V Show all                                                                                                                          |\n|                                                                                 | Select Company<br>马<br>Filter by CR id<br>孟<br>Id. | TradeAsE $\\times$<br>$\\checkmark$<br><b>Institution</b> | <b>Description</b>                       | Submitted at                                       | Submitted by                               | <b>Status</b>                                        | $\\triangleq$                                                                                                                        |\n|                                                                                 | <b>CR-221</b><br>$CR-222$                          | TradeAsE<br>TradeAsE                                    | New Trade As Group<br>Offboarding entity | Mar 20, 2019 3:58:18 PM<br>Mar 20, 2019 3:59:03 PM | TradeAsE.TreasurerA<br>TradeAsE.TreasurerA | 0/1<br>0/1                                           | $\\textcircled{\\texttt{R}} \\lor \\textcircled{\\texttt{i}} \\lor \\equiv$<br>$\\circ \\mathbb{E} \\times \\mathbb{E} \\rightarrow \\mathbb{E}$ |\n| $\\overline{\\mathbb{P}}$                                                         | $\\overline{\\mathcal{R}}$                           |                                                         |                                          |                                                    |                                            |                                                      |                                                                                                                                     |\n| $\\frac{\\mathbf{B}}{\\mathbf{B}} \\mid \\mathbf{C} \\mid \\mathbf{D} \\mid \\mathbf{B}$ | TradeAsE.TreasurerA, TradeAsE // INT               |                                                         |                                          | $\\blacksquare$ $\\blacksquare$ $\\blacksquare$       |                                            |                                                      | Wed, 20. Mar 2019, 15:55:45 GMT // Connected [FFM] @ INT                                                                            |\n\n<span id=\"page-22-1\"></span>**Figure 26 Change Requests: Applied Company**\n\n![](_page_23_Picture_0.jpeg)\n\n## <span id=\"page-23-0\"></span>**4.1.4 Filter by CR id**\n\nEach created Change Request has a unique ID number.\n\nThe Filter by CR id field allows a user to filter for a specific Change Request number using a free text search. The search is not case sensitive.\n\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help     | $D^2$ O AA - D X                                           |\n|---------------------------------------------------------------------------------|--------------|-----------------------------------------|-------------------------|-----------------------|----------------------------------------|-------------------------|---------------------|----------------------------------------------------------|------------------------------------------------------------|\n|                                                                                 |              | <b>RFS REQUESTER</b>                    | <b>ORDER MANAGEMENT</b> |                       | $\\, +$<br><b>BRIDGE ADMINISTRATION</b> |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          | V Show all                                                 |\n| 合                                                                               |              |                                         |                         |                       |                                        | Change Requests (2/2)   |                     |                                                          |                                                            |\n| $\\mathcal{G}$                                                                   |              | Select Category                         | $\\checkmark$            | Institutions $\\times$ |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 | 马            | Select Company                          | $\\checkmark$            | TradeAsE $\\times$     |                                        |                         |                     |                                                          | $\\ominus$                                                  |\n|                                                                                 | ₩            | Filter by CR id                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              | <b>Id</b>                               | Institution             |                       | <b>Description</b>                     | Submitted at            | Submitted by        | <b>Status</b>                                            |                                                            |\n|                                                                                 |              | CR-221                                  | TradeAsE                |                       | New Trade As Group                     | Mar 20, 2019 3:58:18 PM | TradeAsE.TreasurerA | 0/1                                                      | $\\circ$ 3 $\\vee$ 6 $\\triangleright$ 5                      |\n| 厚                                                                               |              | $CR-222$                                | TradeAsE                |                       | Offboarding entity                     | Mar 20, 2019 3:59:03 PM | TradeAsE.TreasurerA | 0/1                                                      | $\\circ \\mathbb{E} \\setminus \\mathbf{t} \\models \\mathbb{E}$ |\n|                                                                                 | $\\mathbb{R}$ |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n| $\\frac{\\mathbf{B}}{\\mathbf{B}} \\mid \\mathbf{C} \\mid \\mathbf{D} \\mid \\mathbf{B}$ |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              |                                         |                         |                       |                                        |                         |                     |                                                          |                                                            |\n|                                                                                 |              | 1/ TradeAsE.TreasurerA, TradeAsE // INT |                         |                       |                                        | $\\Xi$ $\\Xi$ $\\Xi$       |                     | Wed, 20. Mar 2019, 15:57:32 GMT // Connected [FFM] @ INT |                                                            |\n\n<span id=\"page-23-1\"></span>**Figure 27 Change Requests: Filter by CR id**\n\nTo find a specific Change Request, enter the number.\n\n![](_page_24_Picture_0.jpeg)\n\n|                                                                                 | <b>RFS REQUESTER</b>                   | <b>ORDER MANAGEMENT</b>                                 | $\\, +$<br><b>BRIDGE ADMINISTRATION</b> |                         |                     | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\vert$ | $0$ AA $-$ O $\\times$<br>$\\mathbb{D}^2$                                                                 |\n|---------------------------------------------------------------------------------|----------------------------------------|---------------------------------------------------------|----------------------------------------|-------------------------|---------------------|--------------------------------------------------------------|---------------------------------------------------------------------------------------------------------|\n| 合<br>$\\mathcal{G}$                                                              | Select Category                        | Institutions $\\times$<br>$\\vee$                         |                                        | Change Requests (2/2)   |                     |                                                              | V Show all                                                                                              |\n| <b>To</b><br>ú                                                                  | Select Company<br>$cr-222$<br>aq.      | TradeAsE $\\times$<br>$\\checkmark$<br><b>Institution</b> | <b>Description</b>                     | Submitted at            | Submitted by        | <b>Status</b>                                                | $\\triangle$                                                                                             |\n| 見                                                                               | $CR-222$                               | TradeAsE                                                | Offboarding entity                     | Mar 20, 2019 3:59:03 PM | TradeAsE.TreasurerA | 0/1                                                          | $\\circledast \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ }$ |\n| $z\\!\\!\\!\\!\\backslash$                                                           |                                        |                                                         |                                        |                         |                     |                                                              |                                                                                                         |\n|                                                                                 |                                        |                                                         |                                        |                         |                     |                                                              |                                                                                                         |\n|                                                                                 |                                        |                                                         |                                        |                         |                     |                                                              |                                                                                                         |\n|                                                                                 |                                        |                                                         |                                        |                         |                     |                                                              |                                                                                                         |\n|                                                                                 |                                        |                                                         |                                        |                         |                     |                                                              |                                                                                                         |\n|                                                                                 |                                        |                                                         |                                        |                         |                     |                                                              |                                                                                                         |\n| $\\frac{\\mathbf{B}}{\\mathbf{B}} \\mid \\mathbf{C} \\mid \\mathbf{D} \\mid \\mathbf{B}$ |                                        |                                                         |                                        |                         |                     |                                                              |                                                                                                         |\n|                                                                                 | 1 TradeAsE.TreasurerA, TradeAsE // INT |                                                         |                                        | <b>BECT</b>             |                     |                                                              | Wed, 20. Mar 2019, 16:00:53 GMT // Connected [FFM] . INT                                                |\n\n<span id=\"page-24-2\"></span>**Figure 28 Change Requests: Applied filter by CR id**\n\n### <span id=\"page-24-0\"></span>**4.2 Column Headers**\n\nThe Change Request (CR) page contains a table of requested changes with the following column headers:\n\n- Id: Unique id number which is generated for every individual CR.\n- Institution: Entity where the change has been requested.\n- Description: Comment entered when creating the CR.\n- Submitted at: Date and time CR was first created.\n- Submitted by: User that submitted the CR.\n- Status: Pending, Rejected, Applied or Warning; (number of approvals given / required)\n\n### <span id=\"page-24-1\"></span>**4.3 Status**\n\nA Change Request (CR) requires action to be taken on it to be \"resolved\". Resolving a CR may result in the desired change being Approved and Applied or Rejected.\n\n![](_page_25_Picture_0.jpeg)\n\n### <span id=\"page-25-0\"></span>**4.3.1 Number of approvals**\n\nA created Change Request must be approved to move forward in the workflow. The number of approvals required for a CR is configurable on an entity-level.\n\n### **Please note:**\n\n<NAME_EMAIL> or your customer relationship manager in order to set up the desired number of approvals.\n\nThe given and required number of approvals is indicated in the Status column. The first number indicates how many approvals have been given to the particular CR. The second number indicates how many approvals are required.\n\nFor example, a CR with 0 / 1 requires one approval to move forward, but has none. A Change Request with 1 / 1 has all of the required approvals needed to move forward in a workflow.\n\n### <span id=\"page-25-1\"></span>**4.3.2 Status Types**\n\nA Change Request may be in various states. To view the status, hover the mouse over the circle in the Status column. A tool tip will indicate the current Status.\n\nThe following status types are possible:\n\n**Pending**: The CR has been initiated but is pending approval by a client-admin or must still be applied by 360T.\n\n**Rejected**: The CR has been rejected and cannot be applied.\n\n**Applied**: The CR has been approved and applied. This CR is in a final state and can be \"acknowledged\" to remove it from the Change Request table.\n\n**Warning**: The CR was created, but other changes have been made after the CR was created. These changes affect this particular Change Request. Review before approving or applying the request in order to ensure the desired setup.\n\n## <span id=\"page-25-2\"></span>**4.4 Action Icons**\n\nA set of icons is located on the right-side of the Change Requests table.\n\n![](_page_26_Picture_0.jpeg)\n\n|                                                                                                | <b>RFS REQUESTER</b>                   | <b>ORDER MANAGEMENT</b>           | $^{+}$<br><b>BRIDGE ADMINISTRATION</b> |                         |                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help | $\\bullet$ AA - $\\bullet$ X<br>$\\mathbb{R}^3$                     |\n|------------------------------------------------------------------------------------------------|----------------------------------------|-----------------------------------|----------------------------------------|-------------------------|----------------------------|------------------------------------------------------|------------------------------------------------------------------|\n| 合<br>$\\mathcal{G}$                                                                             | Select Category                        | Institutions $\\times$<br>$\\vee$   |                                        | Change Requests (3/3)   |                            |                                                      | V Show all                                                       |\n| Refresh<br>马<br>₩                                                                              | Select Company<br>Filter by CR id      | TradeAsE $\\times$<br>$\\checkmark$ |                                        |                         |                            |                                                      | $\\triangleleft$                                                  |\n|                                                                                                | Id.                                    | <b>Institution</b>                | <b>Description</b>                     | Submitted at            | Submitted by               | <b>Status</b>                                        |                                                                  |\n|                                                                                                | $CR-221$                               | TradeAsE                          | New Trade As Group                     | Mar 20, 2019 3:58:18 PM | <b>TradeAsE TreasurerA</b> | 0/1                                                  | L© ⊠ I<br>$\\begin{array}{c c c c c c c c c c c c c c c c c c c $ |\n|                                                                                                | CR-222                                 | TradeAsE                          | Offboarding entity                     | Mar 20, 2019 3:59:03 PM | TradeAsE TreasurerA        | 0/1                                                  | $\\circ$ $\\circ$<br>Ξ                                             |\n| 見                                                                                              | $CR-223$                               | <b>TradeAsE</b>                   | New user                               | Mar 20, 2019 5:40:52 PM | TradeAsE.TreasurerB        | 0/1                                                  | ◎ ⊠ √ X ▷ ∃                                                      |\n| $z_{\\rm c}$<br>$\\frac{\\mathbf{B}}{\\mathbf{B}} \\mid \\mathbf{C} \\mid \\mathbf{D} \\mid \\mathbf{G}$ | 1 TradeAsE.TreasurerA. TradeAsE // INT |                                   |                                        | <b>Beck</b>             |                            |                                                      | Wed. 20. Mar 2019. 16:41:16 GMT // Connected [FFM] @ INT         |\n\n<span id=\"page-26-0\"></span>**Figure 29 Change Requests: Action icons**\n\n- **Show Changes** : Opens a tab within the specific configuration category that shows you the changes contained in this particular Change Request (CR). Use the Live Audit Log to review the contained changes.\n- **Acknowledge** : Is only active for CRs in a \"final\" status. This feature removes the CR from the Change Request table. Acknowledging a CR may be done when it no longer needs to be reviewed.\n- **Approve** : Approving a CR moves it forward in the workflow. A user may not approve his/her own CR.\n- **Delete** : A user may delete his/her own CR. It will not be possible to approve the CR after it has been deleted.\n- **Reject** : A user may reject the CR of another user. It will not be possible to approve the CR after it has been deleted.\n- **Apply** : Applying a CR saves the change immediately without the required approvals. This action may only be possible for certain types of requested changes.\n- **Show Summary** : Opens a pop-up wich contains the CR-Number, Status, Number of approvals given and required, user information for creation and approval of the CR, time and date the CR was submitted and contains the comment.\n\n![](_page_27_Picture_0.jpeg)\n\n| Change Request Summary: CR-223                             |  |\n|------------------------------------------------------------|--|\n| $\\bullet$ Pending (0/1)                                    |  |\n| Submitted by TradeAsE.TreasurerB (Mar 20, 2019 5:40:52 PM) |  |\n| 66 New user                                                |  |\n|                                                            |  |\n|                                                            |  |\n|                                                            |  |\n\n<span id=\"page-27-1\"></span>**Figure 30 Change Requests: Show Summary pop-up**\n\n# <span id=\"page-27-0\"></span>**5 Appendix I**\n\nThis appendix contains all visible fields, possible changes and actions that can be made in the Bridge Admin – Institution Configuration for a TEX, Trade-As (TAS) or Trade-on-behalf (TOB) entity.\n\n**Please note:** For changes to ITEX entities please see the separate user guide.\n\nRequesting a change to a field will trigger a Change Request (CR). Depending on the field, various CR workflows are possible. Changes to some fields are not possible. Some actions can be taken which do not change data. In this case a CR is not triggered. The action can be taken immediately, without approval.\n\nColumn \"TEX/TAS/TOB Entity\" indicates the type of workflow applied to the requested change.\n\n- **Read-only**: No change possible.\n- **Internal CR**: Request > Approval by client admin > Complete.\n- **360T CR:** Request > Approval by client admin > Request sent to 360T to be completed.\n- **No CR required**: Action can be taken without any approval.\n\n![](_page_28_Picture_0.jpeg)\n\n| Tab                                        | Field                                      | TEX/TAS/TOB<br>Entity |\n|--------------------------------------------|--------------------------------------------|-----------------------|\n| Company Details Tab – Overview             | Company Name                               | Read-only             |\n| Company Details Tab – Overview             | Description                                | Read-only             |\n| Company Details Tab – Overview             | Phone Number                               | Internal CR           |\n| Company Details Tab – Overview             | Fax Number                                 | Internal CR           |\n| Company Details Tab – Overview             | Country                                    | Read-only             |\n| Company Details Tab – Overview             | Currency                                   | Read-only             |\n| Company Details Tab – Overview             | LEI                                        | Read-only             |\n| Company Details Tab – Overview             | Status                                     | Read-only             |\n| Company Details Tab – Overview             | Roles: Requester/Provider/Prime Broker/HFT | Read-only             |\n| Company Details Tab – Prefix               | Create/Delete prefix                       | 360T CR               |\n| Company Details Tab – Internal trades only | Create/Delete default email                | 360T CR               |\n| Company Details Tab – Internal trades only | Create/Delete default phone number         | 360T CR               |\n| Users Tab                                  | Create User                                | 360T CR               |\n| Users Tab                                  | Enable/Disable TEX User                    | 360T CR               |\n| Users Tab                                  | Delete TEX User                            | Internal CR           |\n| Users Tab                                  | Download Users                             | No CR required        |\n| Deal Tracking Groups Tab                   | Create/Rename/Delete Group                 | Internal CR           |\n| Deal Tracking Groups Tab                   | Add/Remove Members                         | Internal CR           |\n| Deal Tracking Groups Tab                   | Add/Remove Viewers                         | Internal CR           |\n| Daughters Tab                              | Download Institutions                      | No CR required        |\n| ITEX Tab                                   | Add/Remove Institutions                    | 360T CR               |\n| Legal Entities Tab                         | Add/Remove Institutions                    | 360T CR               |\n| Legal Entity Groups Tab                    | Create/Rename/Delete Group                 | Internal CR           |\n| Legal Entity Groups Tab                    | Add/Remove Institutions                    | Internal CR           |\n| TAS/TOB Groups                             | Add/Remove TAS Users                       | Internal CR           |\n| TAS/TOB Groups                             | Add/Remove TOB Users                       | Internal CR           |\n| Individual Deals Tab                       | Login Name                                 | read only             |\n| Individual Deals Tab                       | Last Name                                  | 360T CR               |\n| Individual Deals Tab                       | First Name                                 | Internal CR           |\n| Individual Deals Tab                       | Description                                | Internal CR           |\n| Individual Deals Tab                       | Email                                      | 360T CR               |\n| Individual Deals Tab                       | Phone Number                               | 360T CR               |\n| Individual Deals Tab                       | Fax Number                                 | Internal CR           |\n| Individual Deals Tab                       | Salutation                                 | Internal CR           |\n| Individual Deals Tab                       | Position                                   | 360T CR               |\n| Individual Deals Tab                       | Country                                    | Internal CR           |\n| Individual Deals Tab                       | Show user to Provider (toggle)             | Internal CR           |\n| User Deal Tracking Tab                     | Add/Remove Members                         | Internal CR           |\n| User Deal Tracking Tab                     | Add/Remove Viewers                         | Internal CR           |\n| User TAS/TOB Groups Tab                    | Trading Type                               | Internal CR           |\n| User TAS/TOB Groups Tab                    | Add/Remove TAS/TOB Group                   | Internal CR           |\n\n![](_page_29_Picture_0.jpeg)\n\n# <span id=\"page-29-0\"></span>**6 Contacting 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\nGermany 360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Asia Pacific South Asia**\n\nSingapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756\n\n#### **Middle East**\n\nUnited Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\n#### USA\n\n360 Trading Networks, Inc 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902\n\nIndia ThreeSixty Trading Networks (India) Pvt Ltd Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437", "metadata": {"lang": "en"}}]