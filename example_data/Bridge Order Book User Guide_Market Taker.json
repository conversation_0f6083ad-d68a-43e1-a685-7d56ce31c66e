[{"id": "1", "text": "![](_page_0_Picture_0.jpeg)\n\nUSER GUIDE BRIDGE ORDER BOOK\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\n**User Guide 360T Bridge: Order Book for Market Taker**\n\nRelease 4.18 (July 2023)\n\n© 360 Treasury Systems AG, 2023 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG\n\n![](_page_1_Picture_0.jpeg)\n\n#### CONTENTS\n\n| 1 | Introduction                                                                                                                                                                                                                                                                                                                                                  |                                   |                                                                      |\n|---|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------|----------------------------------------------------------------------|\n| 2 | Getting Started                                                                                                                                                                                                                                                                                                                                               | 7                                 |                                                                      |\n| 3 | Order Book Settings                                                                                                                                                                                                                                                                                                                                           |                                   | 9                                                                    |\n| 4 | Order Book: Workflow and Status                                                                                                                                                                                                                                                                                                                               |                                   | 12                                                                   |\n|   | 4.1<br>Create Order                                                                                                                                                                                                                                                                                                                                           | 13                                |                                                                      |\n|   | 4.1.1<br>Common order creation attributes<br>4.1.2<br>Market Order<br>4.1.3<br>Limit Order<br>4.1.4<br>Stop Order<br>4.1.5<br>Fixing Order<br>4.1.6<br>OCO Order ('one-cancels-the-other')<br>4.1.7<br>If-Done Order<br>4.1.8<br>Loop order<br>4.1.9<br>Algorithmic (Algo) order<br>4.1.10<br>Slice order<br>4.1.11<br>Call order<br>4.2<br>Initialized Order |                                   | 15<br>20<br>21<br>23<br>24<br>25<br>27<br>28<br>30<br>31<br>35<br>36 |\n|   | 4.3<br>Active/Delivered Order (Sent tab)                                                                                                                                                                                                                                                                                                                      |                                   | 40                                                                   |\n|   | 4.4<br>Accepted orders                                                                                                                                                                                                                                                                                                                                        | 41                                |                                                                      |\n|   | 4.5<br>Rejected orders                                                                                                                                                                                                                                                                                                                                        | 42                                |                                                                      |\n|   | 4.6<br>Withdrawn orders                                                                                                                                                                                                                                                                                                                                       |                                   | 43                                                                   |\n|   | 4.7<br>Expired orders                                                                                                                                                                                                                                                                                                                                         |                                   | 44                                                                   |\n|   | 4.8<br>Executed / Done orders                                                                                                                                                                                                                                                                                                                                 |                                   | 45                                                                   |\n|   | 4.9<br>Special Slice Order Workflow                                                                                                                                                                                                                                                                                                                           | 46                                |                                                                      |\n| 5 | Grouping Orders                                                                                                                                                                                                                                                                                                                                               |                                   | 51                                                                   |\n|   | 5.1<br>Orders of varying custom field values                                                                                                                                                                                                                                                                                                                  |                                   | 53                                                                   |\n|   | 5.2<br>Orders of varying expiry types                                                                                                                                                                                                                                                                                                                         |                                   | 53                                                                   |\n|   | 5.3<br>Grouping of orders of varying bank baskets                                                                                                                                                                                                                                                                                                             |                                   | 53                                                                   |\n| 6 | Uploading Orders                                                                                                                                                                                                                                                                                                                                              |                                   | 54                                                                   |\n|   | 6.1<br>CSV file upload                                                                                                                                                                                                                                                                                                                                        |                                   | 54                                                                   |\n|   | 6.1.1<br>Import From Clipboard<br>6.1.2<br>Import from file<br>6.1.3<br>CSV file versioning<br>6.1.4<br>6.1.5<br>Bank Basket upload                                                                                                                                                                                                                           | Uploading an abbreviated csv file | 54<br>59<br>60<br>61<br>62                                           |\n\n![](_page_2_Picture_0.jpeg)\n\n| 6.1.6<br>6.1.7<br>6.2<br>XML Upload |                                                 | Upload of a grouped order<br>Relationship change between requester and provider | 63<br>64<br>64 |\n|-------------------------------------|-------------------------------------------------|---------------------------------------------------------------------------------|----------------|\n|                                     | 6.3<br>FIX Upload                               |                                                                                 |                |\n| 7                                   | Order History                                   |                                                                                 |                |\n| 8                                   | Special User Configuration: Four-Eyes Principle |                                                                                 | 67             |\n| 9                                   | Order Backup file                               |                                                                                 | 68             |\n| 10                                  | Contacting 360T                                 |                                                                                 | 69             |\n\n![](_page_3_Picture_0.jpeg)\n\n| FIGURES                                                      |    |\n|--------------------------------------------------------------|----|\n| Figure 1 Add Order Book                                      | 7  |\n| Figure 2 Add Preconfigured Order Management View             | 8  |\n| Figure 3 Order Management Areas and Tabs                     | 9  |\n| Figure 4 Order Book: Feature Settings                        | 9  |\n| Figure 5 Order Book Settings: Setup Orders Screen            | 10 |\n| Figure 6 Order Book Settings: Tabs Visibility Settings       | 10 |\n| Figure 7 Order Book Settings: Column Visibility Settings     | 11 |\n| Figure 8 Order selection in Bridge Order Book                | 13 |\n| Figure 9 Order selection in Bridge RFS Live Pricing          | 14 |\n| Figure 10 Order Creation: Changing Order Type                | 14 |\n| Figure 11 Order Creation: Definition Window                  | 15 |\n| Figure 12 Order Creation: Forward Limit Order                | 16 |\n| Figure 13 Order Creation: Expiry Date of Forward Limit Order | 17 |\n| Figure 14 Order Creation: Provider List                      | 18 |\n| Figure 15 Order Creation: Comments tab                       | 19 |\n| Figure 16 Market Order                                       | 21 |\n| Figure 17 Limit Order                                        | 22 |\n| Figure 18 Limit Order placement to Hypersonic Orderbook      | 23 |\n| Figure 19 Stop Order                                         | 24 |\n| Figure 20 Fixing Order                                       | 25 |\n| Figure 21 OCO Order                                          | 26 |\n| Figure 22 If-Done Order with OCO Order leg                   | 27 |\n| Figure 23 OCO Order as If-Done Order leg.                    | 28 |\n| Figure 24 Loop order                                         | 29 |\n| Figure 25 Algo Order                                         | 30 |\n| Figure 26 Slice Order                                        | 31 |\n| Figure 27 Slice Order LPs per Request validation             | 33 |\n| Figure 28 Slice Order Gap between Requests validation        | 34 |\n| Figure 29 Call Order                                         | 35 |\n| Figure 30 Initialized Tab: Actions area                      | 36 |\n| Figure 31 Initialized Tab: Order View                        | 37 |\n| Figure 32 Multi Selection                                    | 38 |\n| Figure 33 Order Amendment                                    | 39 |\n| Figure 34 Initialized Tab: Order and Request Changes View    | 39 |\n\n![](_page_4_Picture_0.jpeg)\n\n| Figure 35 Sent tab: Actions Area                                               | 40 |\n|--------------------------------------------------------------------------------|----|\n| Figure 36 Sent Tab: Withdraw Order                                             | 40 |\n| Figure 37 Accepted Order                                                       | 41 |\n| Figure 38 Partial Execution of a Large Market Order                            | 42 |\n| Figure 39 Pending Order Rejection                                              | 42 |\n| Figure 40 Withdrawal of delivered order                                        | 43 |\n| Figure 41 Withdrawn Accepted Order                                             | 44 |\n| Figure 42 Expired Orders                                                       | 45 |\n| Figure 43 Executed Order                                                       | 46 |\n| Figure 44 Slice Order creation                                                 | 47 |\n| Figure 45 Slice Order execution -<br>Order View                                | 47 |\n| Figure 46 Slice Order Execution Actions                                        | 48 |\n| Figure 47 Executed Slice Order                                                 | 49 |\n| Figure 48 Slice Order warning ticket                                           | 49 |\n| Figure 49 Slice Order warning message                                          | 50 |\n| Figure 50 Slice Order actions upon failure                                     | 51 |\n| Figure 51 Grouping of<br>orders                                                | 52 |\n| Figure 52 Sent grouped order as RFS (block TOB request)                        | 53 |\n| Figure 53 Validation of orders                                                 | 59 |\n| Figure 54: CSV-file with full header                                           | 60 |\n| Figure 55 Short header for csv-file                                            | 60 |\n| Figure 56 Exemplary CSV file                                                   | 61 |\n| Figure 57 Pre-selection of Providers in the RFS Production Definition window   | 62 |\n| Figure 58 Pre-selection of Providers in the Order Production Definition window | 63 |\n| Figure 59 Matching Group ID                                                    | 63 |\n| Figure 60 Aggregated orders                                                    | 64 |\n| Figure 61 Deal Confirmation: Order History                                     | 65 |\n| Figure 62 Order/Request Changes in Order View Window.                          | 66 |\n| Figure 63 Initialized orders grouped by Requester Individual.                  | 67 |\n\n![](_page_5_Picture_0.jpeg)\n\n#### TABLES\n\n| Table 1 Steps in Order Lifecycle and Order Status |    |\n|---------------------------------------------------|----|\n| Table 2: Supported Trading Workflow               | 20 |\n| Table 3: Aggregation criteria                     | 52 |\n| Table 4 Fields for the Order CSV Upload           | 58 |\n| Table 5: Matching delimiter                       | 60 |\n\n![](_page_6_Picture_0.jpeg)\n\nUser Guide 360T Bridge Order Book\n\n# <span id=\"page-6-0\"></span>**1 Introduction**\n\nThis user manual describes the Order Book feature of 360T Bridge. For more information on other Bridge features and the basic principles of the user interface, please refer to 360T User Guide RFS Market Taker Bridge.\n\n# <span id=\"page-6-1\"></span>**2 Getting Started**\n\nThe Order Book feature can be configured in Bridge by adding a new view. The user can either select and apply the Order Book feature directly to a new view or choose the \"Order Management\" preconfigured view which includes both the Order Book and Execution Area features.\n\n<span id=\"page-6-2\"></span>![](_page_6_Picture_7.jpeg)\n\n**Figure 1 Add Order Book**\n\n![](_page_7_Picture_0.jpeg)\n\n|                                                   | <b>RFS REQUESTER</b>                          | <b>NEW VIEW</b>        | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help<br>$+$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | Δ                 | $\\bullet$ AA - $\\bullet$ X |  |\n|---------------------------------------------------|-----------------------------------------------|------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------|----------------------------|--|\n|                                                   | <b>Preconfigured Views</b>                    | <b>Select Features</b> | Preconfigured Views                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                   |                            |  |\n|                                                   |                                               |                        | Order View<br>Executions (S)<br>Executions (S)<br>$9 - \\theta$<br><b>Market Order</b><br>Legal Entity X<br>ASS 2 Way Price (2) MTF (2) Dearing (8) こと (2)<br>Legal Entity X<br>FX 5pot<br>Ruys EUR / Sells USD<br>FX Spot<br>Buys EUR / Sells USD<br>9.600.000.00 USD<br><b>Market</b><br>E<br>EUR / GBP<br>$\\frac{1}{2}$<br>Order Changes<br>Order Details<br>9.600.000.00 USD<br>е<br>@10685505<br>@1.0685505<br>Duy DUR<br>Selt Cities<br><b>Buy EUR</b><br>Sell Plate<br>FX Spot<br>Product<br>I Sell EUR / Buy CHF<br>Legal Entity V<br>Action<br>Legal Entity Y<br>05 <sub>6</sub><br>06 <sub>5</sub><br>840<br>$-406$<br>FX 5mg<br>Sels EUR / Buys CAD<br>12,500,000.00 USD<br>@ 1.1505505<br>6,250,000.00 EUR<br>FX Switch<br>Notional<br>Sells EUR / Boys CAD<br><b>EXPERIENCE AND RESIDENCE</b><br>e<br>Spot #<br>e<br>12,500,000.00 USD<br>1,12311<br>Market Rate<br>1294389<br>*07120<br>$0.80$ <sup>88.5</sup><br>@1.1505505<br>82 m<br>Partial Fill Progress<br><b>Total</b><br>1.520<br>1.730<br>730<br>とつちど<br>Legal Entity X<br>$******** = 0$<br>Legal Entity X<br>6,250,000.00<br>100k<br><b>Bally</b><br>FX Seap<br>Bays EUR / Sells USD<br>on i<br><b>FX Switch</b><br>$0.8608$ co<br>$0.88$ ng<br>Considered<br>$1.28$ $\\Delta$ $\\Delta$ ex<br>Boys EUR / Sells USD<br>GG 75%<br>$\\equiv$<br>$\\equiv$<br>RFS Requester<br>Order Management |                   |                            |  |\n| $\\zeta$<br>$\\mathbb C$<br>$\\bigcirc$<br>$\\bullet$ | 1/ BankClientM.TreasurerB, BankClientM // INT |                        | Thu, 15. Jul 2021, 14:37:24 GMT (Thu, 15. Jul 2021, 16:37 CEST) // Connected [FFM] ·<br><b>EECT</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | <b>Empty View</b> | Apply                      |  |\n\n<span id=\"page-7-0\"></span>**Figure 2 Add Preconfigured Order Management View**\n\nThe default view of the Order Book feature consists of two main rows including three different tabs:\n\nPending orders: **Initialized** (created but not yet sent/placed orders)\n\n**Sent** (sent but not yet accepted by the provider; pending provider rejections)\n\n**Accepted** orders: tab showing all sent orders accepted by the provider, but not yet executed\n\n![](_page_8_Picture_0.jpeg)\n\n|   | <b>RFS LIVE PRICING</b>                                         | <b>ORDER BOOK</b>           |                     | <b>EXECUTION AREA</b> | <b>DEAL TRACKING</b> |              |                   |                  |                                 |                      |                                    |                                   |  |\n|---|-----------------------------------------------------------------|-----------------------------|---------------------|-----------------------|----------------------|--------------|-------------------|------------------|---------------------------------|----------------------|------------------------------------|-----------------------------------|--|\n|   | Initialized (33)                                                | Q<br>土<br>Sent (4)          |                     |                       |                      |              |                   |                  |                                 |                      |                                    |                                   |  |\n| 间 | <b>Type</b>                                                     | $\\checkmark$<br>Reference # | <b>Order Status</b> | <b>Status</b>         | <b>Legal Entity</b>  | Requester Ac | Currencies        | Product          | <b>Notional Am</b>              | <b>Notional Curr</b> | Rate                               |                                   |  |\n|   | <b>Limit Order</b>                                              | EMSO-1318380                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 5.555.00 EUR                    | <b>EUR</b>           | $1.2$ $\\Box$ $\\Box$                | $\\rightarrow$                     |  |\n|   | <b>Limit Order</b>                                              | EMSO-1318367                | Initialized         | Initialized           | GroupE               | <b>BUV</b>   | CAD/JPY           | FxForward        | 4.008.00 CAD                    | CAD                  | 8: 1                               | $\\rightarrow$                     |  |\n|   | <b>Fixing Order</b>                                             | EMSO-1317488                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | AFN/AED           | <b>FxForward</b> | 5,000,000.00.                   | <b>AFN</b>           | c<br>$\\mathcal{Z}$                 | $\\rightarrow$                     |  |\n|   | <b>Market Order</b>                                             | EMSO-1312547                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | NZD/USD           | <b>FxSpot</b>    | 8,001.00 NZD                    | <b>NZD</b>           | e<br>$\\overline{z}$                | $\\triangleright$<br>$\\rightarrow$ |  |\n|   | <b>Market Order</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | EMSO-1312545                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 4.014.00 EUR                    | <b>EUR</b>           | c<br>$\\overline{z}$                | D<br>$\\rightarrow$                |  |\n|   | <b>Market Order</b>                                             | EMSO-1312275/1              | Initialized         | Initialized           | GroupE               | <b>BUV</b>   | EUR/USD           | <b>FxSpot</b>    | 123,123.00 E.                   | <b>EUR</b>           | $\\hfill \\square$<br>$\\overline{z}$ | $\\triangleright$<br>$\\rightarrow$ |  |\n|   | <b>Limit Order</b>                                              | EMSO-1312274                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 123,132.00 E.                   | <b>EUR</b>           | 1.2<br>$\\bar{z}$                   | $\\rightarrow$                     |  |\n|   | <b>Stop Order</b>                                               | EMSO-1312273/1              | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FXSpot</b>    | 31.231.00 EUR                   | <b>EUR</b>           | 12 <sup>0</sup><br>$\\overline{z}$  | $\\rightarrow$                     |  |\n|   | <b>Fixing Order</b>                                             | EMSO-1312268                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxForward</b> | 12,313.00 EUR                   | <b>EUR</b>           | c<br>$\\overline{z}$                | $\\rightarrow$                     |  |\n|   | <b>Market Order</b><br>$\\epsilon$                               | EMSO-1312267                | Initialized         | Initialized           | <b>GroupE</b>        | <b>BUV</b>   | EUR/USD           | <b>FxSpot</b>    | 1,231.00 EUR                    | <b>EUR</b>           | e<br>$\\frac{1}{2}$                 | $\\triangleright$<br>$\\rightarrow$ |  |\n|   | <b>Stop Order</b>                                               | EMSO-1312266                | Initialized         | Initialized           | <b>GroupE</b>        | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 123.00 EUR                      | <b>FUR</b>           | $1.2$ $\\Box$ $\\Box$                | $\\rightarrow$                     |  |\n|   | <b>Market Order</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | EMSO-1312250                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 12,323.00 EUR                   | <b>EUR</b>           | O<br>$\\mathcal{F}$                 | $\\triangleright$<br>$\\rightarrow$ |  |\n|   | <b>Limit Order</b>                                              | EMSO-1312186                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 2,342.00 EUR                    | <b>EUR</b>           | 1.2<br>- 51                        | $\\rightarrow$                     |  |\n|   | <b>Market Order</b>                                             | EMSO-1312181                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FXSpot</b>    | 1.231.00 EUR                    | <b>EUR</b>           | c<br>$\\Rightarrow$                 | D<br>$\\rightarrow$                |  |\n|   | <b>Market Order</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | EMSO-1312171                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 234.234.00 E                    | <b>EUR</b>           | c<br>$\\overline{z}$                | $\\triangleright$<br>$\\rightarrow$ |  |\n|   | $\\vee$ OCO Order                                                | EMSO-1312170                |                     | Initialized           | GroupE               |              |                   |                  |                                 |                      | p<br>$\\overline{z}$                | $\\rightarrow$                     |  |\n|   | $\\vee$ OCO Order                                                | EMSO-1312167                |                     | Initialized           | GroupE               |              |                   |                  |                                 |                      | c<br>$\\overline{z}$                | $\\rightarrow$                     |  |\n|   | <b>Market Order</b><br>$\\bullet$                                | EMSO-1312164                | Initialized         | Initialized           | GroupE               | <b>Buy</b>   | EUR/USD           | <b>FxSpot</b>    | 1.231.00 EUR EUR                |                      | $\\Box$<br>$\\frac{1}{2}$            | $\\triangleright$<br>$\\rightarrow$ |  |\n|   | l Q<br>Accepted (35)                                            | 土                           |                     |                       |                      |              |                   |                  |                                 |                      |                                    |                                   |  |\n|   | <b>Type</b>                                                     | Reference #                 | <b>Order Status</b> | <b>Status</b>         | <b>Legal Entity</b>  | Requester Ac | <b>Fill Ratio</b> |                  | Remaining A<br>Currencies       | <b>Product</b>       | Notional Am                        | <b>No</b>                         |  |\n|   | <b>Limit Order</b>                                              | EMSO-1318366/1              | Active              | Accepted              | GroupE               | <b>BUV</b>   |                   | 0.00%            | 7.777.00 EUR<br>EUR/USD         | <b>FxSpot</b>        | 7.777.00 EUR                       | $eu \\oplus$                       |  |\n|   | <b>Limit Order</b>                                              | EMSO-1317496                | Active              | Pending Canc.         | GroupE.TAS.T         | sell         |                   | 0.00%            | 55,000,00 GBP<br>GBP/USD        | <b>FXSpot</b>        | 55,000,00 GBP                      | GB ID                             |  |\n|   | <b>Fixing Order</b>                                             | EMSO-1317166                | Active              | Accepted              | <b>GroupE</b>        | Buy          |                   | 0.00%            | 560,000.00 U.<br>USD/JPY        | <b>FxForward</b>     | 560.000.00 U.                      | $US \\nightharpoonup$              |  |\n|   | <b>Fixing Order</b>                                             | EMSO-1317165                | Active              | Accepted              | GroupE               | <b>BUV</b>   |                   | 0.00%            | 55,000.00 GBP<br>GBP/USD        | <b>FxForward</b>     | 55,000.00 GBP                      | GB <sup>D</sup>                   |  |\n|   | <b>Market Order</b>                                             | EMSO-1316214                | Active              | Accepted              | GroupE               | Buy          |                   | 0.00%            | 123,000.00 E.<br><b>EUR/CHF</b> | <b>FXSpot</b>        | 123,000.00 E.                      | Φ<br>EU                           |  |\n|   | <b>Market Order</b>                                             | EMSO-1313543                | Active              | Accepted              | GroupE               | Buy          |                   | 0.00%            | EUR/USD<br>55,000.00 USD        | <b>FxSpot</b>        | 55,000.00 USD                      | $US \\nightharpoonup$              |  |\n|   | $\\wedge$ OCO Order                                              | EMSO-1313540                |                     | Accepted              | <b>GroupE</b>        |              |                   |                  |                                 |                      |                                    | o                                 |  |\n|   | <b>Stop Order</b>                                               | EMSO-1313538                | Active              | Accepted              | <b>GroupE</b>        | <b>BUV</b>   |                   | 0.00%            | 90,000,00 ARS<br>GBP/ARS        | <b>FXSpot</b>        | 90,000,00 ARS                      | AR <sup>D</sup>                   |  |\n|   | <b>Limit Order</b>                                              | EMSO-1313539                | Active              | Accepted              | GroupE               | Buy          |                   | 0.00%            | 100,000.00 A.<br>GBP/ARS        | <b>FxSpot</b>        | 100,000.00 A                       | $AR \\nightharpoonup$              |  |\n|   | $\\vee$ OCO Order                                                | EMSO-1312625                |                     | Accepted              | GroupE               |              |                   |                  |                                 |                      |                                    | ρ                                 |  |\n|   | <b>Market Order</b>                                             | EMSO-1312560                | Active              | Accepted              | GroupE               | <b>BUY</b>   | 75.00%            |                  | 1.743.00 NZD<br>NZD/USD         | <b>FxSpot</b>        | 7,005,00 NZD                       | $NZ \\n\\Box$                       |  |\n|   | <b>Market Order</b>                                             | EMSO-1312550/1              | Active              | Accepted              | GroupE               | Buy          |                   | 0.00%            | 8.004.00 NZD<br>NZD/USD         | <b>FxSpot</b>        | 8,004.00 NZD                       | $NZ \\n\\Box$                       |  |\n|   | <b>Market Order</b>                                             | EMSO-1312180                | Active              | Accepted              | <b>GroupE</b>        | <b>BUV</b>   |                   | 0.00%            | EUR/USD<br>1.231.00 EUR         | <b>FxSpot</b>        | 1.231.00 EUR EU $\\Box$ $\\Box$      |                                   |  |\n\n<span id=\"page-8-1\"></span>**Figure 3 Order Management Areas and Tabs**\n\nPlease note: executed, cancelled or expired orders will be shown in the Deal Tracking feature.\n\n# <span id=\"page-8-0\"></span>**3 Order Book Settings**\n\nThe Order Book Settings can be accessed by clicking the cog wheel icon in the upper right corner of the feature area. Using these settings, the user can configure an individualized screen layout, as well as tab and column visibility settings for the Order Book.\n\n| $\\sim$ Preferences $\\sim$ Help $\\parallel$ $\\oplus$ $\\mathcal{L}^1$ AA $ \\Box$ $\\times$ |                      |      |  |  |                  |\n|-----------------------------------------------------------------------------------------|----------------------|------|--|--|------------------|\n|                                                                                         |                      |      |  |  |                  |\n|                                                                                         |                      |      |  |  | <b>Feature S</b> |\n| Notional Am                                                                             | <b>Notional Curr</b> | Rate |  |  |                  |\n\n#### <span id=\"page-8-2\"></span>**Figure 4 Order Book: Feature Settings**\n\nThe user has the possibility to define:\n\n- Setup of the order screens (one, two or three rows),\n- Visibility of the tabs in each of the rows,\n- Visibility of columns in each of the tabs.\n\n![](_page_9_Picture_0.jpeg)\n\n| Type<br>v 000 Drder<br>v 000 Order | Sent (2) Initialized (9) Q 3<br>Reference # V Order Status Status<br>EMSO-1113.<br>De)<br>ENSO-1113<br>Del | Legal Entity Requester A., Currencies Product Notional A., Notional Cu., Rate   |                          | Order Book Settings | Expiry Type Expiry Date % to market Marker<br>$\\times$ | $\\Box$ $\\Box$<br>$\\Box \\quad \\  \\  \\, \\Xi$ | <b>Execution</b> |\n|------------------------------------|------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------|--------------------------|---------------------|--------------------------------------------------------|--------------------------------------------|------------------|\n| Accepted (0) Q L<br>Type           | Reference # < Order Status Stat                                                                            | > Setup Orders Screen<br>Tabs Visibility Settings<br>Column Visibility Settings | Screen Layout<br>One Row | O Two Rows          | Three Rows                                             | narket Market Rate Remain                  |                  |\n|                                    |                                                                                                            |                                                                                 |                          |                     | Cancel<br>Apply                                        |                                            |                  |\n\n<span id=\"page-9-0\"></span>**Figure 5 Order Book Settings: Setup Orders Screen**\n\nColumns or tabs can be added or removed from a view tab by clicking on the respective eye symbol.\n\n|    |                                      |                                                                          |                                                                                                                          |                                                |                     |                                            |                                                                           | $\\Box$ $\\Box$ $\\Box$ $\\vdash$ AA $ \\Box$ $\\times$ |\n|----|--------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------|------------------------------------------------|---------------------|--------------------------------------------|---------------------------------------------------------------------------|---------------------------------------------------|\n|    | Order Management +                   |                                                                          |                                                                                                                          |                                                |                     |                                            |                                                                           |                                                   |\n|    |                                      | Sent (2) Initialized (9) $\\boxed{Q}$ $\\boxed{\\pm}$                       |                                                                                                                          |                                                |                     |                                            |                                                                           | Executions                                        |\n| 咸  | Type                                 | Reference # $\\vee$ Order Status Status                                   | Legal Entity Requester A Currencies Product Notional A. Notional Cu Rate                                                 |                                                |                     | Expiry Type Expiry Date % to market Market |                                                                           |                                                   |\n| ①  | $\\vee$ OCO Order<br>$\\vee$ OCO Order | EMSO-1113<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>EMSO-1113<br>De |                                                                                                                          |                                                | Order Book Settings | $\\times$                                   | $\\circ$ $\\circ$<br>$\\begin{array}{ c c c c c c c c c c c c c c c c c c c$ |                                                   |\n| Å, |                                      |                                                                          | Setup Orders Screen<br>$\\vee$ Tabs Visibility Settings<br>$>$ Top Row<br><b>Bottom Row</b><br>Column Visibility Settings | © Sent<br>$\\sqrt{ }$ Accepted<br>© Initialized |                     |                                            |                                                                           |                                                   |\n|    | Accepted (0) $Q \\mid \\pm$<br>Type    | Reference # $\\vee$ Order Status Stat                                     |                                                                                                                          |                                                |                     |                                            | market Market Rate Remain                                                 |                                                   |\n|    |                                      |                                                                          |                                                                                                                          |                                                |                     | Cancel  <br>Apply                          |                                                                           |                                                   |\n|    |                                      |                                                                          |                                                                                                                          |                                                |                     |                                            |                                                                           |                                                   |\n\n#### <span id=\"page-9-1\"></span>**Figure 6 Order Book Settings: Tabs Visibility Settings**\n\nThe column visibility settings can be copied to all tabs and modified individually before applying the changes.\n\n![](_page_10_Picture_0.jpeg)\n\n| Reference # > Order Status Status<br>Type:<br>Stop Drder<br>EMSO-1781 Active<br>间<br>Stop Drder<br>EMSO-1281 Active<br>Market Order<br>EASO-1281 Active<br>×.<br>÷. | Ligal Entity Requester A. Currencies Product                                                                                | Notional A., Notional Cu., Rate<br>Order Book Settings                                                                                                                                                                                                                                                                                                                           |                                                                                                                                                                                                                                                                                                                                                | Expiry Type Expiry Date % to market Market Rate Executed A.; Remaining<br>16582<br>102601<br>$\\times$ | 1,23028<br>1,23028<br>123035                        | G)<br>G.<br>10.<br>$\\sqrt{2}$<br>i ci |\n|---------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|-----------------------------------------------------|---------------------------------------|\n| Accepted in Q 5<br>Reference # V Order Status Stat<br>Type                                                                                                          | Setup Orders Screen<br><b>Tabs Visibility Settings</b><br>Column Visibility Settings<br>$>$ Sent<br>Accepted<br>Initialized | $Q_V$<br><b> </b><br>© Order Status<br>C Legal Entity<br>© Currencies<br>Notional Amount<br><b>C</b> Rate<br>C Expiry Date<br>C Market Rate<br><sup>©</sup> Remaining Amount<br>C Provider Action<br>& Base Currency<br>Ø Counterpart<br><b>25 Fixing Date</b><br>@ Maturity Date<br>Ø Near Leg Period<br>(2) Negotiating Status Driginator Company<br>Copy Settings to all Tabs | Type<br>Status<br>Requester Action<br>Product<br>C Notional Currency<br>C Expiry Type<br>S to market<br>C Executed Amount<br><b>EIII Ratio</b><br>Available Actions<br>Completion Progress<br>@ Effective Date<br><b>66</b> Fixing Reference<br>C Maturity Period<br>6 Negotiation Status Comment<br>CO Order Status Local Timestamo<br>Cancel | Apply                                                                                                 | arkot Market Rate Executed A., Remaining Fill Ratio |                                       |\n\n<span id=\"page-10-0\"></span>**Figure 7 Order Book Settings: Column Visibility Settings**\n\n![](_page_11_Picture_0.jpeg)\n\n# <span id=\"page-11-0\"></span>**4 Order Book: Workflow and Status**\n\nThe different steps in the order lifecycle are summarized as follows:\n\n|                                              | Order Status | Status/Request<br>Changes                                  | Name of Order Book<br>Tab or Deal Tracking<br>Tab |\n|----------------------------------------------|--------------|------------------------------------------------------------|---------------------------------------------------|\n| Create order                                 | Initialized  | Initialized                                                | Initialized                                       |\n| Amend created order                          | Initialized  | Initialized                                                | Initialized                                       |\n| Delete created order<br>draft                | -            | -                                                          | -                                                 |\n| Send/place<br>order                          | Initialized  | Delivered or Sent (if<br>no manual trader is<br>logged in) | Sent                                              |\n| Send order as RFS<br>(market/slice<br>order) |              |                                                            | Deal Tracking                                     |\n| Monitor placed order                         | Active       | Accepted                                                   | Accepted                                          |\n| Monitor rejected order                       | Active       | Pending rejection                                          | Sent                                              |\n| Monitor partial order<br>execution           |              |                                                            | Accepted                                          |\n| Confirm order reject                         | Initialized  | Initialized                                                | Initialized                                       |\n| Withdraw placed<br>order                     | Initialized  | Initialized                                                | Initialized                                       |\n| Withdraw accepted<br>order                   | Active       | Pending cancellation                                       | Accepted                                          |\n| Monitor confirmed<br>cancellation request    | Initialized  | Initialized                                                | Initialized                                       |\n| Monitor order<br>expiration                  | Closed       | Pending cancellation;<br>Expired                           | Deal Tracking                                     |\n| Place Slice Order                            | Initialized  | Initialized                                                | Initialized                                       |\n| Pause/Resume Slice<br>Order                  | Initialized  | Initialized                                                | Initialized                                       |\n\n<span id=\"page-11-1\"></span>**Table 1 Steps in Order Lifecycle and Order Status** \n\n![](_page_12_Picture_0.jpeg)\n\n## <span id=\"page-12-0\"></span>**4.1 Create Order**\n\nMarket Takers choose from several options when placing orders to buy or sell FX OTC products. With these orders they instruct a market maker to buy or sell at a specified price. These orders can be placed for a specific time period.\n\nAn order must not necessarily be placed immediately. It can be created ('prepared' or 'initialized') and stay in the Initialized tab in order to be placed (or amended and placed) later. A placed order which is not yet accepted by the provider will appear in the Sent tab.\n\nCurrently, it is possible to place the following order types in 360T Bridge Order Book:\n\n- Limit Order\n- Stop Order\n- Market Order\n- Fixing Order\n- OCO (one-cancels-the-other order)\n- If-Done Order\n- Loop Order\n- Algo Order\n- Slice Order\n- Call Order\n\nThe user can create a new order by clicking on the \"New Order\" icon ( ) in the left panel of the Order Book.\n\n![](_page_12_Picture_17.jpeg)\n\n#### <span id=\"page-12-1\"></span>**Figure 8 Order selection in Bridge Order Book**\n\nThe order type can be alternatively selected from a drop-down menu in the header of the order definition window. It can also be directly created for a specific currency pair and tenor in the RFS Live Pricing feature (Spot/Outright tab) by first selecting the order type (similar to other execution modes) and clicking on the desired currency pair and tenor panel.\n\n![](_page_13_Picture_0.jpeg)\n\n![](_page_13_Picture_2.jpeg)\n\n**Figure 9 Order selection in Bridge RFS Live Pricing**\n\n<span id=\"page-13-0\"></span>\n\n|                                                                                                                                                        | <b>Order Creation</b><br>Market Order $\\sim$                                                                                                                                                                                                                                                       |                                                                                                                                                            |                                     |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------|\n| Order Details                                                                                                                                          | Prod<br>√ Market Order<br>Limit Order<br>Spo<br>Stop Order<br>$\\angle$ Custom<br>Fixing Order<br>Algo Order<br>Bu<br>Loop Order<br>≓<br>OCO Order<br>Sel<br>If-Done Order<br>Effet<br>Call Order<br>Spot<br>THU, 18.03.2021<br>Market Rate<br><b>Expiry</b><br>$GTC \\vee$<br>Tue, 16.03.2021 00:00 | $\\checkmark$<br>nal<br>雦<br>No rolling<br>1.19254<br>(前)                                                                                                   |                                     |\n| Provider List                                                                                                                                          | Comments                                                                                                                                                                                                                                                                                           |                                                                                                                                                            |                                     |\n| 360T.AMERICAS<br>ABN AMRO.DEMO<br><b>BOAL DEMO</b><br><b>BankC</b><br><b>BankM</b><br><b>BankP</b><br>CITIBANK.DEMO<br><b>DB.DEMO</b><br>JPMORGAN.DEMO | 360T.APAC<br><b>BANKWINTER TEST</b><br>Bank of China London.T<br><b>BankD</b><br><b>BankN</b><br>Barclays BARX.DEMO<br><b>COBA.FRA DRESDNER.D</b><br><b>HSBC.DEMO</b><br>RBS.LND.DEMO                                                                                                              | 360TBANK.TEST<br>BNPP.DEMO<br>Bank of Ireland.TEST<br><b>BankH</b><br><b>BankO</b><br>Barclays SEF.DEMO<br>Credit Suisse.DEMO<br>JPM.GTX<br>Unicredit.DEMO | $\\frac{1}{2}$<br>☑<br>$(\\boxtimes)$ |\n|                                                                                                                                                        | Cancel                                                                                                                                                                                                                                                                                             | Create<br>Place                                                                                                                                            |                                     |\n\n<span id=\"page-13-1\"></span>**Figure 10 Order Creation: Changing Order Type**\n\n![](_page_14_Picture_0.jpeg)\n\n### <span id=\"page-14-0\"></span>**4.1.1 Common order creation attributes**\n\nThe order creation screens are divided into three areas for single leg orders and into four areas for combined orders. The upper area contains the order details which can vary for each order type (see details in the following sections). The bottom area includes the Provider List and Comments tabs.\n\n|                                          | <b>Order Creation</b>                             |                    |               |\n|------------------------------------------|---------------------------------------------------|--------------------|---------------|\n|                                          | Stop Order $\\vee$                                 |                    |               |\n| <b>Order Details</b><br>✓                | Custom Fields                                     |                    |               |\n|                                          | $EUR \\vee 0$<br>Buy<br>≓<br>$USD \\vee 0$<br>Sell  | Notional           |               |\n|                                          | <b>Effective Date</b>                             |                    |               |\n|                                          | Mon, 07.05.2018          <br>Spot<br>$\\checkmark$ | No rolling         |               |\n|                                          | Stop Spot Rate<br>Market Rate                     |                    |               |\n|                                          | $1.19759$ $(+)$                                   | 1.19756            |               |\n|                                          | Expiry                                            |                    |               |\n|                                          | $GTC \\vee$<br>Thu, 03.05.2018 00:00               | 前                  |               |\n| Provider List<br>Comments                |                                                   |                    |               |\n| 360TBANK.TEST                            | BNPP.DEMO                                         | <b>BOAL.DEMO</b>   | $\\frac{1}{2}$ |\n| <b>BankB</b>                             | Barclays BARX.DEMO                                | CITIBANK.DEMO      |               |\n| COBA.DEMO                                | <b>COBA.FRA DRESDNER</b>                          | Credit Suisse.DEMO |               |\n| DB.DEMO                                  | HSBC.DEMO                                         | JPMORGAN.DEMO      |               |\n| LLOYDS.DEMO                              | MTFBankA                                          | <b>OTCBankA</b>    |               |\n| PEBANKEMEA1.TEST                         | RBS.LND.DEMO                                      | SEB.DEMO           |               |\n| Scotia Capital.TEST   Unicredit.DEMO     |                                                   |                    |               |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                                                   |                    |               |\n|                                          | Cancel                                            | Create<br>Place    |               |\n\n<span id=\"page-14-1\"></span>**Figure 11 Order Creation: Definition Window**\n\nThe order type can be changed in the header of the order definition window. Changing the order type re-sets the previously entered values, except from the currency pair which remains as initially defined.\n\nThe user can type in the **currency or precious metal** (gold, silver, palladium or platinum) abbreviation or select it from the drop-down list (alphabetical order).\n\nThe **notional amount** can be entered into either currency field. The user can switch the notional amount from one currency to the other by clicking on the green button in front of the amount. Keyboard short keys can be used to quickly enter notional values: \"b\" for billion, \"m\" for million, \"k\" for thousand (for example \"5m\" for 5 million notional or \"10k\" for 10 thousand notional).\n\n**Expiry date:** If no end date should be defined the value GTC ('good till cancelled') can be selected. A GTC order will not expire until the user cancels it. Alternatively, the user can select GTD ('good till date') if the order should \"roll\" but expire on a specific date in the future.\n\nFor spot orders the user can select **\"No rolling\"** if the order should expire on the effective date at the latest. The GTD can be selected within this period.\n\n![](_page_15_Picture_0.jpeg)\n\n|                                                                                                                 | <b>Order Creation</b><br>Limit Order $\\vee$                                                                                                                                                                                                                                 |\n|-----------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <b>Order Details</b>                                                                                            | / Custom Fields                                                                                                                                                                                                                                                             |\n|                                                                                                                 | $EUR \\vee$<br>Buy<br>(<br>Sell<br>Notional<br>$USD \\vee$<br>IО<br><b>Effective Date</b><br>Mon, 07.05.2018      <br>No rolling<br>Spot<br>$\\checkmark$<br>Limit Spot Rate<br>Market Rate<br>1.19704<br>1.19712<br><b>Expiry</b><br>瞓<br>$GTC \\vee$<br>Thu, 03.05.2018 00:00 |\n| Provider List<br>Comments                                                                                       |                                                                                                                                                                                                                                                                             |\n| 360TBANK.TEST<br><b>BankB</b><br>COBA.DEMO<br>DB.DEMO<br>LLOYDS.DEMO<br>PEBANKEMEA1.TEST<br>Scotia Capital.TEST | 웋<br>BNPP.DEMO<br><b>BOAL DEMO</b><br>Barclays BARX.DEMO<br>CITIBANK.DEMO<br><b>COBA.FRA DRESDNER</b><br>Credit Suisse.DEMO<br>HSBC.DEMO<br>JPMORGAN.DEMO<br>MTFBankA<br><b>OTCBankA</b><br>RBS.LND.DEMO<br>SEB.DEMO<br>Unicredit.DEMO                                      |\n|                                                                                                                 | Cancel<br>Create<br>Place                                                                                                                                                                                                                                                   |\n\n<span id=\"page-15-0\"></span>**Figure 12 Order Creation: Forward Limit Order**\n\nIt is possible to place orders with forward dates. To do this, the effective date for a forward can be entered. It is also possible to enter a date using the calendar icon. The limit rate nevertheless always references **the spot rate**.\n\n![](_page_16_Picture_0.jpeg)\n\n|                                                          | <b>Order Creation</b><br>Limit Order $\\vee$                                                                                                                                                                                                                     |\n|----------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <b>Order Details</b>                                     | / Custom Fields<br>$EUR \\vee$<br><b>Buy</b><br>Notional<br>٠<br>$\\Rightarrow$<br>USD $\\vee$<br>Sell<br>$\\circ$<br><b>Effective Date</b><br>Mon, 07.05.2018 (1)<br>No rolling<br>Spot<br>$\\checkmark$                                                            |\n|                                                          | Limit Spot Rate<br>Market Rate<br>1.19704<br>1.19715<br><b>Expiry</b><br>雦<br>GTD V   Fri, 18.05.2018 00:00<br>$\\times$<br><b>May 2018</b><br>Friday<br>⋗                                                                                                       |\n| Provider Lis                                             | M<br>s<br>т<br>W<br>F<br>s<br>т<br><b>MAY</b><br>5<br>$\\overline{2}$<br>3<br>4<br>1                                                                                                                                                                             |\n| 360TBAN<br><b>BankB</b><br><b>COBA DEN</b><br>DB.DEMO    | 8<br>11<br>6<br>7<br>9<br>10<br>12<br>18<br>$\\frac{1}{20}$<br>AL.DEMO<br>(18)<br>13<br>14<br>15<br>16<br>17<br>19<br><b>IBANK.DEMO</b><br>25<br>22<br>23<br>24<br>26<br>20<br>21<br>dit Suisse DEMO<br>28<br>29<br>30<br>31<br>27<br>2018<br><b>MORGAN.DEMO</b> |\n| LLOYDS.Dinwie<br>PEBANKEMEA1.TEST<br>Scotia Capital.TEST | CBankAء ر<br><b>IVITT DOITINA</b><br>SEB.DEMO<br>RBS.LND.DEMO<br>Unicredit.DEMO<br>Place<br>Create<br>Cancel                                                                                                                                                    |\n\nAll forward orders have a GTD ('good till date') expiry date which must be selected within a time period up to the effective date.\n\n<span id=\"page-16-0\"></span>**Figure 13 Order Creation: Expiry Date of Forward Limit Order**\n\n![](_page_17_Picture_0.jpeg)\n\n|                                                                                                                                        | <b>Order Creation</b><br>Market Order $\\vee$                                                                                                                                                                          |                                                                                                                                     |                                |\n|----------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|\n|                                                                                                                                        | Product<br>Spot / Outright                                                                                                                                                                                            |                                                                                                                                     |                                |\n| Order Details<br>✓                                                                                                                     | Custom Fields                                                                                                                                                                                                         |                                                                                                                                     |                                |\n|                                                                                                                                        | $EUR \\vee 0$<br><b>Buy</b><br>$\\rightleftharpoons$<br>USD $\\vee$ 0<br>Sell<br><b>Effective Date</b><br>Tue, 20.11.2018<br>Spot<br>$\\checkmark$<br>Market Rate<br><b>Expiry</b><br>$GTC \\vee$<br>Fri, 16.11.2018 00:00 | 1,000,000<br>雦)<br>No rolling<br>1.13526<br>雦                                                                                       |                                |\n| Provider List                                                                                                                          | Comments                                                                                                                                                                                                              |                                                                                                                                     |                                |\n| 360TBANK.TEST<br><b>BankB</b><br>CITIBANK.DEMO<br>Credit Suisse.DEMO<br>HSBC.DEMO<br><b>MTFBankA</b><br>RBS.LND.DEMO<br>Unicredit.DEMO | BNPP.DEMO<br>Barclays BARX.DEMO<br>COBA.DEMO<br><b>DB.DEMO</b><br>JPMORGAN.DEMO<br>$\\sqrt{\\phantom{a}}$ OTCBankA<br>SEB.DEMO                                                                                          | <b>BOAL.DEMO</b><br><b>CITIBANK</b><br>COBA.FRA DRESDNER.D<br><b>HSBC</b><br>LLOYDS.DEMO<br>PEBANKEMEA1.TEST<br>Scotia Capital.TEST | $\\frac{1}{\\sqrt{2}}$<br>☑<br>⊠ |\n|                                                                                                                                        | Cancel                                                                                                                                                                                                                | Create<br>Place                                                                                                                     |                                |\n\n<span id=\"page-17-0\"></span>**Figure 14 Order Creation: Provider List**\n\nThe user must select at least one provider to create an order (refer to the left screenshot).\n\nIn case several providers were selected during the order creation, the subsequent \"Place Order\" view shows the pre-selected providers as a reduced set of \"Provider List\" items from which one provider has to be chosen prior to the *placement of the order*.\n\nIn contrast, the multiple selected providers during order creation are utilized as a pre-selected provider list in the \"Product Definition\" view prior to the submission of the *RFQ request.* Additional rules and validations apply to provider selection in slice orders, as explained further in section [0](#page-29-2)\n\n![](_page_18_Picture_0.jpeg)\n\n| <b>Order Creation</b>                                                                                      |\n|------------------------------------------------------------------------------------------------------------|\n| Limit Order $\\vee$                                                                                         |\n| $EUR \\vee 0$<br>Notional<br>$\\overset{\\mathsf{Buy}}{\\rightleftharpoons}$<br>$($ USD $\\vee$ $\\circ$<br>Sell |\n| <b>Effective Date</b>                                                                                      |\n| $\\vee$ Mon, 07.05.2018 (iii) No rolling<br>Spot                                                            |\n| Limit Spot Rate Market Rate                                                                                |\n| $\\bigcirc$ 1.19704 $\\bigcirc$<br>1.19707                                                                   |\n| <b>Expiry</b>                                                                                              |\n| 曲<br>GTD $\\vee$ Fri, 18.05.2018 00:00                                                                      |\n| Ref. Time (GMT): Thu, 17.05.2018 22:00                                                                     |\n| Provider List<br>Comments                                                                                  |\n|                                                                                                            |\n| For internal view only                                                                                     |\n| Create<br>Place<br>Cancel                                                                                  |\n\nA comment to an order can be entered in the Comments tab. When flagged, the comment is for internal use only. If not flagged, the comment will be visible internally and to the Provider (market maker).\n\n<span id=\"page-18-0\"></span>**Figure 15 Order Creation: Comments tab**\n\n![](_page_19_Picture_0.jpeg)\n\n## <span id=\"page-19-0\"></span>**4.1.2 Market Order**\n\nA market order is an order to buy or sell a currency at the available current market price with minimum delay.\n\nMarket orders are the only type of trade orders which can be either negotiated as a multidealer Request-For-Stream (RFS) request with multiple liquidity providers or placed as an order against a single liquidity provider depending on the underlying instrument (refer to [Table](#page-19-1)  [2\\)](#page-19-1). In contrast, the remaining order types (e.g. Limit Order) only facilitate placement against a single liquidity provider.\n\n| Instrument of market order | Order Placement supported? | RFS negotiation supported? |\n|----------------------------|----------------------------|----------------------------|\n| Outright                   | Yes                        | Yes                        |\n| NDF                        | Yes (I-TEX requesters)     | Yes                        |\n| Swap                       | Yes (I-TEX requesters)     | Yes                        |\n| NDS                        | No                         | Yes                        |\n| Loan/ Deposit              | No                         | Yes                        |\n| Future1                    | Yes                        | No                         |\n\n<span id=\"page-19-1\"></span>**Table 2: Supported Trading Workflow**\n\n360T operates a Multilateral-Trading-Facility (MTF) which is based on the RFS workflow. Consequently, a MTF enabled trader has the option during the order creation process to determine whether a market order is expected to be traded on the MTF when sent as RFS (refer to [Figure 16\\)](#page-20-1). In case the MTF field has been flagged, the underlying fields \"Trading Capacity\" and \"Investment Decision\" are mandatory and must be filled in.\n\n<sup>1</sup> Clients can route orders to their Futures brokers for execution via broker FIX integration to 360T Bridge. This Futures setup can be used alongside the existing direct exchange connectivity offerings (DMA/SMA), and off-exchange block and EFP negotiation with STP reporting into the exchange.\n\n![](_page_20_Picture_0.jpeg)\n\n|                            | <b>Order Creation</b><br>Market Order $\\sim$                                                        |\n|----------------------------|-----------------------------------------------------------------------------------------------------|\n|                            | Product<br>Spot / Outright<br>$\\checkmark$                                                          |\n|                            | Buy<br>$EUR \\vee$<br>1,000,000<br>$USD \\vee$<br>Sell<br>$\\circ$<br><b>Effective Date</b>            |\n|                            | 1 Week<br>Mon, 26.07.2021<br>7 Days<br>$\\checkmark$<br>1.18142<br>Market Rate                       |\n|                            | <b>Expiry</b><br>篇<br>Fri, 16.07.2021 00:00<br>$GTD \\vee$<br>Ref. Time (GMT): Thu, 15.07.2021 22:00 |\n| Provider List<br>MIFID     | ◯ Comments                                                                                          |\n| <b>VO MTF</b>              |                                                                                                     |\n| <b>Trading Capacity</b>    | <b>DEAL</b>                                                                                         |\n| <b>Investment Decision</b> | GroupA.TreasurerB                                                                                   |\n|                            | Cancel<br><b>Create</b><br>Place                                                                    |\n\n<span id=\"page-20-1\"></span>**Figure 16 Market Order**\n\n## <span id=\"page-20-0\"></span>**4.1.3 Limit Order**\n\nA limit order is an order to buy or sell at a specified price or better. It is a single leg order with no dependencies.\n\nWhen entering a rate in the limit order a check is performed against current market data. This helps to avoid placement of an order at a limit rate which is at the market or \"on the wrong side\" of the market rate. In this case, the limit spot rate font colour changes to red.\n\nThe Place button will only be activated when all the order details are complete and when only one provider is selected.\n\n![](_page_21_Picture_0.jpeg)\n\n|                                                             | <b>Order Creation</b><br>Limit Order $\\vee$                                                                                                                                                                                                                       |                                                                        |                                    |\n|-------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------|------------------------------------|\n| Provider List                                               | $EUR \\vee$<br><b>Buy</b><br>$\\rightleftharpoons$<br>$USD \\vee$ 0<br>Sell<br><b>Effective Date</b><br>Spot<br>$\\checkmark$<br><b>Limit Spot Rate</b><br><b>Market Rate</b><br>$1.17828$ $\\oplus$<br>◒<br>Expiry<br>$GTC \\vee$<br>Thu, 15.07.2021 00:00<br>Comments | 1,000,000<br>Mon, 19.07.2021<br>No rolling<br>1.18137<br>$\\frac{1}{2}$ |                                    |\n| ANZ.DEMO<br>Barclays BARX.DEMO<br>DrKW.DEMO<br>RBS.LND.DEMO | BOAL.DEMO<br>CITIBANK.DEMO<br>HYPO.DEMO<br>SEB FRA DEMO                                                                                                                                                                                                           | <b>BankA</b><br>COBA.DEMO<br><b>ICD.TEST</b><br>TB-HSBC.DEMO           | $\\frac{1}{20}$<br>હ<br>$\\boxtimes$ |\n|                                                             |                                                                                                                                                                                                                                                                   | Create<br>Cancel                                                       | Place                              |\n\n#### <span id=\"page-21-0\"></span>**Figure 17 Limit Order**\n\nA limit order can also be placed to Hypersonic Orderbook, for passively placing interest to be aggressed. This requires specific enablement and is available for FX Spot only.\n\nWhen this option is selected, the limit order is sent to the HST Aggregator as a \"Work and Pounce\" strategy where it will sit both on the CLOB and on the private book.\n\nBridge Order Management can show the progress for HST orders, similarly to other supported order types. HST Limit Orders can be activated by 360T CAS team.\n\n![](_page_22_Picture_0.jpeg)\n\n| <b>Order Creation</b><br>Limit Order $\\vee$                                                                                                                                                                                                                                                                                                                                                                                        |                          |\n|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------|\n| Product<br>Spot / Outright                                                                                                                                                                                                                                                                                                                                                                                                         |                          |\n| / Custom Fields<br>Order Details                                                                                                                                                                                                                                                                                                                                                                                                   |                          |\n| <b>Buy</b><br>$EUR \\vee$ $\\bullet$<br>Notional<br>≓<br>$USD \\vee$<br>Sell<br>10<br><b>Effective Date</b><br>$\\mathbb{R}$ ) $\\Box$ No rolling<br>Spot<br>Thu, 02.03.2023<br>$\\checkmark$<br>Limit Spot Rate<br>Market Rate<br>$1.06210 +$<br>1.06152<br>$\\sqrt{2}$<br><b>Expiry</b><br>$\\left[\\begin{smallmatrix} 1 & 0 & 0 \\\\ 0 & 1 & 0 \\\\ 0 & 1 & 0 \\\\ 0 & 0 & 1 \\end{smallmatrix}\\right]$<br>Tue, 28.02.2023 00:00<br>$GTC \\vee$ |                          |\n| Provider List<br>Comments                                                                                                                                                                                                                                                                                                                                                                                                          |                          |\n| 360T Platform Order (Hypersonic Orderbook)<br>Hosted Order (Place with Counterpart)                                                                                                                                                                                                                                                                                                                                                | 윿<br>Ú<br>$(\\mathbb{X})$ |\n| Cancel<br>Create                                                                                                                                                                                                                                                                                                                                                                                                                   | Place                    |\n\n<span id=\"page-22-1\"></span>**Figure 18 Limit Order placement to Hypersonic Orderbook**\n\n## <span id=\"page-22-0\"></span>**4.1.4 Stop Order**\n\nA stop order is an order to buy or sell a product once the price of the product reaches a specified price level known as the stop price. When the specified price is reached, the stop order becomes a market order (see more in section [4.1.2\\)](#page-19-0). Execution can be at or worse than the stop rate.\n\nWhen creating a stop order a check is performed against current market data. This helps to avoid placement of an order at a stop rate which is at the market or \"on the wrong side\" of the market rate. In this case, the stop spot rate font colour changes to red.\n\nThe Place button will only be activated when all order details are complete, and a provider is selected.\n\n![](_page_23_Picture_0.jpeg)\n\n| ◯ Custom Fields<br>Order Details<br>$EUR \\vee 0$<br><b>Buy</b><br>Notional<br>≓<br>$USD \\vee$ 0<br>Sell<br><b>Effective Date</b><br>Mon, 07.05.2018          <br>Spot<br>No rolling<br>$\\checkmark$<br>Stop Spot Rate<br>Market Rate<br>$1.19818$ $\\oplus$<br>1.19810<br><b>Expiry</b><br>           <br>Thu, 03.05.2018 00:00<br>$GTC \\vee$<br>Provider List<br>Comments<br>$\\approx$<br>360TBANK.TEST<br>BNPP.DEMO<br><b>BOAL DEMO</b><br><b>BankB</b><br>Barclays BARX.DEMO<br>CITIBANK.DEMO<br>COBA.DEMO<br>Credit Suisse.DEMO<br>COBA.FRA DRESDNER<br>DB.DEMO<br>HSBC.DEMO<br>JPMORGAN.DEMO<br>MTFBankA<br><b>OTCBankA</b><br>LLOYDS.DEMO<br>RBS.LND.DEMO<br>SEB.DEMO<br>PEBANKEMEA1.TEST<br>Unicredit.DEMO<br>Scotia Capital.TEST [<br>Select All Unselect All | <b>Order Creation</b><br>Stop Order $\\vee$ |  |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------|--|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |                                            |  |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |                                            |  |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |                                            |  |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |                                            |  |\n| Place<br>Create<br>Cancel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                            |  |\n\n#### <span id=\"page-23-1\"></span>**Figure 19 Stop Order**\n\n## <span id=\"page-23-0\"></span>**4.1.5 Fixing Order**\n\nA fixing order is an intraday order to buy or sell a product at the rate available at a specific time of the day (fixing time). The fixing reference can be set to a fixing authority or a customized fixing date. The possible fixing authorities are the ECB, Reuters EBS, WM/Reuters Intraday, WM/Reuters Closing, WM/Reuters Australia and Bank of Canada Noon (BOC Noon). The different fixing authorities have different fixing dates and times. In case a desired fixing is not included in the drop-down list, you can select \"Custom Fixing\" and then define the required date and time.\n\n![](_page_24_Picture_0.jpeg)\n\n|                           | <b>Order Creation</b>                                                                 |\n|---------------------------|---------------------------------------------------------------------------------------|\n|                           | Fixing Order $\\vee$                                                                   |\n| <b>Order Details</b>      | / Custom Fields                                                                       |\n|                           | $EUR \\vee 0$<br>Buy<br>근<br>Notional<br>$USD \\vee$ 0<br>Sell<br><b>Effective Date</b> |\n|                           | Spot<br>$\\vee$<br>Mon, 07.05.2018                                                     |\n|                           | 1.19828<br>Market Rate                                                                |\n|                           | <b>Fixing Reference</b>                                                               |\n|                           | <b>ECB</b><br>$\\checkmark$                                                            |\n|                           | <b>Fixing Date</b><br>雦<br>Thu, 03.05.2018 14:15 CET                                  |\n|                           | <b>Expiry</b>                                                                         |\n|                           | 雦<br>Thu, 03.05.2018 00:00<br>$GTC \\vee$                                              |\n| Provider List             | Comments                                                                              |\n|                           | 욱                                                                                     |\n| <b>360TBANK TEST</b>      | BNPP.DEMO<br><b>BOAL,DEMO</b>                                                         |\n| <b>BankB</b><br>COBA.DEMO | Barclays BARX.DEMO<br>CITIBANK.DEMO<br><b>COBA.FRA DRESDNER</b><br>Credit Suisse.DEMO |\n| DB.DEMO                   | HSBC.DEMO<br>JPMORGAN.DEMO                                                            |\n| LLOYDS.DEMO               | <b>MTFBankA</b><br><b>OTCBankA</b>                                                    |\n|                           | Cancel<br>Create<br>Place                                                             |\n\n#### <span id=\"page-24-1\"></span>**Figure 20 Fixing Order**\n\n## <span id=\"page-24-0\"></span>**4.1.6 OCO Order ('one-cancels-the-other')**\n\nAn OCO order is a combination of two orders where the execution of one order automatically cancels the second order.\n\nBasically, there is no obligation to link two defined types of orders in an OCO order. In practice, in most cases, one order will be a limit order and the other a stop order. Therefore, this combination is set as default. To change the default, select another order type in the available drop-down menus. Please note that the notional of both order legs must be expressed in the same currency.\n\nWhen creating an OCO order a check with the current market data is performed. This helps to avoid the placement of an order at a limit rate or stop rate which is at the market or \"on the wrong side\" of the market rate. In this case the limit or stop rate font colour changes to red.\n\nThe Place button will only be activated when all order details are complete.\n\n![](_page_25_Picture_0.jpeg)\n\n|                                                                                    | OCO Order                | <b>Order Creation</b><br>$\\mathbb{R}^2$                                       |\n|------------------------------------------------------------------------------------|--------------------------|-------------------------------------------------------------------------------|\n| <b>Order Details</b>                                                               | / Custom Fields          |                                                                               |\n| Order Type                                                                         |                          | Order Type                                                                    |\n| Limit Order                                                                        |                          | Stop Order                                                                    |\n| $EUR \\vee$ $\\bullet$<br><b>Buy</b><br>$\\rightleftharpoons$<br>$USD \\vee 0$<br>Sell | Notional                 | $EUR \\vee$<br>Buy<br>Notional<br>$\\rightleftharpoons$<br>$USD \\vee 0$<br>Sell |\n| <b>Effective Date</b>                                                              |                          | <b>Effective Date</b>                                                         |\n| Spot                                                                               | 篇<br>Thu, 01.03.2018     | 篇<br>Spot<br>Thu, 01.03.2018<br>$\\checkmark$                                  |\n|                                                                                    | No rolling               | No rolling                                                                    |\n| <b>Limit Rate</b>                                                                  | Market Rate              | Market Rate<br>Stop Rate                                                      |\n| $1.23181 +$                                                                        | 1.23186                  | $1.23178 +$<br>1.23179                                                        |\n|                                                                                    | <b>Expiry</b>            |                                                                               |\n| Provider List<br>Comments                                                          |                          |                                                                               |\n| <b>360TBANK TEST</b>                                                               | BNPP.DEMO                | $\\frac{1}{2}$<br><b>BOAL.DEMO</b>                                             |\n| <b>BankB</b>                                                                       | Barclays BARX.DEMO       | CITIBANK DEMO                                                                 |\n| COBA.DEMO                                                                          | <b>COBA FRA DRESDNER</b> | <b>HSBC.DEMO</b>                                                              |\n| JPMORGAN.DEMO                                                                      | PEBANKEMEA1.TEST         | RBS.LND.DEMO                                                                  |\n| SEB.DEMO                                                                           | Scotia Capital.TEST      | Unicredit.DEMO                                                                |\n| Select All Unselect All                                                            |                          |                                                                               |\n|                                                                                    |                          | Cancel<br>Create<br>Place                                                     |\n\n<span id=\"page-25-0\"></span>**Figure 21 OCO Order**\n\n![](_page_26_Picture_0.jpeg)\n\n## <span id=\"page-26-0\"></span>**4.1.7 If-Done Order**\n\nAn If-Done order is a combination of two or more orders whereby the second order or order block becomes active only when the first order has been executed.\n\nThe order type for the first leg and the second leg can be a limit order or a stop order. The second leg can also be an OCO order. The second leg remains passive until the first leg has been executed. Once the second leg has been executed, the order is completed.\n\nA check with the current market data helps to avoid the placement of an order at a limit rate which is at the market or \"on the wrong side\" of the market rate.\n\nThe Place button will only be activated when all the order details are complete.\n\nIn case OCO order is defined as a second leg, the user must define the details of the OCO order in a separate window which opens after clicking on the OCO legs summary icon.\n\n|                                                                                                                                        | <b>Order Creation</b>                                                                                                                                                                                                                                        |                             |\n|----------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------|\n|                                                                                                                                        | If-Done Order $\\vee$                                                                                                                                                                                                                                         |                             |\n| Order Details                                                                                                                          | Trade As<br>SubsidiaryHTAS1<br>$\\checkmark$<br>Custom Fields<br>╱                                                                                                                                                                                            |                             |\n| Order Type<br>Limit Order<br>Buy<br>근<br>EUR $\\vee$ $\\bullet$<br>USD V<br>Sell<br>Effective Date<br>Spot<br>Limit Spot Rate<br>1.11730 | Order Type<br>OCO Order<br>$\\checkmark$<br>1,000<br>Leg 1: Limit Order (1.11735)<br>Leg 2: Stop Order (1.11735)<br>O<br>(1)<br>Thu, 08.08.2019<br>$\\checkmark$<br>No rolling<br>Market Rate<br>1.11743<br>Expiry<br>(11)<br>GTC $\\vee$ Tue, 06.08.2019 00:00 | ⋋                           |\n| Provider List<br>$\\Box$ GroupH                                                                                                         | Comments                                                                                                                                                                                                                                                     | g<br>$\\overline{\\boxtimes}$ |\n|                                                                                                                                        | Create<br>Cancel                                                                                                                                                                                                                                             | Place                       |\n\n<span id=\"page-26-1\"></span>**Figure 22 If-Done Order with OCO Order leg**\n\nThe user can either directly place or create the If-Done Order or navigate back to the main Order definition window by clicking on the \"If-Done Order\" text in the green header area.\n\n![](_page_27_Picture_0.jpeg)\n\n| <b>Order Creation</b>                                                                               |                                                                            |\n|-----------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------|\n| < If-Done Order / OCO Order                                                                         |                                                                            |\n| Trade As<br>SubsidiaryHTAS1                                                                         |                                                                            |\n| Order Type                                                                                          | Order Type                                                                 |\n| Limit Order                                                                                         | Stop Order                                                                 |\n| <b>Buy</b><br>$EUR \\vee 0$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>≓<br>USD $\\vee$ 0<br>Sell | <b>Buy</b><br>$EUR \\vee 0$<br>900<br>$\\Rightarrow$<br>$USD \\vee 0$<br>Sell |\n| Effective Date                                                                                      | Effective Date                                                             |\n| <br>Thu, 08.08.2019<br>Spot<br>$\\checkmark$                                                         | Thu, 08.08.2019<br>雦<br>Spot<br>$\\checkmark$                               |\n| No rolling<br>Limit Spot Rate                                                                       | No rolling                                                                 |\n| Market Rate<br>1.11735<br>1.11781                                                                   | Stop Spot Rate<br>Market Rate<br>1.11765<br>1.11781                        |\n| Provider List<br>C Comments<br>GroupH                                                               | હ                                                                          |\n|                                                                                                     | ⊠                                                                          |\n|                                                                                                     | Create<br>Place<br>Cancel                                                  |\n\n<span id=\"page-27-1\"></span>**Figure 23 OCO Order as If-Done Order leg.**\n\n#### <span id=\"page-27-0\"></span>**4.1.8 Loop order**\n\nA loop order is a combination of two orders where the second order is activated only once the first order is executed. The difference to an If-Done order is that the first order is deactivated after its execution and reactivated again once the second order is executed. A loop order consists of either two take-profit limit orders or two stop-loss orders, for the same amount but in opposite directions. At initial input, Leg 1 is considered active and Leg 2 is considered passive. The status of Leg 2 will only become active once Leg 1 has been triggered and subsequently executed. Leg 1 will then become passive until Leg 2 is triggered and executed, when Leg 1 will again become active and Leg 2 returns to passive status and so forth. Only one Leg is active at any given time, the other leg being passive. A loop order is a perpetual order and will continue until either automatic or manual expiry.\n\n![](_page_28_Picture_0.jpeg)\n\n| <b>Order Creation</b><br>Loop Order $\\vee$ |                           |\n|--------------------------------------------|---------------------------|\n| / Custom Fields<br>Order Details           |                           |\n| Order Type (first to be active)            | Order Type                |\n| Limit Order                                | Limit Order               |\n| <b>Buy</b>                                 | Sell                      |\n| $EUR \\vee$                                 | $EUR \\vee$                |\n| Notional                                   | ٠                         |\n| ٠                                          | Notional                  |\n| €                                          | $\\rightleftharpoons$      |\n| USD $\\vee$                                 | USD $\\vee$                |\n| Sell                                       | <b>Buy</b>                |\n| $\\circ$                                    | $\\circ$                   |\n| <b>Effective Date</b>                      | <b>Effective Date</b>     |\n| Tue, 07.07.2020                            | Tue, 07.07.2020           |\n| 篇                                          | Spot                      |\n| Spot                                       | 篇                         |\n| $\\checkmark$                               | $\\checkmark$              |\n| No rolling                                 | No rolling                |\n| <b>Limit Spot Rate</b>                     | Limit Spot Rate           |\n| Market Rate                                | <b>Market Rate</b>        |\n| $1.12418$ $\\oplus$                         | 1.12418                   |\n| 1.12282                                    | 1.12273                   |\n| Expiry<br>GTC $\\vee$ Fri, 03.07.2020 00:00 |                           |\n| Provider List<br>Comments                  |                           |\n| BOAL.DEMO                                  | 응                         |\n| Barclays BARX.DEMO                         | CITIBANK.DEMO             |\n| COBA.DEMO                                  | RBS.LND.DEMO              |\n| <b>ICD.TEST</b>                            | ☑                         |\n| TB-HSBC.DEMO                               | $\\boxed{\\boxtimes}$       |\n|                                            | Cancel<br>Create<br>Place |\n\n<span id=\"page-28-0\"></span>**Figure 24 Loop order**\n\n![](_page_29_Picture_0.jpeg)\n\n## <span id=\"page-29-0\"></span>**4.1.9 Algorithmic (Algo) order**\n\nAn algorithmic (algo) order is an order which executes based on a specific set of computerized rules collectively known as an 'algorithm strategy'. The rules are based on timing, pricing and quantity. There are a number of providers (market makers) offering access to their proprietary algorithmic strategies via 360T for FX Spot, FX Forward and NDF products.\n\n|                                  | <b>Order Creation</b><br>Algo Order $\\vee$                          |                                                      |  |\n|----------------------------------|---------------------------------------------------------------------|------------------------------------------------------|--|\n|                                  | <b>Trade As</b><br>GroupE                                           |                                                      |  |\n| Order Details                    | / Custom Fields                                                     |                                                      |  |\n|                                  | $EUR \\vee$<br><b>Buy</b><br>٠<br>€<br>$USD \\vee$<br>Sell<br>$\\circ$ | Notional                                             |  |\n|                                  | Market Rate                                                         | 1.13490                                              |  |\n| <b>Provider</b>                  |                                                                     | Strategy                                             |  |\n| BNPP.DEMO                        |                                                                     | bnpp-iguana-test                                     |  |\n| Protection<br><b>Limit Price</b> | logic that enables it to react favourably to market movements.      |                                                      |  |\n|                                  | <b>Advanced Strategy Parameters</b>                                 |                                                      |  |\n| Stop Rate                        |                                                                     |                                                      |  |\n|                                  | $1,13490$ $\\oplus$                                                  |                                                      |  |\n| <b>Internal Liquidity</b>        |                                                                     | <b>iX Match</b>                                      |  |\n| ○ 0) Disabled                    |                                                                     | Disabled<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |  |\n| Rapid Fill                       |                                                                     |                                                      |  |\n| 0) Disabled                      |                                                                     |                                                      |  |\n|                                  |                                                                     |                                                      |  |\n|                                  |                                                                     | Create<br>Cancel<br>Place                            |  |\n\n<span id=\"page-29-2\"></span><span id=\"page-29-1\"></span>**Figure 25 Algo Order**\n\n![](_page_30_Picture_0.jpeg)\n\n## <span id=\"page-30-0\"></span>**4.1.10 Slice order**\n\nA slice order is a mechanism for a client to break a larger order into a series of RFS requests to spread the ticket across their providers.\n\nIt is designed to reduce the workflow burden on the client, who does not have to create x tickets to do x executions or return to the platform after a period of time to spread their execution out.\n\nWhen placed, this order triggers an auto-execution algorithm based on pre-defined execution parameters, reason why when such orders are in progress, they remain in Initialized state.\n\n| <b>Order Creation</b><br><b>Slice Order</b>                                                                                                                                                                                                                                                                                                    |\n|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Product<br>Swap                                                                                                                                                                                                                                                                                                                                |\n| E/ Custom Fields<br><b>Order Details</b><br>Near Leg<br>Far Leg                                                                                                                                                                                                                                                                                |\n| $EUR \\vee$<br>500,000,000<br>Sell<br>$EUR \\vee$<br><b>Buy</b><br>500,000,000<br>$\\rightleftharpoons$<br>≓<br>$USD \\vee$<br>$USD \\vee$<br><b>Sell</b><br><b>Buy</b>                                                                                                                                                                             |\n| <b>Effective Date</b><br><b>Maturity Date</b><br>3 Months<br>Tue 22-02-2022<br>$\\checkmark$<br>Fri 19-11-2021<br>雦<br>雦<br>Spot<br>$\\checkmark$                                                                                                                                                                                                |\n| No Worse Than<br><b>Total Number of Executions</b><br>5<br>25.650<br><b>Market Swap Points</b><br>Trade with LP> once<br>28.500<br>vo                                                                                                                                                                                                          |\n| <b>Trade Size per Execution</b><br><b>LPs per Request</b><br>Gap between Requests<br><b>O</b> All<br>$\\bigcirc$ Equal<br>$\\bullet$ Seconds ( $\\geq 10$ )<br>10<br>Fixed $(23)$<br>$\\bullet$ Random $(+/-20%)$<br>Random (10s-180s)<br>Random                                                                                                   |\n| <b>Provider List</b><br><b>Comments</b>                                                                                                                                                                                                                                                                                                        |\n| હ<br><b>ABC.BANK</b><br>ABN AMRO.DEMO<br><b>BANKWINTER.TEST</b><br><b>BNPP,DEMO</b><br>BOAL.DEMO<br>Bank of Ireland.TEST<br>⊠<br>Barclays BARX.DEMO<br><b>BankD</b><br><b>Barclays SEF.DEMO</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>CITIBANK.DEMO<br><b>DB.DEMO</b><br>JPMORGAN.DEMO<br>HSBC.DEMO<br><b>JPM.GTX</b><br>RBS.LND.DEMO |\n| <b>Regulatory Disclosures</b><br>Create<br><b>Place</b><br>Cancel                                                                                                                                                                                                                                                                              |\n\n<span id=\"page-30-1\"></span>**Figure 26 Slice Order**\n\nThe products in scope are FX Swaps (even amounts only) and FX Forwards. The minimum executable amounts are \\$10mm for FX Forwards and \\$50mm for FX Swaps. Similar to other order types, it can be initially created or drafted to be placed or triggered later. Placing a slice\n\n![](_page_31_Picture_0.jpeg)\n\n- **No worse than:** This is the worst price that the client expects to execute on. If the request returns prices which reach this threshold, the execution is stopped. All previously executed slices will be shown in trade blotter as executed trades. The remaining, not executed notional, can be sent as RFS to selected providers.\n- **Total Number of Executions:** This is the number of slices into which one wants to break the total amount. Maximum value which can be set is 10, minimum is 2 and the default value is 5.\n- **Trade with LP> once** (LP = Liquidity Provider): This is a toggle between Yes and No. If set to Yes, it means if the first execution was with Bank A, then Bank A can still be in the second request. If set to No, Bank A is removed from all subsequent executions until completion.\n- **LPs per Request:**\n  - ➢ All: All ticked LPs in the Bank Basket on every request (subject to Trade with LP> once setting)\n  - ➢ Fixed (≥3): On every request there will be a pre-defined number of randomly selected LPs from the ticked LPs in the Bank Basket. A minimum number of 8 selected LPs in the Bank Baskets required. The minimum are 3 providers, the maximum is limited to the ticked providers in the Bank Basket.\n  - ➢ Random [Default]: A random number of LPs on each request. Minimum are 3 providers, maximum is the selected Bank Basket.\n- **Trade size per execution:**\n  - ➢ Equal: Total trade size / Total number of executions\n  - ➢ Random [Default]: Total trade size / Total number of executions but randomly adjusted by +/- 20% size on each execution, equalling the total trade size.\n\nFor example: \\$500mm trade size in 5 slices could be: \\$100mm, \\$80mm, \\$110mm, \\$105mm, \\$105mm. In this case each slice would be greater than \\$80mm and less than \\$120mm.\n\n- **Gap between requests** (time between slices):\n  - ➢ Fixed: Client can select a number of seconds between 10 and 180.\n  - ➢ Random [Default]: This will randomly vary the time between executions between 10 and 180 seconds.\n\n#### **Other parameters:**\n\n- ➢ Market Rate (for Forward) / Market Swap Points (for Swap): This is an indicative feed of the prevailing market in that CCY and product.\n- ➢ Provider List: based on client's RFS bank basket. The user must click on the providers that should be considered for each individual RFS according to what was defined by parameter LPs per Request.\n\n**Validations:** Some of the execution parameters are validated prior to order creation or placement, as follows:\n\n![](_page_32_Picture_0.jpeg)\n\nLPs per Request = Fixed (≥3)\n\nMinimum number of providers clicked is 8 for this option.\n\nFixed number must be greater than or equal to 3. It also must be a number between 50-100% of the number of providers selected. Example: 8 providers clicked means fixed number must be\n\nbetween 4 and 8.\n\n| <b>Order Creation</b>                                                                                                                   |   |\n|-----------------------------------------------------------------------------------------------------------------------------------------|---|\n| <b>Slice Order</b>                                                                                                                      |   |\n|                                                                                                                                         |   |\n| Product<br>Swap                                                                                                                         |   |\n|                                                                                                                                         |   |\n| ₹ Custom Fields<br><b>Order Details</b>                                                                                                 |   |\n|                                                                                                                                         |   |\n| Near Leg<br>Far Leg                                                                                                                     |   |\n| $EUR \\vee$<br><b>Sell</b><br>$EUR \\vee$<br><b>Buy</b><br>500,000,000<br>500,000,000<br>e<br>ਵ                                           |   |\n| $USD \\vee$<br>$USD \\vee$<br>Sell<br><b>Buy</b>                                                                                          |   |\n| <b>Effective Date</b><br><b>Maturity Date</b>                                                                                           |   |\n| 雦<br>3 Months<br>Tue 22-02-2022<br>Fri 19-11-2021<br>雦<br>Spot                                                                          |   |\n| <b>Total Number of Executions</b><br>No Worse Than<br>25.650<br>5.                                                                      |   |\n| <b>Market Swap Points</b><br>28.500<br>Trade with LP> once                                                                              |   |\n|                                                                                                                                         |   |\n| <b>LPs per Request</b><br><b>Trade Size per Execution</b><br>Gap between Requests                                                       |   |\n| $\\bigcap$ All<br>Eaual<br>$\\bullet$ Seconds ( $\\geq 10$ )<br>10 <sup>1</sup>                                                            |   |\n| $\\bullet$ Fixed ( $\\geq$ 3)<br>Random (+/-20%)<br>Random (10s-180s)<br>$\\overline{2}$                                                   |   |\n| Random                                                                                                                                  |   |\n|                                                                                                                                         |   |\n| <b>Provider List</b><br><b>Comments</b>                                                                                                 |   |\n| ABC.BANK<br>ABN AMRO.DEMO<br>BANKWINTER.TEST                                                                                            | ☑ |\n| <b>BNPP.DEMO</b><br><b>M</b> BOAL, DEMO<br>Bank of Ireland.TEST                                                                         | ⊠ |\n| <b>BankD</b><br>Barclays BARX.DEMO<br>Barclays SEF.DEMO                                                                                 |   |\n| <b>V CITIBANK.DEMO</b><br>Credit Suisse.DEMO<br><b>M</b> DB.DEMO<br>HSBC.DEMO<br>$\\sqrt{ }$ JPM.GTX<br>V JPMORGAN.DEMO                  |   |\n| RBS.LND.DEMO                                                                                                                            |   |\n|                                                                                                                                         |   |\n| <b>Regulatory Disclosures</b>                                                                                                           |   |\n| LPs per Request should be greater than or equal to 3<br>Fixed number of LPs per Request is not valid. 'Fixed (≥3)' should be at least 8 |   |\n|                                                                                                                                         |   |\n| Cancel<br>Create<br><b>Place</b>                                                                                                        |   |\n\n<span id=\"page-32-0\"></span>**Figure 27 Slice Order LPs per Request validation**\n\n![](_page_33_Picture_0.jpeg)\n\n|                                                                 | Product                                  |                                                               |             |\n|-----------------------------------------------------------------|------------------------------------------|---------------------------------------------------------------|-------------|\n|                                                                 | Swap                                     |                                                               |             |\n| E Custom Fields<br><b>Order Details</b>                         |                                          |                                                               |             |\n| Near Leg<br><b>Buy</b><br>$EUR \\vee$<br>≓<br>USD $\\vee$<br>Sell | 500,000,000<br>Sell<br>≓<br><b>Buy</b>   | Far Leg<br>$EUR \\vee$<br>500,000,000<br>$USD \\vee$            |             |\n| <b>Effective Date</b>                                           | <b>Maturity Date</b>                     |                                                               |             |\n| Spot<br>$\\checkmark$                                            | Fri 19-11-2021<br>雦<br>3 Months          | Tue 22-02-2022<br>雦                                           |             |\n| No Worse Than<br><b>Market Swap Points</b>                      | 25.650<br>28,500                         | <b>Total Number of Executions</b><br>5<br>Trade with LP> once |             |\n| <b>LPs per Request</b>                                          | <b>Trade Size per Execution</b>          | Gap between Requests                                          |             |\n| $\\bigcirc$ all<br>Fixed $(23)$<br><b>O</b> Random               | Equal<br>$\\bullet$ Random (+/-20%)       | $\\bullet$ Seconds ( $\\geq 10$ )<br>Random (10s-180s)          | 5           |\n| <b>Provider List</b><br><b>Comments</b>                         |                                          |                                                               |             |\n| ABC.BANK<br><b>BNPP.DEMO</b>                                    | A ABN AMRO.DEMO                          | BANKWINTER.TEST                                               | ☑           |\n| BankD                                                           | <b>V BOAL DEMO</b><br>Barclays BARX.DEMO | Bank of Ireland.TEST<br>Barclays SEF.DEMO                     | $\\boxtimes$ |\n| CITIBANK.DEMO                                                   | Credit Suisse.DEMO                       | $\\nabla$ DB.DEMO                                              |             |\n| HSBC.DEMO                                                       | $\\sqrt{ }$ JPM.GTX                       | V JPMORGAN.DEMO                                               |             |\n| RBS.LND.DEMO                                                    |                                          |                                                               |             |\n|                                                                 |                                          | <b>Regulatory Disclosures</b>                                 |             |\n\nGap between Requests Number of seconds must be between 10 and 180.\n\n<span id=\"page-33-0\"></span>**Figure 28 Slice Order Gap between Requests validation**\n\n![](_page_34_Picture_0.jpeg)\n\n## <span id=\"page-34-0\"></span>**4.1.11 Call order**\n\nA Call order (also Call Level order) is an order to the counterparty to call the order placer when a specific rate is reached.\n\nUnlike other FX orders, Call orders do not have an amount nor a type (e.g. take profit, stop loss). The fields of Call Level orders are the same as limit orders.\n\nThe workflow (Create, Place, Withdraw, Amend) is identical with Limit orders. Once placed and accepted by the provider, the order is displayed in the Accepted orders tab with a status of Active until it is triggered and then 'executed' by the liquidity provider or expired or withdrawn/cancelled by the requester.\n\nOnce the level is reached and the provider has called the customer (offline 360T), the provider 'executes' the Call order. The result is NOT a spot trade. The ticket title should be \"Call Order Notification\".\n\n|                                                | <b>Order Creation</b><br>Call Order $\\sim$                                                                                                                                                         | $\\times$                                           |\n|------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------|\n| Order Details                                  | / Custom Fields<br>$EUR \\vee 0$<br>Buy<br>근<br>Sell<br>$($ USD $\\vee$<br>Call Rate<br>Market Rate<br>$\\bigodot$ 1,19240 $\\bigodot$ 1.19247<br>Expiry<br>(前)<br>$GTC \\sim$<br>Thu, 18.03.2021 00:00 |                                                    |\n| Provider List<br>360TBANK.TEST<br><b>BankD</b> | □ Comments<br><b>BOAL.DEMO</b><br><b>BankC</b><br>Unicredit.DEMO<br><b>BankH</b>                                                                                                                   | $\\stackrel{\\circ}{\\circ\\!\\!-}$<br>હ<br>$\\boxtimes$ |\n|                                                | Create<br>Cancel                                                                                                                                                                                   | Place                                              |\n\n<span id=\"page-34-1\"></span>**Figure 29 Call Order**\n\n![](_page_35_Picture_0.jpeg)\n\n## <span id=\"page-35-0\"></span>**4.2 Initialized Order**\n\nIn the **Initialized** tab the user can find all orders that have not yet been placed. These are instead saved for later placement by using the Create button in the Order Creation window.\n\nDepending on the order type and user permissions, the following actions are available:\n\n- Open Popup Screen\n- Amend\n- Send as RFS (available for market orders; opens RFS Live Pricing product definition window)\n- Place (MTF related parameters of a market order are neglected if placed as an order given that MTF negotiations can only occur via RFS)\n- Delete\n- Print : The printout will include all order details including the changes log (order and request changes).\n\n| Initialized (44)<br><b>Type</b>             | Sent $(1)$ | l±<br>lQ I<br>Reference $\\# \\wedge$ Order Status |             | <b>Status</b> | <b>Legal Entity</b> | Requester A Currencies        |         | Product             | Notional A           | Notional Cu | Rate | E                      |                                                                                               | <b>Executions</b> |\n|---------------------------------------------|------------|--------------------------------------------------|-------------|---------------|---------------------|-------------------------------|---------|---------------------|----------------------|-------------|------|------------------------|-----------------------------------------------------------------------------------------------|-------------------|\n| <b>Limit Order</b>                          |            | <b>@</b> EMSO-1798                               | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 666,666.00           | <b>EUR</b>  |      |                        | 1.12899 G □ シ > → 面 量                                                                         |                   |\n| <b>Market Order</b>                         |            | · EMSO-1797.                                     | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot MILE</b> | 13.00 EUR EUR        |             |      |                        | $\\begin{array}{ c c c c c c c c c c c c c c c c c c c$<br>$\\overline{\\bullet}$                |                   |\n| <b>Market Order</b>                         |            | $\\bullet$ EMSO-1795.                             | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | <b>7.00 EUR EUR</b>  |             |      |                        | $\\Box$ G $\\Box$ J $\\Box$ D $\\rightarrow$ l $\\Box$<br>÷                                        |                   |\n| <b>Market Order</b>                         |            | · EMSO-1795.                                     | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 12,000.00 E EUR      |             |      |                        | $G \\cup V \\cup V \\rightarrow V$<br>÷                                                          |                   |\n| $\\vee$ If-Done Order                        |            | · EMSO-1795.                                     |             | Initialized   | GroupH              |                               |         |                     |                      |             |      | G @ E D                | → 1 面                                                                                         |                   |\n| <b>Algo Order</b>                           |            | <b>@</b> EMSO-1795                               | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 1,000,000.0 EUR      |             |      | $G \\oplus \\mathbb{R}$  | a<br>→ 1 面                                                                                    |                   |\n| $\\vee$ OCO Order                            |            | · EMSO-1795.                                     |             | Initialized   | GroupH              |                               |         |                     |                      |             |      | $G \\oplus \\mathcal{V}$ | e<br>Û<br>$\\rightarrow$                                                                       |                   |\n| <b>Stop Order</b>                           |            | <b>•</b> EMSO-1795                               | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 1,603.00 EUR EUR     |             |      |                        | 1.13259 G □ ジ ▷ 쉬 Ⅲ<br>a                                                                      |                   |\n| <b>Market Order</b>                         |            | · EMSO-1795.                                     | Initialized | Initialized   | GroupH              | Sell                          | EUR/USD | <b>FX Spot</b>      | 1,000,000.0          | <b>USD</b>  |      |                        | BG@ジD→I 亩<br>$\\overline{a}$                                                                   |                   |\n| <b>Market Order</b>                         |            | · EMSO-1784.                                     | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 1,000,000.0          | <b>EUR</b>  |      |                        | $G \\oplus \\mathcal{V} \\upharpoonright \\triangleright \\mathcal{V} \\upharpoonright \\mathcal{V}$ |                   |\n| <b>Market Order</b>                         |            | · EMSO-1702                                      | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 1.000.000.0. EUR     |             |      |                        | $G \\cup \\mathcal{V} \\cup \\mathcal{V} \\rightarrow \\mathcal{V}$<br>晨                            |                   |\n| <b>Limit Order</b>                          |            | · EMSO-1655.                                     | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 300.000.00.          | <b>EUR</b>  |      |                        | 1.12896 G Q シ > - 1<br>昌                                                                      |                   |\n| $\\vee$ OCO Order                            |            | • EMSO-1655.                                     |             | Initialized   | GroupH              |                               |         |                     |                      |             |      |                        | $G \\oplus \\mathscr{A} \\rightarrow \\mathring{\\mathbf{m}}$<br>E                                 |                   |\n| <b>Market Order</b>                         |            | · EMSO-1655.                                     | Initialized | Initialized   | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 7,006.00 EUR EUR     |             |      |                        | $G \\cup \\mathcal{P} \\cup \\mathcal{P} \\rightarrow \\mathcal{P}$                                 |                   |\n| <b>Market Order</b>                         |            | · EMSO-1655                                      | Initialized | Initialized   | GroupH              | <b>Buy</b>                    | EUR/USD | <b>FX Spot</b>      | 4.009.00 EUR EUR     |             |      |                        | $\\begin{array}{ c c c c c c c c c c c c c c c c c c c$<br>$\\overline{a}$                      |                   |\n| Accepted (26) $\\overline{Q}$<br><b>Type</b> |            | Œ<br>Reference $\\# \\wedge$ Order Status          |             | <b>Status</b> | <b>Legal Entity</b> | <b>Requester A Currencies</b> |         | Product             | Notional A           | Notional Cu | Rate | <b>Expiry Type</b>     |                                                                                               |                   |\n| Market Order                                |            | EMSO-1869.                                       | Active      | Accepted      | GroupH              | Buy                           | EUR/INR | <b>FX Spot</b>      | <b>60.00 EUR EUR</b> |             |      | <b>GTC</b>             | $\\Box$ $\\Box$ $\\Box$                                                                          |                   |\n| <b>Market Order</b>                         |            | EMSO-1868.                                       | Active      | Accepted      | GroupH              | Sell                          | EUR/GBP | FX Fo MIF           | 333.00 EUR EUR       |             |      | <b>GTD</b>             | $\\Box$ $\\Box$                                                                                 |                   |\n| <b>Market Order</b>                         |            | EMSO-1868.                                       | Active      | Accepted      | GroupH              | Sell                          | EUR/GBP | FX Fo., MIF         | 1.000.000.0. EUR     |             |      | <b>GTD</b>             | $\\circ$ $\\circ$ $\\circ$                                                                       |                   |\n| <b>Market Order</b>                         |            | EMSO-1868.                                       | Active      | Accepted      | GroupH              | Sell                          | EUR/CHF | FX Fo MIF           | 5,000,000.0          | EUR         |      | <b>GTD</b>             | $C \\otimes C$                                                                                 |                   |\n| <b>Market Order</b>                         |            | EMSO-1868.                                       | Active      | Accepted      | GroupH              | Sell                          | EUR/USD | FX Fo., MIF         | 3.000.000.0          | <b>EUR</b>  |      | <b>GTD</b>             | $\\circ$ $\\circ$ $\\circ$                                                                       |                   |\n| <b>Market Order</b>                         |            | EMSO-1868.                                       | Active      | Accepted      | GroupH              | Sell                          | EUR/USD | FX Fo MIF           | 4.000.000.0          | EUR         |      | <b>GTD</b>             | $\\circ$ 3 $\\circ$                                                                             |                   |\n| <b>Market Order</b>                         |            | EMSO-1868.                                       | Active      | Accepted      | GroupH              | Sell                          | EUR/USD | FX Fo MTF           | 444.00 EUR EUR       |             |      | <b>GTD</b>             | $\\circ$ $\\circ$ $\\circ$                                                                       |                   |\n| <b>Market Order</b>                         |            | EMSO-1859.                                       | Active      | Accepted      | GroupH              | Sell                          | CAD/JPY | FX Fo MIF           | 3,000,000.0 CAD      |             |      | <b>GTC</b>             | $\\circledcirc$ $\\circledcirc$                                                                 |                   |\n| <b>Market Order</b>                         |            | EMSO-1859.                                       | Active      | Accepted      | GroupH              | Buy                           | CAD/JPY | FX Fo MTF           | 3,000,000.0          | CAD         |      | <b>GTC</b>             | $\\circ$ $\\circ$ $\\circ$                                                                       |                   |\n| <b>Market Order</b>                         |            | EMSO-1859.                                       | Active      | Accepted      | GroupH              | Sell                          | CAD/JPY | FX Fo., MIF         | 2.000.000.0. CAD     |             |      | <b>GTC</b>             | $\\Box$                                                                                        |                   |\n| <b>Market Order</b>                         |            | EMSO-1842                                        | Active      | Pending Ca    | GroupH              | Sell                          | EUR/GBP | FX Fo MIF           | 1,000,000.0 EUR      |             |      | <b>GTD</b>             | $\\Theta$ $\\otimes$ $\\Theta$                                                                   |                   |\n| <b>Market Order</b>                         |            | EMSO-1835                                        | Active      | Pending Ca    | GroupH              | Sell                          | EUR/USD | FX Fo MITE          | 2,000,000.0          | <b>EUR</b>  |      | <b>GTD</b>             | $\\Box$                                                                                        |                   |\n|                                             |            | EMSO-1816                                        | Active      | Pending Ca    | GroupH              | Sell                          | EUR/USD | FX Fo MIF           | 1.000.000.0. EUR     |             |      | <b>GTD</b>             | $\\mathbb{P}\\left[\\alpha\\right]$ $\\oplus$                                                      |                   |\n| <b>Market Order</b>                         |            | EMSO-1816.                                       | Active      | Accepted      | GroupH              | Buy                           | EUR/USD | <b>FX Spot</b>      | 4,000.00 EUR EUR     |             |      | <b>GTC</b>             | $\\circ$ $\\circ$ $\\circ$                                                                       |                   |\n| <b>Market Order</b>                         |            |                                                  |             |               |                     |                               |         |                     |                      |             |      |                        |                                                                                               |                   |\n\n<span id=\"page-35-1\"></span>**Figure 30 Initialized Tab: Actions area**\n\nEach order can be opened in a pop-up window by clicking on the Open Popup Screen icon (detached window) or by clicking on the desired order (window attached to the actions area).\n\n![](_page_36_Picture_0.jpeg)\n\n|                | <b>RFS LIVE PRICING</b>         | <b>DEAL TRACKING</b>        |                     | <b>ORDER BOOK</b> | $+$  |                      |                  |                                                                              |                                          |                        |                                       |\n|----------------|---------------------------------|-----------------------------|---------------------|-------------------|------|----------------------|------------------|------------------------------------------------------------------------------|------------------------------------------|------------------------|---------------------------------------|\n|                | Initialized (34)                | 飞<br>Q                      |                     |                   |      |                      |                  |                                                                              |                                          |                        | <b>Order View</b>                     |\n| $\\mathsf{E}^+$ |                                 | Sent (3)                    |                     |                   |      |                      |                  |                                                                              |                                          |                        |                                       |\n|                | <b>Type</b>                     | Reference #<br>$\\checkmark$ | <b>Order Status</b> | <b>Status</b>     |      |                      |                  |                                                                              |                                          |                        | OCO Order                             |\n|                | <b>Limit Order</b>              | EMSO-1318366                | Initialized         | Initialized       | 0 ₹∕ |                      |                  | →Ⅰ 俞                                                                         |                                          |                        | Initialized                           |\n|                | <b>Limit Order</b>              | EMSO-1318365                | Initialized         | Initialized       | ロジ   |                      |                  | →   ■                                                                        |                                          |                        | Stop Order                            |\n|                | <b>Fixing Order</b>             | EMSO-1317488                | Initialized         | Initialized       | P    |                      |                  | $\\rightarrow$                                                                | 一面                                       | <b>Order Details</b>   | Order Changes<br>e                    |\n|                | Market Order @                  | EMSO-1312547                | Initialized         | Initialized       | ロジ   |                      | $\\triangleright$ | →  血                                                                         |                                          |                        |                                       |\n|                | Market Order ·                  | EMSO-1312545                | Initialized         | Initialized       | ロシ   |                      | $\\triangleright$ | →Ⅰ 血                                                                         |                                          | Product                | <b>FxSpot</b><br>I Buy EUR / Sell USD |\n|                | Market Order ·                  | EMSO-1312275                | Initialized         | Initialized       |      | ロシ                   | $\\triangleright$ | → 面                                                                          |                                          | Action<br>Notional     | 1.231.00 EUR                          |\n|                | <b>Limit Order</b><br>$\\bullet$ | EMSO-1312274                | Initialized         | Initialized       | e    | - 57                 |                  | $\\rightarrow$                                                                | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                        |                                       |\n|                | <b>Stop Order</b>               | EMSO-1312273                | Initialized         | Initialized       | 回 シ  |                      |                  | $\\rightarrow$                                                                | 面                                        | <b>Stop Spot Rate</b>  | 1.23879                               |\n|                | $\\wedge$ OCO Order              | EMSO-131227                 |                     | Initialized       | 回シ   |                      |                  | $\\rightarrow$                                                                |                                          | <b>Market Rate</b>     | 1.21147                               |\n|                | <b>Stop Order</b>               | EMSO-131227                 | Initialized         | Initialized       | o    | 57                   |                  | $D \\rightarrow \\mathbb{R}$                                                   |                                          |                        |                                       |\n|                | <b>Limit Order</b>              | EMSO-131227                 | Initialized         | Initialized       | c    | ₩                    |                  | $D \\rightarrow$ If                                                           |                                          | Order ID               | EMSO-1312270/1                        |\n|                | Fixing Order $\\bullet$          | EMSO-1312268                | Initialized         | Initialized       | 回ジ   |                      |                  | $\\triangleright$ $\\rightarrow$ $\\parallel$ $\\stackrel{\\frown}{\\blacksquare}$ |                                          |                        |                                       |\n|                | Market Order $\\bullet$          | EMSO-1312267                | Initialized         | Initialized       | ıO.  | - 57                 | $\\triangleright$ | →  间                                                                         |                                          |                        | Limit Order                           |\n|                | <b>Stop Order</b><br>$\\epsilon$ | EMSO-1312266                | Initialized         | Initialized       | ロシ   |                      |                  | $\\triangleright$ $\\rightarrow$ $\\parallel$ $\\stackrel{\\frown}{\\parallel}$    |                                          | <b>Order Details</b>   | IO.<br>Order Changes                  |\n|                | Market Order <sup>®</sup>       | EMSO-1312250                | Initialized         | Initialized       | 0 ₹  |                      |                  | $D \\rightarrow    \\hat{m}$                                                   |                                          | Product                | <b>FxSpot</b>                         |\n|                | <b>Limit Order</b>              | <b>EMSO-1312186</b>         | Initialized         | Initialized       |      | $\\Box$ $\\Box$ $\\Box$ |                  |                                                                              |                                          | <b>Action</b>          | I Buy EUR / Sell USD                  |\n|                | $\\alpha$<br>Accepted (34)       | $\\stackrel{\\smile}{\\smile}$ |                     |                   |      |                      |                  |                                                                              |                                          | Notional               | 1.231.00 EUR                          |\n|                | <b>Type</b>                     | Reference #                 | <b>Order Status</b> | <b>Status</b>     |      | <b>Legal Er</b>      |                  |                                                                              |                                          | <b>Limit Spot Rate</b> | 1.18002                               |\n|                | <b>Limit Order</b>              | EMSO-1317496                | Active              | Pending Ca.       |      | GroupE. C            |                  |                                                                              |                                          | <b>Market Rate</b>     | 1.21151                               |\n|                | <b>Fixing Order</b>             | EMSO-1317166                | Active              | Accepted          |      |                      |                  | GroupE $\\Box$ $\\boxtimes$                                                    |                                          |                        |                                       |\n|                | <b>Fixing Order</b>             | EMSO-1317165                | Active              | Accepted          |      |                      |                  | GroupE $\\Box$ $\\oslash$                                                      |                                          | Order ID               | EMSO-1312271/1                        |\n|                | <b>Market Order</b>             | EMSO-1316214                | Active              | Accepted          |      |                      |                  | GroupE $\\Box$ $\\oslash$                                                      |                                          |                        |                                       |\n|                | <b>Market Order</b>             | EMSO-1313543                | Active              | Accepted          |      |                      |                  | GroupE $\\Box \\oslash$                                                        |                                          | <b>Expiry</b>          | <b>GTC</b>                            |\n|                | $\\vee$ OCO Order                | EMSO-1313540                |                     | Accepted          |      |                      |                  | GroupE $\\Box$ $\\boxtimes$                                                    |                                          | Order ID               | EMSO-1312272/1                        |\n|                | $\\vee$ OCO Order                | EMSO-1312625                |                     | Accepted          |      |                      |                  | GroupE $\\Box$ $\\Diamond$                                                     |                                          |                        |                                       |\n|                | <b>Market Order</b>             | EMSO-1312560                | Active              | Accepted          |      |                      |                  | GroupE $\\Box$ $\\boxtimes$                                                    |                                          | Counterparts           | <b>Request Changes</b>                |\n|                | <b>Market Order</b>             | EMSO-1312550/1              | Active              | Accepted          |      |                      |                  | GroupE $\\Box$ $\\oslash$                                                      |                                          |                        |                                       |\n|                | <b>Market Order</b>             | EMSO-1312180                | Active              | Accepted          |      |                      |                  | Group $E \\cup \\otimes$                                                       |                                          | Amend                  | Place<br><b>Delete</b>                |\n|                | Market Order                    | <b>EMSO-1312179</b>         | <b>Active</b>       | Accented          |      | GroupE $\\Box$ $\\Box$ |                  |                                                                              |                                          |                        |                                       |\n\n<span id=\"page-36-0\"></span>**Figure 31 Initialized Tab: Order View**\n\nA trader has the possibility to apply an action on several orders simultaneously. After multiple orders are chosen from the 'Initialized' view, the possible action buttons are made available to the trader at the bottom of the 'Order Summary' view, as shown in red in [Figure 32.](#page-37-0)\n\n![](_page_37_Picture_0.jpeg)\n\n|                    | <b>RFS LIVE PRICING</b>                   | <b>ORDER MANAGEMENT</b>                                                                              |                            | <b>DEAL TRACKING</b>       | $+$                 |          |                        |                                   | $\\vee$ Preferences<br>$\\vee$ Help | $\\mathbb{Q}^1$ $\\bullet$ AA - $\\Box$ X<br>>0<                                       |                                                                                                      |\n|--------------------|-------------------------------------------|------------------------------------------------------------------------------------------------------|----------------------------|----------------------------|---------------------|----------|------------------------|-----------------------------------|-----------------------------------|-------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|\n| Ė                  |                                           |                                                                                                      | <b>Trade As</b>            | FundE.2                    |                     |          | $\\rightarrow$          | Order Summary (3)                 |                                   | <b>Executions</b>                                                                   | ∢                                                                                                    |\n|                    | Initialized (7)<br>Sent (0)               | L<br>$\\alpha$                                                                                        |                            |                            |                     |          |                        | <b>Stop Order</b><br>EMSO-1467440 | $\\times$<br>Initialized           | GroupE<br><b>FX Spot</b><br>Buy EUR / Sell USD                                      | $\\checkmark$<br>$\\stackrel{\\leftarrow}{\\sim}$                                                        |\n| Ê                  | Type                                      | Reference # $\\vee$ Order Status                                                                      |                            | <b>Status</b>              | $\\mathbf{I}$        |          | Product                |                                   | <b>FX Spot</b>                    | 1,000,000.00 EUR                                                                    | 6                                                                                                    |\n| $\\hat{\\mathbb{C}}$ | $\\vee$ OCO Order                          | EMSO-14674                                                                                           |                            | Initialized                | 1日ジ                 | →Ⅰ面      | Action                 |                                   | I Buy EUR / Sell USD              | @ 1.24884                                                                           | Z                                                                                                    |\n| し                  | <b>Fixing Order</b>                       | EMSO-14674                                                                                           | Initialized                | Initialized                | 1日ジ                 | →Ⅰ 面     | Notional               |                                   | 1,000,000.00 EUR<br>1.13512       | GroupE<br><b>FX Spot</b>                                                            | $\\checkmark$                                                                                         |\n|                    | <b>Stop Order</b>                         | EMSO-14674.                                                                                          | Initialized                | Initialized                | $\\mathscr{V}$<br>W  | ᅰ面<br>刘面 | <b>Stop Spot Rate</b>  |                                   |                                   | Buy EUR / Sell USD                                                                  | $\\stackrel{\\smash{\\smash{\\scriptscriptstyle\\downarrow}}}{\\smash{\\scriptscriptstyle\\downarrow}}$<br>8 |\n|                    | <b>Limit Order</b><br><b>Market Order</b> | EMSO-14674<br>EMSO-14674                                                                             | Initialized<br>Initialized | Initialized<br>Initialized | $\\mathscr{A}$       | ᅰ面       |                        | <b>Limit Order</b>                | $\\times$                          | 1,000,000.00 EUR<br>@ 1.24884                                                       | Z                                                                                                    |\n|                    | <b>Market Order</b>                       | EMSO-14674                                                                                           | Initialized                | Initialized                | 1日 ジ ト              | → 而      |                        | EMSO-1467439                      | Initialized                       |                                                                                     |                                                                                                      |\n|                    | <b>Market Order</b>                       | EMSO-14674                                                                                           | Initialized                | Initialized                | 1日 ジ ▷ 키 Ⅲ          |          | Product                |                                   | <b>FX Forward</b>                 |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          | Action                 |                                   | I Buy EUR / Sell USD              |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          | Notional               |                                   | 1,000,000.00 EUR<br>1.13504       |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          | <b>Limit Spot Rate</b> |                                   |                                   |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          |                        | <b>Market Order</b>               | $\\times$                          |                                                                                     |                                                                                                      |\n|                    | lQ.<br>Accepted (3)                       | $\\stackrel{\\smash{\\smash[b]{\\smash[b]{\\smash[b]{\\smash[b]{\\smash[b]{\\smash[b]{\\smash[b]{\\smash[b]{\\$ |                            |                            |                     |          |                        | EMSO-1467438                      | Initialized                       |                                                                                     |                                                                                                      |\n|                    | Type                                      | Reference $# \\vee$                                                                                   | <b>Order Status</b>        | <b>Status</b>              | <b>Legal Entity</b> |          | Product                |                                   | <b>FX Spot</b>                    |                                                                                     |                                                                                                      |\n|                    | <b>Market Order</b>                       | EMSO-14674                                                                                           | Active                     | Accepted                   | FundE.2             | $\\Box$   | Action                 |                                   | I Buy EUR / Sell USD              |                                                                                     |                                                                                                      |\n|                    | <b>Market Order</b>                       | EMSO-14673.                                                                                          | Active                     | Accepted                   | FundE.1             | $\\Box$   | Notional               |                                   | 333.00 EUR                        |                                                                                     |                                                                                                      |\n|                    | <b>Market Order</b>                       | EMSO-14673                                                                                           | Active                     | Accepted                   | FundE.1             | $\\Box$   |                        |                                   |                                   |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          |                        |                                   |                                   |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          |                        |                                   |                                   |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          |                        |                                   |                                   |                                                                                     |                                                                                                      |\n| <b>なりの</b>         |                                           |                                                                                                      |                            |                            |                     |          |                        |                                   |                                   |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          | <b>Delete</b>          |                                   | Place<br>Amend                    |                                                                                     |                                                                                                      |\n|                    |                                           |                                                                                                      |                            |                            |                     |          |                        |                                   |                                   |                                                                                     |                                                                                                      |\n|                    | C GroupE.TreasurerM, GroupE // QA2        |                                                                                                      |                            |                            |                     |          | <b>SEMT</b>            |                                   |                                   | Fr, 09. Nov 2018, 14:18:39 GMT // Connected [FFM] ● // Mem: 65.0% of 483 MB GC:0.0% |                                                                                                      |\n\n#### <span id=\"page-37-0\"></span>**Figure 32 Multi Selection**\n\nSelecting \"Amend\" in the actions area will automatically re-open the Order Creation window. The user can make the desired modifications and then click the \"Amend\" button in the Order Amendment window. In the case that no amendment should be made, the user may click \"Cancel\". This will abort the amendment process.\n\n<span id=\"page-38-0\"></span>![](_page_38_Picture_0.jpeg)\n\n|                               | Order Amendment<br><b>Fixing Order</b>                                                                                                                                                                                                                                                                                                                                                |\n|-------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Order Details                 | / Custom Fields<br>$EUR \\vee$ $\\bullet$<br>$\\overset{\\mathsf{Buy}}{\\rightleftharpoons}$<br>1,000,000<br>$USD \\vee 0$<br>Sell<br><b>Effective Date</b><br>Wed, 07.03.2018 (iii)<br>Spot $\\vee$<br>1.23393<br>Market Rate<br><b>Fixing Reference</b><br><b>ECB</b><br><b>Fixing Date</b><br>篇<br>Tue, 06.03.2018 14:15 CET<br><b>Expiry</b><br>Mon, 05.03.2018 00:00<br>m<br>$GTC \\vee$ |\n| Provider List                 | Comments                                                                                                                                                                                                                                                                                                                                                                              |\n| 360TBANK.TEST<br>RBS.LND.DEMO | 욱<br>LLOYDS.DEMO<br>360TTS.TEST<br>Unicredit.DEMO<br>SEB.DEMO                                                                                                                                                                                                                                                                                                                         |\n| Select All Unselect All       |                                                                                                                                                                                                                                                                                                                                                                                       |\n|                               | Amend<br>Cancel                                                                                                                                                                                                                                                                                                                                                                       |\n\n**Figure 33 Order Amendment**\n\nThe Order Changes and Request Changes tabs show changes to the order status generated either on the requester or provider side.\n\n|                             |      | <b>Order View</b>              |      |                   |                   |                 |  |  |  |\n|-----------------------------|------|--------------------------------|------|-------------------|-------------------|-----------------|--|--|--|\n| Limit Order                 |      |                                |      |                   |                   |                 |  |  |  |\n|                             |      |                                |      |                   | Initialized       |                 |  |  |  |\n| Order Details               |      | Order Changes                  |      |                   |                   |                 |  |  |  |\n| State                       | Date |                                | User |                   | Compa             |                 |  |  |  |\n| Initialized                 |      | Mon, 30. Apr 2018 12:10:45 GMT |      | GroupE.TreasurerM | GroupE            |                 |  |  |  |\n| Active                      |      | Mon, 30. Apr 2018 12:10:46 GMT |      | GroupE.TreasurerM | GroupE            |                 |  |  |  |\n| Initialized                 |      | Mon, 30. Apr 2018 12:17:14 GMT |      | GroupE.TreasurerM | GroupE            |                 |  |  |  |\n| Counterparts                |      | <b>Request Changes</b>         |      |                   |                   |                 |  |  |  |\n| <b>State</b>                |      | <b>Date</b>                    |      | <b>User</b>       |                   |                 |  |  |  |\n| Initialized                 |      | Mon, 30. Apr 2018 12:10:45 GMT |      |                   | GroupE.TreasurerM | $\\alpha$<br>GI. |  |  |  |\n| Sent                        |      | Mon, 30. Apr 2018 12:10:46 GMT |      |                   | GroupE.TreasurerM | G               |  |  |  |\n| <b>Delivered</b>            |      | Mon, 30. Apr 2018 12:10:47 GMT |      |                   |                   | H               |  |  |  |\n| <b>Pending Cancellation</b> |      | Mon, 30. Apr 2018 12:17:14 GMT |      |                   | GroupE.TreasurerM | G               |  |  |  |\n| Cancelled                   |      | Mon, 30. Apr 2018 12:17:14 GMT |      |                   |                   | H               |  |  |  |\n| Initialized                 |      | Mon, 30. Apr 2018 12:17:14 GMT |      |                   |                   | H.              |  |  |  |\n|                             |      |                                |      |                   |                   |                 |  |  |  |\n\n<span id=\"page-38-1\"></span>**Figure 34 Initialized Tab: Order and Request Changes View**\n\n![](_page_39_Picture_0.jpeg)\n\n## <span id=\"page-39-0\"></span>**4.3 Active/Delivered Order (Sent tab)**\n\nOrders can be placed either directly from the Order Creation window (see Chapter [4.1.1\\)](#page-14-0) or via a previously created order in the **Initialized** tab. The request status will then change from Initialized to Delivered or Sent until the provider has accepted or rejected it. The status Sent applies to orders which are sent, but not yet delivered (e.g. sent to be manually priced, but not yet delivered because no manual traders are online).\n\n|                        | <b>TRADER WORKSHEET</b>        | <b>ORDER MANAGEMENT</b>           |             | <b>RFS REQUESTER</b> | $\\overline{+}$      |                    |                   |                |                  |             |         |            |        |           |               |\n|------------------------|--------------------------------|-----------------------------------|-------------|----------------------|---------------------|--------------------|-------------------|----------------|------------------|-------------|---------|------------|--------|-----------|---------------|\n| $\\mathbf{r}$           | Initialized (44)<br>Sent $(3)$ | lQ.<br>上                          |             |                      |                     |                    |                   |                |                  |             |         |            |        |           |               |\n|                        | <b>Type</b>                    | Reference $# \\wedge$ Order Status |             | <b>Status</b>        | <b>Legal Entity</b> | <b>Requester A</b> | <b>Currencies</b> | Product        | Notional A       | Notional Cu | Rate    | Expiry'    |        |           |               |\n| $\\frac{1}{2}$          | <b>Stop Order</b>              | EMSO-1871                         | Initialized | Sent                 | GroupH              | Buy                | EUR/USD           | <b>FX Spot</b> | 2,000,000.0      | <b>EUR</b>  | 1.11811 | <b>GTC</b> | e      | $\\otimes$ | $\\Rightarrow$ |\n| $\\hat{\\Xi}$            | <b>Limit Order</b>             | EMSO-1871                         | Initialized | Sent                 | GroupH              | Buy                | EUR/USD           | <b>FX Spot</b> | 1,000,000.0      | <b>EUR</b>  | 1.11815 | GTC        | $\\Box$ | $\\otimes$ | $\\oplus$      |\n| $\\uparrow$<br>$\\smile$ | <b>Market Order</b>            | EMSO-1655.                        | Initialized | Sent                 | GroupH              | Buy                | EUR/USD           | <b>FX Spot</b> | 7,001.00 EUR EUR |             |         | <b>GTC</b> | $\\Box$ | $\\otimes$ | $\\oplus$      |\n|                        |                                |                                   |             |                      |                     |                    |                   |                |                  |             |         |            |        |           |               |\n| 也                      |                                |                                   |             |                      |                     |                    |                   |                |                  |             |         |            |        |           |               |\n|                        |                                |                                   |             |                      |                     |                    |                   |                |                  |             |         |            |        |           |               |\n|                        |                                |                                   |             |                      |                     |                    |                   |                |                  |             |         |            |        |           |               |\n\n#### <span id=\"page-39-1\"></span>**Figure 35 Sent tab: Actions Area**\n\nAn initialized order can still be withdrawn by the requester. This can be done either via the Order View window or directly from the actions area.\n\n|   | <u>. </u>                                  |                                     |                         |                        |                                                                                                |                                              | $\\vee$ Preferences $\\vee$ Help $\\left.\\right $ $\\mathbb{A}^{1}$ AA $ \\Box$ X |                                                       |\n|---|--------------------------------------------|-------------------------------------|-------------------------|------------------------|------------------------------------------------------------------------------------------------|----------------------------------------------|------------------------------------------------------------------------------|-------------------------------------------------------|\n|   | <b>RFS LIVE PRICING</b>                    | <b>DEAL TRACKING</b>                | <b>ORDER BOOK</b>       | $+$                    |                                                                                                |                                              |                                                                              |                                                       |\n|   | Initialized (33)                           | 上<br>Q<br>Sent $(4)$                |                         |                        |                                                                                                | ⇒                                            | <b>Order View</b>                                                            | $\\Box$                                                |\n| ť | <b>Type</b>                                | Reference #                         |                         |                        | $\\checkmark$                                                                                   |                                              | Limit Order                                                                  |                                                       |\n|   | <b>Limit Order</b><br><b>Market Order</b>  | EMSO-1318366/1<br>EMSO-1316954      |                         |                        | $\\Box$<br>ା⊗<br>$\\left\\langle \\mathbf{x}\\right\\rangle$<br>$\\Box$                               | Order Details                                | Order Changes                                                                | <b>Delivered</b>                                      |\n|   | <b>Limit Order</b><br><b>Market Order</b>  | EMSO-1312269<br>EMSO-1299773        |                         |                        | $\\hfill\\ensuremath{\\square}$<br>$\\left\\langle \\mathbf{x}\\right\\rangle$<br>Ŵ<br>$\\rm \\Xi$<br>図■ | Product<br>Action<br>Notional                |                                                                              | <b>FxSpot</b><br>I Buy EUR / Sell USD<br>7,777.00 EUR |\n|   |                                            |                                     |                         |                        |                                                                                                | <b>Limit Spot Rate</b><br><b>Market Rate</b> |                                                                              | 1.20965<br>1.21025                                    |\n|   |                                            |                                     |                         |                        |                                                                                                | <b>Expiry</b><br>Order ID                    |                                                                              | <b>GTC</b><br>EMSO-1318366/1                          |\n|   |                                            |                                     |                         |                        |                                                                                                | Counterparts                                 | Request Changes                                                              |                                                       |\n|   |                                            |                                     |                         |                        |                                                                                                | Provider                                     |                                                                              | HSBC.DEMO                                             |\n|   | Accepted (34)                              | 区<br>Q                              |                         |                        |                                                                                                |                                              |                                                                              | Withdraw                                              |\n|   | <b>Type</b>                                | Reference #                         | <b>Order Status</b>     | <b>Status</b>          | <b>Legal Er</b>                                                                                |                                              |                                                                              |                                                       |\n|   | <b>Limit Order</b><br><b>Fixing Order</b>  | EMSO-1317496<br>EMSO-1317166        | Active<br>Active        | Pending Ca<br>Accepted | GroupE. $\\Box$<br>R)<br>GroupE $\\Box$ $\\oslash$                                                |                                              |                                                                              |                                                       |\n|   | <b>Fixing Order</b><br><b>Market Order</b> | EMSO-1317165<br>EMSO-1316214        | Active<br>Active        | Accepted<br>Accepted   | GroupE $\\Box$ $\\oslash$<br>GroupE $\\Box$ $\\oslash$                                             |                                              |                                                                              |                                                       |\n|   | <b>Market Order</b><br>$\\vee$ OCO Order    | EMSO-1313543<br>EMSO-1313540        | Active                  | Accepted<br>Accepted   | GroupE $\\Box$ $\\boxtimes$<br>GroupE $\\Box$ $\\oslash$                                           |                                              |                                                                              |                                                       |\n|   | $\\vee$ OCO Order                           | EMSO-1312625                        |                         | Accepted               | GroupE $\\Box$ $\\oslash$                                                                        |                                              |                                                                              |                                                       |\n|   | <b>Market Order</b><br><b>Market Order</b> | EMSO-1312560<br>EMSO-1312550/1      | Active<br>Active        | Accepted<br>Accepted   | GroupE $\\Box$ $\\oslash$<br>GroupE $\\Box$ $\\Box$                                                |                                              |                                                                              |                                                       |\n|   | <b>Market Order</b><br>Market Order        | EMSO-1312180<br><b>EMSO-1312179</b> | Active<br><b>Active</b> | Accepted<br>Accented   | GroupE $\\Box$ $\\oslash$<br>$G$ rounE $\\Box$ $\\Box$                                             |                                              |                                                                              |                                                       |\n|   | C GroupE.TreasurerM, GroupE // QA2         |                                     |                         |                        | escin                                                                                          |                                              | Mo, 30. Apr 2018, 08:56:01 GMT // Connected ·                                |                                                       |\n\n<span id=\"page-39-2\"></span>**Figure 36 Sent Tab: Withdraw Order**\n\nA withdrawn order will move to the **Initialized** tab and can be further processed.\n\n![](_page_40_Picture_0.jpeg)\n\n## <span id=\"page-40-0\"></span>**4.4 Accepted orders**\n\nIf the order has been accepted by the market maker it disappears from the **Sent** tab and moves to the **Accepted** tab.\n\nOnce an order has been **accepted** by the market maker it will appear in the bottom area of the screen (**Accepted** tab). Pending orders can be **withdrawn** from this area.\n\n| Initialized (45)          | la l<br>l*<br>Sent $(2)$           |             |               |                     |             |                                                                                                                              |                              |                        | <b>Order View</b>   | $O$ $\\oplus$                      | <b>Executions</b>                    |\n|---------------------------|------------------------------------|-------------|---------------|---------------------|-------------|------------------------------------------------------------------------------------------------------------------------------|------------------------------|------------------------|---------------------|-----------------------------------|--------------------------------------|\n| Type                      | Reference $\\# \\wedge$ Order Status |             | <b>Status</b> | <b>Legal Entity</b> | Requester A |                                                                                                                              |                              |                        | <b>Market Order</b> |                                   | GroupH                               |\n| <b>Limit Order</b>        | EMSO-1871                          | Initialized | Sent          | GroupH              | Buy         | $\\begin{smallmatrix} \\mathbb{G} & \\vee & \\otimes & 0 \\\\ \\mathbb{G} & \\mathbb{G} & \\mathbb{G} & \\mathbb{G} \\end{smallmatrix}$ |                              |                        |                     | Accepted                          | <b>FX Spot</b><br>Buy EUR / Sell USD |\n| <b>Market Order</b>       | EMSO-1655                          | Initialized | Sent          | GroupH              | Buy         | $\\begin{smallmatrix} \\mathbf{C} & \\mathbf{A} & \\mathbf{B} & \\mathbf{B} \\end{smallmatrix}$                                    | <b>Order Details</b>         | Order Changes          | Fills(0)            |                                   | 250.00 EUR                           |\n|                           |                                    |             |               |                     |             |                                                                                                                              |                              |                        |                     |                                   | @ 1.12343                            |\n|                           |                                    |             |               |                     |             |                                                                                                                              | Product                      |                        |                     | <b>FX Forward Swap</b>            | GroupH                               |\n|                           |                                    |             |               |                     |             |                                                                                                                              | <b>Near Leg</b>              |                        |                     | I Buy EUR / Sell GBP              | <b>FX Spot</b><br>Buy EUR / Sell USD |\n| $\\alpha$<br>Accepted (26) | l₹.                                |             |               |                     |             |                                                                                                                              | <b>Effective Date</b>        |                        |                     | Fri. 09. Aug 2019                 | 250.00 EUR<br>@1.10933               |\n| <b>Type</b>               | Reference $\\# \\wedge$ Order Status |             | <b>Status</b> | <b>Legal Entity</b> | Requester A | Curr                                                                                                                         | Far Leg                      |                        |                     | I Sell EUR / Buy GBP              |                                      |\n| <b>Market Order</b>       | EMSO-1869.                         | Active      | Accepted      | GroupH              | Buy         | $EUR$ , $\\Box$ $\\Box$ $\\Box$                                                                                                 | <b>Maturity Date</b>         |                        |                     | Tue, 13. Aug 2019                 |                                      |\n| <b>Market Order</b>       | EMSO-1868.                         | Active      | Accepted      | GroupH              | Sell        | $EUR \\n\\odot \\n\\odot$                                                                                                        | Limit                        |                        |                     | 444                               |                                      |\n| <b>Market Order</b>       | EMSO-1868                          | Active      | Accepted      | GroupH              | Sell        | $ EUR  \\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t$                                           | <b>Clip Size</b>             |                        |                     |                                   |                                      |\n| <b>Market Order</b>       | EMSO-1868.                         | Active      | Accepted      | GroupH              | Sell        | $EUR \\n\\odot \\n\\odot$                                                                                                        | Placement                    |                        |                     | Post a Bid or Offer               |                                      |\n| <b>Market Order</b>       | EMSO-1868.                         | Active      | Accepted      | GroupH              | Sell        | $EUR$ , $\\Box$ $\\Box$ $\\Box$                                                                                                 | <b>Market Rate</b>           |                        |                     | 0.92433                           |                                      |\n| <b>Market Order</b>       | EMSO-1868.                         | Active      | Accepted      | GroupH              | Sell        | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                |                              |                        |                     |                                   |                                      |\n| <b>Market Order</b>       | EMSO-1868.                         | Active      | Accepted      | GroupH              | Sell        | $EUR$ $\\circ$ $\\otimes$ $\\circ$                                                                                              | <b>Partial Fill Progress</b> |                        |                     | <b>Total</b><br>1,000,000.00 EUR  |                                      |\n| <b>Market Order</b>       | EMSO-1859.                         | Active      | Accepted      | GroupH              | Sell        | $CAD, CQ \\otimes C$                                                                                                          |                              |                        |                     | Completed                         |                                      |\n| <b>Market Order</b>       | EMSO-1859.                         | Active      | Accepted      | GroupH              | Buy         | $CAD, \\Box \\oslash \\Box$                                                                                                     |                              |                        | 0%                  | <b>0.00 EUR</b>                   |                                      |\n| <b>Market Order</b>       | EMSO-1859.                         | Active      | Accepted      | GroupH              | Sell        | $CAD, \\Box \\oslash \\Leftrightarrow$                                                                                          |                              |                        |                     | Remaining                         |                                      |\n| <b>Market Order</b>       | EMSO-1842                          | Active      | Pending Ca    | GroupH              | Sell        | $EUR$ <sub>i</sub> $\\Box$<br>8                                                                                               |                              |                        |                     | ************ EUR                  |                                      |\n| <b>Market Order</b>       | EMSO-1835.                         | Active      | Pending Ca.   | GroupH              | Sell        | $\\oplus$<br>$EUR$ <sub>i</sub> $\\Box$                                                                                        | Expiry                       |                        |                     | Tue, 20. Aug 2019 // 23:59:00 GMT |                                      |\n| Market Order              | EMSO-1816.                         | Active      | Pending Ca.   | GroupH              | Sell        | $\\oplus$<br>$EUR$ <sub><math>\\subseteq</math></sub>                                                                          | Order ID                     |                        |                     | EMSO-1868119                      |                                      |\n| <b>Market Order</b>       | EMSO-1816                          | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\mathbb{C} \\n\\mathbb{Z} \\n\\mathbb{R}$                                                                                 |                              |                        |                     |                                   |                                      |\n| <b>Algo Order</b>         | EMSO-1807                          | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                | Counterparts                 | <b>Request Changes</b> |                     |                                   |                                      |\n| $\\vee$ OCO Order          | EMSO-1804.                         |             | Accepted      | GroupH              |             | $O$ $\\otimes$ $\\oplus$                                                                                                       |                              |                        |                     |                                   |                                      |\n| <b>Algo Order</b>         | EMSO-1802                          | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\mathbb{C} \\n\\mathbb{Z} \\n\\mathbb{B}$                                                                                 | Provider                     |                        |                     | * 360T PLATFORM *                 |                                      |\n| <b>Market Order</b>       | EMSO-1802                          | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\mathbb{C} \\n\\mathbb{Z} \\n\\mathbb{R}$                                                                                 |                              |                        |                     |                                   |                                      |\n| <b>Market Order</b>       | EMSO-1802.                         | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\mathbb{C} \\n\\mathbb{Z} \\n\\mathbb{B}$                                                                                 |                              |                        |                     | Withdraw                          |                                      |\n| <b>Stop Order</b>         | EMSO-1801.                         | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\mathbb{C} \\n\\mathbb{Z} \\n\\mathbb{R}$                                                                                 |                              |                        |                     |                                   |                                      |\n| <b>Market Order</b>       | EMSO-1798                          | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                |                              |                        |                     |                                   |                                      |\n| <b>Market Order</b>       | EMSO-1798.                         | Active      | Accepted      | GroupH              | Buy         | $EUR$ $\\cup$ $\\otimes$ $\\oplus$                                                                                              |                              |                        |                     |                                   |                                      |\n| <b>Algo Order</b>         | EMSO-1655                          | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                |                              |                        |                     |                                   |                                      |\n| <b>Algo Order</b>         | EMSO-1655.                         | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                |                              |                        |                     |                                   |                                      |\n| <b>Market Order</b>       | EMSO-1655                          | Active      | Accepted      | GroupH              | Buy         | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                |                              |                        |                     |                                   |                                      |\n| <b>Market Order</b>       | EMSO-1463                          | Active      | Accepted      | GroupH              | Buy         | $EUR$ $\\odot$ $\\odot$ $\\odot$                                                                                                |                              |                        |                     |                                   |                                      |\n\n#### <span id=\"page-40-1\"></span>**Figure 37 Accepted Order**\n\nIn case the user has placed a large order, the counterparty has the possibility to execute it partially in steps. The partial fill progress can be monitored directly in the Order View window (Partial Fill Progress panel) or in the **Accepted** tab (Fill Rate and Remaining Amount columns).\n\n![](_page_41_Picture_0.jpeg)\n\n|                                          |                               | <b>ORDER MANAGEMENT</b>            |               | <b>RFS REQUESTER</b> | $+$           |                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                              |                        |                                          | ☆ 命                                               |                                      |\n|------------------------------------------|-------------------------------|------------------------------------|---------------|----------------------|---------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|------------------------|------------------------------------------|---------------------------------------------------|--------------------------------------|\n| Initialized (45)                         | <b>Q</b><br>l ±<br>Sent $(2)$ |                                    |               |                      |               |                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                              |                        | <b>Order View</b>                        | $O$ $\\oplus$                                      | <b>Executions</b>                    |\n| Type                                     |                               | Reference $# \\wedge$ Order Status  | <b>Status</b> | <b>Legal Entity</b>  | Requester A., |                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                              |                        | <b>Market Order</b>                      |                                                   | GroupH                               |\n| <b>Limit Order</b>                       | EMSO-1871                     | Initialized                        | Sent          | GroupH               | Buy           | $\\rm ^\\odot$<br>$\\sqrt{\\otimes}$                                                                                                                                                                                                                                                                                                                                                                                                                             |                              |                        |                                          | Accepted                                          | <b>FX Spot</b><br>Buy EUR / Sell USD |\n| Market Order                             | EMSO-1655                     | Initialized                        | Sent          | GroupH               | Buy           | $\\begin{array}{c c c c c c c} \\hline \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\mathcal{O}} & \\multicolumn{3}{c }{\\$ | <b>Order Details</b>         | Order Changes          | Fills(1)                                 |                                                   | 250.00 EUR                           |\n|                                          |                               |                                    |               |                      |               |                                                                                                                                                                                                                                                                                                                                                                                                                                                              | Product                      |                        |                                          | <b>FX Forward Swap</b>                            | @1.12343                             |\n|                                          |                               |                                    |               |                      |               |                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                              |                        |                                          |                                                   | GroupH                               |\n|                                          |                               |                                    |               |                      |               |                                                                                                                                                                                                                                                                                                                                                                                                                                                              | Near Leg                     |                        |                                          | I Buy EUR / Sell CHF                              | <b>FX Spot</b><br>Buy EUR / Sell USD |\n| la.<br>Accepted (28)                     | E                             |                                    |               |                      |               |                                                                                                                                                                                                                                                                                                                                                                                                                                                              | <b>Effective Date</b>        |                        |                                          | Fri. 09. Aug 2019                                 | 250.00 EUR                           |\n| Type                                     |                               | Reference $\\# \\wedge$ Order Status | <b>Status</b> | <b>Legal Entity</b>  | Requester A   | Curr                                                                                                                                                                                                                                                                                                                                                                                                                                                         | Far Leg                      |                        |                                          | I Sell EUR / Buy CHF                              | @1.10933                             |\n| <b>Limit Order</b>                       | EMSO-1871                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                | <b>Maturity Date</b>         |                        |                                          | Tue. 13. Aug 2019                                 |                                      |\n| <b>Limit Order</b>                       | EMSO-1871                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          |                                                   |                                      |\n| <b>Market Order</b>                      | EMSO-1869.                    | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                | Limit                        |                        |                                          | 1116                                              |                                      |\n| Market Order                             | EMSO-1868.                    | Active                             | Accepted      | GroupH               | Sell          | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                | Clip Size<br>Placement       |                        |                                          | Post a Bid or Offer                               |                                      |\n| <b>Market Order</b>                      | EMSO-1868.                    | Active                             | Accepted      | GroupH               | Sell          | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                | <b>Market Rate</b>           |                        |                                          | 1.08677                                           |                                      |\n| <b>Market Order</b>                      | EMSO-1868.                    | Active                             | Accepted      | <b>GroupH</b>        | Sell          | $EUR \\bigcirc \\bigotimes \\bigotimes$                                                                                                                                                                                                                                                                                                                                                                                                                         |                              |                        |                                          |                                                   |                                      |\n| <b>Market Order</b>                      | EMSO-1868                     | Active                             | Accepted      | GroupH               | Sell          | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                | <b>Partial Fill Progress</b> |                        |                                          | Total                                             |                                      |\n| <b>Market Order</b>                      | EMSO-1868                     | Active                             | Accepted      | GroupH               | Sell          | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          | 5,000,000.00 EUR                                  |                                      |\n| Market Order                             | EMSO-1868                     | Active                             | Accepted      | GroupH               | Sell          | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Completed<br>3,000,000.00 EUR                     |                                      |\n| Market Order                             | EMSO-1859                     | Active                             | Accepted      | GroupH               | Sell          | $CAD, C \\otimes B$                                                                                                                                                                                                                                                                                                                                                                                                                                           |                              |                        |                                          | Remaining                                         |                                      |\n| <b>Market Order</b>                      | EMSO-1859                     | Active                             | Accepted      | GroupH               | Buy           | $CAD, C \\otimes C$                                                                                                                                                                                                                                                                                                                                                                                                                                           |                              |                        |                                          | 2,000,000.00 EUR                                  |                                      |\n| <b>Market Order</b>                      | EMSO-1859.                    | Active                             | Accepted      | GroupH               | Sell          | CAD, 图图                                                                                                                                                                                                                                                                                                                                                                                                                                                      |                              |                        |                                          |                                                   |                                      |\n| <b>Market Order</b>                      | EMSO-1842                     | Active                             | Pending Ca    | GroupH               | Sell          | e<br>$EUR$ <sub>i</sub> $\\Box$<br>œ.                                                                                                                                                                                                                                                                                                                                                                                                                         | Expiry                       |                        |                                          | Thu, 15. Aug 2019 // 23:59:00 GMT<br>EMSO-1868118 |                                      |\n| <b>Market Order</b>                      | EMSO-1835                     | Active                             | Pending Ca.   | GroupH               | Sell          | e<br>$EUR$ <sub><math>\\Box</math></sub>                                                                                                                                                                                                                                                                                                                                                                                                                      | Order ID                     |                        |                                          |                                                   |                                      |\n| Market Order                             | EMSO-1816                     | Active                             | Pending Ca.   | GroupH               | Sell          | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          |                                                   |                                      |\n| Market Order                             | EMSO-1816                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\mathcal{Q} \\n\\mathcal{Q}$                                                                                                                                                                                                                                                                                                                                                                                                                            | Counterparts                 | <b>Request Changes</b> |                                          |                                                   |                                      |\n| <b>Algo Order</b>                        | EMSO-1807                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                | Provider                     |                        |                                          | * 360T PLATFORM *                                 |                                      |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | EMSO-1804.                    |                                    | Accepted      | GroupH               |               | O & 86                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                              |                        |                                          |                                                   |                                      |\n| Algo Order                               | EMSO-1802                     | Active                             | Accepted      | GroupH               | Buy           | EUR, $\\mathbb{Q} \\otimes \\mathbb{Q}$                                                                                                                                                                                                                                                                                                                                                                                                                         |                              |                        |                                          | Withdraw                                          |                                      |\n| <b>Market Order</b>                      | EMSO-1802.                    | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          |                                                   |                                      |\n| Market Order                             | EMSO-1802                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          |                                                   |                                      |\n| <b>Stop Order</b>                        | EMSO-1801                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\mathcal{Q} \\n\\mathcal{Q}$                                                                                                                                                                                                                                                                                                                                                                                                                            |                              |                        |                                          |                                                   |                                      |\n| <b>Market Order</b>                      | EMSO-1798                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          |                                                   |                                      |\n| <b>Market Order</b>                      | EMSO-1798                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          |                                                   |                                      |\n| Algo Order                               | EMSO-1655                     | Active                             | Accepted      | GroupH               | Buy           | $EUR \\n\\odot \\n\\odot \\n\\odot$                                                                                                                                                                                                                                                                                                                                                                                                                                |                              |                        |                                          |                                                   |                                      |\n\n<span id=\"page-41-1\"></span>**Figure 38 Partial Execution of a Large Market Order**\n\n## <span id=\"page-41-0\"></span>**4.5 Rejected orders**\n\nWhen the provider has refused to accept the order, the order status will change from \"Delivered\" to \"Pending Rejection\" (highlighted in red) and will stay in the Sent tab. The market maker rejection comment can be viewed in the upper area of the Order View window after opening the rejected order. The user must confirm the rejection. After confirmation of the rejection, the order will move to the Initialized tab. The order request changes can be viewed in the Request Changes tab.\n\n| Initialized (32)     | $\\begin{array}{ c c c c c c c c c c c c c c c c c c c$<br>Sent $(4)$ |                                       |                  |                     |                        |         |                |                                          |                        | Order View                             |                                        |\n|----------------------|----------------------------------------------------------------------|---------------------------------------|------------------|---------------------|------------------------|---------|----------------|------------------------------------------|------------------------|----------------------------------------|----------------------------------------|\n| Type                 | Reference #                                                          | <b><i>↓</i></b> Order Status • Status |                  | <b>Legal Entity</b> | Requester A Currencies |         | <b>Product</b> | т.                                       |                        | Limit Order                            |                                        |\n| <b>Limit Order</b>   | EMSO-1318365/1                                                       | Initialized                           | Pending Rei      | GroupE              | <b>Buy</b>             | EUR/USD | ExSpot         | 非面<br>$\\Box$                             |                        |                                        | <b>Pending Rejection</b>               |\n| <b>Market Order</b>  | EMSO-1316954                                                         | Initialized                           | <b>Delivered</b> | GroupE              | <b>Buy</b>             | EUR/AFN | ExSpot         | $\\oslash$<br>$\\Box$                      |                        | Rejected the order due to some reasons |                                        |\n| <b>Limit Order</b>   | EMSO-1312269                                                         | Initialized                           | Sent             | GroupE              | Buy                    | EUR/USD | <b>FxSpot</b>  | $\\oslash$<br>$\\Box$                      |                        |                                        |                                        |\n| <b>Market Order</b>  | EMSO-1299773                                                         | Initialized                           | <b>Delivered</b> | GroupE.TAS          | Buy                    | NOK/CZK | ExSpot         | $\\Box$<br>$\\oslash$                      | <b>Order Details</b>   | Order Changes                          |                                        |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | Product                |                                        | <b>FxSpot</b>                          |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | Action                 |                                        | I Buy EUR / Sell USD                   |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | Notional               |                                        | 2.345.00 EUR                           |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | <b>Limit Spot Rate</b> |                                        | 1.13190                                |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | <b>Market Rate</b>     |                                        | 1.21063                                |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          |                        |                                        |                                        |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | Expiry                 |                                        |                                        |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | Order ID               |                                        | EMSO-1318365/1                         |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | Counterparts           | <b>Request Changes</b>                 |                                        |\n|                      |                                                                      |                                       |                  |                     |                        |         |                |                                          | Provider               |                                        | HSBC.DEMO                              |\n| IQ.<br>Accepted (1)* | 上                                                                    |                                       |                  |                     |                        |         |                |                                          |                        |                                        | <b>Delete</b><br><b>Confirm Reject</b> |\n|                      | $Q -$                                                                |                                       |                  |                     | Apply                  | Save as |                | $\\times$                                 |                        |                                        |                                        |\n| 88<br>Accepted       | Reference # = EMSO-1312560 $\\times$                                  |                                       |                  |                     |                        |         |                | $\\leftrightarrow$                        |                        |                                        |                                        |\n| Type                 | Reference #                                                          | <b>Order Status</b>                   | <b>Status</b>    | <b>Legal Entity</b> | Requester A Fill Ratio |         | Remaining      | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                        |                                        |                                        |\n| <b>Market Order</b>  | EMSO-1312560                                                         | Active                                | Accepted         | GroupE              | Buy                    |         |                | 75.00% 1,743.00 NZD NZD/USD $\\Box$       |                        |                                        |                                        |\n\n<span id=\"page-41-2\"></span>**Figure 39 Pending Order Rejection**\n\n![](_page_42_Picture_0.jpeg)\n\n## <span id=\"page-42-0\"></span>**4.6 Withdrawn orders**\n\nPlaced, delivered or accepted orders can be withdrawn at any time during the validity of the order by clicking on the withdraw icon in the **Sent** or **Accepted** tab, respectively. The user can also right-click on an order to open the context menu and select \"Withdraw\".\n\n| Initialized (33)    | Q <br>lと<br>Sent $(3)$ |                                |                  |                     |                        |                   |               |                                                        |                    | Order View      |                                       |\n|---------------------|------------------------|--------------------------------|------------------|---------------------|------------------------|-------------------|---------------|--------------------------------------------------------|--------------------|-----------------|---------------------------------------|\n| Type                | Reference #            | <b>C</b> Order Status • Status |                  | <b>Legal Entity</b> | Requester A Currencies |                   | Product       | п.                                                     |                    | Market Order    |                                       |\n| <b>Market Order</b> | EMSO-1316954           | Initialized                    | <b>Delivered</b> | <b>GroupE</b>       | Buy                    | EUR/AFN           | <b>FxSpot</b> | ା⊗ା<br>e                                               |                    |                 | <b>Delivered</b>                      |\n| <b>Limit Order</b>  | EMSO-1312269           | Initialized                    | Sent             | GroupE              | Buy                    | EUR/USD           | FxSpot        | $\\qquad \\qquad \\qquad \\qquad \\qquad$<br>$\\circledcirc$ | Order Details      | Order Changes   |                                       |\n| <b>Market Order</b> | EMSO-1299773           | Initialized                    | <b>Delivered</b> | GroupE.TAS          | Buy                    | NOK/CZK           | FxSpot        | 図面<br>$\\Box$                                           |                    |                 |                                       |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        | Product            |                 | <b>FxSpot</b><br>I Buy EUR / Sell AFN |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        | Action<br>Notional |                 | 15,000.00 EUR                         |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        |                    |                 |                                       |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        | <b>Market Rate</b> |                 | 86.09631                              |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        | Expiry             |                 | <b>GTC</b>                            |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        | Order ID           |                 | EMSO-1316954                          |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        |                    |                 |                                       |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        | Counterparts       | Request Changes |                                       |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        |                    |                 |                                       |\n|                     |                        |                                |                  |                     |                        |                   |               |                                                        | Provider           |                 |                                       |\n| Accepted (36)       | 上<br>Q<br>'Qv          |                                |                  |                     | <b>Apply</b>           | Save as           |               |                                                        |                    |                 |                                       |\n| Accepted            |                        |                                |                  |                     |                        |                   |               |                                                        |                    |                 | HSBC.DEMO<br>Withdraw                 |\n| Type                | Reference #            | <b>Order Status</b>            | <b>Status</b>    | <b>Legal Entity</b> | Requester A            | <b>Fill Ratio</b> | Remaining     | Currencie                                              |                    |                 |                                       |\n| <b>Limit Order</b>  | EMSO-1318367           | Active                         | Accepted         | GroupE              | Buy                    | 50.00%            |               | 2,004.00 CAD CAD/JPY (C) 3                             |                    |                 |                                       |\n| <b>Limit Order</b>  | EMSO-1318366/1         | Active                         | Accepted         | GroupE              | Buy                    | 0.00%             |               | 7,777.00 EUR EUR/USD C 3                               |                    |                 |                                       |\n| <b>Limit Order</b>  | EMSO-1317496           | Active                         | Pending Ca.      | GroupE.TAS.         | Sell                   | 0.00%             | 55,000.00 G.  | GBP/USD C                                              |                    |                 |                                       |\n| <b>Fixing Order</b> | EMSO-1317166           | Active                         | Accepted         | GroupE              | Buy                    | 0.00%             | 560,000.00    | USD/JPY $\\Box$ $\\oslash$                               |                    |                 |                                       |\n| <b>Fixing Order</b> | EMSO-1317165           | Active                         | Accepted         | GroupE              | Buy                    | 0.00%             | 55,000.00 G   | GBP/USD (C) (X)                                        |                    |                 |                                       |\n| <b>Market Order</b> | EMSO-1316214           | Active                         | Accepted         | GroupE              | Buy                    | 0.00%             | 123,000.00.   | EUR/CHF $\\Box$ $\\oslash$                               |                    |                 |                                       |\n| <b>Market Order</b> | EMSO-1313543           | Active                         | Accepted         | GroupE              | Buy                    | 0.00%             | 55,000.00 U.  | EUR/USD $\\Box$                                         |                    |                 |                                       |\n| $\\vee$ OCO Order    | EMSO-1313540           |                                | Accepted         | GroupE              |                        |                   |               | $\\circ$                                                |                    |                 |                                       |\n| $\\vee$ OCO Order    | EMSO-1312625           |                                | Accepted         | GroupE              |                        |                   |               | $\\circ$                                                |                    |                 |                                       |\n| <b>Market Order</b> | EMSO-1312560           | Active                         | Accepted         | GroupE              | Buy                    |                   |               | 75.00% 1,743.00 NZD NZD/USD (C) 3                      |                    |                 |                                       |\n\n<span id=\"page-42-1\"></span>**Figure 40 Withdrawal of delivered order**\n\nIn case an accepted order is withdrawn, the original order remains visible in the **Accepted** tab with the status \"Pending Cancellation\" until the market maker acknowledges the cancellation.\n\n![](_page_43_Picture_0.jpeg)\n\n| Initialized (33)    | Q <br>  ≚<br>Sent $(3)$          |                                        |                  |                     |                        |                |               |                            |                              | <b>Order View</b>                  |                                                         |\n|---------------------|----------------------------------|----------------------------------------|------------------|---------------------|------------------------|----------------|---------------|----------------------------|------------------------------|------------------------------------|---------------------------------------------------------|\n| Type                | Reference #                      | <b>C</b> Order Status $\\hat{P}$ Status |                  | <b>Legal Entity</b> | Requester A Currencies |                | Product       | $\\mathbf{r}$               |                              | Limit Order                        |                                                         |\n| <b>Market Order</b> | EMSO-1316954                     | Initialized                            | <b>Delivered</b> | GroupE              | Buy                    | <b>EUR/AFN</b> | <b>FxSpot</b> | 図目<br>$\\mathbbm{G}$        |                              |                                    | <b>Pending Cancellation</b>                             |\n| <b>Limit Order</b>  | EMSO-1312269                     | Initialized                            | Sent             | GroupE              | Buy                    | EUR/USD        | <b>FxSpot</b> | $\\oslash$<br>$\\Box$        | Order Details                | Order Changes                      |                                                         |\n| <b>Market Order</b> | EMSO-1299773                     | Initialized                            | <b>Delivered</b> | GroupE.TAS          | Buy                    | NOK/CZK        | <b>FxSpot</b> | $\\Box$<br>図目               | Product                      |                                    | <b>FxSpot</b>                                           |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            | Action                       |                                    | I Sell GBP / Buy USD                                    |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            | Notional                     |                                    | 55,000,00 GBP                                           |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            |                              |                                    |                                                         |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            | <b>Limit Spot Rate</b>       |                                    | 1.39056                                                 |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            | <b>Market Rate</b>           |                                    | 1.37369                                                 |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            | <b>Partial Fill Progress</b> |                                    | 55,000.00 GBP                                           |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            |                              | 0%                                 | Completed                                               |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            |                              |                                    | 0.00 GBP                                                |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            |                              |                                    |                                                         |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            |                              |                                    |                                                         |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            |                              |                                    |                                                         |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            | Expiry                       |                                    |                                                         |\n|                     |                                  |                                        |                  |                     |                        |                |               |                            | Order ID                     |                                    |                                                         |\n| Accepted (36)       | $\\lceil \\mathsf{Q} \\rceil$<br>ΙŁ |                                        |                  |                     |                        |                |               |                            |                              |                                    |                                                         |\n|                     | 'Qv                              |                                        |                  |                     | Apply                  | Save as        |               | $\\times$                   | Counterparts                 | Comments<br><b>Request Changes</b> |                                                         |\n| Accepted            |                                  |                                        |                  |                     |                        |                |               |                            | Provider                     |                                    |                                                         |\n| Type                | Reference #                      | <b>Order Status</b>                    | <b>Status</b>    | <b>Legal Entity</b> | Requester A Fill Ratio |                | Remaining     | Currencie                  |                              |                                    |                                                         |\n| <b>Limit Order</b>  | EMSO-1318367                     | Active                                 | Accepted         | GroupE              | Buy                    | 50.00%         | 2.004.00 CAD  | CAD/JPY (2) ⊗              |                              |                                    |                                                         |\n| <b>Limit Order</b>  | EMSO-1318366/1                   | Active                                 | Accepted         | GroupE              | Buy                    | 0.00%          |               | 7,777.00 EUR EUR/USD (C) 3 |                              |                                    |                                                         |\n| <b>Limit Order</b>  | EMSO - 1317496                   | Active                                 | Pending Ca       | GroupE.TAS.         | Sell                   | 0.00%          | 55,000.00 G   | GBP/USD IO                 |                              |                                    |                                                         |\n| <b>Fixing Order</b> | EMSO-1317166                     | Active                                 | Accepted         | GroupE              | Buy                    | 0.00%          | 560,000.00    | USD/JPY $\\Box$ $\\oslash$   |                              |                                    |                                                         |\n| <b>Fixing Order</b> | EMSO-1317165                     | Active                                 | Accepted         | GroupE              | Buy                    | 0.00%          | 55,000.00 G   | GBP/USD C 3                |                              |                                    |                                                         |\n| <b>Market Order</b> | EMSO-1316214                     | Active                                 | Accepted         | GroupE              | <b>Buy</b>             | 0.00%          | 123,000.00    | EUR/CHF $\\Box$ $\\oslash$   |                              |                                    |                                                         |\n| <b>Market Order</b> | EMSO-1313543                     | Active                                 | Accepted         | GroupE              | Buy                    | 0.00%          | 55,000.00 U.  | EUR/USD $\\Box$ $\\oslash$   |                              |                                    |                                                         |\n| $\\vee$ OCO Order    | EMSO-1313540                     |                                        | Accepted         | GroupE              |                        |                |               | $\\Box$                     |                              |                                    |                                                         |\n| $\\vee$ OCO Order    | EMSO-1312625                     |                                        | Accepted         | GroupE              |                        |                |               | $\\circ$                    |                              |                                    | Remaining<br>55,000.00 GBP<br>EMSO-1317496<br>BOAL.DEMO |\n\n<span id=\"page-43-1\"></span>**Figure 41 Withdrawn Accepted Order**\n\nOnce the market maker has acknowledged the cancellation, the order moves to the **Initialized** tab.\n\n## <span id=\"page-43-0\"></span>**4.7 Expired orders**\n\nExpired orders automatically disappear from the **Accepted** tab and move to the \"Expired\" tab within the Deal Tracking feature.\n\n![](_page_44_Picture_0.jpeg)\n\n|                                   |                          | QV          |                                     |                  |             |              |              | $\\bullet$ Live $\\bullet$ Historical $\\left(4 \\text{ months} \\right)$ $\\vee$ 01.01.2018 - 30.04.2018 |            | <b>Apply</b>  | Save as       |                                                                               | $\\times$          |\n|-----------------------------------|--------------------------|-------------|-------------------------------------|------------------|-------------|--------------|--------------|-----------------------------------------------------------------------------------------------------|------------|---------------|---------------|-------------------------------------------------------------------------------|-------------------|\n| Expired<br>8.8                    | Type not in RFS $\\times$ |             |                                     |                  |             |              |              |                                                                                                     |            |               |               |                                                                               | $\\leftrightarrow$ |\n| Type                              | Product                  |             | Reference $\\sharp \\vee$ Requester C | Provider Co      | Requester A | Notional A   | <b>Quote</b> | <b>Base Curren</b>                                                                                  |            |               |               | Quote Curre Effective Date Effective Pe Maturity Date Maturity Pe Far Leg Req |                   |\n| Limit Order                       | <b>FxForward</b>         | 0.1095034.  | GroupE                              | HSBC.DEMO        | Sell        | 155,000.00   |              | <b>HKD</b>                                                                                          | CAD        | Wed. 20. Ju   | <b>Broken</b> |                                                                               | $B =$             |\n| If-Done Order                     |                          | 0-10950341  | GroupE                              | HSBC.DEMO        |             |              |              |                                                                                                     |            |               |               |                                                                               | $B =$             |\n| <b>Stop Order</b>                 | <b>FxSpot</b>            | 0-1094635   | GroupE                              | HSBC.DEMO        | Sell        | 500,000,00.  |              | CAD                                                                                                 | <b>JPY</b> | Wed. 02. Ma   | Spot          |                                                                               | $B =$             |\n| Limit Order                       | <b>FxSpot</b>            | $0-1094635$ | GroupE                              | HSBC.DEMO        | Sell        | 500.000.00.  |              | CAD                                                                                                 | <b>JPY</b> | Wed. 02. Ma   | Spot          |                                                                               | $B =$             |\n| OCO Order                         |                          | 0-1094635.  | GroupE                              | HSBC.DEMO        |             |              |              |                                                                                                     |            |               |               |                                                                               | $B =$             |\n| Market Order                      | FxForward                | 0-10944023  | GroupE                              | HSBC.DEMO        | Buy         | 102.000.00   |              | <b>EUR</b>                                                                                          | GBP        | Fri. 23. Mar. | <b>Broken</b> |                                                                               | e e               |\n| Market Order                      | FxForward                | 0-10944017  | GroupE                              | HSBC.DEMO        | Buy         | 55,000.00 G  |              | GBP                                                                                                 | <b>JPY</b> | Wed. 27. Ju   | <b>Broken</b> |                                                                               | a a               |\n| Market Order                      | FxForward                | 0.10943590  | GroupE                              | <b>HSBC.DEMO</b> | Buy         | 25,000.00 T  |              | <b>USD</b>                                                                                          | <b>TRY</b> | Fri. 23. Mar  | <b>Broken</b> |                                                                               | $B =$             |\n| Call Order                        | <b>FxSpot</b>            | 0-********  | GroupE                              | HSBC.DEMO        | Buy         | 0.00 AUD     |              | <b>AUD</b>                                                                                          | <b>JPY</b> | Wed, 02. Ma   | Spot          |                                                                               | $B =$             |\n| Stop Order                        | FxForward                | 0-1093792.  | GroupE                              | 360TBANK         | Buy         | 45,000.00 E. |              | <b>EUR</b>                                                                                          | <b>USD</b> | Thu, 22. Mar  | <b>Broken</b> |                                                                               | $B =$             |\n| Limit Order                       | FxForward                | 0-1093792   | GroupE                              | 360TBANK         | Buy         | 45,000.00 E. |              | <b>EUR</b>                                                                                          | <b>USD</b> | Thu, 22. Mar  | Broken        |                                                                               | $B$ $\\Theta$      |\n| OCO Order                         |                          | 0-********  | GroupE                              | 360TBANK         |             |              |              |                                                                                                     |            |               |               |                                                                               | $B =$             |\n| Market Order                      | <b>FxForward</b>         | 0-********  | GroupE                              | HSBC.DEMO        | Buy         | 100,000.00.  |              | <b>AUD</b>                                                                                          | <b>JPY</b> | Thu, 15. Mar  | <b>Broken</b> |                                                                               | $B =$             |\n| <b>Offline Confirmation Order</b> | <b>FxSpot</b>            | 0-********  | GroupE                              | HSBC.DEMO        | Buy         | 45,000.00 A  |              | <b>AUD</b>                                                                                          | INR        | Thu, 03. Ma   | Spot          |                                                                               | $B =$             |\n|                                   |                          |             |                                     |                  |             |              |              |                                                                                                     |            |               |               |                                                                               |                   |\n\n<span id=\"page-44-1\"></span>**Figure 42 Expired Orders**\n\n## <span id=\"page-44-0\"></span>**4.8 Executed / Done orders**\n\nExecuted orders automatically generate a trade ticket that pops up (depending on user Preferences settings) and must be acknowledged. The user who placed the order and all users belonging to the same deal tracking group will automatically get a notification about the executed order through a ticket popup and mini ticket in Execution area. Please contact our Client Advisory Services team if you would like to limit the visibility of the tickets only to the user who placed the order. If the setting is changed, other users from the same user group will only be able to see the executions by actively watching the Deal Tracking.\n\n![](_page_45_Picture_0.jpeg)\n\n| <b>RFS LIVE PRICING</b><br><b>DEAL TRACKING</b>                                                                                                                                                                                                                                    | <b>ORDER BOOK</b><br><b>EXECUTION AREA</b>                                                                                                                                                                                                                                                                                                     | $+$              |            |            |                                                                                         |      |  |                                             |  |\n|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------|------------|------------|-----------------------------------------------------------------------------------------|------|--|---------------------------------------------|--|\n| All (30)<br>Executed (5)*<br>Cancelled (24) Expired (0)*                                                                                                                                                                                                                           | $ \\alpha $<br>  ±<br>Rejected (0) Done (1)                                                                                                                                                                                                                                                                                                     |                  |            |            |                                                                                         |      |  |                                             |  |\n| Type<br>Product                                                                                                                                                                                                                                                                    | Reference # Y Requester C Provider Co Requester A Notional A                                                                                                                                                                                                                                                                                   | <b>Quote</b>     |            |            | Base Curren Quote Curre Effective Date Effective Pe Maturity Date Maturity Pe Far Leg I |      |  |                                             |  |\n| <b>Limit Order</b><br>Ticket for #EMSO-1318365/1                                                                                                                                                                                                                                   |                                                                                                                                                                                                                                                                                                                                                | $ \\Box$ $\\times$ | <b>EUR</b> | <b>USD</b> | Thu, 03. Ma                                                                             | Spot |  | 自日上口の人                                      |  |\n| Deal Confirmation   Competitive Quotes   Order History                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                | ● √ と            |            |            |                                                                                         |      |  |                                             |  |\n| Reference #<br><b>Trade Date</b><br><b>Local Date</b><br>Requester / Trader<br>Provider / Trader<br>Status<br>Product<br><b>Requester Action</b><br><b>Notional Amount</b><br><b>Opposite Amount</b><br><b>Effective Date</b><br>Spot Rate<br>Order Type<br><b>Limit Spot Rate</b> | <b>Limit Order</b><br>EMSO-1318365/1<br>Mon, 30. Apr 2018 09:37:16.218 GMT<br>Mon, 30. Apr 2018 11:37:16.218 CEST<br>GroupE / GroupE.TreasurerM<br>HSBC.DEMO / TB-HSBC.DEMO.Trader2<br>Done<br><b>FX Spot</b><br>I Buy EUR / Sell USD<br>2,345.00 EUR<br>2,654.31 USD<br>Spot // Thu, 03. May 2018<br>1.13190<br><b>Limit Order</b><br>1.13190 |                  |            |            |                                                                                         |      |  |                                             |  |\n|                                                                                                                                                                                                                                                                                    |                                                                                                                                                                                                                                                                                                                                                |                  |            |            |                                                                                         |      |  |                                             |  |\n|                                                                                                                                                                                                                                                                                    |                                                                                                                                                                                                                                                                                                                                                |                  |            |            |                                                                                         |      |  |                                             |  |\n| GroupE.TreasurerM, GroupE // QA2                                                                                                                                                                                                                                                   |                                                                                                                                                                                                                                                                                                                                                |                  | FECT       |            |                                                                                         |      |  | Mo, 30. Apr 2018, 09:52:56 GMT // Connected |  |\n\n<span id=\"page-45-1\"></span>**Figure 43 Executed Order**\n\n## <span id=\"page-45-0\"></span>**4.9 Special Slice Order Workflow**\n\nDifferent than other order types, Slice orders are not placed to a single provider to be further accepted or rejected. They are sent as Request-For-Stream (RFS) requests with multiple liquidity-providers according to pre-defined parameters.\n\nSlice Orders do not flow from one tab to another. Instead, they remain in Initialized tab until they are fully executed or deleted.\n\nAs shown in Figure 44 [Slice Order creation,](#page-46-0) after a Slice Order is created, it appears in the Initialization Panel and it is possible to amend it, delete it, send it as RFS or place it.\n\nThe difference between \"Send as RFS\" and \"Place\" is that in the first one, a single RFS with the full amount will be sent to the pre-defined liquidity providers while in the second one, it triggers the execution algorithm based on the pre-defined parameters described in [4.1.10.](#page-30-0)\n\n![](_page_46_Picture_0.jpeg)\n\n| <b>RFS REQUESTER</b> | ORDER MANAGEMENT     |              | <b>DEAL TRACKING</b> |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                |                                         |\n|----------------------|----------------------|--------------|----------------------|---------------|-------------------------|---------|------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------|------------------------------------------------|-----------------------------------------|\n|                      |                      |              |                      |               | Trade As (GroupH        |         |            | $\\checkmark$                                                                                                                                                                                                                                                              |                                                                  | Order View                                     | $O$ $R$                                 |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  | <b>Slice Order</b>                             |                                         |\n| Initialized (3)      | Q<br>· Sent (8)      | $\\pm$        |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                |                                         |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                | Initialized                             |\n| Type                 | Reference #          | Order Status | Status               | Legal Entity  | Requester Ac Currencies |         | Product    | Notio:                                                                                                                                                                                                                                                                    | Order Details                                                    | Order Changes<br>Fills (0)                     |                                         |\n| Slice Order          | EMSO-50454           | Initialized  | <b>Initialized</b>   | <b>GroupH</b> | Buy                     | EUR/USD | FX Forward | $\\frac{1}{2}$<br>$10,0($ $\\odot$ $\\mid$ $\\nu$ $\\mid$ $\\triangleright$ $\\mid$                                                                                                                                                                                              | Product                                                          |                                                | <b>FX Forward</b>                       |\n| Market Order         | $\\bullet$ EMSO-24223 | Initialized  | <i>mitialized</i>    | <b>GroupH</b> | Buy                     | EUR/USD | FX SOOT    | $98.7(\\begin{array}{c} 0 \\\\ \\end{array} \\begin{array}{c} \\end{array}) \\begin{array}{c} \\end{array}) \\begin{array}{c} \\begin{array}{c} \\end{array} \\begin{array}{c} \\end{array}) \\begin{array}{c} \\end{array}) \\begin{array}{c} \\end{array}) \\begin{array}{c} \\end{array}$ | Action                                                           |                                                | I Buy EUR / Sell USD                    |\n| Market Order         | <b>0</b> EMSO-24223  | Initialized  | mitialized           | <b>CroupH</b> | Buy                     | EUR/USD | FX Spot    | 123.4 日 ジ D 3     日                                                                                                                                                                                                                                                       | Forward Date                                                     |                                                | Thu, 15, Apr 2021                       |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                |                                         |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           | No worse than                                                    |                                                | 1.1931857                               |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           | Total Number of Executions                                       |                                                | 5                                       |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           | Trade with LP > once                                             |                                                | Yes                                     |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           | <b>LPs per Request</b>                                           |                                                | Random                                  |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>Gap Between Requests |                                                | Random<br>Random                        |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           | Market Rate                                                      |                                                | 1.18097                                 |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                |                                         |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                |                                         |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           | Partial Fill Progress                                            |                                                | Total                                   |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                | 10,000,000.00 EUR                       |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                | completed                               |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  | 0%                                             | 0.00 EUR                                |\n|                      |                      |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                | Remaining<br>10,000,000,00 EUR          |\n| Accepted (24)        | $Q$ $\\pm$            |              |                      |               |                         |         |            |                                                                                                                                                                                                                                                                           |                                                                  |                                                |                                         |\n| Type                 | Reference #          | Order Status | status               | Legal Entity  | Requester Ac Currencies |         | Product    | Notional Am.                                                                                                                                                                                                                                                              | Expiry                                                           |                                                | Thu, 15, Apr 2021 // 22:00:00 GMT       |\n| Limit Order          | EMSO-30936.          | Active       | Pending Can CroupH   |               | Buy                     | EUR/USD | FX Spot    | 5,000,000.00. 日日                                                                                                                                                                                                                                                          | Expiry - Local                                                   |                                                | Fri, 16. Apr 2021 // 00:00:00 CEST      |\n| Algo Order           | EMSO-35907           | Active       | Accepted             | GroupH        | Buy                     | EUR/USD | FX Spot    | 4,006.00 EUR $\\boxed{\\ominus}$ 3                                                                                                                                                                                                                                          | Order ID                                                         |                                                | EMSO-5045475                            |\n| $\\times$ Loop Order  | EMSO-45310.          |              | Pending Can CroupH   |               |                         |         |            | $O$ $G$ $G$                                                                                                                                                                                                                                                               |                                                                  |                                                |                                         |\n| Limit Order          | EMSO-48369.          | Active       | Pending Can GroupH   |               | Buy                     | EUR/NOK | FX SOOT    | $\\circ$ a<br>e<br>40.000.000.0.                                                                                                                                                                                                                                           | Counterparts                                                     | Request Changes                                |                                         |\n| Stop Order           | EMSO-48380           | Active       | Pending Can GroupH   |               | Buy                     | EUR/USD | FX Spot    | $\\Theta$<br>5.000.000.00.<br>$\\mathcal{Q}$                                                                                                                                                                                                                                | 360T.AMERICAS                                                    | 360T.APAC                                      | 360TBANK.TEST                           |\n| Stop Order           | EMSO-48380           | Active       | Pending Can GroupH   |               | Buy                     | EUR/USD | FX Spot    | e<br>$\\circ$<br>12,000,000.0                                                                                                                                                                                                                                              | ABC.BANK                                                         | ABN AMRO.DEMO                                  | <b>BANKWINTER.TEST</b>                  |\n| Limit Order          | EMSO-48380.          | Active       | Pending Can., GroupH |               | Buy                     | EUR/NOK | FX Spot    | 38<br>40,100,000.0.                                                                                                                                                                                                                                                       | BNPP.DEMO                                                        | BOAL DEMO                                      | Bank of China London.TEST               |\n| Limit Order          | EM50-48380.          | Active       | Pending Can GroupH   |               | Buy                     | EUR/NOK | FX Spot    | $\\circ$ $\\circ$<br>o<br>40,300,000.0                                                                                                                                                                                                                                      | Bank of Ireland.TEST                                             | BankC                                          | BankD                                   |\n| Limit Order          | EMSO-48380           | Active       | Pending Can GroupH   |               | Buy                     | EUR/NOK | FX Soot    | $\\circ$ a<br>101,000,000                                                                                                                                                                                                                                                  | BankM                                                            | BankN                                          | BankO                                   |\n| Limit Order          | EMSO-48380           | Active       | Pending Can GroupH   |               | Buy                     | EUR/NOK | FX Spot    | $\\circ$<br>⊕∣⊛<br>99,000,000.0                                                                                                                                                                                                                                            | BankP<br>CITIBANK.DEMO                                           | Barclays BARX.DEMO<br>COBA, FRA DRESDNER, DEMO | Barclays SEF.DEMO<br>Credit Suisse DEMO |\n| Market Order         | EMSO-45996           | Active       | Pending Can., GroupH |               | Buy                     | EUR/USD | FX Spot    | 回晶<br>5,550.00 EUR <b>ID</b>                                                                                                                                                                                                                                              | <b>DB.DEMO</b>                                                   | HSBC.DEMO                                      | JPM.GTX                                 |\n| Stop Order           | EMSO-45520.          | Active       | Pending Can., CroupH |               | Buy                     | EUR/USD | FX Spot    | $\\circ$ a<br>o<br>3,800.00 EUR                                                                                                                                                                                                                                            | JPMORGAN.DEMO                                                    | RBS.LND.DEMO                                   |                                         |\n| $\\vee$ OCD Order     | EMSO-35850.          |              | Accepted             | <b>GroupH</b> |                         |         |            | $C$ $S$ $\\Rightarrow$                                                                                                                                                                                                                                                     |                                                                  |                                                |                                         |\n| $\\vee$ OCO Order     | EMSO-35850.          |              | Accepted             | GroupH        |                         |         |            | C & 8G<br>2,004.00 EUR 0 8                                                                                                                                                                                                                                                | Amend                                                            | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!       | Place<br>Send as RFS                    |\n\n<span id=\"page-46-0\"></span>**Figure 44 Slice Order creation**\n\nAfter the execution algorithm is triggered, the first slice (RFS) is sent to pre-defined liquidity providers and the execution progress can be followed in the Order View (slide ticket) as shown in Figure 45 [Slice Order execution -](#page-46-1) Order View. The figure shows also the first ticket for the executed RFS which also appears in the Executions area.\n\n| Initialized (1)<br>Sent (0)<br>Type                   | $Q \\pm$<br>Reference #<br><b>Order Status</b><br>Status                                        | <b>Legal Entity</b> | Requester A Currencies        |                | Product |                                      |          |                                                         |                        | Order View<br><b>Slice Order</b> | $O$ $\\oplus$                                             | Executions<br>GroupH             |\n|-------------------------------------------------------|------------------------------------------------------------------------------------------------|---------------------|-------------------------------|----------------|---------|--------------------------------------|----------|---------------------------------------------------------|------------------------|----------------------------------|----------------------------------------------------------|----------------------------------|\n| <b>Slice Order</b>                                    | EMSO-1440<br>Initialized<br>Initialized                                                        | <b>GroupH</b>       | <b>Buy</b>                    | <b>EUR/USD</b> |         | FX Forward <b>D</b> / D → <b>1 D</b> |          |                                                         |                        |                                  | Initialized                                              | FX Forward<br>Buy EUR / Sell USD |\n|                                                       | Ticket for #EMSO-1440930                                                                       | $ \\alpha$ $\\times$  |                               |                |         |                                      |          | Order Details<br>Product                                | Order Changes          | Fills(1)                         | <b>FX Forward</b>                                        | 3,280,000.00 EUR<br>@1.1896630   |\n| <b>Deal Confirmation</b>                              | Competitive Quotes Order History                                                               | √ ● と               |                               |                |         |                                      |          | Action<br>Forward Date                                  |                        |                                  | I Buy EUR / Sell USD<br>Tue, 30. Mar 2021                |                                  |\n|                                                       | <b>FX Forward</b>                                                                              |                     |                               |                |         |                                      |          | No worse than                                           |                        |                                  | 1.195                                                    |                                  |\n| Reference #<br>Trade Id                               | EMSO-1440930<br>********                                                                       |                     |                               |                |         |                                      |          | Trade with $LP >$ once<br>LPs per Request               |                        |                                  | Yes<br>Random                                            |                                  |\n| Parent #                                              | EMSO-1440928                                                                                   |                     |                               |                |         |                                      |          | Trade Size per Execution<br><b>Gap Between Requests</b> |                        |                                  | Random<br>60 seconds                                     |                                  |\n| Trade Date<br>Local Date<br>UTI                       | Fri, 19. Mar 2021 13:23:38.105 GMT<br>Fri, 19. Mar 2021 14:23:38.105 CET<br>101000028134601740 |                     |                               |                |         |                                      |          | Market Rate                                             |                        |                                  | 1.18925                                                  |                                  |\n| Requester / Trader<br>Provider / Trader               | GroupH / GroupH.TreasurerD<br>360TBANK.TEST / 360TBANK.AutoDealer                              |                     |                               |                |         |                                      |          | Partial Fill Progress                                   |                        |                                  | Total<br>12.000.000.00 EUR<br>Completed                  |                                  |\n| Status<br>Product                                     | Executed<br>FX Forward                                                                         |                     |                               |                |         |                                      |          |                                                         |                        | 27.33%                           | 3.280.000.00 EUR<br>Remaining<br>8,720,000,00 EUR        |                                  |\n| Requester Action<br>Notional Amount                   | I Buy EUR / Sell USD<br>3,280,000.00 EUR                                                       |                     | <b>Requester A Currencies</b> |                | Product | Notional A                           | Notional | Expiry                                                  |                        |                                  | Tue, 30. Mar 2021 // 22:00:00 GMT                        |                                  |\n| Opposite Amount<br><b>Effective Date</b><br>Spot Rate | 3.902.094.64 USD<br>1 Week // Tue, 30. Mar 2021<br>1,18950                                     |                     |                               |                |         |                                      |          | Expiry - Local<br>Order ID                              |                        |                                  | Wed. 31. Mar 2021 // 00:00:00 CEST<br>EMSO-1440928       |                                  |\n| Forward Points<br>Forward Rate                        | 1.630<br>1.1896630                                                                             |                     |                               |                |         |                                      |          | Counterparts                                            | <b>Request Changes</b> |                                  |                                                          |                                  |\n| <b>PTMMM</b>                                          | 1.1894615                                                                                      |                     |                               |                |         |                                      |          | 360TBANK.TEST<br><b>CITIBANK.DEMO</b>                   |                        | <b>BOAL DEMO</b><br>COBA.DEMO    | <b>Barclays BARX.DEMO</b><br><b>COBA FRA DRESDNER.DE</b> |                                  |\n| Order Type                                            | Market Order                                                                                   |                     |                               |                |         |                                      |          | <b>HSBC.DEMO</b><br>Unicredit.DEMO                      |                        | <b>RBS.LND.DEMO</b>              | SEB.DEMO                                                 |                                  |\n|                                                       |                                                                                                |                     |                               |                |         |                                      |          |                                                         |                        |                                  | Withdraw<br>Pause                                        |                                  |\n|                                                       | <b>EEÜT</b>                                                                                    | Acknowledge         |                               |                |         |                                      |          |                                                         |                        |                                  |                                                          |                                  |\n\n<span id=\"page-46-1\"></span>**Figure 45 Slice Order execution - Order View**\n\nFrom the Order View, the Slice Order can be paused as well as resumed as show in [Figure 46](#page-47-0) [Slice Order Execution Actions.](#page-47-0) Other possible actions are Withdraw and Send as RFS. When a Slice Order is withdrawn during negotiation it can be amended before selecting the next\n\n![](_page_47_Picture_0.jpeg)\n\naction. It is possible to amend \"No Worse Than\" rate only. All other parameters reflect the remaining part of the order or, what was not yet executed. Examples: remaining amount, remaining number of executions and remaining providers in the bank basket. If the amended Slice Order is intended to continue to auto-execute, it has to be placed again.\n\n| Initialized (1)<br>Sent (0)<br>Reference #<br>Type                                                                                                                                                                        | $\\begin{array}{c} \\mathbf{Q} & \\mathbf{E} \\end{array}$<br><b>Order Status</b><br><b>Status</b>                                                                                                                                                                                                                                                                | <b>Legal Entity</b>                        | Requester A., Currencies | Product                           |                                                                                                                                                                                                                                 | Order View<br><b>Slice Order</b>                                                      | $C$ $\\oplus$                                                                                                                                                                                                                                         | Executions<br>GroupH                                               |\n|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------|--------------------------|-----------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|\n| <b>Slice Order</b><br>EMSO-1440.                                                                                                                                                                                          | Initialized<br>Initialized<br>Ticket for #EMSO-1440930                                                                                                                                                                                                                                                                                                        | <b>GroupH</b><br>Buy<br>$ \\alpha$ $\\times$ | EUR/USD                  | 10 / D H 6 5<br><b>FX Forward</b> | <b>Order Details</b>                                                                                                                                                                                                            | Order Changes<br>Fills(1)                                                             | Initialized                                                                                                                                                                                                                                          | FX Forward<br>Buy EUR / Sell USD<br>3.280.000.00 EUR<br>@1.1896630 |\n| <b>Deal Confirmation</b>                                                                                                                                                                                                  | Competitive Quotes Order History                                                                                                                                                                                                                                                                                                                              | √ 昼 と                                      |                          |                                   | Product<br>Action<br><b>Forward Date</b>                                                                                                                                                                                        |                                                                                       | <b>FX Forward</b><br>I Buy EUR / Sell USD<br>Tue, 30, Mar 2021                                                                                                                                                                                       |                                                                    |\n| Reference #<br>Trade Id<br>Parent #<br>Trade Date<br>Local Date<br>UTI<br>Requester / Trader<br>Provider / Trader<br>Status<br>Product<br>Requester Action<br>Notional Amount<br>Opposite Amount<br><b>Effective Date</b> | <b>FX Forward</b><br>EMSO-1440930<br>********<br>EMSO-1440928<br>Fri, 19. Mar 2021 13:23:38.105 GMT<br>Fri, 19. Mar 2021 14:23:38.105 CET<br>101000028134601740<br>GroupH / GroupH.TreasurerD<br>360TBANK.TEST / 360TBANK.AutoDealer<br>Executed<br>FX Forward<br>I Buy EUR / Sell USD<br>3,280,000.00 EUR<br>3.902.094.64 USD<br>1 Week // Tue, 30. Mar 2021 |                                            | Requester A., Currencies | Product<br>Notional A             | No worse than<br>Trade with $LP >$ once<br><b>LPs per Request</b><br>Trade Size per Execution<br><b>Gap Between Requests</b><br>Market Rate<br><b>Partial Fill Progress</b><br>Notional<br>Expiry<br>Expiry - Local<br>Order ID | 27.33%                                                                                | 1.195<br>Yes<br>Random<br>Random<br>60 seconds<br>1.18911<br>Total<br>12,000,000,00 EUR<br>Completed<br>3,280,000.00 EUR<br>Remaining<br>8.720.000.00 EUR<br>Tue, 30. Mar 2021 // 22:00:00 GMT<br>Wed. 31. Mar 2021 // 00:00:00 CEST<br>EMSO-1440928 |                                                                    |\n| Spot Rate<br>Forward Points<br>Forward Rate<br>PTMMM<br>Order Type                                                                                                                                                        | 1.18950<br>1.630<br>1.1896630<br>1.1894615<br>Market Order                                                                                                                                                                                                                                                                                                    |                                            |                          |                                   | Counterparts<br>360TBANK.TEST<br>CITIBANK.DEMO<br><b>HSBC.DEMO</b><br>Unicredit.DEMO                                                                                                                                            | <b>Request Changes</b><br><b>BOAL.DEMO</b><br><b>COBA.DEMO</b><br><b>RBS.LND.DEMO</b> | <b>Barclays BARX.DEMO</b><br><b>COBA FRA DRESDNER.DE</b><br>SEB.DEMO                                                                                                                                                                                 |                                                                    |\n|                                                                                                                                                                                                                           |                                                                                                                                                                                                                                                                                                                                                               |                                            |                          |                                   |                                                                                                                                                                                                                                 | Withdraw                                                                              | Send as RFS<br>Resume                                                                                                                                                                                                                                |                                                                    |\n\n<span id=\"page-47-0\"></span>**Figure 46 Slice Order Execution Actions**\n\nWhen a partially filled slice order is sent as RFS, it sends Request-For-Stream (RFS) with the remaining amount (not yet executed) to the same pre-defined liquidity providers.\n\nAll other actions are similar to other order types workflows as well as the tickets generated.\n\nWhen the Slice Order finishes to execute successfully, it disappears from the Initialization panel as shown in Figure 47 [Executed Slice Order.](#page-48-0) All executions appear in Execution area as well as in Deal Tracking.\n\n![](_page_48_Picture_0.jpeg)\n\n| <b>RFS REQUESTER</b>                                                                                                                                             | $^{+}$<br><b>ORDER MANAGEMENT</b>                                                                                                                              |                                                                                                                                                    |                                                                                                                                                         |                                                                                                                                                                  |                                                                                                                                                         |                    |                                                                                      |                                  |\n|------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|--------------------------------------------------------------------------------------|----------------------------------|\n| Initialized (0)<br>$\\Box$<br>Type                                                                                                                                | $Q \\neq$<br>Sent (0)<br><b>Order Status</b><br>Status<br>Reference #<br><b>Legal Entity</b>                                                                    | Requester A.,. Currencies                                                                                                                          | Product<br>Notional A.                                                                                                                                  | Notional Cu., Rate                                                                                                                                               | <b>Expiry Type</b><br><b>Expiry Date</b> % to market                                                                                                    | Market Rate Re     | Executions<br>GroupH                                                                 | V                                |\n| Ê                                                                                                                                                                | $ \\circ$ $\\times$<br>Ticket for #EMSO-1440930                                                                                                                  |                                                                                                                                                    |                                                                                                                                                         |                                                                                                                                                                  | Ticket for #EMSO-1440937                                                                                                                                | $ \\Box$ $\\times$   | <b>FX Forward</b><br>Buy EUR / Sell USD<br>4,960,000,00 EUR<br>@1.1883630            | $\\frac{1}{2}$<br>$\\oplus$        |\n| 个<br>↓<br><b>Deal Confirmation</b>                                                                                                                               | 最上<br>Competitive Ouotes Order History<br>$\\checkmark$<br>FX Forward                                                                                           | <b>Deal Confirmation</b>                                                                                                                           | $-$ 0 $\\times$<br>Ticket for #EMSO-1440934<br>Competitive Quotes Order History √ →<br>FX Forward                                                        | <b>Deal Confirmation</b>                                                                                                                                         | Competitive Quotes  Order History<br><b>FX Forward</b>                                                                                                  | v a<br>也           | GroupH<br><b>FX Forward</b><br>Buy EUR / Sell USD<br>3.760.000.00 EUR<br>@ 1.1883430 | $\\rightarrow$<br>8               |\n| Reference #<br>Trade Id<br>Parent #<br><b>Trade Date</b><br>Local Date<br>UTI                                                                                    | EMSO-1440930<br>********<br>EMSO-1440928<br>Fri, 19, Mar 2021 13:23:38.105 GMT<br>Fri. 19. Mar 2021 14:23:38.105 CET<br>101000028134601740                     | Reference #<br>Trade Id<br>Parent #<br><b>Trade Date</b><br>Local Date<br>UTI                                                                      | EMSO-1440934<br>10922739<br>EMSO-1440928<br>Fri, 19. Mar 2021 13:34:55.240 GMT<br>Fri, 19. Mar 2021 14:34:55.240 CET<br>101000028134601753              | Reference #<br>Trade Id<br>Parent #<br><b>Trade Date</b><br>Local Date<br>UTI                                                                                    | EMSO-1440937<br>********<br>EMSO-1440928<br>Fri. 19. Mar 2021 13:35:55.466 GMT<br>Fri, 19. Mar 2021 14:35:55.466 CET<br>101000028134601767              |                    | GroupH<br>FX Forward<br>Buy EUR / Sell USD<br>3.280,000.00 EUR<br>@1.1896630         | $\\sqrt{ }$<br>$\\pm$<br>$\\ominus$ |\n| Provider / Trader<br>Status                                                                                                                                      | Requester / Trader<br>GroupH / GroupH.TreasurerD<br>360TBANK.TEST / 360TBANK.AutoDealer<br>Executed                                                            | Requester / Trader<br>Provider / Trader<br>Status                                                                                                  | GroupH / GroupH.TreasurerD<br>COBA.DEMO / COBA.DEMO.AutoDealer<br>Executed                                                                              | Requester / Trader<br>Provider / Trader<br>Status                                                                                                                | GroupH / GroupH.TreasurerD<br>360TBANK.TEST / 360TBANK.AutoDealer<br>Executed                                                                           |                    |                                                                                      |                                  |\n| Product<br><b>Requester Action</b><br>Notional Amount<br><b>Opposite Amount</b><br><b>Effective Date</b><br>Spot Rate<br>Forward Points<br>Forward Rate<br>PTMMM | <b>FX Forward</b><br>I Buy EUR / Sell USD<br>3.280.000.00 EUR<br>3.902.094.64 USD<br>1 Week // Tue, 30, Mar 2021<br>1.18950<br>1.630<br>1.1896630<br>1.1894615 | Product<br>Requester Action<br>Notional Amount<br>Opposite Amount<br><b>Effective Date</b><br>Spot Rate<br>Forward Points<br>Forward Rate<br>PTMMM | FX Forward<br>I Buy EUR / Sell USD<br>3.760.000.00 EUR<br>4.468.169.68 USD<br>1 Week // Tue, 30, Mar 2021<br>1,18818<br>1.630<br>1.1883430<br>1.1883315 | Product<br><b>Requester Action</b><br>Notional Amount<br>Opposite Amount<br><b>Effective Date</b><br>Spot Rate<br>Forward Points<br>Forward Rate<br><b>PTMMM</b> | FX Forward<br>I Buy EUR / Sell USD<br>4,960,000,00 EUR<br>5,894,280.48 USD<br>1 Week // Tue, 30. Mar 2021<br>1.18820<br>1.630<br>1.1883630<br>1.1881615 | Rs                 |                                                                                      |                                  |\n| Order Type                                                                                                                                                       | Market Order                                                                                                                                                   | Order Type                                                                                                                                         | Market Order                                                                                                                                            | Order Type                                                                                                                                                       | Market Order                                                                                                                                            |                    |                                                                                      |                                  |\n|                                                                                                                                                                  | 36 <b>8</b> T<br>Acknowledge                                                                                                                                   |                                                                                                                                                    | <b>BEWT</b><br>Acknowledge                                                                                                                              |                                                                                                                                                                  | $\\equiv$ $\\equiv$ $\\equiv$                                                                                                                              | <b>Acknowledge</b> |                                                                                      |                                  |\n\n#### <span id=\"page-48-0\"></span>**Figure 47 Executed Slice Order**\n\nIn case a Slice Order cannot be filled and execution fails, a warning ticket is generated and displayed in the Execution area as shown in Figure 48 [Slice Order warning ticket.](#page-48-1)\n\n| <b>Executions</b>                                                                   |                  |\n|-------------------------------------------------------------------------------------|------------------|\n| EMSO-1440979<br>GroupH<br><b>FX Swap</b><br>Buy EUR / Sell USD<br>55,555,555.00 EUR | <b>Cancelled</b> |\n\n<span id=\"page-48-1\"></span>**Figure 48 Slice Order warning ticket**\n\n![](_page_49_Picture_0.jpeg)\n\n|                                        | Ticket for #EMSO-1441007                                                                                                                                | $\\Box$ $\\times$ |\n|----------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|\n| <b>Request</b><br><b>Order History</b> |                                                                                                                                                         | 0<br>也<br>J     |\n|                                        | <b>FX Swap</b>                                                                                                                                          |                 |\n| Reference #                            | EMSO-1441007                                                                                                                                            |                 |\n| Parent #                               | EMSO-1440979                                                                                                                                            |                 |\n| Requester / Trader                     | GroupH / GroupH.TreasurerD                                                                                                                              |                 |\n| Provider / Trader                      | 360TBANK.TEST, COBA.DEMO, CITIBANK.DEMO, RBS.LND.DEMO,<br>SEB.DEMO, BOAL.DEMO, COBA.FRA DRESDNER.DEMO, Barclays<br>BARX.DEMO, Unicredit.DEMO, HSBC.DEMO |                 |\n| Status                                 | Cancelled (Rfs negotiation expired. No reference market data<br>available)                                                                              |                 |\n| Product                                | <b>FX Swap</b>                                                                                                                                          |                 |\n| Near Leg                               | I Sell EUR / Buy USD                                                                                                                                    |                 |\n| Notional Amount                        | 55,555,555.00 EUR                                                                                                                                       |                 |\n| <b>Effective Date</b>                  | Spot // Tue, 23. Mar 2021                                                                                                                               |                 |\n| Far Leg                                | I Buy EUR / Sell USD                                                                                                                                    |                 |\n| Notional Amount                        | 55,555,555.00 EUR                                                                                                                                       |                 |\n| Maturity Date                          | Broken / Fri, 28. May 2021                                                                                                                              |                 |\n| Order Type                             | Market Order                                                                                                                                            |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n|                                        |                                                                                                                                                         |                 |\n\nThis is a sample warning message displayed in the form of a ticket when a Slice Order fails to fulfil.\n\n<span id=\"page-49-0\"></span>**Figure 49 Slice Order warning message**\n\nThe warning or cancelled ticket can be cleared from the Executions are only after one of the available actions are selected: Amend, Delete or Send as RFS as shown in [Figure 50](#page-50-1) Slice [Order actions upon failure.](#page-50-1)\n\n![](_page_50_Picture_0.jpeg)\n\n|                                          |                           | ☆ 而                                                                                          |                                                                     |\n|------------------------------------------|---------------------------|----------------------------------------------------------------------------------------------|---------------------------------------------------------------------|\n|                                          | Order View                | $\\theta$ a                                                                                   | <b>Executions</b><br>⋇                                              |\n|                                          | <b>Slice Order</b>        |                                                                                              | Cancelled<br>EMSO-7470827/3                                         |\n| <b>Order Details</b>                     | Order Changes<br>Fills(0) | Initialized                                                                                  | GroupH<br><b>FX Swap</b><br>Sell EUR / Buy USD<br>10,272,633.00 EUR |\n| Product                                  |                           | <b>FX Swap</b>                                                                               |                                                                     |\n| Near Leg                                 |                           | I BUY EUR / Sell USD                                                                         |                                                                     |\n| <b>Effective Date</b>                    |                           | Fri, 19. Nov 2021                                                                            |                                                                     |\n| Far Leg                                  |                           | I Sell EUR / Buy USD                                                                         |                                                                     |\n| <b>Maturity Date</b>                     |                           | Fri, 10. Dec 2021                                                                            |                                                                     |\n| No worse than                            |                           | 4.014                                                                                        |                                                                     |\n| <b>Total Number of Executions</b>        |                           | 3                                                                                            |                                                                     |\n| Trade with $LP >$ once                   |                           | <b>No</b>                                                                                    |                                                                     |\n| LPs per Request                          |                           | Random                                                                                       |                                                                     |\n| <b>Trade Size per Execution</b>          |                           | Random                                                                                       |                                                                     |\n| Gap Between Requests                     |                           | 15 seconds                                                                                   |                                                                     |\n| <b>Market Rate</b>                       |                           | 1.13058                                                                                      |                                                                     |\n| <b>Partial Fill Progress</b>             | 0%                        | <b>Total</b><br>32,785,000.00 EUR<br>Completed<br>0.00 EUR<br>Remaining<br>32,785,000.00 EUR |                                                                     |\n| <b>Expiry</b>                            |                           | Fri, 19. Nov 2021 // 22:00:00 GMT                                                            |                                                                     |\n| Expiry - Local                           |                           | Fri, 19. Nov 2021 // 23:00:00 CET                                                            |                                                                     |\n| Order ID                                 |                           | EMSO-7470827/3                                                                               |                                                                     |\n| Counterparts                             | <b>Request Changes</b>    |                                                                                              |                                                                     |\n| <b>ABC.BANK</b>                          | <b>ABN AMRO.DEMO</b>      | <b>BANKWINTER.TEST</b>                                                                       |                                                                     |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Bank of Ireland TEST      | BankD                                                                                        |                                                                     |\n| Amend<br>BARX.DEMO                       | <b>Barclays SEF.DEMO</b>  | Send as RFS<br><b>Delete</b>                                                                 |                                                                     |\n\n<span id=\"page-50-1\"></span>**Figure 50 Slice Order actions upon failure**\n\n# <span id=\"page-50-0\"></span>**5 Grouping Orders**\n\nThe Bridge Order Book tool offers the functionality to group several 'market orders' with the same product details. Users who are eligible to Trade-On-Behalf (TOB) trading can group orders with several differing legal entities. This is also known as pre-trade allocations, and it shows the respective banks for which fund or legal entity the request is done.\n\nTrade-As (TAS) users can group spot and/or outright as well as NDF orders into block requests among one TAS legal entity.\n\nThe criteria to group several market orders are outlined in [Table 3.](#page-51-1)\n\n![](_page_51_Picture_0.jpeg)\n\n| Product     | Grouping criteria                                                                                    |\n|-------------|------------------------------------------------------------------------------------------------------|\n| Spot        | Trade-on-Behalf user type: legal entities have relationships<br>with the same liquidity providers    |\n|             | Orders have same currency pair                                                                       |\n|             | Orders have same notional currency                                                                   |\n| Outright    | Trade-on-Behalf user type: legal entities have relationships<br>with the same liquidity providers    |\n|             | Orders have same currency pair                                                                       |\n|             | Orders have same notional currency                                                                   |\n| NDF         | Trade-on-Behalf user type:<br>legal entities have relationships<br>with the same liquidity providers |\n|             | Orders have same currency pair                                                                       |\n|             | Orders have same notional currency                                                                   |\n|             | Orders have same fixing reference                                                                    |\n| Swap        | Trade-on-Behalf user type                                                                            |\n|             | Legal entities have relationships with the same liquidity<br>providers                               |\n|             | Orders have same currency pair                                                                       |\n|             | Orders have same notional currency                                                                   |\n|             | Orders have same effective date                                                                      |\n|             | Orders have same maturity date                                                                       |\n| Uneven Swap | Grouping not supported                                                                               |\n| NDS         | Grouping not supported                                                                               |\n\n#### <span id=\"page-51-1\"></span>**Table 3: Aggregation criteria**\n\nIn order to group multiple orders: Choose the desired orders in the 'Initialized' view with a left mouse click while simultaneously holding the CTRL key. Right click on the grouped orders. Select 'Group' from the context menu. As visualized in the right panel of [Figure 51,](#page-51-0) the selected orders are also shown in the 'Order Summary' view.\n\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                            |                             |                     |                         |                     |                     |           |          |                     | $\\vee$ Preferences   | $\\vee$ Help        |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------|-----------------------------|---------------------|-------------------------|---------------------|---------------------|-----------|----------|---------------------|----------------------|--------------------|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | <b>RFS REQUESTER</b>                       | <b>DEAL TRACKING</b>        |                     | <b>ORDER MANAGEMENT</b> | $+$                 |                     |           |          |                     |                      |                    |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                            |                             |                     |                         |                     |                     |           |          |                     |                      |                    |\n| $\\mathsf{E}^\\mathsf{O}$                                                                                                                                                                                                                                                                                                                                                                                                                                                    |                                            |                             |                     | <b>Trade As</b>         | FundE.2             |                     |           |          | Order Summary (2)   |                      |                    |\n| $\\frac{1}{2}$                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                                            |                             |                     |                         |                     |                     |           |          | <b>Market Order</b> |                      | $\\times$           |\n| Ê                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | Initialized (2)<br>Sent $(3)$              | lQ.<br>目と                   |                     |                         |                     |                     |           |          | EMSO-1491545        |                      | Initialized        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | <b>Type</b>                                | Reference #<br>$\\checkmark$ | <b>Order Status</b> | <b>Status</b>           | <b>Legal Entity</b> | <b>Requester Ar</b> |           | Product  |                     |                      | <b>FX Spot</b>     |\n| $\\hat{\\mathcal{L}}$                                                                                                                                                                                                                                                                                                                                                                                                                                                        | <b>Market Ordor</b><br>> Place             | EMED 1401E                  | Initialized         | Initialized             | FundE.2             | <b>BUY</b>          | 刘 面<br>Ŵ. | Action   |                     | I Buy EUR / Sell USD |                    |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | Market <sup>(</sup><br>$\\frac{1}{2}$ Amend |                             | <b>Initialized</b>  | <b>Initialized</b>      | FundE.1             | <b>BUY</b>          | A.<br>ᅰ画  | Notional |                     | 2,000,000.00 EUR     |                    |\n| 也                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | û<br>Delete                                |                             |                     |                         |                     |                     |           |          | <b>Market Order</b> |                      | $\\times$           |\n| $\\frac{1}{2} \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac$ | <b>CO</b> Group                            |                             |                     |                         |                     |                     |           |          | EMSO-1491544        |                      | <b>Initialized</b> |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                            |                             |                     |                         |                     |                     |           | Product  |                     |                      | <b>FX Spot</b>     |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                            |                             |                     |                         |                     |                     |           | Action   |                     | I Buy EUR / Sell USD |                    |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                            |                             |                     |                         |                     |                     |           | Notional |                     | ************ EUR     |                    |\n\n<span id=\"page-51-0\"></span>**Figure 51 Grouping of orders**\n\n![](_page_52_Picture_0.jpeg)\n\nA maximum of 150 orders can be grouped. Once a grouped order has been created, it can be sent out as an RFS request to multiple liquidity providers.\n\n|            | <b>RFS LIVE PRICING</b> | <b>ORDER MANAGEMENT</b>     |                     | <b>DEAL TRACKING</b> | $^+$                       |            |                         |      |                  |               |  |\n|------------|-------------------------|-----------------------------|---------------------|----------------------|----------------------------|------------|-------------------------|------|------------------|---------------|--|\n|            |                         |                             |                     |                      |                            |            |                         |      |                  |               |  |\n| é          |                         |                             |                     |                      | FundE.2<br><b>Trade As</b> |            |                         |      |                  |               |  |\n| Ê          | Initialized (1)         | 土<br>Sent (0)               |                     |                      |                            |            |                         |      |                  |               |  |\n|            | <b>Type</b>             | $\\checkmark$<br>Reference # | <b>Order Status</b> | <b>Status</b>        | <b>Legal Entity</b>        | Requester  |                         |      |                  |               |  |\n| $\\uparrow$ | $\\land$ Group Order     | EMSO-14673                  |                     | Initialized          | <b>GroupE</b>              | <b>BUY</b> | $\\hbox{\\large \\it \\Xi}$ |      | $\\triangleright$ | 画             |  |\n|            | <b>Market Order</b>     | EMSO-14673                  | Initialized         | Initialized          | FundE.1                    | <b>Buy</b> | C                       | =/ D |                  | Send as RFS . |  |\n| と          | <b>Market Order</b>     | EMSO-14660                  | Initialized         | Initialized          | FundE.2                    | Buy        | c                       | S)   | $\\,>$            | →□ 画          |  |\n\n<span id=\"page-52-3\"></span>**Figure 52 Send grouped order as RFS (block TOB request)**\n\nIf necessary, grouped orders can be un-grouped again to the underlying orders.\n\n## <span id=\"page-52-0\"></span>**5.1 Orders of varying custom field values**\n\nMarket Orders consisting of different custom field values can be grouped. In the event of an execution, each leg of the aggregated order results in a transaction with the initially selected custom field values.\n\n## <span id=\"page-52-1\"></span>**5.2 Orders of varying expiry types**\n\nMarket orders consisting of different expiry types (or expiry dates) can be grouped. If one leg expires, it is automatically removed from the order group even though the remaining legs might expire in the future. Subsequently, the grouped order with the remaining legs can be executed.\n\n## <span id=\"page-52-2\"></span>**5.3 Grouping of orders of varying bank baskets**\n\nMarket orders with different pre-selected liquidity providers can only be grouped if they have at least one common bank.\n\n![](_page_53_Picture_0.jpeg)\n\n# <span id=\"page-53-0\"></span>**6 Uploading Orders**\n\nThe Bridge Order Book is a tool which facilitates the upload of orders. This section describes the different upload mechanisms and the supported file formats.\n\nPlease note that it is possible to configure automatic placement of orders uploaded via an API. Please contact Client Advisory Services or your Account Manager for further information.\n\nThis functionality does not support Slice Orders.\n\n## <span id=\"page-53-1\"></span>**6.1 CSV file upload**\n\nThe csv file upload is possible either by uploading a csv file or by using the \"copy to clipboard\" functionality in the applet.\n\n### <span id=\"page-53-2\"></span>**6.1.1 Import From Clipboard**\n\nIn order to use this method of import, click on the \"Download Excel Sheet\" button found in the left vertical panel of the Bridge Order Upload tool. An order excel sheet is downloaded to the chosen path. This sheet helps simplify the definition of orders by pre-defining the set of required fields for the selected products. [Table 4](#page-57-0) provides an overview of the possible fields and their respective meaning.\n\n![](_page_54_Picture_0.jpeg)\n\n| Fields            | Definition                                                                                                                                     | Rules                                                                                            | Format  | Possible Values                                                                                                                              |\n|-------------------|------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|---------|----------------------------------------------------------------------------------------------------------------------------------------------|\n| Product           | 360T Product names which are supported for<br>Orders                                                                                           | Mandatory for all products.                                                                      | String  | Spot, Forward, NDF, Swap, NDS,<br>LoanDeposti                                                                                                |\n| External Id       | Unique Id of the order in an external system                                                                                                   | Not mandatory.                                                                                   | String  | All characters allowed except \"=\"<br>(max. 255)                                                                                              |\n| Group Id          | Unique external group id to create a block<br>order consisting of several legs of varying legal<br>entities, but the same outright instrument. | Not mandatory.                                                                                   | String  | All characters allowed except \"=\"<br>(max. 255)                                                                                              |\n| OCO Leg           | Define if the order is part of an OCO order                                                                                                    | Not mandatory.                                                                                   | Boolean | TRUE, FALSE                                                                                                                                  |\n| Editable          | Decides if the order can be amended (TRUE)<br>or not (FALSE)                                                                                   | Not Mandatory -<br>If no value<br>is chosen, by Default, the<br>Trade Intention is<br>Amendable. | Boolean | TRUE, FALSE                                                                                                                                  |\n| Legal Entity      | Name of the legal entity                                                                                                                       | Mandatory for all products.                                                                      | String  | legal entities                                                                                                                               |\n| Action            | Direction of the trade -<br>Buy/Sell is referring to<br>Currency<br>1, Borrow or Deposit for loan/deposit<br>orders                            | Mandatory for all products.                                                                      | String  | Buy', 'Sell' are supported for Spot<br>and Forward. 'Buy/Sell', 'Sell/Buy' -<br>for Swap and NDS, 'Borrow' or<br>'Deposit' for Loan/Deposits |\n| Currency 1        | Base currency of the Order. The currency<br>being bought by the buyer and sold by the<br>seller                                                | Mandatory for all FX<br>products.<br>Must be empty for<br>LoanDeposit                            | String  | ISO currency codes                                                                                                                           |\n| Currency 2        | Quote currency of the Order. The currency<br>being bought by the seller and sold by the<br>buyer                                               | Mandatory for all FX<br>products.<br>Must be empty for<br>LoanDeposit                            | String  | ISO currency codes                                                                                                                           |\n| Notional Currency | The currency of the notional amount.                                                                                                           | Mandatory for all products.<br>Defines which Day Count is<br>taken for LoanDeposit.              | String  | ISO currency codes                                                                                                                           |\n\n![](_page_55_Picture_0.jpeg)\n\n| Notional Amount 1 | Notional amount                                                                                                     | Mandatory.                                                                                                                                                               | Decimal |            |\n|-------------------|---------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|------------|\n| Notional Amount 2 | Notional amount of a far leg for Swap or NDS.<br>Not required for other Products                                    | Mandatory for FX Swaps<br>and NDS.<br>Must not be specified for<br>other products.                                                                                       | Decimal |            |\n| Effective Date    | Value date of an outright order. Near leg's<br>value date of an FX Swap or NDS.<br>Start date of<br>a Loan/Deposit. | Must not be specified for<br>the product Spot.<br>Mandatory for other<br>products.<br>Should be a valid Trading<br>Date.<br>Effective Date should not<br>be in the Past. | Date    | dd.mm.yyyy |\n|                   |                                                                                                                     | For LoanDeposit cannot<br>be<br>after Maturity Date                                                                                                                      |         |            |\n| Maturity Date     | Far leg's value date of an FX Swap or NDS.<br>Maturity date of a LoanDeposit                                        | Must not be specified for<br>Forward, Spot or NDFs.<br>Mandatory for Swaps and<br>NDS.<br>Should be a valid Value<br>Date.<br>Maturity Date > Effective<br>Date.         | Date    | dd.mm.yyyy |\n| Limit Rate        | Limit rate for a Limit Orders.                                                                                      | Not Mandatory.                                                                                                                                                           | Decimal |            |\n|                   | If the Limit Rate is specified, the uploaded<br>order creates a Limit Order.                                        | Field can only be populated<br>for outright orders.                                                                                                                      |         |            |\n| Stop Rate         | Stop rate for Stop Orders.                                                                                          | Not Mandatory.                                                                                                                                                           | Decimal |            |\n|                   |                                                                                                                     | Field can only be populated<br>for outright orders.                                                                                                                      |         |            |\n\n![](_page_56_Picture_0.jpeg)\n\n| Two-way pricing  | Defines if a standard or 2-way RFS request<br>should be send                                                                               | Not Mandatory.                                                                              | Boolean | TRUE/FALSE                                                                                                                                    |\n|------------------|--------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|---------|-----------------------------------------------------------------------------------------------------------------------------------------------|\n| Fixing           | Fixing reference for Fixing Orders<br>and for NDF<br>market orders.                                                                        | Not Mandatory.                                                                              | String  | List available on demand.<br>For<br>NDF<br>orders, if no fixing reference is<br>provided,<br>the order will default to<br>\"Pre-agreed fixing\" |\n| Fixing Date 1    | Fixing Date for NDF and near Fixing Date for<br>NDS.                                                                                       | Mandatory -<br>for Products<br>NDF and NDS.<br>Must not be specified for<br>other Products. | Date    | dd.mm.yyyy                                                                                                                                    |\n| Fixing Date 2    | Fixing Date of far leg of NDS.                                                                                                             | Mandatory -<br>for Product<br>NDS.<br>Must not be specified for<br>other Products.          | String  | dd.mm.yyyy                                                                                                                                    |\n| Expiry Type      | Type of expiry of the order.                                                                                                               | Not Mandatory.                                                                              | String  | GTC -<br>Good-til-canceled                                                                                                                    |\n|                  |                                                                                                                                            | If no value is chosen, the<br>value defaults to \"GTC\".                                      |         | GTD –<br>Good-til-Day                                                                                                                         |\n| Expiry Date      | Date of expiry of the order in case of a GTD<br>order                                                                                      | Mandatory only if \"Expiry<br>Type\" equals to \"GTD\".                                         | Date    | dd.mm.yyyy hh:mm:ss                                                                                                                           |\n| Bank Basket      | Allows the definition of preferred providers. If<br>more than provider should be uploaded, they<br>have to be separated with character \" \" | Not Mandatory.                                                                              | String  | If needed, a list of current<br>available values can be requested<br>from 360T CAS                                                            |\n| MTF              | Determines whether the order is expected to                                                                                                | Not Mandatory.                                                                              | Boolean | TRUE, FALSE                                                                                                                                   |\n|                  | be negotiated on 360T MTF or off-Venue when<br>sent out as a RFS request.                                                                  | Only relevant in case of a<br>\"market order\".                                               |         |                                                                                                                                               |\n| CTCID            | Complex Trade Component ID                                                                                                                 | Not Mandatory.                                                                              | String  |                                                                                                                                               |\n| Trading Capacity | Trading Capacity reflects the capacity in which<br>the MTF member actually trades                                                          | Not Mandatory.                                                                              | String  | DEAL. MTCH, AOTC, NONE                                                                                                                        |\n\n![](_page_57_Picture_0.jpeg)\n\n|                           |                                                                                                                                                                                    | Only relevant for MTF<br>orders  |        |                                 |\n|---------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------|--------|---------------------------------|\n| IDM                       | Investment decision maker.                                                                                                                                                         | Not Mandatory.                   | String |                                 |\n|                           | This field needs to be populated with a 360T<br>account name identifying the IDM                                                                                                   | Only relevant for MTF<br>orders. |        |                                 |\n| Possible Custom<br>Fields | Customer specific fields. Must be defined case<br>by case.<br>Fields can be mandatory or not, with fixed<br>values (not<br>editable or editable), text, dates,<br>numbers, boolean | Not Mandatory.                   | Any    | \"Portfolio\", \"Description\" etc… |\n\n<span id=\"page-57-0\"></span>**Table 4 Fields for the Order CSV Upload**\n\n![](_page_58_Picture_0.jpeg)\n\nOnce the orders to be uploaded are defined, click on the button within\n\nthe Order Upload Excel sheet. Next, press \"Import from Clipboard\" in order to import the specified orders. Prior to the final import, each entry of the excel sheet is validated and the results are shown in a separate popup window, refer to [Figure 53.](#page-58-1)\n\n|              |                                                      |                              |             |                  |                |                         |                                                 |            |            |            |                                      | v Preferences v Help $ \\Phi \\rangle \\mathbb{Q}^1 \\oplus \\mathbb{A}$ $\\rightarrow$ $\\Box$ X |          |                   |                                                                                                                              |\n|--------------|------------------------------------------------------|------------------------------|-------------|------------------|----------------|-------------------------|-------------------------------------------------|------------|------------|------------|--------------------------------------|--------------------------------------------------------------------------------------------|----------|-------------------|------------------------------------------------------------------------------------------------------------------------------|\n|              | <b>ORDER MANAGEMENT</b>                              | DEAL TRACKING                |             | RFS LIVE PRICING |                | $\\!+\\!$                 |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Initialized                                          |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n| $\\mathbf{F}$ | Type<br>Market Ord                                   |                              |             |                  |                |                         | <b>Upload Orders</b>                            |            |            |            |                                      |                                                                                            | $\\times$ | <b>BUY USD</b>    | $\\checkmark$<br>$\\begin{array}{c} \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ } \\text{ }$ |\n| $\\mathbf{f}$ | Market Ord                                           | Strategy                     | Product     | External Id      | Is OCO Leg     | Editable                | <b>Legal Entity</b>                             | Action     | Currency 1 | Currency 2 | Notional Ccy                         | Notional Amount 1                                                                          |          | 00 EUR<br>$240 -$ | $\\boxed{2}$                                                                                                                  |\n| $\\uparrow$   | Market Ord<br><b>Market Ord</b>                      | Market Order<br>$\\checkmark$ | <b>SPOT</b> | E3               | $\\circledcirc$ | $\\circledcirc$          | GroupE                                          | <b>BUY</b> | <b>EUR</b> | <b>USD</b> | <b>EUR</b>                           | 1230.00                                                                                    |          |                   |                                                                                                                              |\n| $\\downarrow$ | Market Ord                                           | Market Order SPOT            |             |                  | (00)           | $\\circ$                 | GroupE                                          | <b>BUY</b> | <b>EUR</b> | <b>USD</b> | <b>EUR</b>                           | 1230.00                                                                                    |          |                   |                                                                                                                              |\n|              | Market Ord                                           | $\\times$<br>Market Order     | <b>SPOT</b> |                  | $\\circledcirc$ | $\\overline{\\mathbf{C}}$ | GroupE.T                                        | <b>BUY</b> | <b>EUR</b> | <b>USD</b> | <b>EUR</b>                           | 1230.00                                                                                    |          |                   |                                                                                                                              |\n|              | Market Ord                                           |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market Ord                                           |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | <b>Market Ord</b>                                    |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market Ord<br>Market Orde                            |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market Ord                                           |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market Ord                                           |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market Ord                                           |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | <b>Limit Order</b>                                   |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | __                                                   |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Accepted                                             |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Type                                                 |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Limit Or                                             |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market <sup>1</sup>                                  |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market (<br>Market                                   |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!             |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | <b>Market</b>                                        |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market (                                             |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market                                               |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | Market                                               |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n|              | <b>Market</b>                                        |                              |             |                  |                |                         |                                                 |            | Cancel     |            | <b>Import excluding Warnings (1)</b> | Import (2)                                                                                 |          |                   |                                                                                                                              |\n|              | Market (<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | EMSO-13121.                  |             | Accepted         |                | GroupE                  |                                                 |            |            |            |                                      | $\\Box$                                                                                     |          |                   |                                                                                                                              |\n|              | <b>Limit Order</b>                                   | EMSO-13120                   | Active      | Accepted         |                | GroupE                  | Buy                                             | AUD/USD    |            | FX Spot    |                                      | 4,006.00 AUD AUD $\\Box$ $\\Box$                                                             |          |                   |                                                                                                                              |\n|              | $\\vee$ OCO Order                                     | EMSO-13027                   |             | Pending Can      |                | GroupE                  |                                                 |            |            |            |                                      | $\\Box$                                                                                     |          |                   |                                                                                                                              |\n|              | C GroupE.TreasurerM, GroupE // QA2                   |                              |             |                  |                |                         | $\\Xi$ $\\Xi$ $\\Xi$ $\\Xi$ $\\Xi$ $\\Xi$ $\\Xi$ $\\Xi$ |            |            |            |                                      | Mi, 07. Nov 2018, 16:21:54 GMT // Connected [FFM] ● // Mem: 66.0% of 483 MB GC:8.0%        |          |                   |                                                                                                                              |\n|              |                                                      |                              |             |                  |                |                         |                                                 |            |            |            |                                      |                                                                                            |          |                   |                                                                                                                              |\n\n#### <span id=\"page-58-1\"></span>**Figure 53 Validation of orders**\n\nThere is a differentiation between the following validation result statuses:\n\n- No inconsistency detected : Entry can be imported. No erroneous values detected\n- Warning : Entry can be imported, but the yellow marked values should be reviewed\n- Error : Entry cannot be imported due to erroneous values\n\nAs an alternative option to the within the Order Upload Excel Sheet, the defined orders can also be saved via the button as a csv file which can be then imported into the Order Book tool. Please refer to sub-chapter [6.1.2](#page-58-0) for the csv import.\n\n## <span id=\"page-58-0\"></span>**6.1.2 Import from file**\n\nTo import from file, the delimiter utilized by your operational system (e.g. Windows) must be consistent with the Order Book delimiter. To ensure consistency, see [Table 5.](#page-59-3)\n\n![](_page_59_Picture_0.jpeg)\n\n| Operational system's delimiter | Configuration option for Order Book Tool                                                                              |\n|--------------------------------|-----------------------------------------------------------------------------------------------------------------------|\n| Comma (\",\")                    | Option under the menu Preferences -> Shared<br>Settings -> \"Use comma as decimal separator\" has<br>to be un-selected. |\n| Semicolon (\";\")                | Preferences -> Shared Settings -> \"Use comma as<br>decimal separator\" has to be selected                              |\n\n#### <span id=\"page-59-3\"></span>**Table 5: Matching delimiter**\n\nThe description of all fields can be seen in [Table 4.](#page-57-0) If a value is not used for the corresponding product type, it can be left empty. The csv-file **must** contain a header. There are two possible formats:\n\n1. Full header, generated from 360T Order Upload Excel template file:\n\n#### <span id=\"page-59-1\"></span>**Figure 54: CSV-file with full header**\n\n#### 2. Short header, only column names:\n\n#### <span id=\"page-59-2\"></span>**Figure 55 Short header for csv-file**\n\n#### <span id=\"page-59-0\"></span>**6.1.3 CSV file versioning**\n\nAs the Order Book tool progresses in its development, the CSV-file format will likely change in the future. To be able to slowly adapt to changes from 360T, a CSV-file versioning is in place. This feature allows the upload of older CSV-file formats. During the upload process the version of the CSV file is checked and the entries are validated against the version of the csv file. Therefore, it is not required to immediately adapt to the changes done by 360T.\n\nThe versioning depends on the header of the csv file. Therefore, the header is required in order to validate the document format.\n\n![](_page_60_Picture_0.jpeg)\n\nProduct;External Id;Editable;Legal Entity;Action;Currency 1;Currency 2;Notional Currency;Notional Amount 1;Notional Amount 2;Effective Date;Maturity Date;Limit Rate;Stop Rate;Fixing;Fixing Date 1;Fixing Date 2;Expiry Type;Expiry Date;Bank Basket;MTF;CTCID;Trading Capacity;IDM;TradingRegulation;Drop Down;Amount;Type Of Trade;Dept;Title;NumberOfTrades;Approved;Prop;Hedged;LiquidPool;ORCA;Chameleon;Sales Desk;BA?;InTheMoney?;SalesDesk;IQ;Bridge?;Offshore?;Reason Spot;;;GroupE;Buy;EUR;USD;EUR;1230,00;;;;;;;;;;;;FALSE;;;;;FALSE;0,00;---\n\n;QA;;0,00;FALSE;FALSE;FALSE;FALSE;FALSE;FALSE;FALSE;FALSE;FALSE;;0,00;FALSE;FALSE;\n\n#### <span id=\"page-60-1\"></span>**Figure 56 Exemplary CSV file**\n\n#### <span id=\"page-60-0\"></span>**6.1.4 Uploading an abbreviated csv file**\n\nAn abbreviated version of the CSV-file can be generated which restricts the upload from containing all available products. The Order Book tool uses the header of the csv file to identify the correct sequence of order details.\n\nFor example, if a company is not trading NDS all fields related to this product can be suppressed. For a successful upload, the respective headers must be removed from the file.\n\nThe following table lists all fields and the conditions under which the fields can be removed from the standard csv file.\n\n| Field name       | Condition for removal                           |  |\n|------------------|-------------------------------------------------|--|\n| External ID      | if no external reference ID is required         |  |\n| Group ID         | if no block / netted requests are required      |  |\n| OCO Leg          | if OCO orders are not required                  |  |\n| Editable         | if Orders<br>are always non-editable            |  |\n| Limit rate       | if no limit orders are required                 |  |\n| Stop rate        | if no stop orders are required                  |  |\n| Fixing           | if no fixing orders are required                |  |\n| Fixing Date 1    | if no NDF / NDS are required                    |  |\n| Fixing Date 2    | if no NDS are required                          |  |\n| Expiry Type      | if the Expiry Type should default to GTC        |  |\n| Expiry Date      | If the Expiry Type is GTC                       |  |\n| Bank Basket      | if no preferred providers are to be<br>uploaded |  |\n| CTCID            | if this field is not required                   |  |\n| Trading Capacity | if orders are not negotiated on the MTF         |  |\n| IDM              | if orders are not negotiated on the MTF         |  |\n\n![](_page_61_Picture_0.jpeg)\n\n### <span id=\"page-61-0\"></span>**6.1.5 Bank Basket upload**\n\nThe Order Book feature supports the upload of a pre-defined bank basket (group of providers). It is possible to either upload one specific provider or a list of providers per order. If a list of providers is uploaded they must be separated with the \"|\" character in the csv file or comma in the XML upload. The excel template offers a list of all providers for which the relationship with the legal entity is configured. At the moment, it does not take into account if a specific bank basket configuration per product is in place. The provider upload for XML API and FIX API is also supported.\n\nExample:\n\nA user uploads an order which includes the pre-defined bank basket \"360TBANK|BNPP.DEMO\".\n\nWhen the user selects the order and opens the RFS \"Product Definition\" window within the Order Book tool, the providers \"360TBANK\" and \"BNPP.DEMO\" are selected under the tab \"Provider List\", as shown in [Figure 57.](#page-61-1)\n\nIf, instead, the \"Place Order\" window is opened then the providers \"360TBANK\" and \"BNPP.DEMO\" are shown in the tab \"Provider List\" and one provider must be selected before the order can be placed, refer to [Figure 58.](#page-62-1)\n\n|                                | • Product Definition                                  | <b>Competitive Bidding</b>               | - ×           |\n|--------------------------------|-------------------------------------------------------|------------------------------------------|---------------|\n|                                | <b>FX Spot</b>                                        |                                          |               |\n|                                |                                                       |                                          |               |\n|                                | Order ID #EMSO-1464230                                |                                          |               |\n|                                | <b>BUV</b><br>$EUR \\vee$                              | 1.000.000                                |               |\n|                                | $\\rightleftharpoons$<br>USD $\\vee$<br>$\\circ$<br>Sell |                                          |               |\n|                                |                                                       |                                          |               |\n|                                | <b>Effective Date</b>                                 |                                          |               |\n|                                | Tue, 13.11.2018<br>Spot                               | 雦                                        |               |\n|                                |                                                       |                                          |               |\n| Provider List<br>360TBANK.TEST | Transaction<br>Comments<br>SI BNPP.DEMO               | <b>BOALDEMO</b>                          | $\\frac{1}{2}$ |\n| BankB                          | Barclays BARX.DEMO                                    | CITIBANK.DEMO                            |               |\n| COBA.DEMO                      | COBA.FRA DRESDNER.D                                   | Credit Suisse.DEMO                       | હ             |\n| DB.DEMO                        | HSBC                                                  | <b>HSBC.DEMO</b>                         | ☺             |\n| JPMORGAN.DEMO                  | LLOYDS.DEMO                                           | O MTFBankA                               | ☆             |\n| OTCBankA                       | PEBANKEMEA1.TEST                                      | RBS.LND.DEMO                             | Ï             |\n| SEB.DEMO                       | Scotia Capital.TEST                                   | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |               |\n\n<span id=\"page-61-1\"></span>**Figure 57 Pre-selection of Providers in the RFS Production Definition window**\n\n|                      | <b>Place Order</b><br><b>Market Order</b> | – ×                  |\n|----------------------|-------------------------------------------|----------------------|\n| <b>Order Details</b> | Order Changes                             |                      |\n| Product              |                                           | <b>FX Spot</b>       |\n| Action               |                                           | I Buy EUR / Sell USD |\n| Notional             |                                           | 1,000,000.00 EUR     |\n| Market Rate          |                                           | 1.14216              |\n| Expiry               |                                           | <b>GTC</b>           |\n| Order ID             |                                           | EMSO-1464230         |\n| Provider List        | Comments                                  |                      |\n| 360TBANK.TEST        | BNPP.DEMO                                 |                      |\n|                      |                                           |                      |\n|                      |                                           |                      |\n|                      |                                           |                      |\n\n<span id=\"page-62-1\"></span>**Figure 58 Pre-selection of Providers in the Order Production Definition window**\n\n#### <span id=\"page-62-0\"></span>**6.1.6 Upload of a grouped order**\n\nThe same \"Group ID\" value must be utilized in the upload file for orders consisting of the same outright instrument with varying legal entities which are intended to be grouped to an aggregated order (for trade-on-behalf users only). Similarly, a standard or trade-as user can upload single-entity, multi-maturity block spot and forward trades via uploading multiple orders with the same \"Group ID\".\n\n```\nProduct;External Id;Group Id;OCO Leg;Editable;Legal Entity;Action;Currency 1;Currency \n2;Notional Currency;Notional Amount 1;Notional Amount 2;Effective Date;Maturity Date;Limit \nRate;Stop Rate;Fixing;Fixing Date 1;Fixing Date 2;Expiry Type;Expiry Date;Bank \nBasket;MTF;CTCID;Trading Capacity;IDM;\nSpot;E1511;Group1533;FALSE;TRUE;FundE.1;Buy;EUR;USD;EUR;1,00;;;;;;;;;;;360TBANK.TEST;FALSE;\n;;;\nSpot;E1512;Group1533;FALSE;TRUE;FundE.2;Buy;EUR;USD;EUR;2,00;;;;;;;;;;;360TBANK.TEST;FALSE;\n;;;\n```\n\n#### <span id=\"page-62-2\"></span>**Figure 59 Matching Group ID**\n\nUploading two orders results in an aggregated order with 2 legs, as shown in [Figure 60.](#page-63-3)\n\n![](_page_63_Picture_0.jpeg)\n\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                       |                             |                     |                      |                     |              |                    | $\\vee$ Preference |  |  |  |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------|-----------------------------|---------------------|----------------------|---------------------|--------------|--------------------|-------------------|--|--|--|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | <b>RFS LIVE PRICING</b>               | <b>ORDER MANAGEMENT</b>     |                     | <b>DEAL TRACKING</b> | 47                  |              |                    |                   |  |  |  |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                       |                             |                     |                      |                     |              |                    |                   |  |  |  |\n| e                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |                                       |                             |                     | <b>Trade As</b>      | FundE.2             |              |                    | $\\checkmark$      |  |  |  |\n| $\\frac{1}{2} \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac{1}{2} \\right) \\left( \\frac$ | Q<br>土<br>Initialized (1)<br>Sent (0) |                             |                     |                      |                     |              |                    |                   |  |  |  |\n| ⊜                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | <b>Type</b>                           | Reference #<br>$\\checkmark$ | <b>Order Status</b> | <b>Status</b>        | <b>Legal Entity</b> | Requester Ac | <b>External Id</b> | <b>Currencies</b> |  |  |  |\n| $\\wedge$<br>ٺ                                                                                                                                                                                                                                                                                                                                                                                                                                                              | $\\wedge$ Group Order                  | EMSO-14647                  |                     | Initialized          | GroupE              | <b>Buy</b>   |                    | EUR/USD           |  |  |  |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | <b>Market Order</b>                   | EMSO-14647                  | Initialized         | Initialized          | FundE.2             | <b>BUV</b>   | E1512              | EUR/USD           |  |  |  |\n| と                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | <b>Market Order</b>                   | EMSO-14647                  | Initialized         | Initialized          | FundE.1             | <b>Buy</b>   | E1511              | EUR/USD           |  |  |  |\n\n<span id=\"page-63-3\"></span>**Figure 60 Aggregated orders**\n\nThe bank basket field of both orders must match in the Order Upload file. Otherwise the upload will fail.\n\n## <span id=\"page-63-0\"></span>**6.1.7 Relationship change between requester and provider**\n\nDuring the creation and the submission of an order, the relationship with the provider might change. For example, if a provider was removed or blocked in the 360T CRM tool, the Order Book tool does not show the provider in the \"Provider List\" tab of the \"Product Definition\" even though the provider might have been pre-selected during the order creation.\n\n## <span id=\"page-63-1\"></span>**6.2 XML Upload**\n\nThe Order Book tool supports the upload of orders via an XML-based web interface. After the successful upload of an order, its status can be requested via the interface using the provided external ID. For more information on the XML upload interface, please refer to the document \"360T Order XML API\".\n\n## <span id=\"page-63-2\"></span>**6.3 FIX Upload**\n\nA \"Financial Information eXchange\" (FIX) protocol can be utilized to upload orders into the Order Book tool. Please refer to the document \"360T Order Market Taker FIX API\" for more information.\n\n![](_page_64_Picture_0.jpeg)\n\n# <span id=\"page-64-0\"></span>**7 Order History**\n\nThe summary of all order status changes is available either in the Order View window of the Order Book (see chapter [4.1.1\\)](#page-14-0) or in the Order History tab which is available in the order ticket after finalization of an order (via the Deal Tracking feature).\n\n| <b>Deal Confirmation</b> |                        |                                                   |  |                   |                  |  |  |     |\n|--------------------------|------------------------|---------------------------------------------------|--|-------------------|------------------|--|--|-----|\n|                          |                        | <b>Competitive Quotes</b><br><b>Order History</b> |  |                   |                  |  |  | マーチ |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          | <b>Order Changes</b>   |                                                   |  |                   |                  |  |  |     |\n| <b>State</b>             | Date                   |                                                   |  | <b>User</b>       | Company          |  |  |     |\n| Initialized              |                        | Mon. 30. Apr 2018 09:11:50 GMT                    |  | GroupE.TreasurerM | <b>GroupE</b>    |  |  |     |\n| <b>Active</b>            |                        | Mon, 30. Apr 2018 09:12:02 GMT                    |  | GroupE.TreasurerM | <b>GroupE</b>    |  |  |     |\n| Initialized              |                        | Mon, 30. Apr 2018 09:14:25 GMT                    |  | GroupE.TreasurerM | <b>GroupE</b>    |  |  |     |\n| <b>Active</b>            |                        | Mon, 30. Apr 2018 09:37:02 GMT                    |  | GroupE.TreasurerM | GroupE           |  |  |     |\n| Executed                 |                        | Mon, 30. Apr 2018 09:37:16 GMT                    |  | TB-HSBC.DEMO.Trad | HSBC.DEMO        |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          | <b>Request Changes</b> |                                                   |  |                   |                  |  |  |     |\n| <b>State</b>             | <b>Date</b>            |                                                   |  | <b>User</b>       | Company          |  |  |     |\n| Initialized              |                        | Mon, 30. Apr 2018 09:11:50 GMT                    |  | GroupE.TreasurerM | GroupE           |  |  |     |\n| Sent                     |                        | Mon, 30. Apr 2018 09:12:02 GMT                    |  | GroupE.TreasurerM | GroupE           |  |  |     |\n| <b>Delivered</b>         |                        | Mon, 30. Apr 2018 09:12:02 GMT                    |  | TB-HSBC.DEMO.Trad | <b>HSBC.DEMO</b> |  |  |     |\n| Pending R                |                        | Mon, 30. Apr 2018 09:14:25 GMT                    |  | TB-HSBC.DEMO.Trad | <b>HSBC.DEMO</b> |  |  |     |\n| Initialized              |                        | Mon, 30. Apr 2018 09:15:25 GMT                    |  | GroupE.TreasurerM | <b>GroupE</b>    |  |  |     |\n| Sent                     |                        | Mon, 30. Apr 2018 09:37:02 GMT                    |  | GroupE.TreasurerM | GroupE           |  |  |     |\n| <b>Delivered</b>         |                        | Mon. 30. Apr 2018 09:37:02 GMT                    |  | TB-HSBC.DEMO.Trad | <b>HSBC.DEMO</b> |  |  |     |\n| Accepted                 |                        | Mon. 30. Apr 2018 09:37:12 GMT                    |  | TB-HSBC.DEMO.Trad | <b>HSBC.DEMO</b> |  |  |     |\n| Done                     |                        | Mon, 30. Apr 2018 09:37:16 GMT                    |  | GroupE.TreasurerM | <b>GroupE</b>    |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n|                          |                        |                                                   |  |                   |                  |  |  |     |\n\n<span id=\"page-64-1\"></span>**Figure 61 Deal Confirmation: Order History**\n\n**Order Status Changes**: Status of an order or a single order leg.\n\n- Initialized: The order has been prepared and possibly sent, but is **not yet accepted** by the market maker\n- Active: The order is accepted and valid\n- Triggered: The order is triggered\n- Executed: The order was executed\n- Closed: The order is expired or was cancelled\n\n**Request Status Changes**: Negotiation status of the single- or multi-legged order.\n\nAn order can have one of the following states:\n\n- Initialized: The order definition is partly done or done and the order is saved as a draft, but has not yet been sent to the market maker\n- Sent: The order has been placed, but not yet received by the market maker\n\n![](_page_65_Picture_0.jpeg)\n\n- Delivered: The order has been received by the market maker\n- Accepted: The order has been accepted by the market maker\n- Pending cancellation: The order was withdrawn by the requester, but the cancellation has not yet been acknowledged by the provider\n- Pending rejection: The order was rejected by the provider but the rejection has not yet been acknowledged by the requester\n- Rejected: The order has been rejected by the market maker.\n- Done: The order was executed and acknowledged\n\nHistory of this specific leg of the\n\nHistory of the negotiation of the\n\n<span id=\"page-65-0\"></span>**Figure 62 Order/Request Changes in Order View Window.**\n\n![](_page_66_Picture_0.jpeg)\n\n# <span id=\"page-66-0\"></span>**8 Special User Configuration: Four-Eyes Principle**\n\nBridge Order Book supports the option to enforce a four-eyes principle allowing the separation of order creation and execution between two different users. If this setting is activated, a user cannot execute orders he created or amended himself. Thus, it is ensured that two users, i.e. four eyes, are involved in the workflow of an order. By default, this setting is deactivated and can be activated by 360T CAS team upon request.\n\nA user with activated four-eyes principle setting has the following permissions:\n\n- Create a new order, amend or delete it\n- Amend or delete an order created by other user\n- Place an order created or previously amended by another user\n- Send market order created or previously amended by another user as RFS\n- Withdraw, cancel or confirm pending rejection of an order which was previously placed by this user\n\nPlease note that the Bridge RFS Live Pricing feature is not active if a user has the four-eyes principle enabled, however the feature itself should not be removed from the GUI as otherwise sending orders as RFS is not possible.\n\nIn order to keep a better overview of own orders and orders created by other users it is recommendable to create and save customized search tabs with orders initialized by different users:\n\n|                                            |     |                                 |                  |                                              |                           | Trade As GroupH                       |                               |                        |                                  | $\\checkmark$                |                 |                          |                    |                         |                                             |                       |\n|--------------------------------------------|-----|---------------------------------|------------------|----------------------------------------------|---------------------------|---------------------------------------|-------------------------------|------------------------|----------------------------------|-----------------------------|-----------------|--------------------------|--------------------|-------------------------|---------------------------------------------|-----------------------|\n| Initialized (26)                           |     | My initialized orders (2)       |                  | Ready to place (24)                          | lQ.<br>Sent (5)           | 玉                                     |                               |                        |                                  |                             |                 |                          |                    |                         |                                             |                       |\n|                                            |     |                                 |                  |                                              | Qv                        |                                       |                               |                        |                                  | <b>Apply</b><br><b>Save</b> |                 |                          |                    |                         |                                             |                       |\n| Initialized                                | 8.8 |                                 |                  | Requester Individual not in GroupH.HybridA X |                           |                                       |                               |                        |                                  |                             |                 |                          |                    |                         |                                             |                       |\n| <b>Type</b>                                |     | Reference #                     |                  | $\\vee$ Order Status                          | <b>Status</b>             | <b>Legal Entity</b>                   | Requester A Currencies        |                        | Product                          | Notional A                  | Notional Cu     | Rate                     | <b>Expiry Type</b> | <b>Expiry Dat</b>       |                                             |                       |\n| $\\vee$ Group Order                         |     | • EMSO-2599837                  |                  |                                              | Initialized               |                                       | Buy                           | EUR/USD                | <b>FX Spot</b>                   | 800.00 EUR EUR              |                 |                          | <b>GTC</b>         | $Q \\nless$              | $\\triangleright \\rightarrow$                | Û                     |\n| <b>Market Order</b>                        | ٠   | EMSO-2512927                    |                  | Initialized                                  | Initialized               | SubsidiaryH                           | Buy                           | EUR/USD                | <b>FX Spot</b>                   | 5,894.00 EUR EUR            |                 |                          | GTC                | C.<br>$\\bar{z}$         | $\\rightarrow$<br>$\\triangleright$           | 血                     |\n| <b>Market Order</b>                        |     | • EMSO-2505356                  |                  | Initialized                                  | Initialized               | GroupO                                | Buy                           | EUR/USD                | <b>FX Spot</b>                   | 1,000,000.0                 | <b>EUR</b>      |                          | GTC                | $\\Box$<br>$\\frac{1}{2}$ | $\\triangleright \\rightarrow$                | Û                     |\n| $\\vee$ OCO Order                           |     | <b>•</b> EMSO-2504109           |                  |                                              | Initialized               | GroupH                                |                               |                        |                                  |                             |                 |                          | GTC                | O                       | $\\rightarrow$<br>$\\triangleright$           | 甫                     |\n| $\\vee$ OCO Order                           |     | ● EMSO-2504093                  |                  |                                              | Initialized               | GroupH                                |                               |                        |                                  |                             |                 |                          | GTC                | $\\Box$                  | $\\mathbb{Z}$ > $\\rightarrow$ +              | 面                     |\n| <b>Limit Order</b>                         |     | ● EMSO-2504082/2                |                  | Initialized                                  | Initialized               | GroupH                                | Buy                           | EUR/USD                | <b>FX Spot</b>                   | 105.060.00.                 | <b>EUR</b>      | 1.10923                  | <b>GTC</b>         |                         | (□ シ D → L                                  | û                     |\n| $\\vee$ Group Order                         |     | <b>• EMSO-2504063</b>           |                  |                                              | Initialized               |                                       | Buy                           | EUR/USD                | <b>FX Spot</b>                   | 1.00 EUR EUR                |                 |                          | GTC                | c                       | $\\mathbb{Z}$ $\\triangleright$ $\\rightarrow$ | 面                     |\n| Market Order                               | ٠   | EMSO-2504061                    |                  | Initialized                                  | Initialized               | GroupH                                | Buy                           | EUR/USD                | <b>FX Spot</b>                   | 1,000.00 EUR EUR            |                 |                          | GTC                | o<br>$\\overline{z}$     | $D \\rightarrow \\mathbb{R}$                  |                       |\n| <b>Market Order</b>                        |     | EMSO-2496627/1                  |                  | Initialized                                  | Initialized               | GroupH                                | Buy                           | EUR/USD                | <b>FX Spot</b>                   | 315.00 EUR EUR              |                 |                          | <b>GTC</b>         |                         | (ロジト)前                                      |                       |\n|                                            |     |                                 |                  |                                              |                           |                                       |                               |                        |                                  |                             |                 |                          |                    |                         | a 2 2 3 <del>8</del> 0                      |                       |\n| <b>Admitted Posts</b>                      |     | <b>FLIED SABEROE</b>            |                  | <b>Contact Formal</b>                        | <b>Contact of Contact</b> | 2000000000000000000000000000000000000 | Photo                         | <b>PUBLICA</b>         | <b>PM PASA</b>                   | <b>PERSONAL</b>             | <b>Contract</b> |                          | $\\overline{a}$     |                         |                                             |                       |\n|                                            |     |                                 |                  |                                              |                           |                                       |                               |                        |                                  |                             |                 |                          |                    |                         |                                             |                       |\n| Accepted (37)                              | Q   | 吏                               |                  |                                              |                           |                                       |                               |                        |                                  |                             |                 |                          |                    |                         |                                             |                       |\n| Type                                       |     | Reference $# \\vee$ Order Status |                  | <b>Status</b>                                | <b>Legal Entity</b>       |                                       | <b>Requester A Currencies</b> | Product                | Notional A                       | Notional Cu                 | Rate            | <b>Expiry Type</b>       | <b>Expiry Date</b> | % to market             | Marl                                        |                       |\n| <b>Stop Order</b>                          |     | EMSO-2543                       | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/USD                       | <b>FX Spot</b>         | 800,000.00                       | <b>EUR</b>                  |                 | 1.13965 GTC              |                    | 3.54618                 |                                             |                       |\n| <b>Limit Order</b>                         |     | <b>EMSO-2530</b>                | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/OAR                       | <b>FX Spot</b>         | 112,200.00                       | <b>FUR</b>                  |                 | 1.10000 GTC              |                    | 72.56002                | o                                           |                       |\n| <b>Market Order</b>                        |     | EMSO-2505                       | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/USD                       | <b>FX Spot</b>         | 4,000.00 EUR EUR                 |                             |                 | <b>GTC</b>               |                    |                         | c                                           |                       |\n| <b>Market Order</b>                        |     | EMSO-2504.                      | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/USD                       | <b>FX Spot</b>         | 10,000.00 E.                     | <b>EUR</b>                  |                 | GTC                      |                    |                         | c                                           |                       |\n| <b>Limit Order</b>                         |     | EMSO-2496                       | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/USD                       | <b>FX Spot</b>         | 100,000.00                       | <b>EUR</b>                  |                 | 1.11598 GTC              |                    | $-1.39558$              | ι⊡                                          |                       |\n| <b>Market Order</b>                        |     | EMSO-2496.                      | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/USD                       | <b>FX Spot</b>         | 777,777.00                       | <b>EUR</b>                  |                 | GTC                      |                    |                         | o                                           | $\\Box$                |\n| $\\vee$ If-Done Order                       |     | EMSO-2482.                      |                  | Accepted                                     | GroupH                    |                                       |                               |                        |                                  |                             |                 | <b>GTC</b>               |                    |                         |                                             |                       |\n| $\\vee$ If-Done Order                       |     | EMSO-2468                       |                  | Accepted                                     | GroupH                    |                                       |                               |                        |                                  |                             |                 | <b>GTC</b>               |                    |                         | o                                           |                       |\n| <b>Market Order</b>                        |     | EMSO-2073                       | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/USD                       | <b>FX Spot</b>         | 6.002.00 EUR EUR                 |                             |                 | <b>GTC</b>               |                    |                         | c                                           |                       |\n| <b>Market Order</b>                        |     | EMSO-1914                       | Active           | Accepted                                     | GroupH                    | Sell                                  | EUR/USD                       | FX Fo., MIT            | 10,000,000                       | <b>EUR</b>                  |                 | GTC                      |                    |                         | o                                           |                       |\n| <b>Market Order</b>                        |     | EMSO-1914                       | Active           | Accepted                                     | GroupH                    | Buy                                   | EUR/USD                       | FX Fo [MIF]            | 10.000.000.                      | <b>EUR</b>                  |                 | <b>GTC</b>               |                    |                         | $\\Box$                                      |                       |\n| <b>Market Order</b><br><b>Market Order</b> |     | EMSO-1914<br>EMSO-1914          | Active<br>Artive | Accepted<br>Accepted                         | GroupH<br>GroupH          | Buy<br>Sell                           | EUR/USD<br>EUR/USD            | FX Fo MIF<br>FX Fo MIF | 100.00 EUR EUR<br>100.00 EUR EUR |                             |                 | <b>GTC</b><br><b>GTC</b> |                    |                         | c<br>₽                                      | $\\circ$ $\\circ$<br> ⊗ |\n\n<span id=\"page-66-1\"></span>**Figure 63 Initialized orders grouped by Requester Individual.**\n\n![](_page_67_Picture_0.jpeg)\n\n# <span id=\"page-67-0\"></span>**9 Order Backup file**\n\nIn case of emergency (no access to the Order Book feature for any technical reason) a copy of the Order Book is kept in CSV format in your local user profile folder. The CSV-file can be opened with a text editor or spreadsheet application. The naming convention of the file is \"BRIDGE-order-dump-OM-[user login name].csv.\n\nThe file is located in a folder called \"360T\" located in the home directory of the user. On Windows systems, this directory is usually either:\n\n\"C:\\Documents and Settings\\<username>\\360T\\tex\\tex-prod\\logs\", or\n\n\"C:\\Users\\<USER>\\360T\\tex\\tex-prod\\logs\"\"\n\n![](_page_68_Picture_0.jpeg)\n\n# <span id=\"page-68-0\"></span>**10 Contacting 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\nGermany 360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Asia Pacific South Asia**\n\nSingapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756\n\n#### **Middle East**\n\nUnited Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\n#### USA\n\n360 Trading Networks, Inc 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902\n\nIndia ThreeSixty Trading Networks (India) Pvt Ltd Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437", "metadata": {"lang": "en"}}]