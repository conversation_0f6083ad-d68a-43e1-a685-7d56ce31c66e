[{"id": "1", "text": "# **360T AUTODEALING SUITE (ADS)**\n\n![](_page_0_Picture_1.jpeg)\n\n# **MARKET MAKER COCKPIT (MMC)**\n\n# **TO ENABLE**\n\n# **PRICE MAKING, DISTRIBUTION AND RISK MANAGEMENT**\n\n© 360 TREASURY SYSTEMS AG, 2015 THIS FILE CONTAINS PROPRIETARY AND CONF<PERSON>EN<PERSON><PERSON> INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG\n\n| 1            | INTRODUCTION                                                      | 6        |\n|--------------|-------------------------------------------------------------------|----------|\n| 1.1          | MMC<br>COMPONENTS                                                 | 7        |\n| 1.2          | PRICING MODEL<br>                                                 | 7        |\n| 1.3          | OVERALL DATA FLOW<br>                                             | 7        |\n| 1.4          | SUPPORTED PRODUCTS                                                | 8        |\n|              |                                                                   |          |\n| 2            | MARKET MAKER COCKPIT OVERVIEW<br>                                 | 9        |\n| 2.1          | LOGIN AND LOGOUT                                                  | 9        |\n| 2.2          | PANELS,<br>TABS AND TAB CONTAINER                                 | 10       |\n| 2.3          | MODIFYING TABS AND TAB CONTAINERS<br>                             | 10       |\n| 3            | CONFIGURE PRICING<br>                                             | 12       |\n| 3.1          | INSTRUMENT CONTROL                                                | 12       |\n| 3.2          | ADD AND REMOVE MANAGED INSTRUMENTS<br>                            | 13       |\n| 3.3          | OWNERSHIP OF A MANAGED INSTRUMENT                                 | 14       |\n| 3.4          | START/STOP PRICING OF INSTRUMENTS AND CHANNELS                    | 14       |\n| 3.5          | PRICING AND RISK MANAGEMENT MODE                                  | 16       |\n| 3.6          | TIER SIZE CONFIGURATION<br>                                       | 17       |\n| 3.7          | INSTRUMENT CONFIGURATION                                          | 17       |\n| 3.8          | REFERENCE PRICE FINDING                                           | 18       |\n| 3.9          | ADDITONAL AND FIXED SPREAD                                        | 20       |\n| 3.10         | MANUAL SKEW<br>                                                   | 21       |\n| 3.11         | ROUNDING OUTBOUND QUOTES<br>                                      | 22       |\n| 3.12         | REFERENCE STREAM AND PERCENT TOLERANCE                            | 23       |\n| 3.13         | NON-EXECUTABLE STREAMS                                            | 24       |\n| 3.14         | SWEEPABLE AND FULL AMOUNT STREAMS<br>                             | 25       |\n| 3.15         | MANAGING SYNTHETIC CROSSES<br>                                    | 25       |\n| 3.16<br>3.17 | PRICING OF UNMANAGED INSTRUMENTS<br>CONFIGURING TIME SLIPPAGE<br> | 26<br>27 |\n| 3.18         | CONFIGURING PRICE SLIPPAGE<br>                                    | 27       |\n| 3.19         | QUOTE FILTERING<br>                                               | 29       |\n| 3.20         | PTMM                                                              | 31       |\n|              |                                                                   |          |\n| 4            | MONITOR PRICING                                                   | 32       |\n| 4.1          | THE LIQUIDITY DETAILS PANEL                                       | 32       |\n| 4.2          | INBOUND AND OUTBOUND PRICING TIER MONITOR                         | 32       |\n| 4.3          | PRICING<br>DETAILS DIALOG                                         | 33       |\n| 5            | RISK MANAGEMENT                                                   | 36       |\n| 5.1          | MONITORING POSITIONS<br>                                          | 36       |\n| 5.2          | PROFIT AND LOSS (P/L)<br>CALCULATIONS                             | 37       |\n| 5.3          | POSITION FILTERING CONFIGURATION<br>                              | 37       |\n| 5.4          | RISK MANAGEMENT CONFIGURATION                                     | 38       |\n| 5.5          | POSITION RULES<br>                                                | 40       |\n| 5.6          | PRICING RULES                                                     | 42       |\n| 5.7          | ALERT RULES<br>                                                   | 43       |\n| 5.8          | MANUAL POSITION AMENDMENTS<br>                                    | 44       |\n| 5.9          | RESTRICT THE BANK BASKET FOR HEDGE ORDERS                         | 46       |\n| 5.10         | CLIENT ORDER HANDLING RULES<br>                                   | 46       |\n| 5.11         | PRICING AND RISK MANAGEMENT SCENARIOS<br>                         | 48       |\n| 6            | BLOTTERS<br>                                                      | 50       |\n\n| 8 | CONTACT 360T<br><br>53 |                                             |    |  |  |  |\n|---|------------------------|---------------------------------------------|----|--|--|--|\n| 7 |                        | AUDIT                                       | 52 |  |  |  |\n|   | 6.5                    | CLIENT ACTIVITY BLOTTER<br>                 | 51 |  |  |  |\n|   | 6.4                    | COMBINED CLIENT AND HEDGE ORDER BLOTTER<br> | 51 |  |  |  |\n|   | 6.3                    | HEDGE ORDER BLOTTER                         | 51 |  |  |  |\n|   | 6.2                    | CLIENT ORDER BLOTTER<br>                    | 50 |  |  |  |\n|   | 6.1                    | GENERAL BLOTTER FEATURES<br>                | 50 |  |  |  |\n\n# **TABLE OF FIGURES**\n\n| Figure 1 Market Maker Cockpit Overview9                                   |  |\n|---------------------------------------------------------------------------|--|\n| Figure 2<br>Login and Start the Market Maker Cockpit10                    |  |\n| Figure 3 Exit the Market Maker Cockpit10                                  |  |\n| Figure 4 Instrument Control Tab12                                         |  |\n| Figure 5 Open Global Instrument Configuration13                           |  |\n| Figure 6 Global Instrument Configuration<br>14                            |  |\n| Figure 7: Take over instrument ownership14                                |  |\n| Figure 8 Start/Stop Pricing<br>15                                         |  |\n| Figure 9 Start/Stop Channels<br>15                                        |  |\n| Figure 10 Emergency Stop15                                                |  |\n| Figure 11: Select risk management mode<br>16                              |  |\n| Figure 12 Different Risk Management Assignment to Different Streams<br>16 |  |\n| Figure 13 Define Tier Sizes<br>17                                         |  |\n| Figure 14: Open instrument configuration dialog<br>17                     |  |\n| Figure 15: Instrument Configuration Dialog<br>18                          |  |\n| Figure 16: Reference Price Finding rules editor20                         |  |\n| Figure 17 Defining Outbound Spread<br>20                                  |  |\n| Figure 18: Basic skew settings<br>21                                      |  |\n| Figure 19: Tier specific skew factors21                                   |  |\n| Figure 20<br>Allowing Skew to cross Mid Price/ Opposite Side<br>22        |  |\n| Figure 21: Reducing outbound spot rate precision22                        |  |\n| Figure 22Reference Stream and Percent Tolerance23                         |  |\n| Figure 23 Configure Non-Executable Streams<br>24                          |  |\n| Figure 24<br>Non-executable Stream Selection<br>24                        |  |\n| Figure 25: Cross Currency Pair Configuration<br>25                        |  |\n| Figure 26: Pricing unmanaged instruments<br>26                            |  |\n| Figure 27: Time slippage configuration27                                  |  |\n| Figure 28: Example for price slippage<br>28                               |  |\n| Figure 29: Price slippage configuration29                                 |  |\n| Figure 30: Quote filter settings30                                        |  |\n| Figure 31: Monitor filtered quotes<br>30                                  |  |\n| Figure 32: Liquidity Details Panel<br>32                                  |  |\n| Figure 33: Pricing Tier monitor<br>32                                     |  |\n| Figure 34: Raw Inbound Quote Details34                                    |  |\n| Figure 35: Filtered Inbound Quote Details<br>34                           |  |\n| Figure 36: Outbound Price Details35                                       |  |\n|                                                                           |  |\n\n| Figure 37: Managed Positions blotter with context menu36 |\n|----------------------------------------------------------|\n| Figure 38 Context menu for managing open position<br>37  |\n| Figure 39 Position Filtering38                           |\n| Figure 40: Risk management configuration<br>39           |\n| Figure 41 Position Rules<br>41                           |\n| Figure 42: Risk management pricing rules43               |\n| Figure 43 Alert popup message43                          |\n| Figure 44 Context menu of Managed Positions<br>44        |\n| Figure 45 Taskbar for manual position update44           |\n| Figure 46<br>Amend Position<br>44                        |\n| Figure 47 Set Position<br>44                             |\n| Figure 48 Confirmation of Position Reset<br>45           |\n| Figure 49 Confirmation of Position Flattening<br>45      |\n| Figure 50: Restrict hedge order bank basket<br>46        |\n| Figure 51: Manage Client Order Handling Rules<br>47      |\n| Figure 52: Pricing and Risk Management Scenarios<br>49   |\n| Figure 53: Scenario selection<br>49                      |\n| Figure 54: Client Activity Blotter51                     |\n| Figure 55: Accessing the Audit Log<br>52                 |\n| Figure 56: Search by Action<br>53                        |\n\n# <span id=\"page-5-0\"></span>**1 INTRODUCTION**\n\nThe 360T Auto Dealing Suite (ADS) includes Pricing Engine functionality to define outbound prices, based on rates streamed inbound from your liquidity provider(s). The outbound price can be adjusted to include trader spreads, skewing etc. Being passed downstream, further adjustments to these prices like sales or customer spreads can be made by setting destination rules via Auto Dealing Suite (ADS)\n\nThe Market Maker Cockpit (MMC) allows automated risk management and pre-set actions with subsequent positions created by customer flows.\n\nPosition overviews and profit and loss calculation are provided. Users can set up the currency pairs that will be actively managed as well as define currency pairs for price stripping and/or quote pegged currencies.\n\n*DISCLAIMER:*\n\n*Please note that clients shall be solely responsible for the use of 360T's Market Maker Cockpit (\"MMC\").* \n\n*The MMC is a fully operational pricing engine with automated pricing functionalities. Each client using the MMC should be aware that an automated setup in general might lead to unsought trade results, and any use of the MMC requires a certain level of experience, requisite knowledge and skills, constant monitoring of the market and periodical review of all settings. 360T is not in the position to monitor any such activities or settings of the MMC and will under no circumstances interfere with any client's MMC setup.*\n\n*With respect to the MMC, 360T will be in no event, regardless of cause, liable for any direct, indirect, special, incidental, punitive or consequential damages of any kind, whether arising under breach of contract, tort (including negligence), or otherwise, and whether based on this agreement or otherwise, even if advised of the possibility of such damages. This applies in particular to the settings of the MMC, its activation or deactivation and any trade executed (or not made) through the MMC.*\n\n*Between the parties using MMC the client shall be solely responsible for the performance and enforcement of any and all trades resulting from using the MMC. Furthermore the MMC is provided to the client on an \"as is\" and \"as available\" basis without warranty of any kind (either express or implied).* \n\n# <span id=\"page-6-0\"></span>**1.1 MMC Components**\n\nThe Market Maker Cockpit consists four major components;\n\n- **Overview:** providing lists of defined currency pairs, views for in- and outbound prices, positions and deal blotters.\n- **Pricing Controller:** enabling the creation of core prices for each defined currency pair and adjustments with spreads and/or skewing.\n- **Risk Management**, enabling the definition of risk parameters for each currency pair and management rules once triggers and alerts have been breached.\n- **Reference Price Finding**, enabling the selection of price provider(s) and pricing tiers etc.\n\n# <span id=\"page-6-1\"></span>**1.2 Pricing Model**\n\nThe outbound rate to a client (client rate) for a specific quantity tier is determined through the following stages:\n\n- Trader Rate = Reference Rate + Additional Spread + Skew\n- Client Rate = Trader Rate + Sales Margin\n\nThis product deals primarily with the calculation of the trader rate. Sales margin is a parameter of the 360T Auto Dealing Suite (ADS), and can be applied individually by client.\n\n#### **Reference Rate:**\n\nIf not a fixed rate, the reference rate will be derived via price finding. This is a process which takes into account available market data and liquidity, risk management profile, and risk appetite. The reference rate is always a two-way price of bid and ask price. The difference between these two prices is the inbound spread. The middle of these two prices, is the reference mid-price.\n\n#### **Additional spread:**\n\nSpread is defined as the difference between bid and ask price. The difference between the reference bid and ask rate is the so called inbound spread. This inbound spread is often widened for various reasons.\n\n#### **Skew:**\n\nSkewing means to shift the mid-price (the middle of bid and ask rate) either towards the bid or ask side. A skewed and non-skewed two way price has still the same spread!\n\nTrader spread and skew are parameters either manually set by the trader, but also automatically via rules.\n\n# <span id=\"page-6-2\"></span>**1.3 Overall Data Flow**\n\nBelow the general data flow within the MMC:\n\n- 1. Liquidity providers (market makers) send quotes into the system. Each provider sends usually different quote levels. Quotes can be both tradable liquidity, but also pure market data.\n- 2. Quotes are filtered and aggregated during Reference Price Finding. The basic idea here is to filter out unwanted quotes and create a trustable pool of liquidity for pricing.\n\n- 3. Traders will manipulate the inbound liquidity and increase the spread and/or skew the price to the left or ride side.\n- 4. The outbound price is forwarded to clients via RFS, SST, and FIX API's.\n- 5. Clients create orders based on the price provided by the pricing engine. Such orders are routed back to the pricing engine.\n- 6. Accepted client orders are added to the MMC positions.\n- 7. Based on the configured risk management profile, the system will eventually decide to create a hedge order to reduce or flatten a position. Such hedge orders can be created manually too.\n- 8. Trades originating from hedge orders flow back into the pricing engine position.\n\n# <span id=\"page-7-0\"></span>**1.4 Supported Products**\n\nMarket Maker Cockpit owner can price incoming requests for following products:\n\n- 1. FX Spot: Any incoming Spot requests which are negotiated as RFS (Request for Streaming) or SEP (Streaming Executable Prices) can be routed to and priced by Market Maker Cockpit. Spot outbound quotes are constructed via Reference Price Finding as described in Section 3 and downstreamed to AutoDealingSuite where it can be adjusted with sales margins and trader spreads.\n- 2. FX Forward: MMC can price any incoming FX Forward requests by using an additional market data source. MMC owners can provide their own swap points via an interface to 360T or use 360T`s Swap Data Feed (SDF) market data to price incoming Forward requests via MMC. Once a market data source is defined for pricing engine, spot components of the outright rates are received from Outbound quotes (as described in Section 3) and forward points are received from the defined Market Data Source.\n- 3. FX Swap: MMC can price any incoming even and uneven FX Swap requests. Similarly to FX Forwards, the MMC generates the spot rate from the outbound quotes and retrieves the forward points on each leg from the defined forward market data source.\n- 4. FX Future: MMC facilitates the generation of Eurex Exchange listed FX Future outbound prices in two ways:\n  - It utilizes the outbound FX Spot price in order to form the Outbound FX Future price; the outbound FX Future price matches the outbound FX Spot price\n  - The outbound FX Forward price is taken for the generation of the outbound FX Future price.\n\nFX Future prices can be either streamed directly into the Eurex Exchange Central Limit Order Book (CLOB) or as off-book liquidity to 360T customers.\n\n- 5. FX Rolling Spot Futures: MMC can generate off-book or on-book FX Rolling Spot Futures by taking the outbound FX Spot prices.\n- 6. FX NDF: MMC can price any incoming NDF requests by using additional market data source for forward points. MMC owners can provide their own swap points data for NDFs via an interface to 360T or using 360T`s Swap Data Feed (SDF). Please note that Spot component of the outright rates are received from Outbound quotes (as described in Section 3) which are Onshore Spot rates.\n- 7. Block-Trades: Similar to FX Forward pricing, MMC can price any incoming Block-Trade requests with many legs. These requests are priced via an additional market\n\ndata source with forward points information. For spot component, net amount of the block is taken into consideration while determining the side and band size.\n\n## **Please note;**\n\nNever leave the ADS MMC unattended when it was started to actively quote and manage currency positions!\n\n# <span id=\"page-8-0\"></span>**2 MARKET MAKER COCKPIT OVERVIEW**\n\nThe Market Maker Cockpit provides an overview of currency pairs being set up, current positions including profit and loss and various blotters to monitor client (requester) and hedge orders.\n\n| <b>Market Maker Cockpit</b><br><b>Audit Log</b>                                                                                                                                               |                                                                                                                |                                                                                                                                                                                                |                                                     |                                                                  | $\\bullet$ $\\circ$ $\\bullet$ $\\circ$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |\n|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------|------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <b>Liquidity Details</b>                                                                                                                                                                      |                                                                                                                |                                                                                                                                                                                                |                                                     |                                                                  | ⊘                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |\n| EUR/GBP<br>Inbound<br>1 <sub>m</sub><br>085750<br>0.86094<br>5 <sub>m</sub><br>$34.4 -$<br>10 <sub>m</sub><br>0.86201<br>0.8564 <sub>3</sub><br>15 <sub>m</sub><br>$-55.8$<br>25 <sub>m</sub> | Core Channel 1 (2)<br>Outbound<br>0.86094<br>0.8575 <sub>0</sub><br>$34.4 -$<br>0.564<br><b>081201</b><br>55.8 | Inbound<br>1 <sub>m</sub><br>1 <sub>m</sub><br>085797<br>5 <sub>m</sub><br>5m<br>$-25.8$<br>10 <sub>m</sub><br>10 <sub>m</sub><br>15 <sub>m</sub><br>15 <sub>m</sub><br>25 <sub>m</sub><br>25m | Core Channel 2 Q2<br>0.8505 <sub>1</sub><br>0.85797 | Outbound<br>0.8605 <sub>1</sub><br>$-25.8$                       | 1m<br>5 <sub>m</sub><br>10 <sub>m</sub><br>15m<br>25 <sub>m</sub>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |\n| EUR/USD<br>Inbound<br>0.90829<br>0.98765<br>1 <sub>m</sub><br>$6.4 -$<br>0.98704<br>09889n<br>5 <sub>m</sub><br>18.6                                                                          | Core Channel 1 (2)<br>Outbound<br>09377<br>091837<br>$0.83$ $6.4$<br>0.58704<br>09889n<br>18.6                 | Inbound<br>1 <sub>m</sub><br>1m<br>5 <sub>m</sub><br>5m                                                                                                                                        | Core Channel 2 22                                   | Outbound                                                         | 1 <sub>m</sub><br>5 <sub>m</sub>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |\n| <b>Instrument Control</b><br><b>Client Orders</b><br>All Orders<br><b>Hedge Orders</b>                                                                                                        | <b>Client Activity</b>                                                                                         | $\\sqrt{2}$<br><b>Instrument Positions</b>                                                                                                                                                      |                                                     |                                                                  | $\\sqrt{ }$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |\n| Mode<br>Instrument<br>Managed By                                                                                                                                                              | Skew %<br>Skew PIPS                                                                                            | symbol<br>Scenario                                                                                                                                                                             | <b>Updated</b><br>Size CCY1                         | Size CCY2<br>Open PL                                             | Realized PL<br>Total PL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |\n| Managed<br>٠<br><b>All Instruments</b><br>Managed<br>EUR/CBP<br>360TMMCTrader1                                                                                                                | $-00$<br>းဝေ<br>Ő.<br>$\\bullet$<br>$-4.1$<br><b>SO</b>                                                         | $\\circ$<br>EUR/GBP<br>$= 0$<br><b>EUR/USD</b><br>ane.                                                                                                                                          | $\\circ$<br>07:21:11.671<br>$\\circ$<br>07:21:11.701  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>0.00<br>0.00<br>0.00 | $\\forall \\exists \\emptyset$ = $\\textcircled{8}$<br>0.00<br>0.00<br>0.00<br>$\\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{V} \\rightarrow \\mathcal{$<br>0.00 |\n| Managed<br>EUR/USD<br>360TMMCTrader1                                                                                                                                                          | $-99$<br>$- 0 + 00$<br>$\\blacksquare$<br>$\\bullet$<br>$-4$                                                     | one<br>$\\triangle$ $\\circ$<br>CBP/USD                                                                                                                                                          | $\\circ$<br>07:21:11.713                             | 0.00<br>0.00                                                     | $y \\rightarrow 0 = 0$<br>0.00<br>0.00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |\n| Managed<br>CBP/USD<br>360TMMCTrader1                                                                                                                                                          | 00<br>$ 00\\rangle$<br>61<br>$\\bullet$                                                                          | $\\equiv$ $\\alpha$<br>MXN/JPY<br>ane.                                                                                                                                                           | $\\circ$<br>07:21:11.715                             | 0.00<br>0.00                                                     | $V + Q = Q$<br>0.00<br>0.00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |\n| Managed<br>MXN/JPY<br>360TMMCTrader1                                                                                                                                                          | 00<br>00<br>۰<br>$\\circ$                                                                                       | $\\triangle$ $\\alpha$<br><b>LISD/TRY</b>                                                                                                                                                        | $\\circ$<br>07:21:11.668                             | 0.00<br>0.00                                                     | 0.00<br>$\\forall \\exists \\emptyset = \\textcircled{x}$<br>0.00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |\n| Managed<br><b>USD/TRY</b><br>360TMMCTrader1                                                                                                                                                   | O <sup>o</sup><br>00<br>$\\sim$<br>-<br>-                                                                       | $\\alpha$<br><b>XAU/USD</b>                                                                                                                                                                     | $\\circ$<br>07:21:11.682                             | 0.00<br>0.00                                                     | 0.00<br>0.00<br>$\\not\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\!\\$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |\n| Managed<br>XAU/USD<br>360TMMCTrader1<br>Я зботммс. Trader1, 360T.MMC                                                                                                                          | 00<br>GI<br>$0 + 00$<br>$\\bullet$<br>$\\overline{\\phantom{a}}$                                                  | none<br>$B \\triangleq Q$<br>$F = C$                                                                                                                                                            |                                                     |                                                                  | 02.11.2022.08:48.01 // Connected                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |\n\n<span id=\"page-8-2\"></span>Figure 1 Market Maker Cockpit Overview\n\n# <span id=\"page-8-1\"></span>**2.1 Login and Logout**\n\nUsers can login to the MMC user interface through an internet browser by below link:\n\n### [https://ext01.360t.com/PE\\\\_1/static/login.html](https://ext01.360t.com/PE_1/static/login.html)\n\nLogin is possible after entering the username, password and the token sent to the user`s email address.\n\n![](_page_9_Picture_2.jpeg)\n\nFigure 2 Login and Start the Market Maker Cockpit\n\n<span id=\"page-9-2\"></span>To logout, click on logout button on top right or close the browser.\n\n<span id=\"page-9-3\"></span>Figure 3 Exit the Market Maker Cockpit\n\n# <span id=\"page-9-0\"></span>**2.2 Panels, Tabs and Tab Container**\n\nThe user interface is divided into various tabs such as Liquidity Details, Instrument Control, Order blotters and Instrument Positions.\n\nEach tab consists of a tab header (rectangle box in the upper left corner containing the tab title), and tab content.\n\nTabs are organized within tab containers. By default there are four tab containers; one upper left, one bottom left, one upper right, and one at the bottom left. Each tab container is resizable by either moving the border between tab containers, or by maximizing/minimizing the view.\n\nYour customized layout will be automatically saved when logging out to be available at the next login.\n\n# <span id=\"page-9-1\"></span>**2.3 Modifying Tabs and Tab Containers**\n\nTabs can be moved within the same tab container, into another existing tab container, or into a new tab container. Tabs can also be displayed separately by maximising the view.\n\nTo move a tab, click and hold the tab header with the left mouse button, and move the tab into a new location. Release the mouse button over the new target location.\n\nTo re-arrange the tab order within a tab container, drag and drop the tab horizontally within the same tab container.\n\nTo move a tab into another tab container, drop the tab onto the tab header of the target tab container.\n\nTo create a new tab container, drop the tab into the content of any other tab. Dependent where you drop the tab, the target tab will split either horizontally or vertically, and a new tab container will be added.\n\nWhen the last tab of a container is removed, the tab container will disappear automatically.\n\nClicking on maximize icon ( ) will maximize the tab container to the size of the enclosing window. View can be restored by minimizing the tab container by clicking on icon.\n\n# <span id=\"page-11-0\"></span>**3 CONFIGURE PRICING**\n\n# <span id=\"page-11-1\"></span>**3.1 Instrument Control**\n\nThe instrument control tab allows to configure:\n\n- Managed instruments\n- General parameters such as time slippage and handling of unmanaged instruments and crosses\n- Start and stop pricing and set the pricing/risk management mode\n- Pricing tiers\n- Client order handling\n- Position rules\n- pricing and skewing rules\n- reference price finding\n- risk management\n- ownership for an instrument or price channel\n\nThe instrument control panel is organized into rows and columns. Each row contains information for a specific managed instrument or channel.\n\nFor each currency pair the trader can configure multiple pricing channels. Each pricing channel can have different spread/skew settings and Reference Price Finding rules. The assignment of your clients to the different pricing channel prices can be done by your admin users who have access to `Stream Group Mapping` within Business Configuration Tool.\n\n| <b>Instrument Control</b> |  |                   |                                              |                                           |                                                                    |                      |    | ⊘                            |                                             |\n|---------------------------|--|-------------------|----------------------------------------------|-------------------------------------------|--------------------------------------------------------------------|----------------------|----|------------------------------|---------------------------------------------|\n| Instrument                |  | <b>Managed By</b> | Mode                                         | <b>Skew PIPS</b>                          | Skew %                                                             | Scenario             |    |                              |                                             |\n| <b>All Instruments</b>    |  |                   | <b>Managed</b>                               |                                           |                                                                    | none                 |    |                              | ≎                                           |\n| EUR/GBP                   |  | 360TMMC.Trader1   | Managed                                      | $\\Theta$<br>$\\Box$<br>2.1                 | $+$ $+$ $+$ $+$ $+$ $+$<br>- 91 -<br>$\\mathbf{a}$                  | none                 | ⊳  |                              | ∎≏≎                                         |\n| EUR/USD                   |  | 360TMMCTrader1    | <b>B2B</b>                                   | $\\Theta$<br>$\\blacksquare$                | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>-915-<br>10 | none<br>$\\checkmark$ |    |                              | $\\triangleright$ 0 $\\triangle$ $\\heartsuit$ |\n| GBP/USD                   |  | 360TMMC.Trader1   | <b>B2B</b>                                   | $\\Theta$<br>⋴                             | $\\Theta$<br>$\\blacksquare$<br>n                                    | none<br>$\\checkmark$ |    | $\\blacktriangleright$ $\\Box$ | 8 ⇔                                         |\n| USD/BRL                   |  | 360TMMC.Trader1   | B <sub>2</sub> B<br>$\\overline{\\phantom{0}}$ | $\\Theta$<br>- 01-<br>n                    | $+$ $+$ $+$ $+$ $+$ $+$<br>$\\blacksquare$<br>$\\sqrt{2}$            | $\\checkmark$<br>none | >1 |                              | ∎≙≎                                         |\n| USD/TRY                   |  | 360TMMC.Trader1   | <b>B2B</b><br>$\\overline{\\phantom{a}}$       | $\\Theta$<br>$\\bullet$ $-$<br>$\\mathbf{0}$ | $\\Theta$<br>$\\bullet$ $-$<br>$\\Omega$                              | test                 |    |                              | $\\blacktriangleright$                       |\n| XAU/USD                   |  | 360TMMC.Trader1   | <b>Managed</b>                               | $\\Theta$<br>$\\Box$                        | $\\Theta$<br>$\\bullet$ $-$                                          | none                 |    | ▶ ■                          | Α Φ                                         |\n\n<span id=\"page-11-2\"></span>Figure 4 Instrument Control Tab\n\nColumn description:\n\n- **Instrument:** Displays the managed instruments defined in Global Instrument Configuration panel.\n- **Status Lights:** Displays the current pricing status of instrument. Green indicates pricing is on, red indicates pricing is off. Yellow applies to a specific instrument or \"All Instruments\", and indicates a mixed pricing state of channels beneath.\n- **Managed By:** Displays which user currently \"owns\" the instrument. Only the user who owns an instrument can modify pricing and risk management configuration, and start or stop pricing for this instrument.\n- **Mode:** Current risk management mode of the instrument is displayed. The mode can be changed by clicking on the drop down box which will bring the three different modes: B2B, Flow Hedge and Managed. The value can only be changed by the user who currently owns the instrument.\n- **Skew PIPS:** Displays the manual skew in terms of absolute numbers specified in PIPS. Instrument owner can define the manual skew in terms of pips using this\n\ncolumn. The value defined here will be used in combination with the skew factor of the instrument. (Please see…)\n\n- **Skew %:** Displays the manual skew in terms of percentage of the outbound spread. Instrument owner can define the manual skew in terms of percentage using this column. The value defined here will be used in combination with the skew factor of the instrument. (Please see…)\n- **Scenario:** Displays the currently active risk management scenario. Instrument owner can change the scenario per instrument by clicking on the dropdown menu.\n- **Start/Stop buttons:** By clicking on the green button ( ) on the respective row, admin user will start pricing all instruments or specific instrument for all channels. Likewise, clicking on the red button ( ) will stop pricing respective instrument or all instruments for all channels. Pressing start or stop in the \"All Instruments\" row will affects all instruments the user currently owns.\n- **Ownership:** A user can take ownership for a specific instrument by clicking on icon. This button is only enabled for instruments owned by other users.\n- **Configure ( ):** By clicking on icon for specific instrument (currency pair), instrument owner can open the Instrument Configuration panel. From this panel, instrument owner can configure pricing, manage reference price finding settings and risk management parameters. Clicking on this icon for all instruments opens the Global Instrument Configuration panel where admin user can configure general parameters, instruments, cross rules, tier sizes, risk management scenarios and client order handling.\n\n# <span id=\"page-12-0\"></span>**3.2 Add and Remove Managed Instruments**\n\nTo manage pricing and risk for a specific instrument, currency pair must be added to the list\n\nof managed instruments in the Pricing Control tab. To do so press on the button for \"All Instruments\" in the \"Instrument Control\" tab.\n\n| <b>Instrument Control</b> |                   |                |                                                                                       |                                                         |                      |                                                           | ⊘   |\n|---------------------------|-------------------|----------------|---------------------------------------------------------------------------------------|---------------------------------------------------------|----------------------|-----------------------------------------------------------|-----|\n| Instrument                | <b>Managed By</b> | Mode           | Skew PIPS                                                                             | Skew %                                                  | Scenario             |                                                           |     |\n| <b>All Instruments</b>    |                   | <b>Managed</b> |                                                                                       |                                                         | none                 |                                                           | Þ   |\n| <b>EUR/GBP</b>            | 360TMMC.Trader1   | Managed<br>СZ. | $ \\oplus \\infty $<br>$\\bigcap -2.1$                                                   | $  \\oplus \\otimes$<br>$\\left  \\bullet \\right $<br>- 0   | none<br>$\\checkmark$ |                                                           | ⊩ ధ |\n| EUR/USD                   | 360TMMC.Trader1   | <b>B2B</b>     | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>$\\blacksquare$<br>$\\mathbf{0}$ | $+$ $+$ $+$ $\\odot$<br>10<br>$\\blacksquare$             | none                 | $\\triangleright \\blacksquare \\triangle \\triangleright$    |     |\n| USD/BRL                   | 360TMMC.Trader1   | <b>B2B</b>     | $\\Theta$<br>▭                                                                         | $  \\oplus \\otimes$<br>$\\blacksquare$                    | $\\checkmark$<br>none | $\\triangleright \\blacksquare \\triangle \\lozenge$          |     |\n| USD/TRY                   | 360TMMCTrader1    | <b>B2B</b>     | $+100$<br>$\\Box$<br>$\\mathbf{u}$                                                      | $+108$<br>--                                            | test<br>$\\checkmark$ | $\\triangleright$ $\\blacksquare$ $\\uparrow$ $\\updownarrow$ |     |\n| <b>XAU/USD</b>            | 360TMMC.Trader1   | Managed        | $\\Theta$<br>a si pro                                                                  | AB <br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>e 1- | $\\checkmark$<br>none | $\\triangleright \\blacksquare \\triangle \\lozenge$          |     |\n| GBP/USD                   | 360TMMC.Trader1   | <b>B2B</b>     | $\\Theta$<br>- OF                                                                      | $+100$<br>- کالگ                                        | $\\checkmark$<br>none | $\\blacktriangleright$ $\\Box$ $\\triangle$ $\\varnothing$    |     |\n\n<span id=\"page-12-1\"></span>Figure 5 Open Global Instrument Configuration\n\nThis will open the \"Global Instrument Configuration\" dialog:\n\n![](_page_13_Picture_1.jpeg)\n\n<span id=\"page-13-2\"></span>To add a new managed instrument, select base and quote currency, and press add ( ) button.\n\nTo remove a specific managed instrument, click on the remove button ( ) next to the respective currency pair in the Active Instruments list.\n\n# <span id=\"page-13-0\"></span>**3.3 Ownership of a Managed Instrument**\n\nA user can only control pricing and risk management for instruments he currently owns. To take ownership of another user's instrument, user should click on the (take ownership) button in the Pricing Control tab. The button is only enabled for the instruments which are currently owned by another user.\n\n|                 |                                     |                                                          |                                                                        |                      | ĸ                                                             |\n|-----------------|-------------------------------------|----------------------------------------------------------|------------------------------------------------------------------------|----------------------|---------------------------------------------------------------|\n| Managed By      | Mode                                | <b>Skew PIPS</b>                                         | Skew %                                                                 | Scenario             |                                                               |\n|                 | <b>Managed</b>                      |                                                          |                                                                        | none                 | o                                                             |\n| 360TMMC.Trader2 | Managed                             | 2.1 + $\\Theta$<br>0                                      | $+$ $+$ $\\oplus$<br>$\\Box$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                      | $\\theta$                                                      |\n| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>c | $+$ $+$ $\\oplus$<br>6<br>10                                            | $\\checkmark$<br>none | ∎≏ ≎                                                          |\n| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $\\infty$<br>$\\blacksquare$                   | $+$ $+$ $+$ $\\infty$<br>$\\Box$<br>$\\Omega$                             | $\\checkmark$<br>none | ≙≎                                                            |\n| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $+$ $+$<br>c                                 | $+$ $+$ $+$ $+$ $+$<br>œ.<br>$\\Omega$                                  | $\\checkmark$<br>none | ∎≙≎                                                           |\n| 360TMMC.Trader1 | <b>B2B</b>                          | $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$ $+$<br>c | $+$ $+$ $\\oplus$ $\\otimes$<br>$\\Omega$                                 | test<br>$\\checkmark$ | ∎≙≎<br>$\\mathbf{v}$                                           |\n| 360TMMC.Trader1 | Managed<br>$\\overline{\\phantom{a}}$ | $+$ $+$ $\\oplus$ $\\otimes$<br>$\\blacksquare$             | $+$ $+$ $\\odot$ $\\odot$<br>- 91-<br>$\\Omega$                           | $\\checkmark$<br>none | $\\blacktriangleright$ $\\blacksquare$ $\\triangle$ $\\heartsuit$ |\n\n<span id=\"page-13-3\"></span>Figure 7: Take over instrument ownership\n\nThe column \"Managed By\" shows the user who currently owns a specific instrument.\n\n# <span id=\"page-13-1\"></span>**3.4 Start/Stop Pricing of Instruments and Channels**\n\nBy clicking on the green button ( ) on the respective row, admin user will start pricing all instruments or specific instrument for all channels. Likewise, clicking on the red button ( ) will stop pricing respective instrument or all instruments for all channels. Pressing start or stop in the \"All Instruments\" row will affects all instruments the user currently owns.\n\n| Instrument      | <b>Managed By</b> | Mode           | <b>Skew PIPS</b>           | Skew %                              | Scenario |     |\n|-----------------|-------------------|----------------|----------------------------|-------------------------------------|----------|-----|\n| All Instruments |                   | Managed        |                            |                                     | none     | ≎   |\n| EUR/GBP         | 360TMMC.Trader2   | Managed        | $2.1 + \\bigoplus$          | $+$ $+$ $\\otimes$<br>-              |          | Ö   |\n| EUR/USD         | 360TMMC.Trader1   | <b>B2B</b>     | AB<br>-                    | $\\Theta$<br>$\\blacksquare$<br>10    | none     | ⊩ ⇔ |\n| GBP/USD         | 360TMMC.Trader1   | <b>B2B</b>     | $\\Theta$<br>-              | $\\bigoplus$<br>e –                  | none     | Ö   |\n| USD/BRL         | 360TMMC.Trader1   | <b>B2B</b>     | $\\Theta$<br>-              | $\\Theta$<br>- OF                    | none     | ) ບ |\n| USD/TRY         | 360TMMC.Trader1   | <b>B2B</b>     | $ \\oplus \\otimes$<br>-     | $\\bigoplus$<br>$\\blacksquare$<br>n. | test     | ΡΦ  |\n| XAU/USD         | 360TMMC.Trader1   | <b>Managed</b> | $\\Theta$<br>$\\blacksquare$ | $\\Theta$<br>-91-                    | none     | 10  |\n\n<span id=\"page-14-0\"></span>Figure 8 Start/Stop Pricing\n\nIn order to stop/start pricing specific channel of an instrument, user can right-cllick on the respective instrument. This will pop-up a window where owner can start or stop pricing of a certain stream channel.\n\n| <b>Instrument Control</b> |                                                                                                          |                 |\n|---------------------------|----------------------------------------------------------------------------------------------------------|-----------------|\n| Instrument                |                                                                                                          | Managed By      |\n| <b>All Instruments</b>    |                                                                                                          |                 |\n| EUR/GBP                   |                                                                                                          | 360TMMC.Trader1 |\n| EUR/USD                   |                                                                                                          | 360TMMC.Trader1 |\n| GBP/USD                   |                                                                                                          | 360TMMC Trader1 |\n| USD/BRL                   |                                                                                                          | 360TMMC.Trader1 |\n| USD/TRY                   |                                                                                                          | der 1           |\n| XAU/USD                   | <b>USD/TRY</b><br>Core Channel 1<br><b>Core Channel 2</b><br><b>Take Ownership</b><br>Configuration<br>o | er 1            |\n\n<span id=\"page-14-1\"></span>Figure 9 Start/Stop Channels\n\nNote: A user can only start or stop his own managed instruments!\n\nTo stop **all** pricing of **all** currency pairs click on `Stop All` button on top right or on (**Stop All Instruments**) in the \"All Instruments\" row. No request will be quoted any more.\n\n![](_page_14_Picture_10.jpeg)\n\n<span id=\"page-14-2\"></span>Figure 10 Emergency Stop\n\nThe 'Stop All\" button is an emergency stop button. Pressing this button pricing will stop for all managed instruments.\n\n# <span id=\"page-15-0\"></span>**3.5 Pricing and Risk Management Mode**\n\nTo quickly change between pricing and risk management modes for a specific instrument, select the respective mode in the Mode column for the specific instrument.\n\n![](_page_15_Picture_4.jpeg)\n\nFigure 11: Select risk management mode\n\n<span id=\"page-15-1\"></span>The provided modes are:\n\n- **Managed:** All currently defined risk management rules apply. Positions will potentially be accumulated.\n- **B2B:** Back to back, all incoming requester orders will first be hedged, and **only accepted** if the hedge trade was successful (last look)\n- **Flow Hedge:** All incoming requester orders will be accepted, but immediately hedged (without a last look).\n\nA user can change the risk management mode for all his currently owned instruments, by selecting a risk management mode in row \"All Instruments\".\n\nInstrument owner can also assign different risk management mode for different streaming channels. This can be done by right-click on the instrument name under Instrument Control panel.\n\n|                   | EUR/GBP                                      |                   |  |\n|-------------------|----------------------------------------------|-------------------|--|\n| GB                | Mode                                         |                   |  |\n|                   | Core Channel 1<br><b>B2B</b><br>$\\checkmark$ | annel 1 $\\alpha$  |  |\n| Instrum           | Core Channel 2 Flow Hedge                    |                   |  |\n| Instrume          | Take Ownership                               | lode              |  |\n| <b>All Instru</b> | Configuration                                | Managed           |  |\n| EUR/GBP           | 360TMMC.Trader1                              | <b>Flow Hedge</b> |  |\n\n<span id=\"page-15-2\"></span>Figure 12 Different Risk Management Assignment to Different Streams\n\n# <span id=\"page-16-0\"></span>**3.6 Tier Size Configuration**\n\nTo adjust the tier size for instruments, open the global instrument configuration dialog and select option \"Tiers\":\n\n| <b>General Parameters</b>    | Instrument | Tier 1       | Tier <sub>2</sub> | Tier 3        | Tier <sub>4</sub> | Tier 5        | Tier <sub>6</sub> |\n|------------------------------|------------|--------------|-------------------|---------------|-------------------|---------------|-------------------|\n| Instruments                  | EUR/GBP    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |\n| $\\sum$ Tiers                 | EUR/USD    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |\n| Scenarios                    | GBP/USD    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |\n| <b>Cross Rules</b>           | USD/BRL    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |\n| <b>Client Order Handling</b> | USD/TRY    | 1,000,000.00 | 5,000,000.00      | 10,000,000.00 | 15,000,000.00     | 25,000,000.00 | 0.00              |\n|                              | XAU/USD    | 100.00       | 200.00            | 500.00        | 1,000.00          | 5,000.00      | 0.00              |\n|                              |            |              |                   |               |                   |               |                   |\n\n<span id=\"page-16-2\"></span>Figure 13 Define Tier Sizes\n\nThe dialog allows a user to configure up to 6 pricing tiers of arbitrary size individually for each managed instrument.\n\nTo change the size of an existing tier simply overwrite the according cell with a new value. To remove a specific tier, erase the value and leave the cell empty. Tiers can be entered in any order. The MMC GUI will display the tiers automatically sorted by size.\n\nAfter clicking on `Apply` on bottom right, changes in the tier configuration are effective immediately.\n\nNote:\n\nThe MMC stores pricing and reference price finding configurations for each cell in the configuration dialog (e.g. EUR/USD – Tier 3). This configuration will be deleted whenever the size of a specific tier is changed.\n\n# <span id=\"page-16-1\"></span>**3.7 Instrument Configuration**\n\nTo configure pricing, reference price finding and risk management for a specific instrument, click on the Configure button in the Instrument Control tab:\n\n<span id=\"page-16-3\"></span>![](_page_16_Picture_13.jpeg)\n\nFigure 14: Open instrument configuration dialog\n\n| Instrument Configuration EUR/USD |            |                                                                                                                                                                                           |                 |       |                                                                    |                         |                         | X |\n|----------------------------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|-------|--------------------------------------------------------------------|-------------------------|-------------------------|---|\n| $\\vee$ Pricing                   |            |                                                                                                                                                                                           | Spread          |       |                                                                    |                         | Slippage                |   |\n| > Core Channel 1                 | Tier       | Min                                                                                                                                                                                       | <b>Add Pips</b> | Max   | <b>Skew Factor</b>                                                 |                         | Percent                 |   |\n| Core Channel 2                   | 1,000,000  | $\\pm$                                                                                                                                                                                     | $\\pm$           | ÷     | 100 %                                                              |                         |                         | Ŧ |\n| <b>Reference Price Finding</b>   | 5,000,000  |                                                                                                                                                                                           |                 | ÷     | 100 %                                                              |                         |                         | Ð |\n| <b>Risk Management</b>           | 15,000,000 | $\\pm$                                                                                                                                                                                     | ÷               | $\\pm$ | 0%                                                                 |                         |                         | ÷ |\n|                                  | 25,000,000 |                                                                                                                                                                                           |                 | m     | 0%                                                                 |                         |                         |   |\n|                                  |            | Round outbound quotes to decimals:<br>Allow spread less than Market<br>Allow skew to cross Mid Price<br>Allow skew to cross Opposite Side<br>Allow slippage Worse than Client Order Price |                 |       | Reference stream:<br>Percent tolerance:<br>Non executable streams: | 360TEDF.FEED<br>51<br>ヺ |                         |   |\n|                                  |            |                                                                                                                                                                                           |                 |       |                                                                    |                         | <b>Discard</b><br>Apply |   |\n\nThis will open the 'Instrument Configuration' dialog for the selected instrument:\n\n<span id=\"page-17-1\"></span>Figure 15: Instrument Configuration Dialog\n\nFor each managed instruments users can configure:\n\n- Pricing to create outbound prices based on reference prices with additional or fixed spread, manual skew, slippage and cutoff rules\n- [Reference Price Finding](#page-22-0) to determine filter criteria to define reference prices\n- [Risk Management](#page-35-0) to manage position and pricing rules\n\nThe navigation tree on the left side of the dialog allows to select Pricing or Reference Price Finding for a specific channel, and risk management for the entire instrument.\n\n## <span id=\"page-17-0\"></span>**3.8 Reference Price Finding**\n\nIn order to create outbound prices, MMC admin should first define the filter criteria for inbound streams per specific stream channel and tier size.\n\nInitially no provider is selected to create inbound and as a result, outbound prices. To get inbound prices one has to select **Reference Price Finding** and choose the desired provider(s).\n\nThere are various strategies and parameters to filter inbound quotes, and to calculate a reference price for each pricing tier:\n\n| Strategy     | Parameter 1        | Parameter 2       |\n|--------------|--------------------|-------------------|\n| Best Price   | Minimum Quote Size | Minimum Providers |\n| Deep Average | Minimum Quote Size | Levels            |\n| Deep Worst   | Minimum Quote Size | Levels            |\n| VWAP Average | Minimum Quote Size | Maximum Providers |\n\n| Strategy       | Parameter 1        | Parameter 2       |\n|----------------|--------------------|-------------------|\n| VWAP Worst     | Minimum Quote Size | Maximum Providers |\n| Fix Core Price | Bid                | Ask               |\n\n**Best Price** strategy tries to find the best single quote, of at least minimum quote size. To find a reference price, there must be at least 'minimum providers' number of quotes, of minimum quote size available.\n\nThis strategy is ideal for B2B hedging when maximum fill ratio is most important (e.g. in case of RFS client orders). When Best Price is used, B2B hedge orders will be placed as limit orders, with the original reference price. This is to ensure that both trader and sales spread will be preserved.\n\n**Deep Average and Deep Worst** consider the top n (levels) quotes in the book, of at least minimum size. Average will calculate a VWAP price of the top n levels, whereas worst will simply pick the nth level down from top of the book. The number of levels can be configured via the Min Providers/Ask column. A level is considered as a unique price from a specific provider. In other words, identical prices from two different providers are considered as two levels.\n\n| Level | Provider | Bid    | Ask    | Provider |\n|-------|----------|--------|--------|----------|\n| 1     | A        | 1.1240 | 1.1242 | B        |\n| 2     | B        | 1.1240 | 1.1243 | B        |\n| 3     | B        | 1.1239 | 1.1243 | A        |\n| 4     | C        | 1.1238 | 1.1244 | C        |\n| 5     | D        | 1.1237 | 1.1245 | D        |\n\nExample book:\n\nThe example shows on the bid side for the first three levels: 1.1240, 1.1240, 1.2339.\n\nBid levels 1 and 2 are treated as different level because they are from different providers, even though the price is the same.\n\n**VWAP Average and VWAP Worst** will calculate a VWAP reference price by considering all quotes of at least minimum quote size. The required quantity for the VWAP price is in general identical with the pricing tier size. E.g. for a 5m tier, the VWAP algorithm will try to find the best VWAP price for 5m quantity. Only one quote from each provider will be used in the VWAP calculation. The maximum number of providers in the VWAP calculation can be limited.\n\nThe strategy will not return a reference price if the total quantity of quotes available (with minimum quote size) is less than the required amount (pricing tier size).\n\nVWAP is a good strategy for running positions, but also for B2B hedging when partially filling of client orders is acceptable. With VWAP pricing, B2B hedge orders will be placed as limit orders with the requested price of the client order. In other words, in the worst case the hedge order will be filled with the client order price, and both trader and sales spread are lost.\n\n**Fix Core Price** will directly define a bid and ask outbound price for the selected channel and can be used for pegged currencies.\n\n| Instrument Configuration EUR/USD |  |\n|----------------------------------|--|\n|                                  |  |\n\n| Pricing                        | Tier | <b>Providers</b>                         | Hedge        | <b>Strategy</b>                                                             | Min Ouote Size/Bid | Min Providers/Ask |\n|--------------------------------|------|------------------------------------------|--------------|-----------------------------------------------------------------------------|--------------------|-------------------|\n| $\\vee$ Reference Price Finding |      | 360TEDF.FEED 360TEXEC.T =/<br>1,000,000  | <b>Hedge</b> | <b>Best Price</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!               | 1,000,000          |                   |\n| Core Channel 1                 |      | 5,000,000<br>Barclays BARX.DEMO RBS =    | Hedge        | VWAP Average $\\vee$                                                         | 2,000,000          |                   |\n| <b>Core Channel 2</b>          |      | 15,000,000<br>Barclays BARX.DEMO BOA = ∕ | Hedge        | $\\left\\langle \\right\\rangle$ Deep Average $\\left\\langle \\vee \\right\\rangle$ | 1,000,000          |                   |\n| <b>Risk Management</b>         |      | 25,000,000                               | Hedge        | <b>Best Price</b><br>$\\sim$                                                 |                    |                   |\n\n<span id=\"page-19-1\"></span>Figure 16: Reference Price Finding rules editor\n\nClick on the activate button under tier label (e.g. 5m) to enable or disable a specific tier. In the example above the 25m tier is currently disabled.\n\n# <span id=\"page-19-0\"></span>**3.9 Additonal and Fixed Spread**\n\nConfiguring spreads can be done in various ways:\n\n- **Fix Pips:** Fixed outbound spread defined in terms of PIPS independent of the inbound spread\n- **Add to Inbound Spread:** Relative outbound spread based on percentage of inbound spread\n- **Add Pips:** Outbound spread is always n PIPS wider than the inbound spread\n\n|                                  |            |                                              |                   |                        |                         |                               |                         | X |\n|----------------------------------|------------|----------------------------------------------|-------------------|------------------------|-------------------------|-------------------------------|-------------------------|---|\n| Instrument Configuration EUR/GBP |            |                                              |                   |                        |                         |                               |                         |   |\n| $\\vee$ Pricing                   |            |                                              | Spread            |                        |                         |                               | Slippage                |   |\n| > Core Channel 1                 | Tier       | VO)<br>Min                                   | Add to Inb $\\vee$ | $\\bigtriangledown$ Max | <b>Skew Factor</b>      |                               | Percent                 |   |\n| Core Channel 2                   | 1,000,000  | $\\mathbf{Z}$                                 | 100               | 20<br>Ð                | 0%                      |                               |                         | D |\n| <b>Reference Price Finding</b>   | 4,000,000  |                                              |                   |                        | 0%                      |                               |                         | Ð |\n| <b>Risk Management</b>           | 10,000,000 |                                              |                   | $\\pm$                  | 0%                      |                               |                         | Ð |\n|                                  | 15,000,000 |                                              |                   |                        | 0%                      |                               |                         |   |\n|                                  | 25,000,000 |                                              |                   |                        | 0%                      |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n|                                  | VO         | Round outbound quotes to decimals:           | $-4$ +            |                        | Reference stream:       | 360TEDF.FEED                  |                         |   |\n|                                  |            | Allow spread less than Market                |                   |                        | Percent tolerance:      | $\\mathbf{z}$                  |                         |   |\n|                                  |            | Allow skew to cross Mid Price                |                   |                        | Non executable streams: | 360TEXEC.TEST 360TEDF.FEED =/ |                         |   |\n|                                  |            | Allow skew to cross Opposite Side            |                   |                        |                         |                               |                         |   |\n|                                  |            | Allow slippage Worse than Client Order Price |                   |                        |                         |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n|                                  |            |                                              |                   |                        |                         |                               | <b>Discard</b><br>Apply |   |\n|                                  |            |                                              |                   |                        |                         |                               |                         |   |\n\n<span id=\"page-19-2\"></span>Figure 17 Defining Outbound Spread\n\n**Fixed** spreads are calculated around the mid-price of Inbound. When chosen, the system applies the defined fixed spread or, if wider, the inbound spread.\n\nIf wished, select the checkbox \"Allow Spread less than Market\". A fixed spread of \"0\" means that the inbound spread is used.\n\n**Add to Inbound Spread** adds the defined percentage on the inbound spread.\n\n**Add Pips** adds the specified number of pips to the inbound spread.\n\nIndependent of the chosen spread mode, the outbound spread can always be limited by a minimum and a maximum spread in terms of pips.\n\n# <span id=\"page-20-0\"></span>**3.10Manual Skew**\n\nManual Skew can be set both in percentage of inbound spread, and in absolute numbers specified in PIPS. Both options can be used individually but also in combination. Applying manual skew is a combination of **basic instrument wide skew settings**, and \"**Skew Factors**\", which distribute the selected base value to each pricing tier.\n\nBasic skew settings can be adjusted in the \"Instrument Control\" panel in columns \"Skew PIPS\" and \"Skew %\".\n\n| łe            |              | <b>Skew PIPS</b> |          |                           | Skew % | <b>Sce</b> |                 |                |  |\n|---------------|--------------|------------------|----------|---------------------------|--------|------------|-----------------|----------------|--|\n| naged         | $\\checkmark$ |                  |          |                           |        |            |                 | n <sub>c</sub> |  |\n| naged         | $\\checkmark$ |                  | 4        | $\\boxplus \\boxtimes$<br>÷ |        | 40         | $\\Theta$        | n              |  |\n|               |              |                  | $\\bf{0}$ | $+$ $+$ $\\circ$           |        | 10         | $+$ $+$ $\\circ$ |                |  |\n| B             | $\\checkmark$ |                  | $\\bf{0}$ | $ \\oplus \\otimes$<br>÷    |        | 0          | $\\Theta$<br>÷   | nc             |  |\n| B             | $\\checkmark$ |                  | 0        | $\\Theta$<br>÷             |        | o          | $\\Theta$<br>÷   | nc             |  |\n| B             | $\\checkmark$ |                  | $\\bf o$  | $ \\bigoplus$<br>٠         |        | $\\bf{o}$   | $\\Theta$        | te             |  |\n| <b>Inaged</b> | $\\vee$       |                  | $\\bf{0}$ | H(X)<br>٠                 |        | 0          | $+ x$           | nc             |  |\n\n<span id=\"page-20-1\"></span>Figure 18: Basic skew settings\n\nClicking on the inner `+` and `-` buttons increments and decrements PIPS in steps of 0.1, and percentage in steps of 1. Clicking on the outer `+` and `-` buttons buttons increments and decrements PIPS in steps of 1, and percentage in steps of 10.\n\n#### Please note: Any changes in these values is immediately effective.\n\nThe selected values can be distributed to each pricing tier by \"Skew Factors\". A skew factor of 10% for the 1m tier means, only 10% of the skew value chosen in the pricing control panel will be applied to this tier. The maximum skew factor is 100% which means, whatever skew values are chosen in the pricing control panel, will be fully applied to this tier.\n\nTo adjust skew factors for a specific instrument open the \"Instrument Configuration\" dialog, and chose option \"Pricing\":\n\n![](_page_20_Figure_15.jpeg)\n\n<span id=\"page-20-2\"></span>Figure 19: Tier specific skew factors\n\nIn order to allow skewing to cross the mid-rate or even the opposite side, user MUST enable \"Allow Skew to cross Mid Price\" and \"Allow Skew to cross Opposite Side\" options, respectively.\n\n![](_page_21_Figure_3.jpeg)\n\n<span id=\"page-21-1\"></span>Figure 20 Allowing Skew to cross Mid Price/ Opposite Side\n\nClick on **Apply** to confirm and apply the defined rule changes.\n\n# <span id=\"page-21-0\"></span>**3.11 Rounding Outbound Quotes**\n\nFor each pricing channel of a managed instrument, users can configure the decimal places for outbound quote. By default, the quotes are published with the spot rate precision of the respective currency.\n\nFrom Pricing panel under Instrument Configuration, user can reduce the outbound spot rate precision.\n\n| Instrument Configuration EUR/GBP |                                   |                                                                                                                                                                                           |                     |              |     |                                                                    |                                                     |         |          |  |\n|----------------------------------|-----------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|--------------|-----|--------------------------------------------------------------------|-----------------------------------------------------|---------|----------|--|\n| Pricing                          |                                   |                                                                                                                                                                                           | Spread              |              |     |                                                                    |                                                     |         | Slippage |  |\n| > Core Channel 1                 | Tier                              | <b>KO</b><br>Min                                                                                                                                                                          | <b>Fixed (PIPS)</b> | VO)          | Max | <b>Skew Factor</b>                                                 |                                                     | Percent |          |  |\n| Core Channel 2                   | 1,000,000                         |                                                                                                                                                                                           | 8                   |              | 18  | 0%                                                                 |                                                     |         | 17       |  |\n| <b>Reference Price Finding</b>   | 5,000,000                         |                                                                                                                                                                                           |                     | ÷            |     | 0 %                                                                |                                                     |         | 100      |  |\n| <b>Risk Management</b>           | 10,000,000                        |                                                                                                                                                                                           | ÷                   | $\\pm$        | Ð   | 0 %                                                                |                                                     |         |          |  |\n|                                  | 15,000,000                        |                                                                                                                                                                                           |                     |              |     | 0%                                                                 |                                                     |         |          |  |\n|                                  | 25,000,000                        |                                                                                                                                                                                           |                     |              | ÷   | 0%                                                                 |                                                     |         |          |  |\n|                                  | vo<br>$\\circ$<br>KO)<br><b>VO</b> | Round outbound quotes to decimals:<br>Allow spread less than Market<br>Allow skew to cross Mid Price<br>Allow skew to cross Opposite Side<br>Allow slippage Worse than Client Order Price |                     | - 9<br>$4 -$ |     | Reference stream:<br>Percent tolerance:<br>Non executable streams: | 360TEDF.F<br>360TBANK.TEST 360TEDF.FEED 360TEXEC F/ |         |          |  |\n\n<span id=\"page-21-2\"></span>Figure 21: Reducing outbound spot rate precision\n\nFor example, if `Round Outbound Quotes to 4 decimal places` is configured for EURUSD pricing, the outbound quotes of 1,11342 – 1,11347 would be rounded to 1,11340 – 1.11350.\n\n# <span id=\"page-22-0\"></span>**3.12Reference Stream and Percent Tolerance**\n\nInstrument owner can define a reference stream per instrument on channel level to filter out certain inbound quotes by using the reference stream`s quote as benchmark. Together with defined percent tolerance, MMC will calculate a price range that can be used for price construction and will consider any quote outside this as off-market and therefore filter those quotes out.\n\n| $\\sqrt{2}$<br>Instrument Configuration EUR/USD |                         |                                    |                 |     |                                          |                         |              |          |    |\n|------------------------------------------------|-------------------------|------------------------------------|-----------------|-----|------------------------------------------|-------------------------|--------------|----------|----|\n| $\\vee$ Pricing                                 |                         |                                    | Spread          |     |                                          |                         |              | Slippage |    |\n| > Core Channel 1                               | Tier                    | Min                                | <b>Add Pips</b> |     | Max                                      | <b>Skew Factor</b>      |              | Percent  |    |\n| Core Channel 2                                 | 1,000,000               |                                    |                 |     |                                          | 100 %                   |              |          |    |\n| <b>Reference Price Finding</b>                 | 5,000,000               |                                    |                 |     | v.                                       | 100 %                   |              |          |    |\n| <b>Risk Management</b>                         | 15.000.000              | $+$                                |                 | $+$ | --                                       | $0\\%$ (                 |              |          | 62 |\n|                                                | 25,000,000              | - 1                                |                 |     |                                          | 0%                      |              |          |    |\n|                                                |                         |                                    |                 |     |                                          |                         |              |          |    |\n|                                                | $\\mathcal{U}(\\cdot)$    | Round outbound quotes to decimals: |                 |     | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Reference stream:       | 360TEDF.FEED |          |    |\n|                                                |                         | Allow spread less than Market      |                 |     |                                          | Percent tolerance:      | 0.02         |          |    |\n|                                                | $\\overline{\\mathsf{v}}$ | Allow skew to cross Mid Price      |                 |     |                                          | Non executable streams: | ₹∕           |          |    |\n\n<span id=\"page-22-1\"></span>Figure 22Reference Stream and Percent Tolerance\n\nFor example, as in *Figure 21*, user defined Reference Stream as 360T`s Essential Data Feed and tolerance level as 0.02%.\n\nEURUSD bid/ask from 360T EDF is 1.13250 / 1.13260.\n\nThe valid inbound bid/ask is calculated with below formula:\n\n*(1-x%)\\*ReferenceBid <= Valid Inbound Bid <= (1+x%)\\*ReferenceBid*\n\n*(1-x%)\\*ReferenceAsk<= Valid Inbound Ask <= (1+x%)\\*ReferenceAsk*\n\n*Where x is the configured Percent Tolerance.*\n\nUsing 360T EDF as reference, valid bid and ask range is calculated as below:\n\n- (1- 0.0002)\\*1.13250 = 1.13227 <= Valid Inbound Bid <= (1+0.0002)\\*1.13260 = 1.13273\n- (1- 0.0002)\\*1.13260 = 1.13237 <= Valid Inbound Ask <= (1+0.0002)\\*1.13260 = 1.13283\n\n| Provider | Bid     | Provider | Ask     |\n|----------|---------|----------|---------|\n| E        | 1.13285 | B        | 1.13225 |\n| A        | 1.13270 | C        | 1.13272 |\n| C        | 1.13265 | A        | 1.13280 |\n| D        | 1.13220 | D        | 1.13282 |\n| B        | 1.13210 | E        | 1.13290 |\n\nBelow is list of bid/ask quotes from different providers.\n\nAs per Reference Stream and tolerance definition, for bid 1.13285, 1.13270 and 1.13210; for ask 1.13225 and 1.13290 will be not used for any price construction.\n\n## <span id=\"page-23-0\"></span>**3.13Non-Executable Streams**\n\nWhen creating a reference price, it is possible to use streaming prices that are not executable. As an example, 360T`s Essential Data Feed can be used as a source to create EURUSD outbound prices but when it comes to managing the risk, 360T EDF is not a source where MMC owner can hedge back onto.\n\nIn order to use the non-executable streams in price construction while excluding them in hedging, instrument owner can configure these streams as non-executable within Pricing panel under Instrument Configuration dialog.\n\n| Instrument Configuration EUR/USD |            |                                       |                 |                                          |                                                      |                         |                               |          | X |\n|----------------------------------|------------|---------------------------------------|-----------------|------------------------------------------|------------------------------------------------------|-------------------------|-------------------------------|----------|---|\n|                                  |            |                                       |                 |                                          |                                                      |                         |                               |          |   |\n| $\\vee$ Pricing                   |            |                                       | Spread          |                                          |                                                      |                         |                               | Slippage |   |\n| Core Channel 1                   | Tier       | Min                                   | <b>Add Pips</b> |                                          | Max                                                  | <b>Skew Factor</b>      |                               | Percent  |   |\n| Core Channel 2                   | 1,000,000  | ÷<br>$\\sim$                           | -               | $\\pm 1$                                  | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\\equiv$ | 100 %                   |                               | -        |   |\n| <b>Reference Price Finding</b>   | 5,000,000  |                                       |                 |                                          |                                                      | 100 %                   |                               |          |   |\n| <b>Risk Management</b>           | 15,000,000 | ÷                                     |                 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | $\\pm$                                                | 0%                      |                               |          |   |\n|                                  | 25,000,000 |                                       |                 |                                          |                                                      | 0 %                     |                               |          |   |\n|                                  |            |                                       |                 |                                          |                                                      |                         |                               |          |   |\n|                                  |            | VO Round outbound quotes to decimals: |                 | $4 +$                                    |                                                      | Reference stream:       | 360TEDF.FEED                  |          |   |\n|                                  |            | Allow spread less than Market         |                 |                                          |                                                      | Percent tolerance:      | $0.02 -$<br>$+$               |          |   |\n|                                  | M.         | Allow skew to cross Mid Price         |                 |                                          |                                                      | Non executable streams: | 360TEDF.FEED 360TEXEC.TEST =/ |          |   |\n|                                  |            |                                       | $-1$            |                                          |                                                      |                         |                               |          |   |\n\n<span id=\"page-23-1\"></span>Figure 23 Configure Non-Executable Streams\n\nClicking on icon will open a pop-up window where all streaming providers appear and can be chosen. After selecting the desired stream as non-executable and clicking on save from the first channel, **the configuration will be immediately effective for all stream channels.**\n\n**Please note that, non-executable streams can only work in combination with Managed or Flow Hedge risk management modes. When risk management is selected as B2B mode, defining a non-executable stream effectively means removing that stream from price construction too, since MMC has to match the inbound with an outbound price to attempt hedge trade before accepting the client order.**\n\n| 360TEDF.FEED<br>$\\mathcal{S}$ | 360TEXEC.TEST<br>▽⊂ |\n|-------------------------------|---------------------|\n| ALBARAKA.FEED                 | BOAL.DEMO           |\n| Barclays BARX.DEMO            | COBA.DEMO           |\n| PEBANK_APAC.TEST              | RBS.LND.DEMO        |\n|                               |                     |\n|                               |                     |\n\n<span id=\"page-23-2\"></span>Figure 24 Non-executable Stream Selection\n\n# <span id=\"page-24-0\"></span>**3.14Sweepable and Full Amount Streams**\n\nEach pricing channel can be configured to generate either sweepable or full amount/exclusive pricing. Full amount streams allow the market makers to stream prices which can be executed exclusively by one maker i.e. the client full order amount will be executed by a single liquidity provider while sweepable streams allows the maker to fill a portion of an order that might be executed by multiple providers.\n\nThis is mostly relevant to market makers streaming prices to their clients executing spot on 360T Supersonic. All RFQ requests are by default priced as full amount.\n\nThis can be done by your admin users who have access to `Stream Group Mapping` within the Business Configuration Tool.\n\n# <span id=\"page-24-1\"></span>**3.15Managing Synthetic Crosses**\n\nTo define rules how to strip cross currency pairs, open the 'Global Instrument Configuration Dialog' and select option \"Cross Rules\":\n\n![](_page_24_Figure_8.jpeg)\n\n<span id=\"page-24-2\"></span>Figure 25: Cross Currency Pair Configuration\n\nEach row in this dialog defines a rule how to handle a specific cross currency pair. New rules can be added by clicking on the \"+\" button in the center. The button can be used to delete a rule.\n\nEach rule defines:\n\n- **Active**: if ticked, the crossing for the defined currency is active.\n- **Base Ccy and Quote Ccy**: defines the currency or currency pair which should be cross-calculated using managed currency pairs. One of the two currencies can be defined with a wildcard \"\\*\"\n- **Cross:** defines the managed currency pair used to cross over either base or quote currency.\n- **Use Notional Ccy**: if checked, the notional is specified by the quote currency quantity. For example, for EURTRY 1m request, when use notional ccy selected, cross legs would be 1m EUR of EURUSD and 1m EUR equivalent USD of USDTRY.\n\nWhen unchecked, USDTRY cross would be executed in TRY notional (TRY amount = opposite amount of EURTRY).\n\n• **Arrow buttons** can be used to move a rule up and down.\n\n#### **Note:**\n\nRules will be matched from top down. Therefore wildcard rules shall be rather placed at the end of the list!\n\n#### **Important Note:**\n\nThe order of base currency and quote currency while setting cross rules is important. MMC does not support any inconventional currency pair setting. For example, if rule is set as USD/EUR and not as EUR/USD, the rule won`t be matched with incoming request and hence ignored.\n\nPlease contact CAS team to receive latest currency ranking to set the cross rules with right order of base/quote currency.\n\n# <span id=\"page-25-0\"></span>**3.16Pricing of Unmanaged Instruments**\n\nBy default the MMC will only price managed instruments. Users can configure the MMC to price unmanaged instruments too. To do so, open the \"Global Instrument Configuration\" dialog, and select option \"General Parameters\":\n\n| <b>Global Instrument Configuration</b> |                                                                   | $\\times$                       |  |\n|----------------------------------------|-------------------------------------------------------------------|--------------------------------|--|\n| > General Parameters                   |                                                                   |                                |  |\n|                                        | Allow time slippage for Managed/Flow Hedged instruments (ms):     |                                |  |\n| Instruments                            | Allow time slippage for B2B instruments (ms):<br>0.6 <sub>1</sub> |                                |  |\n| Tiers                                  | <b>Price Unmanaged instruments</b>                                |                                |  |\n| Scenarios                              | VO Price Unmanaged Crosses                                        |                                |  |\n| <b>Cross Rules</b>                     |                                                                   |                                |  |\n| <b>Client Order Handling</b>           |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   |                                |  |\n|                                        |                                                                   | <b>Discard</b><br><b>Apply</b> |  |\n\n<span id=\"page-25-1\"></span>Figure 26: Pricing unmanaged instruments\n\nIf option \"Price Unmanaged Instruments\" is enabled, then MMC will provide prices, and execute client orders, for any instrument where liquidity is available. Please note that, if the requested instrument is configured in Cross Rules, then it will get priced as per cross rule.\n\nIf option \"Price Unmanaged Cross Currency\" is enabled, then MMC will provide prices, and execute client orders, for any synthetic cross rate where one of the two legs is not managed by the MMC.\n\nExample:\n\n• EUR/USD is a managed instrument\n\n• User configured a rule for synthetic cross rates for EUR/\\* cross over to EUR/USD\n\nIf option \"Price Unmanaged Cross Currency\" is enabled together with \"Price Unmanaged Instruments\", the MMC will provide prices, and execute client orders, for any instrument with base currency EUR.\n\n### **Note:**\n\nTo price unmanaged instruments, the entire bank basket will be used.\n\n# <span id=\"page-26-0\"></span>**3.17Configuring Time Slippage**\n\nBy applying time slippage, the MMC has more time (if required) to fill a hedge order, which should improve the overall hedge order fill ratio. Time slippage is off by default. To configure time slippage, open the \"Global Instrument Configuration\" dialog, and select option \"General Parameters\":\n\n| Global Instrument Configuration |                                                                               | X     |\n|---------------------------------|-------------------------------------------------------------------------------|-------|\n| > General Parameters            | V Allow time slippage for Managed/Flow Hedged instruments (ms):<br>0.9<br>$+$ |       |\n| Instruments                     | Allow time slippage for B2B instruments (ms):<br>$1.1 +$<br>VO)               |       |\n| Tiers                           | <b>Price Unmanaged instruments</b>                                            |       |\n| Scenarios                       | <b>Price Unmanaged Crosses</b>                                                |       |\n| <b>Cross Rules</b>              |                                                                               |       |\n| <b>Client Order Handling</b>    |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 |                                                                               |       |\n|                                 | <b>Discard</b>                                                                | Apply |\n\n<span id=\"page-26-2\"></span>Figure 27: Time slippage configuration\n\nTime slippage can be adjusted separately for B2B and flow hedged/managed risk managed mode in steps of 1/10th of a second. Time slippage will only be applied in case the hedge order cannot be filled immediately.\n\n# <span id=\"page-26-1\"></span>**3.18Configuring Price Slippage**\n\nPrice slippage occurs, when a client order limit price is worse (from a trader perspective) than the currently published MMC outbound price. This problem typically occurs in fast moving markets, and/or with clients having a high latency between their end and the MMC pricing server.\n\nThe following image shows an example for slippage due to a widening market:\n\n- 1. Inbound price spread increased between time T and T+1\n- 2. Outbound bid and offer follow inbound bid and offer from time T to T+1\n- 3. Client hits at time T+1 the old quote from time T\n- 4. The MMC margin at time T+1 is in this case reduced by price slippage\n\n![](_page_27_Figure_2.jpeg)\n\n<span id=\"page-27-0\"></span>Figure 28: Example for price slippage\n\nWithout accepting price slippage there are two possible results in this situation:\n\n- 1. In case of B2B risk management mode, the client order will be most likely rejected, because no suitable quote to hedge the order can be found\n- 2. In case of flow hedge/managed modes, the MMC will simply reject the client order to fully protect the MMC margin\n\nBy accepting price slippage, traders can give clients (if required) a discount on their trader margin, to improve client order fill ratio, and reduce rejections.\n\nPrice slippage applies differently for B2B and flow hedge/managed risk management modes. In case of B2B mode, price slippage affects the B2B hedge order limit price. By pro-actively worsening the limit price, a hedge order has a higher chance of getting filled in the market. Price slippage will only be applied when necessary. The MMC will always try to execute a hedge order at the best available price (automatic price improvement).\n\nIn case of flow hedge and managed modes, price slippage controls if a client order will be accepted or rejected. It acts in this case as a price and profit protection. The price of any client order hitting the MMC will be compared to the current outbound price (in the example above the price at time T+1). Client orders will only be accepted, if their limit price is within the acceptable slippage as defined by the trader.\n\nTo configure price slippage for a specific instrument, open the \"Instrument Configuration\" dialog, and select option \"Pricing\". Price slippage can be applied for each pricing tier in column \"Slippage\". The options are either in percent of current margin, or in absolute PIPS.\n\n| Instrument Configuration EUR/USD |            |                                                                                                                                                                                           |                         |       |                                                                    |                                                           |                         |                       |\n|----------------------------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|-------|--------------------------------------------------------------------|-----------------------------------------------------------|-------------------------|-----------------------|\n| $\\vee$ Pricing                   |            |                                                                                                                                                                                           | Spread                  |       |                                                                    |                                                           | Slippage                |                       |\n| > Core Channel 1                 | Tier       | Min                                                                                                                                                                                       | <b>Add Pips</b>         | Max   | <b>Skew Factor</b>                                                 |                                                           | Percent                 | $\\boldsymbol{\\wedge}$ |\n| Core Channel 2                   | 1,000,000  |                                                                                                                                                                                           |                         | Ð     | 29 %                                                               |                                                           | <b>Pips</b>             |                       |\n| <b>Reference Price Finding</b>   | 5,000,000  | $\\pm$                                                                                                                                                                                     | $\\overline{2}$          | Ð     | 42%                                                                |                                                           | $\\sqrt{}$ Percent       |                       |\n| <b>Risk Management</b>           | 15,000,000 | $\\ddot{}$                                                                                                                                                                                 | $\\overline{\\mathbf{3}}$ | ÷     | 60%                                                                |                                                           | 50                      |                       |\n|                                  | 25,000,000 | ÷                                                                                                                                                                                         |                         | Ð     | 75%                                                                |                                                           |                         |                       |\n|                                  | VO<br>VC   | Round outbound quotes to decimals:<br>Allow spread less than Market<br>Allow skew to cross Mid Price<br>Allow skew to cross Opposite Side<br>Allow slippage Worse than Client Order Price |                         | $4 +$ | Reference stream:<br>Percent tolerance:<br>Non executable streams: | 360TEDF.FEED<br>$0.02 +$<br>360TEDF.FEED 360TEXEC.TEST =/ |                         |                       |\n|                                  |            |                                                                                                                                                                                           |                         |       |                                                                    |                                                           | <b>Discard</b><br>Apply |                       |\n\n<span id=\"page-28-1\"></span>Figure 29: Price slippage configuration\n\nBy configuring slippage in PIPS, there is always the risk that the actual margin is less than the allowed price slippage. The MMC will automatically limit slippage at the current margin, to prevent traders from accidentally accepting client orders at a loss. This safety measure can be disabled by ticking option \"Allow Slippage Worse than Client Order Price\".\n\nConfiguring price slippage in percent means, a certain percentage of the current margin can be given up to accept client order. If for example the current margin (difference between inbound and outbound price) is 2 PIPS, and the trader configures 50%, he would accept a price slippage of up to 1 PIP. Configuring a price slippage of 100% means, if necessary the trader is willing to give up the entire margin to fill or accept the client order.\n\nBy default, price slippage is set to zero, and slippage worse than client order price is not enabled. If margin is set to zero and \"Allow Slippage Worse than Client Order Price\" option is not enabled, then the slippage defined in practice is redundant.\n\n## <span id=\"page-28-0\"></span>**3.19Quote Filtering**\n\nProblems can arise if the MMC calculates an outbound price based on unreliable inbound quotes such as stale quotes. A quote can be considered as stale if it wasn't updated after the market moved. A B2B hedge order based on a stale quote will often get rejected.\n\nThe problem can become more severe with flow hedging and managed positions. In these modes the MMC will accept any client order within an acceptable price slippage range. A subsequent auto-hedge order might result into a financial loss!\n\nAnother issue with invalid quotes is revaluation of open positions. A user might set a stop loss rule for an open position. If such a rule is triggered by an invalid quote the position might be closed at an unfavourable price (**because the MMC uses market orders to close positions).**\n\nQuote filters can help to reduce such risks!\n\nTo enable quote filtering for a specific instrument open the \"Instrument Configuration\" dialog, select Risk Management/General Parameters and enable `Remove quotes older than ms)` option\n\n| Pricing                        | <b>General Parameters</b>      |                                                                     |  |  |  |  |  |\n|--------------------------------|--------------------------------|---------------------------------------------------------------------|--|--|--|--|--|\n| <b>Reference Price Finding</b> | Maximum Hedge Order Size:      | 10,000,000.00<br>$\\begin{pmatrix} - & 1,000 \\end{pmatrix}$<br>$\\pm$ |  |  |  |  |  |\n| $\\vee$ Risk Management         | Remove quotes older than (ms): |                                                                     |  |  |  |  |  |\n| > General Parameters           |                                |                                                                     |  |  |  |  |  |\n| <b>Position Rules</b>          |                                |                                                                     |  |  |  |  |  |\n| <b>Pricing Rules</b>           |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n|                                |                                |                                                                     |  |  |  |  |  |\n\n<span id=\"page-29-0\"></span>Figure 30: Quote filter settings\n\nIn example above any EUR/USD quote older than 1,000ms will be removed from the inbound stream. Removed quotes will be added back into the book at the next quote update.\n\nUsers can monitor the effect of the quote filter settings in the instrument \"Pricing Details\" dialog. To open this dialog press on the \"Details…\" link in the Liquidity Details panel. Removed quotes will be displayed in the \"Raw Inbound\" view as strikethrough prices. See [4.3](#page-32-0) for more information about the Pricing Details.\n\n| Pricing Details // Core Channel 1@EUR/USD |  |  |  |  |\n|-------------------------------------------|--|--|--|--|\n|-------------------------------------------|--|--|--|--|\n\n| > Raw Inbound    | Snapshot: Wed, 16 Nov 2022 16:44:50 UTC |                  |                |         |         |                 |                  |  |  |\n|------------------|-----------------------------------------|------------------|----------------|---------|---------|-----------------|------------------|--|--|\n| Filtered Inbound |                                         |                  |                |         |         |                 |                  |  |  |\n| Outbound         |                                         | <b>Bid</b>       |                |         | Ask     |                 |                  |  |  |\n|                  |                                         | Provider         | Quantity       | Price   | Price   | Quantity        | Provider         |  |  |\n|                  | 1 <sub>m</sub>                          | <b>COBA.DEMO</b> | 4m             | 1.03914 | 4.03962 | 4m              | BOAL.DEMO        |  |  |\n|                  |                                         | BOAL.DEMO        | $\\pm m$        | 4.03910 | 4.03966 | $+m$            | COBA DEMO        |  |  |\n|                  |                                         | 360TEDF.FEED     | 1 <sub>m</sub> | 1.03907 | 1.03969 | 1 <sub>m</sub>  | 360TEDF.FEED     |  |  |\n|                  |                                         | 360TEDF.FEED     | <b>500k</b>    | 1.03907 | 1.03969 | <b>500k</b>     | 360TEDF.FEED     |  |  |\n|                  |                                         | <b>COBA DEMO</b> | 2m             | 4.03836 | 1.04044 | 2m              | <b>COBA DEMO</b> |  |  |\n|                  |                                         | 360TEDF.FEED     | 5m             | 1.03801 | 1.04075 | 5m              | 360TEDF.FEED     |  |  |\n|                  |                                         | COBA.DEMO        | 4m             | 4.03784 | 4.04096 | 4 <sub>ff</sub> | COBA DEMO        |  |  |\n|                  |                                         | <b>COBA DEMO</b> | 8m             | 1.03732 | 1.04148 | 8m              | <b>COBA DEMO</b> |  |  |\n|                  |                                         | COBA DEMO        | 16m            | 1.03602 | 1.04278 | 46m             | <b>COBA DEMO</b> |  |  |\n|                  |                                         | <b>Bid</b>       |                |         | Ask     |                 |                  |  |  |\n|                  |                                         | Provider         | Quantity       | Price   | Price   | Quantity        | Provider         |  |  |\n|                  | 5 <sub>m</sub>                          | COBA DEMO        | 4m             | 1.03914 | 4.03962 | $4m$            | BOAL, DEMO       |  |  |\n|                  |                                         | BOAL.DEMO        | $+m$           | 4.03910 | 4.03966 | $+m$            | COBA DEMO        |  |  |\n|                  |                                         | 360TEDF.FEED     | 1 <sub>m</sub> | 1.03907 | 1.03969 | 1 <sub>m</sub>  | 360TEDF.FEED     |  |  |\n|                  |                                         | 360TEDF.FEED     | <b>500k</b>    | 1.03907 | 1.03969 | <b>500k</b>     | 360TEDF.FEED     |  |  |\n|                  |                                         | COBA DEMO        | 2m             | 4.03836 | 4.04044 | 2m              | <b>COBA DEMO</b> |  |  |\n|                  |                                         | 360TEDF.FEED     | 5m             | 1.03801 | 1.04075 | 5m              | 360TEDF.FEED     |  |  |\n\n<span id=\"page-29-1\"></span>Figure 31: Monitor filtered quotes\n\n# <span id=\"page-30-0\"></span>**3.20PTMM**\n\nFor all requests priced by the MMC, a PTMM (Pre-Trade Mid-Market) rate is published across all FX derivative products and available to the client at the time of quoting as well as post execution.\n\nThe PTMM is a non-skewed midrate driven of the market maker liquidity. It is calculated based of the MMC spot \"Filtered Inbound\" prices (of the respective Channel associated to the client) and the mid-price of the forward points for the requested settlement date.\n\nWhen the RFQ is quoting, the PTMM is recalculated once a new quote is updated and is also captured at the time of execution.\n\n# <span id=\"page-31-0\"></span>**4 MONITOR PRICING**\n\n# <span id=\"page-31-1\"></span>**4.1 The Liquidity Details Panel**\n\nThe Pricing Monitor tab provides various information for each managed instrument and pricing channel:\n\n- Pricing tier size\n- Inbound price details\n- Outbound price details\n\nEach row contains information for a specific instrument.\n\n![](_page_31_Figure_9.jpeg)\n\n<span id=\"page-31-3\"></span>Figure 32: Liquidity Details Panel\n\nThe following details are shown:\n\n- **Tier**: Shows the tier size.\n- **Inbound details:** Displays inbound price and spread details for the currently selected channel.\n- **Outbound details:** Displays outbound price, spread, and skew (marked with an arrow) details for the currently selected channel.\n\n# <span id=\"page-31-2\"></span>**4.2 Inbound and Outbound Pricing Tier Monitor**\n\nThe panel shows for each channel and tier detailed inbound and outbound price information:\n\n![](_page_31_Figure_17.jpeg)\n\n<span id=\"page-31-4\"></span>Figure 33: Pricing Tier monitor\n\nFor each currency pair multiple pricing channels can be configured. Each pricing channel can have different spread/skew settings and Reference Price Finding rules.\n\nThe assignment of your requesting clients to the different pricing channel prices has to be done by 360T support.\n\nThe pricing tier monitor shows one row per pricing tier (e.g. 1m, 5m, …).\n\n![](_page_32_Picture_5.jpeg)\n\nEach row shows the following information:\n\n- Tier size (e.g. 1m)\n- Big Figure\n- PIPS bid and ask\n- Spread blue bar\n- Skew orange number shows size and orange arrow shows direction\n\n# <span id=\"page-32-0\"></span>**4.3 Pricing Details Dialog**\n\nClick on the icon to get a snapshot of current raw, inbound and outbound prices including all details.\n\nA user can select one of the following option in the navigation tree on the left side:\n\n- Raw Inbound to see all available quotes from all liquidity and market data providers in the bank basket.\n- Filtered Inbound quotes selected by Reference Price Finding\n- Outbound outbound rates for each pricing tier\n\nTo see all available quotes from all liquidity and market data providers in bank basket click on \"Raw Inbound\". Bid quotes are shown on the left, ask quotes are shown on the right. Bank basket can be configured via 360T`s Bank Basket administration panels. For further detail, please contact CAS or your Sales representative.\n\nIn \"Raw Inbound\" panel, bid quotes are sorted in descending order by price, with the best bid rate at the top. Ask quotes are sorted in ascending order by price, with the lowest ask rate at the top. If the strategy for reference price finding is set to `Best Price` or VWAP Average, MMC can use the full amount streams as raw inbound too. In this case, full amount inbound quotes are indicated with an asterisk next to tier size.\n\nAt the top of the panel a user can select a specific pricing tier size. Quotes are additionally marked in various colours, depending on the chosen Reference Price Finding settings. Quotes from selected providers are shown in black, all other quotes are shown in grey.\n\nThose quotes selected by Reference Price Finding for the actual outbound price are additionally marked in yellow.\n\n| red Inbound |\n|-------------|\n|             |\n\n|                | --------------------------------------- |                 |         |         |                 |                  |\n|----------------|-----------------------------------------|-----------------|---------|---------|-----------------|------------------|\n|                | <b>Bid</b>                              |                 |         | Ask     |                 |                  |\n|                | Provider                                | Quantity        | Price   | Price   | Quantity        | Provider         |\n| 1 <sub>m</sub> | COBA.DEMO                               | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | COBA.DEMO        |\n|                | <b>BOAL.DEMO</b>                        | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | <b>BOAL.DEMO</b> |\n|                | 360TEDF.FEED                            | 1 <sub>m</sub>  | 1.03885 | 1.03983 | 1 <sub>m</sub>  | 360TEDF.FEED     |\n|                | 360TEDF.FEED                            | <b>500k</b>     | 1.03885 | 1.03983 | <b>500k</b>     | 360TEDF.FEED     |\n|                | COBA.DEMO                               | 2m              | 1.03830 | 1.04038 | 2m              | COBA.DEMO        |\n|                | 360TEDF.FEED                            | 5m              | 1.03789 | 1.04079 | 5m              | 360TEDF.FEED     |\n|                | COBA.DEMO                               | 4 <sub>m</sub>  | 1.03778 | 1.04090 | 4 <sub>m</sub>  | COBA.DEMO        |\n|                | COBA.DEMO                               | 8m              | 1.03726 | 1.04142 | 8m              | COBA.DEMO        |\n|                | COBA.DEMO                               | 16 <sub>m</sub> | 1.03596 | 1.04272 | 16 <sub>m</sub> | COBA.DEMO        |\n|                | <b>Bid</b>                              |                 |         | Ask     |                 |                  |\n|                | Provider                                | Quantity        | Price   | Price   | Quantity        | Provider         |\n| 5m             | COBA.DEMO                               | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | COBA.DEMO        |\n|                | BOAL.DEMO                               | 1 <sub>m</sub>  | 1.03908 | 1.03960 | 1 <sub>m</sub>  | BOAL.DEMO        |\n|                | 360TEDF.FEED                            | 1 <sub>m</sub>  | 1.03885 | 1.03983 | 1 <sub>m</sub>  | 360TEDF.FEED     |\n|                | 360TEDF.FEED                            | <b>500k</b>     | 1.03885 | 1.03983 | <b>500k</b>     | 360TEDF.FEED     |\n|                | COBA.DEMO                               | 2m              | 1.03830 | 1.04038 | 2m              | COBA.DEMO        |\n|                | 360TEDF.FEED                            | 5m              | 1.03789 | 1.04079 | 5m              | 360TEDF.FEED     |\n\n<span id=\"page-33-0\"></span>Figure 34: Raw Inbound Quote Details\n\nClick on option \"Filtered inbound\" to see the actual quotes chosen by Reference Price Finding. The panel shows for each tier the selected quotes separated by bid and ask.\n\n![](_page_33_Figure_9.jpeg)\n\n<span id=\"page-33-1\"></span>Figure 35: Filtered Inbound Quote Details\n\nClick on option \"Outbound\" to see outbound price details by tier:\n\n| <b>Contract Contract</b><br><b>STATE</b><br><b>Contractor</b><br>Raw Inbound<br>Snapshot: Wed, 16 Nov 2022 17:08:48 UTC<br><b>Filtered Inbound</b> |      |                          |         |         |                           |         |         |                         |                |\n|----------------------------------------------------------------------------------------------------------------------------------------------------|------|--------------------------|---------|---------|---------------------------|---------|---------|-------------------------|----------------|\n| $\\gt$ Outbound                                                                                                                                     | Tier | Inbound<br>(Bid/Mid/Ask) |         |         | Outbound<br>(Bid/Mid/Ask) |         |         | Spread<br>(Manual/Rule) |                |\n|                                                                                                                                                    | 1m   | 1.03927                  | 1.03960 | 1.03993 | 1.03935                   | 1.03968 | 1.04001 | 6.6                     | 0.0            |\n|                                                                                                                                                    | 5m   | 03861                    | 1.03960 | 104059  | 1.03861                   | 103960  | 104059  | 10.8                    | 0 <sup>n</sup> |\n\n<span id=\"page-34-0\"></span>Figure 36: Outbound Price Details\n\nThe panel shows for each pricing tier:\n\n- Tier size\n- Inbound quotes\n- Outbound quotes\n- Manual spread due to configuration\n- Automatic spread due to pricing rules\n- Manual skew due to configuration\n- Automatic skew due to pricing rules\n- Skew cut because inbound price reached configured limits\n\n#### **Skew cut:**\n\nExample: bid / ask / mid: 0.9330 / 0.9331 / 0.93305\n\nUser sets a manual skew of +1 PIP\n\nIn this case the bid rate would cross the mid-rate with 0.5 PIPS\n\nIf bid or ask price aren't allowed to cross the mid-rate (the option \"Allow Skewing to cross Mid\" is not ticked), the price is cut off at the mid-price (in this case by 0.5 PIPS).\n\n# <span id=\"page-35-0\"></span>**5 RISK MANAGEMENT**\n\nRisk Management enables the user to set the notional amount of risk he is prepared or allowed to hold on his own book as well as rules for the Auto Dealer once defined levels are reached.\n\n# <span id=\"page-35-1\"></span>**5.1 Monitoring Positions**\n\nThe Instrument Position blotter shows currency pair positions for all managed currency pairs.\n\n| <b>Instrument Positions</b> |              |              |               |           |             |                 |             |               |                |                                                                                          |\n|-----------------------------|--------------|--------------|---------------|-----------|-------------|-----------------|-------------|---------------|----------------|------------------------------------------------------------------------------------------|\n| Symbol                      | Updated      | Size CCY1    | Size CCY2     | Open PL   | Realized PL | <b>Total PL</b> | Reeval Rate | Avg Buy Price | Avg Sell Price |                                                                                          |\n| EUR/GBP                     | 07:59:00.273 |              | 0.00          | 0.00      | 0.00        | 0.00            | 0.87271     |               |                | $\\mathbb{R} \\times \\mathbb{R} \\times \\mathbb{R} \\times \\mathbb{R} \\times \\mathbb{R}$     |\n| EUR/USD                     | 07:59:00.302 |              | 0.00          | 0.00      | 0.00        | 0.00            | 1.03481     |               |                | $\\triangleright \\rightarrow \\triangleright \\triangleright \\bigcirc = \\bigcirc \\setminus$ |\n| GBP/USD                     | 11:07:47.930 | 1,000,000.00 | $-1.186,100$  | $-632.72$ | 0.00        | $-632.72$       | 1.18535     | 1,18610       |                | $\\mathbb{R} \\rightarrow \\mathbb{R} \\rightarrow \\mathbb{R}$                               |\n| MXN/JPY                     | 07:59:00.337 |              | 0.00          | 0.00      | 0.00        | 0.00            | 7.19474     |               |                | $V \\rightarrow \\vert \\mathcal{L} \\rangle = \\langle \\mathcal{R} \\vert$                    |\n| USD/TRY                     | 11:07:47.931 | 1,186,100.13 | $-22,079,930$ | $-163.77$ | 0.00        | $-163.77$       | 18,61300    | 18.61557      |                | $\\forall \\exists \\emptyset$ = $\\textcircled{x}$                                          |\n| <b>XAU/USD</b>              | 07:59:00.292 |              | 0.00          | 0.00      | 0.00        | 0.00            | 1,764.216   | $\\circ$       |                | $\\forall \\exists \\forall \\Theta = \\textcircled{x}$                                       |\n\n<span id=\"page-35-2\"></span>Figure 37: Managed Positions blotter with context menu\n\nThe following columns are available:\n\n- Position size in CCY1 and CCY2\n- Time of last update\n- Open P/L (calculated in terms of company currency)\n- Realized P/L (calculated in terms of company currency)\n- Total P/L (calculated in terms of company currency)\n- Revaluation rate (rate which was used to calculate open P/L)\n- Average buy and sell price\n\nBy default, blotter tracks the position due to client orders and hedge orders booked via MMC as well as any manual position update done via Trader on the application and/or automated position updates triggered via MMC Position Upload API.\n\nPlease note that, Instrument Position Blotter can be defined to capture all trades done within 360T regardless whether they are manually booked on other 360T trading applications or autopriced/hedged via other pricing sources. MMC Admins will then still have the option to filter in or out certain type of trades that are not done via MMC from the Global Instrument Configuration panel by defining the respective trading parameters. Details of filtering will be explained in Filtering Position section.\n\nThe upgraded position blotter is also able to split the cross position as per defined cross rules into cross legs using MMC`s revaluation rate at the time of booking.\n\nPlease get in touch with 360T CAS if you would like to be rolled out for the upgraded version.\n\n### **Note:**\n\nBy default, positions will be reset at the server side with every new trading day. The exact time is when the value date is rolled. Thus the trade history shows at most the requester order and hedge trades from the current day.\n\nFor users who want to keep their position overnight, there is a global configuration which can be done via 360T CAS.\n\nThe trade history will be wiped/erased with **every manual position reset**!\n\nTrade history for each position is shown in the lower table. Select a specific position in the upper table to see the trade history in the lower table. The table shows both client and hedge orders. For hedge orders additionally all executions are shown as children.\n\n#### **Context menu:**\n\nRight clicking into the blotter data opens the context menu.\n\n![](_page_36_Picture_4.jpeg)\n\nFigure 38 Context menu for managing open position\n\n<span id=\"page-36-2\"></span>For instruments currently owned by the user this dialog offers position blotter specific options:\n\n- Amend Position: Select to amend the position for a specific quantity and price\n- Set Position: Select to set the position to a specific size and price\n- Reset Position: Reset the position to zero and erase the order and trade history\n- Flatten Position: Select to **execute a hedge order in the market** to close the position\n- Cancel Open Orders: Cancel all open hedge orders for the specified currency pair.\n\n# <span id=\"page-36-0\"></span>**5.2 Profit and Loss (P/L) Calculations**\n\nOpen positions are periodically (every 500ms) revaluated mark-to-market with the best available inbound price to calculate **open P/L**. Long positions are revaluated with best bid price, short positions are revaluated with best ask price.\n\nP/L is calculated by one of these two methods:\n\n- **Average cost method**: This method calculates P/L by taking the difference of how much was spent to build the position, and how much was earned by closing the position\n- **First-In, First-out method (FIFO):** This method assumes that every SELL trade covers the oldest open BUY trade, and vice versa\n\nThe chosen P/L calculation method is a system setting and affects all users. By default the \"average cost\" method is used. **To configure your preferred P/L calculation method please contact 360T CAS.**\n\n# <span id=\"page-36-1\"></span>**5.3 Position Filtering Configuration**\n\nIt is possible for MMC users to opt in to include trades done outside MMC into their currency pair position by reaching out to CAS for enablement. After this is enabled, MMC admins will see a menu called Position Filtering inside Global Instrument Configuration dialog.\n\n<span id=\"page-37-1\"></span>Figure 39 Position Filtering\n\nThe configuration consists of 5 filters (Dealer, Legal Entity, Fx Time Period, Product, Execution Method), two operations (include only or exclude) as well as a dual list which shows available and selected values for the filters.\n\nMMC admins can determine what needs to be included or excluded in position. For example if any non-spot trade to be excluded, admin can use product filter where only Spot is selected together with include operation.\n\nPlease also note that any changes done in this configuration requires the roll of the positions which is at NY 5 PM unless you opted in to roll the positions to next day. In that case, changes will take affect with weekend maintenance of 360T platform.\n\n## <span id=\"page-37-0\"></span>**5.4 Risk Management Configuration**\n\nRisk management configuration contains three sub-section: General Parameters, Position Rules and Pricing Rules.\n\n| Pricing                        | <b>General Parameters</b>      |               |  |\n|--------------------------------|--------------------------------|---------------|--|\n| <b>Reference Price Finding</b> | Maximum Hedge Order Size:      | 20,000,000.00 |  |\n| $\\vee$ Risk Management         | Remove quotes older than (ms): | $-5,100 +$    |  |\n| > General Parameters           |                                |               |  |\n| <b>Position Rules</b>          |                                |               |  |\n| <b>Pricing Rules</b>           |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n|                                |                                |               |  |\n\n<span id=\"page-38-0\"></span>Figure 40: Risk management configuration\n\nThe MMC has built in certain so called \"Safeguards\" to limit the risk of eventual losses in case of technical problems. Certain safeguard parameters can be adjusted by users.\n\n#### **Maximum hedge order size:**\n\nThe maximum hedge order size limits the order quantity of auto-hedge orders. The default value is 10 million in base currency. Users can adjust this value individually for each managed instrument. The upper limit for this parameter is 50 million.\n\nTo adjust the maximum hedge order size or a specific managed instrument, open the instrument configuration dialog, and select option \"General Parameters\":\n\nIndependent of position size and auto-hedging rules, no auto-hedge order will be larger than this value.\n\nExample:\n\n- The maximum hedge order size is set to 10 million\n- A user defines a position rule to flatten the position if it is larger than 20 million\n- The position reaches 22 million\n\nThe MMC will create a first hedge order over 10 million. Assuming the hedge order got fully filled, the position will go down to 12 million. At the next position review, the position rule will trigger again, and a second hedge order over 10 million will be created.\n\nNote:\n\nThis parameter only applies to instruments with risk management mode \"Managed\". This parameter has no effect on back-to-back or flow hedge orders.\n\n#### **Minimum hedge order size:**\n\n**The minimum hedge order size is an auto hedge order safety mechanism which prevents any auto hedge order to be placed below certain amount in order to avoid a larger number of small hedge order attempts. The size is set to 100.000 in base**  **currency by default. Please contact CAS in case you would like to amend the minimum hedge order size.**\n\n# <span id=\"page-39-0\"></span>**5.5 Position Rules**\n\nInstrument owners can manage their currency risks and define some action depending on certain triggers by creating position rules. Position rules can be based on one of the following options (triggers) for the selected position:\n\n- **Position:** The absolute position size (long and short positions are treated equally)\n- **Total Loss:** The negative total P/L\n- **Open Profit:** The positive open P/L\n- **Open Loss:** The negative open P/L\n- **Open Profit (PIPS):** Positive open P/L as a price difference to average position price\n- **Open Loss (PIPS):** Negative open P/L as a price difference to average position price\n\nThe selected trigger is compared to a specified value with either greater (>), or greaterequals (>=).\n\nAs soon as a rule is triggered, the specified action will be executed. The available **actions** are:\n\n- **Back2Back:** The client order will only be accepted after it was successfully hedged (last look)\n- **Alert:** Raises an alert to the user when the trigger level is reached\n- **Reduce by:** Reduces the position by the specified amount\n- **Reduce by %:** Reduces the position by the specified percentage of its current size\n- **Reduce to:** Reduces the position to the specified size\n- **Flow hedge:** Hedge the client order immediately (no last look)\n- **Flatten:** Close the position\n- **Switch to Back2Back:** Switch the risk management mode to B2B for this instrument\n- **Switch to Flow Hedge**  switch the risk management mode to flow hedging for this instrument\n- **Place relative to TOB**: Places a pegged order to 360T`s Central Order Book (COB). (Note: Only available for 360T ECN participants).\n- **Stop Pricing for instrument**: Stop publishing outbound prices for the instrument when the trigger is reached.\n\n![](_page_40_Picture_2.jpeg)\n\n![](_page_40_Picture_3.jpeg)\n\n<span id=\"page-40-0\"></span>Figure 41 Position Rules\n\nBack-to-Back (B2B) means that the requester order will only be accepted after it was successfully hedged (last look). A B2B rule which checks a certain position size is triggered when the current position amount, plus the notional of the requester order, would breach the specified limit.\n\nPosition rules are checked and possibly triggered:\n\n- Once with each client order\n- Periodically All rules are evaluated every 500ms\n\nWhen a position size rule is triggered, **it will emit a hedge order** to the market based on the specified rule action.\n\nThe periodic rules review ensures that positions are regularly reviewed even if no requester order is imminent. This is also a retry mechanism for failed or partially filled hedge orders. Instead of re-attempting the same failed hedge order, the system will simply compare positions against rules at the next periodic review.\n\n### **Note:**\n\n**Even though rules are reviewed every 500ms, for safety reasons the system will wait (by default) at least 3 seconds between subsequent hedge orders for the same currency pair.** \n\n#### **Example flatten position:**\n\nA rule action could be Flatten. When this rule is triggered, a hedge order will be created to close the entire position in the market. If this hedge order fails or is only partially filled, another hedge order with the remaining position size will be created **latest after 3 seconds.** The hedge order will be created earlier, if there is a requester order for the same currency pair before the 3 seconds expired.\n\n#### **The auto-hedger will always make sure that:**\n\n• **Positions are not over hedged (flips sides because of a hedge order)**\n\n### • **Position never increase because of a hedge order**\n\n#### **Flow Hedging:**\n\nAs the name suggests, flow hedging means to hedge the flow of requester order one-to-one. Every incoming requester order will be first executed and the position will be updated accordingly. When the updated position breaches a defined limit, and the specified action is Flow Hedge a reverse hedge order of the same size than the requester order will be emitted to the market.\n\nThe goal of flow hedging is to accept all requester orders, without letting positions grow (no guarantee).\n\n#### **Rule based risk management mode switching:**\n\nActions \"Switch to Back2Back\" and \"Switch to Flow hedge\" can be used to automatically switch the risk management mode based on certain criteria. An example is to switch to B2B mode when the total loss for a specific instrument breached a threshold, to avoid any further losses.\n\n#### **Note:**\n\nSwitching the risk management mode back to \"Managed\" is a manual operation to be performed in the Pricing Control tab!\n\n#### **Note:**\n\n- \"Reduce by\" shall be used with care. Every requester order is a single trigger. A position could grow very quickly if the specified \"reduce by\"- quantity is small compared to the requester order size!\n- Consider to use \"Reduce by\" and \"Reduce by %\" with a position size trigger. A P/L based rule can be triggered for any position size, and it is difficult to predict the position size when the rule fires.\n- Flow hedging is an option to avoid requester order rejections, and avoid position growth, but because flow hedging only **attempts** to hedge **after** the requester order was accepted, there is always the possibility that position still grows quickly!\n\n# **It is advisable to always specify a Back2Back rule to limit the maximum position size!**\n\n**Without such a rule, positions can potentially increase unreasonably result in a loss in short time!**\n\n## <span id=\"page-41-0\"></span>**5.6 Pricing Rules**\n\nPricing Rules define how to modify spreads and skew based on position size or P/L.\n\n| Instrument Configuration EUR/USD |           |                                   |                             |                      |                               |                     |                          | $\\times$                                                                                                 |\n|----------------------------------|-----------|-----------------------------------|-----------------------------|----------------------|-------------------------------|---------------------|--------------------------|----------------------------------------------------------------------------------------------------------|\n| Pricing                          | Enabled   | <b>Trigger</b>                    | Operator                    | <b>Trigger Level</b> | Action                        | <b>Action Value</b> |                          |                                                                                                          |\n| <b>Reference Price Finding</b>   | $\\bullet$ | Position<br>$\\blacktriangledown$  | $\\rightarrow$ $\\rightarrow$ | $5,000,000 +$        | Skew%<br>$\\blacktriangledown$ | Œ<br>$10 +$         | $\\hat{\\wedge}$           | û<br>$\\forall$                                                                                           |\n| $\\vee$ Risk Management           | $\\bullet$ | Position<br>$\\checkmark$          | $\\rightarrow$ $\\sim$        | 7,000,000<br>u za    | Skew%<br>$\\checkmark$         | $15 +$              | $\\hat{\\frown}$           | $\\vee$                                                                                                   |\n| <b>General Parameters</b>        | S         | <b>Total Loss</b><br>$\\checkmark$ | $\\Rightarrow = \\sqrt{ }$    | 10,000<br>Ð          | $\\checkmark$<br>Spread%       | $10 +$              | $\\hat{\\curvearrowright}$ | $\\begin{array}{c} \\widehat{\\boxdot} \\\\ \\widehat{\\boxdot} \\end{array}$<br>$\\mathrel{\\mathop{\\mathbb{V}}}$ |\n| <b>Position Rules</b>            |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n| > Pricing Rules                  |           |                                   | $\\pm$                       |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               | <b>Discard</b>      |                          | <b>Apply</b>                                                                                             |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n|                                  |           |                                   |                             |                      |                               |                     |                          |                                                                                                          |\n\n<span id=\"page-42-1\"></span>Figure 42: Risk management pricing rules\n\nSpread and skew due to such rules is added on top of spread and skew configured in the Pricing Controller tab.\n\n# <span id=\"page-42-0\"></span>**5.7 Alert Rules**\n\nWithin position rules, a user can create Alert rules to receive a notification when a position size or P/L reaches a certain limit.\n\nWhen an Alert level is reached, the user receives a pop up notification **and** an acoustic signal.\n\n![](_page_42_Picture_8.jpeg)\n\n<span id=\"page-42-2\"></span>![](_page_42_Figure_9.jpeg)\n\n#### **Note:**\n\nWhen you acknowledge an Alert, the respective rule will be disabled. **You have to manage the position and thereafter tick the rule again and apply it!**\n\n# <span id=\"page-43-0\"></span>**5.8 Manual Position Amendments**\n\nPositions can be manually amended for each managed currency pair. To do so, you can right click on the position to open the context menu or you can use the icons in the instrument position table.\n\n![](_page_43_Picture_4.jpeg)\n\nFigure 44 Context menu of Managed Positions\n\n<span id=\"page-43-1\"></span>\n\n| <b>Instrument Positions</b> |              |              |              |              |                    |                 |                                                                            |\n|-----------------------------|--------------|--------------|--------------|--------------|--------------------|-----------------|----------------------------------------------------------------------------|\n| Symbol                      | Updated      | Size CCY1    | Size CCY2    | Open PL      | <b>Realized PL</b> | <b>Total PL</b> |                                                                            |\n| EUR/GBP                     | 14:11:40.092 | 0.00         | 41,400       | 0.00         | 47,196.70          |                 | 47,196.70 $\\cancel{v}$ $\\rightarrow$ $\\Diamond$ $=$ $\\otimes$              |\n| EUR/USD                     | 15:50:32.405 | 8,000,000.00 | $-8,266,970$ | $-10,928.86$ | 1,307.97           |                 | $-9,620.89$ $\\Rightarrow$ $\\Rightarrow$ $\\Rightarrow$ $\\Rightarrow$<br>⊗ I |\n\n<span id=\"page-43-2\"></span>Figure 45 Taskbar for manual position update\n\n|  | Figure 46 Amend Position |  |\n|--|--------------------------|--|\n\n<span id=\"page-43-3\"></span>**Amend** a position ( ): Enter a quantity and price and click the \"OK\" button. This will add the specified quantity to the position and impact the P/L based on the captured price. To reduce the position, enter a negative quantity.\n\n![](_page_43_Figure_10.jpeg)\n\n<span id=\"page-43-4\"></span>Figure 47 Set Position\n\n**Set** a position ( ): Enter a quantity and price and click the \"OK\" button. This will set the position to the specified position size and impact the P/L based on the captured price.\n\n**Reset** a position ( ): Choose the Reset command and the entire position will be erased and reset to zero. A message is displayed in order to request a confirmation for the reset before it is executed.\n\n![](_page_44_Picture_6.jpeg)\n\n<span id=\"page-44-0\"></span>Figure 48 Confirmation of Position Reset\n\nNone of the above actions will create \"real\" deals with the market!\n\n**Flatten** a position : Choose the Flatten command to create a hedge order to close the position. This action will create real deals with external market makers.\n\n| <b>Flatten Position</b>                                            |               |\n|--------------------------------------------------------------------|---------------|\n| Please confirm that you want to flatten your position for EUR/GBP. |               |\n|                                                                    |               |\n|                                                                    |               |\n|                                                                    |               |\n| Cancel                                                             | Confirm<br>J. |\n\n<span id=\"page-44-1\"></span>Figure 49 Confirmation of Position Flattening\n\nFlatten will attempt to execute your Notional position out in the market back to zero, you will be asked to confirm this action and if it is unable to bring it to zero, you will receive a notification.\n\n# <span id=\"page-45-0\"></span>**5.9 Restrict the bank basket for hedge orders**\n\nBy default, when the MMC places hedge orders, quotes of the entire client's bank basket are considered. Users can restrict the bank basket for B2B and flow hedge orders to make sure hedge orders are only sent to selected providers.\n\nTo restrict the hedge order bank basket for a specific instrument, open the \"Instrument Configuration\" dialog, and select option \"Reference Price Finding\".\n\n| <b>Best Price</b><br><b>KO</b><br><b>KO</b><br>1,000,000<br>360TBANK.TEST 360TEDF =><br>Hedge<br>1,000,000<br><b>SO</b><br>360TBANK.TEST 360TEDF =/<br><b>VWAP Average</b><br>5,000,000<br>Hedge<br>5,000,000<br>Core Channel 2<br><b>SO</b><br>8,000,000<br><b>Best Price</b><br>8,000,000<br>360TBANK.TEST 360TEDF = /<br>Hedge<br><b>SO</b><br><b>Best Price</b><br>BOAL.DEMO Barclays BAR<br>Hedge<br>15,000,000<br>15,000,000<br>$\\bar{z}$ /<br><b>Best Price</b><br>$\\vert 0 \\rangle$<br>Hedge<br>25,000,000<br>₩ | Pricing                        | Tier | Providers | Hedge | <b>Strategy</b> | Min Quote Size/Bid | Min Providers/Ask |\n|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|------|-----------|-------|-----------------|--------------------|-------------------|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | $\\vee$ Reference Price Finding |      |           |       |                 |                    | $\\mathbf{1}$      |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | > Core Channel 1               |      |           |       |                 |                    | 3 <sup>°</sup>    |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | <b>Risk Management</b>         |      |           |       |                 |                    | $\\mathbf{0}$      |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    | $\\bullet$         |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                |      |           |       |                 |                    |                   |\n\n<span id=\"page-45-2\"></span>Figure 50: Restrict hedge order bank basket\n\nTo restrict the hedge order bank basket for a specific pricing tier, tick the checkbox in column \"Hedge\". Any client order hitting this pricing tier, will be hedged with the selected providers for the same tier.\n\n# <span id=\"page-45-1\"></span>**5.10Client Order Handling Rules**\n\nClient Order Handling rules allow users to setup rules to override the instrument's risk management mode for individual client orders. Users can setup an arbitrary number of such client order handling rules. The configuration can be instrument specific but also across multiple or all instruments using wildcards.\n\nExamples for client order handling rules:\n\n- All client orders for EUR/USD up to 1m quantity shall be managed on the position, but all orders larger than 1m shall be flow hedged.\n- Pricing for instrument GBP/USD is configured for up to 10m with risk management mode \"Flow Hedging\". All client orders larger than 5m from counterparty \"ABC Ltd.\" shall be hedged back-to-back.\n\nA client order handling rule consists of client order matching parameters, and an action. The action defines what to do with the client order if the rule matches. If more than one rule is defined, only the first matching rule will be executed!\n\nParameters to match client orders are:\n\n• **Instrument** … either exactly (e.g. EUR/USD) or defined with wildcards (e.g. EUR/\\*)\n\n- **Order quantity** and comparison operator (bigger, bigger equals, equals, smaller equals, smaller)\n- **Counterparty** and comparison operator to match exactly (e.g. equals \"ABC Ltd.\") but also pattern matching (all counterparties starting with \"Bank\").\n\nPossible actions are:\n\n- Hedge B2B\n- Flow Hedge\n- Reject (to reject a client order)\n\nA client order handling rule can **override** the current risk management mode of an instrument, but only **towards less risk**! As an example, if the current risk management mode is \"Flow Hedge\", a client order processing rule can only force order handling with \"B2B\" or \"Reject\" but not \"Managed\".\n\nThe following table provides an overview of possible actions for each instrument risk management mode:\n\n| Instr. risk management mode | Managed | Flow Hedge | B2B | Reject |\n|-----------------------------|---------|------------|-----|--------|\n| Managed                     | Yes     | Yes        | Yes | Yes    |\n| Flow Hedge                  | No      | Yes        | Yes | Yes    |\n| B2B                         | No      | No         | Yes | Yes    |\n\nClient order handling rules where the action is identical with the current risk management mode will be matched, but have no effect!\n\nTo configure client order handling rules, open the \"Global Instrument Configuration\" dialog and select option \"Client Order Handling\". Click on the \"+\" button to add new rules:\n\n![](_page_46_Figure_13.jpeg)\n\n<span id=\"page-46-0\"></span>Figure 51: Manage Client Order Handling Rules\n\nRules are always evaluated for a match starting at the top of the list towards the bottom. The first matching rule will be executed, all remaining rules will be ignored.\n\n### Explanation for the examples above:\n\nRule 1: Any client order bigger or equals 2m from a counterparty ending with \"Fund\" will be rejected\n\nRule 2: Any client order with terms currency USD from \"Abc Corp\" will be hedged B2B\n\nRule 3: Any EUR/USD client order with quantity bigger than 3m will be flow hedged\n\nRule 4: Any client order with quantity bigger or equals 10m will be hedged B2B\n\nIt is important to order rules in the right way! More restrictive rules should be placed before more generic rules. An example are rules 2 and 3. If rule 3 would be placed before rule 2, orders from \"Abc Corp\" bigger than 3m would be flow hedged! Assuming a user wants to hedge any order \\*/USD from \"Abc Corp\" B2B, it is important to make sure that a more generic rule doesn't override it.\n\n#### **Counterparty comparison operators:**\n\nThe system offers a number of comparison operators to match counterparties in various ways:\n\n| Operator    | Explanation                                                   |\n|-------------|---------------------------------------------------------------|\n| Exactly as  | Compares for an exact match of the specified counterparty     |\n| All except  | Matches all counterparties except the specified one           |\n| Contains    | Matches any counterparty which contains the specified text    |\n| Starts With | Matches any counterparty which starts with the specified text |\n| Ends With   | Matches any counterparty which ends with the specified text   |\n| All like    | Complex pattern search using Java regular expressions         |\n\n#### **Counterparty pattern matching:**\n\nThe provided standard counterparty comparison operators should be sufficient for most cases to match either a specific counterparty, or a group of counterparties. Operator \"All like\" provides an alternative in cases where the standard operators don't help. This operator uses so called Java regular expressions to match counterparties. Such regular expressions provide extreme flexibility but can be eventually complex to define.\n\nThe general syntax for Java regular expression can be found in the appendix chapter [1.](#page-52-2) Further information about Java regular expressions can be found in the internet in the official Java documentation (https://docs.oracle.com/javase/tutorial/essential/regex/).\n\n# <span id=\"page-47-0\"></span>**5.11Pricing and Risk Management Scenarios**\n\nPricing and Risk management scenarios (or short scenarios) allow users to quickly adjust pricing and risk management settings with one mouse click. Users can define an arbitrary number of such scenarios.\n\nEach scenario defines:\n\n- Name\n- Spread factor\n- Maximum quote size\n- Risk management mode\n\nOnly the name is mandatory, all other parameters are optional.\n\nIf set, parameters influence pricing and risk management in various ways. The current outbound spread will be widened by the selected spread factor. A spread factor of e.g. 2, will widen the outbound spread by 100%. Maximum quote size can be used to limit the available liquidity for your requesters. If e.g. 5m maximum quote size is chosen, any pricing tier above 5m will be disabled. The selected risk management mode of the scenario will override the current instrument's risk management mode, but only towards less risk. If e.g. the current instruments risk management mode is \"B2B\" and the scenario defines \"Flow Hedge\", the parameter will be ignored.\n\nScenarios are defined globally and can be applied to any managed instrument. Scenarios override current settings only temporarily as long as the scenario is applied.\n\nTo add a new scenario open the \"Global Instrument Configuration\" dialog and select option \"Scenarios\":\n\n| Global Instrument Configuration                                                 |                              |                                                                    |                                       |                                                                                                  | $\\times^1$                                     |\n|---------------------------------------------------------------------------------|------------------------------|--------------------------------------------------------------------|---------------------------------------|--------------------------------------------------------------------------------------------------|------------------------------------------------|\n| <b>General Parameters</b><br>Instruments<br><b>Tiers</b>                        | Name<br>Moderate<br>Volatile | <b>Spread Factor</b><br>$1.5 +$<br>$\\rightarrow$<br>2 <sup>2</sup> | Max Quote Size<br>Ð<br>5,000,000<br>Ð | <b>Risk Management Mode</b><br>$\\checkmark$<br>Managed<br>$\\overline{\\vee}$<br><b>Flow Hedge</b> | $\\widehat{\\boxplus}$<br>$\\widehat{\\mathbb{U}}$ |\n| > Scenarios                                                                     | Unattended                   | $1 - 1$                                                            | 3,000,000<br><b>Report</b>            | $\\overline{\\vee}$<br>B2B                                                                         | $\\widehat{\\mathbb{U}}$                         |\n| <b>Cross Rules</b><br><b>Client Order Handling</b><br><b>Position Filtering</b> |                              | ╫                                                                  |                                       |                                                                                                  |                                                |\n\n<span id=\"page-48-0\"></span>Figure 52: Pricing and Risk Management Scenarios\n\nScenarios can be applied to instruments in the \"Pricing Control\" panel.\n\n| Market Maker Cockpit      | <b>Audit Log</b> |                   |                                           |                                   |                   |\n|---------------------------|------------------|-------------------|-------------------------------------------|-----------------------------------|-------------------|\n| <b>Instrument Control</b> |                  |                   |                                           |                                   |                   |\n| Instrument                | Managed By       | Mode              | Skew PIPS                                 | Skew %                            | Scenario          |\n| <b>All Instruments</b>    |                  | <b>Managed</b>    |                                           |                                   | none<br>$\\sim$    |\n| EUR/GBP                   | 360TMMC.Trader1  | <b>Flow Hedge</b> | 0 <sup>o</sup><br>$\\bullet$ $-$<br>$-4.1$ | $50 + \\bigoplus \\infty$<br>$\\Box$ | none              |\n| EUR/USD                   | 360TMMCTrader1   | <b>Managed</b>    | $\\Theta$<br>$\\blacksquare$                | $+ 0.0$<br>n<br>ei                | moderate          |\n| GBP/USD                   | 360TMMC.Trader2  | Managed           | 00                                        | $+0.0$<br>61<br><b>CONTENT</b>    | volatile          |\n| MXN/JPY                   | 360TMMC.Trader1  | <b>Managed</b>    | $\\Theta$<br>$\\bullet$ $-$                 | $+ 0.0$<br>$\\Box$<br><b>D</b>     | $\\checkmark$ none |\n| USD/TRY                   | 360TMMC.Trader1  | Managed           | 0<br>- 62<br>n                            | $+ 0.0$<br>ei                     | <b>THORNE</b>     |\n| XAU/USD                   | 360TMMC.Trader1  | <b>Managed</b>    | $\\Theta$<br>a Cir<br><b>n</b>             | $+100$<br>$\\bullet$<br><b>n</b>   | none              |\n\n<span id=\"page-48-1\"></span>Figure 53: Scenario selection\n\nThe current scenario can be changed for all instruments simultaneously by selecting a scenario in the \"All Instruments\" row.\n\n# <span id=\"page-49-0\"></span>**6 BLOTTERS**\n\nThe MMC offers various blotters to monitor orders, trade flow, and positions. The blotter currently available are:\n\n- Currency Pair Position Blotter\n- Client Order Blotter\n- Hedge Order Blotter\n- Combined Client and Hedge Order Blotter\n- Client Activity Blotter\n- Trade Blotter\n\n# <span id=\"page-49-1\"></span>**6.1 General Blotter Features**\n\nAll blotters offer the same general set of features which are:\n\n- Choose, move, and resize columns\n- Sorting by one or more columns\n\nColumns can be resized simply by moving the boundary between two columns left or right. The columns order can be changed via drag and drop.\n\nTo move a column, click with the left mouse button on the column header and hold it. Move the column to the next target location and release the mouse button.\n\nBlotter data can be sorted by one or more columns, in ascending or descending order, by clicking one or more time on the according column header. To sort by more than one column, press and hold the CTRL key on your keyboard, and click on the specific column headers. A small triangle in the column header will show the sort direction. The small number next to the triangle indicates the sort order if more than one sort column is selected.\n\n# <span id=\"page-49-2\"></span>**6.2 Client Order Blotter**\n\nThe client order blotter shows every client order received by the pricing engine. For each client order the following information is available:\n\n• Order ID\n\n.\n\n- Time of arrival\n- Currency pair\n- Status (e.g. EXECUTED, REJECTED, ..)\n- Action (BUY or SELL)\n- Order limit price\n- Notional amount and currency\n- Executed amount and rate\n- Type (originating system like RFS or SEP)\n- Trigger (originating organization and user)\n- Realized By ID filled for B2B orders\n- Requester\n\nThe blotter is pre-filled at startup with orders from the current trading day.\n\n#### **Note:**\n\n© 360 Treasury Systems AG - 50 - Version 4.17\n\n- Order action (buy or sell) is shown from the perspective of the requester (a buy order means, the requester bought and the ADS MMC sold)!\n- In case of a cross rate strip order, the blotter will show three requester orders. The actual cross rate order, and two additional orders for each leg.\n\n# <span id=\"page-50-0\"></span>**6.3 Hedge Order Blotter**\n\nThe hedge order blotter shows an entry for each hedge order created for the current trading day. For each hedge order the following information is available:\n\n- Hedge Order ID\n- Time of creation\n- Currency Pair\n- Status\n- Action (BUY or SELL)\n- Order limit price\n- Order notional amount and quantity\n- Executed amount and for which average price\n- Trigger indication why the hedge order was created\n\nThe blotter is pre-filled at startup with orders from the current trading day.\n\n## <span id=\"page-50-1\"></span>**6.4 Combined Client and Hedge Order Blotter**\n\nBlotter with title \"All orders\" combines client and hedge orders into a single blotter. The two different order types can be easily separated by the type column.\n\nThe available columns are the same as for the individual requester and hedge order blotters.\n\n# <span id=\"page-50-2\"></span>**6.5 Client Activity Blotter**\n\nThe client activity blotter shows one entry for every status of a each requested quote:\n\n| <b>Hedge Orders Glient Activity</b><br><b>Client Orders</b><br>All Orders |                           |            |                |                                                               |  |  |  |  |\n|---------------------------------------------------------------------------|---------------------------|------------|----------------|---------------------------------------------------------------|--|--|--|--|\n|                                                                           | $\\wedge$ Time(UTC)        | Type       | <b>Status</b>  | Description                                                   |  |  |  |  |\n| 168333345-360T.MMC                                                        | 17 Nov. 2022 16:09:53.910 | <b>RFS</b> | QUOTED         | The RFS '168333345-360T.MMC received its first guote '1.0304  |  |  |  |  |\n| 168333345-360T.MMC                                                        | 17 Nov. 2022 16:09:53.825 | RFS        | <b>STARTED</b> | The RFS ' EUR/USD SPOT 5000000.00 EUR' was started for '360T  |  |  |  |  |\n| 168333345-360T.MMC                                                        | 17 Nov. 2022 16:09:56.591 | <b>RFS</b> | CANCELED       | The RFS '168333345-360T.MMC was canceled                      |  |  |  |  |\n| 168320542-360TMMC                                                         | 17 Nov. 2022 14:58:27.135 | RFS        | <b>STARTED</b> | The RFS ' EUR/USD SPOT 50000000.00 EUR' was started for '360  |  |  |  |  |\n| 168320542-360T.MMC                                                        | 17 Nov. 2022 14:58:32.061 | <b>RFS</b> | CANCELED       | The RFS '168320542-360T.MMC was canceled                      |  |  |  |  |\n| 168312124-360T.MMC                                                        | 17 Nov. 2022 13:53:49.551 | RFS        | <b>STARTED</b> | The RFS 'Sell USD/BRL SPOT 5000000.00 USD' was started for '3 |  |  |  |  |\n| 168312124-360T.MMC                                                        | 17 Nov. 2022 13:53:52.877 | <b>RFS</b> | CANCELED       | The RFS '168312124-360T.MMC was canceled                      |  |  |  |  |\n\n<span id=\"page-50-3\"></span>Figure 54: Client Activity Blotter\n\nStatus = Started shows time of quote request while the request details are shown in Description.\n\nStatus=Quoted shows when first quote by MMC is provided to the client`s request. The details of the first quote is shown in Description.\n\nStatus = Canceled shows that the request is either cancelled by the requester or rejected by MMC. In case it is a rejection, details are shown in Description.\n\nStatus = Executed indicates that the request is executed. In the details, user can see the client order ID and request ID.\n\n# <span id=\"page-51-0\"></span>**7 AUDIT**\n\nAn audit trail can be accessed within the MMC to view the historical configuration adjustments made on input and output streams. It offers the \"before\" and \"after\" values for each configuration update. This is particularly helpful to understand which configuration settings were applied during a specific trade event.\n\n| <b>Market Maker Cockpit</b>                                                                                              | <b>Audit Log</b>         |                                    |                                |                         | ◙<br>∽   |  |  |\n|--------------------------------------------------------------------------------------------------------------------------|--------------------------|------------------------------------|--------------------------------|-------------------------|----------|--|--|\n|                                                                                                                          |                          |                                    |                                |                         |          |  |  |\n|                                                                                                                          | <b>Qv</b>                |                                    | 01.03.2022 - 16.03.2022        | <b>Search</b>           |          |  |  |\n| UTC Timestamp >= 01.03.2022<br>And<br>UTC Timestamp <= $16.03.2022$<br>And<br>Source = Instrument Configuration $\\times$ |                          |                                    |                                |                         |          |  |  |\n| Timestamp                                                                                                                | <b>Change Originator</b> | Source                             | Config                         | Event                   | Action   |  |  |\n| Mar 16, 2022 3:40:38 PM                                                                                                  | PFAPAC Trader1           | Instrument Configuration - EUR/GBP | <b>Instrument Positions</b>    | <b>Position Amended</b> | Modified |  |  |\n| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Hedge                   | Modified |  |  |\n| Mar 10, 2022 5:22:16 PM                                                                                                  | PFAPAC Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Hedge                   | Modified |  |  |\n| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | <b>Min Providers</b>    | Modified |  |  |\n| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Min Quote Size          | Modified |  |  |\n| Mar 10, 2022 5:22:16 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Strategy                | Modified |  |  |\n| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |\n| Mar 10, 2022 5:21:00 PM                                                                                                  | <b>PEAPACTrader1</b>     | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |\n| Mar 10, 2022 5:21:00 PM                                                                                                  | <b>PEAPAC Trader1</b>    | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |\n| Mar 10, 2022 5:21:00 PM                                                                                                  | <b>PFAPAC Trader1</b>    | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |\n| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | Reference Price Finding        | Provider                | Added    |  |  |\n| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Reference Price Finding</b> | Provider                | Added    |  |  |\n| Mar 10, 2022 5:21:00 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | Reference Price Finding        | Provider                | Added    |  |  |\n| Mar 10, 2022 5:20:41 PM                                                                                                  | PEAPAC.Trader1           | Instrument Configuration - EUR/GBP | <b>Instrument Control</b>      | <b>Pricing Started</b>  | Modified |  |  |\n\nThe audit trail is accessible in MMC via the tab \"Audit Log\".\n\n<span id=\"page-51-1\"></span>Figure 55: Accessing the Audit Log\n\nThe audit trail has a built-in search functionality. It offers the possibility to search event logs within a predefined time period as well as by any of the following parameters (matches the table names of the audit trail):\n\n- Change Originator: The user who adjusted the configuration\n- Source: Refers either to the global or instrument configuration settings\n- Config: Refers to the configuration category\n- Event: The configuration item which was adjusted\n- Action: Type of configuration event (Added, Modified, Removed, Enabled, Disabled, Started, Stopped)\n\n#### Example:\n\nIf modified configurations must be retrieved in the event logs, type \"Action=\" into the Search field. A drop-down popup will appear with the possible values.\n\n<span id=\"page-52-2\"></span>\n\n|         | $Q \\vee$ Action = | 11.11.2022 - 17.11.2022 | 雦<br>Search |\n|---------|-------------------|-------------------------|-------------|\n| Added   |                   |                         |             |\n|         | Modified          |                         |             |\n|         | Removed           | Event                   | Action      |\n| Started |                   |                         |             |\n|         | Stopped           |                         |             |\n|         | Enabled           |                         |             |\n|         | Disabled          |                         |             |\n\n<span id=\"page-52-1\"></span>Figure 56: Search by Action\n\nSelect \"Modified from the drop down field and press enter. The applied filter rule is then shown below the search field.\n\n# <span id=\"page-52-0\"></span>**8 CONTACT 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n#### **Germany**\n\n*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Middle East Asia Pacific**\n\n#### **United Arab Emirates**\n\n*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036\n\n### **EMEA Americas**\n\n### **USA**\n\n*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900\n\n**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #27-01 Republic Plaza Tower 1 Singapore 048619\n\nDubai Phone: +971 4 431 5134 Phone: +65 6325 9970 Fax: +65 6536 0662", "metadata": {"lang": "en"}}]