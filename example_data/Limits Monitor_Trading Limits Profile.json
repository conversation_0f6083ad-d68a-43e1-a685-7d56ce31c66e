[{"id": "1", "text": "# **USER GUIDE**\n\n![](_page_0_Picture_1.jpeg)\n\n# **LIMITS MONITOR**\n\n*FOR TRADING LIMITS*\n\n© 360 TREASURY SYSTEMS AG, 2022\n\nTHIS FILE CONTAINS PROPRIETARY AND CONFID<PERSON><PERSON><PERSON> INFORMATION\n\nINCLUDING <PERSON>RA<PERSON> SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR\n\n| 1 |              | INTRODUCTION3                                      |          |\n|---|--------------|----------------------------------------------------|----------|\n| 2 |              | GETTING STARTED5                                   |          |\n| 3 |              | DEFINING RISK PORTFOLIO GROUPS7                    |          |\n| 4 |              | DEFINING RISK PORTFOLIO RULES<br>9                 |          |\n|   | 4.1          | RULE ID                                            | 10       |\n|   | 4.2          | COUNTERPART                                        | 10       |\n|   | 4.3          | PRODUCT<br>                                        | 11       |\n|   | 4.4          | LEGAL ENTITY GROUP<br>                             | 11       |\n|   | 4.5          | DEALER                                             | 12       |\n|   | 4.6          | ALGORITHMS<br>                                     | 13       |\n|   | 4.6.1        | Daily Gross Trading Limit<br>                      | 13       |\n|   | 4.6.2        | Per Deal Limit                                     | 14       |\n|   | 4.7          | LIMIT<br>                                          | 14       |\n|   | 4.8          | EDIT RULE<br>                                      | 14       |\n|   | 4.9          | ACTIVATE/DEACTIVATE RULE<br>                       | 14       |\n|   | 4.10<br>4.11 | DELETE RULE<br><br>BULK UPLOAD/DOWNLOAD VIA CSV    | 15<br>15 |\n| 5 |              | ACTIVE RULES<br>18                                 |          |\n|   | 5.1          | APPLYING RULES AND LIMITS TO TRADES                | 20       |\n|   | 5.1.1        | Reallocating Trades After End of Day Rollover:<br> | 20       |\n|   | 5.2          | CALCULATING UTILIZATION<br>                        | 20       |\n|   | 5.2.1        | Daily Gross Trading Limit<br>                      | 23       |\n|   | 5.2.2        | Per Deal Limit                                     | 23       |\n|   | 5.3          | UTILIZATION RESET                                  | 24       |\n|   | 5.4          | VISUALIZATION<br>                                  | 24       |\n|   | 5.4.1        | Risk Entries                                       | 24       |\n|   | 5.5          | ALERT EMAILS                                       | 25       |\n|   | 5.6          | SNAPSHOT REPORTS<br>                               | 26       |\n| 6 |              | LIMIT CHECK FLOW27                                 |          |\n| 7 |              | AUDIT LOG<br>31                                    |          |\n| 8 |              | CONTACT 360T<br>32                                 |          |\n\n| Figure 1 Header Bar5                     |  |\n|------------------------------------------|--|\n| Figure 2 Bridge Administration Homepage5 |  |\n| Figure 3 Risk Portfolio Homepage6        |  |\n| Figure 4 HTML Login6                     |  |\n| Figure 5 HTML Access to Limits Monitor7  |  |\n| Figure 6 Risk Portfolio Groups<br>8      |  |\n\n| Figure 7 Counterpart Groups9                                                           |  |\n|----------------------------------------------------------------------------------------|--|\n| Figure 8 Risk Portfolio Rules<br>9                                                     |  |\n| Figure 9 Defining Rule ID<br>10                                                        |  |\n| Figure 10 Defining counterpart for risk portfolio rules<br>10                          |  |\n| Figure 11 Defining product for risk portfolio rules<br>11                              |  |\n| Figure 12 Defining legal entity for risk portfolio rules<br>12                         |  |\n| Figure 13 Defining dealer for risk portfolio rules<br>12                               |  |\n| Figure 14 Setting algorithm for risk portfolio rules13                                 |  |\n| Figure 15 Defining limit for risk portfolio rules14                                    |  |\n| Figure 16 Risk Portfolio Rules Upload/Download<br>15                                   |  |\n| Figure 17 Upload Result File<br>16                                                     |  |\n| Figure 18 CSV Column separator setting<br>16                                           |  |\n| Figure 19 Updating the limit for current trading date18                                |  |\n| Figure 20 Filtering the rules by using search function19                               |  |\n| Figure 21<br>Monitoring the updated utilization amounts within Active Rules tab.<br>21 |  |\n| Figure 22 Reset Utilization<br>24                                                      |  |\n| Figure 23 Risk Entries<br>24                                                           |  |\n| Figure 24 Alert emails<br>25                                                           |  |\n| Figure 25 Limit Breach Details in TWS28                                                |  |\n| Figure 26 Limit Breach Details in TWS for Orders<br>29                                 |  |\n| Figure 27 Audit Log31                                                                  |  |\n\n| Table 1 Field Definition for csv risk portfolio rule upload18                                |  |\n|----------------------------------------------------------------------------------------------|--|\n| Table 2: Field definition for csv active limit upload20                                      |  |\n| Table 3: Sample trades<br>22                                                                 |  |\n| Table 4: 360T EOD rates to convert Cashflow/Notional Ledgers into USD (credit<br>currency)22 |  |\n\n# <span id=\"page-2-0\"></span>**1 INTRODUCTION**\n\nThe purpose of 360T Limits Monitor is to provide 360T clients a rule-based and parametrized limit monitoring and pre-trade limit check system which ensures that the risks resulting from permissioned trading relationships do not exceed specified limits.\n\nLimits Monitor has two different profiles which differentiates the functions made available to the users on entity level:\n\n\"Trading Limits\" profile grants access to part of the configuration parameters such as product, counterpart, dealer as well as groups of these fields and daily gross trading as well as per deal limit types.\n\n\"Full Profile\" grants full access to the tool which grants access to the additional parameters such as currency pair, execution method and time period as well as daily and aggregate settlement limits both net and gross and Potential Future Exposure. Please contact [<EMAIL>](mailto:<EMAIL>) or your customer relationship manager if you are interested in getting onboarded with full access.\n\nThis user guide describes the functionalities provided for Trading Limits profile and how it can be administrated via Risk Portfolio in Bridge Administration panel.\n\nTrading limits can be defined for many parameters such as counterpart, legal entity, dealer and product type.\n\nIn order to set limits on specific segments of your trading portfolio that are important with respect to the types of risk you face, you can define Risk Portfolios based on individual or groups of related counterparties or internal accounts/entities, dealers and products. The criteria and steps to define these risk portfolios are described in detail in this user guide.\n\nDefined *risk portfolio*s can overlap and do not necessarily have to be mutually exclusive. The exposure resulted from the allocated trades is calculated based on the defined algorithm for that specific risk portfolio.\n\n360T`s new limit functionality currently captures all *FX Spot, FX Forward, FX Swap, NDF, NDS, Block FX Forward and Block NDF, FX Time Option* trades, negotiated as RFS, Streaming, MidMatch or Order and initiated through different 360T applications such as *Bridge* , *SST* or *EMS, as well as taker API interfaces.* \n\nThe allocation of trades to the risk portfolios is done based on the defined parameters of Risk Portfolio rules which is explained in Section 3 and Section 4 in detail.\n\nThe tool allows clients to define whether a trade which does not fall into any of the defined Risk Portfolios should basically be allowed or not, by a generic configuration in the initial onboarding. The trade intentions / orders which cannot be allocated to any of the clusters can be either\n\n- Allowed (Risk Portfolios to be configured as *Constraints*)\n- Or disallowed (Risk Portfolios to be configured as *Permissions*).\n\nAll limits defined for the Risk Portfolios are in credit currency<sup>1</sup> . The conversion of trade amounts is done using the end-of-day rates of the 360T Essential Data Feed.\n\nIt is important to note that only users with corresponding user rights are able to administer the Risk Portfolio within Bridge. Please contact [<EMAIL>](mailto:<EMAIL>) or your customer relationship manager for setting up the administrator rights.\n\n<sup>1</sup> Credit currency (also referred to as company currency or home currency) is a single currency defined for 360T entity accounts in the initial onboarding stage. This parameter can be changed by CAS teams upon request.\n\n# <span id=\"page-4-0\"></span>**2 GETTING STARTED**\n\nLimits Monitor can be administrated manually via Risk Portfolio module within the Bridge Administration tool as well as automatically via API. Bridge Administration can be accessed via the menu option \"Administration\" in the screen header of the Bridge application.\n\n|                              | $\\vee$ Preferences | A Administration | $\\vee$ Help $\\bigcup$ $\\bigcirc$ $\\bigcirc$ AA - $\\Box$ X |  |  |\n|------------------------------|--------------------|------------------|-----------------------------------------------------------|--|--|\n|                              |                    |                  |                                                           |  |  |\n| Change Password              |                    |                  |                                                           |  |  |\n| <b>Bridge Administration</b> |                    |                  |                                                           |  |  |\n|                              |                    |                  |                                                           |  |  |\n|                              |                    |                  |                                                           |  |  |\n|                              |                    |                  |                                                           |  |  |\n|                              |                    |                  |                                                           |  |  |\n|                              |                    |                  |                                                           |  |  |\n\n<span id=\"page-4-1\"></span>Figure 1 Header Bar\n\nThe Bridge Administration feature opens to a homepage with shortcuts to several different administration tools and actions for the particular user.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\nThe Risk Portfolio administration panel is opened using the Risk Portfolio button on the Bridge Administration homepage.\n\n![](_page_4_Picture_9.jpeg)\n\nFigure 2 Bridge Administration Homepage\n\n<span id=\"page-4-2\"></span>The \"Risk Portfolio\" icon opens a navigation panel which shows the institution tree. Depending on the setup, the tree may include a single entity or several entities if the user`s entity has trade-as, trade-on-behalf or other ITEX entities configured under the main entity.\n\n![](_page_5_Picture_2.jpeg)\n\nFigure 3 Risk Portfolio Homepage\n\n<span id=\"page-5-0\"></span>A single-click on the entity name opens the Risk Portfolio panel.\n\nFor users who wants to access the tool via their internet browser, there is also an HTML SSO login available (For activation, please contact Client Advisory Services Team).\n\nAfter multi-factor authentication, user will be able to see all trading applications as well as Self-Service portal, which is the administration panel on HTML.\n\n![](_page_5_Picture_7.jpeg)\n\nFigure 4 HTML Login\n\n<span id=\"page-5-1\"></span>Clicking in Self-Service Portal icon will direct user to the 360T Self Service Portal Start Page where all administration panels user has access is displayed. Clicking on LIMMO icon will launch the Limits Monitor administration panel.\n\n![](_page_6_Picture_2.jpeg)\n\nFigure 5 HTML Access to Limits Monitor\n\n<span id=\"page-6-1\"></span>IMPORTANT: Please note that clients with several legal or sub-entities within 360T system will see all of the related entities on the left-hand side menu. However, risk portfolio rules should be created for the credit entity which needs to assign the credit lines to its counterparts and be part of the trades (i.e. main entity).\n\n# <span id=\"page-6-0\"></span>**3 DEFINING RISK PORTFOLIO GROUPS**\n\nRisk Portfolio Groups facilitate the management of parameters that can be used in risk portfolio rule definition. This allows admin users to have a clear overview of the risk categories they wish to create and makes the limit definition process more efficient by giving users the ability to combine several values into one field. It also simplifies the management of parameters as any change to a risk portfolio group affects each associated risk portfolio rule.\n\n| Risk Portfolio Groups Risk Portfolio Rules Active Rules = |                                                                     |            |               |  |                                      |  |  |  |\n|-----------------------------------------------------------|---------------------------------------------------------------------|------------|---------------|--|--------------------------------------|--|--|--|\n|                                                           | Product Groups Legal Entity Groups Dealer Groups Counterpart Groups |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           | $\\alpha$                                                            |            | $\\rightarrow$ |  |                                      |  |  |  |\n| Group Name                                                |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           | Deliverables                                                        |            | $V$ B         |  |                                      |  |  |  |\n|                                                           | NonDeliverables                                                     |            | <b>ze</b>     |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     | $\\! +$     |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           | Available (5)                                                       |            | Selected (2)  |  |                                      |  |  |  |\n|                                                           | <b>Fx Spot</b>                                                      | <b>NDS</b> |               |  |                                      |  |  |  |\n|                                                           | <b>Px Forward</b>                                                   | <b>NDF</b> |               |  |                                      |  |  |  |\n|                                                           | <b>Fx Swap</b><br><b>Block-Trade</b>                                |            |               |  |                                      |  |  |  |\n|                                                           | <b>Fx Time Option</b>                                               |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     | $\\,$       |               |  |                                      |  |  |  |\n|                                                           |                                                                     | $\\epsilon$ |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     | $\\,$       |               |  |                                      |  |  |  |\n|                                                           |                                                                     | $\\ll$      |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  | Discard all Changes Contract Changes |  |  |  |\n| CAGLARCOMP1.TEST X                                        |                                                                     |            |               |  |                                      |  |  |  |\n|                                                           |                                                                     |            |               |  |                                      |  |  |  |\n\n<span id=\"page-7-0\"></span>Figure 6 Risk Portfolio Groups\n\nRisk Portfolio Groups consist of the following parameters:\n\n- *Product Groups*: Includes FX Spot, FX Forward, FX Swap, NDF, NDS, FX Time Option and Block-Trades.\n- *Legal Entity Groups*: An entity or entities (i.e. in case trades are executed on behalf of several legal entities) can be defined within *Legal Entity Groups.*\n- *Dealer Groups:* Single or multiple dealers of an entity can be grouped together via Dealer Groups. All active users of an entity who is allowed to trade will appear in the list.\n- *Counterpart Groups*: Where admin users would like to set a single credit line for several counterparts (either based on credit rating, country risk or related entities etc.), they can create a counterpart group and add multiple counterparts into that group. The available members are determined based on the permissioned trading relationship.\n\n![](_page_8_Picture_2.jpeg)\n\nFigure 7 Counterpart Groups\n\n# <span id=\"page-8-1\"></span><span id=\"page-8-0\"></span>**4 DEFINING RISK PORTFOLIO RULES**\n\nBy combining different risk parameters such as counterparty, legal entity, dealer and product within a Risk Portfolio, limits can be defined for many different types of exposure.\n\nWith a single-click on button, a new rule will be added as a row with nine parameters shown as separate columns, to be defined by the admin user.\n\nIMPORTANT: Please note that adding, removing or amending a rule will be effective immediately *after end of day rollover*. This occurs at 5:00 PM New York Time. Limits can be updated intraday with an immediate effect within the Active Rules tab.\n\nThe switch button on the left of each rule is used to enable or disable the rule, mitigating the need to remove rules and the requirement to redefine should they be needed again.\n\nThe other parameters of the risk portfolio rules will be described in the following subsections in detail.\n\n| Risk Portfolio Groups (Risk Portfolio Rules ) Active Rules = |                 |                      |                    |                                      |                   |                   |   |                                                 |                  |                     |   | $\\begin{array}{c} \\pm \\\\ \\pm \\end{array}$ |\n|--------------------------------------------------------------|-----------------|----------------------|--------------------|--------------------------------------|-------------------|-------------------|---|-------------------------------------------------|------------------|---------------------|---|-------------------------------------------|\n|                                                              | $^{\\circ}$      |                      |                    | $\\rightarrow$                        |                   |                   |   |                                                 | Constraints      | $\\vee$ Rules Switch |   |                                           |\n|                                                              | Enabled Rule Id |                      | $\\vee$ Counterpart | Product                              | Legal Entity      | Dealer            |   | Algorithm                                       | Limit            |                     |   |                                           |\n|                                                              |                 | FatFinger            | Any                | $V$ Any                              | $\\frac{1}{2}$ Any | $\\frac{1}{2}$ Any |   | / Per Deal Limit                                | $7/$ (25,000,000 |                     |   |                                           |\n|                                                              | ∞               | 360TBank-ND          |                    | 360TBANKTEST 5 NonDeliverables 5 Any |                   | $Z$ Any           |   | V Daily Gross Trading Limit V   (50,000,000     |                  |                     | × |                                           |\n|                                                              | O               | 360TBank-Deliverable |                    | 360TBANKTEST / Deliverables / Any    |                   |                   |   | Many / Daily Gross Trading Limit / (100,000,000 |                  |                     |   |                                           |\n|                                                              |                 |                      |                    |                                      |                   |                   | - |                                                 |                  |                     |   |                                           |\n\n<span id=\"page-8-2\"></span>Figure 8 Risk Portfolio Rules\n\n# <span id=\"page-9-0\"></span>**4.1 Rule ID**\n\nRule ID field is a text-box in which users can define the name/ID of their rules. The field helps users to identify their rules and allows an easier matching between several limit systems they are administrating.\n\n| <b>Risk Portfolio Groups</b>             | <b>Risk Portfolio Rules</b> | <b>Active Rules</b>      | Ξ<br><b>Risk Portfolio PFF</b>             |                     |                         |                                    |                                                           |                        | 土土 |\n|------------------------------------------|-----------------------------|--------------------------|--------------------------------------------|---------------------|-------------------------|------------------------------------|-----------------------------------------------------------|------------------------|----|\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                             | $\\rightarrow$            |                                            |                     |                         |                                    | Constraints                                               | <b>VO</b> Rules Switch |    |\n| Enabled                                  | Rule Id                     | $\\wedge$ Counterpart     | Portfolio                                  | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Ex Time Period</b>              | Algorithm                                                 | Limit                  |    |\n| <b>CO</b>                                | 360T Group Risk             | 360T Group Related / Any |                                            | $\\mathbb{Z}$ Any    | $\\mathbb{Z}$ Any        | $\\mathbb{Z}$ Any<br>$\\overline{z}$ | Gross Daily Settlement Limit / 0                          |                        |    |\n| $\\sigma$                                 | 360T.MMC PFE                | 360T.MMC                 | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ Any |                     | $\\frac{1}{2}$ Any       |                                    | ジ TODAY-1 MONTH ジ Potential Future Exposure<br>$\\bar{z}$  | 45,500,000             |    |\n| Ø                                        | 360T.MMC Settlement         | 360T.MMC                 | $\\frac{3}{2}$ Any                          | $\\frac{3}{2}$ Any   | $\\frac{3}{2}$ Any       |                                    | TODAY-3 MONTHS 5 Net Daily Settlement Limit 5 (51,000,000 |                        |    |\n\n<span id=\"page-9-2\"></span>Figure 9 Defining Rule ID\n\nThe Rule ID value also serves to match the rules in Risk Portfolio Rules and Active Rules tab.\n\n# <span id=\"page-9-1\"></span>**4.2 Counterpart**\n\nThe *Counterpart* field defines the single trading counterparty or group of counterparties for which limits will be defined. As described in Section 3, admin users can group several counterparties and set generic rules and limits for the group. Once a rule has been added, users will be able to select the desired counterpart from a list which includes permissioned counterparts and pre-defined counterpart groups. Please note that the group values appear on top of the list.\n\n| (a)                  |                          |                           | Please select: | $\\times$      | Constraints                                                            | <b>O</b> Rules Switch<br>$\\vee$ |  |\n|----------------------|--------------------------|---------------------------|----------------|---------------|------------------------------------------------------------------------|---------------------------------|--|\n| Enabled              | Rule Id                  |                           |                |               | Algorithm                                                              | Limit                           |  |\n| ♡●                   | 360T Group               | Q                         |                | $\\rightarrow$ | $\\mathbb{Z}^2$ Gross Daily Settlement Limit $\\mathbb{Z}^2$ (0)         |                                 |  |\n| VO                   | 360T.MMC PFE             |                           | Available (13) |               | TH $\\frac{1}{2}$ Potential Future Exposure $\\frac{1}{2}$ (45,500,000   |                                 |  |\n| $\\sqrt{\\phantom{a}}$ | 360T.MMC Settlement      | Rating C+                 |                |               | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$   (51,000,000 |                                 |  |\n| $\\sqrt{\\phantom{a}}$ | 360TBANK PFE             | 360T Group Related        |                |               | TH J Potential Future Exposure J 67,500,000                            |                                 |  |\n| V O                  | 360TBANK Settlement      | 360T.MMC<br>360TBANK.TEST |                |               | THS $5$ Net Daily Settlement Limit $5$ (73,000,000                     |                                 |  |\n| $\\sigma$             | <b>COBA PFE</b>          | BNPP, PAR, DEMO           |                |               | TH J Potential Future Exposure J (23,500,000                           |                                 |  |\n| $\\sqrt{\\phantom{a}}$ | <b>COBA Settlement</b>   | BOAL.DEMO                 |                |               | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (29,000,000 |                                 |  |\n| V O                  | PEBANK PFE               | CAGLAR.TEST               |                |               | TH = Potential Future Exposure = = 7 (34,500,000                       |                                 |  |\n| $\\sqrt{\\phantom{a}}$ | <b>PEBANK Settlement</b> | CAGLARMTF.TEST            |                |               | THS F Net Daily Settlement Limit F 40,000,000                          |                                 |  |\n| $\\sqrt{\\phantom{a}}$ | <b>RBS PFE</b>           | COBA.DEMO<br>MOCK.TEST    |                |               | TH 5 Potential Future Exposure 5 2 2.000.000                           |                                 |  |\n| $\\sqrt{\\phantom{a}}$ | <b>RBS Settlement</b>    | PEBANK_APAC.TEST          |                |               | THS 3 Net Daily Settlement Limit 3 (24,000,000                         |                                 |  |\n| VO                   | SEB PFE                  | RBS.LND.DEMO              |                |               | TH $\\mathbb{R}$ Potential Future Exposure $\\mathbb{R}$ (56,500,000     |                                 |  |\n| VO                   | <b>SEB Settlement</b>    | SEB.FRA.DEMO              |                |               | THS $5$ Net Daily Settlement Limit $5$ 62,000,000                      |                                 |  |\n|                      | <b>Total Net Limit</b>   |                           |                |               | Aggregate Net Settlement L. 3/ (2,000,000,000                          |                                 |  |\n|                      |                          |                           |                |               |                                                                        |                                 |  |\n\n<span id=\"page-9-3\"></span>Figure 10 Defining counterpart for risk portfolio rules\n\nIt is also possible to make the rule applicable to all permissioned counterparties by clicking on *Apply `Any`*.\n\n# <span id=\"page-10-0\"></span>**4.3 Product**\n\nThe *Product* field defines the group of products to which the limits will be applied. As described i[n Section 3,](#page-6-0) admin users can create *Product Groups* from the *Risk Portfolio Groups* tab. The pre-configured product groups will then be available in the *Product*  field of the added rule.\n\nIn addition to define pre-configured product groups in risk portfolio rule, it is also possible to *Apply `Any`* which makes the rule applicable for all portfolios, thus for every currency pair and product traded.\n\n![](_page_10_Picture_5.jpeg)\n\nFigure 11 Defining product for risk portfolio rules\n\n# <span id=\"page-10-2\"></span><span id=\"page-10-1\"></span>**4.4 Legal Entity Group**\n\nThe *Legal Entity Group* field defines the group of related entities for which the risk limit will be defined. As described in Section 3, admin users can group several legal entities in order to set generic rules and limits for them. Once a rule has been added, users will be able to select the desired legal entity or pre-defined legal entity group by by selecting it and then clicking on *Apply Selected*. It is also possible to select `Any` which would make the rule applicable to all the legal entities involved in trading from the client's perspective.\n\n*Legal Entity Group* parameter can be especially useful for clients with multiple legal entities who need to manage risks centrally.\n\n| $\\alpha$             |                          |               |                       | $\\times$              |                                                                        | Constraints $\\vee$ | <b>O</b> Rules Switch |  |\n|----------------------|--------------------------|---------------|-----------------------|-----------------------|------------------------------------------------------------------------|--------------------|-----------------------|--|\n| Enabled Rule Id      |                          | (Q            | Please select:        | $\\rightarrow$         | Algorithm                                                              | Limit              |                       |  |\n| $\\circledcirc$       | 360T Group Risk          |               | Available (1)         |                       | $\\mathbb{Z}^2$ Gross Daily Settlement Limit $\\mathbb{Z}^2$   (0)       |                    | 而                     |  |\n| √●                   | 360T.MMC PFE             | 360T.RMS.TAS1 |                       |                       | TH 5 Potential Future Exposure 5 45,500,000                            |                    | 亩                     |  |\n| QO.                  | 360T.MMC Settlement      |               |                       |                       | THS F Net Daily Settlement Limit F 51,000,000                          |                    | 前                     |  |\n| $\\sigma$             | 360TBANK PFE             |               |                       |                       | TH I Potential Future Exposure I 67,500,000                            |                    | 面                     |  |\n| $\\bullet$            | 360TBANK Settlement      |               |                       |                       | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (73,000,000 |                    | 面                     |  |\n| $\\sigma$             | COBA PFE                 |               |                       |                       | TH = Potential Future Exposure = = 23,500,000                          |                    | 前                     |  |\n| $\\sigma$             | <b>COBA Settlement</b>   |               |                       |                       | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (29,000,000 |                    | 育                     |  |\n| $\\sigma$             | PEBANK PFE               |               |                       |                       | TH / Potential Future Exposure / 34,500,000                            |                    | iîi                   |  |\n| $\\sigma$             | <b>PEBANK Settlement</b> |               |                       |                       | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (40,000,000   |                    | 面                     |  |\n| $\\sqrt{2}$           | <b>RBS PFE</b>           |               |                       |                       | TH = Potential Future Exposure = = 2,000,000                           |                    | 前                     |  |\n| $\\sigma$             | <b>RBS Settlement</b>    |               |                       |                       | THS F Net Daily Settlement Limit F 24,000,000                          |                    | 育                     |  |\n| $\\sqrt{\\phantom{a}}$ | SEB PFE                  |               |                       |                       | TH IV Potential Future Exposure IV 56,500,000                          |                    | 面                     |  |\n| (VO)                 | <b>SEB Settlement</b>    |               |                       |                       | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$   62,000,000  |                    | 面                     |  |\n| rv o                 | <b>Total Net Limit</b>   |               |                       |                       | 5 Aggregate Net Settlement L. 5 (2,000,000,000                         |                    | 前                     |  |\n|                      |                          |               | Apply \"Any\"<br>Cancel | <b>Apply Selected</b> |                                                                        |                    |                       |  |\n\n<span id=\"page-11-1\"></span>Figure 12 Defining legal entity for risk portfolio rules\n\n# <span id=\"page-11-0\"></span>**4.5 Dealer**\n\n*Dealer* parameter defines the single or group of individuals who executed the transaction on behalf of the credit entity. As described in *Section 3,* it is possible to group the dealers in order to assign one single limit for a group of them. As well as created groups, all dealers who can trade (Trader, Treasurer, Hybrid and API types of users) and are active will appear as available value for this parameter.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> |                                          | Active Rules Risk Portfolio PFE    | $=$                   |                       |                       |  |                                                         |              |                             | 出土  |\n|------------------------------|-----------------------------|------------------------------------------|------------------------------------|-----------------------|-----------------------|-----------------------|--|---------------------------------------------------------|--------------|-----------------------------|-----|\n| $\\sqrt{a}$                   |                             |                                          | $\\rightarrow$                      |                       |                       |                       |  | Permissions                                             | $\\checkmark$ | <b>O</b> Rules Switch       |     |\n| Enabled Rule Id              |                             | $\\vee$ Cour                              |                                    | Please select:        | $\\times$              | <b>Fx Time Period</b> |  | Algorithm                                               |              | Limit                       |     |\n| ☞                            | RCM                         | Any                                      |                                    |                       |                       | Any                   |  | IV Daily Net Trading Limit                              |              | $\\mathbb{Z}$ (2,000,000,000 | I û |\n| ▽●                           | RCL-GBP                     | Any                                      | Q                                  |                       | $\\rightarrow$         |                       |  | TODAY-1 MONTH   Maily Net Trading Limit                 |              | $\\frac{3}{2}$ (500,000,000) | 亩   |\n| ▽●                           | <b>RCL-CHF</b>              | Any                                      |                                    | Available (5)         |                       |                       |  | TODAY-1 MONTH $\\mathbb{R}$ Daily Net Trading Limit      |              | $7/$ 350,000,000            | 亩   |\n| ∞                            | 97047993-ER                 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Swap Dealers<br>360TMMC.AutoDealer |                       |                       | Any                   |  | T/ Gross Daily Settlement Li. T/ 20,000,000             |              |                             | 面   |\n| $\\sim$ 0                     | 22175403-WER                | 221                                      | 360TMMC.Trader1                    |                       |                       |                       |  | SPOTNEXT-1 YEAR / Potential Future Exposure / 2,400,000 |              |                             | 盲   |\n| $\\sigma$                     | 22175403-ER                 | 221                                      | 360TMMC.API                        |                       |                       | Any<br>Any            |  | Gross Daily Settlement Li.   20,000,000                 |              |                             | 面   |\n| $\\circ$                      | 22175394-ER                 | 221                                      | 360TMMC.Trader2                    |                       |                       |                       |  | Cross Daily Settlement Li. 7 7,500,000                  |              |                             | 前   |\n|                              |                             |                                          |                                    |                       |                       |                       |  |                                                         |              |                             |     |\n|                              |                             |                                          |                                    |                       |                       |                       |  |                                                         |              |                             |     |\n|                              |                             |                                          |                                    | Apply \"Any\"<br>Cancel | <b>Apply Selected</b> |                       |  |                                                         |              |                             |     |\n\n<span id=\"page-11-2\"></span>Figure 13 Defining dealer for risk portfolio rules\n\n# <span id=\"page-12-0\"></span>**4.6 Algorithms**\n\nThe creation of risk portfolios provides a solution to address different types of risk exposures through different combinations of trade parameters, and this is further enhanced by the ability to assign different risk exposure calculation methodologies.\n\n| <b>MISK PORTIONO GROOPS ANSAFARITIONS MARKS ACTIVE NUMBER</b> |                      |                          |          |                                  |                |                                                                                                                                                             |                                     | $\\frac{1}{2}$ |\n|---------------------------------------------------------------|----------------------|--------------------------|----------|----------------------------------|----------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------|---------------|\n|                                                               | $\\sqrt{a}$           |                          |          | $\\rightarrow$                    |                |                                                                                                                                                             | Constraints v <b>D</b> Rules Switch |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      | Enabled Rule Id          |          |                                  |                | v Counterpart Product Legal Entity Dealer Algorithm National Limit                                                                                          |                                     |               |\n|                                                               | $\\sigma$             | FatFinger<br>360TBank-ND |          |                                  |                | Any of Any of Any of Any of Per Deal Limit of (25,000,000)<br>360TBANKTEST V   NonDeliverables V   Any V   Any V   Daily Gross Trading Limit V   50,000,000 | ٠                                   |               |\n|                                                               | $\\overline{(\\cdot)}$ |                          |          |                                  |                | 360TBank-Deliverable 360TBANKTEST & Deliverables & Any by Any 19 Daily Cross Trading Limit 8 (100,000,000                                                   | ٠                                   |               |\n|                                                               | $\\circ$              |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  | Please Select: | $\\times$                                                                                                                                                    |                                     |               |\n|                                                               |                      |                          | $\\alpha$ |                                  |                | $\\rightarrow$                                                                                                                                               |                                     |               |\n|                                                               |                      |                          |          |                                  | Available (2)  |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          | Per Deal Limit                   |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          | <b>Daily Gross Trading Limit</b> |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                | Cancel Apply                                                                                                                                                |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n|                                                               |                      |                          |          |                                  |                |                                                                                                                                                             |                                     |               |\n\n<span id=\"page-12-2\"></span>Figure 14 Setting algorithm for risk portfolio rules.\n\nThe *Algorithm* field of a risk portfolio rule defines what the user is limiting.\n\nTwo different algorithms are available for Trading Limits Profile:\n\n- **Daily Gross Trading Limit**\n- **Per Deal Limit**\n\nUsers who has full access to the tool can also assign one of the below limit types to their risk portfolios:\n\n- **Net Daily Settlement Limit,**\n- **Gross Daily Settlement Limit,**\n- **Aggregate Net Settlement Limit,**\n- **Aggregate Gross Settlement Limit**\n- **Potential Future Exposure and**\n- **Daily Net Trading Limit**\n\nPlease contact your Account Manager to receive more information on the other methodologies.\n\n### <span id=\"page-12-1\"></span>**4.6.1 Daily Gross Trading Limit**\n\nDaily Gross Trading Limit algorithm can be used to limit the total intraday trading volume for the current trading day.\n\n*For example: If Credit Entity* A sets a 10 million Euro trading limit for a specific risk portfolio rule, the company currency equivalent of total transaction done for the current trading day cannot exceed 10 million Euro for that specific risk portfolio. After end of day rollover, the utilization is reset to 0 and another 10 million Euro limit is available.\n\n#### <span id=\"page-13-0\"></span>**4.6.2 Per Deal Limit**\n\nAlso known as Fat-Finger limit, Per Deal Limit aims to restrict the notional amount of one single deal. This limit type is mostly used to address operation risk and associated with dealers.\n\n## <span id=\"page-13-1\"></span>**4.7 Limit**\n\nThe last parameter defined for each portfolio is the amount of the limit. The limit is defined in credit currency of the entity.\n\n**IMPORTANT: Please note that a generic limit set on a risk portfolio rule can be amended within that rule. As with any parameter change in Risk Portfolio Rules, a generic limit change will only be valid after end of day rollover. Should an immediate change to a limit be required, this should be done within the 'Active Rules' tab; such a change will be valid immediately for the duration of that day, and overwritten by the limit set under Risk Portfolio Rules at end of day rollover.**\n\n![](_page_13_Figure_8.jpeg)\n\n<span id=\"page-13-4\"></span>Figure 15 Defining limit for risk portfolio rules.\n\n# <span id=\"page-13-2\"></span>**4.8 Edit Rule**\n\nParameters of a risk portfolio rule can be changed by clicking on icon next to the relevant parameter. *Rule Id* and *Limit* parameters can be amended by single-click on the text-field. Once a change is done on the rule, it is applied by click on `Save`.\n\n![](_page_13_Figure_12.jpeg)\n\n## <span id=\"page-13-3\"></span>**4.9 Activate/Deactivate Rule**\n\nFor users who want to remove a Risk Portfolio rule temporarily, the Risk Portfolio panel provides an option to deactivate the rules instead of completely deleting them. By using the toggle button ( ), it is possible to enable or disable the relevant rule.\n\nIMPORTANT: Please note that, consistent with all actions in Risk Portfolio Rules, this action will only take effect with *end of day rollover*.\n\n# <span id=\"page-14-0\"></span>**4.10Delete Rule**\n\nRisk portfolio rules can be completely removed with the `Delete Rule` function. Clicking on the icon and then saving removes the rule from the Risk Portfolio rules.\n\nIMPORTANT: Please note that a deleted rule is still active until *end of day rollover*. (Active Rules can be monitored under `Active Rules` tab. The function of this tab is explained in [Section 5](file://///360t.com/shares/Projects/Risk%20limits/User%20Manual/360T%20User%20Guide%20Global%20Risk%20Management%20System.docx) in more detail.)\n\n# <span id=\"page-14-1\"></span>**4.11Bulk Upload/Download via CSV**\n\n360T`s Limits Monitor provides a csv upload/download functionality to allow bulk upload/update of risk portfolio rules. The functionality facilitates the manual administration of rule and limit settings by allowing,\n\n- Creation of new risk portfolio rules\n- Update of any parameter of an existing risk portfolio rule\n- Deletion/deactivation of an existing risk portfolio rule.\n\n| Q         |                            |                                 | $\\rightarrow$ |                                          |                   |                     |                   |                         |                       | Constraints                       |           | <b>KO</b><br><b>Rules Switch</b><br>$\\checkmark$ |   |\n|-----------|----------------------------|---------------------------------|---------------|------------------------------------------|-------------------|---------------------|-------------------|-------------------------|-----------------------|-----------------------------------|-----------|--------------------------------------------------|---|\n| Enabled   | Rule Id                    | $\\vee$ Counterpart              |               | Portfolio                                |                   | <b>Legal Entity</b> |                   | <b>Execution Method</b> | <b>Fx Time Period</b> | <b>Algorithm</b>                  |           | Limit                                            |   |\n| ∞         | <b>Total Net Limit</b>     | Any                             |               | - V  <br>$\\frac{y}{x}$ Any               | Any               |                     | $\\frac{1}{2}$ Any |                         | $\\mathbb{Z}$ Any<br>₹ | Aggregate Net Settlement Limit 3  |           | 2,000,000,000                                    | û |\n| ØO        | <b>SEB Settlement</b>      | <b>SEB FRA DEMO</b>             |               | $\\mathcal{V}$ Any                        | $\\mathcal{V}$ Any |                     | $\\mathbb{Z}$ Any  |                         | F/ TODAY-3 MONTHS F/  | Net Daily Settlement Limit        | ∌∕        | 62.000.000                                       | û |\n| $\\bullet$ | SEB PFE                    | SEB.FRA.DEMO                    |               | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$   | Anv               |                     | $\\frac{3}{2}$ Any |                         | 5 TODAY-1 MONTH 5     | Potential Future Exposure         |           | $5/$ 56,500,000                                  | Û |\n| Ø         | <b>RBS Settlement</b>      | RBS.LND.DEMO                    |               | $\\mathbb{Z}$ Any<br>₩                    | Any               | $\\frac{1}{2}$       | Any               | ৶                       | TODAY-3 MONTHS =      | Net Daily Settlement Limit        | Ξ/        | 24,000,000                                       | û |\n| $\\bullet$ | <b>RBS PFE</b>             | RBS.LND.DEMO                    |               | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Any               |                     | $\\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         | $\\bar{z}$ | 2,000,000                                        | û |\n| Ø         | <b>PEBANK Settlement</b>   | PEBANK_APAC.TEST                |               | W.<br>Any                                | Anv               | $\\overline{z}$      | Any               |                         | TODAY-3 MONTHS        | Net Daily Settlement Limit        | ₹∕        | 40,000,000                                       | û |\n| $\\bullet$ | PEBANK PFE                 | PEBANK_APAC.TEST / Any vs G10 / |               |                                          | Anv               |                     | $\\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         |           | $7/$ (34,500,000                                 | Û |\n| ØO        | <b>COBA Settlement</b>     | COBA.DEMO                       |               | $\\frac{1}{2}$ Any<br>5/                  | Anv               | ₹                   | Any               | ₩                       | TODAY-3 MONTHS E      | Net Daily Settlement Limit        | ₹∕        | 29,000,000                                       | û |\n| $\\bullet$ | <b>COBA PFE</b>            | COBA.DEMO                       |               | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Anv               |                     | $\\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         |           | $\\frac{7}{2}$ (23.500.000)                       | û |\n| Ø         | <b>360TBANK Settlement</b> | <b>360TBANKTEST</b>             |               | $\\mathbb{Z}$ Any<br>$\\overline{z}$       | Any               |                     | $\\mathbb{Z}$ Any  | ₩                       | TODAY-3 MONTHS EZ     | Net Daily Settlement Limit        | ₩         | 73,000,000                                       | û |\n| $\\bullet$ | <b>360TBANK PFE</b>        | 360TBANK.TEST                   |               | $\\mathbb{Z}$ Any vs G10 $\\mathbb{Z}$     | Anv               |                     | $\\frac{3}{2}$ Any |                         | V TODAY-1 MONTH       | Potential Future Exposure         |           | 7/67.500.000                                     | û |\n| Ø         | 360T.MMC Settlement        | 360T.MMC                        |               | $\\mathbb{Z}$ Any<br>$\\bar{z}$            | Anv               |                     | $\\mathbb{Z}$ Any  | ₩                       | TODAY-3 MONTHS E      | Net Daily Settlement Limit        | ₩         | 51,000,000                                       | ŵ |\n| $\\bullet$ | 360T.MMC PFE               | 360T.MMC                        |               | $V$ Any vs G10 $V$                       | Any               |                     | $\\frac{3}{2}$ Any | ₩                       | TODAY-1 MONTH         | Potential Future Exposure         |           | $7/$ (45.500.000                                 | û |\n| Ø         | 360T Group Risk            | 360T Group Related %            |               | ₩<br>Any                                 | Any               |                     | $\\frac{3}{2}$ Any | ₩                       | TODAY-3 MONTHS E      | Aggregate Gross Settlement Li., E |           | 50,000,000                                       | û |\n\n<span id=\"page-14-2\"></span>Figure 16 Risk Portfolio Rules Upload/Download\n\nBy clicking on icon on top right of the *Risk Portfolio Rules* view, user can download the snapshot of their current risk portfolio rules as csv file. After making the necessary\n\nchanges in the downloaded file, user can upload the new rules by clicking on icon and then selecting the file.\n\nOnce a rule is uploaded, 360T`s Limit Monitor will create a result file to display the status of the changes. Admin user can save the result file, review it and then save or discard the changes.\n\n#### User Guide Limits Monitor\n\n| Save As                            |                                                                              |                 |                |                                      |          |      |                                        |      |               | $\\times$         |               |                          |                       |                                                         |                                          |                            |                        |      |\n|------------------------------------|------------------------------------------------------------------------------|-----------------|----------------|--------------------------------------|----------|------|----------------------------------------|------|---------------|------------------|---------------|--------------------------|-----------------------|---------------------------------------------------------|------------------------------------------|----------------------------|------------------------|------|\n| $\\leftarrow$ $\\rightarrow$<br>个    | > This PC > Desktop > 360T Limits Monitor                                    |                 |                |                                      | $\\vee$ 0 |      | C Search 360T Limits Monitor           |      |               |                  |               |                          |                       |                                                         |                                          |                            |                        | 土土   |\n| New folder<br>Organize -           |                                                                              |                 |                |                                      |          |      |                                        |      | 日十            | $\\bullet$        |               |                          |                       | Constraints                                             |                                          | $\\checkmark$               | <b>Nules</b> Switch    |      |\n| <sup>19</sup> VisualVM             | Name                                                                         |                 | File ownership | Date modified                        |          | Type |                                        | Size |               |                  |               |                          |                       |                                                         |                                          |                            |                        |      |\n| <sup>1</sup> WINDOWS               | <b>D</b> 360T active limmo rules                                             |                 |                | 10/05/2021 15:18                     |          |      | Microsoft Excel C                      |      | $2$ KB        |                  | <b>Method</b> |                          | <b>Fx Time Period</b> | Algorithm                                               |                                          | Limit                      |                        |      |\n| <b>This PC</b>                     | <b>Di</b> 360T active limmo rules result<br>360T limmo_curreny_couple_groups |                 |                | 10/05/2021 15:19<br>30/04/2021 17:32 |          |      | Microsoft Excel C<br>Microsoft Excel C |      | 2 K B<br>1 KB |                  |               |                          | $\\frac{1}{2}$ Any     | ジ Aggregate Net Settlement Limit ジ                      |                                          | 2.000.000.000              |                        |      |\n| 3D Objects<br>Desktop              | 360T limmo_curreny_couple_groups_result                                      |                 |                | 30/04/2021 17:21                     |          |      | Microsoft Excel C                      |      | 3 KB          |                  |               |                          |                       | TODAY-3 MONTHS I Net Daily Settlement Limit             | ৶                                        | 62,000,000                 | 肯                      |      |\n| <b>Pall</b> Documents              | 360T limmo_rules                                                             |                 |                | 16/05/2021 13:40                     |          |      | Microsoft Excel C                      |      | 2 KB          |                  |               |                          |                       | ■ TODAY-1 MONTH ■ Potential Future Exposure             | U.                                       | 56,500,000                 |                        |      |\n| <b>L</b> Downloads                 |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | 0                        |                       | TODAY-3 MONTHS $\\mathcal{V}$ Net Daily Settlement Limit | ¥.                                       | 24,000,000                 | ŵ                      |      |\n| h Music                            |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\\overline{z}$           |                       | TODAY-1 MONTH   Potential Future Exposure               | $\\bar{z}$                                | 2,000,000                  | 亩                      |      |\n| Pictures                           |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\\bar{z}$                |                       | TODAY-3 MONTHS 5/ Net Daily Settlement Limit            | W.                                       | 40,000,000                 | ŝ                      |      |\n| Videos<br>Local Disk (C)           |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\\overline{\\mathscr{L}}$ |                       | TODAY-1 MONTH IV Potential Future Exposure              | ∛                                        | 34,500,000                 |                        |      |\n| shares (\\\\office-                  |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | V                        |                       | TODAY-3 MONTHS E Net Daily Settlement Limit             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 29.000.000                 | ŵ                      |      |\n|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               |                          |                       | TODAY-1 MONTH 5 Potential Future Exposure               | $\\bar{z}$                                | 23,500,000                 | 旨                      |      |\n| File name: 360T limmo rules result |                                                                              |                 |                |                                      |          |      |                                        |      |               | $\\gamma_{\\rm c}$ |               | $\\bar{z}$ /              |                       | TODAY-3 MONTHS $5/$ Net Daily Settlement Limit          | $\\bar{z}$ /                              | 73,000,000                 | û                      |      |\n| Save as type: CSV                  |                                                                              |                 |                |                                      |          |      |                                        |      |               | $\\sim$           |               | $\\mathbb{Z}^{\\ell}$      |                       | TODAY-1 MONTH   Potential Future Exposure               | び                                        | 67,500,000                 | 请                      |      |\n| $\\land$ Hide Folders               |                                                                              |                 |                |                                      |          |      | Save                                   |      | Cancel        |                  |               | V                        |                       | TODAY-3 MONTHS   Net Daily Settlement Limit             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 51,000,000                 | ŵ                      |      |\n|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               | $\\overline{z}$           |                       | TODAY-1 MONTH = Potential Future Exposure               | $\\bar{z}$                                | 45,500,000                 |                        |      |\n|                                    | $\\sigma$                                                                     | 360T Group Risk |                | 360T Group Related 5 Any             |          |      | $V$ Any                                |      | $V$ Any       |                  |               | $\\mathbb{Z}^d$           |                       | TODAY-3 MONTHS   Aggregate Gross Settlement Li.         |                                          | 50,000,000                 | $\\widehat{\\mathbf{B}}$ |      |\n|                                    | cσ                                                                           | 218631          |                | 360T Group Related 5 Any             |          |      | $5/$ Any                               |      | $5/$ Any      |                  |               |                          |                       | 3 MONTHS-6 MO., 5 Aggregate Gross Settlement LL, 5      |                                          | (20,000,000)               | 亩                      |      |\n|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  | $+$           |                          |                       |                                                         |                                          |                            |                        |      |\n|                                    |                                                                              |                 |                |                                      |          |      |                                        |      |               |                  |               |                          |                       |                                                         |                                          | <b>Discard all Changes</b> |                        | Save |\n\n<span id=\"page-15-0\"></span>Figure 17 Upload Result File\n\nFor successful operation, csv should contain below columns:\n\nThree60tID,RuleId,Active,Counterpart,Portfolio,LegalEntity,Dealer,ExecutionMethod,TimePeriod,AlgorithmType,Limit\n\nColumn separator can be selected as \",\" or \";\" by using Preferences > Shared Settings in Bridge.\n\n|                     |                                                   |                                           | $\\land$ Preferences $\\lor$ Administration $\\lor$ Help $\\parallel$ |           | $\\bullet$ AA $-$ D $\\times$ |\n|---------------------|---------------------------------------------------|-------------------------------------------|-------------------------------------------------------------------|-----------|-----------------------------|\n|                     |                                                   |                                           |                                                                   |           | $\\times$                    |\n| Design Theme        | Other Settings                                    |                                           |                                                                   |           |                             |\n| <b>Display Size</b> |                                                   |                                           |                                                                   |           |                             |\n| Acknowledgement     | 0 0 Enable sounds                                 |                                           |                                                                   |           |                             |\n| Shared Settings     | Enable Auto Dealer ticket sounds                  |                                           |                                                                   |           |                             |\n|                     | 00 Use comma as decimal separator                 |                                           |                                                                   |           |                             |\n|                     | Use semicolon as CSV separator                    |                                           |                                                                   |           |                             |\n|                     | 00 Open the TWS pricing panels in new windows     |                                           |                                                                   |           |                             |\n|                     | 00 Disable animations                             |                                           |                                                                   |           |                             |\n|                     |                                                   |                                           |                                                                   |           |                             |\n|                     |                                                   |                                           |                                                                   |           |                             |\n| <b>RBS PFE</b>      | $V$ Any vs G10 $V$ Any<br>$V$ Any<br>RBS.LND.DEMO | TODAY-1 MONTH   Potential Future Exposure |                                                                   | 2.000.000 |                             |\n\n<span id=\"page-15-1\"></span>Figure 18 CSV Column separator setting\n\nPlease note that, **upload functionality works with snapshot strategy**. This means, the validated rules that are uploaded in the last batch becomes valid as a whole, once the changes are saved. Another way to say it, **if an existing rule is not uploaded in the new batch, then the relevant rule would be removed**.\n\nTherefore, when users\n\n- **a)** *update an existing rule(s)* (for example, changing the limit of a specific counterparty),\n- **b) add a new rule(s)** while keeping the existing configuration,\n\n**it is recommended to download the snapshot of existing configuration via download functionality, do the changes (i.e. change the limit) on the downloaded file and then upload the file again.**\n\nPlease also note that, while creating a new rule, *Three60tID* field should come with null value, whereas while updating an existing rule, *Three60tID* value of an existing rule shouldn`t be changed.\n\n| Field Name      | Type    | Possible Values                                                                 | Description                                                                          |\n|-----------------|---------|---------------------------------------------------------------------------------|--------------------------------------------------------------------------------------|\n| Three60tID      | String  | Determined by 360T.                                                             | Indicates the unique rule                                                            |\n|                 |         | It must be null to create a<br>new rule.                                        | ID assigned automatically<br>by 360T.                                                |\n|                 |         | The current value must be<br>provided<br>to<br>update<br>the<br>existing rule.  |                                                                                      |\n| RuleId          | String  | All characters are allowed.                                                     | Defines<br>the<br>rule<br>ID                                                         |\n|                 |         | When<br>left<br>empty,<br>Three60tID<br>value of the<br>rule will be populated. | determined by client.                                                                |\n| Active          | Boolean | TRUE, FALSE                                                                     | Defines whether the tule<br>is set to enabled (TRUE)<br>or disabled (FALSE).         |\n| Counterpart     | String  | Any or                                                                          | Defines the counterpart or                                                           |\n|                 |         | 360T System name of a<br>permissioned counterpart<br>or                         | group of counterparts for<br>which the limit will be<br>applied for.                 |\n|                 |         | Name<br>of<br>a<br>pre-defined<br>counterpart group.                            |                                                                                      |\n| Portfolio       | String  | Any or                                                                          | Defines the product the                                                              |\n|                 |         | Pre-Defined product group<br>name.                                              | limit will be applied for.                                                           |\n| LegalEntity     | String  | Any or                                                                          | Defines the legal entity or                                                          |\n|                 |         | 360T System name of a<br>legal entity or                                        | group of legal entities for<br>which the limit will be<br>applied for.               |\n|                 |         | a pre-defined legal entity<br>group.                                            |                                                                                      |\n| Dealer          | String  | Any or                                                                          | Defines<br>the<br>dealer<br>or                                                       |\n|                 |         | 360T<br>System<br>name<br>of<br>credit entity`s user who<br>can trade or        | group of dealers for which<br>the limit will be applied for.                         |\n|                 |         | a pre-defined dealer group                                                      |                                                                                      |\n| ExecutionMethod | String  | Any (Not applicable for<br>Trading Limits)                                      | Defines<br>the<br>execution<br>method(s) for which the<br>limit will be applied for. |\n| TimePeriod      | String  | Any (Not applicable for<br>Trading Limits)                                      | Defines for which value<br>dates the limit will be<br>applied for.                   |\n| AlgorithmType   | String  | Daily Gross Trading Limit,<br>Per Deal Limit                                    | Defines the method to be<br>used for limit check prior<br>to executon trade and to   |\n\n|       |         |                                     | calculate<br>the<br>utilization<br>post execution.                                                                     |\n|-------|---------|-------------------------------------|------------------------------------------------------------------------------------------------------------------------|\n| Limit | Decimal | Positive values up to 13<br>digits. | Defines<br>the<br>maximum<br>amount<br>that<br>can<br>be<br>utilized as per defined<br>parameters<br>and<br>algorithm. |\n\n<span id=\"page-17-2\"></span>Table 1 Field Definition for csv risk portfolio rule upload\n\n# <span id=\"page-17-0\"></span>**5 ACTIVE RULES**\n\nThe third tab of Risk Portfolio administration panel, called `Active Rules`, is a dashboard which shows currently active rules and the corresponding `Utilization` amount.\n\nAdmin users can do following operations in *Active Rules*:\n\n**1) Edit Active Limit**: With single-click on the *Limit* free-text area, it is possible to update the active limit amount of a risk portfolio rule. The change done in the *Limit* field is **effective immediately** and is only valid until day rollover: For the new limit to be extended permanently, it must be changed in the relevant `Risk Portfolio Rule`.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> | <b>Active Rules</b> | <b>Risk Portfolio PFE</b> | Ξ                   |                         |                       |                                |                          |             |                    | 土工 |\n|------------------------------|-----------------------------|---------------------|---------------------------|---------------------|-------------------------|-----------------------|--------------------------------|--------------------------|-------------|--------------------|----|\n|                              |                             |                     | →                         |                     |                         |                       |                                |                          |             | <b>Refresh All</b> |    |\n| <b>Rule Id</b>               |                             | $\\vee$ Counterpart  | Portfolio                 | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                      | Limit                    | Utilization |                    |    |\n| Total Net Limit              |                             | <b>Any</b>          | Any                       | Any                 | Any                     | Any                   | Aggregate Net Settlement Limit | $3 000,000,000 $ 803,806 |             | <b>‴</b> ⊙∣�       |    |\n\n<span id=\"page-17-1\"></span>Figure 19 Updating the limit for current trading date\n\n- **2) Visualize the breakdown of the utilization**: Clicking on icon opens a new window as a pop-up in which the user can see the cashflows that is accounted to calculate utilization.\n- **3) Refresh the rule(s):** Clicking on icon next to an active rule brings the latest updated information to the display for the corresponding rule. Although 360T`s Limits Monitor has a real-time update of utilization calculations, it is required to refresh the view to be able to visualize the latest updates.\n\nPlease note that `Refresh All` button on top right of the rule dashboard triggers refresh for all active rules.\n\n- **4) Jump to the Rule:** By clicking on the 'Jump to the Rule' button , admin users can navigate to the corresponding rule under the `Risk Portfolio Rules` panel. This makes it easier to locate the rule that admin user wants to review, edit, or deactivate.\n- **5)** *Search within Active Rules*: The Active Rules tab has a search field where admin users can enter text to filter the rules they are looking for. This makes it easier for them to locate the active rule for which they are searching.\n\nLimit\n\n| $Q$ 360T            |                    | $\\rightarrow$ |                     |                         |                       |                                  |            |                    | <b>Refresh All</b> |\n|---------------------|--------------------|---------------|---------------------|-------------------------|-----------------------|----------------------------------|------------|--------------------|--------------------|\n| Rule Id             | $\\vee$ Counterpart | Portfolio     | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                        | Limit      | <b>Utilization</b> |                    |\n| 360TBANK Settlement | 360TBANK.TEST      | Anv           | Any                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit       | 73,000,000 | $\\mathbf{0}$       | $m \\odot \\phi$     |\n| <b>360TBANK PFE</b> | 360TBANKTEST       | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure        | 67,500,000 | $\\circ$            | 面のゆ                |\n| 360T.MMC Settlement | 360T.MMC           | Anv           | Any                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit       | 51,000,000 | $\\mathbf{0}$       | 而のめ                |\n| 360T.MMC PFE        | 360T.MMC           | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure        | 45,500,000 | $\\circ$            | 面のめ                |\n| 360T Group Risk     | 360T Group Related | Any           | Any                 | Anv                     | TODAY-3 MONTHS        | Aggregate Gross Settlement Limit | 50,000,000 | $\\circ$            | 而らめ                |\n\n<span id=\"page-18-0\"></span>Figure 20 Filtering the rules by using search function\n\n- **6) Download EOD (end-of-day) rates as csv file**: By clicking on , user can download the EOD rates which are used as reference rates to convert the risk exposures into company`s home currency.\n- **7) Download active rules as csv file:** By clicking on , user can download the active rules as csv file into their PC. The downloaded file will have below columns:\n\nThree60tID;RuleId;Active;Counterpart;Portfolio;LegalEntity;Dealer;ExecutionMethod;TimePeriod;AlgorithmType;\n\n**8) Bulk update active limits via csv upload:** Users can update the active limit amount of multiple rules by using csv upload functionality within Active Rules tab. After changing the limit in the downloaded csv file, user can upload the new rules\n\nby clicking on icon and then selecting the file.\n\nFor a successful operation, users can provide all below columns. Although all these columns are required, system will only validate *Three60tID and Limit values.* This means, any changes in other values will be disregarded.\n\nThree60tID;RuleId;Active;Counterpart;Portfolio;LegalEntity;ExecutionMethod;TimePeriod;AlgorithmType;Limit\n\nAfter the file is uploaded, *Limits Monitor* will create a result file to provide feedback to the user.\n\n| Field Name                                                                                                         | Type    | Possible Values                                                                                            | Description                                                                                                                                                                                                                              |\n|--------------------------------------------------------------------------------------------------------------------|---------|------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Three60tID                                                                                                         | String  | Determined by 360T.<br>The<br>current<br>value<br>must be provided to<br>update<br>the<br>active<br>limit. | Indicates<br>the<br>unique<br>rule<br>ID<br>assigned<br>automatically by 360T.                                                                                                                                                           |\n| RuleId,<br>Active, Counterpart,<br>Portfolio. LegalEntity, Dealer<br>ExecutionMethod,<br>TimePeriod, AlgorithmType | Any     | Can be any value.<br>System<br>will<br>ignore<br>these<br>fields<br>to<br>operate limit update.            | Please<br>see<br>….<br>For<br>detailed description of<br>the field. Since these<br>fields are only editable<br>via Risk Portfolio rule<br>configuration, provided<br>values in Active Rules<br>upload<br>will<br>not<br>be<br>validated. |\n| Limit                                                                                                              | Decimal | Positive values up to<br>13 digits.                                                                        | Defines the maximum<br>amount that can be<br>utilized as per defined<br>parameters<br>and<br>algorithm.                                                                                                                                  |\n\n<span id=\"page-19-3\"></span>Table 2: Field definition for csv active limit upload\n\n### <span id=\"page-19-0\"></span>**5.1 Applying rules and limits to trades**\n\nOnce a deal is negotiated or order is placed, the system determines which rule(s) match(es) and carries out a credit check against the limit within that rule(s). If there are many rules that matches the trade, all of them will be checked and request/order will be allowed if and only if all of the checks passes.\n\n#### <span id=\"page-19-1\"></span>**5.1.1 Reallocating Trades After End of Day Rollover:**\n\nLimits Monitor validates any changes made to Risk Portfolio Rules at New York 5 PM. It then rematches the request/orders with the rules again.\n\nIMPORTANT: Please note as a result of change in risk portfolio rule or change in trades that match with a specific risk portfolio rule due to change in value dates, the utilization amount may exceed the active limit after end of day rollover. For example, if user extends the end of Fx Time Period of a rule from 1 Week to 2 Weeks, GTC orders with tenors between 1 to 2 weeks will also be utilized during the rollover phase and this may result in a higher utilization which may exceed the limit for that specific risk portfolio rule.\n\n### <span id=\"page-19-2\"></span>**5.2 Calculating Utilization**\n\nUtilization shows the amount of the exposure calculated per active rule, based on the captured cashflow (trades) and the assigned algorithm. While filtering parameters (Counterpart, Product, Dealer and Legal Entity) determine which trades should be captured and checked, the algorithm assigned to the risk portfolio is the function which calculates the exposure based on the captured risk events (cashflow derived by a trade). For Trading Limit profile, only the gross notional amount for the current date trades are accounted where as for Per Deal Limit, no utilization calculation is done as the limit is set for each and every single request/orders.\n\nFor Swap and NDS transactions, the notional of swap is calculated and not each leg separately. In case of an uneven swap, the larger notional is taken into account.\n\nFor Block-Trades, net spot amount of all legs is taken into account.\n\nUtilization is updated on the system whenever a trade is booked or an order is placed. In case of placing an order into an Order Book such as 360T SUN, utilization is only updated when there is a match and match is allowed only if the counterpart has been allocated for sufficient limit.\n\nIn order to see the most up-to-date utilization values on the UI, users should click the `Refresh All` button on top of the rule dashboard.\n\n|                            |                    | $\\rightarrow$ |                     |                         |                       |                                       |               |                                          | <b>Refresh All</b> |\n|----------------------------|--------------------|---------------|---------------------|-------------------------|-----------------------|---------------------------------------|---------------|------------------------------------------|--------------------|\n| $\\mathbb Q$                |                    |               |                     |                         |                       |                                       |               |                                          |                    |\n| Rule Id                    | $\\vee$ Counterpart | Portfolio     | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                             | Limit         | <b>Utilization</b>                       |                    |\n| <b>Total Net Limit</b>     | Any                | Any           | Any                 | Any                     | Any                   | <b>Aggregate Net Settlement Limit</b> | 2,000,000,000 | 876,867                                  | 而のめ                |\n| <b>SEB Settlement</b>      | SEB.FRA.DEMO       | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 62,000,000    | 5,618                                    | 面のめ                |\n| SEB PFE                    | SEB, FRA, DEMO     | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 56,500,000    | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 面のめ                |\n| <b>RBS Settlement</b>      | RBS.LND.DEMO       | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 24,000,000    | 67,442                                   | 面のめ                |\n| <b>RBS PFE</b>             | RBS.LND.DEMO       | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 2,000,000     | 36,000                                   | 面のめ                |\n| <b>PEBANK Settlement</b>   | PEBANK_APAC.TEST   | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 40,000,000    | $\\circ$                                  | 面のめ                |\n| PEBANK PFE                 | PEBANK APACTEST    | Any vs G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 34,500,000    | $\\circ$                                  | 而のめ                |\n| <b>COBA Settlement</b>     | COBA.DEMO          | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 29.000.000    | $\\circ$                                  | 面のゆ                |\n| <b>COBA PFE</b>            | COBA.DEMO          | Any vs G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 23.500.000    | $\\bullet$                                | 面のめ                |\n| <b>360TBANK Settlement</b> | 360TBANK.TEST      | Any           | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit            | 73,000,000    | $\\circ$                                  | 面のめ                |\n|                            | 360TBANK.TEST      | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure             | 67.500.000    | $\\circ$                                  | 面のめ                |\n| <b>360TBANK PFE</b>        |                    |               |                     |                         |                       |                                       |               |                                          |                    |\n\n<span id=\"page-20-0\"></span>Figure 21 Monitoring the updated utilization amounts within Active Rules tab.\n\n*Example:*\n\n*Bank A* has defined some risk portfolio rules to limit its counterparty risk for *Counterparty A*, as well as to limit the trading activities of its own dealers.\n\nBank A has executed the below trades with Counterparty A.\n\n| ID | Trade Date | Value<br>Date | Currency Pair | Currency1 | Currency2 | Action | Notional<br>Currency | Notional<br>Amount | Executed<br>Rate |\n|----|------------|---------------|---------------|-----------|-----------|--------|----------------------|--------------------|------------------|\n| 1  | 04.10.2019 | 04.10.2019    | USD/TRY       | USD       | TRY       | Sell   | USD                  | 1,000,000          | 5.6545           |\n| 2  | 04.10.2019 | 04.10.2019    | EUR/USD       | EUR       | USD       | Buy    | EUR                  | 2,000,000          | 1.1105           |\n| 3  | 03.10.2019 | 07.10.2019    | EUR/USD       | EUR       | USD       | Buy    | EUR                  | 2,000,000          | 1.1118           |\n| 4  | 03.10.2019 | 07.10.2019    | CHF/EUR       | EUR       | CHF       | Sell   | EUR                  | 1,000,000          | 1.0911           |\n| 5  | 04.10.2019 | 11.10.2019    | EUR/GBP       | EUR       | GBP       | Buy    | GBP                  | 2,000,000          | 0.8948           |\n| 6  | 04.10.2019 | 11.10.2019    | EUR/USD       | EUR       | USD       | Sell   | USD                  | 3,000,000          | 1.1158           |\n| 7  | 04.10.2019 | 11.10.2019    | GBP/TRY       | GBP       | TRY       | Sell   | GBP                  | 4,000,000          | 7.0258           |\n\nTable 3: Sample trades\n\nBelow 360T EOD Rates are used as reference rate to convert different currency exposure into one single currency (in our example, it is USD).\n\n| Currency Pair | EOD Rates |\n|---------------|-----------|\n| USD/TRY       | 5.6520    |\n| EUR/USD       | 1.1050    |\n| GBP/USD       | 1.2525    |\n| USD/CHF       | 0.9995    |\n| USD/USD       | 1         |\n\n<span id=\"page-21-1\"></span><span id=\"page-21-0\"></span>Table 4: 360T EOD rates to convert Cashflow/Notional Ledgers into USD (credit currency).\n\n### <span id=\"page-22-0\"></span>**5.2.1 Daily Gross Trading Limit**\n\nDaily Gross Trading Limit algorithm limits the total gross volume of trades done on a single trading day. Daily Gross Trading Limit sums the notional amount converted to company currency for all value dates across the credit horizon for current trading date.\n\n**Important Note**: For Swap/NDS, Daily Gross Trading Algorithm only considers the larger amount of both of the legs. For Block-Trades, net amount the legs are considered for calculation.\n\nFor example; Bank A has set below Risk Portfolio Rule for Counterparty A.\n\n| Counterpart | Product       | Dealer       | Legal<br>Entity<br>Group | Algorithm           | Limit      |\n|-------------|---------------|--------------|--------------------------|---------------------|------------|\n| Any         | Spot/Outright | BankA.Trader | Any                      | Daily Gross Trading | 50,000,000 |\n\n*Under the assumption of current date = 04.10.2019 and all trades are Spot/Outright,* the rule would apply to 5 of the trades in Table 1 (#1,2, 5, 6 and 7) and the algorithm calculates the utilization amount as follows:\n\n1,000,000 USD (from *Trade Id1)* + 2,210,000 USD (from *Trade ID2)* + 2,505,000 USD (from *Trade Id5)* + 3,000,000 USD (from *Trade Id6) +* 5,010,000 USD (from *Trade Id7) = 13,725,000 USD*\n\n**Utilization:** *13,725,000 USD* \n\n#### <span id=\"page-22-1\"></span>**5.2.2 Per Deal Limit**\n\nPer Deal Limit only limits the notional of a deal that can be traded and therefore, there is no utilization calculation is relevant. The amount defined as limit does a limit check for each trade based and if notional of the deal > per deal limit, limit check fails.\n\nFor example, if Bank A defined 100m USD per deal limit for its traders, any trade with notional higher than 100 million USD equivalent will fail.\n\nFor a swap and NDS trades, only one leg (in case of uneven swap, the larger amount) is considered. For example, USDJPY Spot – 1 Week swap with 60 million USD amount passes the check for any per deal limit equal or higher than 60 million.\n\nFor Block Trades, net amount of all legs is considered.\n\n**Important Note**: For Swap/NDS, Daily Gross Trading Algorithm only considers the largest amount of both of the legs. For Block-Trades, net amount the legs are considered for calculation.\n\n# <span id=\"page-23-0\"></span>**5.3 Utilization Reset**\n\n360T`s Limits Monitor allows authorized users to reset the limit usage calculation for Daily Net and Gross Trading algorithms. Once a user is enabled for the feature, he/she will be able to click on and restart the calculation rather than waiting for day roll.\n\n| Algorithm                 |             | <b>Utilization</b> |  |  |  |\n|---------------------------|-------------|--------------------|--|--|--|\n| Daily Gross Trading Limit | 100,000,000 | o                  |  |  |  |\n\n<span id=\"page-23-3\"></span>Figure 22 Reset Utilization\n\nOnce the functionality used, all admin users will receive an email which informs them about the action.\n\nTo be enabled for the functionality, please contact 360T CAS.\n\n# <span id=\"page-23-1\"></span>**5.4 Visualization**\n\n#### <span id=\"page-23-2\"></span>**5.4.1 Risk Entries**\n\nRisk Entries tab provides the details of the cashflow (trade legs) matches with a specific risk portfolio rule. Information provided in this tab helps admin users to verify the utilization calculation as it includes necessary information.\n\nTable can be exported as csv by clicking on icon.\n\n|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        | $\\times$ |\n|-------------------|-------------------|-------------|-------------------------|------------------|-------------------------|------------------|-------------------------------|------------|---------------------|----------------------------|---------|--------|----------|\n|                   |                   |             | Daily Limit Usage Table |                  | Daily Limit Usage Graph |                  | <b>Limit Used By Currency</b> |            | <b>Risk Entries</b> | 一支                         |         |        |          |\n|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |\n| <b>Value Date</b> | <b>Trade Date</b> | Product     | <b>Legal Entity</b>     | Counterpart      | Base CCY                | <b>Ouote CCY</b> | Notional CCY Receive CCY      |            | Pay CCY             | Notional Amount Trade Rate |         | Revert |          |\n| 16.10.2020        | 15.10.2020        | <b>SPOT</b> | 360T.RMS                | RBS.LND.DEMO     | <b>USD</b>              | <b>TRY</b>       | <b>USD</b>                    | <b>TRY</b> | <b>USD</b>          | 100.000.00                 | 7.93256 | false  |          |\n| 16 10 2020        | 15 10 2020        | <b>SPOT</b> | 360T.RMS                | PEBANK APAC.TEST | <b>USD</b>              | <b>TRY</b>       | <b>USD</b>                    | <b>TRY</b> | <b>USD</b>          | 123,000.00                 | 7.93667 | false  |          |\n| 19.10.2020        | 15.10.2020        | <b>SPOT</b> | 360T.RMS                | PEBANK_APAC.TEST | <b>EUR</b>              | <b>TRY</b>       | <b>EUR</b>                    | <b>EUR</b> | <b>TRY</b>          | 58,100.00                  | 9.29767 | false  |          |\n|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |\n|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |\n|                   |                   |             |                         |                  |                         |                  |                               |            |                     |                            |         |        |          |\n\n<span id=\"page-23-4\"></span>Figure 23 Risk Entries\n\nBelow you can see the description of the information provided in the risk entries table:\n\n- *i. Value Date:* Displays the effective/settlement date of the cashflow. Table is by default ranked ascending by *Value Date* parameter.\n- *ii. Trade Date:* Displays the date that trade which causes the cashflow is executed.\n- *iii. Product:* Indicates the product type. Please note that, it shows the type of the product that is originally executed i.e. Spot will be displayed even though value date doesn`t indicate a spot date for that specific currency pair any longer.\n- *iv. Legal Entity:* Shows the name of the legal entity for which the credit entity made the transaction for.\n- *v. Counterpart:* Displays 360T system name of the counterparty against which the credit entity dealt.\n\n- *vi. Base CCY:* Displays the base currency for a specific trade/trade intention.\n- *vii. Quote CCY:* Displays the quote currency for a specific trade/trade intention.\n- *viii. Notional CCY:* Displays the currency which specify the amount to be exchanged via the corresponding trade/trade intention.\n- *ix. Receive CCY:* Indicates the currency which credit entity will receive at value date.\n- *x. Pay CCY:* Indicates the currency which credit entity agreed to pay at value date.\n- *xi. Notional Amount:* Shows the notional amount of transaction.\n- *xii. Trade Rate:* Shows the agreed execution rate or limit/stop rate. This information is used to calculate opposite amount of cashflow for net algorithms.\n- **xiii.** *Revert:* Indicates whether the created risk entry is reverted (True) due to nonexecution or a final entry (false). In case of an order which is not fulfilled, Limits Monitor would allocate the exposed amount at the moment of placing or accepting order. This creates a risk entry in the system. However, when this order is withdrawn for ex., negation of the record would also be created by creating the same entry with `Revert = True`.\n\n### <span id=\"page-24-0\"></span>**5.5 Alert Emails**\n\n360T`s Limits Monitor can notify the users who has access to the tool when the utilization of a limit for a certain rule has reached to a certain threshold value.\n\nThe automatically triggered email contains all the parameters of an active rule and aims to help client admins to notice the level of limit usage so that they can closely monitor the limits for risk portfolio rule and when necessary, make an action accordingly.\n\n| Dear Test Admin,                                                                                                                                                                                                                                                  |\n|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| The 75% threshold defined for 164715 has been reached as of 2021-06-28 16:35:32 GMT. In case, you need to take any action, please go to Risk Portfolio/Bridge Administration panel.                                                                               |\n| Rule $ID = 164715$<br>Counterpart = MTBankA<br>$Portfolio = Any$<br>Legal Entity Group = $Any$<br>Execution Method = Any<br>$FX$ Time Period = TODAY - SPOT<br>Algorithm = Net Daily Settlement Limit<br>$Limit = 50,000,000,00$<br>Utilization = $38,359,709.00$ |\n| Note: Please note that, limits and utilizations may have been updated with any further activity after this email is triggered.                                                                                                                                    |\n| For any further questions, please contact 360T Client Advisory Services using the details below.                                                                                                                                                                  |\n| Thank you.                                                                                                                                                                                                                                                        |\n| Client Advisory Services<br>Global email: <EMAIL><br>Global fax: +49 69 900 289 59                                                                                                                                                                           |\n| <b>EMEA</b><br>Business: +49 69 900 289 73                                                                                                                                                                                                                        |\n| AMERICAS<br>Tel: ****** 776 2920                                                                                                                                                                                                                                  |\n| APAC<br>Tel: +65 6325 9973                                                                                                                                                                                                                                        |\n| Grüneburgweg 16-18 / Westend Carrée<br>60322 Frankfurt am Main<br>affiliate support on the state.                                                                                                                                                                 |\n\n<span id=\"page-24-1\"></span>Figure 24 Alert emails\n\nBy default, there are three levels of thresholds for 75, 85 and 95%, although it is possible to set different threshold values. In case you would like to start receiving the alert emails, please contact Client Advisory Services team at [<EMAIL>.](mailto:<EMAIL>)\n\n# <span id=\"page-25-0\"></span>**5.6 Snapshot Reports**\n\n360T`s Limits Monitor can provide the snapshot of the Active Rules as a file in .csv format via the email. The reports are generated twice a day -end of day and beginning of day- and sent only to the admin users who are activated for the reports.\n\nEnd of day reports are generated slightly before day rollover at NY 5 PM and displays the active risk portfolio rules as well as the corresponding limit and utilization values. Beginning of day reports are generated slightly after day rollover is completed after NY 5 PM.\n\nThe reports help clients to\n\n- 1) Monitor if any existing active rule has not available limit mainly as a result of changes due to day rollover.\n- 2) Monitor the limit usage across time by comparing snapshots of different dates.\n\nPlease contact our Client Advisory Services team [\\(<EMAIL>\\)](mailto:<EMAIL>) to start receiving the reports.\n\n# <span id=\"page-26-0\"></span>**6 LIMIT CHECK FLOW**\n\nAs discussed in previous sections, Limits Monitor is able to conduct limit check for all negotiation types for supported products. Due to difference in the flow of different negotiations, at which stage limit check and utilization update is conducted also change. This section will provide more details on the nuances of limit check:\n\n### **RFS:**\n\nLimits Monitor conducts limit check at two stages for RFS negotiation. First limit check is conducted when the negotiation is initiated. At this stage;\n\n- If a limit assigned to a quote provider is not sufficient (i.e. in case of limit check failure) from requester`s point of view, that particular provider is taken out of the bank basket and requests won`t be delivered to them.\n- From provider point of view, if limit against a requester is not sufficient, there can be two different consequence depending on the provider`s preference:\n  - o Request can be routed to dealer intervention with a notification that shows the details of limit breach(es). This will give trader the option to price request manually despite the limit check failure. Please contact [<EMAIL>](mailto:<EMAIL>) for activation.\n\n![](_page_27_Picture_2.jpeg)\n\n<span id=\"page-27-0\"></span>Figure 25 Limit Breach Details in TWS\n\no Provider is taken out of bank basket which means provider cannot receive the request (no option to override limit).\n\nAfter the initial limit check, Limits Monitor conducts another check upon execution attempt initiated by the requester. This ensures that all recent updates during RFS negotiation. In case this last limit check fails, execution will not be allowed and rejected. If limit check passes, trade will be booked and utilization will be updated immediately.\n\n#### **Order:**\n\nFor private book orders, Limits Monitor conducts limit check at order entry stage and reserve utilization immediately as soon as limit check passes and order is accepted by the providers. At this stage;\n\n- If a limit assigned to an order provider is not sufficient (i.e. in case of limit check failure) from requester`s point of view, then order cannot be delivered to the provider and stays in Initialized stage.\n- From provider point of view, if limit against a requester is not sufficient, there can be two different consequence depending on the provider`s preference:\n  - o Order can be routed to dealer intervention with a notification that shows the details of limit breach(es). This will give trader the option to accept order manually despite the limit check failure. Please contact [<EMAIL>](mailto:<EMAIL>) for activation.\n\n|                                                                                                                                         | O<br><b>• Order View Execution</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |\n|-----------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n|                                                                                                                                         | Limit Order<br><b>O</b> GTC                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |\n| is 120,200.<br>is 120,200.                                                                                                              | Manual Intervention required due to Limit breach:<br>. Rule 22175394-ER: Available limit -22,500,000 is insufficient for<br>25.11.2022 for counterpart 360T.RMS. Required free limit for the request<br>· Rule 97047993-ER: Available limit -10,000,000 is insufficient for<br>25.11.2022 for counterpart 360T.RMS. Required free limit for the request<br>. Rule 270504: Available limit 0 is insufficient for 25.11.2022 for<br>counterpart 360T.RMS. Required free limit for the request is 120,200. |\n| <b>Order Details</b>                                                                                                                    | Order Changes                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |\n| <b>Listed Product</b><br>Requester Action<br>Notional Amount<br>Limit Spot Rate<br><b>Effective Date</b><br>Market Rate<br>Counterparts | FX Spot<br>Client Buys RUB / Sells TRY<br>10,000,000.00 RUB<br>0.30880<br>Spot // Fri, 25, Nov 2022<br>0.30860<br>Request Changes                                                                                                                                                                                                                                                                                                                                                                       |\n| Requester Company                                                                                                                       | 360T.RMS                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |\n| Requester Individual                                                                                                                    | 360TRMS.RiskManager                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |\n| Expiry Date<br>Expiry Date - Local<br>Reference #                                                                                       | <b>GTC</b><br>PO-1800144-627                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |\n|                                                                                                                                         | <b>Override Limit</b><br>Reject                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |\n\n<span id=\"page-28-0\"></span>Figure 26 Limit Breach Details in TWS for Orders\n\no Provider is taken out of bank basket which means provider cannot receive the order (no option to override limit).\n\n#### **Streaming:**\n\nIn Streaming flow, limit check conducted continuously and any quote beyond the limit is filtered out/made unavailable for execution by Limits Monitor. Even though, liquidity is monitored and filtered accordingly, another limit check is conducted when order is placed against a quote and in case of failure, execution attempt is rejected.\n\n### **Order Book (M|M, COB):**\n\nFor central order books, Limits Monitor conducts limit check on order book level to ensure that pending orders can only be aggressed by counterparties that is eligible as per either their own limits or their counterpart`s limits. In M|M, when there is no sufficient limit for a certain order in the book, traders can see the liquidity but it would be unavailable for them to trade against.\n\n# <span id=\"page-30-0\"></span>**7 AUDIT LOG**\n\n360T`s Limits Monitor has a live audit log view which records every change done on the configurations by admin users.\n\nAudit Log view can be accessed by clicking on icon next to the Risk Portfolio PFE tab.\n\nThe view has three columns:\n\n- Log Date Time = Timestamp of the change (in GMT).\n- User = The user who has performed the change.\n- Description = The content of the change.\n\nSimilar to other tabs, Audit Log has a search function, by which users can filter the audit log entries and find the changes they want to see easily.\n\n| $Q_1$                   |                     | $\\rightarrow$                                                                                        |\n|-------------------------|---------------------|------------------------------------------------------------------------------------------------------|\n| <b>Log Date Time</b>    | <b>User</b>         | <b>Description</b>                                                                                   |\n| May 20, 2021 6:31:48 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:100000000, Institution:360T.RMS), Active LimmoRule(Id:205870) |\n| May 20, 2021 9:32:59 AM | ********            | Active Limmo rule limit updated (Limit:1000000, Institution:360T.RMS), Active LimmoRule(Id:205870)   |\n| May 16, 2021 1:17:41 PM | 360TRMS.RiskManager | TimePeriodGroup updated (TimePeriodGroup:-1, TimePeriodGroupName:Any), LimmoRule(Id:205869)          |\n| May 16, 2021 1:17:41 PM | 360TRMS.RiskManager | Time period updated (From:TODAY, To:TODAY), LimmoRule(Id:205869).                                    |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:217293)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205866)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205867)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205868)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205869)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205870)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205864)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205865)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205863)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205862)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205861)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205858)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205859)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205860)         |\n| May 9, 2021 7:56:29 PM  | 360TRMS.RiskManager | Limit updated (Limit:50000000), LimmoRule(Id:217293)                                                 |\n| May 9, 2021 7:56:20 PM  | 360TRMS.RiskManager | Algorithm updated (AlgorithmType:Aggregate Gross Settlement Limit), LimmoRule(Id:217293)             |\n\n<span id=\"page-30-1\"></span>Figure 27 Audit Log\n\n# <span id=\"page-31-0\"></span>**8 CONTACT 360T**\n\n#### **Germany**\n\n*360 Treasury Systems AG* Grüneburgweg 16-18 60322 Frankfurt am Main Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Singapore**\n\n*360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756\n\n#### **Middle East**\n\n#### **United Arab Emirates**\n\n*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\n#### **USA**\n\n*360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902\n\n#### **Asia Pacific South Asia**\n\n### **India**\n\n*ThreeSixty Trading Networks (India) Pvt Ltd* Level 8, Vibgyor Towers, G Block, C-62, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4090 7165 Fax: +91 22 4090 7070", "metadata": {"lang": "en"}}]