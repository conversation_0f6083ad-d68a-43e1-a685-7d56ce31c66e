[{"id": "1", "text": "# **USER GUIDE BRIDGE ADMINISTRATION**\n\n![](_page_0_Picture_1.jpeg)\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\nUSER GUIDE\n\nBRIDGE ADMINISTRATION: REGULATORY DATA\n\n© 360 TREASURY SYSTEMS AG, 2023 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION\n\n| 1 |       | INTRODUCTION5                                                  |  |\n|---|-------|----------------------------------------------------------------|--|\n| 2 |       | GETTING STARTED<br>5                                           |  |\n| 3 |       | COMPANY ADMIN CONTROL7                                         |  |\n|   | 3.1   | COMPANY DETAILS<br>7                                           |  |\n|   | 3.1.1 | Company Overview7                                              |  |\n|   | 3.1.2 | Company MTF Details8                                           |  |\n|   | 3.1.3 | Common Entity Details10                                        |  |\n|   | 3.1.4 | Adding or Modifying LEI Details<br>11                          |  |\n|   | 3.1.5 | LEI for Legal Entities12                                       |  |\n|   | 3.2   | USER DETAILS<br>13                                             |  |\n|   | 3.2.1 | Overview<br>13                                                 |  |\n|   | 3.2.2 | Trader MTF Details13                                           |  |\n|   | 3.2.3 | User Download and Upload Functionality<br>15                   |  |\n|   | 3.2.4 | Download MTF Trader Details for EU MTF and Upload for UK MTF16 |  |\n|   | 3.3   | AUTODEALER USER<br>18                                          |  |\n|   | 3.4   | SECURITY AND DATA PROTECTION20                                 |  |\n|   | 3.5   | DATA RETENTION PERIOD<br>20                                    |  |\n| 4 |       | EXTERNAL MAPPING20                                             |  |\n| 5 |       | CREATE AN EXTERNAL INDIVIDUAL<br>24                            |  |\n| 6 |       | ANNEX30                                                        |  |\n|   | 6.1   | NATIONAL CLIENT ID FOR UK<br>MTF30                             |  |\n|   | 6.2   | NATIONAL CLIENT ID FOR EU<br>MTF32                             |  |\n|   | 6.3   | CONCAT<br>FORMAT 34                                            |  |\n| 7 |       | CONTACTING 360T35                                              |  |\n\n## **TABLE OF FIGURES**\n\n| Figure 1 Header Bar6                                                                    |    |\n|-----------------------------------------------------------------------------------------|----|\n| Figure 2 Bridge Administration: Homepage.<br>6                                          |    |\n| Figure 3 Regulatory Data Administration: Start page7                                    |    |\n| Figure 4 Regulatory section: Company Overview.<br>8                                     |    |\n| Figure 5: Company MTF Details section9                                                  |    |\n| Figure 6: Trading Capacity and Investment Decision10                                    |    |\n| Figure 7 Regulatory Data Administration: Common tab<br>11                               |    |\n| Figure 8 Bridge Institution Category: Company Details with LEI field11                  |    |\n| Figure 9 Bridge Institution Category: Submission of a LEI Change Request12              |    |\n| Figure 10 Bridge Institution Category: Approval of a LEI Change Request<br>12           |    |\n| Figure 11: Trader Overview13                                                            |    |\n| Figure 12 Regulatory Data Administration: MTF User Details14                            |    |\n| Figure 13: Trader Overview Download/Upload feature<br>15                                |    |\n| Figure 14: Trader Overview for EU MTF and UK MTF17                                      |    |\n| Figure 15: Selection of venue<br>17                                                     |    |\n| Figure 16: AutoDealer user18                                                            |    |\n| Figure 17: Back2back Trading Capacities<br>19                                           |    |\n| Figure 18: Algo IDs20                                                                   |    |\n| Figure 19 360T MTF Identification of users behind API<br>21                             |    |\n| Figure 20 Bridge Administration Homepage: External Mappings feature<br>21               |    |\n| Figure 21 External Mapping: Create new configuration.<br>22                             |    |\n| Figure 22 External Mapping: Adding external code<br>22                                  |    |\n| Figure 23 External Mapping: Upload of external codes23                                  |    |\n| Figure 24 External Mapping: Modification of external codes<br>24                        |    |\n| Figure 25 Bridge Administration: Help Wizard.<br>25                                     |    |\n| Figure 26 Bridge Administration: Help Wizard Step 1 -<br>Select an Institution.<br>26   |    |\n| Figure 27 Bridge Administration: Help Wizard Step 2 –<br>Individual details.<br>27      |    |\n| Figure 28 Bridge Administration: Help Wizard Step 2 –<br>Individual details completed28 |    |\n| Figure 29 Bridge Administration: Help Wizard Step 3 –<br>Individual details overview.   | 29 |\n\n## **TABLE OF TABLES**\n\n| Table 1: Field description of entity details<br>10          |  |\n|-------------------------------------------------------------|--|\n| Table 2 Field description of user details15                 |  |\n| Table 3: PII differences between EU and UK MTF<br>18        |  |\n| Table 4: Field description of AutoDealer user details<br>19 |  |\n| Table 5: Field description of External user details28       |  |\n\n## <span id=\"page-4-0\"></span>**1 INTRODUCTION**\n\n360T operates two separate Multi-Lateral Trading Facilities (MTFs): \"UK MTF\" (MIC: G360) and \"EU MTF\" (MIC: 360T). The MTF activation process for new MTF participants is identical for both MTFs. Therefore, the term \"360T MTF\" is used throughout this document to refer to both MTFs interchangeably.\n\nAn MTF participant must enter certain Personally Identifiable Information (PII) of its users alongside static data into 360T's systems before being able to start trading on 360T MTF. 360T offers the tools \"Company Admin Control\", \"External Mapping\" and \"Create External Individual\" accessible via the Bridge Administration application for this purpose.\n\nThis manual outlines the how-to steps to enter PII and static data and describes how confidential data is guaranteed.\n\nPlease note: An operator of an MTF must collect the above-mentioned information to fulfill MiFID II reporting requirements. Therefore, trading on 360T MTF is not possible until the necessary data is entered into 360T's systems.\n\nThe Bridge Administration tool is available only to users who have regulatory administrator rights. As part of the onboarding process, a sub-set of administrator users to whom the tool should be accessible must be defined. These users are responsible for entering the information as described in the following chapters. Please contact *<EMAIL>* or your customer relationship manager to request permissions for the relevant administrator users.\n\nMTF participants are enabled for 360T MTF by the 360T CAS team as part of the 360T MTF Onboarding process. The initial activation of individual users for 360T MTF can only be done by the regulatory data administrator of the institution as 360T has no access to the confidential personal data of individual users which is required for MTF activation. Per default, all entities and users are OTC enabled (off-MTF) for MiFID II relevant products: Forwards, Block, Swap, non-SEF NDF, non-SEF NDS and non-SEF Options.\n\n## <span id=\"page-4-1\"></span>**2 GETTING STARTED**\n\nThe Bridge Administration tool can be accessed via the menu option \"Administration\" in the screen header of the Bridge application. Several features may be visible in the left menu depending on user permissions.\n\n|                                                                                                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                            | $\\vee$ Preferences<br>$\\vee$ Help<br>△ Administration                                                                                                                                                                                                                                                                    | $\\bullet$ AA $-\\bullet$ X |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|\n| Change Password<br><b>Bridge Administration</b>                                                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                          | $\\times$                  |\n| Sell EUR<br><b>Buy EUR</b><br>$1.06$ 246<br>$1.06$ 252<br>Spot // 16.10.2023<br>1.06 27 920<br>1.06 28 170<br>2.920<br>2.970<br><b>1 Week</b> // 23.10.2023<br>1.06 37 963<br>1.06 38715<br>13363<br>13515<br>$\\frac{1 \\text{ Month}}{16.11.2023}$<br>1.0716100<br>1.0717300<br>91.500<br>92.000<br>6 Months // 16.04.2024 | Sell GBP<br><b>Buy GBP</b><br>1.23<br>1.23078<br>Spot // 16.10.2023<br>1.2308510<br>1.2307670<br>0.470<br>0.510<br>1 Week // 23.10.2023<br>1.2308849<br>$1.23$ 10 495<br>1.649<br>2.495<br>1 Month // 16.11.2023<br>1.23 2 1 500<br>1.2322800<br>14.400<br>15,000<br>6 Months // 16.04.2024 | Sell EUR<br><b>BUY EUR</b><br>$0.86$ 323<br>$0.86\\overline{334}$<br>Spot // 16.10.2023<br>$0.86$ 35 280<br>0.8634110<br>2.010<br>2.080<br>1 Week // 23.10.2023<br>0.8641559<br>0.8643172<br>9.159<br>9.772<br>$\\frac{1 \\text{ Month}}{16.11.2023}$<br>0.8696100<br>0.8698000<br>63.800<br>64.600<br>6 Months // 16.04.2024 | Sell USD<br><b>Buy USD</b><br>0.90064<br>0.90078<br>Spot // 16.10.2023<br>0.8999820<br>0.9001270<br>$-6.580$<br>$-6.530$<br>1 Week // 23.10.2023<br>0.8976918<br>0.8978705<br>$-29.482$<br>$-29.095$<br>$\\boxed{1$ Month // $16.11.2023$<br>0.8825200<br>0.8826500<br>$-181.500$<br>$-180.700$<br>6 Months // 16.04.2024 |                           |\n| All (0)<br>Executed (0)<br>Cancelled (0)<br>Type<br>Product<br>Reference #<br>*<br>$\\begin{array}{c c} \\hline \\textbf{0} & \\textbf{0} & \\textbf{0} \\end{array}$                                                                                                                                                            | Rejected (0)<br>Done (0)<br>Expired (0)<br>Requester C Provider Co Requester A Notional A                                                                                                                                                                                                   | Q<br>∣,↓,<br>Quote                                                                                                                                                                                                                                                                                                         | Base Curren Quote Curre Effective Date Effective Pe Maturity Date Maturit                                                                                                                                                                                                                                                |                           |\n| 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2                                                                                                                                                                                                                                                                                |                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                            | [EIII T] 08:20:45 GMT (Do, 12. Okt 2023, 10:20 MESZ) // Connected [FFM] ● // Mem: 25.0% of 580 MB GC:0.0%                                                                                                                                                                                                                |                           |\n\n<span id=\"page-5-0\"></span>Figure 1 Header Bar\n\nThe Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools and actions for the user.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\n| <b>RFS REQUESTER</b>                              | <b>BRIDGE ADMINISTRATION</b><br>$+$ |                        | $\\vee$ Administration<br>$\\vee$ Preferences                                                               | $\\vee$ Help                  | $\\bullet$ AA $ \\bullet$ X |  |  |  |\n|---------------------------------------------------|-------------------------------------|------------------------|-----------------------------------------------------------------------------------------------------------|------------------------------|---------------------------|--|--|--|\n| 合                                                 | Configurations                      |                        | Administration Start                                                                                      |                              |                           |  |  |  |\n|                                                   |                                     |                        |                                                                                                           |                              |                           |  |  |  |\n|                                                   | Institution<br><b>Actions</b>       | <b>Regulatory Data</b> | <b>External Mapping</b>                                                                                   | <b>Company Admin Control</b> |                           |  |  |  |\n|                                                   |                                     |                        |                                                                                                           |                              |                           |  |  |  |\n|                                                   | <b>Change Request</b>               | Wizards                |                                                                                                           |                              |                           |  |  |  |\n|                                                   |                                     |                        |                                                                                                           |                              |                           |  |  |  |\n| $\\vec{\\Omega}$<br>$\\frac{1}{\\sigma}$<br>$\\bullet$ |                                     |                        |                                                                                                           |                              |                           |  |  |  |\n| 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2       |                                     |                        | [EEECD T 08:23:09 GMT (Do, 12. Okt 2023, 10:23 MESZ) // Connected [FFM] ● // Mem: 42.0% of 580 MB GC:0.0% |                              |                           |  |  |  |\n\n<span id=\"page-5-1\"></span>Figure 2 Bridge Administration: Homepage.\n\n## <span id=\"page-6-0\"></span>**3 COMPANY ADMIN CONTROL**\n\nThe \"Company Admin Control\" quick link opens a navigation panel which contains an institution tree. The tree includes a list of trade-as, trade-on-behalf or I-TEX entities configured under the main entity.\n\nThe selection of the institutions is done by single-click within the institution tree which opens a new form/sheet with the available details of that entity. The selected item is shown next to the home icon.\n\n|                                                        |                                                                                                                       |                                                                                                                                    |                              |     |  |  | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help                                                    | $\\parallel$ $\\triangleright^1$ 0 AA - 0 X |  |  |\n|--------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------|------------------------------|-----|--|--|---------------------------------------------------------------------------------------------------------|-------------------------------------------|--|--|\n|                                                        |                                                                                                                       | <b>RFS REQUESTER</b>                                                                                                               | <b>BRIDGE ADMINISTRATION</b> | $+$ |  |  |                                                                                                         |                                           |  |  |\n|                                                        | 合<br>$\\mathcal{G}$<br>$\\underline{\\mathbb{F}}_{\\mathbb{Q}}$<br>$\\qquad \\qquad \\textcolor{red}{\\textbf{a}}$<br>$\\circ$ | Q<br>⋒<br>$\\triangleright$ TradeAsG<br>TradeAsG.TAS.B1<br>TradeAsG.TAS.B2<br>$\\frac{1}{11}$ TradeAsG.TAS.B3<br>m TradeAsG.TradeAsG | $\\rightarrow$<br>k           |     |  |  |                                                                                                         |                                           |  |  |\n| $\\begin{array}{c c c c c c c c c c c c c c c c c c c $ |                                                                                                                       |                                                                                                                                    |                              |     |  |  |                                                                                                         |                                           |  |  |\n|                                                        |                                                                                                                       | 1/ TradeAsG.TreasurerA, TradeAsG // QA2                                                                                            |                              |     |  |  | [EECD T08:37:13 GMT (Do, 12. Okt 2023, 10:37 MESZ) // Connected [FFM] ● // Mem: 28.0% of 580 MB GC:0.0% |                                           |  |  |\n\n<span id=\"page-6-3\"></span>Figure 3 Regulatory Data Administration: Start page.\n\nIn the search field , the user can type in an alphanumeric value to find the desired institution.\n\nThe navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.\n\n## <span id=\"page-6-1\"></span>**3.1 Company Details**\n\n#### <span id=\"page-6-2\"></span>**3.1.1 Company Overview**\n\nSelecting the main entity from the institution tree exposes several tabs including a \"Regulatory\" section.\n\n| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                               | $+$                                                                   |                                                                             | $\\vee$ Preferences             | $\\vee$ Administration | $\\sim$ Help           | ⊵'                              | $0$ AA - $0 \\times$ |\n|------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------|-----------------------------------------------------------------------------|--------------------------------|-----------------------|-----------------------|---------------------------------|---------------------|\n| $\\rightarrow$ )<br>$\\overline{\\left( $<br>Q<br>合<br>$\\mathbf{\\hat{m}}$ > TradeAsG                                                  | <b>Company Details</b><br><b>Users</b><br><b>UK MTF</b><br><b>OTC</b> | Regulatory<br>Daughter<br><b>Company Overview</b><br><b>Trader Overview</b> | <b>Order Management Groups</b> | F                     |                       |                                 | とむ                  |\n| $\\mathcal{G}$<br>TradeAsG.TAS.B1<br>$\\underline{\\hat{\\mathbf{m}}}$ TradeAsG.TAS.B2<br>嘔<br>TradeAsG.TAS.B3<br>ft TradeAsG.TradeAsG | Name                                                                  | ΓQ                                                                          |                                | $\\rightarrow$         | <b>UK MTF Enabled</b> |                                 |                     |\n| $\\Box$                                                                                                                             |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n| $\\circ$                                                                                                                            |                                                                       | <b>TradeAsG</b><br>TradeAsG.TAS.B1                                          |                                |                       | true                  | $\\approx$<br>$\\bar{\\hat{\\phi}}$ |                     |\n|                                                                                                                                    |                                                                       | TradeAsG.TAS.B2                                                             |                                |                       | true<br>true          | $\\approx$                       |                     |\n|                                                                                                                                    |                                                                       | TradeAsG.TAS.B3                                                             |                                |                       | true                  | $\\vec{\\sim}$                    |                     |\n|                                                                                                                                    |                                                                       | TradeAsG.TradeAsG                                                           |                                |                       | false                 | $\\phi$                          |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n| $\\frac{\\phi}{D}$                                                                                                                   |                                                                       |                                                                             |                                |                       |                       |                                 |                     |\n|                                                                                                                                    |                                                                       |                                                                             |                                |                       |                       | <b>Discard all Changes</b>      | Save                |\n| $\\bigcirc$<br>$\\bigoplus$                                                                                                          | TradeAsG $\\times$                                                     |                                                                             |                                |                       |                       |                                 |                     |\n| 1/ QA2 TradeAsG.TreasurerA, TradeAsG // QA2                                                                                        |                                                                       | Do, 1 ===================================                                   |                                |                       |                       |                                 |                     |\n\n<span id=\"page-7-1\"></span>Figure 4 Regulatory section: Company Overview.\n\nIn the \"Company Overview\" area within the \"Regulatory\" section, a list of trade-as and tradeon-behalf institutions are shown, alongside the information whether they are MTF enabled or not. In the example of [Figure 4,](#page-7-1) the contractual entity \"TradeAsG\" is a participant of UK MTF. Therefore, its admin users can only view the details of the UK MTF. Participants of EU MTF or SEF see the details related to EU MTF or SEF, respectively.\n\nFor record keeping purposes, the admin user can download this list of entities and their corresponding \"MTF Enabled\" status as a CSV file via the button.\n\n**Important:** Static data **must** be configured for the *main entity*. Click on the arrow button of the main entity to reach the section \"Company MTF Details\" where the necessary configuration must be done, refer to next chapter.\n\n#### <span id=\"page-7-0\"></span>**3.1.2 Company MTF Details**\n\nIn the \"Company MTF Details\" section, the \"Trading Capacity\" as well as the \"Investment decision within firm\" must be set.\n\n|                                                                                                                                                                                                                                                         | $\\odot$<br>AA<br>$-$ 0 $\\times$<br>$\\vee$ Preferences<br>$\\vee$ Administration<br>$\\vee$ Help<br>⊳                                                                                                                                                                                                                                                                                                                    |\n|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                                                                                                                                                    | $+$                                                                                                                                                                                                                                                                                                                                                                                                                   |\n| $\\rightarrow$<br>Q<br>$\\overline{\\left(}$<br>合<br>$\\hat{\\mathbf{m}}$ > TradeAsG<br>$\\mathcal{G}$<br>TradeAsG.TAS.B1<br>$\\underline{\\hat{\\mathbf{m}}}$ TradeAsG.TAS.B2<br>甄<br>血 TradeAsG.TAS.B3<br>fft TradeAsG.TradeAsG<br>$\\mathbf \\Theta$<br>$\\circ$ | <b>Company Details</b><br>Regulatory<br><b>Order Management Groups</b><br><b>Users</b><br><b>Daughter</b><br>₽<br><b>UK MTF</b><br><b>Company Overview</b><br><b>Trader Overview</b><br><b>OTC</b><br><b>Company MTF Details</b><br><b>Trader MTF Details</b><br><b>Common</b><br>TradeAsG<br><b>Company Name</b><br>$\\sqrt{\\bullet}$<br><b>MTF Enabled</b><br><b>AOTC</b><br><b>Trading Capacity</b><br>$\\checkmark$ |\n| 0 <br>Q<br>$\\ominus$                                                                                                                                                                                                                                    | <b>Investment Decision</b><br><b>Invest decision within firm</b><br><b>NONE</b><br>$\\checkmark$<br><b>Defined Individual</b><br>$\\checkmark$<br><b>Discard all Changes</b><br>Save<br>TradeAsG $\\times$                                                                                                                                                                                                               |\n| 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2                                                                                                                                                                                                             | Do, 1 <b>ELECT DE 55 GMT</b> (Do, 12. Okt 2023, 14:33 MESZ) // Connected [FFM] ● // Mem: 29.0% of 580 MB GC:0.0%                                                                                                                                                                                                                                                                                                      |\n|                                                                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                                                                                                                                                       |\n\n<span id=\"page-8-0\"></span>Figure 5: Company MTF Details section\n\nThe table below explains the meaning of the different drop-down fields.\n\n| No. | Field Name                            | Reference                                      | Details                                                                                                                                                                                                                                      |  |  |  |  |\n|-----|---------------------------------------|------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|\n| 1   | Trading<br>Capacity                   | RTS 22, Annex                                  | Drop-down menu consists of the following three values:                                                                                                                                                                                       |  |  |  |  |\n|     |                                       | I,<br>Table<br>2,<br>Field 28                  | - 'DEAL' – dealing on own account                                                                                                                                                                                                            |  |  |  |  |\n|     |                                       |                                                | - 'MTCH' – matched principal trading: a transaction where three<br>conditions must be met:                                                                                                                                                   |  |  |  |  |\n|     |                                       | RTS<br>24,<br>Annex,<br>Table<br>2, Field 7    | - The facilitator interposes between buyer and seller to the<br>transaction in such a way that it is never exposed to market<br>risk                                                                                                         |  |  |  |  |\n|     |                                       |                                                | – Both sides are executed simultaneously (timing)                                                                                                                                                                                            |  |  |  |  |\n|     |                                       |                                                | – The transaction is executed at a price where the facilitator<br>makes no profit or loss, other than a previously disclosed<br>commission, fee or charge of the transaction.                                                                |  |  |  |  |\n|     |                                       |                                                | - 'AOTC' – any other capacity                                                                                                                                                                                                                |  |  |  |  |\n| 2   | Investment<br>decision within<br>firm | RTS 22, Annex<br>I,<br>Table<br>2,<br>Field 57 | This field is only applicable if the Trading Capacity \"DEAL\" is used.                                                                                                                                                                        |  |  |  |  |\n|     |                                       |                                                | It offers two different options:                                                                                                                                                                                                             |  |  |  |  |\n|     |                                       |                                                | -<br>TRADER: The Investment Decision Maker is always the<br>trader executing the transaction<br>-<br>DEFINED_USER: The Investment Decision Maker is a<br>defined user which can be selected from the \"Defined<br>individual\" drop down menu. |  |  |  |  |\n|     |                                       |                                                | When the Trading Capacity \"AOTC\" or \"MTCH\" is used, this field can<br>be set to NONE.                                                                                                                                                        |  |  |  |  |\n| 3   | Defined<br>individual                 | RTS 22, Annex<br>I,<br>Table<br>2,<br>Field 57 | This field is only applicable if the option \"DEFINED_USER\" was<br>selected from the field \"Investment decision within firm\". Select a<br>defined physical person who is considered the Investment Decision<br>Maker for 360T MTF trades.     |  |  |  |  |\n\n<span id=\"page-9-2\"></span>Table 1: Field description of entity details\n\nThe configured fields are shown as default values in the MiFID tab of the different 360T trading applications, refer to [Figure 6.](#page-9-1) It is possible for the trader to overwrite the default values.\n\n|                                          | • Product Definition Competitive Bidding                                                                                                                                                                                                    | ×                                        |\n|------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------|\n|                                          | <b>FX Forward</b>                                                                                                                                                                                                                           |                                          |\n|                                          | <b>Trade As</b><br>TradeAsG.TAS.B1<br>Sell<br>$EUR \\vee$<br>Notional<br>٠<br>⊜<br>USD $\\vee$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br><b>Buy</b><br><b>Effective Date</b><br>雦<br>1 Week<br>Tue, 24.10.2023<br>$\\checkmark$<br>7 Days |                                          |\n| Provider List Transaction                | Comments<br>MIFID                                                                                                                                                                                                                           |                                          |\n| <b>Trading Venue</b>                     | <b>UK_MTF</b>                                                                                                                                                                                                                               |                                          |\n| <b>Trading Capacity</b>                  | <b>DEAL</b>                                                                                                                                                                                                                                 |                                          |\n| <b>Investment Decision</b>               | TradeAsG.TreasurerA                                                                                                                                                                                                                         |                                          |\n| <b>Indicative Value</b><br>1.05<br>L 420 | <b>Request Timeout</b><br>$\\bigodot$ 01:00 $\\bigodot$                                                                                                                                                                                       | Regulatory Disclosures<br>Send<br>Cancel |\n\n<span id=\"page-9-1\"></span>Figure 6: Trading Capacity and Investment Decision\n\n#### <span id=\"page-9-0\"></span>**3.1.3 Common Entity Details**\n\nThe Common tab shows two read-only fields: Investment Firm and LEI.\n\nThe Investment Firm flag indicates whether the entity is an investment firm covered by MIFID II Directive (RTS 22, Annex I, Table 2, Field 6). 360T MTF has a transaction reporting obligation if the MTF participant is not an investment firm (e.g., a corporation). 360T uses this field to determine whether it has a transaction reporting obligation in relation to its capacity as an MTF.\n\nThe Legal Entity Identifier (LEI) is a 20-character, alpha-numeric code based on the ISO 17442 standard developed by the International Organization for Standardization (ISO). It relates to key reference information that enables clear and unique identification of legal entities participating in financial transactions. The LEI must be provided for each entity which should be enabled for MTF. The following chapter describes how a new LEI can be added to the configuration.\n\n|                                                                      |                                                                             | <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b>                                                                                                                                    | $+$                                                                        |                                                                                                             |                                                                                                                   |                       |                      | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help                                                          | ∾                          | $\\bullet$ AA - $\\bullet$ X |\n|----------------------------------------------------------------------|-----------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------|-----------------------|----------------------|---------------------------------------------------------------------------------------------------------------|----------------------------|----------------------------|\n| $\\begin{array}{c c} \\circ & \\circ \\\\ \\circ & \\circ \\end{array}$<br>ø | 合<br>$\\mathcal{G}$<br>嘔<br>$\\qquad \\qquad \\text{ } \\blacksquare$<br>$\\circ$ | $\\rightarrow$ $\\mid$ <<br>ŗα,<br>$\\mathbf{\\hat{m}}$ > TradeAsG<br>fft TradeAsG.TAS.B1<br>$\\underline{\\widehat{\\mathbf{m}}}$ TradeAsG.TAS.B2<br>TradeAsG.TAS.B3<br>fft TradeAsG.TradeAsG | <b>Company Details</b><br><b>OTC</b><br><b>UK MTF</b><br>TradeAsG $\\times$ | <b>Users</b><br><b>Daughter</b><br><b>Company Overview</b><br>Company MTF Details Trader MTF Details Common | <b>Regulatory</b><br><b>Order Management Groups</b><br><b>Trader Overview</b><br><b>UK Investment Firm</b><br>LEI | $\\equiv$<br>$\\bullet$ | 22345678901234567871 |                                                                                                               | <b>Discard all Changes</b> | <b>Save</b>                |\n|                                                                      |                                                                             | TradeAsG.TreasurerA, TradeAsG // QA2                                                                                                                                                    |                                                                            |                                                                                                             |                                                                                                                   |                       |                      | 3. Okt 2023, 12:42:27 GMT (Fr, 13. Okt 2023, 14:42 MESZ) // Connected [FFM] . // Mem: 52.0% of 580 MB GC:0.0% |                            |                            |\n\n<span id=\"page-10-1\"></span>Figure 7 Regulatory Data Administration: Common tab\n\n#### <span id=\"page-10-0\"></span>**3.1.4 Adding or Modifying LEI Details**\n\nA LEI (Legal Entity Identifier) must be added to Company Details tab under the Institution category in Bridge Administration. The respective field \"LEI\" can be found under Company Details in the Overview tab.\n\n| <b>Company Details</b> |                 | Users (3) | <b>Deal Tracking Groups (1)</b> | Daughter (3) | <b>Legal Entities</b> | <b>Entity Groups (1)</b> | <b>TAS/TOB Groups</b>              |  |\n|------------------------|-----------------|-----------|---------------------------------|--------------|-----------------------|--------------------------|------------------------------------|--|\n|                        |                 |           |                                 |              |                       |                          |                                    |  |\n| <b>Overview</b>        | <b>Prefixes</b> |           | Internal trades only            |              |                       |                          |                                    |  |\n|                        |                 |           |                                 |              |                       |                          |                                    |  |\n|                        |                 |           |                                 |              | Company Name *        |                          | TradeForE                          |  |\n|                        |                 |           |                                 |              | Description           |                          |                                    |  |\n|                        |                 |           |                                 |              | Phone Number          |                          |                                    |  |\n|                        |                 |           |                                 |              | Fax Number            |                          |                                    |  |\n|                        |                 |           |                                 |              | Country *             |                          | Germany<br>$\\checkmark$            |  |\n|                        |                 |           |                                 |              | US Person *           |                          | O Disabled                         |  |\n|                        |                 |           |                                 |              | Currency              |                          | EUR<br>$\\checkmark$                |  |\n|                        |                 |           |                                 |              |                       |                          |                                    |  |\n|                        |                 |           |                                 | LEI          |                       |                          |                                    |  |\n|                        |                 |           |                                 |              | Status *              |                          | Institution active<br>$\\checkmark$ |  |\n|                        |                 |           |                                 |              | Provider Role         |                          | O Disabled                         |  |\n|                        |                 |           |                                 |              | <b>Requestor Role</b> |                          | $\\vee$ Enabled                     |  |\n|                        |                 |           |                                 |              |                       |                          |                                    |  |\n|                        |                 |           |                                 |              | Prime Broker          |                          | Disabled<br>$\\bullet$              |  |\n|                        |                 |           |                                 |              | High Frequency Trader |                          | Disabled<br>$\\bullet$              |  |\n|                        |                 |           |                                 |              |                       |                          |                                    |  |\n|                        |                 |           |                                 |              |                       |                          |                                    |  |\n|                        |                 |           |                                 |              |                       |                          |                                    |  |\n\n<span id=\"page-10-2\"></span>Figure 8 Bridge Institution Category: Company Details with LEI field\n\nEntering or modifying a LEI requires submission of a change request (button \"Create Change Request\").\n\n| Company Details<br>Deal Tracking Groups (1)<br>Users (3)<br>Daughter (3) | <b>Legal Entities</b><br>Entity Groups (1) | <b>TAS/TOB Groups</b>          | $9 \\circ \\equiv$            |\n|--------------------------------------------------------------------------|--------------------------------------------|--------------------------------|-----------------------------|\n| Overview<br>Prefixes<br>Internal trades only                             |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          | Company Name *                             | TradeForE                      |                             |\n|                                                                          | Description                                |                                |                             |\n|                                                                          | Phone Number                               |                                |                             |\n|                                                                          | Fax Number                                 | ٠                              |                             |\n|                                                                          | Country *                                  | $\\bigtriangledown$<br>Cermany  |                             |\n|                                                                          | US Person *                                | $\\bullet$ 0 Disabled           |                             |\n|                                                                          | Currency                                   | EUR<br>$\\vee$                  |                             |\n|                                                                          | LEI                                        | 529900P0204W9HA8JP36           |                             |\n|                                                                          | Status *                                   | Institution active<br>$\\vee$ ) |                             |\n|                                                                          | <b>Provider Role</b>                       | O Disabled                     |                             |\n|                                                                          | <b>Requestor Role</b>                      | V Enabled                      |                             |\n|                                                                          | Prime Broker                               | O Disabled                     |                             |\n|                                                                          | High Frequency Trader                      | · Disabled                     |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n|                                                                          |                                            |                                |                             |\n| Create Change Request                                                    |                                            |                                | Discard All Changes<br>save |\n|                                                                          |                                            |                                |                             |\n\n<span id=\"page-11-1\"></span>Figure 9 Bridge Institution Category: Submission of a LEI Change Request\n\nOnce the Change Request has been submitted, it must be approved by a Supervisor or Super Administrator in the Change Request tool in Bridge Administration.\n\n| <b>Change Requests</b>            | <b>HTML Change Requests</b>  |                | Change Requests (1/1) |                     |                                      |               | <b>O</b> Show all                   |\n|-----------------------------------|------------------------------|----------------|-----------------------|---------------------|--------------------------------------|---------------|-------------------------------------|\n| Select Category<br>Solott Company | $\\checkmark$<br>$\\checkmark$ |                |                       |                     |                                      |               | $\\qquad \\qquad \\Longleftrightarrow$ |\n| Filter by CR id                   | Institution                  | <b>Comment</b> |                       | Submitted at        |                                      | <b>Status</b> |                                     |\n| M<br>$CR - 7327$                  | TradeForE                    |                |                       | 05.12.2023 14:25:51 | Submitted by<br>TradeForE.TreasurerA | 0/1           | $\\circ \\circ (\\vee) \\circ \\vDash$   |\n|                                   |                              |                |                       |                     |                                      |               |                                     |\n\n<span id=\"page-11-2\"></span>Figure 10 Bridge Institution Category: Approval of a LEI Change Request\n\nThis approved change request will be automatically submitted with 360T CAS for final review and approval. 360T will confirm the application of the change in writing. The LEI details appear under the Company Details.\n\n#### <span id=\"page-11-0\"></span>**3.1.5 LEI for Legal Entities**\n\nDepending on the setup, the Legal Entities tab may or may not be visible. To add an LEI for a Legal Entity, please complete the above process for each individual entity.\n\nFor updates on multiple entities via bulk upload, please contact the CAS team at [<EMAIL>](mailto:<EMAIL>)\n\n## <span id=\"page-12-0\"></span>**3.2 User details**\n\n#### <span id=\"page-12-1\"></span>**3.2.1 Overview**\n\nSimilarly to \"Company Overview\" tab, \"Trader Overview\" shows which users are enabled for a regulated trading venue (e.g. UK MTF).\n\n|                         |                                                                         |                                           |                        |                                 | $\\vee$ Preferences<br>$\\vee$ Administration                                                                                                                                                                | $\\mathcal{D}^1$<br>$\\vee$ Help | $\\bullet$ AA - $\\bullet$ X |\n|-------------------------|-------------------------------------------------------------------------|-------------------------------------------|------------------------|---------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|----------------------------|\n|                         | <b>RFS REQUESTER</b>                                                    | <b>BRIDGE ADMINISTRATION</b>              | $+$                    |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         | (Q                                                                      | $\\rightarrow$<br>$\\overline{\\phantom{a}}$ | <b>Company Details</b> | <b>Daughter</b><br><b>Users</b> | <b>Order Management Groups</b><br><b>Regulatory</b>                                                                                                                                                        | $\\equiv$                       | 土工                         |\n| 合                       | $\\hat{\\mathbf{m}}$ > TradeAsG                                           | <b>OTC</b>                                | <b>UK MTF</b>          | <b>Company Overview</b>         | <b>Trader Overview</b>                                                                                                                                                                                     |                                |                            |\n| $\\mathcal{G}$           | $\\underline{\\widehat{\\mathbf{m}}}$ TradeAsG.TAS.B1                      |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n| 甄                       | m TradeAsG.TAS.B2<br>$\\underline{\\widehat{\\mathbf{m}}}$ TradeAsG.TAS.B3 |                                           |                        |                                 | $\\Omega$                                                                                                                                                                                                   | $\\rightarrow$                  |                            |\n|                         | m TradeAsG.TradeAsG                                                     |                                           |                        | <b>Name</b>                     |                                                                                                                                                                                                            | <b>UK MTF Enabled</b>          |                            |\n| $\\ominus$               |                                                                         |                                           |                        | TradeAsG.TreasurerA             |                                                                                                                                                                                                            | $\\beta$<br>false               |                            |\n| $\\circ$                 |                                                                         |                                           |                        | TradeAsG.TreasurerB             |                                                                                                                                                                                                            | $\\beta$<br>false               |                            |\n|                         |                                                                         |                                           |                        | TradeAsG.TreasurerC             |                                                                                                                                                                                                            | $\\beta$<br>false               |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n| $\\frac{1}{2}$           |                                                                         |                                           |                        |                                 |                                                                                                                                                                                                            | <b>Discard all Changes</b>     | Save                       |\n| $\\bigcirc$<br>$\\ominus$ |                                                                         | TradeAsG $\\times$                         |                        |                                 |                                                                                                                                                                                                            |                                |                            |\n|                         | 1/ OA2 TradeAsG.TreasurerA, TradeAsG // QA2                             |                                           |                        |                                 | Fr, 13. Okt <a>E<br/> T<br/> MT (Fr, 13. Okt 2023, 06:12 MESZ) // Connected [FFM] <a> (FM] <a> (Mem: 30.0% of 580 MB GC:0.0%</a> (FC:0.0%</a> (FC:0.0%</a> (FC:0.0% (FC:0.0% (FC:0.0% (FC:0.0% (FC:0.0% (F |                                |                            |\n\n<span id=\"page-12-3\"></span>Figure 11: Trader Overview\n\nTo enable a user for MTF trading, click the arrow button . This will open the \"Trader MTF Details\" for the chosen user. Please note this process must be completed for all individual and AutoDealer/API users who require MTF trading.\n\n### <span id=\"page-12-2\"></span>**3.2.2 Trader MTF Details**\n\nAdmin users of 360T MTF participants are required to activate the individual users for 360T MTF and enter their personal details. Please refer to [Figure 12](#page-13-0) and the corresponding field descriptions in [Table 2](#page-14-2) for configuration of a physical user.\n\n| <b>Company Details</b><br><b>Daughter</b><br><b>Regulatory</b><br>$\\equiv$<br><b>Users</b><br>$\\rightarrow$ $\\kappa$<br>Q<br>合<br><b>UK MTF</b><br><b>Company Overview</b><br><b>OTC</b><br><b>Trader Overview</b><br>$\\mathbf{\\hat{m}}$ > TradeAsG<br>$\\mathcal{G}$<br>fil TradeAsG.TAS.B1<br><b>Company MTF Details</b><br><b>Trader MTF Details</b><br>$\\hat{\\mathbb{m}}$ TradeAsG.TAS.B2<br><b>Common</b><br><b>ID</b><br>$\\underline{\\widehat{\\mathbf{m}}}$ TradeAsG.TAS.B3<br><b>Trader MTF Overview</b><br>fil TradeAsG.TradeAsG<br>$\\Box$<br>TradeAsG.TreasurerA<br><b>User Name</b><br>$\\circledcirc$<br>$\\sqrt{\\bullet}$<br><b>MTF Enabled</b><br>First Name*<br>Mack<br>Last Name*<br>John<br>Nationality *<br><b>Czech Republic</b><br><b>Country of Branch</b><br>Germany<br><b>National Client ID</b><br>7360285163<br><b>National Identification Number</b><br><b>Passport Number</b><br><b>CONCAT</b><br>$\\ddot{\\mathbf{r}}$<br>D<br><b>Discard all Changes</b><br>Save | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b> | $\\odot$<br>$AA - CD X$<br>$\\vee$ Preferences<br>$\\vee$ Administration<br>$\\vee$ Help<br>$+$ |\n|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|---------------------------------------------------------------------------------------------|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                                      |                                                                                             |\n| TradeAsG $\\times$<br>$\\ominus$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | $\\bigcirc$                                           |                                                                                             |\n\n<span id=\"page-13-0\"></span>Figure 12 Regulatory Data Administration: MTF User Details\n\n| No. | Field Name               | Reference                                                                                     | Details                                                                                                                                                                                                                                                                                                               |\n|-----|--------------------------|-----------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 1   | First Name               |                                                                                               | The first name is set up as part of the user on-boarding process by<br>360T CAS. This value is NOT editable and shown for reference<br>purposes only.                                                                                                                                                                 |\n| 2   | Last Name                |                                                                                               | The last name is set up as part of the user on-boarding process by<br>360T CAS. This value is NOT editable and shown for reference<br>purposes only.                                                                                                                                                                  |\n| 3   | Nationality              | RTS 22,<br>Annex I, Table<br>2, Field 57 &<br>59<br>RTS 24,<br>Annex, Table<br>2, Field 4 & 5 | Nationality of the user                                                                                                                                                                                                                                                                                               |\n| 4   | Country of the<br>Branch | RTS 22,<br>Annex I, Table<br>2, Field 60                                                      | Field used to identify the country of the branch for the user. If a user<br>is not supervised by a branch, this field should be populated with the<br>country code of the home Member State of the firm OR the country<br>code of the country where the firm has established its head office or<br>registered office. |\n| 5   | National Client<br>ID    | RTS 22,<br>Annex I, Table<br>2, Field 57 &<br>59                                              | 360T, as a trading platform, is required to collect the National Client<br>ID of users.<br>The fields shown in the configuration tool are derived from the<br>nationality of the user as defined in the Annex. The identifier must be<br>assigned in accordance with the priority levels in the Annex. When           |\n\n|  | RTS 24,<br>Annex, Table<br>2, Field 4 & 5 | the configuration tool offers several entry fields for the National Client<br>ID then the first field must be used to define the identifier. If the person<br>does not have the first priority identifier, the second priority identifier<br>should be used, and so forth. |\n|--|-------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n|  |                                           | E.g. 1: idm A is a citizen of Denmark with a personal identity code<br>\"0707781234\", where the first 6 numbers represent the date of birth<br>in \"DDMMYY\" format and the last 4 numbers a sequence number.                                                                 |\n|  |                                           | In this case, idm A would need to enter 0707781234 in the<br>configuration as the first priority identifier.                                                                                                                                                               |\n|  |                                           | E.g. 2: idm B is a citizen of the United Kingdom but does not have a<br>UK National Insurance number.                                                                                                                                                                      |\n|  |                                           | In this case, idm B would need to provide the concatenation<br>(CONCAT) of the birthday, first name and second name in the format<br>as specified in RTS 22, Article 6. The length of the CONCAT string<br>must be 18 characters, refer to 0.                              |\n|  |                                           | For example, 19800512JOHN#KING#                                                                                                                                                                                                                                            |\n|  |                                           | Note: There are slight differences between the National Client IDs<br>that are collected for the EU MTF and UK MTF as outlined in the<br>Annex or Table 3.                                                                                                                 |\n\n<span id=\"page-14-2\"></span>Table 2 Field description of user details\n\n#### <span id=\"page-14-0\"></span>**3.2.3 User Download and Upload Functionality**\n\nEnabling each individual physical user one by one for 360T MTF as described in the previous chapters may be time consuming. The \"Trader Overview\" section offers a download and upload feature to facilitate a simplified user activation process for setups with many users.\n\n|                           |                                       |                                            |                              |                        |                     | $\\vee$ Preferences |                         | $\\vee$ Administration                                           | $\\vee$ Help | $\\mathcal{D}^1$            | $\\odot$   | $AA - Q \\times$ |\n|---------------------------|---------------------------------------|--------------------------------------------|------------------------------|------------------------|---------------------|--------------------|-------------------------|-----------------------------------------------------------------|-------------|----------------------------|-----------|-----------------|\n|                           |                                       | <b>RFS REQUESTER</b>                       | <b>BRIDGE ADMINISTRATION</b> |                        | $+$                 |                    |                         |                                                                 |             |                            |           |                 |\n|                           |                                       | Q                                          | $\\rightarrow$ )              | <b>Company Details</b> |                     | <b>Users</b>       | <b>Daughter</b>         | <b>Regulatory</b>                                               | $\\gg$ =     |                            |           | とむ              |\n|                           | 合                                     | $\\hat{\\mathbf{a}}$ > TradeAsG              |                              | <b>OTC</b>             | <b>UK MTF</b>       |                    | <b>Company Overview</b> | <b>Trader Overview</b>                                          |             |                            |           |                 |\n|                           | $\\mathcal{G}$                         | m TradeAsG.TAS.B1                          |                              |                        |                     |                    |                         |                                                                 |             |                            |           |                 |\n|                           | 蚽                                     | fft TradeAsG.TAS.B2<br>fil TradeAsG.TAS.B3 |                              |                        |                     | Q                  |                         |                                                                 |             | $\\rightarrow$              |           |                 |\n|                           | film TradeAsG.TradeAsG<br>$\\bigoplus$ |                                            |                              | <b>Name</b>            |                     |                    |                         |                                                                 |             | <b>UK MTF Enabled</b>      |           |                 |\n|                           |                                       |                                            |                              |                        | TradeAsG.TreasurerA |                    |                         |                                                                 |             |                            | true      | $\\beta$         |\n|                           | $\\circ$                               |                                            |                              |                        | TradeAsG.TreasurerB |                    |                         |                                                                 |             | false                      | $\\approx$ |                 |\n|                           |                                       |                                            |                              |                        | TradeAsG.TreasurerC |                    |                         |                                                                 |             |                            | false     | $\\beta$         |\n| ☆                         |                                       |                                            |                              |                        |                     |                    |                         |                                                                 |             |                            |           |                 |\n| $\\frac{1}{\\overline{10}}$ |                                       |                                            |                              |                        |                     |                    |                         |                                                                 |             | <b>Discard all Changes</b> |           | Save            |\n| $\\ominus$                 |                                       |                                            |                              | TradeAsG $\\times$      |                     |                    |                         |                                                                 |             |                            |           |                 |\n| $^{\\circ}$                |                                       | TradeAsG.TreasurerA, TradeAsG // QA2       |                              |                        |                     |                    |                         | Sa, 14. Okt 2023, 04:38:17 ==================================== |             |                            |           |                 |\n\n<span id=\"page-14-1\"></span>Figure 13: Trader Overview Download/Upload feature\n\nThe list of users can be downloaded as a CSV file via the download button. It consists of following columns:\n\n- Trader Name\n- First Name\n- Last Name\n- Nationality Country of Branch\n- National Client ID 1\n\n- National Client ID2\n- National Client ID 3\n- User Role\n- MTF Enabled\n\nExample of the content of the downloaded file:\n\nTrader Name,First Name,Last Name,Nationality,Country of Branch,National Client ID 1,National Client ID 2,National Client ID 3,User Role,MTF Enabled\n\nTradeAsG.TreasurerA,John,Mack,CZ,DE,7360285163,,,Treasurer,true\n\nTradeAsG.TreasurerB,Silvia,Jones,,,,,,Treasurer,false\n\nTradeAsG.TreasurerC,Eduardo,Rodriges,,,,,,Treasurer,false\n\nFollowing information must be adjusted in the downloaded CSV file before uploading it again for 360T MTF user activation:\n\n- Nationality Country of Branch\n- National Client ID 1 (if not existent National Client ID 2 etc.)\n- MTF Enabled = true\n\nExample:\n\nTrader Name,First Name,Last Name,Nationality,Country of Branch,National Client ID 1,National Client ID 2,National Client ID 3,User Role,MTF Enabled\n\nTradeAsG.TreasurerB,Silvia,Jones,GB,GB,AB123456D,,,Treasurer,false\n\nTradeAsG.TreasurerC,Eduardo,Rodriges,ES,GB, X2482300W,,,Treasurer,false\n\nPress the upload button and then select the adjusted CSV to activate the given users. Subsequently, a response file can be stored on the local machine which shows whether all records were uploaded successfully.\n\n#### <span id=\"page-15-0\"></span>**3.2.4 Download MTF Trader Details for EU MTF and Upload for UK MTF**\n\nEU MTF Participants who must migrate to UK MTF have to *re-enter* PII trader details for UK MTF. To make this process easier, it is possible to download the PII data of the existing EU MTF users as CSV file and make the necessary adjustments before uploading the file for UK MTF. This process is described further in this sub-chapter.\n\nAdmin users who have access to EU MTF and UK MTF can see in the \"Trader Overview\" section the MTF Enabled status of users for EU MTF and UK MTF.\n\n|                   |               |                                                      |                                                                                                                      |             | $\\vee$ Preferences     |               | $\\vee$ Administration   | $\\vee$ Help | $\\mathcal{D}^1$            | $\\odot$           | $AA - CD X$           |                   |\n|-------------------|---------------|------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------|-------------|------------------------|---------------|-------------------------|-------------|----------------------------|-------------------|-----------------------|-------------------|\n|                   |               | <b>RFS REQUESTER</b>                                 | <b>BRIDGE ADMINISTRATION</b>                                                                                         |             | $+$                    |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   | 合             | Q                                                    | $\\rightarrow$<br>$\\overline{\\left(}$                                                                                 |             | <b>Company Details</b> | <b>Users</b>  | <b>Daughter</b>         | Regulatory  | $\\gg$ =                    |                   |                       | とむ                |\n|                   |               | $\\hat{\\mathbf{a}}$ > TradeAsG                        |                                                                                                                      | <b>OTC</b>  | <b>EU MTF</b>          | <b>UK MTF</b> | <b>Company Overview</b> |             | <b>Trader Overview</b>     |                   |                       |                   |\n|                   | $\\mathcal{G}$ | $\\underline{\\widehat{\\mathbf{m}}}$ TradeAsG.TAS.B1   |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               | fil TradeAsG.TAS.B2                                  |                                                                                                                      |             |                        | Q             |                         |             | $\\rightarrow$              |                   |                       |                   |\n|                   | 鹀             | fil TradeAsG.TAS.B3                                  |                                                                                                                      | <b>Name</b> |                        |               |                         |             | <b>EU MTF Enabled</b>      |                   | <b>UK MTF Enabled</b> |                   |\n|                   | $\\mathbf{r}$  | $\\underline{\\widehat{\\mathbf{m}}}$ TradeAsG.TradeAsG |                                                                                                                      |             | TradeAsG.TreasurerA    |               |                         |             |                            | $\\leftrightarrow$ | false                 | $\\leftrightarrow$ |\n|                   | $\\circ$       |                                                      |                                                                                                                      |             | TradeAsG.TreasurerB    |               |                         |             | true<br>true               | $\\leftrightarrow$ | false                 | $\\beta$           |\n|                   |               |                                                      |                                                                                                                      |             | TradeAsG.TreasurerC    |               |                         | true        | $\\triangleright$           | false             | Þ,                    |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n| ☆<br>D            |               |                                                      |                                                                                                                      |             |                        |               |                         |             |                            |                   |                       |                   |\n| $\\bigcirc$        |               |                                                      |                                                                                                                      |             |                        |               |                         |             | <b>Discard all Changes</b> |                   |                       | Save              |\n| $\\leftrightarrow$ |               |                                                      |                                                                                                                      |             | TradeAsG $\\times$      |               |                         |             |                            |                   |                       |                   |\n|                   |               |                                                      | 16:10 MES2) // Connected [FFM] ● // Mem: 53.0% of 580 MB GC:0.0% // Connected [FM] ● // Mem: 53.0% of 580 MB GC:0.0% |             |                        |               |                         |             |                            |                   |                       |                   |\n\n<span id=\"page-16-0\"></span>Figure 14: Trader Overview for EU MTF and UK MTF\n\nWhen pressing on the download button, a popup appears and requests to select the desired venue:\n\n| <b>Export CSV</b>                                      |\n|--------------------------------------------------------|\n| Please select venue to export                          |\n|                                                        |\n| <b>EU MTF</b><br><b>UK MTF</b><br>Cancel<br><b>SEF</b> |\n\n<span id=\"page-16-1\"></span>![](_page_16_Figure_6.jpeg)\n\nThe \"EU MTF\" option must be selected to download the EU MTF related PII data of users.\n\nNote: There are differences between the PII data that are collected for EU MTF and UK MTF, please refer to the table below:\n\n| Nationality   | National Client Identifiers for<br>EU MTF                                                      | National Client Identifiers for UK<br>MTF          |  |  |  |  |\n|---------------|------------------------------------------------------------------------------------------------|----------------------------------------------------|--|--|--|--|\n| UK            | 1.<br>Passport Number<br>2.<br>CONCAT                                                          | 1.<br>UK National Insurance Number<br>2.<br>CONCAT |  |  |  |  |\n| Liechtenstein | 1.<br>National Passport Number<br>2.<br>National<br>Identity<br>Card<br>Number<br>3.<br>CONCAT | 1.<br>CONCAT                                       |  |  |  |  |\n\n<span id=\"page-17-2\"></span>Table 3: PII differences between EU and UK MTF\n\nTherefore, National Client Identifiers for users with the nationality \"UK\" or \"Liechtenstein\" must be adjusted in the downloaded file.\n\nOnce done, press the upload button, select \"UK MTF\" and select the file to be uploaded.\n\n### <span id=\"page-17-0\"></span>**3.3 AutoDealer User**\n\nIn case an AutoDealer is used for MTF trading, the AutoDealer user must be MTF-enabled. Refer to [Figure 16,](#page-17-1) [Figure 17](#page-18-0) and the corresponding field descriptions in [Table 4.](#page-18-1)\n\n|  |  |  | 1 |  |\n|--|--|--|---|--|\n|  |  |  |   |  |\n|  |  |  |   |  |\n|  |  |  |   |  |\n|  |  |  |   |  |\n|  |  |  |   |  |\n|  |  |  |   |  |\n|  |  |  |   |  |\n|  |  |  |   |  |\n|  |  |  |   |  |\n\n<span id=\"page-17-1\"></span>Figure 16: AutoDealer user\n\n|  |  |  |  |  |  |  | 2 |  |\n|--|--|--|--|--|--|--|---|--|\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  | 3 |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n|  |  |  |  |  |  |  |   |  |\n\n<span id=\"page-18-0\"></span>Figure 17: Back2back Trading Capacities\n\n| No. | Field Name                                 | Details                                                                                                                                                                            |\n|-----|--------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 1   | Trading Capacity PS                        | This value is non-editable and always set to \"DEAL\".                                                                                                                               |\n|     |                                            | If a bank does not provide the Trading Capacity via the Market Maker<br>API then the Trading Capacity of an<br>MTF transaction will<br>automatically default to the value \"DEAL\".  |\n| 2   | Trading Capacity B2B                       | Trading Capacity used for Back2Back trading. This value is only<br>relevant for B2B scenarios where ITEX or OTC trades are hedged on<br>360T MTF.                                  |\n|     |                                            | E.g.: Trading Capacity B2B is set to \"DEAL\".                                                                                                                                       |\n|     |                                            | If an ITEX or OTC transaction is hedged on MTF, 360T will record the<br>Trading Capacity of the MTF transaction as \"DEAL\".                                                         |\n|     |                                            | Note: If the Trading Capacity was set to \"AOTC\" or \"MTCH\", then the<br>client must have a valid LEI configured on our systems. Otherwise,<br>B2B hedging on 360T MTF cannot occur. |\n| 3   | Client-specific<br>Trading<br>Capacity B2B | It is feasible to set a different Trading Capacity B2B for a given client<br>(e.g., when the client has no LEI and the Trading Capacity AOTC or<br>MTCH cannot be used).           |\n\n<span id=\"page-18-1\"></span>Table 4: Field description of AutoDealer user details\n\nPlease note that the autodealer routing must be reconfigured for MTF trading in the 360T Auto Dealing Suite (ADS). The necessary configurations of the ADS are not in scope of this document.\n\nIn the area \"Algo Ids\", a list of whitelisted Algo IDs can be viewed.\n\n|                                                                                                                         |                                                                                    |                 |                                                                                                                                                                                | $\\vee$ Preferences                                                                              | $\\vee$ Administration<br>$\\vee$ Help                                                              | $\\overline{0}$ AA $ \\overline{0}$ X<br>$\\mathcal{D}^1$ |  |\n|-------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------|-----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------|--------------------------------------------------------|--|\n|                                                                                                                         | <b>TRADER WORKSHEET</b>                                                            | <b>NEW VIEW</b> | <b>BRIDGE ADMINISTRATION</b>                                                                                                                                                   | $+$                                                                                             |                                                                                                   |                                                        |  |\n| 合<br>$\\mathcal{G}$<br>甄<br>$\\bigoplus$                                                                                  | Q<br>$\\hat{\\mathbf{a}}$ > BankA<br><b>II</b> BankClientA<br><b>III</b> BankClientB | $\\rightarrow$   | <b>Company Details</b><br><b>Users</b><br><b>Daughter</b><br><b>Company Overview</b><br><b>UK MTF</b><br><b>OTC</b><br><b>Company MTF Details</b><br><b>Trader MTF Details</b> | <b>Regulatory</b><br><b>Trader Overview</b><br><b>Common</b><br><b>Trader MTF Overview</b><br>Q | <b>Order Management Groups</b><br>F<br>Algo IDS<br><b>B2B Trading Capacities</b><br>$\\rightarrow$ |                                                        |  |\n|                                                                                                                         |                                                                                    |                 | <b>Algo IDS</b>                                                                                                                                                                | <b>Description</b><br>Type                                                                      |                                                                                                   | <b>Default</b>                                         |  |\n|                                                                                                                         |                                                                                    |                 | MyAlgo                                                                                                                                                                         | desc<br><b>RFS</b>                                                                              |                                                                                                   | Set as default                                         |  |\n|                                                                                                                         |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 | $\\! + \\!\\!\\!\\!$                                                                                   |                                                        |  |\n|                                                                                                                         |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 |                                                                                                   |                                                        |  |\n| $\\frac{1}{2}$                                                                                                           |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 |                                                                                                   | <b>Discard all Changes</b><br>Save                     |  |\n| $\\ominus$                                                                                                               |                                                                                    |                 | BankA $\\times$                                                                                                                                                                 |                                                                                                 |                                                                                                   |                                                        |  |\n| <sup>1</sup> / BankA.TraderA, BankA // QA2<br>Sa, 14. Okt <a>&gt;<a>&gt;<a><a><a><a><a></a></a></a></a>&lt;</a></a></a> |                                                                                    |                 |                                                                                                                                                                                |                                                                                                 |                                                                                                   |                                                        |  |\n\n<span id=\"page-19-3\"></span>Figure 18: Algo IDs\n\nAn Algorithmic (algo) trading strategy, identified by its algo ID, is required to undergo conformance tests prior to being utilized on 360T MTF. Algos which pass the relevant conformance tests are added by 360T to the Algo ID white-list and are then tradable on 360T MTF.\n\nThe Algo IDs are only applicable for Market Taker APIs or Market Maker APIs.\n\n## <span id=\"page-19-0\"></span>**3.4 Security and data protection**\n\nIndividual confidential data, including Nationality and National Client ID will be recorded and maintained within the 360T datacenters located in Frankfurt, in an encrypted format. As part of the MTF solution, 360T will roll out infrastructure which allows only dedicated internal reporting services to decrypt MTF data. An example of a decryption service would be an \"Extract, Transform, Load\" (ETL) process, responsible for the submission of transaction reports to an \"approved reporting mechanism\" (ARM). Reports will not be saved to permanent storage and will not be backed up.\n\nPlease note that the 360T Client Advisory Services (CAS) team has NO access to the confidential data of MTF customers. The responsibility lies with the MTF participant to ensure that all data entered in the configuration tool is valid.\n\n### <span id=\"page-19-1\"></span>**3.5 Data retention period**\n\nData is stored according to regulatory and legal requirements, including requirements of the EU General Data Protection Regulation. They will be deleted once they are no longer necessary in relation to the purposes for which they were collected or otherwise processed and relevant reporting and archiving periods have elapsed.\n\n## <span id=\"page-19-2\"></span>**4 EXTERNAL MAPPING**\n\nEvery configured user has a 360T specific short code. This short code will be used during the trading workflow to identify the investment decision maker and execution decision maker.\n\nIndividual user details of manual traders who are using a proprietary trading system connected to 360T MTF via an API are also required to be captured in the tool. Such manual traders are then identified by their 360T specific short code via the API, as shown in [Figure 19.](#page-20-0) It is mandatory that user details are provided prior to the usage of the corresponding short codes. In case a short code is received in the execution report with unknown user details, 360T would reject the execution.\n\n![](_page_20_Figure_3.jpeg)\n\n<span id=\"page-20-0\"></span>Figure 19 360T MTF Identification of users behind API\n\nThe External Mapping feature allows to map the above-mentioned external codes with 360T short codes. Additionally, it provides mapping functionality for those liquidity providers who do not intend to maintain 360T specific short codes.\n\n|                                                      |                                     | <i><b>BEL NUICE</b></i> | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help<br>$AA - B \\times$ |\n|------------------------------------------------------|-------------------------------------|-------------------------|-------------------------------------------------------------------------|\n| <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b> | $\\boldsymbol{+}$                    |                         |                                                                         |\n| 合                                                    | Administration Start                |                         |                                                                         |\n|                                                      | Configurations                      |                         |                                                                         |\n|                                                      |                                     | $\\bigodot$              |                                                                         |\n|                                                      | <b>Regulatory Data</b>              | <b>External Mapping</b> |                                                                         |\n|                                                      |                                     |                         |                                                                         |\n|                                                      |                                     |                         |                                                                         |\n|                                                      |                                     |                         |                                                                         |\n|                                                      |                                     |                         |                                                                         |\n|                                                      |                                     |                         |                                                                         |\n|                                                      |                                     |                         |                                                                         |\n| $\\vec{\\zeta}$                                        |                                     |                         |                                                                         |\n| $\\overline{\\mathbf{C}}$                              |                                     |                         |                                                                         |\n| $\\bigcirc$<br>TradeAsG.TreasurerA, TradeAsG // DEMO  | $\\equiv$ $\\equiv$ $\\equiv$ $\\equiv$ |                         | Fr, 17. Nov 2017, 10:47:24 GMT // Connected ·                           |\n\n<span id=\"page-20-1\"></span>Figure 20 Bridge Administration Homepage: External Mappings feature\n\nThe External Mapping configuration can be accessed via the Bridge Administration homepage. Clicking on the shortcut will open the initial view with \"Create New Configuration\" button.\n\n| <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b>                                                                                                                                                                                                                                     | $\\pm$                   |                                                               | $AA - B \\times$<br>$\\vee$ Preferences $\\vee$ Administration $\\vee$ Help                                          |\n|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|---------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------|\n| Q ※<br>$\\overline{\\left( \\right. }%$<br>上三<br><mark>⋒</mark><br>$\\land \\triangleq$ TradeAsG<br>∽<br><sup> TradeAsG.TAS.B1</sup><br><sup> TradeAsG.TAS.B2</sup><br><sup> TradeAsG.TAS.B3</sup><br>$\\qquad \\qquad \\textcircled{\\scriptsize{1}}$<br><sup> medeAsG.TradeAsG</sup><br>$\\circ$ | <b>External Mapping</b> | No configuration available<br><b>Create New Configuration</b> | $\\textcolor{black}{\\textcolor{black}{\\textcircled{\\small\\tt\\odot}}\\hspace{0.1cm}}\\mathop{\\mathbb{R}}\\nolimits\\;$ |\n| ☆<br>$\\mathbb C$<br>$\\bigcirc$<br>TradeAsG.TreasurerA, TradeAsG // DEMO                                                                                                                                                                                                                  | TradeAsG $\\times$       | <b>esun</b>                                                   | Discard all changes<br>Save<br>Fr, 17. Nov 2017, 10:49:05 GMT // Connected ·                                     |\n\n<span id=\"page-21-0\"></span>Figure 21 External Mapping: Create new configuration.\n\nThe admin user can either add external codes individually or mass upload multiple codes using a CSV template.\n\nTo add a code, the user must select the \"Add Code\" icon, enter the external code and select the corresponding 360T short code of the individual.\n\n|                                              |                                                      |                         |                      |                        | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\parallel$ AA - $\\Box$ X |\n|----------------------------------------------|------------------------------------------------------|-------------------------|----------------------|------------------------|--------------------------------------------------------------------------------|\n|                                              | <b>BRIDGE ADMINISTRATION</b><br><b>RFS REQUESTER</b> | $\\boldsymbol{+}$        |                      |                        |                                                                                |\n|                                              | Q<br>$\\overline{\\left( \\right. }%$                   | <b>External Mapping</b> |                      |                        | $\\mathfrak{O} \\curvearrowright \\equiv$                                         |\n| 合                                            | $\\wedge \\triangleq$ TradeAsG                         |                         |                      |                        |                                                                                |\n| $\\mathcal{G}$                                | <sup> TradeAsG.TAS.B1</sup>                          | <u>nazi</u>             |                      |                        |                                                                                |\n|                                              | <sup> madeAsG.TAS.B2</sup>                           |                         | <b>External Code</b> | <b>Individual Name</b> |                                                                                |\n| $\\qquad \\qquad \\textcircled{\\scriptsize{1}}$ | <sup> madeAsG.TAS.B3</sup>                           |                         |                      |                        |                                                                                |\n|                                              | <sup> medeAsG.TradeAsG</sup>                         |                         |                      |                        |                                                                                |\n| $\\overline{\\omega}^*$                        |                                                      |                         |                      |                        | <b>Add Code</b>                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n| $\\stackrel{\\leftrightarrow}{\\sim}$           |                                                      |                         |                      |                        |                                                                                |\n| $\\mathbb C$                                  |                                                      |                         |                      |                        | <b>Discard all changes</b><br>Save                                             |\n| $\\bigcirc$                                   |                                                      | $TradeASG \\times$       |                      |                        |                                                                                |\n|                                              |                                                      |                         |                      |                        |                                                                                |\n|                                              | TradeAsG.TreasurerA, TradeAsG // DEMO                |                         | <b>escript</b>       |                        | Fr, 17. Nov 2017, 10:50:49 GMT // Connected ·                                  |\n\n<span id=\"page-21-1\"></span>Figure 22 External Mapping: Adding external code\n\nTo mass-upload the external short codes, the admin user must download a CSV template including a list of all available users and add the codes to this file. The file can then be uploaded, and the list of mapped users will be immediately visible in the External Mapping tab. The uploaded mappings can be discarded or saved.\n\nBy default, only one mapping table can be created. In case several mapping tables for different interfaces are required, please contact [<EMAIL>](mailto:<EMAIL>) who can create additional tables.\n\n![](_page_22_Picture_4.jpeg)\n\n<span id=\"page-22-0\"></span>Figure 23 External Mapping: Upload of external codes\n\nEach manually entered or uploaded mapping can be modified or deleted.\n\n|                                              |                                                                                                                                      |                         |                      |                        | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help | $AA - B \\times$                                   |\n|----------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|-------------------------|----------------------|------------------------|------------------------------------------------------|---------------------------------------------------|\n|                                              | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                                 | $\\pm$                   |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n| 合                                            | Q 豢<br>$\\frac{1}{2}$ $\\frac{1}{2}$<br>$\\overline{\\left\\langle \\right\\rangle }$                                                       | <b>External Mapping</b> |                      |                        |                                                      | $\\mathcal{A} \\curvearrowright \\mathcal{R} \\equiv$ |\n| $\\mathcal{G}$                                | $\\land \\triangleq$ TradeAsG<br>fradeAsG.TAS.B1                                                                                       | 自工业                     |                      |                        |                                                      |                                                   |\n|                                              | TradeAsG.TAS.B2                                                                                                                      |                         | <b>External Code</b> | <b>Individual Name</b> |                                                      |                                                   |\n| $\\qquad \\qquad \\textcircled{\\scriptsize{1}}$ | <sup> TradeAsG.TAS.B3</sup>                                                                                                          |                         | TradeAsG.Smith       | TradeAsG.TreasurerB    | $ \\mathcal{V} $ O                                    |                                                   |\n| $\\bullet$                                    | <sup> TradeAsG.TradeAsG</sup>                                                                                                        |                         | TradeAsG.Mueller     | TradeAsG.TreasurerA    | y û                                                  |                                                   |\n|                                              |                                                                                                                                      |                         | TradeAsG.Kowalski    | TradeAsG.TreasurerC    | √面<br>$\\vee$                                         |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        | Add Code                                             |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n|                                              |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n| ☆                                            |                                                                                                                                      |                         |                      |                        |                                                      |                                                   |\n| O                                            |                                                                                                                                      |                         |                      |                        | Discard all changes                                  | Save                                              |\n| $\\bigcirc$                                   |                                                                                                                                      | TradeAsG $\\times$       |                      |                        |                                                      |                                                   |\n|                                              | 1/DEMO TradeAsG.TreasurerB, TradeAsG // DEMO<br>$\\equiv$ $\\equiv$ $\\equiv$ $\\equiv$<br>Fr, 17. Nov 2017, 11:45:22 GMT // Connected · |                         |                      |                        |                                                      |                                                   |\n\n<span id=\"page-23-1\"></span>Figure 24 External Mapping: Modification of external codes\n\nIt is mandatory that user details are provided prior to the usage of the corresponding short codes.\n\n## <span id=\"page-23-0\"></span>**5 CREATE AN EXTERNAL INDIVIDUAL**\n\nSection 3.2 describes that admin users of 360T MTF participants are required to enter the personal details of individual users within the Bridge administration tool.\n\nTo capture user-static details each individual acting as either an execution decision maker (EDM) or investment decision maker (IDM) on 360T MTF must have a unique user ID.\n\nA unique user ID is created for an individual depending on the user type:\n\n- (1) **Platform users**: These individuals log in directly to the 360T GUI to access the MTF venue. Platform users receive a unique user ID from 360Ts Client Advisory Services team when access is requested for them via the New User Creation process.\n- (2) **External users**: These individuals are not direct users and do not log in to the 360T GUI. They may still be identified as an EDM or IDM on the 360T MTF venue and/or access the venue via an API. Unique user IDs for these individuals are created by 360T MTF admin users via the \"Create an External Individual\" wizard.\n\nThe 'Create an External Individual' wizard can be accessed via the \"Wizards\" quick link from the Bridge Administration homepage. The Wizards homepage may contain several types of Help Wizards to assist in performing quick actions.\n\n![](_page_24_Picture_2.jpeg)\n\nFigure 25 Bridge Administration: Help Wizard.\n\n<span id=\"page-24-0\"></span>Click 'Create an External Individual' to open the Help Wizard. Step 1 - Please select an Institution. In the event your setup contains multiple entities the main TEX entity will appear first in the Institution Tree. Related entities (trade as, trade-on-behalf, ITEX etc.) will appear below the main TEX entity.\n\nThe user may be configured as an Execution Decision Maker (EDM) or Investment Decision Maker (IDM) of the selected institution. Therefore, care should be taken in selecting the correct entity.\n\nHighlight the Institution and click Next.\n\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     | $\\vee$ Preferences           | $\\vee$ Administration                                | $\\vee$ Help | $A$ $A$ $ B$ $\\times$ |  |\n|---------------|----------------------------------------------|--------------------------------------------------------------------------------------------------------------------|----------------------|----------------------------|------------------------------|-------------------------------------|------------------------------|------------------------------------------------------|-------------|-----------------------|--|\n|               |                                              | <b>RFS REQUESTER</b>                                                                                               | <b>DEAL TRACKING</b> |                            | <b>BRIDGE ADMINISTRATION</b> | $+$                                 |                              |                                                      |             |                       |  |\n|               |                                              | $\\left  \\begin{array}{cc} 1 & 2 \\end{array} \\right\\rangle$ 3 $\\left  \\begin{array}{c} 1 \\end{array} \\right\\rangle$ |                      |                            |                              | Create Individual                   |                              |                                                      |             |                       |  |\n|               | <mark>⋒</mark>                               |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               | $\\mathcal{G}$                                |                                                                                                                    |                      |                            |                              |                                     | Please select an Institution |                                                      |             |                       |  |\n|               | $\\qquad \\qquad \\textcircled{\\scriptsize{1}}$ |                                                                                                                    |                      |                            | Q Search Institution         |                                     |                              |                                                      |             |                       |  |\n|               | $\\bullet$                                    |                                                                                                                    |                      | $\\vee \\mathbf{m}$ TradeAsG |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               | 厚                                            |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               | $\\mathcal{L}_{\\mathcal{N}}^{k}$              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n| $\\frac{1}{2}$ |                                              | Previous                                                                                                           |                      |                            |                              |                                     |                              |                                                      | Cancel      | <b>Next</b>           |  |\n| $\\bigcirc$    |                                              |                                                                                                                    |                      |                            |                              |                                     |                              |                                                      |             |                       |  |\n|               |                                              | 1 TradeAsG.TreasurerA, TradeAsG // DEMO                                                                            |                      |                            |                              | $\\equiv$ $\\equiv$ $\\equiv$ $\\equiv$ |                              | Wed, 18. Jul 2018, 08:54:14 GMT // Connected [FFM] · |             |                       |  |\n\n<span id=\"page-25-0\"></span>Figure 26 Bridge Administration: Help Wizard Step 1 - Select an Institution.\n\nPursuant to RTS 24, 360T must record trade information enriched with user details related to the execution decision maker and investment decision maker. Most personal details and default values for trader-related dynamic fields are entered within the Regulatory Category of the Bridge administration tool.\n\nAs a first step, some basic information is required in order to create the user. Please refer to [Figure 27](#page-26-0) and the corresponding field descriptions in [Table 5.](#page-27-1)\n\n|                                  |                     | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b>                                                              | <b>BRIDGE ADMINISTRATION</b>   | $\\vee$ Preferences<br>$+$             | $\\vee$ Administration $\\vee$ Help | $AA -$<br>Δ                                          | $Q \\times$  |  |  |\n|----------------------------------|---------------------|-----------------------------------------------------------------------------------------------------------|--------------------------------|---------------------------------------|-----------------------------------|------------------------------------------------------|-------------|--|--|\n|                                  | 合                   | $\\left  \\begin{array}{c} 1 \\\\ 2 \\end{array} \\right $ $\\left  \\begin{array}{c} 3 \\\\ 7 \\end{array} \\right $ |                                | Create Individual                     |                                   |                                                      |             |  |  |\n|                                  | $\\mathcal{G}$       |                                                                                                           |                                | Please fill in the Individual details |                                   |                                                      |             |  |  |\n|                                  | $\\bigoplus$         |                                                                                                           | Login Name *                   | TradeAsG<br>$\\vee$ )                  |                                   |                                                      |             |  |  |\n|                                  | $\\circ$             |                                                                                                           | Last Name*                     | Last Name should be specified         |                                   |                                                      |             |  |  |\n|                                  | 厚                   | First Name*                                                                                               | First Name should be specified |                                       |                                   |                                                      |             |  |  |\n|                                  | $\\mathbf{z}_\\infty$ |                                                                                                           | Description                    |                                       |                                   |                                                      |             |  |  |\n|                                  |                     |                                                                                                           | Email *                        |                                       |                                   |                                                      |             |  |  |\n|                                  |                     |                                                                                                           |                                | Email should be specified             |                                   |                                                      |             |  |  |\n|                                  |                     |                                                                                                           | Phone Number*                  | Phone Number should be specified      |                                   |                                                      |             |  |  |\n|                                  |                     |                                                                                                           | Fax Number                     |                                       |                                   |                                                      |             |  |  |\n|                                  |                     |                                                                                                           | Salutation *                   | $MR \\vee$                             |                                   |                                                      |             |  |  |\n|                                  |                     |                                                                                                           | Country *                      | Germany                               | $\\checkmark$                      |                                                      |             |  |  |\n|                                  |                     |                                                                                                           |                                |                                       | * Mandatory                       |                                                      |             |  |  |\n|                                  |                     |                                                                                                           |                                |                                       |                                   |                                                      |             |  |  |\n|                                  |                     |                                                                                                           |                                |                                       |                                   |                                                      |             |  |  |\n| $\\frac{\\alpha}{D}$<br>$\\bigcirc$ |                     | <b>Previous</b>                                                                                           |                                |                                       |                                   | Cancel                                               | <b>Next</b> |  |  |\n|                                  |                     | 1/ TradeAsG.TreasurerA, TradeAsG // DEMO                                                                  |                                | $\\equiv$ $\\equiv$ $\\equiv$ $\\equiv$   |                                   | Wed, 18. Jul 2018, 08:58:20 GMT // Connected [FFM] · |             |  |  |\n\n<span id=\"page-26-0\"></span>Figure 27 Bridge Administration: Help Wizard Step 2 – Individual details.\n\n| No.                                             | Field Name  | Details                                                                                                                                                                                                                                                                                       |  |  |\n|-------------------------------------------------|-------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|\n| 1                                               | Login Name  | Every<br>configured<br>user<br>has<br>a<br>360T<br>specific<br>short<br>code<br>(PREFIX.Lastname). This short code will be used during the trading<br>workflow in order to identify the investment decision maker (IDM) and<br>execution decision maker (EDM).                                |  |  |\n|                                                 |             | The PREFIX can be selected via the dropdown menu if more than<br>one prefix is configured for your entity. The Login Name will be<br>autogenerated based on the user's last name.                                                                                                             |  |  |\n| 2                                               | Last Name   | This field is mandatory and will be included in the reporting data sent<br>to the appropriate Regulatory Authority.                                                                                                                                                                           |  |  |\n| 3                                               | First Name  | This field is mandatory and will be included in the reporting data sent<br>to the appropriate Regulatory Authority.                                                                                                                                                                           |  |  |\n| 4                                               | Description | This field is not mandatory. It is a free text field.                                                                                                                                                                                                                                         |  |  |\n| 5<br>Email                                      |             | This field is mandatory and must contain \"@\".                                                                                                                                                                                                                                                 |  |  |\n|                                                 |             | It is recommended, but not required, that the field contains the user's<br>professional email address. If the user is converted to a platform user<br>360T requires a valid value prior to release of access credentials.                                                                     |  |  |\n| 6<br>Phone Number                               |             | This field is not mandatory.                                                                                                                                                                                                                                                                  |  |  |\n|                                                 |             | Any value entered must begin with \"+\" followed by a country code. It<br>is recommended, but not required, that the field contains the user's<br>professional phone number. If the user is converted to a platform user<br>360T requires a valid value prior to release of access credentials. |  |  |\n| 7<br>Fax Number<br>This field is not mandatory. |             |                                                                                                                                                                                                                                                                                               |  |  |\n|                                                 |             | Any value entered must begin with \"+\" followed by a country code.                                                                                                                                                                                                                             |  |  |\n| 8                                               | Salutation  | This field is mandatory. The field contains three possible values:<br>\"MR\", \"MS\" or \"DR\". \"MR\" is populated by default.                                                                                                                                                                       |  |  |\n\n| 9 | Country | This field is mandatory. The country of the company is populated by<br>default.                                                                                                                                                                                                              |  |  |\n|---|---------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|\n|   |         | This field is for administrative purposes only and does not correspond<br>to the field \"Country of the Branch\" (see Regulatory Category). A<br>helpful warning will be provided if the Country and Phone Number<br>country code do not match. The warning will not prevent user<br>creation. |  |  |\n\n<span id=\"page-27-1\"></span>Table 5: Field description of External user details\n\n| Add values to the mandatory fields and | click Next. |\n|----------------------------------------|-------------|\n|----------------------------------------|-------------|\n\n|                                              |                                                                                                                                                                                                                                                                                |                              | $\\vee$ Preferences                    | $\\vee$ Administration $\\vee$ Help | $\\vert$ $\\Diamond$ AA $-$ O $\\times$                 |\n|----------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|---------------------------------------|-----------------------------------|------------------------------------------------------|\n|                                              | RFS REQUESTER<br><b>DEAL TRACKING</b>                                                                                                                                                                                                                                          | <b>BRIDGE ADMINISTRATION</b> | $^{+}$                                |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |\n| 合                                            | $\\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\right  \\right  \\right  \\right  \\right  \\right  \\right  \\right  \\right  \\right  \\geq \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\left  \\$ |                              | Create Individual                     |                                   |                                                      |\n| $\\mathcal{G}$                                |                                                                                                                                                                                                                                                                                |                              | Please fill in the Individual details |                                   |                                                      |\n| $\\qquad \\qquad \\textcircled{\\scriptsize{1}}$ |                                                                                                                                                                                                                                                                                | Login Name *                 | $\\sqrt{a}$<br>TradeAsG<br>$\\vee$ 1    |                                   |                                                      |\n| $\\bullet$                                    |                                                                                                                                                                                                                                                                                | Last Name *                  | Doe                                   |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                | First Name *                 | John                                  |                                   |                                                      |\n| 厚                                            |                                                                                                                                                                                                                                                                                | Description                  |                                       |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                | Email *                      | <EMAIL>                    |                                   |                                                      |\n| $\\mathcal{R}_{\\chi}$                         |                                                                                                                                                                                                                                                                                | <b>Phone Number*</b>         |                                       |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              | Phone Number should be specified      |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                | <b>Fax Number</b>            |                                       |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                | Salutation *<br>Country *    | $MR \\vee$                             |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              | Germany                               | $\\checkmark$                      |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              |                                       | * Mandatory                       |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |\n|                                              |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |\n| $\\frac{\\phi}{D}$                             |                                                                                                                                                                                                                                                                                |                              |                                       |                                   |                                                      |\n| $\\bigcirc$                                   | <b>Previous</b>                                                                                                                                                                                                                                                                |                              |                                       |                                   | <b>Next</b><br>Cancel                                |\n|                                              | 1 TradeAsG.TreasurerA, TradeAsG // DEMO                                                                                                                                                                                                                                        |                              | <b>EECT</b>                           |                                   | Wed, 18. Jul 2018, 11:18:45 GMT // Connected [FFM] · |\n\n<span id=\"page-27-0\"></span>Figure 28 Bridge Administration: Help Wizard Step 2 – Individual details completed.\n\nBefore creating the user, carefully review the individual details for correctness. Click Create.\n\n|               |                             |                                                           |                                          | $\\vee$ Preferences                   | $\\vee$ Administration                                | $\\vee$ Help | $\\vert$ $\\uparrow$ $\\uparrow$ AA $-$ O $\\times$ |  |\n|---------------|-----------------------------|-----------------------------------------------------------|------------------------------------------|--------------------------------------|------------------------------------------------------|-------------|-------------------------------------------------|--|\n|               |                             | RFS REQUESTER $\\vee$<br><b>DEAL TRACKING</b>              | <b>BRIDGE ADMINISTRATION</b>             | $^{+}$                               |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          | Create Individual                    |                                                      |             |                                                 |  |\n|               | ⋒                           | $\\left  \\frac{1}{2} \\right $ $\\left  \\frac{3}{2} \\right $ |                                          |                                      |                                                      |             |                                                 |  |\n|               | $\\mathcal{G}$               |                                                           |                                          | Overview: Please review your changes |                                                      |             |                                                 |  |\n|               |                             |                                                           | <b>Individual Name</b>                   | TradeAsG.Doe                         |                                                      |             |                                                 |  |\n|               | $\\qquad \\qquad \\textbf{a}$  |                                                           | <b>Last Name</b>                         | <b>Doe</b>                           |                                                      |             |                                                 |  |\n|               | $\\bullet$                   |                                                           | First Name                               | John                                 |                                                      |             |                                                 |  |\n|               |                             |                                                           | <b>Description</b>                       |                                      |                                                      |             |                                                 |  |\n|               | 厚                           |                                                           | Email                                    | <EMAIL>                   |                                                      |             |                                                 |  |\n|               | $\\mathcal{L}_{\\mathcal{K}}$ |                                                           | <b>Phone Number</b><br><b>Fax Number</b> |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           | Salutation                               | Mr.                                  |                                                      |             |                                                 |  |\n|               |                             |                                                           | Position                                 | External                             |                                                      |             |                                                 |  |\n|               |                             |                                                           | Country                                  | Germany                              |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n|               |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n| $\\frac{1}{2}$ |                             |                                                           |                                          |                                      |                                                      |             |                                                 |  |\n| $\\bigcirc$    |                             | <b>Previous</b>                                           |                                          |                                      |                                                      | Cancel      | Create                                          |  |\n|               |                             | TradeAsG.TreasurerA, TradeAsG // DEMO                     |                                          | <b>ascript</b>                       | Wed, 18. Jul 2018, 11:22:53 GMT // Connected [FFM] · |             |                                                 |  |\n\n<span id=\"page-28-0\"></span>Figure 29 Bridge Administration: Help Wizard Step 3 – Individual details overview.\n\n## <span id=\"page-29-0\"></span>**6 ANNEX**\n\n## <span id=\"page-29-1\"></span>**6.1 National Client Id for UK MTF**\n\n| ISO       | Country<br>Name | st priority identifier<br>1                            | nd<br>2<br>priority<br>identifier | rd<br>3<br>priority |\n|-----------|-----------------|--------------------------------------------------------|-----------------------------------|---------------------|\n| 3166<br>– |                 |                                                        |                                   | identifier          |\n| 1         |                 |                                                        |                                   |                     |\n| Alpha 2   |                 |                                                        |                                   |                     |\n|           |                 |                                                        |                                   |                     |\n| AT        | Austria         | CONCAT                                                 |                                   |                     |\n| BE        | Belgium         | Belgian National Number                                | CONCAT                            |                     |\n|           |                 | (Numéro de registre national —<br>Rijksregisternummer) |                                   |                     |\n| BG        | Bulgaria        | Bulgarian Personal Number                              | CONCAT                            |                     |\n| CY        | Cyprus          | National Passport Number                               | CONCAT                            |                     |\n| CZ        | Czech Republic  | National identification number                         | Passport Number                   | CONCAT              |\n|           |                 | (Rodné číslo)                                          |                                   |                     |\n| DE        | Germany         | CONCAT                                                 |                                   |                     |\n| DK        | Denmark         | Personal identity code                                 | CONCAT                            |                     |\n|           |                 | 10 digits alphanumerical:<br>DDMMYYXXXX                |                                   |                     |\n| EE        | Estonia         | Estonian Personal Identification<br>Code               |                                   |                     |\n|           |                 | (Isikukood)                                            |                                   |                     |\n| ES        | Spain           | Tax identification number                              |                                   |                     |\n|           |                 | (Código de identificación fiscal)                      |                                   |                     |\n| FI        | Finland         | Personal identity code                                 | CONCAT                            |                     |\n| FR        | France          | CONCAT                                                 |                                   |                     |\n| GB        | United Kingdom  | UK National Insurance number                           | CONCAT                            |                     |\n| GR        | Greece          | 10 DSS digit investor share                            | CONCAT                            |                     |\n| HR        | Croatia         | Personal Identification Number                         | CONCAT                            |                     |\n|           |                 | (OIB — Osobni identifikacijski<br>broj)                |                                   |                     |\n| HU        | Hungary         | CONCAT                                                 |                                   |                     |\n| IE        | Ireland         | CONCAT                                                 |                                   |                     |\n| IS        | Iceland         | Personal Identity Code (Kennitala)                     |                                   |                     |\n| IT        | Italy           | Fiscal code                                            |                                   |                     |\n\n|                        |                             | (Codice fiscale)                          |                                     |        |\n|------------------------|-----------------------------|-------------------------------------------|-------------------------------------|--------|\n| LI                     | Liechtenstein               | CONCAT                                    |                                     |        |\n| LT                     | Lithuania                   | Personal code                             | National Passport<br>Number         | CONCAT |\n|                        |                             | (Asmens kodas)                            |                                     |        |\n| LU                     | Luxembourg                  | CONCAT                                    |                                     |        |\n| LV                     | Latvia                      | Personal code                             | CONCAT                              |        |\n|                        |                             | (Personas kods)                           |                                     |        |\n| MT                     | Malta                       | National Identification Number            | National Passport<br>Number         |        |\n| NL                     | Netherlands                 | National Passport Number                  | National identity<br>card number    | CONCAT |\n| NO                     | Norway                      | 11-digit personal id                      | CONCAT                              |        |\n|                        |                             | (Foedselsnummer)                          |                                     |        |\n| PL                     | Poland                      | National Identification Number            | Tax Number                          |        |\n|                        |                             | (PESEL)                                   | (Numer identyfikacji<br>podatkowej) |        |\n| PT                     | Portugal                    | Tax number                                | National Passport<br>Number         | CONCAT |\n|                        |                             | (Número de Identificação Fiscal)          |                                     |        |\n| RO                     | Romania                     | National Identification Number            | National Passport<br>Number         | CONCAT |\n|                        |                             | (Cod Numeric Personal)                    |                                     |        |\n| SE                     | Sweden                      | Personal identity number                  | CONCAT                              |        |\n| SI                     | Slovenia                    | Personal Identification Number            | CONCAT                              |        |\n|                        |                             | (EMŠO: Enotna Matična Številka<br>Občana) |                                     |        |\n| SK                     | Slovakia                    | Personal number                           | National Passport<br>Number         | CONCAT |\n|                        |                             | (Rodné číslo)                             |                                     |        |\n| All other<br>countries | National Passport<br>Number | CONCAT                                    |                                     |        |\n|                        |                             |                                           |                                     |        |\n\n## <span id=\"page-31-0\"></span>**6.2 National Client Id for EU MTF**\n\n| ISO<br>3166<br>–<br>1<br>Alpha 2 | Country<br>Name | st priority identifier<br>1                                                       | nd<br>2<br>priority<br>identifier | rd<br>3<br>priority<br>identifier |\n|----------------------------------|-----------------|-----------------------------------------------------------------------------------|-----------------------------------|-----------------------------------|\n| AT                               | Austria         | CONCAT                                                                            |                                   |                                   |\n| BE                               | Belgium         | Belgian National Number<br>(Numéro de registre national —<br>Rijksregisternummer) | CONCAT                            |                                   |\n| BG                               | Bulgaria        | Bulgarian Personal Number                                                         | CONCAT                            |                                   |\n| CY                               | Cyprus          | National Passport Number                                                          | CONCAT                            |                                   |\n| CZ                               | Czech Republic  | National identification number<br>(Rodné číslo)                                   | Passport Number                   | CONCAT                            |\n| DE                               | Germany         | CONCAT                                                                            |                                   |                                   |\n| DK                               | Denmark         | Personal identity code<br>10 digits alphanumerical:<br>DDMMYYXXXX                 | CONCAT                            |                                   |\n| EE                               | Estonia         | Estonian Personal Identification<br>Code<br>(Isikukood)                           |                                   |                                   |\n| ES                               | Spain           | Tax identification number<br>(Código de identificación fiscal)                    |                                   |                                   |\n| FI                               | Finland         | Personal identity code                                                            | CONCAT                            |                                   |\n| FR                               | France          | CONCAT                                                                            |                                   |                                   |\n| GR                               | Greece          | 10 DSS digit investor share                                                       | CONCAT                            |                                   |\n| HR                               | Croatia         | Personal Identification Number<br>(OIB — Osobni identifikacijski<br>broj)         | CONCAT                            |                                   |\n| HU                               | Hungary         | CONCAT                                                                            |                                   |                                   |\n| IE                               | Ireland         | CONCAT                                                                            |                                   |                                   |\n| IS                               | Iceland         | Personal Identity Code (Kennitala)                                                |                                   |                                   |\n| IT                               | Italy           | Fiscal code<br>(Codice fiscale)                                                   |                                   |                                   |\n\n<span id=\"page-32-0\"></span>\n\n| LI                     | Liechtenstein               | National Passport Number                                                    | National Identity<br>Card Number                  | CONCAT |\n|------------------------|-----------------------------|-----------------------------------------------------------------------------|---------------------------------------------------|--------|\n| LT<br>Lithuania        |                             | Personal code<br>(Asmens kodas)                                             | National Passport<br>Number                       | CONCAT |\n| LU                     | Luxembourg                  | CONCAT                                                                      |                                                   |        |\n| LV                     | Latvia                      | Personal code<br>(Personas kods)                                            | CONCAT                                            |        |\n| MT                     | Malta                       | National Identification Number                                              | National Passport<br>Number                       |        |\n| NL                     | Netherlands                 | National Passport Number                                                    | National identity<br>card number                  | CONCAT |\n| NO                     | Norway                      | 11-digit personal id<br>(Foedselsnummer)                                    | CONCAT                                            |        |\n| PL                     | Poland                      | National Identification Number<br>(PESEL)                                   | Tax Number<br>(Numer identyfikacji<br>podatkowej) |        |\n| PT                     | Portugal                    | Tax number<br>(Número de Identificação Fiscal)                              | National Passport<br>Number                       | CONCAT |\n| RO                     | Romania                     | National Identification Number<br>(Cod Numeric Personal)                    | National Passport<br>Number                       | CONCAT |\n| SE                     | Sweden                      | Personal identity number                                                    | CONCAT                                            |        |\n| SI                     | Slovenia                    | Personal Identification Number<br>(EMŠO: Enotna Matična Številka<br>Občana) | CONCAT                                            |        |\n| SK                     | Slovakia                    | Personal number<br>(Rodné číslo)                                            | National Passport<br>Number                       | CONCAT |\n| All other<br>countries | National Passport<br>Number | CONCAT                                                                      |                                                   |        |\n\n## <span id=\"page-33-0\"></span>**6.3 CONCAT format**\n\nThe CONCAT ID has a length of 18 alpha-numerical characters and consists of the following elements in the following order:\n\n- o the date of birth of the person in the format YYYYMMDD;\n- o the first five characters of the first name;\n- o the first five characters of the surname.\n\nTo obtain the first name and surname, following method should be applied:\n\n- Any prefixes to the names that denote titles, position, profession or academic qualifications, are to be removed (e.g. Dr.)\n- Prefixes to surnames that are not included in the below list, or prefixes attached to names, i.e. McDonald, MacChrystal, O'Brian, O'Neal, should not be removed; but note that the apostrophes will be removed in the next step. The below list is not case sensitive:\n\nam, auf, auf dem, aus der, d, da, de, de l', del, de la, de le, di, do, dos, du, im, la, le, mac, mc, mhac, mhíc, mhic giolla, mic, ni, ní, níc, o, ó, ua, ui, uí, van, van de, van den, van der, vom, von, von dem, von den, von der\n\n- Transliteration of special characters:\n  - o diacritics, apostrophes, hyphens, punctuation marks and spaces must be removed.\n  - o special characters such as German \"Umlauts\" or accented characters are translated according to a table into a characters from the range A..Z, e.g. Ä becomes A, ß becomes S\n- Lower case letters must be transformed to capital letters.\n\n| First<br>name(s) | Family<br>name/Surname(s) | Date<br>of<br>birth         | CONCAT             | Comment                                                              |\n|------------------|---------------------------|-----------------------------|--------------------|----------------------------------------------------------------------|\n| George           | O'Brian                   | 17th of June<br>1987        | 19870617GEORGOBRIA | O' is attached to name,<br>not converted. Removed<br>apostrophe.     |\n| Sung-mi          | Ødegård                   | 14th of<br>November<br>1999 | 19991114SUNGMODEGA | Removed hyphen from<br>first name. Converted Ø<br>to O, and å to A   |\n| Anna             | Von der Früht             | 6th of<br>January<br>1977   | 10770106ANNA#FRUHT | Padded Anna to 5<br>characters: Removed<br>prefix. Converted ü to U: |\n\n## <span id=\"page-34-0\"></span>**7 CONTACTING 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n**Germany** *360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Middle East Asia Pacific**\n\n**United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\n**USA** *360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900\n\n**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "metadata": {"lang": "en"}}]