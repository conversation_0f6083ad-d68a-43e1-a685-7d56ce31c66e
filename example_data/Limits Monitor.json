[{"id": "1", "text": "## **USER GUIDE**\n\n![](_page_0_Picture_1.jpeg)\n\n# **LIMITS MONITOR**\n\n© 360 TREASURY SYSTEMS AG, 2022 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG\n\n| 1 |              | INTRODUCTION5                                      |          |\n|---|--------------|----------------------------------------------------|----------|\n| 2 |              | GETTING STARTED7                                   |          |\n| 3 |              | DEFINING RISK PORTFOLIO GROUPS9                    |          |\n| 4 |              | DEFINING RISK PORTFOLIO RULES<br>14                |          |\n|   |              |                                                    |          |\n|   | 4.1          | RULE ID                                            | 15       |\n|   | 4.2          | COUNTERPART                                        | 16       |\n|   | 4.3          | PORTFOLIO                                          | 16       |\n|   | 4.4          | LEGAL ENTITY GROUP<br>                             | 17       |\n|   | 4.5          | DEALER                                             | 18       |\n|   | 4.6          | EXECUTION METHOD<br>                               | 18       |\n|   | 4.7          | FX TIME PERIOD<br>                                 | 19       |\n|   | 4.8          | ALGORITHMS<br>                                     | 20       |\n|   | 4.8.1        | Settlement Limits<br>                              | 21       |\n|   | 4.8.2        | Trading Limits                                     | 22       |\n|   | 4.8.3<br>4.9 | Potential Future Exposure<br><br>LIMIT<br>         | 22<br>22 |\n|   | 4.10         | EDIT RULE<br>                                      | 23       |\n|   | 4.11         | ACTIVATE/DEACTIVATE RULE<br>                       | 23       |\n|   | 4.12         | DELETE RULE<br>                                    | 23       |\n|   | 4.13         | BULK UPLOAD/DOWNLOAD VIA CSV                       | 24       |\n|   |              |                                                    |          |\n| 5 |              | ACTIVE RULES<br>27                                 |          |\n|   | 5.1          | APPLYING RULES AND LIMITS TO TRADES                | 29       |\n|   | 5.1.1        | Reallocating Trades After End of Day Rollover:<br> | 29       |\n|   | 5.2          | CALCULATING UTILIZATION<br>                        | 30       |\n|   | 5.2.1        | Net Daily Settlement Limit                         | 32       |\n|   | 5.2.2        | Gross Daily Settlement Limit                       | 34       |\n|   | 5.2.3        | Aggregate Net Settlement Limit                     | 35       |\n|   | 5.2.4        | Aggregate Gross Settlement Limit                   | 37       |\n|   | 5.2.5        | Potential Future Exposure<br>                      | 37       |\n|   | 5.2.6        | Daily Net Trading Limit<br>                        | 39       |\n|   | 5.2.7        | Daily Gross Trading Limit<br>                      | 41       |\n|   | 5.2.8        | Per Deal Limit                                     | 42       |\n|   | 5.3          | EXCEPTIONAL LIMIT                                  | 42       |\n|   | 5.4          | UTILIZATION RESET                                  | 43       |\n|   | 5.5          | VISUALIZATION<br>                                  | 44       |\n|   | 5.5.1        | Daily Limit Usage Table<br>                        | 45       |\n|   | 5.5.2        | Daily Limit Usage Graph<br>                        | 45       |\n|   | 5.5.3        | Limit Used By Currency<br>                         | 46       |\n|   | 5.5.4        | Risk Entries                                       | 46       |\n|   | 5.5.5        | Pending Order Amount                               | 48       |\n|   | 5.5.6        | Utilization Updates                                | 48       |\n| 6 |              | ALERT EMAILS49                                     |          |\n| 7 |              | SNAPSHOT REPORTS49                                 |          |\n| 8 |              | RISK PORTFOLIO PFE51                               |          |\n|   | 8.1          | CONFIGURING PFE<br>FACTORS                         | 51       |\n|   | 8.2          | BULK UPLOAD/DOWNLOAD VIA CSV                       | 52       |\n| 9 |              | LIMIT CHECK FLOW54                                 |          |\n\n| Figure 1 Limit monitoring in 360T platform<br>6                               |    |\n|-------------------------------------------------------------------------------|----|\n| Figure 2 Header Bar7                                                          |    |\n| Figure 3 Bridge Administration Homepage7                                      |    |\n| Figure 4 Risk Portfolio Homepage8                                             |    |\n| Figure 5 HTML Login8                                                          |    |\n| Figure 6 HTML Access to Limits Monitor9                                       |    |\n| Figure 7 Risk Portfolio Groups<br>10                                          |    |\n| Figure 8 Product Groups<br>10                                                 |    |\n| Figure 9 Currency Couple Groups11                                             |    |\n| Figure 10 Time Period Groups<br>12                                            |    |\n| Figure 11 Porfolios<br>12                                                     |    |\n| Figure 12 Counterpart Groups13                                                |    |\n| Figure 13 Execution Method Groups<br>14                                       |    |\n| Figure 14 Model Type selection for Risk Portfolio Rules15                     |    |\n| Figure 15 Risk Portfolio Rules<br>15                                          |    |\n| Figure 16 Defining Rule ID<br>16                                              |    |\n| Figure 17 Defining counterpart for risk portfolio rules<br>16                 |    |\n| Figure 18 Defining portfolio for risk portfolio rules17                       |    |\n| Figure 19 Defining legal entity for risk portfolio rules<br>18                |    |\n| Figure 20 Defining dealer for risk portfolio rules<br>18                      |    |\n| Figure 21 Defining execution method for risk portfolio rules<br>19            |    |\n| Figure 22 Defining Fx Time Period for risk portfolio rules<br>20              |    |\n| Figure 23 Setting algorithm for risk portfolio rules20                        |    |\n| Figure 24 Defining limit for risk portfolio rules23                           |    |\n| Figure 25 Risk Portfolio Rules Upload/Download<br>24                          |    |\n| Figure 26 Upload Result File<br>24                                            |    |\n| Figure 27 CSV Column separator setting<br>25                                  |    |\n| Figure 28 Updating the limit for current trading date27                       |    |\n| Figure 29 Filtering the rules by using search function28                      |    |\n| Figure 30 Capturing the Risk Entries29                                        |    |\n| Figure 31 Monitoring the updated utilization amounts within Active Rules tab. | 30 |\n| Figure 32 Exceptional Limit<br>42                                             |    |\n| Figure 33 Adding an exceptional limit entry<br>43                             |    |\n\n| Figure 34 Highlighted rule with an exceptional limit<br>43             |  |\n|------------------------------------------------------------------------|--|\n| Figure 35 Reset Utilization<br>43                                      |  |\n| Figure 36 Daily Limit Usage Table45                                    |  |\n| Figure 37 Daily Limit Usage Graph45                                    |  |\n| Figure 38 Limit Used By Currency (Aggregate)<br>46                     |  |\n| Figure 39 Limit Used By Currency (Table)46                             |  |\n| Figure 40 Risk Entries<br>46                                           |  |\n| Figure 41 Pending Order Amount48                                       |  |\n| Figure 42 Utilization changes arisen due API updates48                 |  |\n| Figure 43 Alert emails<br>49                                           |  |\n| Figure 44 Adding multiple PFE tables<br>51                             |  |\n| Figure 45 Assigning PFE table to a risk portfolio<br>51                |  |\n| Figure 46 Risk Portfolio PFE<br>52                                     |  |\n| Figure 47 Adding Currency Couple<br>to Risk Portfolio PFE Matrix<br>52 |  |\n| Figure 48 Add Tenor to Risk Portfolio PFE Matrix<br>52                 |  |\n| Figure 49 Amending PFE value52                                         |  |\n| Figure 50 Risk Portfolio PFE Upload/Download<br>53                     |  |\n| Figure 51 PFE CSV upload results file sample53                         |  |\n| Figure 52 PFE CSV upload file sample<br>53                             |  |\n| Figure 50 Limit Breach Details in TWS55                                |  |\n| Figure 51 Limit Breach Details in TWS for Orders<br>56                 |  |\n| Figure 52 Audit Log58                                                  |  |\n\n| Table 1: Field Definition for Currency Couple Upload12                                       |    |\n|----------------------------------------------------------------------------------------------|----|\n| Table 2 Field Definition for csv risk portfolio rule upload27                                |    |\n| Table 3: Field definition for csv active limit upload29                                      |    |\n| Table 4: Trades executed between Counterparty A and Bank A as of 04.10.2019                  | 31 |\n| Table 5: 360T EOD rates to convert Cashflow/Notional Ledgers into USD (credit<br>currency)31 |    |\n| Table 6: Trades converted into cashflow.<br>33                                               |    |\n| Table 7: Daily net cashflow ledger33                                                         |    |\n| Table 8: Daily net cashflow ledger converted into credit currency33                          |    |\n| Table 9: Daily payable and receivable exposure.<br>33                                        |    |\n| Table 10: Daily net settlement risk exposure<br>33                                           |    |\n| Table 11: Daily gross settlement exposure in terms of credit currency<br>34                  |    |\n| Table 12: Aggregated cashflow ledger.<br>36                                                  |    |\n| Table 13: Aggregated cashflow ledger converted into credit currency USD36                    |    |\n\n| Table 14: Aggregated receivable and payable exposures.<br>37              |  |\n|---------------------------------------------------------------------------|--|\n| Table 15: Risk Portfolio PFE Table38                                      |  |\n| Table 16: PFE Value calculation for each risk entry.<br>39                |  |\n| Table 17: Trades translated into cashflow.<br>40                          |  |\n| Table 18: Aggregated cashflow ledger.<br>40                               |  |\n| Table 19: Aggregated cashflow ledger converted into credit currency USD41 |  |\n| Table 20: Aggregated receivable and payable exposures.<br>41              |  |\n\n### <span id=\"page-4-0\"></span>**1 INTRODUCTION**\n\nThis user guide describes new limit management feature of 360T and its administration panel Risk Portfolio in which the admin users can assign their limits on risk portfolios referring to set of trading parameters.\n\nThe purpose of the tool is to provide 360T clients a rule-based and parametrized limit monitoring which ensures that the risks resulting from permissioned trading relationships do not exceed specified limits.\n\nRisk portfolios have many parameters i.e currency pair, tenor, Counterparty And product type etc.; risk exposure can be measured using a number of different algorithms.\n\nIn order to set limits on specific segments of your trading portfolio that are important with respect to the types of risk you face, you can define Risk Portfolios based on individual or groups of related counterparties or internal accounts/entities, on value dates, products and currency pairs. The criteria and steps to define these risk portfolios are described in detail in this user guide.\n\nDefined *risk portfolio*s can overlap and do not necessarily have to be mutually exclusive. As illustrated in *Figure 1*, once enabled for a specific entity, limit system will allocate the trades to the corresponding risk portfolios. The exposure resulted from the allocated trades is calculated based on the defined algorithm for that specific risk portfolio.\n\n![](_page_5_Figure_2.jpeg)\n\n<span id=\"page-5-0\"></span>Figure 1 Limit monitoring in 360T platform\n\n360T`s new limit functionality currently captures all *FX Spot, FX Forward, FX Swap, NDF, NDS, Block FX Forward, Block NDF, FX Time Option as well as Metals Outright, Metals Spread and Metals Quarterly Strips* trades, negotiated as RFS, Streaming, MidMatch or Order and initiated through different 360T applications such as *Bridge* , *SST* or *EMS, as well as taker API interfaces.* \n\nThe allocation of trades to the risk portfolios is done based on the defined parameters of Risk Portfolio rules which is explained in Section 3 and Section 4 in detail.\n\nThe tool allows clients to define whether a trade which does not fall into any of the defined Risk Portfolios should basically be allowed or not, by a generic configuration in the initial onboarding. If we consider *Figure 1* as the risk universe of the company which has been activated for limit monitor, the trade intentions / orders which cannot be allocated to any of the clusters can be either\n\n- Allowed (Risk Portfolios to be configured as *Constraints*)\n- Or disallowed (Risk Portfolios to be configured as *Permissions*).\n\nThe detailed explanation on *Constraints* vs *Permissions* can be found on [section 4.](#page-13-0)\n\nAll limits defined for the Risk Portfolios are in credit currency<sup>1</sup> . The conversion of trade amounts is done using the end-of-day rates of the 360T Essential Data Feed.\n\nIt is important to note that only users with corresponding user rights are able to administer the Risk Portfolio within Bridge. Please contact [<EMAIL>](mailto:<EMAIL>) or your customer relationship manager for setting up the administrator rights.\n\n<sup>1</sup> Credit currency (also referred to as company currency or home currency) is a single currency defined for 360T entity accounts in the initial onboarding stage. This parameter can be changed by CAS teams upon request.\n\n### <span id=\"page-6-0\"></span>**2 GETTING STARTED**\n\nLimits Monitor can be administrated manually via Risk Portfolio module within the Bridge Administration tool as well as automatically via API. Bridge Administration can be accessed via the menu option \"Administration\" in the screen header of the Bridge application.\n\n|                              | $\\vee$ Preferences | $\\sim$ Administration | $\\vee$ Help $\\big \\bigcirc^1$ 0 AA - 0 X |  |  |  |\n|------------------------------|--------------------|-----------------------|------------------------------------------|--|--|--|\n|                              |                    |                       |                                          |  |  |  |\n| Change Password              |                    |                       |                                          |  |  |  |\n| <b>Bridge Administration</b> |                    |                       |                                          |  |  |  |\n|                              |                    |                       |                                          |  |  |  |\n|                              |                    |                       |                                          |  |  |  |\n|                              |                    |                       |                                          |  |  |  |\n|                              |                    |                       |                                          |  |  |  |\n|                              |                    |                       |                                          |  |  |  |\n\n<span id=\"page-6-1\"></span>Figure 2 Header Bar\n\nThe Bridge Administration feature opens to a homepage with shortcuts to several different administration tools and actions for the particular user.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\nThe Risk Portfolio administration panel is opened using the Risk Portfolio button on the Bridge Administration homepage.\n\n![](_page_6_Picture_9.jpeg)\n\nFigure 3 Bridge Administration Homepage\n\n<span id=\"page-6-2\"></span>The \"Risk Portfolio\" icon opens a navigation panel which shows the institution tree. Depending on the setup, the tree may include a single entity or several entities if the user`s entity has trade-as, trade-on-behalf or other ITEX entities configured under the main entity.\n\n![](_page_7_Picture_2.jpeg)\n\nFigure 4 Risk Portfolio Homepage\n\n<span id=\"page-7-0\"></span>A single-click on the entity name opens the Risk Portfolio panel. By default the page will have no configuration defined.\n\nFor users who wants to access the tool via their internet browser, there is also an HTML SSO login available (For activation, please contact Client Advisory Services Team).\n\nAfter multi-factor authentication, user will be able to see all trading applications as well as Self-Service portal, which is the administration panel on HTML.\n\n![](_page_7_Picture_7.jpeg)\n\nFigure 5 HTML Login\n\n<span id=\"page-7-1\"></span>Clicking in Self-Service Portal icon will direct user to the 360T Self Service Portal Start Page where all administration panels user has access is displayed. Clicking on LIMMO icon will launch the Limits Monitor administration panel.\n\n![](_page_8_Picture_2.jpeg)\n\nFigure 6 HTML Access to Limits Monitor\n\n<span id=\"page-8-1\"></span>IMPORTANT: Please note that clients with several legal or sub-entities within 360T system will see all of the related entities on the left-hand side menu. However, risk portfolio rules should be created for the credit entity which needs to assign the credit lines to its counterparts and be part of the trades (i.e. main entity).\n\nLimits Monitor has two different profiles which differentiates the functions made available to the users on entity level:\n\n\"Trading Limits\" profile grants access to part of the configuration parameters such as product, counterpart, dealer as well as groups of these fields and trading limit algorithms.\n\n\"Full Profile\" grants full access to the tool. Please contact [<EMAIL>](mailto:<EMAIL>) or your customer relationship manager if you are interested in getting onboarded with full access.\n\n### <span id=\"page-8-0\"></span>**3 DEFINING RISK PORTFOLIO GROUPS**\n\nRisk Portfolio Groups facilitate the management of parameters that can be used in risk portfolio rule definition. This allows admin users to have a clear overview of the risk categories they wish to create and makes the limit definition process more efficient by giving users the ability to combine several values into one field i.e. currency one and currency two. It also simplifies the management of parameters as any change to a risk portfolio group affects each associated risk portfolio rule.\n\n![](_page_9_Picture_2.jpeg)\n\nFigure 7 Risk Portfolio Groups\n\n<span id=\"page-9-0\"></span>Risk Portfolio Groups consist of the following parameters:\n\n• *Product Groups*: Includes FX Spot, FX Forward, FX Swap, NDF, NDS, FX Time Option, Block-Trades, Metals Outrights, Metals Spreads and Metal Quarterly Strips.\n\n|                                                                                                                                           | <b>ORDER MANAGEMENT</b><br><b>RFS REQUESTER</b>                                      |                              | <b>TRADER WORKSHEET</b>                                             | <b>BRIDGE ADMINISTRATION</b>                         | $+$                                            |               |                        |                                                                |                         | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ O AA - $\\ominus \\times$ |  |                     |                                                                  |\n|-------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------|------------------------------|---------------------------------------------------------------------|------------------------------------------------------|------------------------------------------------|---------------|------------------------|----------------------------------------------------------------|-------------------------|-------------------------------------------------------------------------------------|--|---------------------|------------------------------------------------------------------|\n| 合                                                                                                                                         | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\\rightarrow$<br><b> 令 &gt; 360T.RMS</b> | <b>Risk Portfolio Groups</b> | Product Groups Currency Couple Groups Time Period Groups Portfolios | Risk Portfolio Rules Active Rules Risk Portfolio PFE |                                                | $\\equiv$      |                        | Legal Entity Groups Counterpart Groups Execution Method Groups |                         |                                                                                     |  |                     |                                                                  |\n| $\\mathfrak{S}$<br>$\\mathcal{I}_{\\Box}$                                                                                                    | 盒 360T.RMS.TAS1                                                                      |                              |                                                                     |                                                      |                                                | $\\alpha$      |                        |                                                                | $\\rightarrow$           |                                                                                     |  |                     |                                                                  |\n| $\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\overline{\\over$ |                                                                                      |                              |                                                                     |                                                      | Group Name<br>Deliverable<br>Non-Deliverables  |               |                        |                                                                | <b>VB</b><br>$ v $ :    |                                                                                     |  |                     |                                                                  |\n|                                                                                                                                           |                                                                                      |                              |                                                                     |                                                      | Spot/Outright                                  |               |                        |                                                                | $\\mathcal{V} \\parallel$ |                                                                                     |  |                     |                                                                  |\n|                                                                                                                                           |                                                                                      |                              |                                                                     |                                                      | <b>Fx Spot</b><br><b>Fx Forward</b><br>Fx Swap | Available (5) |                        | <b>NDF</b><br><b>NDS</b>                                       | Selected (2)            |                                                                                     |  |                     |                                                                  |\n|                                                                                                                                           |                                                                                      |                              |                                                                     |                                                      | <b>Block-Trade</b><br><b>Fx Time Option</b>    |               |                        |                                                                |                         |                                                                                     |  |                     |                                                                  |\n|                                                                                                                                           |                                                                                      |                              |                                                                     |                                                      |                                                |               | $\\,$<br>$\\langle$<br>× |                                                                |                         |                                                                                     |  |                     |                                                                  |\n|                                                                                                                                           |                                                                                      |                              |                                                                     |                                                      |                                                |               | $\\gg$<br>$\\ll$         |                                                                |                         |                                                                                     |  |                     |                                                                  |\n|                                                                                                                                           |                                                                                      |                              |                                                                     |                                                      |                                                |               |                        |                                                                |                         |                                                                                     |  |                     |                                                                  |\n| $\\ddot{\\zeta}$                                                                                                                            |                                                                                      |                              |                                                                     |                                                      |                                                |               |                        |                                                                |                         |                                                                                     |  |                     |                                                                  |\n| $\\frac{\\mathsf{D}}{\\mathsf{D}}$<br>Θ                                                                                                      | 1 360TRMS.RiskManager, 360T.RMS // INT                                               | $360T.RMS \\times$            |                                                                     |                                                      |                                                | <b>BECT</b>   |                        |                                                                |                         |                                                                                     |  | Discard all Changes | Sare<br>Wed, 21. Apr 2021, 10:07:43 GMT // Connected [FFM] @ INT |\n\n<span id=\"page-9-1\"></span>Figure 8 Product Groups\n\n• *Currency Couple Groups:* It is possible to group currency pairs which then can be used as part of *Portfolio* configuration. For example, an admin user wants to permit only G10 vs Local currency trades for some of their counterparts. They can define a currency couple group as G10 against the local currency of the company, for example TRY, and then include this group in their Risk Portfolio rules and define limits against it.\n\n*Currency Couple Group Creation via Manual CSV Upload: C*urrency couple groups can be created or updated via csv upload functionality.\n\n|       |                              |                               |                              |                                          |                            |                    |                                  | $\\vee$ Preferences $\\vee$ Administration | $\\vee$ Help                                       | $\\bullet$ AA $-$<br>$\\times$<br>ு |\n|-------|------------------------------|-------------------------------|------------------------------|------------------------------------------|----------------------------|--------------------|----------------------------------|------------------------------------------|---------------------------------------------------|-----------------------------------|\n| EMENT |                              | <b>TRADER WORKSHEET</b>       | <b>BRIDGE ADMINISTRATION</b> | $+$                                      |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       | <b>Risk Portfolio Groups</b> | <b>Risk Partfolio Rules</b>   | <b>Active Rules</b>          | <b>Risk Portfolio PFE</b>                | $\\equiv$                   |                    |                                  |                                          |                                                   | 土土                                |\n|       | <b>Product Groups</b>        | <b>Currency Couple Groups</b> | <b>Time Period Groups</b>    | Partfolios                               | <b>Legal Entity Groups</b> | Counterpart Groups | <b>Execution Method Groups</b>   |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          | <b>Bulk Upload and</b>                            |                                   |\n|       |                              |                               |                              |                                          | ΓQ                         |                    | $\\rightarrow$                    |                                          | Download via CSV                                  |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              | G10 V CAD                                |                            |                    | $\\frac{1}{2}$                    |                                          |                                                   |                                   |\n|       |                              |                               |                              | G10 vs G10                               |                            |                    | $\\nu$ t                          |                                          |                                                   |                                   |\n|       |                              |                               |                              | G10 vs Local                             |                            |                    | $\\mathcal{V}$ $\\hat{\\mathbb{D}}$ |                                          |                                                   |                                   |\n|       |                              |                               |                              | GCC vs Major                             |                            |                    | $\\nu$ t                          |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    | Create currency                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    | currency group                   |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    | manually.                        |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          |                            |                    |                                  |                                          | Discard all Changes                               | Save                              |\n|       | 360T.RMS $\\times$            |                               |                              |                                          |                            |                    |                                  |                                          |                                                   |                                   |\n|       |                              |                               |                              |                                          | <b>Expert Co</b>           |                    |                                  |                                          | Fri 30 Ant 2021 14:59:23 CMT // Connected (EEM) @ | <b>Date</b>                       |\n\n<span id=\"page-10-0\"></span>Figure 9 Currency Couple Groups\n\nFor a successful upload, csv should contain below three columns which are separated by \";\". Please see an example below:\n\n```\nGroupName;BaseCcy;QuoteCcy\nG10 V CAD;AUD;CAD\nG10 V CAD;CHF;CAD\nG10 V CAD;DKK;CAD\nG10 V CAD;EUR;CAD\nG10 V CAD;GBP;CAD\nG10 V CAD;HKD;CAD\nG10 V CAD;JPY;CAD\nG10 V CAD;SGD;CAD\nG10 V CAD;USD;CAD\n```\n\nPlease note that, upload function for currency couple group works with snapshot strategy when the composition of an existing group is updated. This means, the latest data uploaded for a group name becomes valid as a whole, once saves the changes. **Therefore, when users want to change the composition of an existing currency couple group, it is recommended to download the existing configuration as csv, do the changes on the downloaded file and then upload it,**.\n\n| Field Name         | Type   | Possible<br>Values            | Description                                                                                                                                                                                  |  |  |  |\n|--------------------|--------|-------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|\n| GroupName          | String | All<br>Characters<br>allowed. | Defines the name of the currency<br>couple<br>group.<br>If<br>there<br>is<br>an<br>existing group with the specified<br>group name, composition of group<br>is changed by the latest upload. |  |  |  |\n| BaseCcy            | String | CCY ISO Code                  | Defines the base currency.                                                                                                                                                                   |  |  |  |\n| QuoteCcy<br>String |        | CCY ISO Code                  | Defines the quote currency.                                                                                                                                                                  |  |  |  |\n\n<span id=\"page-11-2\"></span>Table 1: Field Definition for Currency Couple Upload\n\n• *Time Period Groups*: The credit horizon can be pre-defined via Time Period Groups, and the group can then be associated with any risk portfolio rule.\n\n![](_page_11_Picture_5.jpeg)\n\nFigure 10 Time Period Groups\n\n<span id=\"page-11-0\"></span>• *Portfolios*: Product groups and currency couple groups can be combined via Portfolios.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b>   | <b>Active Rules</b>       | <b>Risk Portfolio PFE</b> | $\\equiv$                   |                           |                                |                        |\n|------------------------------|-------------------------------|---------------------------|---------------------------|----------------------------|---------------------------|--------------------------------|------------------------|\n| <b>Product Groups</b>        | <b>Currency Couple Groups</b> | <b>Time Period Groups</b> | Portfolios                | <b>Legal Entity Groups</b> | <b>Counterpart Groups</b> | <b>Execution Method Groups</b> |                        |\n|                              |                               |                           |                           |                            |                           |                                |                        |\n|                              |                               |                           |                           | Q                          |                           | $\\rightarrow$                  |                        |\n|                              |                               |                           |                           |                            |                           |                                |                        |\n|                              |                               |                           | <b>Group Name</b>         |                            |                           |                                |                        |\n|                              |                               |                           | Any vs G10                |                            |                           |                                | y 6                    |\n|                              |                               |                           | Outright G10              |                            |                           |                                | \" ■                    |\n|                              |                               |                           | SpotForward v Local       |                            |                           |                                | ショ                     |\n|                              |                               |                           |                           |                            |                           |                                |                        |\n|                              |                               |                           | Product                   |                            | <b>Currency Couple</b>    |                                |                        |\n|                              |                               |                           | Any                       |                            | G10 vs G10                |                                | $\\widehat{\\mathbb{U}}$ |\n|                              |                               |                           |                           |                            |                           |                                |                        |\n|                              |                               |                           |                           |                            |                           |                                |                        |\n|                              |                               |                           |                           |                            | $+$                       |                                |                        |\n|                              |                               |                           |                           |                            |                           |                                |                        |\n| .                            |                               |                           |                           |                            |                           |                                |                        |\n\n<span id=\"page-11-1\"></span>Figure 11 Porfolios\n\n- *Legal Entity Groups*: An entity or entities (i.e. in case trades are executed on behalf of several legal entities) can be defined within *Legal Entity Groups.*\n- *Dealer Groups:* Single or multiple dealers of an entity can be grouped together via Dealer Groups. All active users of an entity who is allowed to trade will appear in the list.\n- *Counterpart Groups*: Where admin users would like to set a single credit line for several counterparts (either based on credit rating, country risk or related entities etc.), they can create a counterpart group and add multiple counterparts into that group. The available members are determined based on the permissioned trading relationship.\n\n|                             |                                                 |                                                                            |                                                      |                                   | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\begin{array}{ c c c c }\\hline \\blacktriangleright^1 & \\circledast & \\mathrm{A}\\mathtt{A} & = & \\circledcirc & \\times \\\\\\hline \\end{array}$ |\n|-----------------------------|-------------------------------------------------|----------------------------------------------------------------------------|------------------------------------------------------|-----------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n|                             | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b> | <b>TRADER WORKSHEET</b><br><b>BRIDGE ADMINISTRATION</b>                    | $\\ddot{\\phantom{1}}$                                 |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n| 侖                           | $\\rightarrow$<br>$\\Omega$                       | Risk Portfolio Groups Risk Portfolio Rules Active Rules Risk Portfolio PFE | E                                                    |                                   |                                                                                                                                                                                                   |\n|                             | $\\hat{\\mathcal{C}}$ > 360T.RMS                  | Product Groups Currency Couple Groups<br><b>Time Period Groups</b>         | Portfolios Legal Entity Groups<br>Counterpart Groups | <b>Execution Method Groups</b>    |                                                                                                                                                                                                   |\n| $\\mathcal{L}_{\\mathcal{T}}$ | 盒 360T.RMS.TAS1                                 |                                                                            | 360T Group Related                                   | $ v $ i                           |                                                                                                                                                                                                   |\n| 亟                           |                                                 |                                                                            | Rating C+                                            | $\\nu$ 0                           |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n| $\\overline{\\phantom{a}}$    |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | $\\alpha$                                             | $\\rightarrow$                     |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | Available (7)                                        | Selected (4)                      |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | <b>BNPP.PAR.DEMO</b><br><b>BOALDEMO</b>              | <b>MOCK-TEST</b><br>360TBANK.TEST |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | <b>CAGLAR.TEST</b>                                   | <b>CAGLARMTF.TEST</b>             |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | <b>COBA.DEMO</b>                                     | 360T.MMC                          |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | PEBANK_APAC.TEST<br>RBS.LND.DEMO                     |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | <b>SEB.FRA.DEMO</b>                                  |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | $\\,$                                                 |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | $\\zeta$                                              |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | $\\gg$                                                |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            | $\\ll$                                                |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n|                             |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n| 亞                           |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n| $\\mathbb C$                 |                                                 |                                                                            |                                                      |                                   | <b>Discard all Changes</b>                                                                                                                                                                        |\n| $\\circ$                     |                                                 |                                                                            |                                                      |                                   |                                                                                                                                                                                                   |\n| G                           |                                                 | $360T.RMS \\times$                                                          |                                                      |                                   |                                                                                                                                                                                                   |\n|                             | 1 360TRMS.RiskManager, 360T.RMS // INT          |                                                                            | <b>BECT</b>                                          |                                   | Mon, 03. May 2021, 15:03:46 GMT // Connected [FFM] @ INT                                                                                                                                          |\n\n<span id=\"page-12-0\"></span>Figure 12 Counterpart Groups\n\n• *Execution Method Groups*: This parameter defines the trading venue(s) where the trade is executed or negotiated. It includes all 360T trading facilities including RFS, SEP, OMT, MidMatch, HST Orderbook, HST Engine and GTX CLOB.\n\n|                       | <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b>   | <b>Active Rules</b>       | <b>Risk Portfolio PFE</b>               | $\\equiv$            |                           |                                 |                                                                     |                                                      |             |\n|-----------------------|------------------------------|-------------------------------|---------------------------|-----------------------------------------|---------------------|---------------------------|---------------------------------|---------------------------------------------------------------------|------------------------------------------------------|-------------|\n| <b>Product Groups</b> |                              | <b>Currency Couple Groups</b> | <b>Time Period Groups</b> | Portfolios                              | Legal Entity Groups | <b>Counterpart Groups</b> | <b>Execution Method Groups</b>  |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         | $\\overline{a}$      |                           | $\\rightarrow$                   |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           | Group Name                              |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           | MidMatch<br><b>Streaming</b>            |                     |                           |                                 | $\\mathbb{V} \\left  \\right. \\mathbb{B}$<br>$\\overline{\\mathbb{Z}}$ o |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           | OMT                                     | Available (3)       |                           | Selected (4)<br><b>RFS</b>      |                                                                     |                                                      |             |\n|                       |                              |                               |                           | <b>HST OrderBook</b><br><b>GTX CLOB</b> |                     |                           | <b>SEP</b><br><b>HST Engine</b> |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           | MidMatch                        |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     | $\\,$                      |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     | $\\,$ $\\,$                 |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     | $\\gg$                     |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     | $\\ll$                     |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         |                     |                           |                                 |                                                                     | <b>Discard all Changes</b>                           | <b>Save</b> |\n| 360T.RMS $\\times$     |                              |                               |                           |                                         |                     |                           |                                 |                                                                     |                                                      |             |\n|                       |                              |                               |                           |                                         | $\\blacksquare$      |                           |                                 |                                                                     | Mon, 03. May 2021, 15:06:56 GMT // Connected [FFM] . | INT         |\n\n<span id=\"page-13-1\"></span>Figure 13 Execution Method Groups\n\n### <span id=\"page-13-0\"></span>**4 DEFINING RISK PORTFOLIO RULES**\n\nTo enable market participants to allocate limits in a granular fashion, *360T`s Limits Monitor* gives risk managers a tool to segment their trading portfolio into Risk Portfolios that accurately reflect risks in the way they wish to manage them. By combining different risk parameters such as counterparty, value date, currency pair and product within a Risk Portfolio, limits can be defined for many different types of exposure.\n\nAs explained in the [Introduction,](#page-4-0) Risk Portfolio rules can be set either as\n\n- `*Permission*` which means the defined rules give permission to trade. No rule means no permission to trade. Any order or request which does not fall into any of the defined risk portfolios will be prevented/blocked/rejected.\n- Or as `*Constraint*`, which means the defined rules set conditions for the intended requests or orders. In this configuration, no rule means no constraint, i.e. free to trade. In contrast to the \"permissioned\" rules, any order or request to trade which does not fall into any of the defined risk portfolios can be traded without limitation.\n\n**IMPORTANT: Please note that the configuration of the Risk Portfolio as `Permission` or `Constraint` will be done by the 360T CAS team in the initial enablement process and will be valid for all Risk Portfolio rules.**\n\n| Q         |                            |                      | $\\rightarrow$ |                                        |                     |                         |    |                                     | Constraints<br><b>Permissions</b>                    |               | $\\wedge$   | V Rules Switch |\n|-----------|----------------------------|----------------------|---------------|----------------------------------------|---------------------|-------------------------|----|-------------------------------------|------------------------------------------------------|---------------|------------|----------------|\n| Enabled   | <b>Rule Id</b>             | $\\vee$ Counterpart   |               | Portfolio                              | <b>Legal Entity</b> | <b>Execution Method</b> |    | <b>Fx Time Period</b>               | $\\sqrt{\\phantom{a}}$ Constraints<br>Algorithm        |               |            |                |\n| $\\sim$    | <b>Total Net Limit</b>     | Anv                  |               | $\\frac{1}{2}$ Any                      | $\\frac{1}{2}$ Any   | $\\mathbb{Z}$ Any        |    | $\\frac{1}{2}$ Any<br>$\\overline{z}$ | Aggregate Net Settlement Limit = /     2.000.000.000 |               |            |                |\n| Ø         | <b>SEB Settlement</b>      | SEB.FRA.DEMO         | ₹∕            | Any                                    | $5/2$ Any           | $\\frac{1}{2}$ Any       |    |                                     | TODAY-3 MONTHS T/ Net Daily Settlement Limit         | $\\bar{z}$     | 62.000.000 |                |\n| <b>OD</b> | SEB PFE                    | SEB.FRA.DEMO         |               | $\\frac{1}{2}$ Any vs G10 $\\frac{1}{2}$ | Any                 | $\\frac{1}{2}$ Any       |    | V TODAY-1 MONTH V                   | Potential Future Exposure                            | $\\frac{1}{2}$ | 56,500,000 |                |\n| Ø         | <b>RBS Settlement</b>      | RBS.LND.DEMO         | ₹∕            | ₩<br>Any                               | Any                 | ₹ Any                   |    | ジ TODAY-3 MONTHS ジ                  | Net Daily Settlement Limit                           | ₩             | 24.000.000 |                |\n| $\\bullet$ | <b>RBS PFE</b>             | RBS.LND.DEMO         |               | $\\frac{1}{2}$ Any vs G10 $\\frac{1}{2}$ | Any                 | $\\frac{2}{3}$ Any       |    | V TODAY-1 MONTH V                   | Potential Future Exposure                            | $\\frac{1}{2}$ | 2,000,000  |                |\n| Ø         | <b>PEBANK Settlement</b>   | PEBANK_APAC.TEST     |               | $\\bar{z}$<br>Anv                       | Any                 | $\\frac{1}{2}$ Any       |    | V TODAY-3 MONTHS ジ                  | Net Daily Settlement Limit                           | $\\bar{z}$     | 40.000.000 |                |\n| $\\bullet$ | <b>PEBANK PFE</b>          | PEBANK_APAC.TEST     |               | Any vs G10 5 Any                       |                     | $\\frac{1}{2}$ Any       |    |                                     | 5⁄ TODAY-1 MONTH 5⁄ Potential Future Exposure        | $\\frac{1}{2}$ | 34,500,000 |                |\n| Ø         | <b>COBA Settlement</b>     | COBA.DEMO            | $\\bar{z}$     | ₩<br>Any                               | Any                 | $\\mathcal{V}$ Any       |    | V TODAY-3 MONTHS ジ                  | Net Daily Settlement Limit                           | ₹             | 29,000,000 |                |\n| $\\bullet$ | <b>COBA PFE</b>            | COBA.DEMO            |               | ₹ Any vs G10 ₹ Any                     |                     | $\\frac{1}{2}$ Any       |    |                                     | 5⁄ TODAY-1 MONTH 5⁄ Potential Future Exposure        | $\\frac{1}{2}$ | 23,500,000 |                |\n| ØO        | <b>360TBANK Settlement</b> | 360TBANK.TEST        | ₹∕            | ₩<br>Any                               | Any                 | $\\frac{1}{2}$ Any       | ₹∕ | TODAY-3 MONTHS =                    | Net Daily Settlement Limit                           | $\\frac{1}{2}$ | 73,000,000 |                |\n| Ø         | <b>360TBANK PFF</b>        | <b>360TBANK TEST</b> |               | $\\frac{1}{2}$ Any vs G10 $\\frac{1}{2}$ | Any                 | $\\mathbb{Z}/$ Any       |    | V TODAY-1 MONTH V                   | Potential Future Exposure                            | $\\frac{1}{2}$ | 67.500.000 |                |\n| <b>KO</b> | 360T.MMC Settlement        | 360T.MMC             | $\\bar{z}$     | ₩<br>Any                               | Any                 | $\\frac{1}{2}$ Any       |    | V TODAY-3 MONTHS V                  | Net Daily Settlement Limit                           | ₹∕            | 51.000.000 |                |\n| $\\bullet$ | 360T.MMC PFE               | 360T.MMC             |               | ₹ Any vs G10 ₹                         | Any                 | $\\frac{y}{x}$ Any       |    |                                     | 5⁄ TODAY-1 MONTH 5⁄ Potential Future Exposure        | $\\frac{1}{2}$ | 45,500,000 |                |\n\n<span id=\"page-14-1\"></span>Figure 14 Model Type selection for Risk Portfolio Rules\n\nWith a single-click on button, a new rule will be added as a row with nine parameters shown as separate columns, to be defined by the admin user.\n\nIMPORTANT: Please note that adding, removing or amending a rule will be effective immediately *after end of day rollover*. This occurs at 5:00 PM New York Time. Limits can be updated intraday with an immediate effect within the Active Rules tab.\n\nThe switch button on the left of each rule is used to enable or disable the rule, mitigating the need to remove rules and the requirement to redefine should they be needed again.\n\nThe other parameters of the risk portfolio rules will be described in the following subsections in detail.\n\n| Q         |                          |                      | $\\rightarrow$  |                                        |                     |   |                         |               |                       | Constraints                                                       | Rules Switch<br>$\\vee$ |   |\n|-----------|--------------------------|----------------------|----------------|----------------------------------------|---------------------|---|-------------------------|---------------|-----------------------|-------------------------------------------------------------------|------------------------|---|\n| Enabled   | <b>Rule Id</b>           | $\\wedge$ Counterpart |                | Portfolio                              | <b>Legal Entity</b> |   | <b>Execution Method</b> |               | <b>Fx Time Period</b> | Algorithm                                                         | Limit                  |   |\n| ∞         | 206263                   | Any                  |                | $\\frac{1}{2}$ Any                      | $\\frac{1}{2}$ Any   |   | $\\frac{1}{2}$ Any       |               | $\\frac{1}{2}$ Any     | ジ   Gross Daily Settlement Limit ジ   (0)                          |                        | Û |\n| ∞         | 360T.MMC PFE             | 360T.MMC             |                | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ | Any                 |   | $\\mathcal{V}$ Any       |               |                       | ジ TODAY-1 MONTH シ Potential Future Exposure シ                     | 45,500,000             | û |\n| Ø         | 360T.MMC Settlement      | 360T.MMC             |                | $\\frac{1}{2}$ Any<br>$\\overline{z}$    | Any                 |   | $\\frac{1}{2}$ Any       |               |                       | TODAY-3 MONTHS 5 Net Daily Settlement Limit<br>$\\overline{z}$     | 51,000,000             | û |\n| Ø         | <b>360TBANK PFE</b>      | 360TBANK.TEST        | ₹              | Any ys G10 5/                          | Any                 |   | $\\mathcal{V}$ Any       | $\\frac{1}{2}$ |                       | TODAY-1 MONTH = / Potential Future Exposure<br>$\\overline{z}$     | 67,500,000             | û |\n| $\\bullet$ | 360TBANK Settlement      | 360TBANK.TEST        | $\\overline{z}$ | Any<br>$\\overline{z}$                  | Any                 |   | $\\frac{3}{2}$ Any       |               |                       | TODAY-3 MONTHS   Net Daily Settlement Limit<br>$\\overline{z}$     | 73,000,000             | 亩 |\n| Ø         | <b>COBA PFE</b>          | COBA.DEMO            | $\\bar{z}$      | Any ys G10 $\\frac{1}{2}$               | Any                 | ∛ | Any                     |               |                       | D' TODAY-1 MONTH D' Potential Future Exposure<br>$\\bar{z}$        | 23,500,000             | û |\n| $\\bullet$ | <b>COBA Settlement</b>   | <b>COBA DEMO</b>     |                | $\\frac{1}{2}$ Any<br>$\\bar{z}$         | Any                 |   | $\\bar{z}$ Any           |               |                       | TODAY-3 MONTHS T/ Net Daily Settlement Limit<br>$\\overline{z}$    | 29,000,000             | û |\n| Ø         | <b>PEBANK PFE</b>        | PEBANK_APAC.TEST =   |                | Any vs G10 $5$                         | Any                 |   | ₹ Any                   |               |                       | シ TODAY-1 MONTH シ Potential Future Exposure<br>$\\bar{z}$          | 34,500,000             | û |\n| Ø         | <b>PEBANK Settlement</b> | PEBANK_APAC.TEST     |                | Any                                    | $\\frac{2}{3}$ Any   |   | $\\frac{3}{2}$ Any       |               |                       | 5⁄ TODAY-3 MONTHS 5⁄ Net Daily Settlement Limit<br>$\\overline{z}$ | 40,000,000             | 亩 |\n| Ø         | <b>RBS PFE</b>           | RBS.LND.DEMO         | $\\bar{z}$      | Any vs G10 5                           | Any                 |   | $\\mathcal{V}$ Any       |               |                       | ジ TODAY-1 MONTH ジ Potential Future Exposure<br>$\\bar{z}$ /        | 2,000,000              | û |\n| Ø         | <b>RBS Settlement</b>    | RBS.LND.DEMO         |                | $\\frac{3}{2}$ Any                      | $\\frac{3}{2}$ Any   |   | $\\frac{3}{2}$ Any       |               |                       | 5⁄ TODAY-3 MONTHS 5⁄ Net Daily Settlement Limit<br>$\\overline{z}$ | 24,000,000             | û |\n| ∞         | <b>SEB PFE</b>           | SEB.FRA.DEMO         |                | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ | Any                 |   | ₹ Any                   |               |                       | ジ TODAY-1 MONTH ジ Potential Future Exposure<br>- 5/               | 56,500,000             | û |\n| ØO        | <b>SEB Settlement</b>    | SEB.FRA.DEMO         |                | $\\frac{3}{2}$ Any                      | $\\frac{3}{2}$ Any   |   | $\\frac{3}{2}$ Any       |               |                       | ず TODAY-3 MONTHS シ Net Daily Settlement Limit ブ                   | 62.000.000             | û |\n| ØO        | <b>Total Net Limit</b>   | Any                  | $\\bar{z}$      | $\\overline{z}$<br>Any                  | Any                 |   | $\\mathcal{V}$ Any       |               | ₹ Any                 | 5⁄ Aggregate Net Settlement L. 5⁄                                 | 2.000.000.000          | û |\n\n<span id=\"page-14-2\"></span>![](_page_14_Figure_9.jpeg)\n\n### <span id=\"page-14-0\"></span>**4.1 Rule ID**\n\nRule ID field is a text-box in which users can define the name/ID of their rules. The field helps users to identify their rules and allows an easier matching between several limit systems they are administrating.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> | <b>Active Rules</b>  |                   | <b>Risk Portfolio PFF</b>                  | Ξ |                     |                       |                         |                                             |           |                                                 |               |                                        | 土土 |\n|------------------------------|-----------------------------|----------------------|-------------------|--------------------------------------------|---|---------------------|-----------------------|-------------------------|---------------------------------------------|-----------|-------------------------------------------------|---------------|----------------------------------------|----|\n| Ч                            |                             |                      | $\\rightarrow$     |                                            |   |                     |                       |                         |                                             |           | Constraints                                     |               | <b>VO</b> Rules Switch<br>$\\checkmark$ |    |\n| Enabled                      | Rule Id                     | $\\wedge$ Counterpart |                   | Portfolio                                  |   | <b>Legal Entity</b> |                       | <b>Execution Method</b> | <b>Fx Time Period</b>                       | Algorithm |                                                 |               | Limit                                  |    |\n|                              | 360T Group Risk             | 360T Group Related シ |                   | Any                                        |   | $\\mathbb{Z}$ Any    | $\\mathbb{Z}^{\\prime}$ | Any                     | $\\mathbb{Z}$ Any                            |           | ジ   Gross Daily Settlement Limit ジ   (0)        |               |                                        |    |\n| Œ                            | 360T.MMC PFE                | 360T.MMC             |                   | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ Any |   |                     |                       | $\\mathcal{V}$ Any       | ジ TODAY-1 MONTH ジ Potential Future Exposure |           |                                                 |               | û<br>$5/$ (45,500,000                  |    |\n|                              |                             |                      |                   |                                            |   |                     |                       |                         |                                             |           |                                                 |               |                                        |    |\n| ∞                            | 360T.MMC Settlement         | 360T.MMC             | $\\frac{3}{2}$ Any |                                            |   | $\\mathcal{V}$ Any   |                       | $\\frac{3}{2}$ Any       |                                             |           | 5⁄ TODAY-3 MONTHS 5⁄ Net Daily Settlement Limit | $\\frac{1}{2}$ | û<br>51,000,000                        |    |\n\n<span id=\"page-15-2\"></span>Figure 16 Defining Rule ID\n\nThe Rule ID value also serves to match the rules in Risk Portfolio Rules and Active Rules tab.\n\n### <span id=\"page-15-0\"></span>**4.2 Counterpart**\n\nThe *Counterpart* field defines the single trading counterparty or group of counterparties for which limits will be defined. As described in Section 3, admin users can group several counterparties and set generic rules and limits for the group. Once a rule has been added, users will be able to select the desired counterpart from a list which includes permissioned counterparts and pre-defined counterpart groups. Please note that the group values appear on top of the list.\n\n| $\\alpha$             |                          | $\\times$                                       | Constraints                                                            | V Rules Switch<br>$\\vee$ |     |\n|----------------------|--------------------------|------------------------------------------------|------------------------------------------------------------------------|--------------------------|-----|\n|                      |                          | Please select:                                 |                                                                        |                          |     |\n|                      | Enabled Rule Id          | `Q<br>$\\rightarrow$                            | Algorithm                                                              | Limit                    |     |\n| $(\\sqrt{\\bullet})$   | 360T Group               | Available (13)                                 | $\\mathbb{Z}^2$ Gross Daily Settlement Limit $\\mathbb{Z}^2$ (0)         |                          |     |\n| VO                   | 360T.MMC PFE             | Rating C+                                      | TH $\\frac{1}{2}$ Potential Future Exposure $\\frac{1}{2}$ (45,500,000   |                          | 亩   |\n| $\\sqrt{\\phantom{a}}$ | 360T.MMC Settlement      | 360T Group Related                             | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (51,000,000 |                          | 前   |\n| $\\sigma$             | 360TBANK PFE             | 360T.MMC                                       | TH J Potential Future Exposure J 67,500,000                            |                          | iii |\n| V O                  | 360TBANK Settlement      | 360TBANK.TEST                                  | THS E Net Daily Settlement Limit E 73,000,000                          |                          | 肯   |\n| $\\sqrt{\\phantom{a}}$ | <b>COBA PFE</b>          | BNPP.PAR.DEMO                                  | TH = Potential Future Exposure = = 7 (23,500,000                       |                          | iîi |\n| $\\sigma$             | <b>COBA Settlement</b>   | BOAL.DEMO                                      | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (29,000,000 |                          | 亩   |\n| V O                  | PEBANK PFE               | CAGLAR.TEST<br>CAGLARMTF.TEST                  | TH = Potential Future Exposure = = 7 (34,500,000                       |                          | 前   |\n| $\\sqrt{\\phantom{a}}$ | <b>PEBANK Settlement</b> | COBA.DEMO                                      | THS F Net Daily Settlement Limit F 40,000,000                          |                          | iii |\n| VO                   | <b>RBS PFE</b>           | MOCK.TEST                                      | TH 5/ Potential Future Exposure 5/ 2,000,000                           |                          | 亩   |\n| V O                  | <b>RBS Settlement</b>    | PEBANK APAC.TEST                               | THS 5 Net Daily Settlement Limit 5 24,000,000                          |                          | iîi |\n| vo                   | SEB PFE                  | RBS.LND.DEMO                                   | TH $\\frac{1}{2}$ Potential Future Exposure $\\frac{1}{2}$ (56,500,000   |                          | 前   |\n| VO                   | <b>SEB Settlement</b>    | SEB.FRA.DEMO                                   | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ 62,000,000    |                          | 前   |\n|                      | <b>Total Net Limit</b>   |                                                | Aggregate Net Settlement L. 7 (2,000,000,000                           |                          | iii |\n|                      |                          | <b>Apply Selected</b><br>Apply \"Any\"<br>Cancel |                                                                        |                          |     |\n\n<span id=\"page-15-3\"></span>Figure 17 Defining counterpart for risk portfolio rules\n\nIt is also possible to make the rule applicable to all permissioned counterparties by clicking on *Apply `Any`*.\n\n### <span id=\"page-15-1\"></span>**4.3 Portfolio**\n\nThe *Portfolio* field defines the combination of product and currency pair to which the limits will be applied. As described in [Section 3,](#page-8-0) admin users can create *Portfolios* from the *Risk Portfolio Groups* tab by selecting Product and Currency Couple. The preconfigured portfolios will then be available in the *Portfolio* field of the added rule. *Portfolio* field permits the user to set limits for product and currency pair combinations.\n\nIn addition to define pre-configured portfolios in risk portfolio rule, it is also possible to *Apply `Any`* which makes the rule applicable for all portfolios, thus for every currency pair and product traded.\n\n| Q                    |                          |                            | Please select: |               |                                                                        |       |     |\n|----------------------|--------------------------|----------------------------|----------------|---------------|------------------------------------------------------------------------|-------|-----|\n|                      | Enabled Rule Id          | Q                          |                | $\\rightarrow$ | Algorithm                                                              | Limit |     |\n| $\\circledcirc$       | 360T Group Risk          |                            | Available (5)  |               | $\\mathbb{Z}^2$ Gross Daily Settlement Limit $\\mathbb{Z}^2$ (0          |       | û   |\n| ▽●                   | 360T.MMC PFE             | Outright G10               |                |               | TH = Potential Future Exposure = = (45,500,000                         |       | 音   |\n| V O                  | 360T.MMC Settlement      | Swap v G10                 |                |               | THS T/ Net Daily Settlement Limit T/ 51,000,000                        |       | iîi |\n| $\\sqrt{\\phantom{a}}$ | <b>360TBANK PFE</b>      | SpotForward v Local        |                |               | TH = Potential Future Exposure = = 67,500,000                          |       | 前   |\n| $\\sigma$             | 360TBANK Settlement      | Swap v Local<br>Any vs G10 |                |               | THS 5 Net Daily Settlement Limit 5 73,000,000                          |       |     |\n| V O                  | COBA PFE                 |                            |                |               | TH & Potential Future Exposure & 23.500.000                            |       | 面   |\n| VO.                  | <b>COBA Settlement</b>   |                            |                |               | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (29,000,000 |       | 音   |\n| $\\sqrt{2}$           | PEBANK PFE               |                            |                |               | TH / Potential Future Exposure / 34,500,000                            |       | 宜   |\n| $\\sigma$             | <b>PEBANK Settlement</b> |                            |                |               | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (40,000,000 |       | 亩   |\n| VO.                  | <b>RBS PFE</b>           |                            |                |               | TH = Potential Future Exposure = = 2,000,000                           |       | 音   |\n| $\\sigma$             | <b>RBS Settlement</b>    |                            |                |               | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ 24,000,000    |       | 面   |\n| M O                  | SEB PFE                  |                            |                |               | TH = Potential Future Exposure = = (56,500,000                         |       | 前   |\n| VO                   | <b>SEB Settlement</b>    |                            |                |               | THS J Net Daily Settlement Limit / 62,000,000                          |       | 盲   |\n| √●                   | <b>Total Net Limit</b>   |                            |                |               | Aggregate Net Settlement L. 7 (2,000,000,000                           |       | 面   |\n|                      |                          |                            |                |               |                                                                        |       |     |\n|                      |                          |                            |                |               |                                                                        |       |     |\n|                      |                          |                            |                |               |                                                                        |       |     |\n|                      |                          |                            |                |               |                                                                        |       |     |\n\n<span id=\"page-16-1\"></span>Figure 18 Defining portfolio for risk portfolio rules\n\n### <span id=\"page-16-0\"></span>**4.4 Legal Entity Group**\n\nThe *Legal Entity Group* field defines the group of related entities for which the risk limit will be defined. As described in Section 3, admin users can group several legal entities in order to set generic rules and limits for them. Once a rule has been added, users will be able to select the desired legal entity or pre-defined legal entity group by by selecting it and then clicking on *Apply Selected*. It is also possible to select `Any` which would make the rule applicable to all the legal entities involved in trading from the client's perspective.\n\n*Legal Entity Group* parameter can be especially useful for clients with multiple legal entities who need to manage risks centrally.\n\n|                  | Risk Portfolio Groups (Risk Portfolio Rules) | Active Rules Risk Portfolio PFE | Ξ                     |                       |          |                                                                        |                                               |                        | 出 3 |\n|------------------|----------------------------------------------|---------------------------------|-----------------------|-----------------------|----------|------------------------------------------------------------------------|-----------------------------------------------|------------------------|-----|\n| $\\alpha$         |                                              |                                 | Please select:        |                       | $\\times$ |                                                                        | $\\left(\\text{Constraints} \\quad \\vee \\right)$ | <b>CO</b> Rules Switch |     |\n| Enabled Rule Id  |                                              | (Q                              |                       | $\\rightarrow$         |          | Algorithm                                                              | Limit                                         |                        |     |\n| $\\circledcirc$   | 360T Group Risk                              |                                 | Available (1)         |                       |          | ジ   Gross Daily Settlement Limit シ   (0                                |                                               | 而                      |     |\n| ▽●               | 360T, MMC PFE                                | 360T.RMS.TAS1                   |                       |                       |          | TH $\\mathbb{R}$ Potential Future Exposure $\\mathbb{R}$ (45,500,000     |                                               | 前                      |     |\n| $\\sigma$         | 360T.MMC Settlement                          |                                 |                       |                       |          | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (51,000,000 |                                               | 前                      |     |\n| $\\sigma$         | 360TBANK PFE                                 |                                 |                       |                       |          | TH I Potential Future Exposure I 67,500,000                            |                                               | 面                      |     |\n| $\\sim$           | 360TBANK Settlement                          |                                 |                       |                       |          | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (73,000,000 |                                               | 面                      |     |\n| $\\sigma$         | COBA PFE                                     |                                 |                       |                       |          | TH = Potential Future Exposure = = 23,500,000                          |                                               | 前                      |     |\n| $\\sigma$         | <b>COBA Settlement</b>                       |                                 |                       |                       |          | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (29,000,000 |                                               | 首                      |     |\n| $\\sigma$         | PEBANK PFE                                   |                                 |                       |                       |          | TH I Potential Future Exposure I 34,500,000                            |                                               | 面                      |     |\n| $\\sigma$         | <b>PEBANK Settlement</b>                     |                                 |                       |                       |          | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (40,000,000   |                                               | 面                      |     |\n| $\\sigma$         | <b>RBS PFE</b>                               |                                 |                       |                       |          | TH = Potential Future Exposure = = 2,000,000                           |                                               | 前                      |     |\n| V O              | <b>RBS Settlement</b>                        |                                 |                       |                       |          | THS F Net Daily Settlement Limit F 24,000,000                          |                                               | 育                      |     |\n| $\\sigma$         | SEB PFE                                      |                                 |                       |                       |          | TH I Potential Future Exposure I 56,500,000                            |                                               | 面                      |     |\n| $\\sqrt{\\bullet}$ | <b>SEB Settlement</b>                        |                                 |                       |                       |          | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ 62,000,000    |                                               | 面                      |     |\n|                  | <b>Total Net Limit</b>                       |                                 |                       |                       |          | 5 Aggregate Net Settlement L. 5 (2,000,000,000                         |                                               | 前                      |     |\n|                  |                                              |                                 |                       |                       |          |                                                                        |                                               |                        |     |\n|                  |                                              |                                 |                       |                       |          |                                                                        |                                               |                        |     |\n|                  |                                              |                                 |                       |                       |          |                                                                        |                                               |                        |     |\n|                  |                                              |                                 | Apply \"Any\"<br>Cancel | <b>Apply Selected</b> |          |                                                                        |                                               |                        |     |\n\n<span id=\"page-17-2\"></span>Figure 19 Defining legal entity for risk portfolio rules\n\n### <span id=\"page-17-0\"></span>**4.5 Dealer**\n\n*Dealer* parameter defines the single or group of individuals who executed the transaction on behalf of the credit entity. As described in *Section 3,* it is possible to group the dealers in order to assign one single limit for a group of them. As well as created groups, all dealers who can trade (Trader, Treasurer, Hybrid and API types of users) and are active will appear as available value for this parameter.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> |                                          | Active Rules Risk Portfolio PFE    | $=$            |             |                       |          |                       |                                                         |                                       |     | 出土 |\n|------------------------------|-----------------------------|------------------------------------------|------------------------------------|----------------|-------------|-----------------------|----------|-----------------------|---------------------------------------------------------|---------------------------------------|-----|----|\n| $\\sqrt{a}$                   |                             |                                          | $\\rightarrow$                      |                |             |                       |          |                       | Permissions                                             | <b>O</b> Rules Switch<br>$\\checkmark$ |     |    |\n| Enabled Rule Id              |                             | $\\vee$ Cou                               |                                    | Please select: |             |                       | $\\times$ | <b>Fx Time Period</b> | Algorithm                                               | Limit                                 |     |    |\n| $\\oslash$                    | RCM                         | Any                                      |                                    |                |             |                       |          | Any                   | $\\mathbb{R}$ Daily Net Trading Limit                    | $\\frac{1}{2}$ (2,000,000,000          | I û |    |\n| ▽●                           | RCL-GBP                     | Any                                      | Q                                  |                |             | $\\rightarrow$         |          |                       | TODAY-1 MONTH   Maily Net Trading Limit                 | $\\frac{3}{2}$ (500,000,000)           | 庙   |    |\n| ▽●                           | RCL-CHF                     | Any                                      |                                    | Available (5)  |             |                       |          |                       | TODAY-1 MONTH $\\mathbb{R}$ Daily Net Trading Limit      | $7/$ 350,000,000                      | 亩   |    |\n| ▽●                           | 97047993-ER                 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Swap Dealers<br>360TMMC.AutoDealer |                |             |                       |          | Any                   | T/ Gross Daily Settlement Li. T/ 20,000,000             |                                       | 面   |    |\n| ▽●                           | 22175403-WER                | 221                                      | 360TMMC.Trader1                    |                |             |                       |          |                       | SPOTNEXT-1 YEAR F Potential Future Exposure F 2,400,000 |                                       | 庙   |    |\n| $\\sigma$                     | 22175403-ER                 | 221                                      | 360TMMC.API                        |                |             |                       |          | Any                   | Gross Daily Settlement Li.   20,000,000                 |                                       | 前   |    |\n| $\\circ$                      | 22175394-ER                 | 221                                      | 360TMMC.Trader2                    |                |             |                       |          | Any                   | ジ Gross Daily Settlement Li. ジ (7,500,000               |                                       | 前   |    |\n|                              |                             |                                          |                                    |                |             |                       |          |                       |                                                         |                                       |     |    |\n|                              |                             |                                          |                                    | Cancel         | Apply \"Any\" | <b>Apply Selected</b> |          |                       |                                                         |                                       |     |    |\n\n<span id=\"page-17-3\"></span>Figure 20 Defining dealer for risk portfolio rules\n\n### <span id=\"page-17-1\"></span>**4.6 Execution Method**\n\nThe *Execution Method* parameter defines the trading venue where the trade is dealt and includes RFS, SEP, OMT, MidMatch, HST Orderbook, HST Engine and GTX CLOB. Selecting one of the venues as custom or by creating a pre-defined group includes several of them would help credit entity to filter out the trades based on the trading facility of 360T that it got executed/submitted. Parameter is especially helpful for the clients who wants to limit the check to a specific trading facility.\n\n| $\\sqrt{Q}$           |                          |                   | Please select:        | $\\times$              |                  | Constraints                                                          | $\\checkmark$ | <b>Rules Switch</b><br>M 01 |            |\n|----------------------|--------------------------|-------------------|-----------------------|-----------------------|------------------|----------------------------------------------------------------------|--------------|-----------------------------|------------|\n| Enabled              | Rule Id                  | q                 |                       | $\\rightarrow$         |                  | Algorithm                                                            | Limit.       |                             |            |\n| ☞                    | 360T Group Risk          |                   | Available (9)         |                       |                  | $\\mathbb{Z}^2$ Gross Daily Settlement Limit $\\mathbb{Z}^2$   (0)     |              |                             | û.         |\n| √●                   | 360T, MMC PFE            | <b>Streaming</b>  |                       |                       |                  | TH = Potential Future Exposure = = (45,500,000                       |              |                             | 面          |\n| VO                   | 360T.MMC Settlement      | MidMatch          |                       |                       |                  | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (51,000,000 |              |                             | <b>iii</b> |\n| VO                   | 360TBANK PFE             | SEP               |                       |                       |                  | TH 5 Potential Future Exposure 5 67,500,000                          |              |                             | 前          |\n| VO                   | 360TBANK Settlement      | <b>RFS</b><br>OMT |                       |                       |                  | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (73,000,000 |              |                             | <b>iii</b> |\n| 70                   | <b>COBA PFE</b>          | MidMatch          |                       |                       |                  | TH 5 Potential Future Exposure 5 23,500,000                          |              |                             | 面          |\n| VO                   | <b>COBA Settlement</b>   | <b>HST Engine</b> |                       |                       | THS $\\mathbb{Z}$ | Net Daily Settlement Limit 7 29,000,000                              |              |                             | <b>面</b>   |\n| ▽●                   | PEBANK PFE               | HST OrderBook     |                       |                       |                  | TH 5 Potential Future Exposure 5 34,500,000                          |              |                             | 面          |\n| VO                   | <b>PEBANK Settlement</b> | <b>GTX CLOB</b>   |                       |                       |                  | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (40,000,000 |              |                             | <b>面</b>   |\n| ▽●                   | <b>RBS PFE</b>           |                   |                       |                       |                  | TH 5 Potential Future Exposure 5 2 2.000.000                         |              |                             | 面          |\n| $\\sqrt{\\phantom{a}}$ | <b>RBS</b> Settlement    |                   |                       |                       |                  | THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (24,000,000 |              |                             | <b>面</b>   |\n| VO                   | SEB PFE                  |                   |                       |                       |                  | TH $\\mathbb{R}$ Potential Future Exposure $\\mathbb{R}$ (56,500,000   |              |                             | 面          |\n| $\\sqrt{\\phantom{a}}$ | <b>SEB Settlement</b>    |                   |                       |                       |                  | THS / Net Daily Settlement Limit / 62,000,000                        |              |                             | <b>面</b>   |\n| VO                   | <b>Total Net Limit</b>   |                   |                       |                       |                  | ₹ Aggregate Net Settlement L. ₹ (2,000,000,000                       |              |                             | 童          |\n|                      |                          |                   | Apply \"Any\"<br>Cancel | <b>Apply Selected</b> |                  |                                                                      |              |                             |            |\n\n<span id=\"page-18-1\"></span>Figure 21 Defining execution method for risk portfolio rules\n\n### <span id=\"page-18-0\"></span>**4.7 Fx Time Period**\n\nThe *Fx Time Period* field defines the credit horizon for the risk portfolio rules. Similar to the other parameters, admin users can define the time period among a list of tenors and pre-defined time period groups. Available tenors on the platform (Today, Tomorrow, Spot, Spotnext etc.) are combined as start-end period and listed in the menu. By the help of search functionality, users can easily find the desired combination without having to go through the extensive list.\n\n| $\\alpha$ |                            |                                          | Please select:  | $\\times$      | Constraints                                                            |        | <b>VO</b> Rules Switch |\n|----------|----------------------------|------------------------------------------|-----------------|---------------|------------------------------------------------------------------------|--------|------------------------|\n| Enabled  | Rule Id                    | Q TODAY-3 MONTHS                         |                 | $\\rightarrow$ | Algorithm                                                              | Limit. |                        |\n| ☞        | 360T Group Risk            |                                          | Available (302) |               | $\\mathbb{R}$ Gross Daily Settlement Limit $\\mathbb{R}$   0             |        | 面                      |\n|          | 360T.MMC PFE               | PreSpot                                  |                 |               | TH / Potential Future Exposure / 45,500,000                            |        | îĩ                     |\n| ∨●       | 360T.MMC Settlement        | Upto2W                                   |                 |               | THS 3⁄ Net Daily Settlement Limit 3⁄ 51,000,000                        |        | 亩                      |\n| ▽●       | <b>360TBANK PFE</b>        | Spotnext to 1m                           |                 |               | TH $\\frac{1}{2}$ Potential Future Exposure $\\frac{1}{2}$ 67,500,000    |        | 重                      |\n| び●       | <b>360TBANK Settlement</b> | TODAY-UNLIMITED                          |                 |               | THS F Net Daily Settlement Limit F 73,000,000                          |        | îĩ                     |\n| ▽●       | <b>COBA PFE</b>            | TOMORROW-UNLIMITED<br>SPOT-UNLIMITED     |                 |               | TH 3 Potential Future Exposure 3 23,500,000                            |        | 前                      |\n| VO       | <b>COBA Settlement</b>     | SPOTNEXT-UNLIMITED                       |                 |               | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ (29,000,000 |        | 甫                      |\n| ▽●       | PEBANK PFE                 | 1 WEEK-UNLIMITED                         |                 |               | TH J Potential Future Exposure J 34,500,000                            |        | ΰ                      |\n| VO.      | <b>PEBANK Settlement</b>   | 2 WEEKS-UNLIMITED                        |                 |               | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ 40,000,000  |        | 亩                      |\n| ▽●       | <b>RBS PFE</b>             | 3 WEEKS-UNLIMITED                        |                 |               | TH $\\frac{1}{2}$ Potential Future Exposure $\\frac{1}{2}$ (2,000,000    |        | 甫                      |\n| VO       | <b>RBS Settlement</b>      | 1 MONTH-UNLIMITED                        |                 |               | THS F Net Daily Settlement Limit F 24,000,000                          |        | îĩ                     |\n| ▽●       | SEB PFE                    | 2 MONTHS-UNLIMITED                       |                 |               | TH = Potential Future Exposure = = (56,500,000                         |        | 前                      |\n| ∨•       | <b>SEB Settlement</b>      | 3 MONTHS-UNLIMITED<br>4 MONTHS-UNLIMITED |                 |               | THS $\\frac{1}{2}$ Net Daily Settlement Limit $\\frac{1}{2}$ 62,000,000  |        | 甫                      |\n| √●       | <b>Total Net Limit</b>     | 5 MONTHS-UNLIMITED                       |                 |               | Aggregate Net Settlement L. 3 (2,000,000,000                           |        | <b>iii</b>             |\n|          |                            | 6 MONTHS-UNLIMITED                       |                 |               |                                                                        |        |                        |\n|          |                            | 7 MONTHS-UNLIMITED                       |                 |               |                                                                        |        |                        |\n|          |                            | 8 MONTHS-UNLIMITED                       |                 |               |                                                                        |        |                        |\n|          |                            | 9 MONTHS-UNLIMITED                       |                 |               |                                                                        |        |                        |\n|          |                            | 10 MONTHS-UNLIMITED                      |                 |               |                                                                        |        |                        |\n\n<span id=\"page-19-1\"></span>Figure 22 Defining Fx Time Period for risk portfolio rules\n\nThe defined FX Time Periods will determine which rule will be applied to which trades based on the value dates of the trades.\n\nSimilar to the other parameters, it is possible to include all non-due trades irrespective of their value dates by clicking on *Apply `Any`.*\n\n### <span id=\"page-19-0\"></span>**4.8 Algorithms**\n\nThe creation of risk portfolios provides a solution to address different types of risk exposures through different combinations of trade parameters, and this is further enhanced by the ability to assign different risk exposure calculation methodologies.\n\n| <b>Risk Portfolio Groups</b>                                                                               | <b>Risk Portfolio Rules</b>                                                                                                                                                                                                                                                                          | <b>Active Rules</b><br><b>Risk Portfolio PFE</b><br>Ξ                                                                                                                                                                   |               |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |                                                                                                           |\n|------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------|\n| $\\alpha$                                                                                                   |                                                                                                                                                                                                                                                                                                      | Please select:                                                                                                                                                                                                          | $\\times$      | Constraints                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | <b>Rules Switch</b><br>$\\checkmark$                                                                       |\n| <b>Enabled</b>                                                                                             | Rule Id                                                                                                                                                                                                                                                                                              | Q                                                                                                                                                                                                                       | $\\rightarrow$ | Algorithm                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | Limit                                                                                                     |\n| ♡●<br>∕●<br>V O<br>$\\sigma$<br>$\\sigma$<br>$\\sigma$<br>$\\sigma$<br>VO<br>VO,<br>▽●<br>♡●<br>▽●<br>VO<br>VO | 360T Group Risk<br>360T.MMC PFE<br>360T.MMC Settlement<br><b>360TBANK PFE</b><br>360TBANK Settlement<br><b>COBA PFE</b><br><b>COBA Settlement</b><br>PEBANK PFE<br><b>PEBANK Settlement</b><br><b>RBS PFE</b><br><b>RBS Settlement</b><br>SEB PFE<br><b>SEB Settlement</b><br><b>Total Net Limit</b> | Available (7)<br>Net Daily Settlement Limit<br><b>Gross Daily Settlement Limit</b><br><b>Aggregate Gross Settlement Limit</b><br>Aggregate Net Settlement Limit<br>Potential Future Exposure<br>Daily Net Trading Limit |               | THS $\\mathcal{V}$ Gross Daily Settlement Limit $\\mathcal{V}$ $(0, 0)$<br>TH => Potential Future Exposure = => (45,500,000<br>THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$   (51,000,000<br>TH 3⁄ Potential Future Exposure 3⁄ 67,500,000<br>THS $\\mathbb{R}^2$ Net Daily Settlement Limit $\\mathbb{R}^2$ (73,000,000<br>TH = Potential Future Exposure = = プ   (23,500,000<br>THS $5/$ Net Daily Settlement Limit $5/$ (29,000,000<br>TH J Potential Future Exposure J (34,500,000<br>THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (40,000,000<br>TH J Potential Future Exposure J (2,000,000<br>THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ (24,000,000<br>TH $\\mathbb{R}$ Potential Future Exposure $\\mathbb{R}$   (56,500,000<br>THS $\\mathbb{R}$ Net Daily Settlement Limit $\\mathbb{R}$ 62,000,000<br>Aggregate Net Settlement L., 3 (2,000,000,000 | û<br>前<br>道<br>道<br><b>ii</b><br>Û<br><b>O</b><br><b>iii</b><br>iii<br><b>iii</b><br>iii<br>iii<br>市<br>童 |\n|                                                                                                            |                                                                                                                                                                                                                                                                                                      | cancel                                                                                                                                                                                                                  | <b>Apply</b>  |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |                                                                                                           |\n\n<span id=\"page-19-2\"></span>Figure 23 Setting algorithm for risk portfolio rules.\n\nThe *Algorithm* field of a risk portfolio rule defines:\n\n- <span id=\"page-20-1\"></span>1) What the user is limiting,\n- 2) How the utilization of a trade should be calculated.\n\n360T`s Limits Monitor supports 9 different algorithms:\n\n- **Net Daily Settlement Limit,**\n- **Gross Daily Settlement Limit,**\n- **Aggregate Net Settlement Limit,**\n- **Aggregate Gross Settlement Limit**\n- **Potential Future Exposure and**\n- **Daily Net Trading Limit**\n- **Daily Gross Trading Limit**\n- **Per Deal Limit**\n- **Gross DSL with Settlement Period<sup>2</sup>**\n\nIn the following sub-sections, the different methodologies are explained and compared. More detailed examples with regards to utilization calculations for each of the algorithms are provided in [Section 5.1.](#page-29-0)\n\n#### <span id=\"page-20-0\"></span>**4.8.1 Settlement Limits**\n\n360T`s Limit Monitor provides 4 types of algorithms to allow clients to monitor and limit their settlement risks. Algorithms associated with settlement risk can be grouped as per how the cashflows are accounted as well as how they are aggregated:\n\n#### **4.8.1.1 Accounting Methodology: Net vs Gross Settlement**\n\n360T`s limit functionality allows credit entities to calculate their exposure via net and gross settlement methodologies. For Net settlement methodology, the risk exposure comes from the cashflow generated by each leg of the trade in terms of credit currency<sup>3</sup> . This methodology nets the cashflow for each currency account.\n\n*For example: Credit Entity A* buys 2 million Euro in one trade and sells 1 million Euro in another trade. Under the assumption of both trades are captured under the same Risk Portfolio Rule, the entity`s Euro exposure would be 1 million Euro receivable (long).\n\nIn contrast, Gross Settlement methodology sums the cashflow amounts to derive a gross exposure. In above example, the gross settlement exposure would thus be 3 million Euro.\n\n<sup>2</sup> Please contact CAS [\\(<EMAIL>\\)](mailto:<EMAIL>) for activation.\n\n<sup>3</sup> Credit currency is the currency defined for an entity on the platform, also known as company currency, which instructs the currency associated with limit and utilization calculations.\n\n#### **4.8.1.2 Aggregation Methodology: Daily vs Aggregate**\n\n360T`s limit functionality offers both daily and aggregate methodologies. As naming suggests, daily settlement limits are applied for each relevant settlement date. Setting a limit with one of the daily algorithms means setting a limit for each value date across the defined FX Time Period.\n\n*For example:* A daily limit of 5 million USD set for the Time Period Today to Spot, means setting a 5 million USD limit per day for Today, Tomorrow and Spot, i.e. for each value date separately across the credit horizon.\n\nOn the other hand, defining an aggregate limit would mean setting one absolute limit for all value dates across the credit horizon. In the example just cited, 5million USD limit would be a total limit valid from Today to Spot.\n\n#### <span id=\"page-21-0\"></span>**4.8.2 Trading Limits**\n\nLimits Monitor provides two different algorithms to allow clients to monitor and limit the risk arisen by trading activities in current business day.\n\n#### <span id=\"page-21-3\"></span>**4.8.2.1 Daily Net Trading Limit**\n\nDaily Net Trading Limit algorithm allows clients to limit the net open position for the current trading day..\n\n*For example: If Credit Entity* A sets a 10 million Euro trading limit for a specific risk portfolio rule, the net open position of that specific portfolio cannot exceed 10 million Euro for the current trade date. After end of day rollover, the utilization is reset to 0 and another 10 million Euro limit is available.\n\n#### <span id=\"page-21-4\"></span>**4.8.2.2 Daily Gross Trading Limit**\n\nDaily Gross Trading Limit algorithm can be used to limit the total intraday trading volume for the current trading day.\n\n*For example: If Credit Entity* A sets a 10 million Euro trading limit for a specific risk portfolio rule, the net open position of that specific portfolio cannot exceed 10 million Euro for the current trade date. After end of day rollover, the utilization is reset to 0 and another 10 million Euro limit is available.\n\n#### ********* Per Deal Limit**\n\nAlso known as Fat-Finger limit, Per Deal Limit aims to restrict the notional amount of one single deal. This limit type is mostly used to address operation risk and associated with dealers.\n\n#### <span id=\"page-21-1\"></span>**4.8.3 Potential Future Exposure**\n\n360T`s limit functionality provides a Potential Future Exposure algorithm for the entities wishing to allocate limits not on the cashflow or notional itself but on risk weighing factors they define by currency pair and days left to maturity. This algorithm calculates the exposure by multiplying the notional of the trade by the PFE factor defined.\n\nFor clients who requires to allocate different risk weighing factor depending on other trading parameters such as counterpart, Time Period etc. it is also possible to define different PFE tables and assign the risk portfolio rule to the respective table.\n\n### <span id=\"page-21-2\"></span>**4.9 Limit**\n\nThe last parameter defined for each portfolio is the amount of the limit. The limit is defined in credit currency of the entity.\n\n**IMPORTANT: Please note that a generic limit set on a risk portfolio rule can be amended within that rule. As with any parameter change in Risk Portfolio Rules, a generic limit change will only be valid after end of day rollover. Should an immediate change to a limit be required, this should be done within the 'Active Rules' tab; such a change will be valid immediately for the duration of that day, and overwritten by the limit set under Risk Portfolio Rules at end of day rollover.**\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> | <b>Active Rules</b>                   | <b>Risk Portfolio PFE</b> |                          | Ξ                   |                         |                       |                                                     |                            |                | 出土 |\n|------------------------------|-----------------------------|---------------------------------------|---------------------------|--------------------------|---------------------|-------------------------|-----------------------|-----------------------------------------------------|----------------------------|----------------|----|\n| Ч                            |                             |                                       | $\\rightarrow$             |                          |                     |                         |                       | Constraints                                         |                            | Rules Switch   |    |\n| Enabled                      | Rule Id                     | $\\wedge$ Counterpart                  |                           | Portfolio                | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                                           | Limit                      |                |    |\n| $\\sim$                       | 360T Group Risk             | 360T Group Related $\\mathbb{R}^2$ Any |                           |                          | $\\mathbb{Z}$ Any    | $\\mathbb{Z}$ Any        |                       | ■ TODAY-3 MONTHS ■ Aggregate Gross Settlement Limit |                            | 50,000,000   面 |    |\n| $\\sigma$                     | 360T MMC PEE                | 360T MMC                              |                           | $5/$ Any ys G10 $5/$ Any |                     | $\\frac{1}{2}$ Any       |                       | F/ TODAV-1 MONTH F/ Potential Future Evonsure       | $\\frac{1}{2}$ (45.500.000) | 一命             |    |\n\n<span id=\"page-22-3\"></span>Figure 24 Defining limit for risk portfolio rules.\n\n### <span id=\"page-22-0\"></span>**4.10Edit Rule**\n\nParameters of a risk portfolio rule can be changed by clicking on icon next to the relevant parameter. *Rule Id* and *Limit* parameters can be amended by single-click on the text-field. Once a change is done on the rule, it is applied by click on `Save`.\n\n| Enabled | <b>Rule Id</b>         | Counterpart              | Portfolio | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Ex Time Period</b> | Algorithm                                                                                                     | Limit |  |\n|---------|------------------------|--------------------------|-----------|---------------------|-------------------------|-----------------------|---------------------------------------------------------------------------------------------------------------|-------|--|\n|         | <b>360T Group Risk</b> | 360T Group Related ₹ Any |           | Any                 | Any                     |                       | $\\times$   TODAY-3 MONTHS $\\,$ $\\leq$   Aggregate Gross Settlement Limit $\\,$ $\\leq$   $\\,$   50,000,000 $\\,$ |       |  |\n\n### <span id=\"page-22-1\"></span>**4.11Activate/Deactivate Rule**\n\nFor users who want to remove a Risk Portfolio rule temporarily, the Risk Portfolio panel provides an option to deactivate the rules instead of completely deleting them. By using the toggle button ( ), it is possible to enable or disable the relevant rule.\n\nIMPORTANT: Please note that, consistent with all actions in Risk Portfolio Rules, this action will only take effect with *end of day rollover*.\n\n### <span id=\"page-22-2\"></span>**4.12Delete Rule**\n\nRisk portfolio rules can be completely removed with the `Delete Rule` function. Clicking on the icon and then saving removes the rule from the Risk Portfolio rules.\n\nIMPORTANT: Please note that a deleted rule is still active until *end of day rollover*. (Active Rules can be monitored under `Active Rules` tab. The function of this tab is explained in [Section 5](file://///360t.com/shares/Projects/Risk%20limits/User%20Manual/360T%20User%20Guide%20Global%20Risk%20Management%20System.docx) in more detail.)\n\n### <span id=\"page-23-0\"></span>**4.13Bulk Upload/Download via CSV**\n\n360T`s Limits Monitor provides a csv upload/download functionality to allow bulk upload/update of risk portfolio rules. The functionality facilitates the manual administration of rule and limit settings by allowing,\n\n- Creation of new risk portfolio rules\n- Update of any parameter of an existing risk portfolio rule\n- Deletion/deactivation of an existing risk portfolio rule.\n\n| Q         |                            |                           | $\\rightarrow$ |                                        |                     |                         |                  |                       | Constraints                             | $\\checkmark$               | <b>KO</b><br><b>Rules Switch</b> |\n|-----------|----------------------------|---------------------------|---------------|----------------------------------------|---------------------|-------------------------|------------------|-----------------------|-----------------------------------------|----------------------------|----------------------------------|\n| Enabled   | <b>Rule Id</b>             | $\\vee$ Counterpart        |               | Portfolio                              | <b>Legal Entity</b> | <b>Execution Method</b> |                  | <b>Fx Time Period</b> | Algorithm                               | Limit                      |                                  |\n| $\\circ$   | <b>Total Net Limit</b>     | Any                       |               | $\\mathbb{Z}^r$<br>$\\frac{1}{2}$ Any    | Any                 | $\\frac{1}{2}$ Any       |                  | $\\frac{1}{2}$ Any     | 5⁄ Aggregate Net Settlement Limit 5⁄    | 2,000,000,000              | û                                |\n| Ø         | <b>SEB Settlement</b>      | SEB.FRA.DEMO              |               | $\\frac{3}{2}$ Any                      | $\\mathcal{V}$ Any   | $\\frac{3}{2}$ Any       |                  | TODAY-3 MONTHS 5      | Net Daily Settlement Limit              | $5/$ 62,000,000            | 俞                                |\n| ØO        | <b>SEB PFE</b>             | SEB.FRA.DEMO              |               | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ | Any                 | $\\frac{3}{2}$ Any       |                  | V TODAY-1 MONTH       | Potential Future Exposure               | $5/$ 56,500,000            | û                                |\n| ØO        | <b>RBS Settlement</b>      | RBS.LND.DEMO              |               | $\\frac{3}{2}$ Any<br>Ξ/                | Anv                 | ₹ Any                   |                  | 5/ TODAY-3 MONTHS 5/  | ₹∕<br>Net Daily Settlement Limit        | 24.000.000                 | Û                                |\n| $\\bullet$ | <b>RBS PFE</b>             | RBS.LND.DEMO              |               | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ | Any                 | $\\frac{3}{2}$ Any       |                  | V TODAY-1 MONTH       | Potential Future Exposure               | $\\frac{3}{2}$ (2,000,000   | Û                                |\n| ØO        | <b>PEBANK Settlement</b>   | PEBANK APACTEST           |               | W.<br>Anv                              | Any                 | $\\mathcal{V}$ Any       |                  | V TODAY-3 MONTHS      | $\\bar{z}$<br>Net Daily Settlement Limit | (40.000.000)               | û                                |\n| $\\bullet$ | PEBANK PFE                 | PEBANK APACTEST           |               | Any vs G10 5                           | Any                 | $\\frac{3}{2}$ Any       |                  | E/ TODAY-1 MONTH      | Potential Future Exposure               | $\\frac{7}{2}$ (34,500,000  | Û                                |\n| Ø         | <b>COBA Settlement</b>     | COBA.DEMO                 |               | $\\mathbb{Z}$ Any<br>$\\bar{z}$ /        | Any                 | ₹ Any                   | ৶                | TODAY-3 MONTHS        | Net Daily Settlement Limit<br>₹∕        | 29.000.000                 | ŵ                                |\n| $\\bullet$ | <b>COBA PFE</b>            | COBA.DEMO                 |               | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ | Any                 | $\\frac{1}{2}$ Any       |                  | V TODAY-1 MONTH       | Potential Future Exposure               | $\\frac{7}{2}$ (23.500.000) | û                                |\n| Ø         | <b>360TBANK Settlement</b> | <b>360TBANKTEST</b>       |               | $\\frac{3}{2}$ Any<br>5/                | Any                 | ₹ Any                   | $\\overline{z}$ / | TODAY-3 MONTHS EZ     | Net Daily Settlement Limit<br>₩         | 73,000,000                 | û                                |\n| $\\bullet$ | <b>360TBANK PFE</b>        | 360TBANK.TEST             |               | $\\frac{3}{2}$ Any vs G10 $\\frac{3}{2}$ | Any                 | $\\frac{3}{2}$ Any       |                  | V TODAY-1 MONTH       | Potential Future Exposure               | 7/67.500.000               | û                                |\n| Ø         | 360T.MMC Settlement        | 360T.MMC                  |               | $\\mathbb{Z}$ Any<br>$\\overline{z}$     | Any                 | $\\mathcal{V}$ Any       | ₩                | TODAY-3 MONTHS EX     | ₩<br>Net Daily Settlement Limit         | 51,000,000                 | û                                |\n| $\\bullet$ | 360T.MMC PFE               | 360T.MMC                  |               | $\\frac{m}{2}$ Any vs G10 $\\frac{m}{2}$ | Any                 | $\\frac{3}{2}$ Any       |                  | V TODAY-1 MONTH       | Potential Future Exposure               | $7/$ (45.500.000           | û                                |\n| Ø         | 360T Group Risk            | 360T Group Related E/ Any |               | ₩                                      | Any                 | $V$ Any                 | ₩                | TODAY-3 MONTHS EX     | Aggregate Gross Settlement Li., E/      | 50,000,000                 | û                                |\n\n<span id=\"page-23-1\"></span>Figure 25 Risk Portfolio Rules Upload/Download\n\nBy clicking on icon on top right of the *Risk Portfolio Rules* view, user can download the snapshot of their current risk portfolio rules as csv file. After making the necessary\n\nchanges in the downloaded file, user can upload the new rules by clicking on icon and then selecting the file.\n\nOnce a rule is uploaded, 360T`s Limit Monitor will create a result file to display the status of the changes. Admin user can save the result file, review it and then save or discard the changes.\n\n| Save As                      |                                                                               |                |                                      |          |                                        |                              |               | $\\times$  |        |                |                       |                                                         |                       |               |                            |      |\n|------------------------------|-------------------------------------------------------------------------------|----------------|--------------------------------------|----------|----------------------------------------|------------------------------|---------------|-----------|--------|----------------|-----------------------|---------------------------------------------------------|-----------------------|---------------|----------------------------|------|\n| $\\leftarrow$ $\\rightarrow$   | > This PC > Desktop > 360T Limits Monitor                                     |                |                                      | $\\vee$ 0 |                                        | C Search 360T Limits Monitor |               |           |        |                |                       |                                                         |                       |               |                            | 土土   |\n| New folder<br>Organize -     |                                                                               |                |                                      |          |                                        |                              | Bi +          | $\\bullet$ |        |                |                       |                                                         |                       |               |                            |      |\n| <sup>19</sup> VisualVM       | Name                                                                          | File ownership | Date modified                        |          | Type                                   | Size                         |               |           |        |                |                       | Constraints                                             |                       | $\\checkmark$  | <b>Rules Switch</b>        |      |\n| <b>P</b> WINDOWS             | <b>D</b> <sup>2</sup> 360T active limmo rules                                 |                | 10/05/2021 15:18                     |          | Microsoft Excel C                      |                              | $2$ KB        |           | Method |                | <b>Fx Time Period</b> | Algorithm                                               |                       | Limit         |                            |      |\n| This PC                      | <b>Di</b> 360T active limmo rules result<br>[360T_limmo_curreny_couple_groups |                | 10/05/2021 15:19<br>30/04/2021 17:32 |          | Microsoft Excel C<br>Microsoft Excel C |                              | 2 K B<br>1 KB |           |        |                | $V$ Any               | ■ Aggregate Net Settlement Limit ■                      |                       | 2.000.000.000 |                            |      |\n| 3D Objects<br><b>Desktop</b> | [34] 360T limmo curreny couple groups result                                  |                | 30/04/2021 17:21                     |          | Microsoft Excel C.                     |                              | 3 KB          |           |        |                |                       | TODAY-3 MONTHS 5 Net Daily Settlement Limit             | ₩                     | 62,000,000    | 畲                          |      |\n| <b>El Documents</b>          | <b>D</b> 360T limmo_rules                                                     |                | 16/05/2021 13:40                     |          | Microsoft Excel C                      |                              | 2 KB          |           |        |                |                       | TODAY-1 MONTH I Potential Future Exposure               | $\\mathbb{Z}^{\\times}$ | 56,500,000    |                            |      |\n| Downloads                    |                                                                               |                |                                      |          |                                        |                              |               |           |        | ₽              |                       | TODAY-3 MONTHS E Net Daily Settlement Limit             | $\\bar{\\nu}$           | 24,000,000    | ŵ                          |      |\n| Music                        |                                                                               |                |                                      |          |                                        |                              |               |           |        |                |                       | TODAY-1 MONTH 5 Potential Future Exposure               | $\\bar{z}$             | 2.000.000     | û                          |      |\n| Pictures<br><b>N</b> Videos  |                                                                               |                |                                      |          |                                        |                              |               |           |        | ∛              |                       | TODAY-3 MONTHS   Net Daily Settlement Limit             | $\\bar{z}$             | 40,000,000    | ŭ.                         |      |\n| Local Disk (C:)              |                                                                               |                |                                      |          |                                        |                              |               |           |        |                |                       | TODAY-1 MONTH I Potential Future Exposure               | $\\mathbb{Z}^{\\ell}$   | 34,500,000    |                            |      |\n| shares (\\\\office-            |                                                                               |                |                                      |          |                                        |                              |               |           |        | U)             |                       | TODAY-3 MONTHS $\\mathcal{V}$ Net Daily Settlement Limit | $\\overline{z}$        | 29,000,000    | û                          |      |\n|                              |                                                                               |                |                                      |          |                                        |                              |               |           |        | $\\overline{z}$ |                       | TODAY-1 MONTH   / Potential Future Exposure             | $\\overline{z}$        | 23,500,000    | 谙                          |      |\n|                              | File name: 360T_limmo_rules_result                                            |                |                                      |          |                                        |                              |               | ×.        |        |                |                       | TODAY-3 MONTHS 5 Net Daily Settlement Limit             | $\\bar{z}$             | 73,000,000    | â                          |      |\n| Save as type: CSV            |                                                                               |                |                                      |          |                                        |                              |               | $\\sim$    |        |                |                       | TODAY-1 MONTH 5 Potential Future Exposure               | $\\bar{z}$             | 67,500,000    | û                          |      |\n| $\\land$ Hide Folders         |                                                                               |                |                                      |          | Save                                   |                              | Cancel        |           |        | V              |                       | TODAY-3 MONTHS I Net Daily Settlement Limit             | $\\mathbb{Z}^{\\prime}$ | 51,000,000    | Ŵ                          |      |\n|                              |                                                                               |                |                                      |          |                                        |                              |               |           |        |                |                       | TODAY-1 MONTH 5 Potential Future Exposure               | $\\bar{z}$             | 45,500,000    | ă                          |      |\n|                              | $\\sigma$<br>360T Group Risk                                                   |                | 360T Group Related 5/ Any            |          | 5/ Any                                 |                              | 5/ Any        |           |        |                |                       | F   TODAY-3 MONTHS F   Aggregate Gross Settlement Li.   |                       | 50,000,000    | û                          |      |\n|                              | $\\sigma$<br>218631                                                            |                | 360T Group Related [ Any             |          | $V$ Any                                |                              | $V$ Any       |           |        |                |                       | 3 MONTHS-6 MO I Aggregate Gross Settlement LI. I        |                       | 20,000,000    | 貢                          |      |\n|                              |                                                                               |                |                                      |          |                                        |                              |               |           | $^{+}$ |                |                       |                                                         |                       |               |                            |      |\n|                              |                                                                               |                |                                      |          |                                        |                              |               |           |        |                |                       |                                                         |                       |               | <b>Discard all Changes</b> | Save |\n\n<span id=\"page-23-2\"></span>Figure 26 Upload Result File\n\nFor successful operation, csv should contain below columns:\n\nThree60tID,RuleId,Active,Counterpart,Portfolio,LegalEntity,Dealer,ExecutionMethod,TimePeriod,AlgorithmType,Limit\n\nColumn separator can be selected as \",\" or \";\" by using Preferences > Shared Settings in Bridge.\n\n|                        | <b>EXTREMENTARY COMMUNICATIONS COMPTAINERS IN THE SECOND CONTRACT OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREET OF STREE</b><br>. |\n|------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n|                        | $\\times$                                                                                                                                                                                                                                  |\n| Design Theme           | Other Settings                                                                                                                                                                                                                            |\n| <b>Display Size</b>    |                                                                                                                                                                                                                                           |\n| Acknowledgement        | 00 Enable sounds                                                                                                                                                                                                                          |\n| <b>Shared Settings</b> | Enable Auto Dealer ticket sounds                                                                                                                                                                                                          |\n|                        | 00 Use comma as decimal separator                                                                                                                                                                                                         |\n|                        | 0 0 Use semicolon as CSV separator                                                                                                                                                                                                        |\n|                        | O 0 Open the TWS pricing panels in new windows                                                                                                                                                                                            |\n|                        | 00 Disable animations                                                                                                                                                                                                                     |\n|                        |                                                                                                                                                                                                                                           |\n|                        |                                                                                                                                                                                                                                           |\n| <b>RBS PFE</b>         | M Any vs G10 M Any<br>TODAY-1 MONTH   Potential Future Exposure<br>V Any<br>(2.000.000)<br>RBS.LND.DEMO                                                                                                                                   |\n\n<span id=\"page-24-0\"></span>Figure 27 CSV Column separator setting\n\nPlease note that, **upload functionality works with snapshot strategy**. This means, the validated rules that are uploaded in the last batch becomes valid as a whole, once the changes are saved. Another way to say it, **if an existing rule is not uploaded in the new batch, then the relevant rule would be removed**.\n\nTherefore, when users\n\n- **a)** *update an existing rule(s)* (for example, changing the limit of a specific counterparty),\n- **b) add a new rule(s)** while keeping the existing configuration,\n\n**it is recommended to download the snapshot of existing configuration via download functionality, do the changes (i.e. change the limit) on the downloaded file and then upload the file again.**\n\nPlease also note that, while creating a new rule, *Three60tID* field should come with null value, whereas while updating an existing rule, *Three60tID* value of an existing rule shouldn`t be changed.\n\n| Field Name | Type   | Possible Values                                                                | Description                                                        |\n|------------|--------|--------------------------------------------------------------------------------|--------------------------------------------------------------------|\n| Three60tID | String | Determined by 360T.<br>It must be null to create a<br>new rule.                | Indicates the unique rule<br>ID assigned automatically<br>by 360T. |\n|            |        | The current value must be<br>provided<br>to<br>update<br>the<br>existing rule. |                                                                    |\n| RuleId     | String | All characters are allowed.                                                    | Defines<br>the<br>rule<br>ID<br>determined by client.              |\n\n|                 |         | When<br>left<br>empty,<br>Three60tID<br>value of the                                                                                                                                                 |                                                                                                                                          |\n|-----------------|---------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------|\n|                 |         | rule will be populated.                                                                                                                                                                              |                                                                                                                                          |\n| Active          | Boolean | TRUE, FALSE                                                                                                                                                                                          | Defines whether the tule<br>is set to enabled (TRUE)<br>or disabled (FALSE).                                                             |\n| Counterpart     | String  | Any or<br>360T System name of a<br>permissioned counterpart<br>or<br>Name<br>of<br>a<br>pre-defined<br>counterpart group.                                                                            | Defines the counterpart or<br>group of counterparts for<br>which the limit will be<br>applied for.                                       |\n| Portfolio       | String  | Any or                                                                                                                                                                                               | Defines the product and                                                                                                                  |\n|                 |         | Pre-Defined<br>portfolio<br>group name.                                                                                                                                                              | currency pairs for which<br>the limit will be applied for.                                                                               |\n| LegalEntity     | String  | Any or                                                                                                                                                                                               | Defines the legal entity or                                                                                                              |\n|                 |         | 360T System name of a<br>legal entity or                                                                                                                                                             | group of legal entities for<br>which the limit will be<br>applied for.                                                                   |\n|                 |         | a pre-defined legal entity<br>group.                                                                                                                                                                 |                                                                                                                                          |\n| Dealer          | String  | Any or                                                                                                                                                                                               | Defines<br>the<br>dealer<br>or                                                                                                           |\n|                 |         | 360T<br>System<br>name<br>of<br>credit entity`s user who<br>can trade or                                                                                                                             | group of dealers for which<br>the limit will be applied for.                                                                             |\n|                 |         | a pre-defined dealer group                                                                                                                                                                           |                                                                                                                                          |\n| ExecutionMethod | String  | Any or<br>A<br>single<br>supported<br>execution method (SEP,<br>RFS,<br>OMT,<br>MidMatch,<br>HST<br>Engine,<br>HST<br>OrderBook or GTX CLOB)<br>or<br>a<br>pre-defined<br>execution<br>method group. | Defines<br>the<br>execution<br>method(s) for which the<br>limit will be applied for.                                                     |\n| TimePeriod      | String  | Any or                                                                                                                                                                                               | Defines for which value                                                                                                                  |\n|                 |         | A single tenor combination<br>(for ex. SPOT-1 WEEK)                                                                                                                                                  | dates the limit will be<br>applied for.                                                                                                  |\n|                 |         | Or a pre-defined FX Time<br>Period group.                                                                                                                                                            |                                                                                                                                          |\n| AlgorithmType   | String  | Potential Future Exposure,<br>Net Daily Settlement Limit,<br>Gross<br>Daily<br>Settlement<br>Limit,<br>Aggregate Net Settlement<br>Limit,                                                            | Defines the method to be<br>used for limit check prior<br>to executon trade and to<br>calculate<br>the<br>utilization<br>post execution. |\n\n|       |         | Aggregate<br>Gross<br>Settlement Limit, |                                                                                                                        |\n|-------|---------|-----------------------------------------|------------------------------------------------------------------------------------------------------------------------|\n|       |         | Daily Net Trading Limit                 |                                                                                                                        |\n| Limit | Decimal | Positive values up to 13<br>digits.     | Defines<br>the<br>maximum<br>amount<br>that<br>can<br>be<br>utilized as per defined<br>parameters<br>and<br>algorithm. |\n\n<span id=\"page-26-2\"></span>Table 2 Field Definition for csv risk portfolio rule upload\n\n### <span id=\"page-26-0\"></span>**5 ACTIVE RULES**\n\nThe third tab of Risk Portfolio administration panel, called `Active Rules`, is a dashboard which shows currently active rules and the corresponding `Utilization` amount.\n\nAdmin users can do following operations in *Active Rules*:\n\n**1) Edit Active Limit**: With single-click on the *Limit* free-text area, it is possible to update the active limit amount of a risk portfolio rule. The change done in the *Limit* field is **effective immediately** and is only valid until day rollover: For the new limit to be extended permanently, it must be changed in the relevant `Risk Portfolio Rule`.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> | <b>Active Rules</b> | <b>Risk Portfolio PFE</b> | Ξ                   |                         |                       |                                       |                          |                    |                    | 土土 |\n|------------------------------|-----------------------------|---------------------|---------------------------|---------------------|-------------------------|-----------------------|---------------------------------------|--------------------------|--------------------|--------------------|----|\n| $\\sim$<br>$\\sim$             |                             |                     | →                         |                     |                         |                       |                                       |                          |                    | <b>Refresh All</b> |    |\n| Rule Id                      |                             | $\\vee$ Counterpart  | Portfolio                 | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                             | Limit                    | <b>Utilization</b> |                    |    |\n| Total Net Limit              |                             | Any                 | Any                       | Any                 | Any                     | Any                   | <b>Aggregate Net Settlement Limit</b> | $3 000,000,000 $ 803,806 |                    | <b>≝</b> ⊙∣∂       |    |\n\n<span id=\"page-26-1\"></span>Figure 28 Updating the limit for current trading date\n\n- **2) Define exceptional limit**: [Please see 5.3 for detailed explanation.](#page-41-1)\n- **3) Visualize the breakdown of the utilization**: Clicking on icon opens a new window as a pop-up in which the user can see the breakdown of the utilization. Please see section 5.3. for detailed explanation of this feature.\n- **4) Refresh the rule(s):** Clicking on icon next to an active rule brings the latest updated information to the display for the corresponding rule. Although 360T`s Limits Monitor has a real-time update of utilization calculations, it is required to refresh the view to be able to visualize the latest updates.\n\nPlease note that `Refresh All` button on top right of the rule dashboard triggers refresh for all active rules.\n\n- **5) Jump to the Rule:** By clicking on the 'Jump to the Rule' button , admin users can navigate to the corresponding rule under the `Risk Portfolio Rules` panel. This makes it easier to locate the rule that admin user wants to review, edit, or deactivate.\n- **6)** *Search within Active Rules*: The Active Rules tab has a search field where admin users can enter text to filter the rules they are looking for. This makes it easier for them to locate the active rule for which they are searching.\n\nLimit\n\n|                     | <b>Active Rules</b> |               | Ξ                   |                         |                       |                                  |            |                    |                    |\n|---------------------|---------------------|---------------|---------------------|-------------------------|-----------------------|----------------------------------|------------|--------------------|--------------------|\n| $Q$ 360T            |                     | $\\rightarrow$ |                     |                         |                       |                                  |            |                    | <b>Refresh All</b> |\n| Rule Id             | $\\vee$ Counterpart  | Portfolio     | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                        | Limit      | <b>Utilization</b> |                    |\n| 360TBANK Settlement | 360TBANK.TEST       | Anv           | Anv                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit       | 73,000,000 | $\\circ$            | 而のめ                |\n| <b>360TBANK PFE</b> | 360TBANK.TEST       | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure        | 67,500,000 | $\\circ$            | 面のめ                |\n| 360T.MMC Settlement | 360T.MMC            | Anv           | Any                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit       | 51,000,000 | $\\circ$            | 而らめ                |\n| 360T.MMC PFE        | 360T.MMC            | Any ys G10    | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure        | 45,500,000 | $\\circ$            | 面のめ                |\n| 360T Group Risk     | 360T Group Related  | Any           | Any                 | Anv                     | TODAY-3 MONTHS        | Aggregate Gross Settlement Limit | 50,000,000 | $\\circ$            | 而らめ                |\n\n<span id=\"page-27-0\"></span>Figure 29 Filtering the rules by using search function\n\n- **7) Download EOD (end-of-day) rates as csv file**: By clicking on , user can download the EOD rates which are used as reference rates to convert the risk exposures into company`s home currency.\n- **8) Download active rules as csv file:** By clicking on , user can download the active rules as csv file into their PC. The downloaded file will have below columns:\n\nThree60tID;RuleId;Active;Counterpart;Portfolio;LegalEntity;Dealer;ExecutionMethod;TimePeriod;AlgorithmType;\n\n**9) Bulk update active limits via csv upload:** Users can update the active limit amount of multiple rules by using csv upload functionality within Active Rules tab. After changing the limit in the downloaded csv file, user can upload the new rules\n\nby clicking on icon and then selecting the file.\n\nFor a successful operation, users can provide all below columns. Although all these columns are required, system will only validate *Three60tID and Limit values.* This means, any changes in other values will be disregarded.\n\nThree60tID;RuleId;Active;Counterpart;Portfolio;LegalEntity;ExecutionMethod;TimePeriod;AlgorithmType;Limit\n\nAfter the file is uploaded, *Limits Monitor* will create a result file to provide feedback to the user.\n\n| Field Name                                                                                                         | Type    | Possible Values                                                                                            | Description                                                                                                                                                                                                                              |\n|--------------------------------------------------------------------------------------------------------------------|---------|------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Three60tID                                                                                                         | String  | Determined by 360T.<br>The<br>current<br>value<br>must be provided to<br>update<br>the<br>active<br>limit. | Indicates<br>the<br>unique<br>rule<br>ID<br>assigned<br>automatically by 360T.                                                                                                                                                           |\n| RuleId,<br>Active, Counterpart,<br>Portfolio. LegalEntity, Dealer<br>ExecutionMethod,<br>TimePeriod, AlgorithmType | Any     | Can be any value.<br>System<br>will<br>ignore<br>these<br>fields<br>to<br>operate limit update.            | Please<br>see<br>….<br>For<br>detailed description of<br>the field. Since these<br>fields are only editable<br>via Risk Portfolio rule<br>configuration, provided<br>values in Active Rules<br>upload<br>will<br>not<br>be<br>validated. |\n| Limit                                                                                                              | Decimal | Positive values up to<br>13 digits.                                                                        | Defines the maximum<br>amount that can be<br>utilized as per defined<br>parameters<br>and<br>algorithm.                                                                                                                                  |\n\n<span id=\"page-28-3\"></span>Table 3: Field definition for csv active limit upload\n\n### <span id=\"page-28-0\"></span>**5.1 Applying rules and limits to trades**\n\nOnce a deal is negotiated or order is placed, the system determines which rule matches and carries out a credit check against the limit within that rule, for example:\n\n*Credit Entity A* has created risk portfolio rules for its counterpart 360TBANK.TEST as shown in the screenshot below.\n\n| <b>Risk Portfolio Rules</b><br><b>Risk Portfolio Groups</b> | <b>Active Rules</b> | <b>Risk Portfolio PFE</b> | Ξ                   |                         |                       |                            |            |                                          | 土土                                                      |\n|-------------------------------------------------------------|---------------------|---------------------------|---------------------|-------------------------|-----------------------|----------------------------|------------|------------------------------------------|---------------------------------------------------------|\n| Q 360TBANK                                                  |                     | $\\rightarrow$             |                     |                         |                       |                            |            |                                          | <b>Refresh All</b>                                      |\n| Rule Id                                                     | $\\vee$ Counterpart  | Portfolio                 | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                  | Limit      | <b>Utilization</b>                       |                                                         |\n| <b>360TBANK Settlement</b>                                  | 360TBANK.TEST       | Any                       | Any                 | Any                     | TODAY-TODAY           | Net Daily Settlement Limit | 73,000,000 | $\\overline{0}$                           | $\\frac{1}{2}$ $\\frac{1}{2}$ $\\frac{1}{2}$ $\\frac{1}{2}$ |\n| <b>360TBANK PFE</b>                                         | 360TBANK.TEST       | Any ys G10                | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure  | 67,500,000 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | 而のめ                                                     |\n\n<span id=\"page-28-2\"></span>Figure 30 Capturing the Risk Entries\n\n360TBANK.TEST and *Credit Entity A* have executed a EUR/USD Forward with value date tomorrow.\n\nOnly The 1st rule applies to the deal as all filtering parameters match (counterpart, portfolio, legal entity group and fx time period).\n\nThe 2 nd rule doesn`t match as its tenor is limited to value today. (see FX Time Period column in the rule).\n\n#### <span id=\"page-28-1\"></span>**5.1.1 Reallocating Trades<sup>4</sup> After End of Day Rollover:**\n\nLimits Monitor validates any changes made to Risk Portfolio Rules at New York 5 PM. It then validates rules applied to trades for the new value date, for example:\n\nIn the EURUSD forward for value tomorrow, between Credit Entity A and its counterpart 360TBANK.TEST, as of the trade date only the first rule applies, as explained previously.\n\nAfter end of day rollover, the tomorrow tenor would become value Today and therefore both 1st and 2nd rules would apply.\n\nIMPORTANT: Please note as a result of change in risk portfolio rule or change in trades that match with a specific risk portfolio rule due to change in value dates, the utilization amount may exceed the active limit after end of day rollover. For example, if user extends the end of Fx Time Period of a rule from 1 Week to 2 Weeks, trades with tenors between 1 to 2 weeks will also be utilized during the rollover phase and this\n\n<sup>4</sup> IMPORTANT: Please note that the system handles each leg of a trade separately. This means, for Swap or Block Trades, active rules apply to the legs separately. If one leg of the request/order breaches the limit, the entire request/order is blocked.\n\nmay result in a higher utilization which may exceed the limit for that specific risk portfolio rule.\n\n### <span id=\"page-29-0\"></span>**5.2 Calculating Utilization**\n\nUtilization shows the amount of the exposure calculated per active rule, based on the captured cashflow (trades) and the assigned algorithm. While filtering parameters (Counterpart, Portfolio, Dealer, Legal Entity, Execution Method and FX Time Period) determine which trades should be captured and checked, the algorithm assigned to the risk portfolio is the function which calculates the exposure based on the captured risk events (cashflow derived by a trade).\n\nUtilization is updated on the system whenever a trade is booked or an order is placed to a provider (if credit entity is requester) or a placed order is accepted by the provider (if credit entity is provider). In case of placing an order into an Order Book such as MidMatch, utilization is only updated when there is a match and match is allowed only if the counterpart has been allocated for sufficient limit.\n\nIn order to see the most up-to-date utilization values on the UI, users should click the `Refresh All` button on top of the rule dashboard.\n\n| <b>Risk Portfolio Rules</b><br><b>Risk Portfolio Groups</b> | <b>Active Rules</b> | <b>Risk Portfolio PFE</b> | $\\equiv$            |                         |                       |                                         |               |                    |                    |     |\n|-------------------------------------------------------------|---------------------|---------------------------|---------------------|-------------------------|-----------------------|-----------------------------------------|---------------|--------------------|--------------------|-----|\n| Q                                                           |                     | $\\rightarrow$             |                     |                         |                       |                                         |               |                    | <b>Refresh All</b> |     |\n| <b>Rule Id</b>                                              | $\\vee$ Counterpart  | Portfolio                 | <b>Legal Entity</b> | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                               | Limit.        | <b>Utilization</b> |                    |     |\n| <b>Total Net Limit</b>                                      | Any                 | Any                       | Any                 | Any                     | Any                   | Aggregate Net Settlement Limit          | 2,000,000,000 | 876,867            |                    | 面のめ |\n| <b>SEB Settlement</b>                                       | SEB.FRA.DEMO        | Any                       | Any                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit              | 62.000.000    | 5,618              |                    | 面のめ |\n| SEB PFE                                                     | SEB.FRA.DEMO        | Any vs G10                | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure               | 56,500,000    | 600                |                    | 而のめ |\n| <b>RRS Settlement</b>                                       | <b>RBS LND DEMO</b> | Anv                       | Anv                 | Anv                     | TODAY-3 MONTHS        | Net Daily Settlement Limit              | 24,000,000    | 67.442             |                    | 面のめ |\n| <b>RBS PFE</b>                                              | RBS.LND.DEMO        | Any ys G10                | Anv                 | Anv                     | TODAY-1 MONTH         | Potential Future Exposure               | 2,000,000     | 36,000             |                    | 前のめ |\n| <b>PEBANK Settlement</b>                                    | PEBANK_APAC.TEST    | Any                       | Any                 | Anv                     | TODAY-3 MONTHS        | Net Daily Settlement Limit              | 40,000,000    | $\\bullet$          |                    | 而のめ |\n| PEBANK PFE                                                  | PEBANK_APAC.TEST    | Any ys G10                | Any                 | Anv                     | TODAY-1 MONTH         | Potential Future Exposure               | 34,500,000    | $\\circ$            |                    | 而のめ |\n| <b>COBA Settlement</b>                                      | COBA.DEMO           | Any                       | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit              | 29,000,000    | $\\bullet$          |                    | 面のめ |\n| <b>COBA PFE</b>                                             | COBA.DEMO           | Any ys G10                | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure               | 23,500,000    | $\\circ$            |                    | 而のめ |\n| <b>360TBANK Settlement</b>                                  | 360TBANK.TEST       | Any                       | Any                 | Any                     | <b>TODAY-3 MONTHS</b> | Net Daily Settlement Limit              | 73,000,000    | $\\circ$            |                    | 面のめ |\n| <b>360TBANK PFE</b>                                         | 360TBANK.TEST       | Any vs G10                | Any                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure               | 67,500,000    | $\\bullet$          |                    | 而らめ |\n| 360T.MMC Settlement                                         | 360T.MMC            | Any                       | Any                 | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit              | 51,000,000    | $\\bullet$          |                    | 面のめ |\n| 360T.MMC PFE                                                | 360T.MMC            | Any ys G10                | Anv                 | Any                     | TODAY-1 MONTH         | Potential Future Exposure               | 45,500,000    | $\\circ$            |                    | 而らめ |\n| 360T Group Risk                                             | 360T Group Related  | Anv                       | Any                 | Anv                     | TODAY-3 MONTHS        | <b>Aggregate Gross Settlement Limit</b> | 50,000,000    | $\\circ$            |                    | 面のめ |\n\n<span id=\"page-29-1\"></span>Figure 31 Monitoring the updated utilization amounts within Active Rules tab.\n\nThe calculation steps of each algorithm are explained in the following sub-sections using the same set of trades and risk portfolios for ease of comparison:\n\n*Example:*\n\n*Bank A* has defined some risk portfolio rules to limit its counterparty risk for *Counterparty A*, as well as to limit the trading activities of its own dealers.\n\nBank A has executed the below trades with Counterparty A.\n\n| ID | Trade Date | Value<br>Date | Currency Pair | Currency1 | Currency2 | Action | Notional<br>Currency | Notional<br>Amount | Executed<br>Rate |\n|----|------------|---------------|---------------|-----------|-----------|--------|----------------------|--------------------|------------------|\n| 1  | 04.10.2019 | 04.10.2019    | USD/TRY       | USD       | TRY       | Sell   | USD                  | 1,000,000          | 5.6545           |\n| 2  | 04.10.2019 | 04.10.2019    | EUR/USD       | EUR       | USD       | Buy    | EUR                  | 2,000,000          | 1.1105           |\n| 3  | 03.10.2019 | 07.10.2019    | EUR/USD       | EUR       | USD       | Buy    | EUR                  | 2,000,000          | 1.1118           |\n| 4  | 03.10.2019 | 07.10.2019    | CHF/EUR       | EUR       | CHF       | Sell   | EUR                  | 1,000,000          | 1.0911           |\n| 5  | 04.10.2019 | 11.10.2019    | EUR/GBP       | EUR       | GBP       | Buy    | GBP                  | 2,000,000          | 0.8948           |\n| 6  | 04.10.2019 | 11.10.2019    | EUR/USD       | EUR       | USD       | Sell   | USD                  | 3,000,000          | 1.1158           |\n| 7  | 04.10.2019 | 11.10.2019    | GBP/TRY       | GBP       | TRY       | Sell   | GBP                  | 4,000,000          | 7.0258           |\n\nTable 4: Trades executed between Counterparty A and Bank A as of 04.10.2019\n\nBelow 360T EOD Rates are used as reference rate to convert different currency exposure into one single currency (in our example, it is USD).\n\n| Currency Pair | EOD Rates |\n|---------------|-----------|\n| USD/TRY       | 5.6520    |\n| EUR/USD       | 1.1050    |\n| GBP/USD       | 1.2525    |\n| USD/CHF       | 0.9995    |\n| USD/USD       | 1         |\n\nTable 5: 360T EOD rates to convert Cashflow/Notional Ledgers into USD (credit currency).\n\n<span id=\"page-30-1\"></span><span id=\"page-30-0\"></span>:\n\n### <span id=\"page-31-0\"></span>**5.2.1 Net Daily Settlement Limit**\n\nAs explained in [Section 4.5,](#page-19-0) net settlement limit algorithms net the cashflows per currency account. Combining the net settlement limit with the daily method, the Net Daily Settlement Limit algorithm calculates the net cashflow for each value date of the relevant Risk Portfolio Rule. All payables and receivables in terms of credit currency are then summed separately into daily receivable and payable exposures. The larger of these amounts is considered as the net settlement exposure for that specific value date.\n\nThe maximum utilization amount across the defined time period is displayed as utilization amount.\n\n|  |  | For example; Bank A has set below Risk Portfolio Rule for Counterparty A |\n|--|--|--------------------------------------------------------------------------|\n\n| Counterpart       | Portfolio | Legal<br>Group | Entity | Fx<br>Period | Time | Algorithm         |       | Limit      |\n|-------------------|-----------|----------------|--------|--------------|------|-------------------|-------|------------|\n| Counterparty<br>A | Any       | Any            |        | TODAY – 1Y   |      | Net<br>Settlement | Daily | 15,000,000 |\n\nThe rule would apply all of the trades in Table 1 and the algorithm does the following to calculate utilization:\n\n*Step 1:* Convert trades into payables/receivables by using execution rates to calculate the term amount.\n\nCashflows are calculated as follows:\n\nIf notional currency = base currency, then\n\n- Base currency cashflow is the signed<sup>5</sup> notional amount (buy = positive/receivable, sell = negative/payable)\n- Quote currency cashflow is (Base Currency Cashflow) x (Executed Rate) x (-1)\n\nIf notional currency = quote currency, then:\n\n- Quote currency cashflow equals the signed notional amount (buy = positive/receivable, sell = negative/payable)\n- Base currency cashflow equals to (Quote Currency Cashflow) / (Executed Rate) x (-1)\n\nFor our example portfolio, the trades are translated into below payable and receivable cashflows:\n\n| ID | Value Date | Receivable<br>Currency | Receivable<br>Cashflow | Payable Currency | Payable<br>Cashflow |\n|----|------------|------------------------|------------------------|------------------|---------------------|\n| 1  | 04.10.2019 | TRY                    | 5,654,500              | USD              | -1,000,000          |\n\n<sup>5</sup> To be able to net the opposite direction of cashflows, payables are multiplied with (-1) and displayed as negative.\n\n| 2 | 04.10.2019 | EUR | 2,000,000  | USD | -2,221,000 |\n|---|------------|-----|------------|-----|------------|\n| 3 | 07.10.2019 | EUR | 2,000,000  | USD | -2,223,600 |\n| 4 | 07.10.2019 | CHF | 1,091,100  | EUR | -1,000,000 |\n| 5 | 11.10.2019 | GBP | 2,000,000  | EUR | -2,235,136 |\n| 6 | 11.10.2019 | EUR | 2,688,654  | USD | -3,000,000 |\n| 7 | 11.10.2019 | TRY | 28,103,200 | GBP | -4,000,000 |\n\n<span id=\"page-32-0\"></span>Table 6: Trades converted into cashflow.\n\n*Step 2:* Net all cashflow entries per each currency and each value date.\n\n| Currency | 04.10.2019 | 07.10.2019 | 11.10.2019 |\n|----------|------------|------------|------------|\n| USD      | -3,221,000 | -2,223,600 | -3,000,000 |\n| TRY      | 5,654,500  | 0          | 28,103,200 |\n| GBP      | 0          | 0          | -2,000,000 |\n| EUR      | 2,000,000  | 1,000,000  | 453,518    |\n| CHF      | 0          | 1,091,100  | 0          |\n\n<span id=\"page-32-1\"></span>Table 7: Daily net cashflow ledger\n\n*Step 3:* Convert cashflows into USD (credit currency of Bank A) by using 360T EOD rates.\n\nIMPORTANT: Please note that, system converts cashflows into the credit currency already in Step 1. Step 2 (Table 4) is displayed solely for the purpose of demonstrating the calculations.\n\n| Cashflow (in terms of credit currency) | 04.10.2019 | 07.10.2019 | 11.10.2019 |\n|----------------------------------------|------------|------------|------------|\n| USD                                    | -3,221,000 | -2,223,600 | -3,000,000 |\n| TRY                                    | 1,000,442  | 0          | 4,972,258  |\n| GBP                                    | 0          | 0          | -2,505,000 |\n| EUR                                    | 2,210,000  | 1,105,000  | 453,518    |\n| CHF                                    | 0          | 1,091,100  | 0          |\n\n<span id=\"page-32-2\"></span>Table 8: Daily net cashflow ledger converted into credit currency.\n\n*Step 4:* Sum all net currency positions which are short (cashflow where Bank A is a net payer) and sum all net currency positions which are long (cashflow where Bank A is a net receiver) for each value date.\n\n| Cashflow (in terms of credit currency) | 04.10.2019 | 07.10.2019 | 11.10.2019 |\n|----------------------------------------|------------|------------|------------|\n| Receivable                             | 3,210,442  | 2,196,646  | 5,473,394  |\n| Payable                                | -3,221,000 | -2,223,600 | -5,505,000 |\n\n<span id=\"page-32-3\"></span>Table 9: Daily payable and receivable exposure.\n\n*Step 5:* Calculate the daily net settlement exposure on each value date by taking the higher value between the receivable and the absolute value of payable.\n\n|                         | 04.10.2019 | 07.10.2019 | 11.10.2019 |\n|-------------------------|------------|------------|------------|\n| Net Settlement Exposure | 3,221,060  | 2,223,600  | 5,505,000  |\n\n<span id=\"page-32-4\"></span>Table 10: *Daily net settlement risk exposure*\n\n*Step 6:* Display the final utilization value by calculating the maximum settlement exposure on each value date across the credit horizon.\n\n#### **Utilization** : *5,505,000 USD*\n\n*IMPORTANT: Please note* that the limit defined for a risk portfolio with daily algorithms (for both gross and net) is valid for each value date for the defined time period. This means a 15,000,000 USD limit applied to our example enables user to trade another 11,779,000 USD (15,000,000 – 3,221,000) for value date 10.04.2019. Please also note that once utilization exceeds limit for a certain value date (which currently can only happen if the limit is reduced), the respective rule will block trading for other value dates as well i.e. if limit is reduced to 5,000,000 in above example, Counterparty A won`t be allowed to trade for any other value dates as well.\n\n#### <span id=\"page-33-0\"></span>**5.2.2 Gross Daily Settlement Limit**\n\nAs explained in [Section 4.8.1,](#page-20-1) gross settlement limit algorithms base the calculation of the risk exposure on the gross value. Combining gross settlement limit with daily calculation method, the Gross DSL algorithm calculates notional exposure for each value date of the relevant risk portfolio. Notional exposures from each trade are first converted in terms of credit currency and then summed up with respect to its value dates.\n\nThe maximum utilization amount across the defined time period is displayed as utilization amount.\n\n| Counterpart       | Portfolio | Legal<br>Group | Entity | Fx Time Period | Algorithm           |       | Limit      |\n|-------------------|-----------|----------------|--------|----------------|---------------------|-------|------------|\n| Counterparty<br>A | Any       | Any            |        | TODAY – 1Y     | Gross<br>Settlement | Daily | 25,000,000 |\n\nFor example; Bank A has set below Risk Portfolio Rule for Counterparty A.\n\nThe rule would apply all of the trades in Table 1 and the algorithm does the following to calculate utilization:\n\n*Step 1*: Sum all notional amounts (in terms of credit currency) with respect to value date to calculate gross daily exposure.\n\n04.10.2019 Exposure = 1,000,000 USD from *Trade Id1* + 2,210,000 USD from *Trade ID2* = 3,210,000 USD\n\n07.10.2019 Exposure = 2,210,000 USD from *Trade Id3* + 1,105,000 USD from *Trade Id4* = 3,315,000 USD\n\n11.10.2019 Exposure = 2,505,000 USD from *Trade Id5* + 3,000,000 USD from *Trade Id6 +* 5,010,000 USD from *Trade Id7 = 10,515,000 USD*\n\n|                                    | 04.10.2019 | 07.10.2019 | 11.10.2019 |\n|------------------------------------|------------|------------|------------|\n| Daily Gross Settlement<br>Exposure | 3,210,000  | 3,315,000  | 10,515,000 |\n\n<span id=\"page-33-1\"></span>Table 11: Daily gross settlement exposure in terms of credit currency\n\n*Step 2:* Calculate total utilization value by using the maximum gross daily exposures across the credit horizon.\n\n#### **Utilization:** *10,515,000 USD*\n\n*IMPORTANT:* Please note that the limit defined for a risk portfolio with daily algorithms (both for gross and net) is valid for each value date for the defined time period. So, in the above example, the defined limit applies to each value date within the defined period which means on value date 04.10.2019, Counterparty A has a free credit line of 21,790,000 USD (25,000,000 – 3,210,000) equivalent.\n\n#### ********* Gross DSL with Settlement Period**\n\nIn order to account for the cutoff time differences in settling the transactions with a correspondent bank in overseas, some banks want to reflect the settlement risk exposures to the previous and next business dates.\n\nTo address this requirement, Limits Monitor provides another limit type called Gross DSL with Settlement Period. Different than Gross Daily Settlement Limit, in Gross DSL with Settlement Period algorithm, limit check is conducted for previous and next business days. The resulting exposure is also registered for entire settlement period.\n\nLimits Monitor provides some additional configuration to allow clients to set the settlement period for certain currencies as 2 days (Settlement Date and next business date).\n\nPlease contact Client Advisory Services team for activation.\n\n#### <span id=\"page-34-0\"></span>**5.2.3 Aggregate Net Settlement Limit**\n\nCombining net settlement limit with aggregate method, the Aggregate Net Settlement Limit algorithm calculates the net cashflow for each currency account across all FX Time Periods and doesn't go through separate calculation for each value date. Calculated net cashflows for each currency account are then converted into credit currency by using EOD rates. All payables and receivables (in terms of credit currency) are summed up separately to calculate the aggregate receivable and payable exposure. The maximum of the aggregate payables and receivables exposure is considered as the aggregate net settlement exposure for the relevant risk portfolio rule.\n\n| Counterpart       | Portfolio | Legal<br>Entity<br>Group | Fx<br>Time<br>Period | Algorithm                | Limit      |\n|-------------------|-----------|--------------------------|----------------------|--------------------------|------------|\n| Counterparty<br>A | Any       | Any                      | TODAY –<br>1Y        | Aggregate Net Settlement | 25,000,000 |\n\nFor example; Bank A has set below Risk Portfolio Rule for Counterparty A.\n\nThe rule would apply all of the trades in Table 1 and the algorithm does the following to calculate utilization:\n\n*Step 1:* Convert trades into payables/receivables by using execution rates to calculate opposite amount.\n\nCashflows are calculated as below:\n\nIf notional currency is base currency, then\n\n- Base currency cashflow is the signed<sup>6</sup> notional amount (buy = positive/receivable, sell = negative/payable)\n- Quote currency cashflow is (Base Currency Cashflow) X (Executed Rate) X (-1)\n\nIf notional currency is quote currency then:\n\n- Quote currency cashflow equals the signed notional amount (buy = positive/receivable, sell = negative/payable)\n- Base currency cashflow equals to (Quote Currency Cashflow) / (Executed Rate) x (-1)\n\nPlease refer to Table 3 for detailed cashflow.\n\n*Step 2:* Sum all cashflow entries per each cashflow currency account.\n\n| Currency | Aggregated Cashflow |\n|----------|---------------------|\n| USD      | -8,444,660          |\n| TRY      | 33,757,760          |\n| GBP      | -2,000,000          |\n| EUR      | 3,453,519           |\n| CHF      | 1,091,100           |\n\n<span id=\"page-35-0\"></span>Table 12: *Aggregated cashflow ledger.*\n\n*Step 3:* Convert aggregated cashflow entries into one single credit currency (for Bank A, USD) by using 360T EOD rates.\n\nIMPORTANT: Please note that, system converts all cashflows into the credit currency in Step 1 Step 2 (Table 9) is displayed for the purpose of demonstrating the calculations.\n\n| Currency | Aggregated Cashflow<br>(USD) |\n|----------|------------------------------|\n| USD      | -8,444,660                   |\n| TRY      | 5,972,700                    |\n| GBP      | -2,505,000                   |\n| EUR      | 3,816,137                    |\n| CHF      | 1,091,646                    |\n\n<span id=\"page-35-1\"></span>Table 13: Aggregated cashflow ledger converted into credit currency USD.\n\n*Step 4:* Sum all payables (cashflow where Bank A is a net payer) and receivables (cashflow where Bank A is a net receiver).\n\n|                    | Aggregated Cashflow |  |\n|--------------------|---------------------|--|\n| Cashflow Direction | (USD)               |  |\n\n<sup>6</sup> To be able to net the opposite direction of cashflows, payables are multiplied with (-1) and displayed as negative.\n\n| Receivable | 10,880,483  |\n|------------|-------------|\n| Payable    | -10,949,660 |\n\n<span id=\"page-36-2\"></span>Table 14: *Aggregated receivable and payable exposures.*\n\n*Step 5:* Utilize the higher absolute value of receivable and payable exposures\n\n#### **Utilization** : *10,949,660 USD*\n\n#### <span id=\"page-36-0\"></span>**5.2.4 Aggregate Gross Settlement Limit**\n\nCombining gross settlement limit with aggregate method, the Aggregate Gross Settlement Limit algorithm calculates the notional exposure across the defined FX Time Period of a Risk Portfolio Rule and does not go through separate calculation steps for each value date and currency account. Notional exposures from each trade are converted into the credit currency by using 360T end-of-day rates and then summed. The result gives the aggregate gross settlement exposure for that specific risk portfolio.\n\nFor example; Bank A has set below Risk Portfolio Rule for Counterparty A.\n\n| Counterpart       | Portfolio | Legal<br>Entity<br>Group | Fx<br>Time<br>Period | Algorithm                        | Limit      |\n|-------------------|-----------|--------------------------|----------------------|----------------------------------|------------|\n| Counterparty<br>A | Any       | Any                      | TODAY –<br>1Y        | Aggregate<br>Gross<br>Settlement | 25,000,000 |\n\nThe rule would apply all of the trades in Table 1 and the algorithm calculates the utilization amount as follows:\n\n1,000,000 USD (from *Trade Id1)* + 2,210,000 USD (from *Trade ID2)* + 2,210,000 USD (from *Trade Id3)* + 1,105,000 USD (from *Trade Id4*) + 2,505,000 USD (from *Trade Id5)*  + 3,000,000 USD (from *Trade Id6) +* 5,010,000 USD (from *Trade Id7) = 17,040,000 USD*\n\n#### **Utilization:** *17,040,000 USD*\n\n#### <span id=\"page-36-1\"></span>**5.2.5 Potential Future Exposure**\n\nAs explained in [section 4.8.3,](#page-21-1) instead of considering the notional amounts, the Potential Future Exposure algorithm uses different weighing factors assigned to currency pair and day left to the maturity within the Risk Portfolio PFE table, and multiplies the notional amount by these factors.\n\n*For example*; Bank A has set below Risk Portfolio Rule for Counterparty A with Potential Future Exposure algorithm assigned to the rule and assigned PFE factors to currency pair and day left to maturity as it is defined in Table 12.\n\n| Counterpart       | Portfolio | Legal<br>Entity<br>Group | Fx<br>Time<br>Period | Algorithm                 | Limit      |\n|-------------------|-----------|--------------------------|----------------------|---------------------------|------------|\n| Counterparty<br>A | Any       | Any                      | TODAY –<br>1Y        | Potential Future Exposure | 25,000,000 |\n\n| Currency Couple / PFE Days | 1    | 4    | 8    | 16   |\n|----------------------------|------|------|------|------|\n| USD/TRY                    | 2.0% | 3.5% | 5.0% | 8.0% |\n| EUR/USD                    | 0.5% | 1.6% | 1.8% | 2.0% |\n| EUR/CHF                    | 0.5% | 0.4% | 0.6% | 0.8% |\n| EUR/GBP                    | 0.6% | 1.7% | 2.0% | 2.3% |\n| GBP/TRY                    | 2.2% | 3.7% | 5.3% | 8.5% |\n\n<span id=\"page-37-0\"></span>Table 15: Risk Portfolio PFE Table\n\nThe rule would apply all of the trades in Table 1 and the algorithm follows below steps to calculate utilization amount:\n\n#### *Step 1:* Determine the PFE factor by looking up Risk Portfolio PFE.\n\n*Under the assumption of the trade (today`s) date is 04.10.2019*, day to maturity for EURUSD with value date 07.10.2019 is (07.10.2019 -04.10.2019) = 3 days.\n\nThe columns of the Risk Portfolio PFE table represents the number of days using `up to` logic. The lowest value 1, in our example, thus matches with any trade which has maximum 1 day left to maturity; the second column for 4 days captures trade legs with day lef to maturity between 1 days but up to 4 days and so on.\n\nFollowing this logic, a value of 1.60% would be selected as the PFE factor (EUR/USD, 4 Days).\n\n*Step 2*: Calculate PFE exposure value for each trade by multiplying the notional amount of trade with PFE factor and convert the value into credit currency.\n\n| ID | Value Date | Ccy Pair | Ccy1 | Ccy2 | Notional<br>Currency | Notional<br>Amount | PFE<br>Factor | PFE<br>Value | PFE<br>Value<br>(USD) |\n|----|------------|----------|------|------|----------------------|--------------------|---------------|--------------|-----------------------|\n| 1  | 04.10.2019 | USD/TRY  | USD  | TRY  | USD                  | 1,000,000          | 2.00%         | 20,000       | 20,000                |\n| 2  | 04.10.2019 | EUR/USD  | EUR  | USD  | EUR                  | 2,000,000          | 0.50%         | 10,000       | 11,050                |\n| 3  | 07.10.2019 | EUR/USD  | EUR  | USD  | EUR                  | 2,000,000          | 1.60%         | 32,000       | 35,360                |\n| 4  | 07.10.2019 | EUR/CHF  | EUR  | CHF  | EUR                  | 1,000,000          | 0.40%         | 4,000        | 4,420                 |\n| 5  | 11.10.2019 | EUR/GBP  | EUR  | GBP  | GBP                  | 2,000,000          | 2.00%         | 40,000       | 50,100                |\n| 6  | 11.10.2019 | EUR/USD  | EUR  | USD  | USD                  | 3,000,000          | 1.80%         | 54,000       | 54,000                |\n| 7  | 11.10.2019 | GBP/TRY  | GBP  | TRY  | GBP                  | 4,000,000          | 5.30%         | 212,000      | 265,530               |\n\n<span id=\"page-38-1\"></span>Table 16: PFE Value calculation for each risk entry.\n\n*Step 3:* Sum all *PFE exposures* to calculate aggregate PFE utilization.\n\n**Utilization: 440,460 USD**\n\n#### <span id=\"page-38-0\"></span>**5.2.6 Daily Net Trading Limit**\n\nAs explained in [section 4.8.2.1,](#page-21-3) Daily Net Trading Limit algorithm limits the net amount of trades done on a single trading day. Similar to the Aggregate Net Settlement Limit algorithm, Daily Net Trading Limit nets all the cashflow per currency account for all value dates across the credit horizon. The only difference is that the Daily Net Trading Limit algorithm only nets the cashflows generated within the current trading day and not for all outstanding (not settled) transactions.\n\nFor example; Bank A has set below Risk Portfolio Rule for Counterparty A.\n\n| Counterpart       | Portfolio | Legal<br>Entity<br>Group | Fx<br>Time<br>Period | Algorithm         | Limit      |\n|-------------------|-----------|--------------------------|----------------------|-------------------|------------|\n| Counterparty<br>A | Any       | Any                      | TODAY –<br>1Y        | Daily Net Trading | 25,000,000 |\n\n*Under the assumption of current date = 04.10.2019, t*he rule would apply to 5 of the trades in Table 1 and the algorithm calculates the utilization amount as follows:\n\n*Step 1:* Convert trades into payables/receivables by using execution rates to calculate opposite amount (Note: Trade Id3 and Id4 are filtered as they have a trade date of 03.10.2019).\n\n| ID | Trade Date | Value Date | Receivable<br>Currency | Receivable<br>Cashflow | Payable Currency | Payable<br>Cashflow |\n|----|------------|------------|------------------------|------------------------|------------------|---------------------|\n| 1  | 04.10.2019 | 04.10.2019 | TRY                    | 5,654,500              | USD              | -1,000,000          |\n| 2  | 04.10.2019 | 04.10.2019 | EUR                    | 2,000,000              | USD              | -2,223,060          |\n| 5  | 04.10.2019 | 11.10.2019 | GBP                    | 2,000,000              | EUR              | -2,235,136          |\n| 6  | 04.10.2019 | 11.10.2019 | EUR                    | 2,688,655              | USD              | -3,000,000          |\n| 7  | 04.10.2019 | 11.10.2019 | TRY                    | 28,103,200             | GBP              | -4,000,000          |\n\n<span id=\"page-39-0\"></span>Table 17: *Trades translated into cashflow.*\n\n*Step 2:* Sum all cashflow entries for each cashflow currency account.\n\n| Cashflow Currency | Aggregated Cashflow |\n|-------------------|---------------------|\n| USD               | -6,221,000          |\n| TRY               | 33,757,700          |\n| GBP               | -2,000,000          |\n| EUR               | 3,453,518           |\n\n<span id=\"page-39-1\"></span>Table 18: *Aggregated cashflow ledger.*\n\n*Step 3:* Convert aggregated cashflow entries into one single credit currency (for Bank A, USD) using 360T EOD rates.\n\nIMPORTANT: Please note that, system converts cashflows into credit currency already in Step 1. Step 2 (Table 15) is displayed for the purpose of demonstrating the calculations.\n\n| Currency | Aggregated Cashflow<br>(USD) |\n|----------|------------------------------|\n| USD      | -6,221,000                   |\n| TRY      | 5,972,700                    |\n| GBP      | -2,505,000                   |\n| EUR      | 3,816,137                    |\n\n<span id=\"page-40-1\"></span>Table 19: Aggregated cashflow ledger converted into credit currency USD.\n\n*Step 4:* Sum all payables (cashflow where Bank A is a net payer) and receivables (cashflow where Bank A is a net receiver).\n\n| Cashflow Direction | Aggregated Cashflow<br>(USD) |  |  |\n|--------------------|------------------------------|--|--|\n| Receivable         | 9,788,836                    |  |  |\n| Payable            | -8,726,000                   |  |  |\n\n<span id=\"page-40-2\"></span>Table 20: *Aggregated receivable and payable exposures.*\n\n*Step 5:* Utilize the higher absolute value of the receivable and payable exposures **Utilization** : *9,788,836 USD*\n\n#### <span id=\"page-40-0\"></span>**5.2.7 Daily Gross Trading Limit**\n\nAs explained in [section 4.8.2.2,](#page-21-4) Daily Gross Trading Limit algorithm limits the total gross volume of trades done on a single trading day. Daily Gross Trading Limit sums the notional amount converted to company currency for all value dates across the credit horizon for current trading date.\n\n**Important Note**: For Swap/NDS, Daily Gross Trading Algorithm only considers the largest amount of both of the legs. For Block-Trades, net amount the legs are considered for calculation.\n\nFor example; Bank A has set below Risk Portfolio Rule for Counterparty A.\n\n| Counterpart | Portfolio | Dealer       | Legal<br>Entity<br>Group | Fx<br>Time<br>Period | Algorithm           | Limit      |\n|-------------|-----------|--------------|--------------------------|----------------------|---------------------|------------|\n| Any         | Any       | BankA.Trader | Any                      | TODAY –<br>1Y        | Daily Gross Trading | 50,000,000 |\n\n*Under the assumption of current date = 04.10.2019 and all traders are Spot/Outright,* the rule would apply to 5 of the trades in Table 1 (#1,2, 5, 6 and 7) and the algorithm calculates the utilization amount as follows:\n\n1,000,000 USD (from *Trade Id1)* + 2,210,000 USD (from *Trade ID2)* + 2,505,000 USD (from *Trade Id5)* + 3,000,000 USD (from *Trade Id6) +* 5,010,000 USD (from *Trade Id7) = 13,725,000 USD*\n\n**Utilization:** *13,725,000 USD* \n\n#### <span id=\"page-41-0\"></span>**5.2.8 Per Deal Limit**\n\nPer Deal Limit only limits the notional of a deal that can be traded and therefore, there is no utilization calculation is relevant. The amount defined as limit does a limit check for each trade based and if notional of the deal > per deal limit, limit check fails.\n\nFor example, if Bank A defined 100m USD per deal limit for its traders, any trade with notional higher than 100 million USD equivalent will fail.\n\nFor a swap and NDS trades, only one leg (in case of uneven swap, the larger amount) is considered. For example, USDJPY Spot – 1 Week swap with 60 million USD amount passes the check for any per deal limit equal or higher than 60 million.\n\nFor Block Trades, net amount of all legs is considered.\n\n**Important Note**: For Swap/NDS, Daily Gross Trading Algorithm only considers the largest amount of both of the legs. For Block-Trades, net amount the legs are considered for calculation.\n\n### <span id=\"page-41-1\"></span>**5.3 Exceptional Limit**\n\nLimits Monitor provides a functionality which allows users to define different limit values for specific value dates across credit horizon for daily settlement limits. With the help of this functionality clients can flexibly set their daily settlement limits without needing to change the daily limit for each value date.\n\nIn order to define limit specific for certain value date(s), admin users can click on the\n\nicon next to the active rule with Gross Daily Settlement Limit or Net Daily Settlement Limit algorithm.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> | <b>Active Rules</b> | Ξ<br><b>Risk Portfolio PFE</b> |                         |                       |                            |            |                    |                                                                       |\n|------------------------------|-----------------------------|---------------------|--------------------------------|-------------------------|-----------------------|----------------------------|------------|--------------------|-----------------------------------------------------------------------|\n| $\\sim$                       |                             | →                   |                                |                         |                       |                            |            |                    | <b>Refresh All</b>                                                    |\n|                              |                             |                     |                                |                         |                       |                            |            |                    |                                                                       |\n| Rule Id                      | $\\vee$ Counterpart          | Portfolio           | <b>Legal Entity</b>            | <b>Execution Method</b> | <b>Fx Time Period</b> | Algorithm                  | Limit      | <b>Utilization</b> |                                                                       |\n| 360T.MMC Settlement          | 360T.MMC                    | Any                 | Any                            | Any                     | TODAY-3 MONTHS        | Net Daily Settlement Limit | 51.000.000 | 354,060            | $\\frac{1}{2}$ $\\frac{1}{2}$ $\\frac{1}{2}$ $\\frac{1}{2}$ $\\frac{1}{2}$ |\n\n<span id=\"page-41-2\"></span>Figure 32 Exceptional Limit\n\nClicking on icon opens a pop-up window. User can click on icon to add the respective date which will open a calendar. Selecting the respective date will add an entry in table view where the corresponding limit value can be defined. Clicking on `Apply` will save the changes.\n\nPlease note that the changes are persistent to rollover, meaning that the defined limit will be persistent as long as the rule itself is active and/or the date of the exceptional limit is not in the past.\n\n|             | 360T.MMC Settlement                                                                           |\n|-------------|-----------------------------------------------------------------------------------------------|\n| Date        | Limit                                                                                         |\n| 24-Dec-2021 | û<br>0)                                                                                       |\n|             |                                                                                               |\n|             | $2021$ $\\vee$<br>Dec<br>$\\langle$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\\vee$       |\n|             | Mo Tu We Th<br>Fr.<br>Su<br>Sa                                                                |\n|             | 30<br>$\\overline{\\mathbf{3}}$<br>28<br>29<br>$\\mathbf{1}$<br>$\\overline{2}$<br>$\\overline{4}$ |\n|             | 5<br>6<br>8<br>10<br>$\\overline{7}$<br>9<br>11                                                |\n|             | 12<br>13<br>14 15<br>16<br>17<br>18                                                           |\n|             | 21 22<br>23<br>25<br>19<br>20<br>$-24$                                                        |\n|             | 28 29<br>31<br>26<br>27<br>30<br>$\\overline{1}$                                               |\n|             | $\\overline{2}$<br>$3 -$<br>$4 -$<br>5<br>$-6$<br>$\\overline{7}$<br>8                          |\n|             |                                                                                               |\n|             |                                                                                               |\n|             |                                                                                               |\n|             |                                                                                               |\n\n<span id=\"page-42-1\"></span>Figure 33 Adding an exceptional limit entry\n\nThis will particularly address two specific requirements:\n\n- 1) To prevent settlement on a certain value date due to bank holidays by setting an exceptional limit of 0 for that particular date.\n- 2) To carve out the limit for specific value date due to an exposure on that date which is not reflected in 360T`s Limits Monitor.\n\nWhen there is an exceptional limit for a particular rule, limit check for the matching trade intention is conducted against the defined exceptional limit value.\n\n*For example*, user has defined 10 million USD gross daily settlement limit against its counterpart. Same user now defines 5 million USD limit for 25th January, 2022. Limit check will fail for any deal above 5 million USD equivalent for value date of 25th January value between that particular counterpart and the client itself.\n\nIn order to highlight a rule which has an exceptional limit, icon will be highlighted with blue colour.\n\n| Limit      | <b>Utilization</b> |               |\n|------------|--------------------|---------------|\n| 51,000,000 | 354,060            | $\\Rightarrow$ |\n\n<span id=\"page-42-2\"></span>Figure 34 Highlighted rule with an exceptional limit\n\n### <span id=\"page-42-0\"></span>**5.4 Utilization Reset**\n\n360T`s Limits Monitor allows authorized users to reset the limit usage calculation for Daily Net and Gross Trading algorithms. Once a user is enabled for the feature, he/she will be able to click on and restart the calculation rather than waiting for day roll.\n\n| Algorithm                 | Limit       | <b>Utilization</b> |  |\n|---------------------------|-------------|--------------------|--|\n| Daily Gross Trading Limit | 100,000,000 | o                  |  |\n\n<span id=\"page-42-3\"></span>Figure 35 Reset Utilization\n\nOnce the functionality used, all admin users will receive an email which informs them about the action.\n\nTo be enabled for the functionality, please contact 360T CAS.\n\n### <span id=\"page-43-0\"></span>**5.5 Visualization**\n\n360T`s Limits Monitor feature provides various exposure calculation methods which include several parameters and steps to calculate the risk. The different dimension of the exposure requires further transparency to be provided. `Visualization` functionality addresses this requirement and provides visual aid to the admin users so that they can have a better overview on their companies` risk exposures.\n\nBased on the type of the limit algorithm assigned to an active risk portfolio rule, `visualization` feature shows different types of tables or graphs which includes breakdowns of the exposure per date, currency and/or cashflows.\n\n| Algorithm                     | Daily Limit<br>Usage Table | Daily Limit<br>Usage Graph | Limit Used by<br>Currency | Risk<br>Entries |\n|-------------------------------|----------------------------|----------------------------|---------------------------|-----------------|\n| Net Daily<br>Settlement       | +                          | +                          | +                         | +               |\n| Gross Daily<br>Settlement     | +                          | +                          |                           | +               |\n| Aggregate Net<br>Settlement   |                            |                            | +                         | +               |\n| Aggregate Gross<br>Settlement |                            |                            |                           | +               |\n| Daily Net Trading             |                            |                            | +                         | +               |\n| Daily Gross<br>Trading        |                            |                            |                           | +               |\n| Per Deal                      |                            |                            |                           |                 |\n| PFE                           |                            |                            |                           | +               |\n\nIn order to access to the feature, Risk Portfolio admins can click on `visualization` icon next to the relevant active risk portfolio rule. Clicking on this icon will pop-up a page with different tabs which show different components of the utilization. The details shown will differ for different algorithms due to different nature of the calculation methods.\n\n#### <span id=\"page-44-0\"></span>**5.5.1 Daily Limit Usage Table**\n\nThe Daily Limit Usage Table displays the breakdown of the daily usage of the limit of a risk portfolio. By showing the limit and utilization separately for each settlement date that the limit is utilized, table gives transparency to admin users to monitor the available limit left for each value date. By default, screen displays the current calendar month. In order to navigate across the credit horizon, users can click on arrows and see the usage details of other calendar months.\n\n| <b>Risk Portfolio Groups</b>     | <b>Risk Portfolio Rules</b> | <b>Active Rules</b> | <b>Risk Portfolio PFE</b>      |                                |            |                                |                                     |             |                    | $\\mathcal{A} \\quad \\mathcal{A} \\quad \\mathbb{R}$ |\n|----------------------------------|-----------------------------|---------------------|--------------------------------|--------------------------------|------------|--------------------------------|-------------------------------------|-------------|--------------------|--------------------------------------------------|\n|                                  |                             |                     |                                |                                |            |                                |                                     |             |                    |                                                  |\n| Provide text for filtering rules |                             |                     |                                |                                |            |                                |                                     |             |                    |                                                  |\n| Counterpart                      | Portfolio                   |                     | <b>Legal Entity Group</b>      | <b>Fx Time Period</b>          |            | Algorithm                      |                                     | Limit       | <b>Utilization</b> |                                                  |\n| Any                              | Spot                        | Any                 |                                | TodTomSpot                     |            | Net Daily Settlement Limit     |                                     | 10,000,000  | 243,337            | $\\nu \\nightharpoonup \\nightharpoonup$            |\n| 360TBANK.TEST                    | Any vs G10                  | Any                 |                                | TOMORROW - 1 MONTH             |            | Potential Future Exposure      |                                     | 50,000,000  | $\\bullet$          | $\\nu$ and $\\nu$                                  |\n| COBA.DEMO                        | Any                         | Any                 |                                | TODAY - 1 MONTH                |            | Potential Future Exposure      |                                     | 1,000,000   | 355,000            | $\\mathbb{Z}$ and $\\mathbb{Z}$                    |\n| Any                              | Any                         | Any                 |                                | Any                            |            | <b>Daily Net Trading Limit</b> |                                     | 100,000,000 | 1,232,621          | ジ面め                                              |\n| Any                              | Any                         | Any                 |                                | Any                            |            |                                | Aggregate Net Settlement Limit      | 100,000,000 | 1,232,621          | $\\nu$ $\\omega$                                   |\n| PEBANK APAC.TEST                 | Any vs G10                  | Any                 |                                | Any                            |            |                                | <b>Gross Daily Settlement Limit</b> | 5,000,000   | 385,706            | $\\nu$ $\\omega$                                   |\n| RBS.LND.DEMO                     | Any ye $610$                | Any                 |                                | Any                            |            |                                | Aggregate Groce Settlement Li       | 10.000.000  | $\\Omega$           | $\\mathbb{Z}$ and $\\mathbb{Z}$                    |\n|                                  |                             |                     |                                |                                |            |                                |                                     |             | $\\times$           |                                                  |\n|                                  |                             |                     | <b>Daily Limit Usage Table</b> | <b>Daily Limit Usage Graph</b> |            | <b>Risk Entries</b>            | 土                                   |             |                    |                                                  |\n|                                  |                             |                     | $\\leftarrow$                   | October 2020/November 2020     |            | $\\rightarrow$                  |                                     |             |                    |                                                  |\n|                                  |                             |                     |                                |                                |            |                                |                                     |             |                    |                                                  |\n|                                  | Type                        |                     | 20.10.2020                     |                                | 27.10.2020 |                                |                                     | 30.10.2020  |                    |                                                  |\n|                                  | <b>Daily Exposure</b>       |                     |                                | 385,706.00                     |            |                                | 240,000.00                          |             | 231,272.00         |                                                  |\n|                                  | Limit                       |                     |                                | 5,000,000.00                   |            |                                | 5,000,000.00                        |             | 5,000,000.00       |                                                  |\n|                                  |                             |                     |                                |                                |            |                                |                                     |             |                    |                                                  |\n|                                  |                             |                     |                                |                                |            |                                |                                     |             |                    |                                                  |\n\n<span id=\"page-44-2\"></span>Figure 36 Daily Limit Usage Table\n\nTable is available for the daily limit algorithms which considers the effective dates of the cashflows in risk calculation.\n\n#### <span id=\"page-44-1\"></span>**5.5.2 Daily Limit Usage Graph**\n\nThe Daily Limit Usage Graph visualizes the breakdown of the daily usage of the limit of a risk portfolio. Graph helps users to have a quick overview on the limit usage across the time period for a specific risk portfolio.\n\n![](_page_44_Figure_9.jpeg)\n\n<span id=\"page-44-3\"></span>Figure 37 Daily Limit Usage Graph\n\n#### <span id=\"page-45-0\"></span>**5.5.3 Limit Used By Currency**\n\n`Limit Used By Currency` tab displays the net position per currency accounts for a specific risk portfolio. For net algorithms, currency positions are displayed as a table which can also be exported as csv file.\n\n| Currency |            | Exposure |                 |\n|----------|------------|----------|-----------------|\n|          | <b>EUR</b> |          | 1,950,000.00    |\n|          | <b>TRY</b> |          | $-1,980,561.00$ |\n|          | <b>USD</b> |          | 50,000.00       |\n\n<span id=\"page-45-2\"></span>Figure 38 Limit Used By Currency (Aggregate)\n\nFor Net Daily Settlement Algorithms, users can see the daily cashflow ledger as a table.\n\n| Daily Limit Usage Table | Daily Limit Usage Graph |                             | <b>Limit Used By Currency</b>            | <b>Risk Entries</b> | 也 | $\\times$ |\n|-------------------------|-------------------------|-----------------------------|------------------------------------------|---------------------|---|----------|\n|                         | $\\leftarrow$            |                             | October 2020/November 2020 $\\rightarrow$ |                     |   |          |\n|                         |                         |                             |                                          |                     |   |          |\n|                         | <b>EUR</b>              | Currency 16.10.2020<br>0.00 | 19.10.2020<br>58,100.00                  |                     |   |          |\n|                         | <b>TRY</b>              | 243,337.00                  | $-74,287.00$                             |                     |   |          |\n|                         | <b>USD</b>              | $-206,295.00$               | 0.00                                     |                     |   |          |\n|                         |                         |                             |                                          |                     |   |          |\n|                         |                         |                             |                                          |                     |   |          |\n|                         |                         |                             |                                          |                     |   |          |\n\n<span id=\"page-45-3\"></span>Figure 39 Limit Used By Currency (Table)\n\n#### <span id=\"page-45-1\"></span>**5.5.4 Risk Entries**\n\nRisk Entries tab provides the details of the cashflow (trade legs) matches with a specific risk portfolio rule. Information provided in this tab helps admin users to verify the utilization calculation as it includes necessary information.\n\n|  |  |  | Table can be exported as csv by clicking on | icon. |\n|--|--|--|---------------------------------------------|-------|\n|  |  |  |                                             |       |\n\n|                 |                   |                   |                  |             | <b>Pending Order Amount</b> | <b>Risk Entries</b> | <b>Utilization Updates</b> | 也               |             |                     |                    |                 | $\\times$               |\n|-----------------|-------------------|-------------------|------------------|-------------|-----------------------------|---------------------|----------------------------|-----------------|-------------|---------------------|--------------------|-----------------|------------------------|\n| <b>Event Id</b> | <b>Value Date</b> | Trade Date $\\vee$ | App. Specific Id | Product     | <b>Execution Method</b>     | <b>Legal Entity</b> | Counterpart                | <b>Base CCY</b> | Quote CCY   | <b>Notional CCY</b> | <b>Receive CCY</b> | Pay CCY         | <b>Notional Amount</b> |\n| 684361          | 31.03.2023        | 29.03.2023        | 17213062         | <b>SPOT</b> | <b>SEP</b>                  | 360T, MMC           | COBA.DEMO                  | <b>EUR</b>      | <b>THB</b>  | <b>EUR</b>          | <b>EUR</b>         | <b>THB</b>      | 1,000,000.00           |\n| 684359          | 31.03.2023        | 29.03.2023        | 17213060         | <b>SPOT</b> | <b>SEP</b>                  | 360T, MMC           | COBA.DEMO                  | <b>EUR</b>      | <b>TRY</b>  | <b>EUR</b>          | <b>EUR</b>         | <b>TRY</b>      | 1,000,000.00           |\n| 684357          | 31.03.2023        | 29.03.2023        | 17213058         | <b>SPOT</b> | <b>SEP</b>                  | 360T, MMC           | COBA.DEMO                  | <b>EUR</b>      | <b>TRY</b>  | <b>EUR</b>          | <b>EUR</b>         | <b>TRY</b>      | 1,000,000.00           |\n| 684355          | 31.03.2023        | 29.03.2023        | 17213056         | <b>SPOT</b> | <b>SEP</b>                  | 360T.MMC            | <b>BOAL.DEMO</b>           | <b>EUR</b>      | <b>TRY</b>  | <b>EUR</b>          | <b>EUR</b>         | <b>TRY</b>      | 1,000,000.00           |\n| 557682          | 31.03.2023        | 29.03.2023        | 182420519        | <b>SPOT</b> | <b>OMT</b>                  | 360T.MMC            | 360T.RMS                   | <b>EUR</b>      | <b>TRY</b>  | <b>EUR</b>          | <b>TRY</b>         | <b>EUR</b>      | 123,124.00             |\n| 513412          | 31.03.2023        | 29.03.2023        | 174429276        | <b>SPOT</b> | <b>OMT</b>                  | 360T.MMC            | <b>360T.RMS</b>            | <b>EUR</b>      | <b>USD</b>  | <b>EUR</b>          | <b>USD</b>         | <b>EUR</b>      | 123,123.00             |\n| 513410          | 31.03.2023        | 29.03.2023        | 174429274        | <b>SPOT</b> | <b>OMT</b>                  | 360T.MMC            | 360T.RMS                   | <b>EUR</b>      | <b>USD</b>  | <b>EUR</b>          | <b>USD</b>         | <b>EUR</b>      | 13,000,000.00          |\n| 557682          | 30.03.2023        | 28.03.2023        | 182420519        | <b>SPOT</b> | <b>OMT</b>                  | 360T.MMC            | <b>360T.RMS</b>            | <b>EUR</b>      | <b>TRY</b>  | <b>EUR</b>          | <b>TRY</b>         | <b>EUR</b>      | 123,124.00             |\n| 513412          | 30.03.2023        | 28.03.2023        | 174429276        | <b>SPOT</b> | <b>OMT</b>                  | 360T.MMC            | 360T.RMS                   | <b>EUR</b>      | <b>USD</b>  | <b>EUR</b>          | <b>USD</b>         | <b>EUR</b>      | 123,123.00             |\n| 513410          | 30.03.2023        | 28.03.2023        | 174429274        | <b>SPOT</b> | OMT                         | 360T.MMC            | 360T.RMS                   | <b>EUR</b>      | <b>USD</b>  | <b>EUR</b>          | <b>USD</b>         | <b>EUR</b>      | 13,000,000.00          |\n| 241869          | 29.03.2023        | 28.03.2023        | 17212581         | <b>SPOT</b> | <b>GTX CLOB</b>             | 360T.MMC            | COBA.DEMO                  | <b>USD</b>      | <b>TRY</b>  | <b>USD</b>          | <b>TRY</b>         | <b>USD</b>      | 108.00                 |\n| 684016          | 30.03.2023        | 28.03.2023        | 17211196         | <b>SPOT</b> | <b>SEP</b>                  | 360T.MMC            | 360T.RMS                   | <b>EUR</b>      | <b>TRY</b>  | <b>EUR</b>          | <b>EUR</b>         | <b>TRY</b>      | 100.00                 |\n| 241867          | 29.03.2023        | 28.03.2023        | 17212579         | <b>SPOT</b> | <b>GTX CLOB</b>             | 360T.MMC            | BOAL.DEMO                  | <b>USD</b>      | <b>TRY</b>  | <b>USD</b>          | <b>TRY</b>         | <b>USD</b>      | 108.00                 |\n| 684014          | 30.03.2023        | 28.03.2023        | 17211194         | <b>SPOT</b> | <b>SEP</b>                  | 360T.MMC            | 360T.RMS                   | <b>EUR</b>      | <b>TRY</b>  | <b>EUR</b>          | <b>EUR</b>         | <b>TRY</b>      | 100.00                 |\n| <b>DARDER</b>   | 30.00.0000        | DO OD DODD        | 1777777777       | <b>COOT</b> | CONTRACTOR COMPLETE         | <b>DERTSHIP</b>     | CODA DELIGI                | <b>ALCOHOL:</b> | <b>TEMP</b> | <b>ALLOW</b>        | <b>TERMS</b>       | <b>ALCOHOL:</b> | 400.00                 |\n\n<span id=\"page-45-4\"></span>Figure 40 Risk Entries\n\nBelow you can see the description of the information provided in the risk entries table:\n\n- *i. Event ID:* Displays the unique sequential ID of the risk entry. Table can be sorted by Event ID. However, please note that sequence might have different start point per execution method but in general for same trade date transactions, it can guide user what events came first.\n- *ii. Value Date:* Displays the effective/settlement date of the cashflow. Table can be ranked by *Value Date* parameter.\n- *iii. Trade Date:* Displays the date that trade which causes the cashflow is executed. It is possible to sort the table by Trade Date.\n- *iv. App. Specific ID:* Displays the unique identifier for the reference transaction. For RFS, this ID is the reference ID of the trade which can be easily found in Deal Tracking as well. For SEP, the ID is the numerical value of SuperSonic orders for ex. 12345 refers to SO-12345 order ID which can also be found within deal tracking and SST applications. For OMT orders, the ID displayed in UI cannot be directly referenced to an Order by clients but would help CAS team for any investigation.\n- *v. Product:* Indicates the product type. Please note that, it shows the type of the product that is originally executed i.e. Spot will be displayed even though value date doesn`t indicate a spot date for that specific currency pair any longer.\n- *vi. Legal Entity:* Shows the name of the legal entity for which the credit entity made the transaction for.\n- *vii. Counterpart:* Displays 360T system name of the counterparty against which the credit entity dealt.\n- *viii. Base CCY:* Displays the base currency for a specific trade/trade intention.\n- *ix. Quote CCY:* Displays the quote currency for a specific trade/trade intention.\n- *x. Notional CCY:* Displays the currency which specify the amount to be exchanged via the corresponding trade/trade intention.\n- *xi. Receive CCY:* Indicates the currency which credit entity will receive at value date.\n- *xii. Pay CCY:* Indicates the currency which credit entity agreed to pay at value date.\n- *xiii. Notional Amount:* Shows the notional amount of transaction.\n- *xiv. Trade Rate:* Shows the agreed execution rate or limit/stop rate. This information is used to calculate opposite amount of cashflow for net algorithms.\n- **xv.** *Revert:* Indicates whether the created risk entry is reverted (True) due to nonexecution or a final entry (false). In case of an order which is not fulfilled, Limits Monitor would allocate the exposed amount at the moment of placing or accepting order. This creates a risk entry in the system. However, when this order is withdrawn for ex., negation of the record would also be created by creating the same entry with `Revert = True`.\n\nPlease note that, in GUI, maximum 200 entries are displayed. Full list of entries can be accessed by exporting entries as csv file into your PC.\n\n#### <span id=\"page-47-0\"></span>**5.5.5 Pending Order Amount**\n\nLimits Monitor reserves limit usage for private book orders such as limit, stop, algo, fixing orders etc. immediately when order is placed. The unfilled amount is then reverted when order is cancelled or partially filled. When order is filled, nothing changes (except that entry includes trade rate now) in terms of utilization as system already accounted for that.\n\nClients who are using API connection to update their exposure in Limits Monitor and who has no Order API connection to 360T are not automatically informed when an order is still pending. To inform these customers about the exposure saved in 360T due to such active orders in the system and also to compensate for the exposure update they sent through the API, Limits Monitor is now able to track these pending exposures for Gross algorithms.\n\n|      | <b>Pending Order Amount</b><br><b>Risk Entries</b> | <b>Utilization Updates</b> | 也                |\n|------|----------------------------------------------------|----------------------------|------------------|\n| Type |                                                    |                            |                  |\n|      | <b>Total Exposure</b>                              |                            | 116,013,428.00   |\n|      | <b>Pending Order Amount</b>                        |                            | 13,246,247.00    |\n|      | Limit                                              |                            | 1,000,000,000.00 |\n\n<span id=\"page-47-2\"></span>Figure 41 Pending Order Amount\n\nThe field is displayed as an additional tab for Aggregate algorithms in Visualization panel. For Gross Daily Settlement limit algorithm, pending exposures are displayed in daily limit usage table as a separate row.\n\nPlease note that, the field is not by default available. Please contact CAS for further information and activation.\n\n#### <span id=\"page-47-1\"></span>**5.5.6 Utilization Updates**\n\nClients who have an API connection to the Limits Monitor are able to send their own risk exposures either by overwriting what 360T calculates or by sending the incremental changes.\n\nIn order to provide more transparency on the final utilization calculation, Limits Monitor displays the risk exposure changes arisen due to the updates done through API.\n\n| Value Date |            | Currency | Exposure   |                 | isDelta |  |\n|------------|------------|----------|------------|-----------------|---------|--|\n|            | 02.11.2023 |          | EUR        | 8,000,000.00    | false   |  |\n|            | 02.11.2023 |          | <b>USD</b> | $-8,000,000.00$ | false   |  |\n|            | 03.11.2023 |          | <b>EUR</b> | 2,000,000.00    | false   |  |\n|            | 03.11.2023 |          | <b>USD</b> | $-3,000,000.00$ | false   |  |\n\n<span id=\"page-47-3\"></span>Figure 42 Utilization changes arisen due API updates\n\nTable has 4 columns: Value Date, Currency, Exposure and isDelta.\n\n*Value Date* is sent by clients via API for DSL algorithms. For any other algorithm, Limits Monitor is assigning the latest value date where there is a risk exposure.\n\n*Currency* is sent by client via API for net algorithms since Limits Monitor expects to receive currency exposure to be able to calculate available limit per each currency separately. For any other algorithm type, this field is irrelevant.\n\n*Exposure* displayed in this table is the amount of change in the exposure for that respective value date and/or currency when API sends a new update. In case API sends an overwrite, Limits Monitor is calculating the incremental change and saving this as a delta exposure. The amount here is in terms of company currency.\n\n*isDelta* is to specify that the exposure written in table is the amount of change in exposure.\n\nPlease also note that, any change in utilization following an API call is also tracked in Audit Logs.\n\n### <span id=\"page-48-0\"></span>**6 ALERT EMAILS**\n\n360T`s Limits Monitor can notify the users who has access to the tool when the utilization of a limit for a certain rule has reached to a certain threshold value.\n\nThe automatically triggered email contains all the parameters of an active rule and aims to help client admins to notice the level of limit usage so that they can closely monitor the limits for risk portfolio rule and when necessary, make an action accordingly.\n\n![](_page_48_Figure_9.jpeg)\n\n<span id=\"page-48-2\"></span>Figure 43 Alert emails\n\nBy default, there are three levels of thresholds for 75, 85 and 95%, although it is possible to set different threshold values. In case you would like to start receiving the alert emails, please contact Client Advisory Services team at [<EMAIL>.](mailto:<EMAIL>)\n\n### <span id=\"page-48-1\"></span>**7 SNAPSHOT REPORTS**\n\n360T`s Limits Monitor can provide the snapshot of the Active Rules as a file in .csv format via the email. The reports are generated twice a day -end of day and beginning of day- and sent only to the admin users who are activated for the reports.\n\nEnd of day reports are generated slightly before day rollover at NY 5 PM and displays the active risk portfolio rules as well as the corresponding limit and utilization values. Beginning of day reports are generated slightly after day rollover is completed after NY 5 PM.\n\nThe reports help clients to\n\n- 1) Monitor if any existing active rule has not available limit mainly as a result of changes due to day rollover.\n- 2) Monitor the limit usage across time by comparing snapshots of different dates.\n\nPlease contact our Client Advisory Services team [\\(<EMAIL>\\)](mailto:<EMAIL>) to start receiving the reports.\n\n### <span id=\"page-50-0\"></span>**8 RISK PORTFOLIO PFE**\n\n### <span id=\"page-50-1\"></span>**8.1 Configuring PFE factors**\n\nThe 'Risk Portfolio PFE' tab is the configuration panel in which client admins can define PFE table(s) where risk weighing factors for each currency pair and tenor (days left to the settlement date) can be entered. The data entered in these table(s) are input to calculate the Potential Future Exposure of the respective risk portfolio.\n\n**IMPORTANT:** Please note that any change to the Risk Portfolio PFE table will be active after the first end of day rollover, executed every day at NY 5 PM. This includes the amendment of an existing value or an addition or deletion.\n\nBy default, there is only one PFE table available which can then be assigned to a risk portfolio rule by selecting the algorithm of the rule as Potential Future Exposure. Upon request, It is also possible to create multiple tables and define different risk weighing factors.\n\n| <b>Risk Portfolio Groups</b><br>$\\mathsf{CSA} \\times \\mathsf{DEFAULT}$ + |                                          |        |        | Risk Portfolio Rules Active Rules Risk Portfolio PFE |\n|--------------------------------------------------------------------------|------------------------------------------|--------|--------|------------------------------------------------------|\n| $\\overline{\\phantom{000000000000000000000000000000000000$                |                                          |        |        |                                                      |\n| Currency Couple                                                          | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Day 60 | Day 90 | <b>DEFAULT</b>                                       |\n| EURAUD                                                                   | (0.12)                                   | (0.23) | (0.25) | $\\left( 0.5 \\right)$                                 |\n| <b>EURUSD</b>                                                            | 0.14                                     | (0.21) | (0.34) | (0.6)                                                |\n| <b>USDCHF</b>                                                            | 0.19                                     | (0.29) | (0.41) | (0.65)                                               |\n| DEF                                                                      | 0.3                                      | (0.6)  | 0.9    |                                                      |\n\n<span id=\"page-50-2\"></span>Figure 44 Adding multiple PFE tables\n\n**IMPORTANT:** Please contact Client Advisory Services (CAS) team for activation of multiple PFE tables\n\nOnce enabled, admin users can create new tables by clicking on icon next to Default tab. These factors then can be assigned to the respective Risk Portfolio rule by selecting the PFE algorithm associated with the table name.\n\n|                                | PEBANK PFE                   | PEBAN         |                                           | TODAY-1 MONTH V PFE - DEFAULT |                                                                    | <b>7</b> 6.000,000       |  |\n|--------------------------------|------------------------------|---------------|-------------------------------------------|-------------------------------|--------------------------------------------------------------------|--------------------------|--|\n| び●                             | Mock Test                    | <b>MOCKT</b>  | Please Select:                            | Any                           | Cross Daily Settlement Li_ % (15,000,000                           |                          |  |\n| ♡●                             | Mock NOP                     | MOCK.         | Q<br>$\\rightarrow$                        | Any                           | Aggregate Net Settlement _ % 3,000,000                             |                          |  |\n| $\\overline{\\mathsf{v}\\bullet}$ | Dealer-FatFinger RiskManager | Any           |                                           | Any                           | <b>EX</b> Per Deal Limit                                           | V (100,000,000)          |  |\n| է∕●                            | <b>COBA Settlement</b>       | <b>COBA D</b> | Available (9)                             |                               | TODAY-3 MONTHS V Net Daily Settlement Limit V 2,000,000            |                          |  |\n| $\\sim$ 0                       | COBA PFE                     | COBAD         | Net Daily Settlement Limit                | TODAY-1 MONTH M PFE - DEFAULT |                                                                    | $V$ (23.500.000          |  |\n| $\\circ$                        | <b>360TBANK Settlement</b>   | 360TB         | Gross Daily Settlement Limit              |                               | TODAY-TODAY I/ Net Daily Settlement Limit I/ 73,000,000            |                          |  |\n| $\\sim$ $\\bullet$               | <b>360TBANK PFE</b>          | 360TB/        | Aggregate Gross Settlement Limit          | TODAY-1 MONTH E PFE - DEFAULT |                                                                    | $7/$ 67.500.000          |  |\n| $\\sim$                         | 360T.MMC Settlement          | 360T.N        | Aggregate Net Settlement Limit            |                               | TODAY-3 MONTHS / Gross Daily Settlement Li_ / 500,000,000,000      |                          |  |\n| ☞                              | 360T.MMC NOP S/L             | 360T.N        | Daily Net Trading Limit<br>Per Deal Limit |                               | TODAY-1 MONTH E Aggregate Net Settlement _ E 50,000,000            |                          |  |\n| r o                            | 360T Group Risk              | 360T G        | Daily Gross Trading Limit                 |                               | TODAY-3 MONTHS E Aggregate Gross Settleme. E (5,000,000,000        |                          |  |\n| $\\sim$                         | 300802                       | Any           | PFE - DEFAULT                             | Any                           | $\\mathbb{Z}$ Daily Gross Trading Limit $\\mathbb{Z}$ (1,000,000,000 |                          |  |\n| $\\sim$                         | 284132                       | <b>COBA D</b> | PFE - CSA                                 | Any                           | Gross Daily Settlement Li V (1,000,000                             |                          |  |\n| $\\sim$                         | 261286                       | Clients       |                                           | Any                           | Aggregate Net Settlement _ = / 2,500,000,000                       |                          |  |\n| $\\vee$                         | 242638                       | <b>360TB</b>  |                                           | Anv                           | $5$ PFE - DEFAULT                                                  | $\\frac{3}{2}$ (1,000,000 |  |\n| ឃ●                             | 029200208G8P7WI6I763-NG      | <b>RBSLM</b>  |                                           |                               | TODAY-3 MONTHS / Net Daily Settlement Limit / 24,000,000           |                          |  |\n|                                |                              |               |                                           |                               |                                                                    |                          |  |\n\n<span id=\"page-50-3\"></span>Figure 45 Assigning PFE table to a risk portfolio\n\nThis allows clients to calculate risk exposure differently based on who the counterpart is (for ex. A counterpart who has Credit Support Annex vs who doesn`t have etc), what the tenor is etc.\n\nThe PFE table(s) has a default value of 1 for every currency pair. This means that the Potential Future Exposure algorithm multiplies the notional exposure for each currency pair for any value date by 1 until changed by the user administrator.\n\n| <b>Currency Couple</b> | Day 3 | Day 5   | <b>Day 12</b> | <b>Day 24</b> | <b>Day 48</b> | <b>Day 92</b> | <b>Day 96</b> | <b>Day 256</b> | <b>Day 300</b> | <b>Day 730</b> | <b>DEFAULT</b> |  |\n|------------------------|-------|---------|---------------|---------------|---------------|---------------|---------------|----------------|----------------|----------------|----------------|--|\n| <b>EURCHF</b>          | 0.1   | 0.35    | 0.21          | 0.22          | 0.26          |               | 0.35          | 0.6            | $\\sqrt{0.8}$   | 1.1            | 1.5            |  |\n| <b>EURUSD</b>          | 0.12  | 0.52545 | 0.21          | 0.25          | 0.33          |               | 0.5           | 0.7            | 0.9            | $1.2$          | 1.7            |  |\n| GBPAUD                 | 0.5   |         | 1.5           |               | 2.5           |               |               | 3.5            | Δ              | 4.5            |                |  |\n| <b>GBPCHF</b>          | 0.1   | 0.15    | 0.2           | 0.25          | 0.3           |               | 0.35          | 0.5            | 0.8            | 1.1            | 1.2            |  |\n| <b>DEF</b>             |       |         |               |               |               |               |               |                |                | 1.5            | 2.5            |  |\n\n<span id=\"page-51-1\"></span>Figure 46 Risk Portfolio PFE\n\nNew currency couple and new tenors can be added via `Add Currency Couple` and `Add Tenor` buttons.\n\n|               | Day 3          | Day 5   | <b>Day 12</b> | <b>Day 24</b> | <b>Day 48</b>                    | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | <b>Day 96</b> | Day 256 | <b>Day 300</b> | Day 730 | <b>DEFAULT</b> |                            |\n|---------------|----------------|---------|---------------|---------------|----------------------------------|------------------------------------------|---------------|---------|----------------|---------|----------------|----------------------------|\n| <b>EURCHF</b> | 0.1            | 0.35    | 0.21          | 0.22          | 0.26                             | $\\overline{1}$                           | 0.35          | 0.6     | 0.8            | (1.1)   | 1.5            |                            |\n| <b>EURUSD</b> | 0.12           | 0.52545 |               |               |                                  |                                          | $\\times$      | 0.7     | (0.9)          | 1.2     | 1.7            |                            |\n| <b>GBPAUD</b> | 0.5            | 1       |               |               | Please provide a currency couple |                                          |               | 3.5     | $\\mathbf{A}$   | 4.5     | 5 <sup>5</sup> |                            |\n| <b>GBPCHF</b> | $\\boxed{0.1}$  | 0.15    |               |               |                                  |                                          |               | 0.5     | 0.8            | 1.1     | 1.2            |                            |\n|               | $\\overline{1}$ |         | EUR           |               | GBP<br>$\\checkmark$              |                                          | $\\checkmark$  |         | $\\mathbf{1}$   | 1.5     | 2.5            | 画                          |\n|               |                |         |               |               |                                  |                                          |               |         |                |         | Add Tenor      | <b>Add Currency Couple</b> |\n\n<span id=\"page-51-2\"></span>Figure 47 Adding Currency Couple to Risk Portfolio PFE Matrix\n\n|                            | 1.5                | 1.1 | 0.8                  | 0.6 | 0.35     | 0.26<br>$\\overline{1}$     | 0.21<br>0.22 | 0.35         | $\\left( 0.1 \\right)$ | <b>EURCHF</b> |\n|----------------------------|--------------------|-----|----------------------|-----|----------|----------------------------|--------------|--------------|----------------------|---------------|\n|                            | 1.7                | 1.2 | $\\left( 0.9 \\right)$ | 0.7 |          |                            |              | 0.52545      | 0.12                 | <b>EURUSD</b> |\n|                            | $5\\overline{)}$    | 4.5 | $\\overline{4}$       | 3.5 | $\\times$ | Please provide a new tenor |              | 1            | 0.5                  | GBPAUD        |\n|                            | 1.2                | 1.1 | 0.8                  | 0.5 |          |                            |              | 0.15         | $\\left( 0.1 \\right)$ | <b>GBPCHF</b> |\n|                            | $\\left(2.5\\right)$ | 1.5 | $\\overline{1}$       |     |          |                            | 180          | $\\mathbf{1}$ | $\\overline{1}$       | DEF           |\n| <b>Add Currency Couple</b> | <b>Add Tenor</b>   |     |                      |     |          |                            |              |              |                      |               |\n\n<span id=\"page-51-3\"></span>Figure 48 Add Tenor to Risk Portfolio PFE Matrix\n\nNew parameters are added by default with PFE factor 1. The factors can be amended by single-click on to the respective values.\n\n| <b>Risk Portfolio Groups</b> | <b>Risk Portfolio Rules</b> | <b>Active Rules</b> | <b>Risk Portfolio PFE</b> | Ξ             |               |               |               |                |                |                |                |  |\n|------------------------------|-----------------------------|---------------------|---------------------------|---------------|---------------|---------------|---------------|----------------|----------------|----------------|----------------|--|\n| <b>Currency Couple</b>       | Day 3                       | Day 5               | <b>Day 12</b>             | <b>Day 24</b> | <b>Day 48</b> | <b>Day 92</b> | <b>Day 96</b> | <b>Day 256</b> | <b>Day 300</b> | <b>Day 730</b> | <b>DEFAULT</b> |  |\n|                              |                             |                     |                           |               |               |               |               |                |                |                |                |  |\n\n<span id=\"page-51-4\"></span>Figure 49 Amending PFE value\n\n### <span id=\"page-51-0\"></span>**8.2 Bulk Upload/Download via CSV**\n\nA csv upload/download functionality is available to allow bulk upload of PFE factors to update of any existing PFE factor.\n\n| <b>Risk Portfolio Groups</b>   | <b>Risk Portfolio Rules</b> |                | <b>Active Rules</b>              | <b>Risk Portfolio PFE</b> | $\\equiv$                   | 土土                       |\n|--------------------------------|-----------------------------|----------------|----------------------------------|---------------------------|----------------------------|--------------------------|\n| $> 0M \\times$<br>$>10Y \\times$ | $>1M \\times$                | $>1Y$ $\\times$ | $>2Y$ $\\times$<br>$>5Y$ $\\times$ | <b>DEFAULT</b>            | Inf $\\times$<br>$+$        | $\\overline{\\phantom{a}}$ |\n|                                |                             |                |                                  |                           |                            |                          |\n| <b>Currency Couple</b>         | <b>Day 32</b>               | Day 367        | Day 732                          | Day 1827                  | Day 3652<br><b>DEFAULT</b> |                          |\n| <b>EURCHF</b>                  | 0.02                        | 0.08           | 0.16                             | 0.32                      | 0.64                       |                          |\n| <b>EURCZK</b>                  | 0.02                        | 0.08           | 0.16                             | 0.32                      | 0.64                       |                          |\n\n<span id=\"page-52-0\"></span>Figure 50 Risk Portfolio PFE Upload/Download\n\nBy clicking on icon on top right of the *Risk Portfolio PFE* view, users can download a snapshot of their current PFE factors as csv file. The downloaded file can be used as the base to make the necessary changes before uploading back the updated csv\n\nfile by clicking on icon and then selecting the file.\n\nOnce the file is uploaded, a result file is created to display the status of the changes.\n\n<span id=\"page-52-1\"></span>Figure 51 PFE CSV upload results file sample\n\nPlease note that this functionality does not currently support the addition or deletion of PFE factors.\n\nFor a successful upload, the csv should have the below format:\n\n- Row1 includes the headers:\n  - o Column 1: 'CurrencyCouple'\n  - o Column 2 and followings: Tenor code as shown in the Limits Monitor (eg: Day 32)\n- Next rows:\n  - o Column 1: currency couple ISO code (eg: EURUSD) or DEF for the default setting.\n  - o Column 2 and followings: PFE factor for each tenor, in integer with a maximum of 3 decimal places\n\nCurrencyCouple,Day 32,Day 367,Day 732,Day 1827,Day 3652,Default\n\nEURCHF,0.04,0.09,0.16,0.32,0.64,1\n\nEURGBP,0.04,0.15,0.28,0.54,0.9,1.2\n\nEURUSD,0.06,0.18,0.32,0.6,1,1.3\n\nDEF,0.15,0.3,0.6,1.1,2,2.1\n\n<span id=\"page-52-2\"></span>Figure 52 PFE CSV upload file sample\n\nPlease note that the column separator can be selected as \",\" or \";\" by using Preferences > Shared Settings in Bridge.\n\n### <span id=\"page-53-0\"></span>**9 LIMIT CHECK FLOW**\n\nAs discussed in previous sections, Limits Monitor is able to conduct limit check for all negotiation types for supported products. Due to difference in the flow of different negotiations, at which stage limit check and utilization update is conducted also change. This section will provide more details on the nuances of limit check:\n\n### **RFS:**\n\nLimits Monitor conducts limit check at two stages for RFS negotiation. First limit check is conducted when the negotiation is initiated. At this stage;\n\n- If a limit assigned to a quote provider is not sufficient (i.e. in case of limit check failure) from requester`s point of view, that particular provider is taken out of the bank basket and requests won`t be delivered to them.\n- From provider point of view, if limit against a requester is not sufficient, there can be two different consequence depending on the provider`s preference:\n  - o Request can be routed to dealer intervention with a notification that shows the details of limit breach(es). This will give trader the option to price request manually despite the limit check failure. Please contact [<EMAIL>](mailto:<EMAIL>) for activation.\n\n![](_page_54_Picture_2.jpeg)\n\n<span id=\"page-54-0\"></span>Figure 53 Limit Breach Details in TWS\n\no Provider is taken out of bank basket which means provider cannot receive the request (no option to override limit).\n\nAfter the initial limit check, Limits Monitor conducts another check upon execution attempt initiated by the requester. This ensures that all recent updates during RFS negotiation. In case this last limit check fails, execution will not be allowed and rejected. If limit check passes, trade will be booked and utilization will be updated immediately.\n\n#### **Order:**\n\nFor private book orders, Limits Monitor conducts limit check at order entry stage and reserve utilization immediately as soon as limit check passes and order is accepted by the providers. At this stage;\n\n- If a limit assigned to an order provider is not sufficient (i.e. in case of limit check failure) from requester`s point of view, then order cannot be delivered to the provider and stays in Initialized stage.\n- From provider point of view, if limit against a requester is not sufficient, there can be two different consequence depending on the provider`s preference:\n  - o Order can be routed to dealer intervention with a notification that shows the details of limit breach(es). This will give trader the option to accept order manually despite the limit check failure. Please contact [<EMAIL>](mailto:<EMAIL>) for activation.\n\n|                                                                                                                         | <b>• Order View Execution</b><br>ıo<br>Limit Order<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                                                                                                                                                                                                                                                                                                     |\n|-------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Manual Intervention required due to Limit breach:<br>is 120,200.<br>is 120,200.                                         | . Rule 22175394-ER: Available limit -22,500,000 is insufficient for<br>25.11.2022 for counterpart 360T.RMS. Required free limit for the request<br>. Rule 97047993-ER: Available limit -10,000,000 is insufficient for<br>25.11.2022 for counterpart 360T.RMS. Required free limit for the request<br>. Rule 270504: Available limit 0 is insufficient for 25.11.2022 for<br>counterpart 360T.RMS. Required free limit for the request is 120,200. |\n| <b>Order Details</b>                                                                                                    | Order Changes                                                                                                                                                                                                                                                                                                                                                                                                                                      |\n| <b>Listed Product</b><br>Requester Action<br>Notional Amount<br>Limit Spot Rate<br><b>Effective Date</b><br>Market Rate | FX Spot<br>Client Buys RUB / Sells TRY<br>10,000,000.00 RUB<br>0.30880<br>Spot // Fri, 25. Nov 2022<br>0.30860                                                                                                                                                                                                                                                                                                                                     |\n| Counterparts                                                                                                            | Request Changes                                                                                                                                                                                                                                                                                                                                                                                                                                    |\n| Requester Company<br>Requester Individual                                                                               | 360T.RMS<br>360TRMS.RiskManager                                                                                                                                                                                                                                                                                                                                                                                                                    |\n| <b>Expiry Date</b><br>Expiry Date - Local<br>Reference #                                                                | <b>GTC</b><br>PO-1800144-627                                                                                                                                                                                                                                                                                                                                                                                                                       |\n|                                                                                                                         | <b>Override Limit</b><br>Reject                                                                                                                                                                                                                                                                                                                                                                                                                    |\n\n<span id=\"page-55-0\"></span>Figure 54 Limit Breach Details in TWS for Orders\n\no Provider is taken out of bank basket which means provider cannot receive the order (no option to override limit).\n\n#### **Streaming:**\n\nIn Streaming flow, limit check conducted continuously and any quote beyond the limit is filtered out/made unavailable for execution by Limits Monitor. Even though, liquidity is monitored and filtered accordingly, another limit check is conducted when order is placed against a quote and in case of failure, execution attempt is rejected.\n\n#### **Order Book (M|M, COB):**\n\nFor central order books, Limits Monitor conducts limit check on order book level to ensure that pending orders can only be aggressed by counterparties that is eligible as per either their own limits or their counterpart`s limits. In M|M, when there is no sufficient limit for a certain order in the book, traders can see the liquidity but it would be unavailable for them to trade against.\n\n## <span id=\"page-57-0\"></span>**10 AUDIT LOG**\n\n360T`s Limits Monitor has a live audit log view which records every change done on the configurations by admin users.\n\nAudit Log view can be accessed by clicking on icon next to the Risk Portfolio PFE tab.\n\nThe view has three columns:\n\n- Log Date Time = Timestamp of the change (in GMT).\n- User = The user who has performed the change.\n- Description = The content of the change.\n\nSimilar to other tabs, Audit Log has a search function, by which users can filter the audit log entries and find the changes they want to see easily.\n\n| Q                       |                     | $\\rightarrow$                                                                                        |\n|-------------------------|---------------------|------------------------------------------------------------------------------------------------------|\n| <b>Log Date Time</b>    | <b>User</b>         | <b>Description</b>                                                                                   |\n| May 20, 2021 6:31:48 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:100000000, Institution:360T.RMS), Active LimmoRule(Id:205870) |\n| May 20, 2021 9:32:59 AM | ********            | Active Limmo rule limit updated (Limit:1000000, Institution:360T.RMS), Active LimmoRule(Id:205870)   |\n| May 16, 2021 1:17:41 PM | 360TRMS.RiskManager | TimePeriodGroup updated (TimePeriodGroup:-1, TimePeriodGroupName:Anv), LimmoRule(Id:205869)          |\n| May 16, 2021 1:17:41 PM | 360TRMS.RiskManager | Time period updated (From:TODAY, To:TODAY), LimmoRule(Id:205869)                                     |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:217293)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205866)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205867)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205868)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205869)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205870)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205864)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205865)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205863)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205862)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205861)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205858)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205859)         |\n| May 10, 2021 1:19:50 PM | 360TRMS.RiskManager | Active Limmo rule limit updated (Limit:0, Institution:360T.RMS), Active LimmoRule(Id:205860)         |\n| May 9, 2021 7:56:29 PM  | 360TRMS.RiskManager | Limit updated (Limit:50000000), LimmoRule(Id:217293)                                                 |\n| May 9, 2021 7:56:20 PM  | 360TRMS.RiskManager | Algorithm updated (AlgorithmType:Aggregate Gross Settlement Limit), LimmoRule(Id:217293)             |\n\n<span id=\"page-57-1\"></span>Figure 55 Audit Log\n\n## <span id=\"page-58-0\"></span>**11 CONTACT 360T**\n\n#### **Germany**\n\n*360 Treasury Systems AG* Grüneburgweg 16-18 60322 Frankfurt am Main Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Singapore**\n\n*360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756\n\n#### **Middle East**\n\n#### **United Arab Emirates**\n\n*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\n#### **USA**\n\n*360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902\n\n#### **Asia Pacific South Asia**\n\n### **India**\n\n*ThreeSixty Trading Networks (India) Pvt Ltd* Level 8, Vibgyor Towers, G Block, C-62, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4090 7165 Fax: +91 22 4090 7070", "metadata": {"lang": "en"}}]