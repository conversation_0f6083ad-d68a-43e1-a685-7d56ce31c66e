[{"id": "1", "text": "# **360T AUTODEALING SUITE (ADS)**\n\n![](_page_0_Picture_1.jpeg)\n\n# **MARKET MAKER COCKPIT (MMC)**\n\n# **TO ENABLE**\n\n# **PRICE MAKING, DISTRIBUTION AND RISK MANAGEMENT**\n\n© 360 TREASURY SYSTEMS AG, 2015 THIS FILE CONTAINS PROPRIETARY AND CONF<PERSON><PERSON><PERSON>AL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG\n\n| 1 |              | INTRODUCTION                                                                 | 6        |\n|---|--------------|------------------------------------------------------------------------------|----------|\n|   | 1.1          | MMC<br>COMPONENTS                                                            | 7        |\n|   | 1.2          | PRICING MODEL<br>                                                            | 7        |\n|   | 1.3          | OVERALL DATA FLOW<br>                                                        | 7        |\n| 2 |              | MARKET MAKER COCKPIT<br>OVERVIEW<br>                                         | 9        |\n|   | 2.1          | LOGIN AND LOGOUT                                                             | 9        |\n|   | 2.2          | PANELS,<br>TABS AND TAB CONTAINER                                            | 10       |\n|   | 2.3          | RE-ARRANGING TABS                                                            | 10       |\n|   | 2.4          | MODIFYING TABS AND TAB CONTAINERS<br>                                        | 10       |\n| 3 |              | CONFIGURE PRICING<br>                                                        | 12       |\n|   | 3.1          | PRICING CONTROL                                                              | 12       |\n|   | 3.2          | ADD AND REMOVE MANAGED INSTRUMENTS<br>                                       | 13       |\n|   | 3.3          | OWNERSHIP OF A MANAGED INSTRUMENT                                            | 14       |\n|   | 3.4          | START/STOP PRICING OF INSTRUMENTS AND CHANNELS<br>                           | 14       |\n|   | 3.5          | PRICING AND RISK MANAGEMENT MODE                                             | 14       |\n|   | 3.6          | TIER SIZE CONFIGURATION<br>                                                  | 15       |\n|   | 3.7          | INSTRUMENT CONFIGURATION                                                     | 16       |\n|   | 3.8          | ADDITONAL AND FIXED SPREAD                                                   | 17       |\n|   | 3.9          | MANUAL SKEW<br>                                                              | 17       |\n|   | 3.10         | REFERENCE PRICE FINDING                                                      | 18       |\n|   | 3.11         | MANAGING SYNTHETIC CROSSES<br>                                               | 20       |\n|   | 3.12         | PRICING OF UNMANAGED INSTRUMENTS                                             | 21       |\n|   | 3.13         | CONFIGURING TIME SLIPPAGE<br>                                                | 22       |\n|   | 3.14         | CONFIGURING PRICE SLIPPAGE<br>                                               | 22       |\n|   | 3.15         | QUOTE FILTERING<br>                                                          | 24       |\n| 4 |              | MONITORING PRICING<br>                                                       | 26       |\n|   | 4.1          | THE PRICING MONITOR PANEL<br>                                                | 26       |\n|   | 4.2          | INBOUND AND OUTBOUND PRICING TIER MONITOR                                    | 26       |\n|   | 4.3          | PRICING DETAILS DIALOG                                                       | 27       |\n| 5 |              | RISK MANAGEMENT                                                              | 30       |\n|   | 5.1          | MONITORING POSITIONS<br>                                                     | 30       |\n|   | 5.2          | PROFIT AND LOSS (P/L)<br>CALCULATIONS                                        | 31       |\n|   | 5.3          | RISK MANAGEMENT CONFIGURATION                                                | 31       |\n|   | 5.4          | POSITION RULES<br>                                                           | 32       |\n|   | 5.5          | PRICING RULES                                                                | 34       |\n|   | 5.6          | ALERT RULES<br>                                                              | 35       |\n|   | 5.7          | MANUAL POSITION AMENDMENTS<br>                                               | 35       |\n|   | 5.8          | AUTO-HEDGING SAFEGUARDS<br>                                                  | 36       |\n|   | 5.9          | RESTRICT THE BANK BASKET FOR HEDGE ORDERS                                    | 37       |\n|   | 5.10<br>5.11 | CLIENT ORDER HANDLING RULES<br><br>PRICING AND RISK MANAGEMENT SCENARIOS<br> | 38<br>41 |\n|   |              |                                                                              |          |\n| 6 |              | BLOTTERS<br>                                                                 | 43       |\n|   | 6.1          | GENERAL BLOTTER FEATURES<br>                                                 | 43       |\n|   | 6.2          | CLIENT ORDER BLOTTER<br>                                                     | 43       |\n|   | 6.3<br>6.4   | HEDGE ORDER BLOTTER<br>COMBINED CLIENT AND HEDGE ORDER BLOTTER<br>           | 44<br>44 |\n|   |              |                                                                              |          |\n\n|   | 6.5 | CLIENT ACITIVITY BLOTTER           | 44 |\n|---|-----|------------------------------------|----|\n|   | 6.6 | TRADE BLOTTER                      | 45 |\n| 7 |     | AUDIT                              | 46 |\n| 8 |     | APPENDIX<br>                       | 46 |\n|   | 8.1 | JAVA REGULAR EXPRESSION SYNTAX<br> | 46 |\n| 9 |     | CONTACT 360T<br>                   | 47 |\n\n# **TABLE OF FIGURES**\n\n| Figure 1 Market Maker Cockpit Overview9                  |  |\n|----------------------------------------------------------|--|\n| Figure 2 Login and Start the Market Maker Cockpit9       |  |\n| Figure 3 Exit the Market Maker Cockpit10                 |  |\n| Figure 4: Pricing Control<br>Panel12                     |  |\n| Figure 5 Open Global Instrument Configuration13          |  |\n| Figure 6 Global Instrument Configuration dialog13        |  |\n| Figure 7: Take over instrument ownership14               |  |\n| Figure 8: Start/Stop Pricing<br>14                       |  |\n| Figure 9 Emergency button<br>14                          |  |\n| Figure 10: Select risk management mode<br>15             |  |\n| Figure 11: Global Instrument Configuration Dialog<br>15  |  |\n| Figure 12: Open instrument configuration dialog<br>16    |  |\n| Figure 13: Instrument Configuration Dialog<br>16         |  |\n| Figure 14: Basic skew settings<br>17                     |  |\n| Figure 15: Tier specific skew factors18                  |  |\n| Figure 16: Reference Price Finding rules editor20        |  |\n| Figure 17: Cross Currency Pair Configuration<br>20       |  |\n| Figure 18: Pricing unmanaged instruments<br>21           |  |\n| Figure 19: Time slippage configuration22                 |  |\n| Figure 20: Example for price slippage<br>23              |  |\n| Figure 21: Price slippage configuration24                |  |\n| Figure 22: Quote filter settings25                       |  |\n| Figure 23: Monitor filtered quotes<br>25                 |  |\n| Figure 24: Pricing Monitor Panel<br>26                   |  |\n| Figure 25: Pricing Tier monitor<br>27                    |  |\n| Figure 26: Raw Inbound Quote Details28                   |  |\n| Figure 27: Filtered Inbound Quote Details<br>28          |  |\n| Figure 28: Outbound Price Details29                      |  |\n| Figure 29: Managed Positions blotter with context menu30 |  |\n| Figure 30: Risk management position rules32              |  |\n| Figure 31: Risk management pricing rules34               |  |\n| Figure 32 Alert popup message35                          |  |\n| Figure 33 Context menu of Managed Positions<br>35        |  |\n| Figure 34 Amend Position<br>35                           |  |\n| Figure 35 Set Position<br>36                             |  |\n| Figure 36 Confirmation of Position Reset<br>36           |  |\n|                                                          |  |\n\n| Figure 37 Confirmation of Position Flattening<br>36    |  |\n|--------------------------------------------------------|--|\n| Figure 38 Maximum Hedge Order Size Configuration<br>37 |  |\n| Figure 39: Restrict hedge order bank basket<br>38      |  |\n| Figure 40: Manage Client Order Handling Rules<br>39    |  |\n| Figure 41: Client Order Handling Rule testing<br>40    |  |\n| Figure 42: Pricing and Risk Management Scenarios<br>42 |  |\n| Figure 43: Scenario selection<br>42                    |  |\n| Figure 44: Client Activity Blotter45                   |  |\n\n# <span id=\"page-5-0\"></span>**1 INTRODUCTION**\n\nThe 360T Auto Dealing Suite (ADS) includes Pricing Engine functionality to define outbound prices, based on rates streamed inbound from your liquidity provider(s). The outbound price can be adjusted to include trader spreads, skewing etc. Being passed downstream, further adjustments to these prices like sales or customer spreads can be made by using the ADS module Central Distribution Control (CDC).\n\nThe Market Maker Cockpit (MMC) allows automated risk management and pre-set actions with subsequent positions created by customer flows.\n\nPosition overviews and profit and loss calculation are provided. Users can set up the currency pairs that will be actively managed as well as define currency pairs for price stripping and/or quote pegged currencies.\n\n*DISCLAIMER:*\n\n*Please note that clients shall be solely responsible for the use of 360T's Market Maker Cockpit (\"MMC\").* \n\n*The MMC is a fully operational pricing engine with automated pricing functionalities. Each client using the MMC should be aware that an automated setup in general might lead to unsought trade results, and any use of the MMC requires a certain level of experience, requisite knowledge and skills, constant monitoring of the market and periodical review of all settings. 360T is not in the position to monitor any such activities or settings of the MMC and will under no circumstances interfere with any client's MMC setup.*\n\n*With respect to the MMC, 360T will be in no event, regardless of cause, liable for any direct, indirect, special, incidental, punitive or consequential damages of any kind, whether arising under breach of contract, tort (including negligence), or otherwise, and whether based on this agreement or otherwise, even if advised of the possibility of such damages. This applies in particular to the settings of the MMC, its activation or deactivation and any trade executed (or not made) through the MMC.*\n\n*Between the parties using MMC the client shall be solely responsible for the performance and enforcement of any and all trades resulting from using the MMC. Furthermore the MMC is provided to the client on an \"as is\" and \"as available\" basis without warranty of any kind (either express or implied).* \n\n# <span id=\"page-6-0\"></span>**1.1 MMC Components**\n\nThe Market Maker Cockpit consists four major components;\n\n- **Overview:** providing lists of defined currency pairs, views for in- and outbound prices, positions and deal blotters.\n- **Pricing Controller:** enabling the creation of core prices for each defined currency pair and adjustments with spreads and/or skewing.\n- **Risk Management**, enabling the definition of risk parameters for each currency pair and management rules once triggers and alerts have been breached.\n- **Reference Price Finding**, enabling the selection of price provider(s) and pricing tiers etc.\n\n# <span id=\"page-6-1\"></span>**1.2 Pricing Model**\n\nThe outbound rate to a client (client rate) for a specific quantity tier is determined through the following stages:\n\n- Trader Rate = Reference Rate + Additional Spread + Skew\n- Client Rate = Trader Rate + Sales Margin\n\nThis product deals primarily with the calculation of the trader rate. Sales margin is a parameter of the 360T Auto Dealing Suite (ADS), and can be applied individually by client.\n\n#### **Reference Rate:**\n\nIf not a fixed rate, the reference rate will be derived via price finding. This is a process which takes into account available market data and liquidity, risk management profile, and risk appetite. The reference rate is always a two-way price of bid and ask price. The difference between these two prices is the inbound spread. The middle of these two prices, is the reference mid-price.\n\n#### **Additional spread:**\n\nSpread is defined as the difference between bid and ask price. The difference between the reference bid and ask rate is the so called inbound spread. This inbound spread is often widened for various reasons.\n\n#### **Skew:**\n\nSkewing means to shift the mid-price (the middle of bid and ask rate) either towards the bid or ask side. A skewed and non-skewed two way price has still the same spread!\n\nTrader spread and skew are parameters either manually set by the trader, but also automatically via rules.\n\n#### <span id=\"page-6-2\"></span>**1.3 Overall Data Flow**\n\nBelow the general data flow within the MMC:\n\n- 1. Liquidity providers (market makers) send quotes into the system. Each provider sends usually different quote levels. Quotes can be both tradable liquidity, but also pure market data.\n- 2. Quotes are filtered and aggregated during Reference Price Finding. The basic idea here is to filter out unwanted quotes and create a trustable pool of liquidity for pricing.\n\n- 3. Traders will manipulate the inbound liquidity and increase the spread and/or skew the price to the left or ride side.\n- 4. The outbound price is forwarded to clients via RFS, SST, and FIX API's.\n- 5. Clients create orders based on the price provided by the pricing engine. Such orders are routed back to the pricing engine.\n- 6. Accepted client orders are added to the MMC positions.\n- 7. Based on the configured risk management profile, the system will eventually decide to create a hedge order to reduce or flatten a position. Such hedge orders can be created manually too.\n- 8. Trades originating from hedge orders flow back into the pricing engine position.\n\n#### **Please note;**\n\nNever leave the ADS MMC unattended when it was started to actively quote and manage currency positions!\n\n# <span id=\"page-8-0\"></span>**2 MARKET MAKER COCKPIT OVERVIEW**\n\nThe Market Maker Cockpit provides an overview of currency pairs being set up, current positions including profit and loss and various blotters to monitor client (requester) and hedge orders.\n\n| <b>图 Market Maker Cockpit</b>                    |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    |                              |                     |                              |              |                          |                          | 101<br>$\\overline{\\phantom{0}}$                                                  |                        |\n|--------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------|------------------|------------|---------------------|-----------------|----------------------------------------------------------------------|-----------------|------------|----------------------------------------------------------------------------------------|--------------------|------------------------------|---------------------|------------------------------|--------------|--------------------------|--------------------------|----------------------------------------------------------------------------------|------------------------|\n| File Tools Help                                  |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n| <b>Pricing Monitor</b>                           |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      | $\\boxdot$       | Stop All   | <b>Client Orders</b> Client Activity                                                   |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n| Core Channel 1                                   |                                                                                                                                                                                                                         |                            |                  |            | Core Channel 2      |                 |                                                                      |                 |            | Order                                                                                  | <b>Time</b>        | <b>CCY</b>                   |                     |                              |              |                          |                          | Status Side Limit Notional Am Notional Cu Executed Amount E                      |                        |\n| <b>E AUD/USD</b>                                 |                                                                                                                                                                                                                         |                            |                  |            |                     |                 | Net Position: 0 EUR P/L: Open 0 EUR / Realized 39 EUR / Total 39 EUR |                 |            | PE- 03:42:58.608 EUR/USD EXEC Buv 1.27199                                              |                    |                              |                     |                              |              | 1,000,000                | <b>EUR</b><br><b>USD</b> | 1.000.000                                                                        |                        |\n| <b>EUR/USD</b>                                   |                                                                                                                                                                                                                         |                            |                  |            |                     |                 | Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR   |                 |            | PE- 03:42:58.572 USD/JPY EXEC Buv 107.167<br>PE- 03:42:58.616 EUR/JPY EXEC Buy 136.315 |                    |                              |                     |                              |              | 1.271.986<br>1,000,000   | <b>EUR</b>               | 1,271,986<br>1,000,000                                                           |                        |\n| Inbound                                          |                                                                                                                                                                                                                         | Details.<br>Outbound       |                  | Details.   | Inbound             |                 | Details<br>Outbound                                                  |                 | Details.   | PE- 03:42:30.231 AUD/U EXEC Buy 0.87986                                                |                    |                              |                     |                              |              | 1,000,000                | <b>AUD</b>               | 1,000,000                                                                        |                        |\n| $\\overline{12}$<br>$1.27 + 89$<br>1 <sub>m</sub> |                                                                                                                                                                                                                         | 1.27201<br>$1.27 + 89$     | 1.2              | 1.27201    | $1.27 + 8.9$        | $\\bar{12}$      | 1.27201<br>$1.27 + 89$                                               | $\\overline{12}$ | 1.2720     | PE- 03:42:14.397 USD/JPY EXEC Buy 107.158<br>PE- 03:42:02.460 EUR/USD EXEC Buy 1.27194 |                    |                              |                     |                              |              | 1,000,000<br>2.000.000   | <b>USD</b><br><b>EUR</b> | 1.000.000<br>2.000.000                                                           |                        |\n| $\\overline{12}$<br>1.27 189<br>5 <sub>m</sub>    |                                                                                                                                                                                                                         | $1.27$ 201<br>$1.27 + 8$ s | $\\overline{12}$  | $1.27$ 201 | $1.27 + 8.9$        | $\\overline{12}$ | 1.27201<br>$1.27 + 89$                                               | $\\overline{12}$ | 1.27201    | PE- 03:41:46.964 EUR/USD EXEC Buy 1.27179                                              |                    |                              |                     |                              |              | 1,000,000                | <b>EUR</b>               | 1.000.000                                                                        |                        |\n| $1.27$ 184<br>2.1<br>10 <sub>m</sub>             |                                                                                                                                                                                                                         | 1.2720s<br>$1.27$ 185      | 2.1              | 1.27 206   | $1.27$ 184          | 2.1             | 1.27 205<br>$1.27$ 185                                               | 2.1             | 1.27206    |                                                                                        |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n| $1.27$ 184<br>2.1<br>15 <sub>m</sub>             |                                                                                                                                                                                                                         | 1.27206<br>1.27 185        | 2.1              | 1.27 206   | $1.27$ 184          | 2.1             | 1.27206<br>$1.27$ 185                                                | 2.1             | 1.27206    | Ⅲ 1                                                                                    |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n|                                                  | 25 <sub>m</sub><br>×.                                                                                                                                                                                                   |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    |                              |                     |                              |              |                          |                          |                                                                                  | 国                      |\n|                                                  | <b>Hedge Orders All Orders</b><br><b>E GBP/USD</b><br>Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR<br>Order Time CCY Status Side Limit Notional Amount Notional Currency Executed Amount Executio |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n|                                                  | <b>E USD/INR</b><br>Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR<br>PE- 03:4 EU EXEC Sell 1.27<br>1.000.000<br><b>EUR</b><br>1.000.000                                                            |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n|                                                  | <b>ELISDAIPY</b><br>Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR                                                                                                                                  |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    | PE- 03:4 US EXEC Sell 107    |                     | 1,271,986<br>1.000.000       |              | <b>USD</b><br><b>AUD</b> |                          | 1,271,986                                                                        |                        |\n|                                                  |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            | PE- 03:4 AU EXEC Sell 0.87<br>PE- 03:4 US EXEC Sell 107                                |                    |                              |                     | 1,000,000                    |              | <b>USD</b>               |                          | 1.000.000<br>1,000,000                                                           |                        |\n|                                                  |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            | PE- 03:4 EU EXEC Sell 1.27                                                             |                    |                              |                     | 2.000.000                    |              | <b>EUR</b>               |                          | 2.000.000                                                                        |                        |\n|                                                  |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            | PE- 03:4 EU EXEC Sell 1.27                                                             |                    |                              |                     | 1,000,000                    |              | <b>EUR</b>               |                          | 1,000,000                                                                        |                        |\n|                                                  |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n|                                                  |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            | Ⅲ 4                                                                                    |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n|                                                  |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            | <b>Currency Pair Positions</b> Trade Blotter                                           |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n| <b>Pricing Control</b>                           |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 | ⊞          | Total Net Position: 0 EUR P/L: Open 0 EUR / Realized 39 EUR / Total 39 EUR             |                    |                              |                     |                              |              |                          |                          |                                                                                  |                        |\n| Instrument/Ch $\\mathbf{A}^1$                     | <b>Status</b>                                                                                                                                                                                                           | Managed                    |                  |            | Control             |                 | Mode                                                                 | Inbound         | Inboun     | Symbol                                                                                 |                    |                              |                     |                              |              |                          |                          | Updated Size CCY1 Size CCY2 Open P/L Realized P/L Total P/L Reval. Rate Avg. Buy |                        |\n|                                                  |                                                                                                                                                                                                                         | By                         | Start            | Stop       | Ownership Configure |                 |                                                                      | <b>BID</b>      | <b>ASK</b> | <b>EUR/USD</b><br>USD/JPY                                                              | 03:42:5<br>03:42:5 |                              | 0.00<br>0.00        | $\\mathbf{0}$<br>$\\mathbf{0}$ | 0.00<br>0.00 | 0.00<br>0.00             | 0.00<br>0.00             | 1.27201<br>107.182                                                               | 1.2<br>10 <sub>1</sub> |\n| <b>All Instruments</b>                           | $\\Omega$                                                                                                                                                                                                                |                            | $\\bullet$        | Θ          |                     | Ð.,             | Change •                                                             |                 |            | <b>AUD/USD</b>                                                                         | 03:42:3            |                              | 0.00                | 50                           | 0.00         | 39.31                    | 39.31                    | 0.87989                                                                          | 0.8                    |\n| <b>AUD/USD</b>                                   | $\\bullet$                                                                                                                                                                                                               | PEAPAC.T.                  | $\\Theta$         | Θ          | Take                | ο.              | Managed <b>v</b>                                                     |                 |            | <b>GBP/USD</b>                                                                         | 03:33:1            |                              | $\\overline{0}$      | $\\mathbf{0}$                 | 0.00         | $\\mathbf{0}$             | 0.00                     | 1.60673                                                                          |                        |\n| <b>EUR/USD</b>                                   | $\\Omega$                                                                                                                                                                                                                | PEAPAC.T                   | $\\odot$          | 0          | Take                | $\\mathbf{Q}$    | B <sub>2</sub> B<br>$\\overline{\\phantom{a}}$                         |                 |            | <b>USD/INR</b><br>Ⅲ 4                                                                  | 03:33:3            |                              | $\\mathbf{0}$        | $\\mathbf{0}$                 | 0.00         | $\\mathbf{0}$             | 0.00                     | 60.9630                                                                          |                        |\n| Core Chann                                       | $\\bullet$                                                                                                                                                                                                               |                            | ⊜                | Θ          |                     | Ο.              |                                                                      | 1.27189         | 1.272      | Trade ID                                                                               |                    | $Cre$ $+1$                   | Type                | Trigger                      | Side         | Quantity                 | Price                    | Counterparty                                                                     |                        |\n| <b>Core Chann</b>                                | $\\bullet$                                                                                                                                                                                                               |                            | ⊜                | Θ          |                     | ο.              |                                                                      | 1.27189         | 1.272      | PE-458511                                                                              |                    | 03:42:5                      | Cross               |                              | Buv          | 1.000.000                |                          | 1.27199 PE-458502                                                                | Ш                      |\n| <b>E GBP/USD</b>                                 | Θ                                                                                                                                                                                                                       | PEAPAC.T.                  | $\\bullet$        | ⊖          | Take                | Ô.,             | <b>B2B</b>                                                           |                 |            | $P_{E-458542}$                                                                         |                    | 03:42:5                      |                     | Hedge PE-458511              | Sell         | 1.000.000                | 1.27199                  |                                                                                  |                        |\n| <b>E USD/INR</b>                                 | Θ                                                                                                                                                                                                                       | PEAPAC.T.                  | $\\mathbf \\Theta$ | ⊖          | Take                | ο.              | Managed ~                                                            |                 |            | PE-458405                                                                              |                    | SO-216186 03:42:5<br>03:42:0 | Hedge<br><b>RFS</b> |                              | Sell<br>Buy  | 1.000.000                |                          | 1.27199 Barclays BARX.D<br>2,000,000 1.27194 360T.Fischer                        |                        |\n|                                                  | Ο                                                                                                                                                                                                                       |                            | ⊜                | 0          |                     |                 |                                                                      |                 |            | PE-458413                                                                              |                    | 03:42:0                      |                     | Hedge PE-458405              | Sell         | 2,000,000                | 1.27194                  |                                                                                  |                        |\n| <b>E USD/JPY</b>                                 |                                                                                                                                                                                                                         | PEAPAC.T                   |                  |            | Take                | <b>Q</b>        | Flow He -                                                            |                 |            | SO-216186                                                                              |                    | 03:42:0                      | Hedge               |                              | Sell         |                          |                          | 2,000,000 1.27194 Barclays BARX.D                                                |                        |\n|                                                  | Ⅲ 4                                                                                                                                                                                                                     |                            |                  |            |                     |                 |                                                                      |                 |            | PE-458382                                                                              |                    | 03:41:4.                     | <b>RFS</b>          |                              | Buy          |                          |                          | 1.000.000 1.27179 360T.Fischer                                                   |                        |\n| $\\Theta$                                         |                                                                                                                                                                                                                         |                            |                  |            |                     |                 |                                                                      |                 |            |                                                                                        |                    |                              |                     |                              |              |                          |                          | User: PEAPAC.Trader1, Company: PEBANK_APAC.TEST ETERS T                          |                        |\n\n<span id=\"page-8-2\"></span>Figure 1 Market Maker Cockpit Overview\n\n# <span id=\"page-8-1\"></span>**2.1 Login and Logout**\n\nLogin to the MMC user interface via the 360T single sign-on applet. To start the MMC GUI click on the MMC button:\n\n<span id=\"page-8-3\"></span>![](_page_8_Picture_8.jpeg)\n\nFigure 2 Login and Start the Market Maker Cockpit\n\nThe application buttons are shown in green when they are starting or currently running. More than one application can be started at the same time.\n\nWhen you activate the radio button right next to the application button, the application will always automatically restart on your next login.\n\nTo close the application select menu **File** and click on **Exit** in the drop down menu.\n\n![](_page_9_Figure_5.jpeg)\n\n<span id=\"page-9-3\"></span>Figure 3 Exit the Market Maker Cockpit\n\n## <span id=\"page-9-0\"></span>**2.2 Panels, Tabs and Tab Container**\n\nThe user interface is divided into various tabs such as \"Pricing Monitor\", \"Managed Positions\", \"Pricing Control\" and Order blotters.\n\nEach tab consists of a tab header (rectangle box in the upper left corner containing the tab title), and tab content.\n\nTabs are organized within tab containers. By default there are three tab containers; one upper left, one upper right, and one at the bottom. Each tab container is resizable by either moving the border between tab containers, or by resizing the main window. New tab containers can be added, and existing tab containers can be removed.\n\nYour customized user interface layout will be automatically saved when logging out to be available at the next login\n\n#### <span id=\"page-9-1\"></span>**2.3 Re-arranging Tabs**\n\nTabs can be moved within the same tab container, into another existing tab container, or into a new tab container. Tabs can also be completely detached from the main window and placed somewhere on the screen.\n\nTo move a tab, click and hold the tab header with the left mouse button, and move the tab into a new location. Release the mouse button over the new target location.\n\nTo re-arrange the tab order within a tab container, drag and drop the tab horizontally within the same tab container.\n\nTo move a tab into another tab container, drop the tab onto the tab header of the target tab container.\n\nTo create a new tab container, drop the tab into the content of any other tab. Dependent where you drop the tab, the target tab will split either horizontally or vertically, and a new tab container will be added.\n\nWhen the last tab of a container is removed, the tab container will disappear automatically.\n\n#### <span id=\"page-9-2\"></span>**2.4 Modifying Tabs and Tab Containers**\n\nRight clicking with the mouse on a tab header shows the following context menu options:\n\n- **Floating:** This will detach the tab from the main window into a separate window. This \"child\" window can be placed anywhere on the desktop. The same can be achieved by placing the mouse on the tab header, pressing and holding the left mouse button, and dragging the tab out of the main window.\n- **Auto-Hide:** This will shrink the entire tab container to the size of the tab headers. The tab headers will be placed at the nearest window border. To view and hide a tab the user has to click on one of the tab headers.\n- **Maximize:** This will maximize the tab container to the size of the enclosing window. The same can be achieved by double clicking the tab header with the left mouse button.\n- **Restore:** This option is only available when the tab container is maximized. Clicking it will shrink the tab container to the original size. The same can be achieved by double clicking on the tab header with the left mouse button.\n\n# <span id=\"page-11-0\"></span>**3 CONFIGURE PRICING**\n\n# <span id=\"page-11-1\"></span>**3.1 Pricing Control**\n\nThe pricing control tab allows to configure:\n\n- Managed instruments\n- Global instrument configuration\n- Start and stop pricing and set the pricing/risk management mode\n- Configure pricing tiers\n- Configure pricing and skewing rules\n- Configure reference price finding\n- Configure risk management\n- Acquire ownership for an instrument or price channel\n\nThe pricing control panel is organized into rows and columns. Each row contains information for a specific managed instrument or channel.\n\nFor each currency pair the trader can configure multiple pricing channels. Each pricing channel can have different spread/skew settings and Reference Price Finding rules. The assignment of your clients to the different pricing channel prices has to be done by 360T support.\n\n| <b>Pricing Control</b>       |               |               |                |                   |             |                    |                              |                              |            |            | 圓                                       |\n|------------------------------|---------------|---------------|----------------|-------------------|-------------|--------------------|------------------------------|------------------------------|------------|------------|-----------------------------------------|\n| Instrument/Ch $\\mathbf{A}^1$ | <b>Status</b> | Managed<br>By | <b>Start</b>   | Stop              | Control     | Ownershi Configure | Mode                         | <b>Inbound</b><br><b>BID</b> | <b>ASK</b> | <b>BID</b> | Inbound Outbound Outbound<br><b>ASK</b> |\n| <b>All Instruments</b>       | $\\bullet$     |               | $\\mathbf O$    | $\\mathbf 0$       |             | 0.                 | Change                       |                              |            |            |                                         |\n| I⊞ AUD/USD                   | O             | PEAPAC.T      | $\\odot$        | $\\mathbf{\\Theta}$ | Take        | O                  | Managed $\\blacktriangledown$ |                              |            |            |                                         |\n| $\\equiv$ Eur/USD             | $\\mathbf{O}$  | PEAPAC.T      | $\\circledcirc$ | 0                 | <b>Take</b> | o<br>$\\cdots$      | B <sub>2</sub> B             |                              |            |            |                                         |\n| Core Chann                   | O             |               | $\\Theta$       | $\\mathbf{\\Theta}$ |             | 0.                 |                              | 1.27194                      | 1.27206    | 1.27194    | 1.27206                                 |\n| Core Chann                   | O             |               | $\\odot$        | $\\mathbf{\\Theta}$ |             | $\\bullet$          |                              | 1.27194                      | 1.27206    | 1.27194    | 1.27206                                 |\n| I⊞ GBP/USD                   | O             | PEAPAC.T      | $\\mathbf O$    | $_{\\tiny{\\odot}}$ | Take        | 0.                 | B <sub>2</sub> B<br>▼        |                              |            |            |                                         |\n| <b>E</b> USD/INR             | O             | PEAPAC.T      | $\\mathbf O$    | $\\Theta$          | Take        | O                  | Managed $\\blacktriangledown$ |                              |            |            |                                         |\n| <b>E USD/JPY</b>             | $\\mathbf O$   | PEAPAC.T      | $\\odot$        | $\\mathbf{O}$      | Take        | O                  | Flow He •                    |                              |            |            |                                         |\n\n<span id=\"page-11-2\"></span>Figure 4: Pricing Control Panel\n\nColumn description:\n\n- **Instrument/Channel:** Displays the instrument (top level), or a specific channel for the instrument (child level)\n- **Status:** Displays the current pricing status per instrument or channel. Green indicates pricing is on, red indicates pricing is off. Yellow applies to a specific instrument or \"All Instruments\", and indicates a mixed pricing state of channels beneath.\n- **Managed By:** Displays which user currently \"owns\" the instrument. Only the user who owns an instrument can change its pricing and risk management configuration, and start or stop pricing for this instrument. An empty value indicates that nobody currently owns the instrument. This happens only when an instrument was newly added.\n- **Start/Stop buttons:** Clicking the green button will start pricing for that instrument or channel, clicking the red button will stop pricing for the instrument or channel. Pressing the start or stop button on an instrument level affects all channels for this\n\ninstrument. Pressing start or stop in the \"All Instruments\" row will affects all instruments the user currently owns.\n\n- **Ownership:** A user can take ownership for a specific instrument by clicking the \"Take\" button. This button is only enabled for instruments owned by other users.\n- **Configure:** Depending on the selected row, a user can either adjust certain settings for all instruments, or pricing and Reference Price Finding for a specific instrument by clicking this button which is only enabled for instruments the user currently owns.\n- **Mode:** Here is the current risk management mode per instrument displayed. The mode can be changed by clicking the button, which will open a drop down box. The value can only be changed by the user who currently owns the instrument.\n- **Inbound Bid/Ask:** Best available inbound price for this channel\n- **Outbound Bid/Ask:** Best available outbound price for this channel\n\n#### <span id=\"page-12-0\"></span>**3.2 Add and Remove Managed Instruments**\n\nTo manage pricing and risk for a specific instrument, it must be added to the list of managed instruments in the Pricing Control tab. To do so press on the configure button for \"All Instruments\" in the \"Pricing Control\" panel.\n\n|  | <b>Pricing Control</b>       |               |            |              |      |           |           |         |  |\n|--|------------------------------|---------------|------------|--------------|------|-----------|-----------|---------|--|\n|  |                              |               | Managed    |              |      | Control   |           |         |  |\n|  | Instrument/Ch $\\mathbf{A}^1$ | <b>Status</b> | By         | <b>Start</b> | Stop | Ownership | Configure | Mode    |  |\n|  | All Instruments              |               |            |              |      |           | <b>M.</b> | Change  |  |\n|  | ⊪⊞ AUD/USD                   |               | PFAPAC.Tr. |              |      | Take      |           | Managed |  |\n\n<span id=\"page-12-1\"></span>Figure 5 Open Global Instrument Configuration\n\nThis will open the \"Global Instrument Configuration\" dialog:\n\n| $\\mathbf x$<br>Global Instrument Configuration                                                                                                  |                                                                                                                                                         |  |  |  |  |  |  |  |\n|-------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|\n| <b>General Parameters</b><br>$\\triangleq$ Instruments<br><b>Tiers</b><br><b>Scenarios</b><br><b>Cross Rules</b><br><b>Client Order Handling</b> | <b>Instruments</b><br>Base CCY<br>Quote C<br><b>USD</b><br><b>JPY</b><br>Add<br>$\\mathbf{v}$<br>▼<br>AUD/USD<br>Remove<br>EUR/USD<br>GBP/USD<br>USD/JPY |  |  |  |  |  |  |  |\n|                                                                                                                                                 | QK<br>Cancel<br>Apply                                                                                                                                   |  |  |  |  |  |  |  |\n\n<span id=\"page-12-2\"></span>Figure 6 Global Instrument Configuration dialog\n\nTo add a new managed instrument, select base and quote currency, and press the Add button.\n\nTo remove a specific managed instrument, select the currency pair in the list, and press the Remove button.\n\n# <span id=\"page-13-0\"></span>**3.3 Ownership of a Managed Instrument**\n\nA user can only control pricing and risk management for instruments he currently owns. To take ownership of another user's instrument, click on the \"Take\" button in the Pricing Control tab. The \"Take\" button is only enabled for the instruments which are currently owned by another user.\n\n|                 | Control |      |             |  |  |\n|-----------------|---------|------|-------------|--|--|\n| Managed By      | Start   | Stop | Ownership   |  |  |\n|                 |         |      |             |  |  |\n| PEEMEA1.Trader2 |         |      | Take        |  |  |\n| PEEMEA1.Trader2 |         |      | <b>Take</b> |  |  |\n| PEEMEA1.Trader2 |         |      | Take        |  |  |\n| PEEMEA1.Trader1 |         |      | Take        |  |  |\n\n<span id=\"page-13-3\"></span>Figure 7: Take over instrument ownership\n\nThe column \"Managed By\" shows the user who currently owns a specific instrument.\n\n## <span id=\"page-13-1\"></span>**3.4 Start/Stop Pricing of Instruments and Channels**\n\nTo start or stop pricing of an instrument or channel, click the \"Start\" or \"Stop\" button in the according row in the \"Pricing Control\" tab.\n\n![](_page_13_Picture_10.jpeg)\n\nFigure 8: Start/Stop Pricing\n\n<span id=\"page-13-4\"></span>Note: A user can only start or stop his own managed instruments!\n\nTo stop **all** pricing of **all** currency pairs click \"**Stop All**\". No request will be quoted any more.\n\n![](_page_13_Picture_14.jpeg)\n\n<span id=\"page-13-5\"></span>The 'Stop All\" button is an emergency stop button. Pressing this button pricing will stop for all managed instruments!\n\n#### <span id=\"page-13-2\"></span>**3.5 Pricing and Risk Management Mode**\n\nTo quickly change between pricing and risk management modes for a specific instrument, select the respective mode in the Mode column for the specific instrument.\n\n![](_page_14_Picture_2.jpeg)\n\nFigure 10: Select risk management mode\n\n<span id=\"page-14-1\"></span>The provided modes are:\n\n- **Managed:** All currently defined risk management rules apply. Positions will potentially be accumulated.\n- **B2B:** Back to back, all incoming requester orders will first be hedged, and **only accepted** if the hedge trade was successful (last look)\n- **Flow Hedge:** All incoming requester orders will be accepted, but immediately hedged (without a last look)\n\nA user can change the risk management mode for all his currently owned instruments, by selecting a risk management mode in row \"All Instruments\".\n\n# <span id=\"page-14-0\"></span>**3.6 Tier Size Configuration**\n\nTo adjust the tier size for instruments, open the global instrument configuration dialog and select option \"Tiers\":\n\n| $\\mathbf x$<br><b>Global Instrument Configuration</b><br>MAC |                   |           |           |                                              |                          |                      |              |  |\n|--------------------------------------------------------------|-------------------|-----------|-----------|----------------------------------------------|--------------------------|----------------------|--------------|--|\n| <b>General Parameters</b>                                    | <b>Tiers</b>      |           |           |                                              |                          |                      |              |  |\n| Instruments<br>$\\triangleq$ Tiers                            | <b>Instrument</b> | Tier 1    | Tier 2    | Tier 3                                       | Tier 4                   | Tier 5               | Tier 6       |  |\n| <b>Scenarios</b>                                             | AUD/USD           | 1,000,000 |           | 5,000,000 10,000,000 15,000,000 25,000,000   |                          |                      |              |  |\n| <b>Cross Rules</b>                                           | EUR/USD           | 1,000,000 | 5,000,000 | $ 10,000,000 $ $ 15,000,000 $ $ 25,000,000 $ |                          |                      |              |  |\n| Client Order Handling                                        | GBP/USD           | 500,000   | 1,000,000 | 3,000,000                                    |                          | 5,000,000 10,000,000 |              |  |\n|                                                              | USD/JPY           | 1,000,000 | 2,000,000 | 4,000,000                                    |                          | 8,000,000 12,000,000 |              |  |\n|                                                              |                   |           |           |                                              |                          |                      |              |  |\n|                                                              |                   |           |           |                                              |                          |                      |              |  |\n|                                                              |                   |           |           |                                              |                          |                      |              |  |\n|                                                              |                   |           |           |                                              |                          |                      |              |  |\n|                                                              |                   |           |           |                                              |                          |                      |              |  |\n|                                                              |                   |           |           |                                              | $\\overline{\\mathsf{OK}}$ | Cancel               | <b>Apply</b> |  |\n\n<span id=\"page-14-2\"></span>Figure 11: Global Instrument Configuration Dialog\n\nThe dialog allows a user to configure up to 6 pricing tiers of arbitrary size individually for each managed instrument.\n\nTo change the size of an existing tier simply overwrite the according cell with a new value. To remove a specific tier, erase the value and leave the cell empty. Tiers can be entered in any order. The MMC GUI will display the tiers automatically sorted by size.\n\nAfter pressing Ok or Apply, changes in the tier configuration are effective immediately.\n\nNote:\n\nThe MMC stores pricing and reference price finding configurations for each cell in the configuration dialog (e.g. EUR/USD – Tier 3). This configuration will be deleted whenever the size of a specific tier is changed!\n\n## <span id=\"page-15-0\"></span>**3.7 Instrument Configuration**\n\nTo configure pricing for a specific instrument, click on the Configure button in the Pricing Control tab:\n\n| Managed |\n|---------|\n| Managed |\n| B2B     |\n\n<span id=\"page-15-1\"></span>Figure 12: Open instrument configuration dialog\n\nThis will open the 'Instrument Configuration' dialog for the selected instrument:\n\n| Instrument Configuration - EUR/USD           |                                                             |                                                   |                                                                          | $\\mathbf x$                |  |  |  |  |\n|----------------------------------------------|-------------------------------------------------------------|---------------------------------------------------|--------------------------------------------------------------------------|----------------------------|--|--|--|--|\n| Pricing<br>$\\blacklozenge$ Core Channel 1    | <b>Pricing.Core Channel 1</b>                               |                                                   |                                                                          |                            |  |  |  |  |\n| Core Channel 2                               | <b>Spread</b>                                               | <b>Skew</b>                                       |                                                                          | <b>Slippage</b>            |  |  |  |  |\n| Reference Price Finding                      | M<br>$H +$ Pips $\\blacktriangleright$<br><b>Min</b><br>Tier | <b>Max</b>                                        | <b>Skew Factor</b>                                                       | ℅                          |  |  |  |  |\n| Core Channel 1                               | $0.5 -$<br>$0.2 -$<br>1 <sub>m</sub>                        | d♡<br>$0.2 -$                                     |                                                                          | $\\mathbf{0}$ $\\div$<br>8%  |  |  |  |  |\n| Core Channel 2                               | $0.7 -$<br>$0.5 -$<br>5 <sub>m</sub>                        | 0.5<br>ж,                                         |                                                                          | $0\\frac{1}{2}$<br>26%      |  |  |  |  |\n| Risk Management<br><b>General Parameters</b> | $0.8 -$<br>10 <sub>m</sub>                                  | $1\\div$<br>0.8                                    | ж.                                                                       | $0 \\rightarrow$<br>42%     |  |  |  |  |\n| <b>Position Rules</b>                        | $0.0 -$<br>1.2 <sub>1</sub><br>15m                          | ÷<br>0.0                                          | œ                                                                        | $\\mathbf{0}$ $\\div$<br>61% |  |  |  |  |\n| <b>Pricing Rules</b>                         | $0.0 -$<br>$0.0 -$<br>25m                                   | 0.0 <sub>1</sub>                                  | ☞                                                                        | $\\mathbf{0}$ $\\div$<br>87% |  |  |  |  |\n| Quote Filtering                              |                                                             | િ<br>$\\mathbf{I}$<br>$\\mathbf{I}$<br>$\\mathbf{L}$ | <b>The Committee of the Committee of the Committee</b><br>$\\blacksquare$ | 0%                         |  |  |  |  |\n|                                              | Allow Spread less than Market                               |                                                   |                                                                          |                            |  |  |  |  |\n|                                              | ∨ Allow Skew to cross Mid Price                             |                                                   |                                                                          |                            |  |  |  |  |\n| Allow Skew to cross Opposite Side            |                                                             |                                                   |                                                                          |                            |  |  |  |  |\n|                                              | Allow Slippage Worse than Client Order Price                |                                                   |                                                                          |                            |  |  |  |  |\n|                                              |                                                             |                                                   | OK                                                                       | Cancel<br><b>Apply</b>     |  |  |  |  |\n\n<span id=\"page-15-2\"></span>Figure 13: Instrument Configuration Dialog\n\nFor each managed instruments users can configure:\n\n- Pricing additional or fixed spread, manual skew, and cutoff rules\n- Reference Price Finding filter criteria to define reference prices\n- Risk Management position and pricing rules\n\nThe navigation tree on the left side of the dialog allows to select Pricing or Reference Price Finding for a specific channel, and risk management for the entire instrument.\n\n# <span id=\"page-16-0\"></span>**3.8 Additonal and Fixed Spread**\n\nConfiguring spreads can be done in various ways:\n\n- **Fixed:** Fixed outbound spread independent of the inbound spread\n- **% Inbound:** Relative outbound spread based on percentage of inbound spread\n- **+PIPS:** Outbound spread is always n PIPS wider than the inbound spread\n\n**Fixed** spreads are calculated around the mid-price of Inbound. When chosen, the system applies the defined fixed spread or, if wider, the inbound spread.\n\nIf wished, select the checkbox \"Allow Spread less than Market\". A fixed spread of \"0\" means that the inbound spread is used.\n\n**% Inb.** adds the defined percentage on the inbound spread.\n\n**+PIPS** adds the specified number of pips to the inbound spread.\n\nIndependent of the chosen spread mode, the outbound spread can always be limited by a minimum and a maximum spread.\n\n#### <span id=\"page-16-1\"></span>**3.9 Manual Skew**\n\nManual Skew can be set both in percentage of inbound spread, and in absolute numbers specified in PIPS. Both options can be used individually but also in combination. Applying manual skew is a combination of **basic instrument wide skew settings**, and \"**Skew Factors**\", which distribute the selected base value to each pricing tier.\n\nBasic skew settings can be adjusted in the \"Pricing Control\" panel in columns \"Skew PIPS\" and \"Skew Percent\". If the columns are not visible, right click on any other column in the \"Pricing Control\" panel and select option \"Choose Columns…\".\n\n| <b>Skew</b><br><b>PIPS</b> |        | <b>Skew</b><br>Percent               |                   |    |                                  |\n|----------------------------|--------|--------------------------------------|-------------------|----|----------------------------------|\n|                            |        |                                      |                   |    |                                  |\n| ¥<br>v                     | 1.3    | $\\lambda$ $\\lambda$<br>$\\cdots$      | ×<br>$\\checkmark$ | 0  | ⋩<br>$\\hat{\\phantom{a}}$<br>1.11 |\n| ¥<br>$\\checkmark$          | 0      | <b>^   ☆</b><br><b>STATE</b>         | ×.<br>$\\check{}$  | 14 | $\\hat{\\phantom{a}}$<br>☆<br>     |\n| ¥<br>v                     | $-2.1$ | ⋩<br>$\\hat{\\phantom{a}}$<br>$\\cdots$ | ×.<br>v           | -6 | ⋩<br>$\\hat{\\phantom{a}}$<br>     |\n\n<span id=\"page-16-2\"></span>Figure 14: Basic skew settings\n\nIn general, clicking on the single arrow buttons increments and decrements PIPS in steps of 0.1, and percentage in steps of 1. Clicking on the double arrow buttons increments and decrements PIPS in steps of 1, and percentage in steps of 10. For certain instruments like **USD/INR** which are quoted in quarterly PIPS, clicking on the single arrow buttons in the \"Skew PIPS\" column will increment and decrement the value in steps of 0.25.\n\n#### Any change in these values is immediately effective!\n\nThe selected values can be distributed to each pricing tier by \"Skew Factors\". A skew factor of 10% for the 1m tier means, only 10% of the skew value chosen in the pricing control panel will be applied to this tier. The maximum skew factor is 100% which means, whatever skew values are chosen in the pricing control panel, will be fully applied to this tier.\n\nTo adjust skew factors for a specific instrument open the \"Instrument Configuration\" dialog, and chose option \"Pricing\":\n\n| <b>Skew</b>                                                                                                            |     |\n|------------------------------------------------------------------------------------------------------------------------|-----|\n| <b>Skew Factor</b>                                                                                                     |     |\n| Ţ.                                                                                                                     | 10% |\n| Œ,                                                                                                                     | 34% |\n| ŀ.                                                                                                                     | 50% |\n| p.                                                                                                                     | 74% |\n| إعبإ                                                                                                                   | 95% |\n| les j<br>ī<br>$\\mathbf{I}$<br>$\\mathbf{I}$<br>$\\mathbf{I}$<br>$\\mathbf{I}$<br>ī<br>ī<br>$\\mathbf{I}$<br>$\\blacksquare$ | 0%  |\n\n<span id=\"page-17-1\"></span>Figure 15: Tier specific skew factors\n\nIf skewing to cross the mid-rate or even the opposite side should be allowed, one MUST tick the respective options to demonstrate one has taken an informed decision.\n\nHit **Apply** to confirm and apply the defined rule changes.\n\n### <span id=\"page-17-0\"></span>**3.10Reference Price Finding**\n\nThe definition of the reference price(s) to be the base for the inbound price is required.\n\nInitially no provider is selected. To get inbound prices one has to select **Reference Price Finding** and hose the desired provider(s).\n\nThere are various strategies and parameters to filter inbound quotes, and to calculate a reference price for each pricing tier:\n\n| Strategy       | Parameter 1        | Parameter 2       |\n|----------------|--------------------|-------------------|\n| Best Price     | Minimum Quote Size | Minimum Providers |\n| Deep Average   | Minimum Quote Size | Levels            |\n| Deep Worst     | Minimum Quote Size | Levels            |\n| VWAP Average   | Minimum Quote Size | Maximum Providers |\n| VWAP Worst     | Minimum Quote Size | Maximum Providers |\n| Fix Core Price | Bid                | Ask               |\n\n**Best Price** will try to find the best single quote, of at least minimum quote size. To find a reference price, there must be at least 'minimum providers' number of quotes, of minimum size available.\n\nThis strategy is ideal for B2B hedging when maximum fill ratio is most important (e.g. in case of RFS client orders). When Best Price is used, B2B hedge orders will be placed as limit orders, with the original reference price. This is to ensure that both trader and sales spread will be preserved.\n\n**Deep Average and Deep Worst** consider the top n (levels) quotes in the book, of at least minimum size. Average will calculate a VWAP price of the top n levels, whereas worst will simply pick the nth level down from top of the book. A level is considered as a unique price from a specific provider. In other words, identical prices from two different providers are considered as two levels.\n\n**Level Provider Bid Ask Provider** A 1.1240 1.1242 B B 1.1240 1.1243 B B 1.1239 1.1243 A C 1.1238 1.1244 C D 1.1237 1.1245 D\n\nExample book:\n\nThe example shows on the bid side for the first three levels: 1.1240, 1.1240, 1.2339.\n\nBid levels 1 and 2 are treated as different level because they are from different providers, even though the price is the same.\n\n**VWAP Average and VWAP Worst** will calculate a VWAP reference price by considering all quotes of at least minimum quote size. The required quantity for the VWAP price is in general identical with the pricing tier size. E.g. for a 5m tier, the VWAP algorithm will try to find the best VWAP price for 5m quantity. Only one quote from each provider will be used in the VWAP calculation. The maximum number of providers in the VWAP calculation can be limited.\n\nThe strategy will not return a reference price if the total quantity of quotes available (with minimum quote size) is less than the required amount (pricing tier size). This can be overruled by enabling the option **\"Allow VWAP price calculation for less than required quantity\"**. When this option is enabled, the strategy will try to calculate the best VWAP price, by reducing the required quantity step by step.\n\nVWAP is a good strategy for running positions, but also for B2B hedging when partially filling of client orders is acceptable. With VWAP pricing, B2B hedge orders will be placed as limit orders with the requested price of the client order. In other words, in the worst case the hedge order will be filled with the client order price, and both trader and sales spread are lost.\n\n**Fix Core Price** will directly define a bid and ask outbound price for the selected channel and can be used for pegged currencies.\n\n| Instrument Configuration - EUR/USD                                                            |                                                                                                                                |       | Х                                                                                                                                                                                                                                                                                                       |\n|-----------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------|-------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <b>Pricing</b>                                                                                | Reference Price Finding.Core Channel 1                                                                                         |       |                                                                                                                                                                                                                                                                                                         |\n| Core Channel 1<br>Core Channel 2                                                              | <b>Providers</b><br><b>Tier</b>                                                                                                | Hedge | <b>Strategy</b>                                                                                                                                                                                                                                                                                         |\n| Reference Price Finding<br>Core Channel 1<br>Core Channel 2                                   | BOAL.DEMO; Barclays  ▼<br>1 <sub>m</sub>                                                                                       |       | Min. Quote Size Min. Providers<br>1,000,000<br>$ALL$ $\\rightarrow$<br><b>Best Price</b><br>Min. Quote Size Min. Providers                                                                                                                                                                               |\n| Risk Management                                                                               | BOAL.DEMO; Barclays  ▼<br>5 <sub>m</sub>                                                                                       |       | 5,000,000<br>ALL $\\div$<br><b>Best Price</b>                                                                                                                                                                                                                                                            |\n| <b>General Parameters</b><br><b>Position Rules</b><br><b>Pricing Rules</b><br>Quote Filtering | <b>Barclays BARX.DEMO</b><br>10 <sub>m</sub><br>CITIBANK.DEMO<br>15 <sub>m</sub><br>25 <sub>m</sub>                            |       | Min. Quote Size Min. Providers<br>10,000,000<br>$ALL \\rightleftharpoons$<br><b>Best Price</b><br><b>Best Price</b><br>Min. Quote Size Min. Providers<br>Deep Average<br>15,000,000<br>ALL $\\frac{1}{x}$<br>Deep Worst<br>Min. Quote Size Min. Providers<br><b>WAP Average</b><br>$25,000,000 -$<br>ALL- |\n|                                                                                               | <b>Override</b>                                                                                                                |       | <b>WAP Worst</b><br><b>Fix Core Prices</b>                                                                                                                                                                                                                                                              |\n|                                                                                               | All tiers best price and minimum quote size at least tier size<br>Allow VWAP price calculation for less than required quantity |       |                                                                                                                                                                                                                                                                                                         |\n|                                                                                               |                                                                                                                                |       | $\\overline{OK}$<br>Cancel<br>Apply                                                                                                                                                                                                                                                                      |\n\n<span id=\"page-19-1\"></span>Figure 16: Reference Price Finding rules editor\n\nClick on the tier size label (e.g. 5m) to enable or disable a specific tier. In the example above the 25m tier is currently disabled.\n\nOccasionally a trader might want to switch temporarily all channels to best price without changing the entire Reference Price Finding configuration. This is typically the case when risk management mode is set to B2B.\n\nSelecting the option \"**All tiers best price and minimum quote size at least pricing tier size**\" allows to temporarily configure all outbound prices as suitable for B2B hedging with \"Fill or Kill\".\n\n# <span id=\"page-19-0\"></span>**3.11Managing Synthetic Crosses**\n\nTo define rules how to strip cross currency pairs, open the 'Global Instrument Configuration Dialog' and select option \"Cross Rules\":\n\n| <b>EXICO Global Instrument Configuration</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | $\\mathbf x$ |\n|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|\n| <b>Cross Rules</b><br><b>General Parameters</b><br>Instruments<br>Acti Base Ccy Quote  Cross<br><b>Use Notional Ccy</b><br><b>Tiers</b><br>$\\mathbf{u}$<br>Scenarios<br>$\\blacksquare$ USD<br><b>EUR</b><br>$\\overline{\\mathsf{v}}$<br>$\\mathbf{r}$ $\\mathbf{r}$<br>▼ EUR/USD<br>$\\uparrow$<br>$\\downarrow$<br>$\\ddotsc$<br>$\\triangleright$ Cross Rules<br>⊣<br>$\\overline{\\mathsf{v}}$<br>$T$ $1$ $$<br>$\\overline{\\phantom{a}}$ GBP<br>$\\blacktriangleright$ GBP/USD<br>$\\blacksquare$ GBP<br>ŵ,<br><b>Client Order Handling</b> |             |\n| QK<br>Cancel<br>Apply                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |             |\n\n<span id=\"page-19-2\"></span>Figure 17: Cross Currency Pair Configuration\n\nEach row in this dialog defines a rule how to handle a specific cross currency pair. New rules can be added by clicking on the \"+\" button in the right upper corner. The \"X\"-button is used to delete a rule.\n\nEach rule defines:\n\n- Active: if ticked, the crossing for the defined currency is active\n- Base Ccy and Quote Ccy: defines the currency or currency pair which should be cross-calculated using managed currency pairs. One of the two currencies can be defined with a wildcard \"\\*\"\n- Cross: defines the managed currency pair used to cross over either base or quote currency\n- Use Notional Ccy: if checked, the notional is specified by the quote currency quantity\n- Arrow buttons to move a rule up and down\n\n#### **Note:**\n\nRules will be matched from top down. Therefore wildcard rules shall be rather placed at the end of the list!\n\n#### <span id=\"page-20-0\"></span>**3.12Pricing of Unmanaged Instruments**\n\nBy default the MMC will only price managed instruments. Users can configure the MMC to price unmanaged instruments too. To do so, open the \"Global Instrument Configuration\" dialog, and select option \"General Parameters\":\n\n| <b>ESSEE Global Instrument Configuration</b>                                                                                  |                                                                                                                                                                                                                                                                      |\n|-------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| ♦ General Parameters<br>Instruments<br><b>Tiers</b><br><b>Scenarios</b><br><b>Cross Rules</b><br><b>Client Order Handling</b> | <b>General Parameters</b><br>⊠ Allow Time Slippage of $\\left  0.6 \\right $ sec for all Managed/Flow Hedged instruments<br>⊠ Allow Time Slippage of $\\left  0.6 \\right $ sec for all B2B instruments<br>Price Unmanaged Instruments<br>Price Unmanaged Cross Currency |\n|                                                                                                                               | OK<br>Cancel<br>Apply                                                                                                                                                                                                                                                |\n\n<span id=\"page-20-1\"></span>Figure 18: Pricing unmanaged instruments\n\nIf option \"Price Unmanaged Instruments\" is enabled, the MMC will provide prices, and execute client orders, for any instrument where liquidity is available.\n\nIf option \"Price Unmanaged Cross Currency\" is enabled, the MMC will provide prices, and execute client orders, for any synthetic cross rate where one of the two legs is not managed by the MMC.\n\nExample:\n\n- EUR/USD is a managed instruments\n- User configured a rule for synthetic cross rates for EUR/\\* cross over to EUR/USD\n\nIf option \"Price Unmanaged Cross Currency\" is enabled, the MMC will provide prices, and execute client orders, for any instrument with base currency EUR.\n\n#### **Note:**\n\nTo price unmanaged instruments, the entire bank basket will be used. Client orders for unmanaged instruments will be automatically hedged back-to-back.\n\n### <span id=\"page-21-0\"></span>**3.13Configuring Time Slippage**\n\nBy applying time slippage, the MMC has more time (if required) to fill a hedge order, which should improve the overall hedge order fill ratio. Time slippage is off by default. To configure time slippage, open the \"Global Instrument Configuration\" dialog, and select option \"General Parameters\":\n\n| <b>ESS</b> Global Instrument Configuration                                                                                  | Х                                                                                                                                                                                                                                                                |\n|-----------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| General Parameters<br><b>Instruments</b><br>Tiers<br><b>Scenarios</b><br><b>Cross Rules</b><br><b>Client Order Handling</b> | <b>General Parameters</b><br>Allow Time Slippage of $\\left  0.8 \\right $ sec for all Managed/Flow Hedged instruments<br>$\\Box$ Allow Time Slippage of $\\boxed{1.1}$ sec for all B2B instruments<br>Price Unmanaged Instruments<br>Price Unmanaged Cross Currency |\n|                                                                                                                             | <b>OK</b><br>Cancel<br>Apply                                                                                                                                                                                                                                     |\n\n<span id=\"page-21-2\"></span>Figure 19: Time slippage configuration\n\nTime slippage can be adjusted separately for B2B and flow hedged/managed risk managed mode in steps of 1/10th of a second. Time slippage will only be applied in case the hedge order cannot be filled immediately.\n\n#### <span id=\"page-21-1\"></span>**3.14Configuring Price Slippage**\n\nPrice slippage occurs, when a client order limit price is worse (from a trader perspective) than the currently published MMC outbound price. This problem typically occurs in fast moving markets, and/or with clients having a high latency between their end and the MMC pricing server.\n\nThe following image shows an example for slippage due to a widening market:\n\n- 1. Inbound price spread increased between time T and T+1\n- 2. Outbound bid and offer follow inbound bid and offer from time T to T+1\n- 3. Client hits at time T+1 the old quote from time T\n- 4. The MMC margin at time T+1 is in this case reduced by price slippage\n\n![](_page_22_Figure_2.jpeg)\n\n<span id=\"page-22-0\"></span>Figure 20: Example for price slippage\n\nWithout accepting price slippage there are two possible results in this situation:\n\n- 1. In case of B2B risk management mode, the client order will be most likely rejected, because no suitable quote to hedge the order can be found\n- 2. In case of flow hedge/managed modes, the MMC will simply reject the client order to fully protect the MMC margin\n\nBy accepting price slippage, traders can give clients (if required) a discount on their trader margin, to improve client order fill ratio, and reduce rejections.\n\nPrice slippage applies differently for B2B and flow hedge/managed risk management modes. In case of B2B mode, price slippage affects the B2B hedge order limit price. By pro-actively worsening the limit price, a hedge order has a higher chance of getting filled in the market. Price slippage will only be applied when necessary. The MMC will always try to execute a hedge order at the best available price (automatic price improvement).\n\nIn case of flow hedge and managed modes, price slippage controls if a client order will be accepted or rejected. It acts in this case as a price and profit protection. The price of any client order hitting the MMC will be compared to the current outbound price (in the example above the price at time T+1). Client orders will only be accepted, if their limit price is within the acceptable slippage as defined by the trader.\n\nTo configure price slippage for a specific instrument, open the \"Instrument Configuration\" dialog, and select option \"Pricing\". Price slippage can be applied for each pricing tier in column \"Slippage\". The options are either in percent of current margin, or in absolute PIPS.\n\n| Instrument Configuration - EUR/USD           |                                                |                                  | х                      |  |  |  |  |\n|----------------------------------------------|------------------------------------------------|----------------------------------|------------------------|--|--|--|--|\n| Pricing                                      | <b>Pricing.Core Channel 1</b>                  |                                  |                        |  |  |  |  |\n| Core Channel 1<br>Core Channel 2             | <b>Spread</b>                                  | <b>Skew</b>                      | <b>Slippage</b>        |  |  |  |  |\n| Reference Price Finding                      | M<br>$+$ Pips $-$<br><b>Min</b><br><b>Tier</b> | <b>Skew Factor</b><br><b>Max</b> | %                      |  |  |  |  |\n| Core Channel 1                               | $0.5 -$<br>$0.2 -$<br>1 <sub>m</sub>           | ≂<br>$0.2 -$                     | <b>Pips</b><br>8%      |  |  |  |  |\n| Core Channel 2                               | $0.7 -$<br>$0.5 -$<br>5 <sub>m</sub>           | M.<br>0.5                        | %<br>26%               |  |  |  |  |\n| Risk Management<br><b>General Parameters</b> | $1.0 -$<br>$0.8+$<br>10 <sub>m</sub>           | ŀ.<br>0.8                        | $20 -$<br>42%          |  |  |  |  |\n| <b>Position Rules</b>                        | $1.2 -$<br>0.0 <sub>1</sub><br>15m             | ŲΞ,<br>0.0                       | $40 -$<br>61%          |  |  |  |  |\n| <b>Pricing Rules</b>                         | $\\left  \\cdot \\right $<br>$0.0 -$<br>25m       | t¤<br>$0.0 -$                    | $0 -$<br>87%           |  |  |  |  |\n| Quote Filtering                              |                                                | and a straight and a straight    | 0%                     |  |  |  |  |\n|                                              | Allow Spread less than Market                  |                                  |                        |  |  |  |  |\n|                                              | Ⅳ Allow Skew to cross Mid Price                |                                  |                        |  |  |  |  |\n|                                              | Allow Skew to cross Opposite Side              |                                  |                        |  |  |  |  |\n|                                              | Allow Slippage Worse than Client Order Price   |                                  |                        |  |  |  |  |\n|                                              |                                                | <b>OK</b>                        | Cancel<br><b>Apply</b> |  |  |  |  |\n\n<span id=\"page-23-1\"></span>Figure 21: Price slippage configuration\n\nBy configuring slippage in PIPS, there is always the risk that the actual margin is less than the allowed price slippage. The MMC will automatically limit slippage at the current margin, to prevent traders from accidentally accepting client orders at a loss. This safety measure can be disabled by ticking option \"Allow Slippage Worse than Client Order Price\".\n\nConfiguring price slippage in percent means, a certain percentage of the current margin. If for example the current margin (difference between inbound and outbound price) is 2 PIPS, and the trader configures 50%, he would accept a price slippage of up to 1 PIP. Configuring a price slippage of 100% means, if necessary the trader is willing to give up the entire margin to fill or accept the client order.\n\nPrice slippage is set to zero, and \"allow slippage worse than client order price\" is not enabled, by default!\n\n#### <span id=\"page-23-0\"></span>**3.15Quote Filtering**\n\nProblems can arise if the MMC calculates an outbound price based on unreliable inbound quotes such as stale quotes. A quote can be considered as stale if it wasn't updated after the market moved. A B2B hedge order based on a stale quote will often get rejected.\n\nThe problem can become more severe with flow hedging and managed positions. In these modes the MMC will accept any client order within an acceptable price slippage range. A subsequent auto-hedge order might result into a financial loss!\n\nAnother issue with invalid quotes is revaluation of open positions. A user might set a stop loss rule for an open position. If such a rule is triggered by an invalid quote the position might be closed at an unfavourable price (because the MMC uses market orders to close positions).\n\nQuote filters can help to reduce such risks!\n\nTo enable quote filtering for a specific instrument open the \"Instrument Configuration\" dialog and select option \"Quote Filtering\":\n\n| х<br>Instrument Configuration - EUR/USD |                                  |  |  |  |  |  |  |  |  |\n|-----------------------------------------|----------------------------------|--|--|--|--|--|--|--|--|\n| Pricing                                 | <b>Quote Filtering</b>           |  |  |  |  |  |  |  |  |\n| Core Channel 1                          | 900 $\\div$ ms                    |  |  |  |  |  |  |  |  |\n| Core Channel 2                          | Remove quotes older than $100 +$ |  |  |  |  |  |  |  |  |\n| Reference Price Finding                 |                                  |  |  |  |  |  |  |  |  |\n| Core Channel 1                          |                                  |  |  |  |  |  |  |  |  |\n| Core Channel 2                          |                                  |  |  |  |  |  |  |  |  |\n| Risk Management                         |                                  |  |  |  |  |  |  |  |  |\n| <b>General Parameters</b>               |                                  |  |  |  |  |  |  |  |  |\n| <b>Position Rules</b>                   |                                  |  |  |  |  |  |  |  |  |\n| <b>Pricing Rules</b>                    |                                  |  |  |  |  |  |  |  |  |\n| ♦ Quote Filtering                       |                                  |  |  |  |  |  |  |  |  |\n|                                         |                                  |  |  |  |  |  |  |  |  |\n|                                         |                                  |  |  |  |  |  |  |  |  |\n|                                         |                                  |  |  |  |  |  |  |  |  |\n|                                         | QK<br>Cancel<br>Apply            |  |  |  |  |  |  |  |  |\n\n<span id=\"page-24-0\"></span>Figure 22: Quote filter settings\n\nIn example above any EUR/USD quote older than 1,000ms will be removed from the inbound stream. Removed quotes will be added back into the book at the next quote update.\n\nUsers can monitor the effect of the quote filter settings in the instrument \"Pricing Details\" dialog. To open this dialog press on the \"Details…\" link in the pricing monitor panel. Removed quotes will be displayed in the \"Raw Inbound\" view as strikethrough prices. See [4.3](#page-26-0) for more information about the Pricing Details.\n\n| $\\mathbf{x}$<br>Pricing Details - EUR/USD Core Channel 2       |                                                                                |                  |                 |                 |                  |                    |                 |  |  |  |  |\n|----------------------------------------------------------------|--------------------------------------------------------------------------------|------------------|-----------------|-----------------|------------------|--------------------|-----------------|--|--|--|--|\n| $\\triangle$ Raw Inbound<br><b>Filtered Inbound</b><br>Outbound | Raw Inbound<br>$1m$ $\\sim$<br>Tier:<br>Snapshot: Fri, 13 Nov 2015 08:03:47 UTC |                  |                 |                 |                  |                    |                 |  |  |  |  |\n|                                                                | <b>Bid</b><br><b>Ask</b>                                                       |                  |                 |                 |                  |                    |                 |  |  |  |  |\n|                                                                | <b>Provider</b>                                                                | Quantity         | <b>Price</b>    | <b>Price</b>    | Quantity         | Provider           |                 |  |  |  |  |\n|                                                                | CITIBANK.DEMO                                                                  | 1 <sub>m</sub>   |                 | 1.07771 1.07772 | 2.5m             | COBA.DEMO          |                 |  |  |  |  |\n|                                                                | CITIBANK.DEMO                                                                  | 2.5 <sub>m</sub> |                 | 1.07764 1.07778 | 5m               | COBA.DEMO          |                 |  |  |  |  |\n|                                                                | RBS.LND.DEMO                                                                   | 1 <sub>m</sub>   | 1.07761 1.07783 |                 | 1 <sub>m</sub>   | CITIBANK, DEMO     |                 |  |  |  |  |\n|                                                                | COBA.DEMO                                                                      | 2.5 <sub>m</sub> |                 | 1.07760 1.07790 | 2.5 <sub>m</sub> | CITIBANK.DEMO      |                 |  |  |  |  |\n|                                                                | Barclays BARX.DEMO                                                             | 5m               |                 | 1.07757 1.07793 | 5m               | Barclays BARX.DEMO |                 |  |  |  |  |\n|                                                                | COBA.DEMO                                                                      | 5m               | 1.07754 1.07828 |                 | 1 <sub>m</sub>   | <b>BOAL.DEMO</b>   |                 |  |  |  |  |\n|                                                                | <b>BOAL,DEMO</b>                                                               | 1 <sub>m</sub>   | 1.07727 1.07834 |                 | 2.5 <sub>m</sub> | <b>BOAL,DEMO</b>   |                 |  |  |  |  |\n|                                                                | <b>BOAL,DEMO</b>                                                               | 2.5 <sub>m</sub> |                 | 1.07721 1.07871 | 1m               | RBS.LND.DEMO       |                 |  |  |  |  |\n|                                                                |                                                                                |                  |                 |                 |                  |                    |                 |  |  |  |  |\n|                                                                |                                                                                |                  |                 |                 |                  | <b>Refresh</b>     | $\\overline{OK}$ |  |  |  |  |\n\n<span id=\"page-24-1\"></span>Figure 23: Monitor filtered quotes\n\n# <span id=\"page-25-0\"></span>**4 MONITORING PRICING**\n\n## <span id=\"page-25-1\"></span>**4.1 The Pricing Monitor Panel**\n\nThe Pricing Monitor tab provides various information for each managed instrument and pricing channel:\n\n- Net Position and P/L in company currency\n- Pricing tier size\n- Inbound price details\n- Outbound price details\n\nThe tab contains an emergency \"Stop All\" button. Clicking this button will stop pricing for **all** instruments and channels, independent who currently owns an instrument.\n\n|                                                                                                 | <b>Pricing Monitor</b> |     |            |            |                    |                                                                               |                |       |            |          | 圓   | $\\Theta$ Stop All |\n|-------------------------------------------------------------------------------------------------|------------------------|-----|------------|------------|--------------------|-------------------------------------------------------------------------------|----------------|-------|------------|----------|-----|-------------------|\n| Tier                                                                                            | Core Channel 1         |     |            |            |                    |                                                                               | Core Channel 2 |       |            |          |     |                   |\n|                                                                                                 | E AUD/USD              |     |            |            |                    | Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR            |                |       |            |          |     |                   |\n|                                                                                                 | <b>EUR/USD</b>         |     |            |            |                    | Net Position: 4m EUR P/L: Open -3,572 EUR / Realized 3,608 EUR / Total 37 EUR |                |       |            |          |     |                   |\n|                                                                                                 | Inbound                |     | Details    | Outbound   |                    | Details                                                                       | Inbound        |       | Details    | Outbound |     | Details           |\n| 1 <sub>m</sub>                                                                                  | 1.36628                |     | 1.36628    | 1.36620    | $0.2$ $\\sqrt{2.0}$ | 1.36640                                                                       | 1.36628        |       | 1.36628    | 1.36603  | 5.0 | 1.36653           |\n| 5 <sub>m</sub>                                                                                  | 1.36628                | 1.4 | 1.36642    | 1.36620    | $0.7 - 4.4$        | 1.36 664                                                                      | 1.36628        | 1.4   | 1.36642    | 1.36610  | 5.0 | 1.36660           |\n| 10 <sub>m</sub>                                                                                 | 1.36628                | 1.4 | 1.36642    | 1.36619    | 1.25.4             | 1.36673                                                                       | 1.36628        | 1.4   | 1.36642    | 1.36628  | 14  | 1.36642           |\n| 15 <sub>m</sub>                                                                                 | 1.36628                | 1.4 | 1.36642    | 1.36626    | $1.8 - 5.4$        | 1.36 680                                                                      | 1.36628        | 1.4   | 1.36642    | 1.36 628 | 14  | 1.36642           |\n| 25 <sub>m</sub>                                                                                 |                        |     |            |            |                    |                                                                               |                |       |            |          |     |                   |\n|                                                                                                 | GBP/USD                |     |            |            |                    | Net Position: 1.2m EUR P/L: Open 1,208 EUR / Realized 0 EUR / Total 1,208 EUR |                |       |            |          |     |                   |\n|                                                                                                 | Inbound                |     | Details    | Outbound   |                    | Details                                                                       | Inbound        |       | Details    | Outbound |     | Details           |\n| 2m                                                                                              | 1.67707                | 1.6 | $1.67$ 723 | $1.67$ 707 | 1.6                | $1.67$ 723                                                                    | 1.67707        | $1.2$ | 1.67719    | 1.67707  | 1.2 | 1.67719           |\n| 5 <sub>m</sub>                                                                                  | $1.67$ 707             | 1.6 | $1.67$ 723 | 1.67692    | 4.6                | 1.67738                                                                       | $1.67$ 707     | 1.4   | $1.67$ 721 | 1.67707  | 1.4 | $1.67$ 721        |\n| <b>E USD/INR</b><br>Net Position: 0 EUR P/L: Open 0 EUR / Realized 0 EUR / Total 0 EUR          |                        |     |            |            |                    |                                                                               |                |       |            |          |     |                   |\n| <b>E USD/JPY</b><br>Net Position: 2.2m EUR P/L: Open -22 EUR / Realized -29 EUR / Total -50 EUR |                        |     |            |            |                    |                                                                               |                |       |            |          |     |                   |\n|                                                                                                 |                        |     |            |            |                    |                                                                               |                |       |            |          |     |                   |\n|                                                                                                 |                        |     |            |            |                    |                                                                               |                |       |            |          |     |                   |\n|                                                                                                 |                        |     |            |            |                    |                                                                               |                |       |            |          |     |                   |\n\nEach row contains information for a specific instrument.\n\n<span id=\"page-25-3\"></span>Figure 24: Pricing Monitor Panel\n\nThe following columns are shown:\n\n- **Tier column**: Shows the tier size. Only configured tiers are shown.\n- **Inbound details:** Displays inbound price and spread details for the currently selected channel.\n- **Outbound details:** Displays outbound price, spread, and skew (marked with an arrow) details for the currently selected channel.\n\n# <span id=\"page-25-2\"></span>**4.2 Inbound and Outbound Pricing Tier Monitor**\n\nThe panel shows for each channel and tier detailed inbound and outbound price information:\n\n|                 | Inbound |     | Details        | Outbound | Details |          |\n|-----------------|---------|-----|----------------|----------|---------|----------|\n| 1 <sub>m</sub>  | 1.36648 | 0.9 | 1.36657        | 1.36640  | 0.22.9  | 1.36669  |\n| 5 <sub>m</sub>  | 1.36643 | 1.4 | 1.36657        | 1.36635  | 0.74.4  | 1.36679  |\n| 10 <sub>m</sub> | 1.36643 | 1.4 | 1.36657        | 1.36634  | 1.95.4  | 1.36 688 |\n| 15m             | 1.36643 | 1.4 | 1.36657        | 1.36641  | 1.895.4 | 1.36695  |\n| 25m             |         |     | $\\blacksquare$ |          |         |          |\n\n<span id=\"page-26-1\"></span>Figure 25: Pricing Tier monitor\n\nFor each currency pair multiple pricing channels can be configured. Each pricing channel can have different spread/skew settings and Reference Price Finding rules.\n\nThe assignment of your requesting clients to the different pricing channel prices has to be done by 360T support.\n\nThe pricing tier monitor shows one row per pricing tier (e.g. 1m, 5m, …).\n\n![](_page_26_Picture_7.jpeg)\n\nEach row shows the following information:\n\n- Tier size (e.g. 1m)\n- Big Figure\n- PIPS bid and ask\n- Spread blue bar\n- Skew orange number shows size and orange arrow shows direction\n\n#### <span id=\"page-26-0\"></span>**4.3 Pricing Details Dialog**\n\nClick on the \"Details…\" link to get a snapshot of current raw, inbound and outbound prices including all details.\n\nA user can select one of the following option in the navigation tree on the left side:\n\n- Raw Inbound to see the all available quotes from all liquidity and market data providers in the bank basket\n- Filtered Inbound quotes selected by Reference Price Finding\n- Outbound outbound rates for each pricing tier\n\nTo see all available quotes from all liquidity and market data provider in the basket click on \"Raw Inbound\". Bid quotes are shown on the left, ask quotes are shown on the right.\n\nBid quotes are sorted in descending order by price, with the best bid rate at the top. Ask quotes are sorted in ascending order by price, with the lowest ask rate at the top.\n\nAt the top of the panel a user can select a specific pricing tier size. Quotes are additionally marked in various colours, depending on the chosen Reference Price Finding settings. Quotes from selected providers are shown in black, all other quotes are shown in grey.\n\nThose quotes selected by Reference Price Finding for the actual outbound price are additionally marked in yellow.\n\n| PE Pricing Details - EUR/USD Core Channel 1                                                                                                                                                 |                           |                  |  |                 |                  |                           | $\\mathbf{x}$         |  |  |  |  |\n|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|------------------|--|-----------------|------------------|---------------------------|----------------------|--|--|--|--|\n| <b>Raw Inbound</b><br>$\\triangleright$ Raw Inbound<br><b>Filtered Inbound</b><br>1 <sub>m</sub><br>$\\overline{\\phantom{a}}$<br>Tier:<br>Outbound<br>Snapshot: Thu, 15 May 2014 13:14:28 UTC |                           |                  |  |                 |                  |                           |                      |  |  |  |  |\n|                                                                                                                                                                                             | <b>Bid</b><br><b>Ask</b>  |                  |  |                 |                  |                           |                      |  |  |  |  |\n|                                                                                                                                                                                             | <b>Provider</b>           | Quantity Price   |  | <b>Price</b>    | Quantity         | <b>Provider</b>           |                      |  |  |  |  |\n|                                                                                                                                                                                             | <b>Barclays BARX.DEMO</b> | 5m               |  | 1.36643 1.36657 | 5m               | <b>Barclays BARX.DEMO</b> |                      |  |  |  |  |\n|                                                                                                                                                                                             | RBS.LND.DEMO              | 2.5 <sub>m</sub> |  | 1.36643 1.36658 | 2.5 <sub>m</sub> | COBA.DEMO                 |                      |  |  |  |  |\n|                                                                                                                                                                                             | COBA.DEMO                 | 2.5 <sub>m</sub> |  | 1.36642 1.36658 | 1 <sub>m</sub>   | CITIBANK.DEMO             |                      |  |  |  |  |\n|                                                                                                                                                                                             | CITIBANK.DEMO             | 1 <sub>m</sub>   |  | 1.36642 1.36666 | 5 <sub>m</sub>   | COBA.DEMO                 |                      |  |  |  |  |\n|                                                                                                                                                                                             | COBA.DEMO                 | 5m               |  | 1.36634 1.36667 | 3.5 <sub>m</sub> | CITIBANK.DEMO             |                      |  |  |  |  |\n|                                                                                                                                                                                             | RBS.LND.DEMO              | 5 <sub>m</sub>   |  | 1.36634 1.36701 | 1 <sub>m</sub>   | <b>BOAL.DEMO</b>          |                      |  |  |  |  |\n|                                                                                                                                                                                             | CITIBANK.DEMO             | 3.5 <sub>m</sub> |  | 1.36633 1.36708 | 2.5 <sub>m</sub> | <b>BOAL.DEMO</b>          |                      |  |  |  |  |\n|                                                                                                                                                                                             | RBS.LND.DEMO              | 7.5 <sub>m</sub> |  | 1.36629 1.36757 | 2.5 <sub>m</sub> | RBS.LND.DEMO              |                      |  |  |  |  |\n|                                                                                                                                                                                             | <b>BOAL.DEMO</b>          | 1 <sub>m</sub>   |  | 1.36599 1.36766 | 5 <sub>m</sub>   | RBS.LND.DEMO              |                      |  |  |  |  |\n|                                                                                                                                                                                             | <b>BOAL.DEMO</b>          | 2.5 <sub>m</sub> |  | 1.36592 1.36771 | 7.5 <sub>m</sub> | RBS.LND.DEMO              |                      |  |  |  |  |\n|                                                                                                                                                                                             |                           |                  |  |                 |                  |                           | <b>Refresh</b><br>OK |  |  |  |  |\n\n<span id=\"page-27-0\"></span>Figure 26: Raw Inbound Quote Details\n\nClick on option \"Filtered inbound\" to see the actual quotes chosen by Reference Price Finding. The panel shows for each tier the selected quotes separated by bid and ask.\n\n| PE Pricing Details - EUR/USD Core Channel 1 |                                         |                                  |            |                          |                                  |                |  | $\\mathbf x$ |  |  |  |  |\n|---------------------------------------------|-----------------------------------------|----------------------------------|------------|--------------------------|----------------------------------|----------------|--|-------------|--|--|--|--|\n| Raw Inbound                                 | <b>Filtered Inbound</b>                 |                                  |            |                          |                                  |                |  |             |  |  |  |  |\n| ♦ Filtered Inbound<br>Outbound              | Snapshot: Thu, 15 May 2014 13:14:28 UTC |                                  |            |                          |                                  |                |  |             |  |  |  |  |\n|                                             | <b>Tier</b>                             | <b>Bid</b>                       | <b>Ask</b> |                          |                                  |                |  |             |  |  |  |  |\n|                                             | 1 <sub>m</sub>                          | 1.36643                          |            | 1.4                      | 1.36657                          |                |  |             |  |  |  |  |\n|                                             |                                         | Barclays BARX.DEMO: 1.36643 (5m) |            |                          | Barclays BARX.DEMO: 1.36657 (5m) |                |  |             |  |  |  |  |\n|                                             | 5 <sub>m</sub>                          | 1.36643                          |            | 1.4                      | 1.36657                          |                |  |             |  |  |  |  |\n|                                             |                                         | Barclays BARX.DEMO: 1.36643 (5m) |            |                          | Barclays BARX.DEMO: 1.36657 (5m) |                |  |             |  |  |  |  |\n|                                             | 10 <sub>m</sub>                         | 1.36643                          |            | 1.4                      | 1.36657                          |                |  |             |  |  |  |  |\n|                                             |                                         | Barclays BARX.DEMO: 1.36643 (5m) |            |                          | Barclays BARX.DEMO: 1.36657 (5m) |                |  |             |  |  |  |  |\n|                                             | 15 <sub>m</sub>                         | 1.36643                          |            | 1.4                      | 1.36657                          |                |  |             |  |  |  |  |\n|                                             |                                         | Barclays BARX.DEMO: 1.36643 (5m) |            |                          | Barclays BARX.DEMO: 1.36657 (5m) |                |  |             |  |  |  |  |\n|                                             |                                         |                                  |            | Market Best Bid: 1.36643 |                                  |                |  |             |  |  |  |  |\n|                                             |                                         |                                  |            |                          |                                  |                |  |             |  |  |  |  |\n|                                             |                                         |                                  |            |                          |                                  |                |  |             |  |  |  |  |\n|                                             |                                         |                                  |            |                          |                                  | <b>Refresh</b> |  | QK          |  |  |  |  |\n\n<span id=\"page-27-1\"></span>Figure 27: Filtered Inbound Quote Details\n\nClick on option \"Outbound\" to see outbound price details by tier:\n\n| PE Pricing Details - EUR/USD Core Channel 1     |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     | $\\mathbf{x}$ |\n|-------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|-----------------------------------------------------------------|--|-----|-----|---------|-----|--------------|\n| Raw Inbound                                     | Outbound                                                                                                                                              |  |  |  |  |                                                                 |  |     |     |         |     |              |\n| <b>Filtered Inbound</b><br>$\\triangle$ Outbound | Snapshot: Thu, 15 May 2014 13:14:28 UTC                                                                                                               |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 | <b>Inbound</b><br><b>Outbound</b><br><b>Spread</b><br><b>Skew</b><br><b>Tier</b><br>(Bid/Mid/Ask)<br>(Bid/Mid/Ask)<br>(Manual/Rule) (Manual/Rule/Cut) |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 | 1 <sub>m</sub>                                                                                                                                        |  |  |  |  | 1.36643 1.36650 1.36657 1.36636 1.36653 1.36670                 |  | 3.4 | 0.0 | 0.3     | 0.0 | 0.0          |\n|                                                 |                                                                                                                                                       |  |  |  |  | 5m   1.36643   1.36650   1.36657   1.36635   1.36657   1.36679  |  | 4.4 | 0.0 | 0.7     | 0.0 | 0.0          |\n|                                                 |                                                                                                                                                       |  |  |  |  | 10m   1,36643   1,36650   1,36657   1,36634   1,36661   1,36688 |  | 5.4 | 0.0 | 1.1     | 0.0 | 0.0          |\n|                                                 |                                                                                                                                                       |  |  |  |  | 15m 1.36643 1.36650 1.36657 1.36641 1.36668 1.36695             |  | 5.4 | 0.0 | 1.8     | 0.0 | 0.0          |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     | Refresh |     |              |\n|                                                 |                                                                                                                                                       |  |  |  |  |                                                                 |  |     |     |         |     | QK           |\n\n<span id=\"page-28-0\"></span>Figure 28: Outbound Price Details\n\nThe panel shows for each pricing tier:\n\n- Tier size\n- Inbound quotes\n- Outbound quotes\n- Manual spread due to configuration\n- Automatic spread due to pricing rules\n- Manual skew due to configuration\n- Automatic skew due to pricing rules\n- Skew cut because inbound price reached configured limits\n\n#### **Skew cut:**\n\nExample: bid / ask / mid: 0.9330 / 0.9331 / 0.93305\n\nUser sets a manual skew of +1 PIP\n\nIn this case the bid rate would cross the mid-rate with 0.5 PIPS\n\nIf bid or ask price aren't allowed to cross the mid-rate (the option \"Allow Skewing to cross Mid\" is not ticked), the price is cut off at the mid-price (in this case by 0.5 PIPS).\n\nNote:\n\nPressing the \"Refresh\" button will update the entire dialog with the latest market data snapshot.\n\n# <span id=\"page-29-0\"></span>**5 RISK MANAGEMENT**\n\nRisk Management enables the user to set the notional amount of risk he is prepared or allowed to hold on his own book as well as rules for the Auto Dealer once defined levels are reached.\n\n# <span id=\"page-29-1\"></span>**5.1 Monitoring Positions**\n\nThe Managed Position blotter shows currency pair positions for all managed currency pairs, and the trade history for each position in a separate table below. The trade history contains all requester orders and trades from hedge orders, making up the current position.\n\n| <b>Currency Pair Positions</b> |                           |                                             |  |              |                              |                       |  |                |                           |                                                                                         |                           | 圓                                                                                     |\n|--------------------------------|---------------------------|---------------------------------------------|--|--------------|------------------------------|-----------------------|--|----------------|---------------------------|-----------------------------------------------------------------------------------------|---------------------------|---------------------------------------------------------------------------------------|\n|                                |                           |                                             |  |              |                              |                       |  |                |                           | Total Net Position: 7.4m EUR P/L: Open -4,107 EUR / Realized 3,577 EUR / Total -530 EUR |                           |                                                                                       |\n| Symbol                         |                           |                                             |  |              |                              |                       |  |                |                           |                                                                                         |                           | Updated Size CCY1 Size CCY2 Open P/L CCY1 Open P/L CCY2 Open P/L Realized P/L CCY1 Re |\n| <b>EUR/USD</b>                 | 10-10-2                   |                                             |  | 1.00000      | $E$ $ACQ$ $22$               |                       |  |                | $-1,228.90$               |                                                                                         | $-1,680.00$ $-1,228.90$   | 3,606.23                                                                              |\n| USD/JPY                        |                           |                                             |  |              | Print Table Content Ctrl-P   |                       |  |                | $-6.254.55$               |                                                                                         | $-636,000.00$ $-4,575.12$ | $-39.34$                                                                              |\n| <b>AUD/USD</b>                 |                           | <b>Export to CSV</b>                        |  |              | Ctrl-E                       |                       |  |                | 0.00                      | 0.00                                                                                    | 0.00                      | 0                                                                                     |\n| <b>GBP/USD</b>                 |                           |                                             |  |              |                              |                       |  |                | 1,382.81                  |                                                                                         | 2,320.00 1,697.04         | 0.00                                                                                  |\n| USD/INR                        |                           | <sup>1</sup> Amend Position                 |  |              |                              | Ctrl+Shift-A          |  |                | 0.00                      | 0.00                                                                                    | 0.00                      |                                                                                       |\n|                                |                           | Set Position                                |  |              |                              | Ctrl+Shift-S          |  |                |                           |                                                                                         |                           |                                                                                       |\n|                                |                           | <b>EE</b> I <b>BESSIER</b> A Reset Position |  |              |                              | 羉<br>$Ctrl + Shift-R$ |  |                |                           |                                                                                         |                           |                                                                                       |\n| Trade                          | <b>坐 Elatten Position</b> |                                             |  | Ctrl+Shift-F |                              |                       |  |                | uantity Price Counterpart |                                                                                         |                           |                                                                                       |\n| PE-2708                        |                           |                                             |  |              |                              |                       |  | $000.0$ 1.3    |                           |                                                                                         |                           | Ш                                                                                     |\n| <b>PE-2710</b>                 |                           |                                             |  |              | Risk Management Ctrl+Shift-M |                       |  | $000, 0$ 1.3   |                           |                                                                                         |                           |                                                                                       |\n| PE-2711                        |                           |                                             |  |              |                              |                       |  | $ 000, 0 $ 1.3 |                           |                                                                                         |                           |                                                                                       |\n| $P_{E-270818}$                 |                           |                                             |  |              | 07:17:5 He PE-27 Sell        |                       |  | $1,000,0$ 1.3. |                           |                                                                                         |                           |                                                                                       |\n| $SO-196167$                    |                           | $ 07:17:5 $ Fill                            |  |              |                              | Sell                  |  |                |                           | 1,000,0 1.3 RBS.LND.D                                                                   |                           |                                                                                       |\n| PE-270806                      |                           |                                             |  |              | 07:17:5 RFS 360T.F Buy       |                       |  | $1,000,0$ 1.3. |                           |                                                                                         |                           |                                                                                       |\n| $P_{E-271098}$                 |                           |                                             |  |              | 12:15:2  He IPE-27           | Sell                  |  | $1,000,0$ 1.3. |                           |                                                                                         |                           |                                                                                       |\n| SO-196196 12:15:2 Fill         |                           |                                             |  |              |                              | Sell                  |  |                |                           | 1,000,0 1.3 RBS.LND.D                                                                   |                           |                                                                                       |\n| PE-270907                      |                           |                                             |  |              | 07:54:0 RFS 360TF Buy        |                       |  | 500,000 1.3    |                           |                                                                                         |                           |                                                                                       |\n\n<span id=\"page-29-2\"></span>Figure 29: Managed Positions blotter with context menu\n\n#### **Note:**\n\nPositions will be reset at the server side with every new trading day. The exact time is when the value date is rolled. Thus the trade history shows at most the requester order and hedge trades from the current day.\n\nThe trade history will be wiped/erased with **every manual position reset**!\n\nThe upper table shows currency pair position information for each managed instrument. The following columns are available:\n\n- Position size in CCY1 and CCY2\n- Time of last update\n- Open P/L in CCY1, CCY2, and company currency\n- Realized P/L in CCY1, CCY2, and company currency\n- Total P/L in CCY1, CCY2, and company currency\n- Revaluation rate (rate which was used to calculate open P/L)\n- Average buy and sell price\n\nColumn sorting can be changed by clicking a column and moving it left or right.\n\nTrade history for each position is shown in the lower table. Select a specific position in the upper table to see the trade history in the lower table. The table shows both client and hedge orders. For hedge orders additionally all executions are shown as children.\n\n#### **Trade history columns:**\n\n- Trade ID a unique ID for each trade\n- Created timestamp when the trade was created\n- Side buy or sell\n- Quantity order or trade quantity\n- Price requested or executed price\n- Counterparty liquidity provider who filled the hedge order\n- Type source system for requester orders (e.g. RFS, SEP) or hedge\n- Trigger originating user of the requester order, or hedge order trigger reason\n\n#### **Context menu:**\n\nRight clicking into the blotter data opens the context menu.\n\nFor instruments currently owned by the user this dialog offers position blotter specific options:\n\n- Amend Position: Select to amend the position for a specific quantity and price\n- Set Position: Select to set the position to a specific size and price\n- Reset Position: Reset the position to zero and erase the order and trade history\n- Flatten Position: Select to **execute a hedge order in the market** to close the position\n- Risk Management: Opens the Risk Management configuration dialog\n\n# <span id=\"page-30-0\"></span>**5.2 Profit and Loss (P/L) Calculations**\n\nOpen positions are periodically (every 500ms) revaluated mark-to-market with the best available inbound price to calculate **open P/L**. Long positions are revaluated with best bid price, short positions are revaluated with best ask price.\n\nP/L is calculated by one of these two methods:\n\n- **Average cost method**: This method calculates P/L by taking the difference of how much was spent to build the position, and how much was earned by closing the position\n- **First-In, First-out method (FIFO):** This method assumes that every SELL trade covers the oldest open BUY trade, and vice versa\n\nThe chosen P/L calculation method is a system setting and affects all users. By default the \"average cost\" method is used. **To configure your preferred P/L calculation method please contact 360T Support.**\n\n#### <span id=\"page-30-1\"></span>**5.3 Risk Management Configuration**\n\nTo configure risk management for a specific instrument select option \"Risk Management\" in the instrument configuration dialog:\n\n| <b>IEI</b> Instrument Configuration - EUR/USD                         |                                                                                                                                                                                                                                           | $\\mathbf x$          |  |  |  |  |  |  |  |  |  |\n|-----------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------|--|--|--|--|--|--|--|--|--|\n| Pricing                                                               | <b>Risk Management.Position Rules</b>                                                                                                                                                                                                     |                      |  |  |  |  |  |  |  |  |  |\n| Core Channel 1<br>Core Channel 2                                      | <b>Position Rules</b>                                                                                                                                                                                                                     | $\\ddot{}$            |  |  |  |  |  |  |  |  |  |\n| Reference Price Finding                                               | 10,000,000 Back2Back<br>$0 =$<br><b>V</b> Position<br>$\\mathbf{v}$ >= $\\mathbf{v}$<br>$\\uparrow$<br>▼<br>$\\perp$<br>$\\boldsymbol{\\times}$                                                                                                 |                      |  |  |  |  |  |  |  |  |  |\n| Core Channel 1<br>Core Channel 2                                      | $5,000$ - Flatten<br>$0$ $\\uparrow$<br>Open Loss<br>$\\  \\mathbf{v} \\ _{\\geq \\alpha}$<br>$\\uparrow$<br>$\\downarrow$<br>$\\mathbf{r}$                                                                                                        | $\\mathsf{x}$         |  |  |  |  |  |  |  |  |  |\n| Risk Management                                                       | 3,000 F Flatten<br>$0 = 1$<br>Open Profit<br>$\\vert \\mathbf{v} \\vert \\vert \\mathbf{v} = \\vert \\mathbf{v} \\vert$<br>▼                                                                                                                      | $\\parallel$ $\\times$ |  |  |  |  |  |  |  |  |  |\n| <b>General Parameters</b><br>♦ Position Rules<br><b>Pricing Rules</b> |                                                                                                                                                                                                                                           |                      |  |  |  |  |  |  |  |  |  |\n|                                                                       | B2B<br><b>C</b> Alert A Pos.<br>$\\mathbf{I}$                                                                                                                                                                                              |                      |  |  |  |  |  |  |  |  |  |\n|                                                                       | $\\circ$<br>$\\alpha$<br>0.2<br>0.1<br>0.2<br>0.8<br>$\\overline{\\mathbf{a}} \\cdot \\mathbf{e}$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>0.3<br>0.4<br>0.5<br>0.5<br>0.7<br>0.8<br>7.0<br>0.1<br>0.3<br>0.4<br>0.8<br>0.9<br>0.9<br>0.0 | $1.0 \\quad 1.1$      |  |  |  |  |  |  |  |  |  |\n|                                                                       | $\\overline{OK}$<br>Cancel                                                                                                                                                                                                                 | Apply                |  |  |  |  |  |  |  |  |  |\n\n<span id=\"page-31-1\"></span>Figure 30: Risk management position rules\n\nClick the + button in the upper right corner to add an additional rule. Rules are top down in priority order. Click arrow down / up, to change the order, x to cancel a rule.\n\n#### <span id=\"page-31-0\"></span>**5.4 Position Rules**\n\nThe following various rule types will be used to automatically manage position size. Position rules can be based on one of the following options (triggers) for the selected position:\n\n- **Position:** The absolute position size (long and short positions are treated equally)\n- **Total Loss:** The negative total P/L\n- **Open Profit:** The positive open P/L\n- **Open Loss:** The negative open P/L\n- **Open Profit (PIPS):** Positive open P/L as a price difference to average position price\n- **Open Loss (PIPS):** Negative open P/L as a price difference to average position price\n\nThe selected trigger is compared to a specified value with either greater (>), or greaterequals (>=).\n\nAs soon as a rule is triggered, the specified action will be executed. The available **actions** are:\n\n- **Back2Back:** The client order will only be accepted after it was successfully hedged (last look)\n- **Alert:** Raises an alert to the user when the trigger level is reached\n- **Reduce by:** Reduces the position by the specified amount\n- **Reduce by %:** Reduces the position by the specified percentage of its current size\n- **Reduce to:** Reduces the position to the specified size\n- **Flow hedge:** Hedge the client order immediately (no last look)\n- **Flatten:** Close the position\n- **Switch to Back2Back:** Switch the risk management mode to B2B for this instrument\n- **Switch to Flow Hedge**  switch the risk management mode to flow hedging for this instrument\n\nBack-to-Back (B2B) means that the requester order will only be accepted after it was successfully hedged (last look). A B2B rule which checks a certain position size is triggered when the current position amount, plus the notional of the requester order, would breach the specified limit.\n\nPosition rules are checked and possibly triggered:\n\n- Once with each client order\n- Periodically All rules are evaluated every 500ms\n\nWhen a position size rule is triggered, **it will emit a hedge order** to the market based on the specified rule action.\n\nThe periodic rules review ensures that positions are regularly reviewed even if no requester order is imminent. This is also a retry mechanism for failed or partially filled hedge orders. Instead of re-attempting the same failed hedge order, the system will simply compare positions against rules at the next periodic review.\n\n#### **Note:**\n\n**Even though rules are reviewed every 500ms, for safety reasons the system will wait (by default) at least 3 seconds between subsequent hedge orders for the same currency pair.** \n\n#### **Example flatten position:**\n\nA rule action could be Flatten. When this rule is triggered, a hedge order will be created to close the entire position in the market. If this hedge order fails or is only partially filled, another hedge order with the remaining position size will be created **latest after 3 seconds.** The hedge order will be created earlier, if there is a requester order for the same currency pair before the 3 seconds expired.\n\n#### **The auto-hedger will always make sure that:**\n\n- **Positions are not over hedged (flips sides because of a hedge order)**\n- **Position never increase because of a hedge order**\n\n#### **Flow Hedging:**\n\nAs the name suggests, flow hedging means to hedge the flow of requester order one-to-one. Every incoming requester order will be first executed and the position will be updated accordingly. When the updated position breaches a defined limit, and the specified action is Flow Hedge a reverse hedge order of the same size than the requester order will be emitted to the market.\n\nThe goal of flow hedging is to accept all requester orders, without letting positions grow (no guarantee).\n\n#### **Rule based risk management mode switching:**\n\nActions \"Switch to Back2Back\" and \"Switch to Flow hedge\" can be used to automatically switch the risk management mode based on certain criteria. An example is to switch to B2B mode when the total loss for a specific instrument breached a threshold, to avoid any further losses.\n\n#### **Note:**\n\nSwitching the risk management mode back to \"Managed\" is a manual operation to be performed in the Pricing Control tab!\n\n#### **Note:**\n\n- \"Reduce by\" shall be used with care. Every requester order is a single trigger. A position could grow very quickly if the specified \"reduce by\"- quantity is small compared to the requester order size!\n- Consider to use \"Reduce by\" and \"Reduce by %\" with a position size trigger. A P/L based rule can be triggered for any position size, and it is difficult to predict the position size when the rule fires.\n- Flow hedging is an option to avoid requester order rejections, and avoid position growth, but because flow hedging only **attempts** to hedge **after** the requester order was accepted, there is always the possibility that position still grows quickly!\n\n# **It is advisable to always specify a Back2Back rule to limit the maximum position size!**\n\n**Without such a rule, positions can potentially increase unreasonably result in a loss in short time!**\n\n# <span id=\"page-33-0\"></span>**5.5 Pricing Rules**\n\nPricing Rules define how to modify spreads and skew based on position size or P/L.\n\n| <b>Imaging Instrument Configuration - EUR/USD</b> |                                                                                                                                                                                                        | $\\mathbf x$ |  |  |  |  |  |  |  |  |  |  |\n|---------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|--|--|--|--|--|--|--|--|--|--|\n| Pricing                                           | <b>Risk Management.Pricing Rules</b>                                                                                                                                                                   |             |  |  |  |  |  |  |  |  |  |  |\n| Core Channel 1<br>Core Channel 2                  | <b>Pricing Rules</b>                                                                                                                                                                                   |             |  |  |  |  |  |  |  |  |  |  |\n| Reference Price Finding                           | 5,000,000 Skew%<br>$10 -$<br><b>V</b> Position<br>$\\vert \\mathbf{v} \\vert \\vert \\mathbf{v} = \\vert \\mathbf{v} \\vert$<br>$\\blacktriangledown$<br>T<br>$\\uparrow$<br>$\\boldsymbol{\\mathsf{x}}$           |             |  |  |  |  |  |  |  |  |  |  |\n| Core Channel 1<br>Core Channel 2                  | 8,000,000 Skew%<br>$20 -$<br><b>V</b> Position<br>$\\vert \\mathbf{v} \\vert \\vert \\mathbf{v} = \\vert \\mathbf{v} \\vert$<br>$\\overline{\\phantom{a}}$<br>$\\downarrow$<br>Ť<br>$\\boldsymbol{\\mathsf{x}}$     |             |  |  |  |  |  |  |  |  |  |  |\n| Risk Management<br><b>General Parameters</b>      | 10,000,000 = Skew%<br>$30 -$<br><b>V</b> Position<br>$\\  \\cdot \\ $ > = $\\ $<br>$\\overline{\\phantom{a}}$<br>T<br>$\\mathsf{x}$                                                                           |             |  |  |  |  |  |  |  |  |  |  |\n| <b>Position Rules</b>                             | 10,000 = Spread% -<br>$20 -$<br><b>V</b> Total Loss<br>$\\  \\cdot \\ $ $>$ $=$ $\\  \\cdot \\ $<br>$\\downarrow$<br>$\\uparrow$<br>$\\boldsymbol{\\times}$                                                      |             |  |  |  |  |  |  |  |  |  |  |\n| ♦ Pricing Rules                                   |                                                                                                                                                                                                        |             |  |  |  |  |  |  |  |  |  |  |\n|                                                   |                                                                                                                                                                                                        |             |  |  |  |  |  |  |  |  |  |  |\n|                                                   |                                                                                                                                                                                                        |             |  |  |  |  |  |  |  |  |  |  |\n|                                                   | Skew % -Spread %<br>30                                                                                                                                                                                 |             |  |  |  |  |  |  |  |  |  |  |\n|                                                   | 25<br>20                                                                                                                                                                                               |             |  |  |  |  |  |  |  |  |  |  |\n|                                                   | 15<br>10<br>5                                                                                                                                                                                          |             |  |  |  |  |  |  |  |  |  |  |\n|                                                   | $\\alpha$<br>0.5<br>2.0<br>2.5<br>3.0<br>3.5<br>6.5<br>9.0<br>0.0<br>1.0<br>1.5<br>4.0<br>4.5<br>5.0<br>5.5<br>6.0<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>7.5<br>8.0<br>8.5<br>9.5<br>10.0 10.5 |             |  |  |  |  |  |  |  |  |  |  |\n|                                                   | QK<br>Cancel<br>Apply                                                                                                                                                                                  |             |  |  |  |  |  |  |  |  |  |  |\n\n<span id=\"page-33-1\"></span>Figure 31: Risk management pricing rules\n\nSpread and skew due to such rules is added on top of spread and skew configured in the Pricing Controller tab.\n\nThe chart shows the effective \"skew curve\" as defined by the rules.\n\n# <span id=\"page-34-0\"></span>**5.6 Alert Rules**\n\nWithin position rules, a user can create Alert rules to receive a notification when a position size or P/L reaches a certain limit.\n\nWhen an Alert level is reached, the user receives a pop up notification **and** an acoustic signal.\n\n| <b>IEI</b> Message                                                                                  |                              |\n|-----------------------------------------------------------------------------------------------------|------------------------------|\n|                                                                                                     | =<br><b>TRADING NETWORKS</b> |\n| INSTRUMENT ALERT for EURUSD SPOT<br>2014-02-28 11:23:43: OPEN POSITION ALERT [3m] rule was disabled |                              |\n|                                                                                                     | ок                           |\n\n<span id=\"page-34-2\"></span>Figure 32 Alert popup message\n\n#### **Note:**\n\nWhen you acknowledge an Alert, the respective rule will be disabled. **You have to manage the position and thereafter tick the rule again and apply it!**\n\n# <span id=\"page-34-1\"></span>**5.7 Manual Position Amendments**\n\nPositions can be manually amended for each managed currency pair. To do so, select one of the following options in the position blotter context menu.\n\n| F Expand All                 |              |\n|------------------------------|--------------|\n| I⊟ Collapse All              |              |\n| Print Table Content          | Ctrl-P       |\n| <b>Export to CSV</b>         | Ctrl-E       |\n| Amend Position               | Ctrl+Shift-A |\n| Set Position                 | Ctrl+Shift-S |\n| <b>Reset Position</b>        | Ctrl+Shift-R |\n| <b>V</b> Flatten Position    | Ctrl+Shift-F |\n| Risk Management Ctrl+Shift-M |              |\n\n<span id=\"page-34-3\"></span>Figure 33 Context menu of Managed Positions\n\n|                | Ж<br>PE Amend Position for USD/HKD |\n|----------------|------------------------------------|\n|                | <b>TRADING NETWORKS</b>            |\n| <b>Amount:</b> |                                    |\n| Price:         | 0.0000                             |\n|                | Cancel<br>OK                       |\n\n<span id=\"page-34-4\"></span>Figure 34 Amend Position\n\n**Amend** a position: Enter a quantity and price and click the \"OK\" button. This will add the specified quantity to the position and impact the P/L based on the captured price. To reduce the position, enter a negative quantity.\n\n![](_page_35_Picture_2.jpeg)\n\nFigure 35 Set Position\n\n<span id=\"page-35-1\"></span>**Set** a position: Enter a quantity and price and click the \"OK\" button. This will set the position to the specified position size and impact the P/L based on the captured price.\n\n**Reset** a position: Choose the Reset command and the entire position will be erased and reset to zero. A message is displayed in order to request a confirmation for the reset before it is executed.\n\n| <b>PE Confirm Reset Position</b>                                                                                                                  | 22               |\n|---------------------------------------------------------------------------------------------------------------------------------------------------|------------------|\n|                                                                                                                                                   | NG NETWOR        |\n| Please confirm that you want to reset your USD/HKD position. The entire position information<br>including order and trade history will be purged. |                  |\n|                                                                                                                                                   | Yes<br><b>No</b> |\n\n<span id=\"page-35-2\"></span>Figure 36 Confirmation of Position Reset\n\nNone of the above actions will create \"real\" deals with the market!\n\n**Flatten** a position: Choose the Flatten command to create a hedge order to close the position IN THE MARKET. This action will create real deals with external market makers.\n\n| <b>PE</b> Confirm Flatten Position                                                        | 2.                      |\n|-------------------------------------------------------------------------------------------|-------------------------|\n|                                                                                           | <b>TRADING NETWORKS</b> |\n| Please confirm that you want to flatten your USD/HKD position by executing a hedge order. |                         |\n|                                                                                           | <b>Yes</b><br>No        |\n\n<span id=\"page-35-3\"></span>Figure 37 Confirmation of Position Flattening\n\n#### **Note:**\n\nFlatten will attempt to execute your Notional position out in the market back to zero, you will be asked to confirm this action and if it is unable to bring it to zero, you will receive a notification.\n\n#### <span id=\"page-35-0\"></span>**5.8 Auto-Hedging Safeguards**\n\nThe MMC has built in certain so called \"Safeguards\" to limit the risk of eventual losses in case of technical problems. Certain safeguard parameters can be adjusted by users.\n\n#### **Maximum hedge order size:**\n\nThe maximum hedge order size limits the order quantity of auto-hedge orders. The default value is 10 million in base currency. Users can adjust this value individually for each managed instrument. The upper limit for this parameter is 50 million.\n\nTo adjust the maximum hedge order size or a specific managed instrument, open the instrument configuration dialog, and select option \"General Parameters\":\n\n| х<br>Instrument Configuration - EUR/USD   |                                                |  |\n|-------------------------------------------|------------------------------------------------|--|\n| Pricing                                   | <b>Risk Management.General Parameters</b>      |  |\n| Core Channel 1<br>Core Channel 2          | 10,000,000<br><b>Maximum Hedge Order Size:</b> |  |\n| Reference Price Finding<br>Core Channel 1 |                                                |  |\n| Core Channel 2                            |                                                |  |\n| Risk Management<br>General Parameters     |                                                |  |\n| <b>Position Rules</b>                     |                                                |  |\n| <b>Pricing Rules</b>                      |                                                |  |\n| Quote Filtering                           |                                                |  |\n|                                           |                                                |  |\n|                                           | QK<br>Cancel<br>Apply                          |  |\n\n<span id=\"page-36-1\"></span>Figure 38 Maximum Hedge Order Size Configuration\n\nIndependent of position size and auto-hedging rules, no auto-hedge order will be larger than this value.\n\nExample:\n\n- The maximum hedge order size is set to 10 million\n- A user defines a position rule to flatten the position if it is larger than 20 million\n- The position reaches 22 million\n\nThe MMC will create a first hedge order over 10 million. Assuming the hedge order got fully filled, the position will go down to 12 million. At the next position review, the position rule will trigger again, and a second hedge order over 10 million will be created.\n\nNote:\n\nThis parameter only applies to instruments with risk management mode \"Managed\". This parameter has no effect on back-to-back or flow hedge orders.\n\n#### <span id=\"page-36-0\"></span>**5.9 Restrict the bank basket for hedge orders**\n\nBy default, when the MMC places hedge orders, quotes of the entire client's bank basket are considered. Users can restrict the bank basket for B2B and flow hedge orders to make sure hedge orders are only sent to selected providers.\n\nTo restrict the hedge order bank basket for a specific instrument, open the \"Instrument Configuration\" dialog, and select option \"Reference Price Finding\".\n\n| <b>EUR/USD</b> Instrument Configuration - EUR/USD  |                                                                                                                                                   |                         | $\\mathbf x$                                                                                          |  |  |\n|----------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|------------------------------------------------------------------------------------------------------|--|--|\n| Pricing                                            | Reference Price Finding.Core Channel 1                                                                                                            |                         |                                                                                                      |  |  |\n| Core Channel 1<br>Core Channel 2                   | <b>Providers</b><br><b>Tier</b>                                                                                                                   | <b>Hedge</b>            | <b>Strategy</b>                                                                                      |  |  |\n| Reference Price Finding<br>Core Channel 1          | BOAL.DEMO; Barclays ▼<br>1m                                                                                                                       |                         | Min. Quote Size Min. Providers<br>1,000,000<br>$ALL$ $\\rightarrow$<br><b>Best Price</b>              |  |  |\n| Core Channel 2<br>Risk Management                  | BOAL.DEMO; Barclays ▼<br>5 <sub>m</sub>                                                                                                           | $\\overline{\\mathsf{v}}$ | Min. Quote Size Min. Providers<br>5,000,000<br>ALL<br><b>Best Price</b>                              |  |  |\n| <b>General Parameters</b><br><b>Position Rules</b> | <b>Barclays BARX.DEMO</b><br>10 <sub>m</sub>                                                                                                      | 罓                       | Min. Quote Size Min. Providers<br>10,000,000<br>ALL⊢<br><b>Best Price</b>                            |  |  |\n| <b>Pricing Rules</b><br>Quote Filtering            | CITIBANK.DEMO<br>15 <sub>m</sub>                                                                                                                  |                         | Min. Quote Size Min. Providers<br>15,000,000<br>$ALL \\rightleftharpoons$<br><b>Best Price</b>        |  |  |\n|                                                    | 25m                                                                                                                                               |                         | Min. Quote Size Min. Providers<br>$25,000,000 \\rightarrow$<br>$ALL \\rightarrow$<br><b>Best Price</b> |  |  |\n|                                                    | <b>Override</b><br>All tiers best price and minimum quote size at least tier size<br>Allow VWAP price calculation for less than required quantity |                         |                                                                                                      |  |  |\n|                                                    |                                                                                                                                                   |                         | OK<br>Cancel<br><b>Apply</b>                                                                         |  |  |\n\n<span id=\"page-37-1\"></span>Figure 39: Restrict hedge order bank basket\n\nTo restrict the hedge order bank basket for a specific pricing tier, tick the checkbox in column \"Hedge\". Any client order hitting this pricing tier, will be hedged with the selected providers for the same tier.\n\n#### <span id=\"page-37-0\"></span>**5.10Client Order Handling Rules**\n\nClient Order Handling rules allow users to setup rules to override the instrument's risk management mode for individual client orders. Users can setup an arbitrary number of such client order handling rules. The configuration can be instrument specific but also across multiple or all instruments using wildcards.\n\nExamples for client order handling rules:\n\n- All client orders for EUR/USD up to 1m quantity shall be managed on the position, but all orders larger than 1m shall be flow hedged.\n- Pricing for instrument GBP/USD is configured for up to 10m with risk management mode \"Flow Hedging\". All client orders larger than 5m from counterparty \"ABC Ltd.\" shall be hedged back-to-back.\n\nA client order handling rule consists of client order matching parameters, and an action. The action defines what to do with the client order if the rule matches. If more than one rule is defined, only the first matching rule will be executed!\n\nParameters to match client orders are:\n\n- **Instrument** … either exactly (e.g. EUR/USD) or defined with wildcards (e.g. EUR/\\*)\n- **Order quantity** and comparison operator (bigger, bigger equals, equals, smaller equals, smaller)\n- **Counterparty** and comparison operator to match exactly (e.g. equals \"ABC Ltd.\") but also pattern matching (all counterparties starting with \"Bank\").\n\nPossible actions are:\n\n- Hedge B2B\n- Flow Hedge\n- Reject (to reject a client order)\n\nA client order handling rule can **override** the current risk management mode of an instrument, but only **towards less risk**! As an example, if the current risk management mode is \"Flow Hedge\", a client order processing rule can only force order handling with \"B2B\" or \"Reject\" but not \"Managed\".\n\nThe following table provides an overview of possible actions for each instrument risk management mode:\n\n| Instr. risk management mode | Managed | Flow Hedge | B2B | Reject |\n|-----------------------------|---------|------------|-----|--------|\n| Managed                     | Yes     | Yes        | Yes | Yes    |\n| Flow Hedge                  | No      | Yes        | Yes | Yes    |\n| B2B                         | No      | No         | Yes | Yes    |\n\nClient order handling rules where the action is identical with the current risk management mode will be matched, but have no effect!\n\nTo configure client order handling rules, open the \"Global Instrument Configuration\" dialog and select option \"Client Order Handling\". Click on the \"+\" button to add new rules:\n\n| <b>ESS</b> Global Instrument Configuration |                                                                           |                                                 |                                             |                                  |                                        | $\\mathbf x$    |  |\n|--------------------------------------------|---------------------------------------------------------------------------|-------------------------------------------------|---------------------------------------------|----------------------------------|----------------------------------------|----------------|--|\n| <b>General Parameters</b><br>Instruments   | <b>Client Order Handling</b>                                              |                                                 |                                             |                                  |                                        |                |  |\n|                                            | Instrument                                                                | Quantity                                        | Counterparty                                | Action                           | $+$                                    | <b>Test</b>    |  |\n| <b>Tiers</b><br>Scenarios                  | $\\overline{\\phantom{a}}$<br>$\\overline{\\mathsf{v}}$<br>ŵ.<br>$\\mathbf{v}$ | $\\geq$ $=$ $\\neq$                               | 2,000,000 Ends With v Fund                  | Reject<br>$\\blacktriangledown$   | $\\uparrow$<br>$\\downarrow$             | $\\pmb{\\times}$ |  |\n| <b>Cross Rules</b>                         | $\\mathbf{v}$ USD $\\mathbf{v}$<br>$\\overline{\\mathsf{v}}$<br>ŵ.            | $\\overline{\\phantom{a}}$                        | 0 Exactly as $\\blacktriangleright$ Abc Corp | B <sub>2</sub> B<br>$\\mathbf{r}$ | $\\uparrow$<br>$\\downarrow$             | $\\times$       |  |\n| Client Order Handling                      | $\\bullet$ USD $\\bullet$<br>$\\overline{\\mathsf{v}}$<br>EUR                 | 3,000,000<br>$\\overline{\\phantom{a}}$<br>$\\geq$ | $\\overline{\\phantom{a}}$                    | Flow Hed • 1                     | $\\downarrow$                           | $\\mathbf{x}$   |  |\n|                                            | $\\overline{\\mathsf{v}}$<br>$\\mathbf{v}$ $\\mathbf{x}$<br>ŵ.<br>▾           | 10,000,000<br>▼                                 | ▾⊩                                          | B <sub>2</sub> B<br>▾            | $\\uparrow$<br>$\\vert \\downarrow \\vert$ | $\\mathbf{x}$   |  |\n|                                            |                                                                           |                                                 |                                             |                                  |                                        |                |  |\n|                                            |                                                                           |                                                 |                                             | QK                               | Cancel                                 | Apply          |  |\n\n<span id=\"page-38-0\"></span>Figure 40: Manage Client Order Handling Rules\n\nRules are always evaluated for a match starting at the top of the list towards the bottom. The first matching rule will be executed, all remaining rules will be ignored.\n\nExplanation for the examples above:\n\nRule 1: Any client order bigger or equals 2m from a counterparty ending with \"Fund\" will be rejected\n\nRule 2: Any client order with terms currency USD from \"Abc Corp\" will be hedged B2B\n\nRule 3: Any EUR/USD client order with quantity bigger than 3m will be flow hedged\n\nRule 4: Any client order with quantity bigger or equals 10m will be hedged B2B\n\nIt is important to order rules in the right way! More restrictive rules should be placed before more generic rules. An example are rules 2 and 3. If rule 3 would be placed before rule 2, orders from \"Abc Corp\" bigger than 3m would be flow hedged! Assuming a user wants to hedge any order \\*/USD from \"Abc Corp\" B2B, it is important to make sure that a more generic rule doesn't override it.\n\n#### **Counterparty comparison operators:**\n\nThe system offers a number of comparison operators to match counterparties in various ways:\n\n| Operator    | Explanation                                                   |\n|-------------|---------------------------------------------------------------|\n| Exactly as  | Compares for an exact match of the specified counterparty     |\n| All except  | Matches all counterparties except the specified one           |\n| Contains    | Matches any counterparty which contains the specified text    |\n| Starts With | Matches any counterparty which starts with the specified text |\n| Ends With   | Matches any counterparty which ends with the specified text   |\n| All like    | Complex pattern search using Java regular<br>expressions      |\n\n#### **Counterparty pattern matching:**\n\nThe provided standard counterparty comparison operators should be sufficient for most cases to match either a specific counterparty, or a group of counterparties. Operator \"All like\" provides an alternative in cases where the standard operators don't help. This operator uses so called Java regular expressions to match counterparties. Such regular expressions provide extreme flexibility but can be eventually complex to define.\n\nThe general syntax for Java regular expression can be found in the appendix chapter [8.1.](#page-45-2) Further information about Java regular expressions can be found in the internet in the official Java documentation (https://docs.oracle.com/javase/tutorial/essential/regex/).\n\n#### **Rule testing:**\n\nRules can be tested without any risk with a dedicated testing dialog. Click on button \"Test\" to open the \"Client Order Handling Testing\" dialog:\n\n| <b>MAC</b>                           | $\\mathbf x$                   |  |  |  |  |\n|--------------------------------------|-------------------------------|--|--|--|--|\n| <b>Client Order Handling Testing</b> |                               |  |  |  |  |\n| <b>Client Order Parameters</b>       |                               |  |  |  |  |\n| Instrument                           | <b>USD</b><br><b>EUR</b><br>▼ |  |  |  |  |\n| Quantity                             | 3,000,000                     |  |  |  |  |\n| Counterparty                         | <b>Test Comp</b>              |  |  |  |  |\n| <b>Instrument Settings</b>           |                               |  |  |  |  |\n| Risk Management                      | Managed                       |  |  |  |  |\n| <b>Test Results</b>                  |                               |  |  |  |  |\n| <b>Rule Result</b>                   | <b>Flow Hedge</b>             |  |  |  |  |\n| <b>Effective action</b>              | Flow Hedge                    |  |  |  |  |\n|                                      | <b>Test</b><br>Close          |  |  |  |  |\n|                                      |                               |  |  |  |  |\n\n<span id=\"page-39-0\"></span>Figure 41: Client Order Handling Rule testing\n\nThis dialog can be used to simulate client orders to verify if any of the defined rules will match. To test the current set of rules, specify a currency pair, order quantity, counterparty, and press on the \"Test\" button. The action of the first matching rule will be displayed in the \"Rule Result\" field. If no matching rule can be found \"No match\" will be displayed. New rules and changes can be tested without applying the change!\n\nAs mentioned earlier, a client order handling rule can only override the instrument's risk management mode towards less risk. In other words, a rule defines an action, but the effective action will be determined in combination with the instrument's risk management mode. This behavior can be simulated in the rule testing dialog too by selecting a risk management mode. The effective action will be either the result of a matching rule, or the current instrument's risk management mode.\n\n#### <span id=\"page-40-0\"></span>**5.11Pricing and Risk Management Scenarios**\n\nPricing and Risk management scenarios (or short scenarios) allow users to quickly adjust pricing and risk management settings with one mouse click. Users can define an arbitrary number of such scenarios.\n\nEach scenario defines:\n\n- Name\n- Spread factor\n- Maximum quote size\n- Risk management mode\n\nOnly the name is mandatory, all other parameters are optional.\n\nIf set, parameters influence pricing and risk management in various ways. The current outbound spread will be widened by the selected spread factor. A spread factor of e.g. 2, will widen the outbound spread by 100%. Maximum quote size can be used to limit the available liquidity for your requesters. If e.g. 5m maximum quote size is chosen, any pricing tier above 5m will be disabled. The selected risk management mode of the scenario will override the current instrument's risk management mode, but only towards less risk. If e.g. the current instruments risk management mode is \"B2B\" and the scenario defines \"Flow Hedge\", the parameter will be ignored.\n\nScenarios are defined globally and can be applied to any managed instrument. Scenarios override current settings only temporarily as long as the scenario is applied.\n\nTo add a new scenario open the \"Global Instrument Configuration\" dialog and select option \"Scenarios\":\n\n| Global Instrument Configuration |                   |                   |               |                                    | $\\mathbf x$  |  |\n|---------------------------------|-------------------|-------------------|---------------|------------------------------------|--------------|--|\n| <b>General Parameters</b>       | <b>Scenarios</b>  |                   |               |                                    |              |  |\n| Instruments<br><b>Tiers</b>     | <b>Name</b>       | <b>Spread Fac</b> | Max. Quot     | Risk Managem                       | $^{+}$       |  |\n| $\\triangle$ Scenarios           | <b>Moderate</b>   | $1.5 -$           | Undefined     | ▼                                  | X            |  |\n| <b>Cross Rules</b>              | <b>Volatile</b>   | $2\\div$           | 5,000,000     | Flow Hedge<br>$\\blacktriangledown$ | $\\mathbf{x}$ |  |\n| <b>Client Order Handling</b>    | <b>Unattended</b> | $1.0 -$           | $3,000,000 -$ | $\\blacksquare$<br>B <sub>2</sub> B | $\\mathbf x$  |  |\n|                                 |                   |                   |               |                                    |              |  |\n|                                 |                   |                   | QK            | Cancel                             | Apply        |  |\n\n<span id=\"page-41-0\"></span>Figure 42: Pricing and Risk Management Scenarios\n\nScenarios can be applied to instruments in the \"Pricing Control\" panel.\n\n| <b>Pricing Control</b> Trade Blotter     |             |                 |                                          |  |                          |               |                  |          |  |\n|------------------------------------------|-------------|-----------------|------------------------------------------|--|--------------------------|---------------|------------------|----------|--|\n| Instrument/Channel $\\mathbb{A}^1$ Status |             | Managed         | Control                                  |  |                          |               |                  |          |  |\n|                                          |             | By              | <b>Start</b>                             |  | Stop Ownership Configure |               | Mode             | Scenario |  |\n| <b>All Instruments</b>                   | $\\circ$     |                 | U                                        |  |                          | 0<br>$\\cdots$ | Change           | Change   |  |\n| I ⊞ EUR/USD                              | $\\mathbf O$ | PEEMEA1.Trader1 | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |  | Take                     | $\\bullet$     | B <sub>2</sub> B | Moderate |  |\n| I⊞ GBP/USD                               | O           | PFFMFA1.Trader1 |                                          |  | Take                     | $\\bullet$     | Flow Hedge       | Volatile |  |\n| I⊞ USD/CAD                               | O           | PEEMEA1.Trader1 | $\\bigcirc$                               |  | Take                     | Ô.,           | Managed          |          |  |\n\n<span id=\"page-41-1\"></span>Figure 43: Scenario selection\n\nIf not visible at startup, the column \"Scenario\" can be added by right clicking on the panel header and selecting the option \"Choose Columns …\".\n\nThe current scenario can be changed for all instruments simultaneously by selecting a scenario in the \"All Instruments\" row.\n\n# <span id=\"page-42-0\"></span>**6 BLOTTERS**\n\nThe MMC offers various blotters to monitor orders, trade flow, and positions. The blotter currently available are:\n\n- Currency Pair Position Blotter\n- Client Order Blotter\n- Hedge Order Blotter\n- Combined Client and Hedge Order Blotter\n- Client Activity Blotter\n- Trade Blotter\n\n## <span id=\"page-42-1\"></span>**6.1 General Blotter Features**\n\nAll blotters offer the same general set of features which are:\n\n- Choose, move, and resize columns\n- Sorting by one or more columns\n- Filters\n- Save and load settings\n- Print and export blotter data\n\nColumns can be resized simply by moving the boundary between two columns left or right. The columns order can be changed via drag and drop.\n\nTo move a column, click with the left mouse button on the column header and hold it. Move the column to the next target location and release the mouse button.\n\nTo show and hide columns, right click with the mouse on the column headers, and select \"Choose Columns …\" from the popup menu.\n\nChanges can be reverted back **to default** by right clicking on the column headers, and select \"Load Default Settings\" from the popup menu.\n\nBlotter data can be exported to CSV by right clicking into the blotter data, and selecting \"Export to CSV\". Similar applies to printing blotter data.\n\nBlotter data can be sorted by one or more columns, in ascending or descending order, by clicking one or more time on the according column header. To sort by more than one column, press and hold the CTRL key on your keyboard, and click on the specific column headers. A small triangle in the column header will show the sort direction. The small number next to the triangle indicates the sort order if more than one sort column is selected.\n\n#### <span id=\"page-42-2\"></span>**6.2 Client Order Blotter**\n\nThe client order blotter shows every client order received by the pricing engine. For each client order the following information is available:\n\n- Order ID\n- Time of arrival\n- Currency pair\n- Status (e.g. EXECUTED, REJECTED, ..)\n- Action (BUY or SELL)\n- Order limit price\n- Notional amount and currency\n- Executed amount and rate\n\n- Type (originating system like RFS or SEP)\n- Trigger (originating organization and user)\n- Fill or Kill flag (FoK)\n- Realized By ID filled for B2B orders\n- Requester\n- Requester Institution\n\nThe blotter is pre-filled at startup with orders from the current trading day.\n\nThe font color for each entry changes with the execution status:\n\n- Green order was successfully executed\n- Red order was rejected or failed\n\n#### **Note:**\n\n- Order action (buy or sell) is shown from the perspective of the requester (a buy order means, the requester bought and the ADS MMC sold)!\n- In case of a cross rate strip order, the blotter will show three requester orders. The actual cross rate order, and two additional orders for each leg.\n\n# <span id=\"page-43-0\"></span>**6.3 Hedge Order Blotter**\n\nThe hedge order blotter shows an entry for each hedge order created for the current trading day. For each hedge order the following information is available:\n\n- Hedge Order ID\n- Time of creation\n- Currency Pair\n- Status\n- Action (BUY or SELL)\n- Order limit price\n- Order notional amount and quantity\n- Executed amount and for which average price\n- Trigger indication why the hedge order was created\n- Fill or Kill order flag\n\nThe blotter is pre-filled at startup with orders from the current trading day.\n\nThe font color for each entry changes with the execution status:\n\n- Green order was successfully executed\n- Red order was rejected or failed\n\n# <span id=\"page-43-1\"></span>**6.4 Combined Client and Hedge Order Blotter**\n\nBlotter with title \"All orders\" combines client and hedge orders into a single blotter. The two different order types can be easily separated by the type column.\n\nThe available columns are the same as for the individual requester and hedge order blotters.\n\n# <span id=\"page-43-2\"></span>**6.5 Client Acitivity Blotter**\n\nThe client activity blotter shows one entry for every requested quote from a client:\n\n| <b>Client Activity</b>                  |                      |                       |                     |             | 圓                                                                                                             |  |  |\n|-----------------------------------------|----------------------|-----------------------|---------------------|-------------|---------------------------------------------------------------------------------------------------------------|--|--|\n| Q-<br>21 out of 21 Activities           |                      |                       |                     |             |                                                                                                               |  |  |\n| Id                                      | $-1$<br>Time (UTC)   | <b>Type</b>           | <b>Status</b>       | Description |                                                                                                               |  |  |\n| $\\Box$ 6164423-PEBANK APAC.TEST         | 5/15/14 2:48:46 PM   | EUR/USD RFS           | <b>EXECUTED</b>     | The RFS '6  | Ш                                                                                                             |  |  |\n| <b>EXECUTED</b>                         | 5/15/14 2:48:48 PM   | <b>RFS</b>            | <b>EXECUTED</b>     | The RFS '6  | $\\blacktriangle$                                                                                              |  |  |\n| <b>OUOTED</b>                           | 5/15/14 2:48:47 PM   | <b>RFS</b>            | <b>OUOTED</b>       | The RFS '6  |                                                                                                               |  |  |\n| <b>STARTED</b>                          | 5/15/14 2:48:46 PM   | <b>RFS</b>            | <b>STARTED</b>      | The RFS 'S  |                                                                                                               |  |  |\n| $\\blacksquare$ 6164417-PEBANK_APAC.TEST | 5/15/14 2:47:16 PM   | <b>USD/INR RFS</b>    | <b>CANCELED</b>     | The RFS '6  |                                                                                                               |  |  |\n| <b>CANCELED</b>                         | 5/15/14 2:48:16 PM   | <b>RFS</b>            | <b>CANCELED</b>     | The RFS '6  | 1999-1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - 1999 - |  |  |\n| <b>REJECTED</b>                         | 5/15/14 2:47:18 PM   | <b>RFS</b>            | <b>REJECTED</b>     | The RFS '6  |                                                                                                               |  |  |\n| <b>QUOTED</b>                           | 5/15/14 2:47:16 PM   | <b>RFS</b>            | QUOTED              | The RFS '6  |                                                                                                               |  |  |\n| <b>STARTED</b>                          | 5/15/14 2:47:16 PM   | <b>RFS</b>            | <b>STARTED</b>      | The RFS 'S  |                                                                                                               |  |  |\n| $\\Box$ 6164401-PEBANK APAC.TEST         | 5/15/14 2:32:26 PM   | EUR/USD RFS           | <b>CANCELED</b>     | The RFS '6  |                                                                                                               |  |  |\n| <b>CANCELED</b>                         | 5/15/14 2:33:22 PM   | <b>RFS</b>            | <b>CANCELED</b>     | The RFS '6  |                                                                                                               |  |  |\n| <b>QUOTED</b>                           | 5/15/14 2:32:26 PM   | <b>RFS</b>            | <b>OUOTED</b>       | The RFS '6  |                                                                                                               |  |  |\n| <b>STARTED</b>                          | 5/15/14 2:32:26 PM   | <b>RFS</b>            | <b>STARTED</b>      | The RFS 'B  |                                                                                                               |  |  |\n| <b>CACASTA BEDANIK ABACTECT</b>         | $E/1E/1A$ D-00-EC DM | <b>FLID /LICO DEC</b> | CANCELED The DEC 16 |             |                                                                                                               |  |  |\n\n<span id=\"page-44-1\"></span>Figure 44: Client Activity Blotter\n\nGreen rows show accepted requests, red rows show either cancelled (by the user), or rejected (by the MMC) requests. Accepted and system rejected requets are also shown in the client order blotter. Requests cancelled by the user (e.g. because he didn't accept the price) are only shown in this blotter!\n\nUsers can search for specific blotter entries with the search box at the top. Click on the magnifier glass to select search attributes, and enter comparison values.\n\n#### <span id=\"page-44-0\"></span>**6.6 Trade Blotter**\n\nThe trade blotter shows all trades from client and hedge orders across all managed instruments. For each trade the following information is available:\n\n- Order ID\n- Trade ID\n- Time of creation\n- Currency Pair\n- Side (BUY or SELL)\n- Type (e.g. RFS, Hedge, Cross, …)\n- Trigger (order which caused the trade)\n- Executed quantity\n- Execution price\n- Counterparty\n\n# <span id=\"page-45-0\"></span>**7 AUDIT**\n\nAll changes to the Market Maker Cockpit or ANY action by a user or triggered by the engine are stored in audit logs.\n\n# <span id=\"page-45-1\"></span>**8 APPENDIX**\n\n#### <span id=\"page-45-2\"></span>**8.1 Java Regular Expression Syntax**\n\nHere is the table listing down all the regular expression metacharacter syntax available in Java:\n\n| Expression   | Matches                                                                     |  |  |  |\n|--------------|-----------------------------------------------------------------------------|--|--|--|\n| ^            | Matches beginning of line.                                                  |  |  |  |\n| \\$           | Matches end of line.                                                        |  |  |  |\n|              | Matches any single character                                                |  |  |  |\n| []           | Matches any single character in brackets.                                   |  |  |  |\n| [^]          | Matches any single character not in brackets                                |  |  |  |\n| \\A           | Beginning of entire string                                                  |  |  |  |\n| \\z           | End of entire string                                                        |  |  |  |\n| \\Z           | End of entire string except allowable final line terminator.                |  |  |  |\n| re*          | Matches 0 or more occurrences of preceding expression.                      |  |  |  |\n| re+          | Matches 1 or more of the previous thing                                     |  |  |  |\n| re?          | Matches 0 or 1 occurrence of preceding expression.                          |  |  |  |\n| re{ n}       | Matches exactly n number of occurrences of preceding expression.            |  |  |  |\n| re{ n,}      | Matches n or more occurrences of preceding expression.                      |  |  |  |\n| re{ n, m}    | Matches at least n and at most m occurrences of preceding expression.       |  |  |  |\n| a  b         | Matches either a or b.                                                      |  |  |  |\n| (re)         | Groups regular expressions and remembers matched text.                      |  |  |  |\n| (?: re)      | Groups regular expressions without remembering matched text.                |  |  |  |\n| (?> re)      | Matches independent pattern without backtracking.                           |  |  |  |\n| \\w           | Matches word characters.                                                    |  |  |  |\n| \\W           | Matches nonword characters.                                                 |  |  |  |\n| \\s           | Matches whitespace. Equivalent to [\\t\\n\\r\\f].                               |  |  |  |\n| \\S           | Matches nonwhitespace.                                                      |  |  |  |\n| \\d           | Matches digits. Equivalent to [0-9].                                        |  |  |  |\n| \\D           | Matches nondigits.                                                          |  |  |  |\n| \\A           | Matches beginning of string.                                                |  |  |  |\n| \\Z           | Matches end of string. If a newline exists, it matches just before newline. |  |  |  |\n| \\z           | Matches end of string.                                                      |  |  |  |\n| \\G           | Matches point where last match finished.                                    |  |  |  |\n| \\n           | Back-reference to capture group number \"n\"                                  |  |  |  |\n| \\b           | Matches word boundaries when outside brackets                               |  |  |  |\n| \\B           | Matches nonword boundaries.                                                 |  |  |  |\n| \\n, \\t, etc. | Matches newlines, carriage returns, tabs, etc.                              |  |  |  |\n| \\Q           | Escape (quote) all characters up to \\E                                      |  |  |  |\n| \\E           | Ends quoting begun with \\Q                                                  |  |  |  |\n\n# <span id=\"page-46-0\"></span>**9 CONTACT 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n#### **Germany**\n\n*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Middle East Asia Pacific**\n\n**United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\n#### **USA**\n\n*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900\n\n**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "metadata": {"lang": "en"}}]