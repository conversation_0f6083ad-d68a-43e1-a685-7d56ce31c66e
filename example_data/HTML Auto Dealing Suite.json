[{"id": "1", "text": "![](_page_0_Picture_0.jpeg)\n\n# AUTO DEALING SUITE\n\n# TEX MULTIDEALER TRADING SYSTEM\n\n# User Guide 360T: Auto Dealing Suite (HTML)\n\nRelease 4.22 (November 2024)\n\n© 360 Treasury Systems AG, 2024 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG\n\n![](_page_1_Picture_0.jpeg)\n\n# CONTENTS\n\n| 1  |                               | Introduction                                            | 6  |  |  |  |  |\n|----|-------------------------------|---------------------------------------------------------|----|--|--|--|--|\n| 2  |                               | Accessing the Autodealing Suite and Auto Dealer Control | 7  |  |  |  |  |\n| 3  | HTML ADS Configuration Groups |                                                         |    |  |  |  |  |\n|    | 3.1                           | Requester Groups                                        | 10 |  |  |  |  |\n|    | 3.2                           | Provider Groups                                         | 17 |  |  |  |  |\n|    | 3.3                           | Product Groups                                          | 18 |  |  |  |  |\n|    | 3.4                           | Activation Period Groups                                | 18 |  |  |  |  |\n|    | 3.5                           | MM Currency Groups                                      | 20 |  |  |  |  |\n|    | 3.6                           | Currency Couple Groups                                  | 21 |  |  |  |  |\n|    | 3.7                           | Notional Amounts Groups                                 | 22 |  |  |  |  |\n|    | 3.8                           | Time Period Groups                                      | 23 |  |  |  |  |\n|    | 3.9                           | RFS Algorithm Groups                                    | 24 |  |  |  |  |\n|    | 3.10                          | Fixing Reference Groups                                 | 24 |  |  |  |  |\n|    | 3.11                          | Order Strategy Groups                                   | 25 |  |  |  |  |\n|    | 3.12                          | Negotiation Groups                                      | 26 |  |  |  |  |\n|    | 3.13                          | Manual Routing Groups                                   | 26 |  |  |  |  |\n|    | 3.14                          | Margin Groups                                           | 27 |  |  |  |  |\n| 4  |                               | Pricing Routing Rules                                   | 33 |  |  |  |  |\n|    | 4.1                           | Creation and Modification of Rules                      | 34 |  |  |  |  |\n|    | 4.2                           | Rule Parameters                                         | 35 |  |  |  |  |\n|    | 4.3                           | Pricing Routing                                         | 39 |  |  |  |  |\n| 5  |                               | Margin Rules                                            | 47 |  |  |  |  |\n|    | 5.1                           | Margin Application                                      | 50 |  |  |  |  |\n| 6  | Rule Search                   |                                                         |    |  |  |  |  |\n| 7  | Audit Log                     |                                                         |    |  |  |  |  |\n| 8  |                               | Auto Dealer Control                                     | 54 |  |  |  |  |\n| 10 | Contacting 360T               |                                                         |    |  |  |  |  |\n\n![](_page_2_Picture_0.jpeg)\n\n# TABLE OF FIGURES\n\n| Figure 1 360T HTML Self Service Portal.  7                                      |  |\n|---------------------------------------------------------------------------------|--|\n| Figure 2 Entity selection to reach the ADS overview page.  8                    |  |\n| Figure 3 ADS Rules Tool with Configuration Groups, Pricing and Margin Rules.  8 |  |\n| Figure 4 Auto Dealer Control Panel for RFS, Orders and SEP Configuration.  9    |  |\n| Figure 5 Administration of ADS Configuration Groups  10                         |  |\n| Figure 6 Default Requester Group.  11                                           |  |\n| Figure 7 Creation of ADS Configuration Groups  11                               |  |\n| Figure 8 Requester Group: Selection of Group Members.  12                       |  |\n| Figure 9 Creation of custom Requester Groups 12                                 |  |\n| Figure 10 Requester Groups: Creation or Modification via CSV Upload.  13        |  |\n| Figure 11 Editing requesters in Requester Group overview mode.  14              |  |\n| Figure 12 Accessing and editing single custom Requester Group.  14              |  |\n| Figure 13 Requesters tab.  15                                                   |  |\n| Figure 14 Editing group membership in Requesters tab.  16                       |  |\n| Figure 15 Creation of a new Provider Group.  17                                 |  |\n| Figure 16 Providers tab.  17                                                    |  |\n| Figure 17 Providers tab.  18                                                    |  |\n| Figure 18 Creation of a custom Activation Period Group  19                      |  |\n| Figure 19 Adding multiple time periods to a single Activation Period Group  19  |  |\n| Figure 20 Creation of MM Currency Group.  20                                    |  |\n| Figure 21 Currency Groups  20                                                   |  |\n| Figure 22 Creation of a Currency Couple Group 21                                |  |\n| Figure 23 Creation of a Notional Amount Group  22                               |  |\n| Figure 24 Time Period Groups  23                                                |  |\n| Figure 25 Time Period Group with discontinuous time ranges.  23                 |  |\n| Figure 26 Market Link Algorithm definition.  24                                 |  |\n| Figure 27 Fixing Reference Groups  25                                           |  |\n| Figure 28 Creation of an Order Strategy Group.  25                              |  |\n| Figure 29 Creation of a Negotiation Group.  26                                  |  |\n| Figure 30 Manual Routing Group configuration.  26                               |  |\n| Figure 31 Margin Groups and their types 28                                      |  |\n| Figure 32 Example of supported margin values.  29                               |  |\n| Figure 33 Margin groups: hiding margin columns.  32                             |  |\n\n© 2024 – 360 Treasury Systems AG <sup>3</sup>\n\n![](_page_3_Picture_0.jpeg)\n\n| Figure 34 Pricing Routing Rules.  33                                                                                  |\n|-----------------------------------------------------------------------------------------------------------------------|\n| Figure 35 New Pricing Routing Rule.  34                                                                               |\n| Figure 36 Selecting a Trading Venue  37                                                                               |\n| Figure 37 Route column.  39                                                                                           |\n| Figure 38 Creation of Margin Rules.  47                                                                               |\n| Figure 39 Applying margin transformation.  51                                                                         |\n| Figure 40 Rule Search 52                                                                                              |\n| Figure 41 Saving Rule Search Filters.  52                                                                             |\n| Figure 42 HTML ADS Audit Log.  53                                                                                     |\n| Figure 43 Audit Log: Text Search.  53                                                                                 |\n| Figure 44 Auto Dealer Control enabling and disabling Auto Dealer switch 54                                            |\n| Figure 45 Auto Dealer Control - Auto Dealer Schedule Enabled switch  54                                               |\n| Figure 46 Auto Dealer Control – Auto Dealer Start and Stop Times  54                                                  |\n| Figure 47 Auto Dealer Control - Auto Dealer Start Enabled switch  55                                                  |\n| Figure 48 Auto Dealer Control – Auto Dealer Start related alert  55                                                   |\n| Figure 49 Auto Dealer Control Day by Day Definition schedule table  56                                                |\n| Figure 50 Removing a time range from Day by Day Definition schedule table  57                                         |\n| Figure 51 Adding a time range in Day by Day Definition: First click (start time)  57                                  |\n| Figure 52 Adding a time range in Day by Day Definition: Second click (stop time) on<br>same day  57                   |\n| Figure 53 Adding a time range in Day by Day Definition: first click on one day and second<br>click on another day  57 |\n| Figure 54 Example of Auto Dealer Schedule setup to run continuously from Sunday to<br>Friday  58                      |\n\n![](_page_4_Picture_0.jpeg)\n\n# TABLES\n\n| Table 1 Pricing Routing availability by Negotiation, Product and Order Type.  46 |  |\n|----------------------------------------------------------------------------------|--|\n| Table 2 Margin Type availability by Negotiation, Product and Order Type.  50     |  |\n\n![](_page_5_Picture_0.jpeg)\n\n# 1 Introduction\n\nLiquidity providers retrieve price information from different channels to price their customers. The 360T Auto Dealing Suite (ADS) is a routing component embedded within 360T's trading platform that forwards negotiation requests and prices between the customer and a price source based on a custom ruleset. Price sources can be, inter alia, a pricing server, a manual trader, or a market link.\n\n360T has offered so far various versions of Auto Dealing Suite (ADS). The newest, HTML supported version, is a successor solution that offers enhanced and improved rule management capabilities, including pricing and margin rules separation, better organization and structuring by facilitation of product grouping, and in general, user experience simplification.\n\nThe new HTML ADS can be accessed via the 360T HTML Self Service Portal or Bridge Administration tool.\n\nThe legacy ADS versions will be decommissioned in future. Clients utilizing legacy ADS in BCT or Bridge Administration ADS will be upgraded to the HTML ADS version. They will be contacted by the 360T CAS team with further information on the upgrade process.\n\nThis user guide also describes the complimentary ADS tool allowing to start and stop auto-pricing, either manually or automatically according to a schedule. The ADS Control Panel is available in Bridge Administration tool as a category called Auto Dealer Control and will be described in Section 8.\n\n![](_page_6_Picture_0.jpeg)\n\n# 2 Accessing the Autodealing Suite and Auto Dealer Control\n\nThe ADS tool can then be accessed either via 360T HTML Self Service Portal by clicking on the \"ADS\" icon, or via Bridge Administration, which can be accessed under the menu option \"Administration\" in the screen header of the Bridge application.\n\n![](_page_6_Picture_3.jpeg)\n\nFigure 1 360T HTML Self Service Portal.\n\nThe Bridge Administration feature opens to a homepage \"Administration Start\" with available shortcuts to different configuration tools and actions for the user. The new HTML ADS can be accessed by clicking on the \"HTML ADS\" icon . Clients transitioning from Bridge Administration ADS version will be still able to access it via \"ADS Rules\".\n\nEntities accessible to the user are displayed in the left navigation panel. The user can select the entity for which the ADS rules are to be configured.\n\n![](_page_7_Picture_0.jpeg)\n\n![](_page_7_Picture_2.jpeg)\n\nFigure 2 Entity selection to reach the ADS overview page.\n\nThis opens the Auto Dealing Suite Rules tool for the selected entity and displays all administration functions enabled for the user.\n\n| 侖                                | Q Search For Institution<br>$\\rightarrow$ | ADS Configuration Groups | Pricing Routing Rules Margin Rules Audit log                                                                                                                        |             |  |               |                |                      |                     |             |\n|----------------------------------|-------------------------------------------|--------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|--|---------------|----------------|----------------------|---------------------|-------------|\n|                                  | 350TBANK APACTEST                         | Requester Groups         | Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups >> |             |  |               |                |                      |                     |             |\n| $\\mathcal{L}_{\\mathcal{F}}$<br>_ | 360TBANK APACTEST                         |                          |                                                                                                                                                                     | $\\mathsf Q$ |  | $\\rightarrow$ |                |                      |                     |             |\n| 马                                |                                           | Name                     | Group Members                                                                                                                                                       |             |  |               | No. of Members | No. of Routing Rules | No. of Margin Rules |             |\n| ₿                                |                                           | DEFAULT                  | BankE, HanscoTAS, SLEntity 2.TEST, SLEntity 1.TEST, SLONE TEST, Hanscompany, SLMain, 360T.APAC, LIM EMS TEST, ADS COMP.TEST                                         |             |  |               | 10             |                      |                     | $\\triangle$ |\n| $\\overline{u}$                   |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |\n| 69                               |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |\n| $\\overline{\\mathbb{L}}$ .        |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |\n| $\\leftrightarrow$                |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |\n| å.                               |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |\n|                                  |                                           |                          |                                                                                                                                                                     |             |  |               |                |                      |                     |             |\n\nFigure 3 ADS Rules Tool with Configuration Groups, Pricing and Margin Rules.\n\nThe Auto Dealer Control can be accessed by clicking on the correspondent icon.\n\nSelecting the entity accessible to the user from the left navigation panel (Figure 2) opens the Auto Dealer Control tool for the selected entity and displays all administration functions enabled for the user.\n\n![](_page_8_Picture_0.jpeg)\n\n| <b>TRADER WORKSHEET</b>                                                                  | T.<br><b>BRIDGE ADMINISTRATION</b>                                                                   |                                                                                                                                                                                                                   |                                                                                                                                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help | $\\sum$ $\\theta$ AA $ \\theta$ X |\n|------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|--------------------------------|\n| Q 带 1 血<br>侖<br>血 BankD<br>$\\mathcal{L}_{\\mathcal{T}}$<br><b>e</b><br>$\\Rightarrow$<br>_ | <b>RFS Auto Dealer Configuration</b><br>Orders Auto Dealer Configuration<br>RFS Auto Dealer Schedule | SEP Auto Dealer Configuration<br>RFS Auto Dealer Enabled<br>RFS Auto Dealer Schedule Enabled<br>RFS Auto Dealer Start Enabled<br>Day by Day Definition<br>RFS Auto Dealer Start Time<br>RFS Auto Dealer Stop Time | <b>CO</b> Enabled<br>CO Enabled<br><b>CO</b> Enabled<br>O o Disabled<br>$\\bullet$ 06:00 $\\bullet$<br>(07:00 CEST)<br>$2200$ $\\circledcirc$ |                                                      | $n \\approx \\equiv$             |\n|                                                                                          |                                                                                                      |                                                                                                                                                                                                                   | (23:00 CEST)                                                                                                                               |                                                      |                                |\n\nFigure 4 Auto Dealer Control Panel for RFS, Orders and SEP Configuration.\n\n# 3 HTML ADS Configuration Groups\n\nThe enhanced HTML ADS allows the user to define a wide range of rules using customized and centrally managed groups of parameters such as negotiations (RFS, Orders, SEP), FX or MM products, order types, currencies, currency pairs, notional ranges, rule activation time windows, maturities, etc. which can be defined within the ADS Configuration Groups, refer to Figure 7.\n\nA rule is a combination of:\n\n- Conditions, which define the specific criteria/constraints that trigger an action.\n- Outcome, which defines the action to be undertaken when the said criteria are met.\n\nIn general, ADS Configuration Groups can be re-used for both Pricing Routing Rules and Margin rules. Elements of the groups and combinations of those groups determine which pricing route or margin group type can be configured. For example, an \"Order to Order\" pricing route will only be selectable, if the Negotiation Group only contains the negotiation \"Orders\", whereas the \"No Pricing\" routing is available for all combinations of negotiations, products or order types.\n\n| <b>Requester Groups</b> | Providers<br>Requesters | <b>Activation Period Groups</b><br>Provider Groups<br>Product Groups                       |          | $\\gg$<br>MM Currency Groups<br>Currency Couple Groups                                                                                                  |                     |             |\n|-------------------------|-------------------------|--------------------------------------------------------------------------------------------|----------|--------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|-------------|\n| Name                    | <b>Group Members</b>    | Q                                                                                          | No. of I | Notional Amount Groups<br><b>Time Period Groups</b>                                                                                                    | No. of Margin Rules |             |\n| DEFAULT                 |                         | BankE, HansCoTAS, SLEntity 2.TEST, SLEntity 1.TEST, SLONE.TEST, HansCompany, SLM<br>$^{+}$ | 10       | RFS Algorithm Groups<br><b>Fixing Reference Groups</b><br>Order Strategy Groups<br><b>Negotiation Groups</b><br>Manual Routing Groups<br>Margin Groups | $\\circ$             | $\\triangle$ |\n\n![](_page_9_Picture_0.jpeg)\n\n#### Figure 5 Administration of ADS Configuration Groups\n\nThe available rule parameters in the ADS Configuration Groups are:\n\n- Requester Groups (accessible via tabs \"Requester Groups\" and \"Requesters\")\n- Negotiation Groups\n- Product Groups\n- Activation Period Groups\n- MM Currency Groups\n- Currency Couple Groups\n- Notional Amount Groups\n- Time Period Groups\n- RFS Algorithm Groups\n- Fixing Reference Groups\n- Order Strategy Groups\n\nMargin Groups are not rule parameters but are used to configure the outcome of a Margin Rule which results in the application of a set of margin values of a specific type.\n\nProvider Groups (accessible via tabs \"Provider Groups\" and \"Providers\") and Manual Routing Groups also belong to rule output category.\n\nThe next section focuses on the available ADS Configuration Groups within the enhanced ADS. The order of the Configuration Groups, as shown in the enhanced ADS, is reflected in the same order in the subsequent sub-sections.\n\nGeneral user interface features relevant for ADS Configuration Groups will be described in detail using the example of the Requester Groups.\n\n# 3.1 Requester Groups\n\nThe various customers of a provider are referred to as Requesters. Requester entities can be added or removed from the group in a central place without the need to edit the rules themselves.\n\nAfter setup and acceptance of the counterparty relationship in Bridge Administration Counterparty Relationship tool, a requester entity is first assigned to the \"DEFAULT\" Requester Group. As soon as the requester entity\n\n![](_page_10_Picture_0.jpeg)\n\nis assigned to a custom, non-default Requester Group, it will be removed automatically from the Default Requester Group.\n\nThe group is shown as a table entry, containing the following headers:\n\n- Name (either \"Default\" or a custom name, must be unique within a single configuration group)\n- Group Members (preview of selected group elements, however limited to the width of the column)\n- No. of Members (number of selected group elements)\n- No. of Routing Rules (number of Pricing Routing Rules configured with this group)\n- No. of Margin Rules (number of Margin Rules configured with this group)\n\n|                  | <b>ADS Configuration Groups</b> | <b>Pricing Routing Rules</b> | <b>Margin Rules</b>                                                             | Audit log      |                          |                             |                     |               |\n|------------------|---------------------------------|------------------------------|---------------------------------------------------------------------------------|----------------|--------------------------|-----------------------------|---------------------|---------------|\n| Requester Groups | Requesters                      | Providers                    | Provider Groups                                                                 | Product Groups | Activation Period Groups | $\\gg$<br>MM Currency Groups |                     |               |\n|                  |                                 |                              |                                                                                 |                |                          | $\\rightarrow$               |                     |               |\n| Name             | <b>Group Members</b>            |                              |                                                                                 |                | No. of Members           | No. of Routing Rules        | No. of Margin Rules |               |\n| DEFAULT          |                                 |                              | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany,   10 |                |                          | $\\circ$                     | $\\circ$             | $\\Rightarrow$ |\n|                  |                                 |                              |                                                                                 |                | $^+$                     |                             |                     |               |\n\nFigure 6 Default Requester Group.\n\nA list of all group members (in this case Requesters) with their group assignments can be downloaded as a CSV file. This file can be used for creating new groups or re-assigning memberships via CSV upload. Download\n\nand upload icons are available for each Configuration Group and can be found in the upper right corner of the page.\n\n## 3.1.1 Creation of a new Requester Group\n\nCreation of a new group starts with clicking on the green plus icon where the user then has the possibility to enter the group name and select the group members.\n\n| Requester Groups | Providers<br>Requesters                                                      | Provider Groups<br>Product Groups | <b>Activation Period Groups</b> | MM Currency Groups<br>$\\gg$ |                     |                         |\n|------------------|------------------------------------------------------------------------------|-----------------------------------|---------------------------------|-----------------------------|---------------------|-------------------------|\n|                  | $\\alpha$                                                                     |                                   |                                 | $\\rightarrow$               |                     |                         |\n| Name             | <b>Group Members</b>                                                         |                                   | No. of Members                  | No. of Routing Rules        | No. of Margin Rules |                         |\n| DEFAULT          | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany 10 |                                   |                                 | O                           | Đ                   | $\\approx$ 11            |\n| ter Name         | Select Group Members                                                         |                                   |                                 |                             |                     | $\\times \\triangleright$ |\n\nFigure 7 Creation of ADS Configuration Groups\n\n![](_page_11_Picture_0.jpeg)\n\nEach rule must have a unique name within the configuration group. The user can select multiple elements within the rule either by selecting the value from the drop-down list or by typing its name. A parent requester entity with all underlying legal entities can be added by clicking on the \"Select Group\" icon next to its name.\n\n|         | $\\alpha$                                                                                                                                                             |                | $\\rightarrow$                            |                     |               |\n|---------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|------------------------------------------|---------------------|---------------|\n| Name    | Group Members                                                                                                                                                        | No. of Members | No. of Routing Rules                     | No. of Margin Rules |               |\n| DEFAULT | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany,                                                                                           | 10             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | $\\circ$             | $\\Rightarrow$ |\n| AAA+    | Select Group Members                                                                                                                                                 |                |                                          |                     | $\\times$      |\n|         | STATUTE CITY PASS INTERNATIONAL EXCEPTION<br>360T.APAC<br><b>Select Group</b><br>ADS COMPTEST<br>ADS COMP.TEST<br>BankE (Select Group<br>BankE<br>Hans1 Select Group |                |                                          |                     |               |\n\n#### Figure 8 Requester Group: Selection of Group Members.\n\nThe creation of a single group is finished by applying the green checkbox icon.\n\nAll changes can be saved or discarded in one step by using the corresponding \"Discard all Changes\" or \"Save\" icons.\n\n|                  | Q                                                                          |                | $\\rightarrow$        |                     |                 |\n|------------------|----------------------------------------------------------------------------|----------------|----------------------|---------------------|-----------------|\n| Name             | <b>Group Members</b>                                                       | No. of Members | No. of Routing Rules | No. of Margin Rules |                 |\n| $AAA+$           | ADS COMP.TEST                                                              | $\\mathbf{1}$   | $\\circ$              | $\\circ$             | <b>今面</b>       |\n| DEFAULT          | BankE, HansCoTAS, SLEntity2.TEST, SLEntity1.TEST, SLONE.TEST, HansCompany, | $\\overline{9}$ | $\\circ$              | $\\circ$             | Ũ<br>$\\approx$  |\n| <b>B</b> Clients | 360T.APAC                                                                  | $\\,1$          | $\\circ$              | $\\circ$             | <b>今回</b>       |\n|                  |                                                                            |                |                      |                     |                 |\n| Enter Name       | Select Group Members                                                       |                |                      |                     | $\\times$ $\\vee$ |\n|                  |                                                                            |                |                      |                     |                 |\n|                  |                                                                            |                |                      |                     |                 |\n\nFigure 9 Creation of custom Requester Groups.\n\n![](_page_12_Picture_0.jpeg)\n\nVarious groups within one configuration group can contain the same element, for example the same requester entity can be a member of multiple requester groups (excluding the Default Requester Group).\n\nThe trash bin symbol deletes a configured group and all its values. In case there are pricing or margin rules where this group is used, the group cannot be deleted.\n\nA new group or multiple groups can alternatively be created via CSV upload. In this case it is sufficient to add the name of the new group to the list of downloaded group elements in the column \"Groups membership\", separated by a pipe. The group will be automatically created, and the group members will be selected. A group assignment can be removed by removing the name of the group from \"Groups membership\". After the upload, a results file will be generated, and the user can review all changes before saving them in the application.\n\nThe figure below shows how Requesters can be assigned to existing or new groups:\n\n| $\\overline{ }$ |                                                     |                                | D                    |                             |                                                                           |\n|----------------|-----------------------------------------------------|--------------------------------|----------------------|-----------------------------|---------------------------------------------------------------------------|\n| Requester      | Legal entity                                        | Long Name                      | Negotiation          | <b>Trading venue</b>        | Groups membership                                                         |\n|                | 360T.EMSCorporate 360T.EMSCorporate.FOND.A1 *Empty* |                                | Orders               | OTC. EU MTF, UK MTF DEFAULT |                                                                           |\n| SubsidiaryG    | SubsidiaryG                                         | *Empty*                        | RFS, Orders, SEP OTC |                             | Gold   Platinium   Bronze                                                 |\n| SubsidiaryG    | Fund <sub>G.1</sub>                                 | Fund 1 SubsidiaryG RFS, Orders |                      |                             | OTC, EU MTF, UK MTF Gold   Platinium   Requester1   Silver2   Top Clients |\n| SubsidiaryG    | FundSubG.1                                          | FundSubG.1                     | <b>RFS. Orders</b>   | <b>OTC</b>                  | Gold   Platinium   Requester1   Silver2   Top Clients                     |\n| SubsidiaryG    | FundSubG.2                                          | FundSubG.2                     | <b>RFS, Orders</b>   | <b>OTC</b>                  | <b>DEFAULT</b>                                                            |\n\nFigure 10 Requester Groups: Creation or Modification via CSV Upload.\n\n## 3.1.2 Adding and removing members of a Requester Group\n\nThe members of a group can be added or removed either directly in the \"Requester Groups\" tab in Group Members columns by simply double-clicking\n\non it, or by accessing the individual custom group's tab via the arrow icon placed in the last column of each group entry. Modifications of group assignments can be alternatively done via CSV upload, as explained in previous chapter.\n\n![](_page_13_Picture_0.jpeg)\n\n| Requester Groups | Providers<br>Requesters  | Provider Groups | $\\gg$<br>Product Groups |                     |          |\n|------------------|--------------------------|-----------------|-------------------------|---------------------|----------|\n|                  | Q                        |                 |                         | $\\rightarrow$       |          |\n| Name             | <b>Group Members</b>     | No. of Members  | No. of Routing Rules    | No. of Margin Rules |          |\n| AAA+             | ADS COMP.TEST $\\chi$     | 1               | $\\mathbf{o}$            | $\\overline{0}$      | ゆ面       |\n| <b>B</b> Clients | 360T.APAC                | $\\mathbf{1}$    | ö                       | o                   | 2回       |\n| DEFAULT          | BankE, HansCoTAS, SLEnti | $_{\\rm 8}$      | $\\circ$                 | $\\circ$             | ाति<br>È |\n\nFigure 11 Editing requesters in Requester Group overview mode.\n\nThe name of a custom group can be changed by double clicking and modifying it.\n\nIn case a group has many elements, it is more convenient to use the single group's edit mode which opens a new tab and allows to add or remove its members.\n\n| $\\leftarrow$  | Requester Groups > Group AAA+ |                  |                      |                       |                        |\n|---------------|-------------------------------|------------------|----------------------|-----------------------|------------------------|\n|               |                               | $\\alpha$         |                      | $\\rightarrow$         |                        |\n| Requester     | Legal entity                  | Negotiation      | <b>Trading venue</b> | All Groups Membership |                        |\n| LIM EMS TEST  | LIM EMS TEST                  | RFS              | OTC                  | AAA+                  | $\\widehat{\\boxplus}$   |\n| ADS COMP.TEST | ADS COMP.TEST                 | RFS, Orders, SEP | OTC                  | AAA+                  | $\\widehat{\\mathbb{U}}$ |\n| Banke         | BankE                         | RFS. Orders      | <b>OTC</b>           | AAA+                  | $\\widehat{\\mathbb{U}}$ |\n| Hans1         | HansCoTAS                     | RFS              | OTC                  | AAA+                  | û                      |\n| SLONE TEST    | SLEntity2.TEST                | RFS, Orders      | OTC.                 | AAA+                  | $\\widehat{\\mathbb{U}}$ |\n| SLONE.TEST    | SLEntity1.TEST                | RFS. Orders      | OTC                  | AAA+                  | Û                      |\n| SLONE.TEST    | SLONE TEST                    | RFS, Orders      | <b>OTC</b>           | AAA+                  | $\\widehat{\\mathbb{U}}$ |\n| SLONE.TEST    | SLMain                        | RFS, Orders      | OTC                  | AAA+                  | Û                      |\n| 360T.APAC     | 360T.APAC                     | RFS, Orders, SEP | OTC, EU MTF          | AAA+, B Clients       | ⑪                      |\n\nFigure 12 Accessing and editing single custom Requester Group.\n\n![](_page_14_Picture_0.jpeg)\n\n## 3.1.3 Requesters tab\n\nAll individual requester entities and their group memberships are listed alphabetically in a table, in the \"Requesters\" tab.\n\n|              |                | $\\alpha$      |                  |                      | $\\rightarrow$     |  |\n|--------------|----------------|---------------|------------------|----------------------|-------------------|--|\n| Requester    | Legal Entity   | Long Name     | Negotiation      | <b>Trading Venue</b> | Groups Membership |  |\n| 360T.APAC    | 360T.APAC      | 360T.APAC     | RFS, Orders, SEP | OTC. EU MTF          | AAA+, B Clients   |  |\n| ADS COMPTEST | ADS COMP.TEST  | ADS COMP.TEST | RFS, Orders, SEP | OTC                  | AAA+              |  |\n| BankE        | <b>BankE</b>   | \"Empty\"       | RFS, Orders      | OTC                  | AAA+              |  |\n| Hans1        | HansCoTAS      | HansCoTAS     | <b>RFS</b>       | OTC                  | AAA+              |  |\n| HansCompany  | HansCompany    | Hans Company  | <b>RFS</b>       | OTC                  | DEFAULT           |  |\n| LIM EMS TEST | LIM EMS TEST   | *Empty*       | <b>RFS</b>       | OTC                  | AAA+              |  |\n| SLONE TEST   | SLONE.TEST     | \"Empty\"       | RFS, Orders      | OTC                  | AAA+              |  |\n| SLONE.TEST   | SLEntity1.TEST | \"Empty\"       | RFS, Orders      | OTC                  | AAA+              |  |\n| SLONE.TEST   | SLEntity2.TEST | \"Empty\"       | RFS, Orders      | OTC                  | AAA+              |  |\n| SLONE TEST   | SLMain         | SLMain Ltd    | RFS, Orders      | OTC                  | AAA+              |  |\n| SLMain       | SLMain         | SLMain Ltd    | RFS, Orders      | OTC                  | AAA+              |  |\n| SLMain       | SLEntity1.TEST | \"Empty\"       | RFS, Orders      | OTC                  | AAA+              |  |\n| SLMain       | SLEntity2.TEST | \"Empty\"       | RFS, Orders      | OTC                  | AAA+              |  |\n| SLMain       | SLONE TEST     | *Empty*       | RFS, Orders      | OTC                  | $AAA+$            |  |\n\n#### Figure 13 Requesters tab.\n\nFollowing column headers are available:\n\n- Requester: system name of the legal entity\n- Legal Entity: indicates if the legal entity has another parent entity; in case of a parent entity both Requester and Legal entity are the same\n- Long Name: Legal name of the entity\n- Negotiation: type of negotiation (RFS, Orders or SEP), for which the provider and requester legal entity have an accepted counterparty relationship\n- Trading Venue: shows the trading venue for which a requester entity is enabled (OTC, EU MTF, UK MTF)\n\nGroups Membership: shows Requester Groups of which the legal entity is a member; double clicking on a cell opens up the editing view of the groups\n\n![](_page_15_Picture_0.jpeg)\n\n| Requester Groups | Requesters        | Providers<br>Provider Groups | Product Groups   | Activation Period Groups | $\\gg$<br>MM Currency Groups |  |\n|------------------|-------------------|------------------------------|------------------|--------------------------|-----------------------------|--|\n|                  |                   | $\\alpha$                     |                  | $\\rightarrow$            |                             |  |\n| Requester        | Legal Entity      | Long Name                    | Negotiation      | <b>Trading Venue</b>     | Groups Membership           |  |\n| 360T.APAC        | 360T.APAC         | 360T.APAC                    | RFS, Orders, SEP | OTC, EU MTF              | AAA+, B Clients             |  |\n| ADS COMP.TEST    | ADS COMP.TEST     | ADS COMP.TEST                | RFS, Orders, SEP | OTC                      | AAA+                        |  |\n| BankE            | BankE             | \"Empty\"                      | RFS, Orders      | OTC                      | AAA+                        |  |\n| Hans1            | HansCoTAS         | HansCoTAS                    | <b>RFS</b>       | OTC                      | AAA+                        |  |\n| HansCompany      | HansCompany       | <b>Hans Company</b>          | <b>RFS</b>       | <b>OTC</b>               | DEFAULT X)                  |  |\n| LIM EMS TEST     | LIM EMS TEST      | *Empty*                      | <b>RFS</b>       | OTC                      | AAA+                        |  |\n| SLONE TEST       | <b>SLONE TEST</b> | \"Empty\"                      | RFS, Orders      | OTC                      | <b>B</b> Clients            |  |\n| SLONE.TEST       | SLEntity1.TEST    | *Empty*                      | RFS, Orders      | OTC                      | DEFAULT                     |  |\n| SLONE TEST       | SLEntity2.TEST    | \"Empty\"                      | RFS, Orders      | OTC                      | AAA+                        |  |\n| SLONE TEST       | SLMain            | SLMain Ltd                   | RFS, Orders      | OTC                      | AAA+                        |  |\n| SLMain           | SLMain            | SLMain Ltd                   | RFS, Orders      | OTC                      | AAA+                        |  |\n| SLMain           | SLEntity1.TEST    | \"Empty\"                      | RFS, Orders      | OTC                      | AAA+                        |  |\n| SLMain           | SLEntity2.TEST    | \"Empty\"                      | RFS, Orders      | OTC                      | AAA+                        |  |\n| SLMain           | SLONE TEST        | *Empty*                      | RFS, Orders      | OTC                      | AAA+                        |  |\n\n#### membership, allowing to add or remove a group (see\n\n#### Figure 14 Editing group membership in Requesters tab.\n\nThe search area allows to search by specific text within the columns Requester, Legal Entity and Long Name.\n\n![](_page_16_Picture_0.jpeg)\n\n# 3.2 Provider Groups\n\nThe ADS facilitates the option to forward incoming requests to a group of Liquidity Providers (referred to as Market Link routing) or to single provider in case of Orders. These groups can be defined under the menu item Provider Groups before including them in the respective market link destination rules.\n\nCreation or editing of Provider Groups can be done in the tabs Provider Groups and Providers, in the same manner as for the Requester Groups or Requesters, as described in the previous chapter.\n\nThe main difference between both groups is the absence of a Default group in case of Providers. By default, a provider is not a member of a group. However a default pricing rule setting includes all providers (value \"Any\") with an accepted CRM relationship.\n\n|                         |                          |  | Activation Period Groups | MM Currency Groups      | $\\gg$                |          |\n|-------------------------|--------------------------|--|--------------------------|-------------------------|----------------------|----------|\n|                         | $\\alpha$                 |  |                          | $\\rightarrow$           |                      |          |\n| Name                    | <b>Group Members</b>     |  |                          | No. of Members          | No. of Routing Rules |          |\n| Market Link RFS         | 360TBANK.TEST, BOAL.DEMO |  |                          | $\\overline{\\mathbf{c}}$ | $\\circ$              | ゆ面       |\n| ter Name<br><b>CEN-</b> | Select Group Members     |  |                          |                         |                      | $\\times$ |\n\nFigure 15 Creation of a new Provider Group.\n\nThe tab Providers shows all available providers, negotiation types and trading venues they are pricing on.\n\n| Requester Groups   | Providers<br>Requesters | Provider Groups                                                  | MM Currency Groups    Currency Couple Groups<br>Product Groups<br>Activation Period Groups |                      |                   |\n|--------------------|-------------------------|------------------------------------------------------------------|--------------------------------------------------------------------------------------------|----------------------|-------------------|\n|                    |                         | $\\alpha$                                                         | $\\rightarrow$                                                                              |                      |                   |\n| Provider           | Legal Entity            | Long Name                                                        | Negotiation                                                                                | <b>Trading Venue</b> | Groups Membership |\n| 360TBANKTEST       | 360TBANKTEST            | PLEASE DONT SEF ENABLE THIS BANK - CREATE A SEPARATE ONE INSTEAD | RFS, Orders, SEP                                                                           | OTC. EU MTF          | Market Link RFS   |\n| BOAL DEMO          | BOAL DEMO               | BOAL DEMO                                                        | RFS, Orders, SEP                                                                           | OTC, EU MTF          | Market Link RFS   |\n| Barclays BARX.DEMO | Barclays BARX.DEMO      | Barclays BARX.DEMO                                               | RFS, Orders, SEP                                                                           | OTC. EU MTF          |                   |\n| CITIBANK.DEMO      | CITIBANK.DEMO           | CITIBANK DEMO                                                    | RFS, Orders, SEP                                                                           | OTC                  |                   |\n| RBS.LND.DEMO       | RBS.LND.DEMO            | RBS.LND.DEMO                                                     | RFS, Orders, SEP                                                                           | OTC, EU MTF          |                   |\n| SEB.DEMO           | SEB.DEMO                | SEB.DEMO                                                         | RFS, Orders, SEP                                                                           | OTC                  |                   |\n\n#### Figure 16 Providers tab.\n\nA list of all group members (in this case Providers) with their group assignments can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n![](_page_17_Picture_0.jpeg)\n\n# 3.3 Product Groups\n\nProduct groups can contain the following products:\n\n- FX Spot\n- FX Forward\n- FX Time Option\n- FX Future\n- FX Swap\n- Block Trade\n- NDF\n- NDS\n- FX Option\n- Loan / Deposit\n- Cross Currency Portfolio (EMS)\n- Commodity Asian Swap\n- Commodity Bullet Swap\n- Metals products: Outrights, Quarterly Strips and Metals Spreads\n\n|                 | Requesters<br>Providers<br>Requester Groups                                                                                 | Provider Groups    |          | Product Groups Activation Period Groups MM Currency Groups | Currency Couple Groups | Notional Amount Groups >>> |                     |            |\n|-----------------|-----------------------------------------------------------------------------------------------------------------------------|--------------------|----------|------------------------------------------------------------|------------------------|----------------------------|---------------------|------------|\n|                 |                                                                                                                             |                    | $\\alpha$ |                                                            | $\\rightarrow$          |                            |                     |            |\n| Name            | <b>Group Members</b>                                                                                                        |                    |          |                                                            | No. of Members         | No. of Routing Rules       | No. of Margin Rules |            |\n|                 |                                                                                                                             |                    |          |                                                            |                        |                            |                     | <b>N</b> 面 |\n| Spot and Forwar | $Fx$ Forward $\\times$                                                                                                       | $Fx$ Spot $\\times$ |          |                                                            |                        |                            |                     | $\\times$   |\n|                 | Block-Trade<br>Commodity Asian Swap<br>Commodity Bullet Swap<br>Cross Currency Portfolio<br>Deposit<br>$\\sqrt{}$ Fx Forward |                    |          |                                                            |                        |                            |                     |            |\n|                 | Fx Future                                                                                                                   |                    |          |                                                            |                        |                            |                     |            |\n|                 | Fx Option                                                                                                                   |                    |          |                                                            |                        |                            |                     |            |\n\n#### Figure 17 Providers tab.\n\nA list of all Products with their group assignments can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n# 3.4 Activation Period Groups\n\nOften, the set of rules that apply depend on the time of the day the request is made. The ADS capabilities allow the definition of activation periods, which can be used in conjunction with other parameters to simplify rule creation and vastly reduce the number of rules to achieve the same outcome.\n\nIn the field \"Time Zone\", it is possible to have a time zone defined other than UTC. In case the Company time zone is a time zone where Daylight Savings Time (DST) is considered, there is no need for manual adjustments of any rules to accommodate DST. The system will automatically take DST into account.\n\n![](_page_18_Picture_0.jpeg)\n\nGroups with activation time windows can be created, removed, and renamed similarly to Requester Groups.\n\n|      |                     |                     |                   | Time Zone | Europe/Berlin, Currently: +02:00 (UTC+02:00) |                | $\\checkmark$         |       |\n|------|---------------------|---------------------|-------------------|-----------|----------------------------------------------|----------------|----------------------|-------|\n|      |                     |                     |                   |           |                                              |                |                      |       |\n|      |                     | $\\alpha$            |                   |           |                                              | $\\rightarrow$  |                      |       |\n| Name | <b>Time Periods</b> |                     |                   |           |                                              | No. of Members | No. of Routing Rules | No. 0 |\n|      |                     |                     |                   |           |                                              |                |                      | ゆ画    |\n| Day  | 00:00               |                     | $\\Theta$<br>17:59 | Ő         |                                              |                |                      |       |\n|      | $\\checkmark$<br>ĸе  | $\\approx$ 00 $\\sim$ |                   |           |                                              |                |                      |       |\n|      | $\\times$ Cancel     | $\\sqrt{$ Apply      |                   | $^+$      |                                              |                |                      |       |\n\n#### Figure 18 Creation of a custom Activation Period Group\n\nWithin a group, an activation time window can be defined. The user can define a broken / discontinuous activation time for the group by simply adding more than a one-time window. This must be done in the single Activation Period Group preview which can be opened by clicking on the arrow icon.\n\n| <b>ADS Configuration Groups</b><br>Providers<br>Requester Groups<br>Requesters | <b>Pricing Routing Rules</b><br><b>Margin Rules</b><br>Provider Groups | Settings<br>Product Groups | Audit log<br><b>Activation Period Groups</b> | MM Currency Groups | Currency Couple Groups | Notional Amount Groups | $\\gg$ |\n|--------------------------------------------------------------------------------|------------------------------------------------------------------------|----------------------------|----------------------------------------------|--------------------|------------------------|------------------------|-------|\n|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |\n| Activation Period Groups > Group Day                                           | StartTime                                                              |                            |                                              | End Time           |                        |                        |       |\n|                                                                                | 00:00                                                                  |                            |                                              | 23:59              |                        | $\\widehat{\\mathbb{U}}$ |       |\n|                                                                                | 00:00                                                                  | Θ<br>23:59                 | O.                                           |                    |                        |                        |       |\n|                                                                                |                                                                        |                            | $^+$                                         |                    |                        |                        |       |\n|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |\n|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |\n|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |\n|                                                                                |                                                                        |                            |                                              |                    |                        |                        |       |\n\n#### Figure 19 Adding multiple time periods to a single Activation Period Group\n\nFor example, let's assume that Swaps are manually priced, and the Swap desk is situated in Singapore and New York. In this case, a group can be created to cover the availability times of this desk, and this group would contain a row each to hold the availability time in each city.\n\nThe system allows the creation of groups with overlapping activation time windows as these groups could have applicability in different rules.\n\nThe setting \"Any\" available in pricing and margin rules tabs encompasses all times of the day, denoted by a time window from 00:00 GMT to 00:00 GMT representing start and end times.\n\nNote: Activation periods can be used for all negotiation types. However, for SEP, the activation period is only assessed at the time of a new subscription.\n\n![](_page_19_Picture_0.jpeg)\n\nA list of all activation time ranges with their Groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n## 3.5 MM Currency Groups\n\nMM (Money Market) Currency Groups are intended to allow the classification of currencies. Once created, a currency group can be used to simplify rule creation for interest rate products like Loans or Deposits. Currencies can be added or removed from the group in a central place without the need to edit the rules themselves.\n\nCreation of a new group starts with clicking on the green plus icon:\n\n#### Figure 20 Creation of MM Currency Group.\n\nThe user can select multiple currencies within the rule either by selecting the value from the drop-down list or by typing its name. The symbol \"\\*\\*\\*\" stands for any available currency. The system does not restrict the creation of groups with an overlapping set of currencies.\n\n|                  | Requester Groups Requesters Providers Provider Groups |          | Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups >>> |               |                |                      |                     |     |\n|------------------|-------------------------------------------------------|----------|--------------------------------------------------------------------------------------------------------------|---------------|----------------|----------------------|---------------------|-----|\n|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |\n|                  |                                                       | $\\alpha$ |                                                                                                              | $\\rightarrow$ |                |                      |                     |     |\n| Name             | Currencies                                            |          |                                                                                                              |               | No. of Members | No. of Routing Rules | No. of Margin Rules |     |\n|                  |                                                       |          |                                                                                                              |               |                |                      |                     | ☆ 面 |\n|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |\n|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |\n|                  | GBP $\\times$ (EUR $\\times$ )                          |          |                                                                                                              |               |                |                      |                     |     |\n|                  |                                                       |          |                                                                                                              |               |                |                      |                     |     |\n|                  | AUD                                                   |          |                                                                                                              |               |                |                      |                     |     |\n|                  | CAD                                                   |          |                                                                                                              |               |                |                      |                     |     |\n|                  | CHF                                                   |          |                                                                                                              |               |                |                      |                     |     |\n|                  | $\\sqrt{$ FUR                                          |          |                                                                                                              |               |                |                      |                     |     |\n|                  | $\\sqrt{GBP}$                                          |          |                                                                                                              |               |                |                      |                     |     |\n|                  | <b>HKD</b>                                            |          |                                                                                                              |               |                |                      |                     |     |\n| Autopriced curre | JPY                                                   |          |                                                                                                              |               |                |                      |                     | X   |\n\n#### Figure 21 Currency Groups\n\nA list of all Currencies assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n![](_page_20_Picture_0.jpeg)\n\n# 3.6 Currency Couple Groups\n\nCurrency Couple Groups allow bucketing of currency pairs. Once created, a currency couple group can be used to simplify rule definition, among others, for FX Spots, Forwards, Swaps, NDF, NDS, Options and Block Trades.\n\nPer default, pricing or routing rules are configured with value \"Any\" including all currency pairs available.\n\nCurrency Couple Groups can be created, removed, and renamed similarly to Requesters Groups, as explained in Section 3.5.\n\n|        | Requester Groups Requesters Providers Provider Groups                                                                    |   | Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups >>> |                |                      |                     |                |\n|--------|--------------------------------------------------------------------------------------------------------------------------|---|--------------------------------------------------------------------------------------------------------------|----------------|----------------------|---------------------|----------------|\n|        |                                                                                                                          | Q |                                                                                                              | $\\rightarrow$  |                      |                     |                |\n| Name   | Couples                                                                                                                  |   |                                                                                                              | No. of Members | No. of Routing Rules | No. of Margin Rules |                |\n|        |                                                                                                                          |   |                                                                                                              |                |                      |                     | ☆ 面            |\n| Majors | EUR/USD $\\times$ )<br>$EUR/GBP \\times$                                                                                   |   |                                                                                                              |                |                      |                     | $\\times \\sqrt$ |\n|        | $\\sqrt{\\mathsf{EUR}/\\mathsf{USD}}$<br>$\\sqrt{\\text{EUR/GBP}}$<br>EUR/CHF<br>$EUR/***$<br>GBP/USD<br>GBP/CHF<br>$GBP/***$ |   |                                                                                                              |                |                      |                     |                |\n\nFigure 22 Creation of a Currency Couple Group\n\nWithin a group, the green plus button \"Add Currency Couple\" creates a new line in which a currency pair can be added.\n\nNew currency pairs can be added to, or removed from a group, thus providing the ability to impact targeted rules, whilst not needing to manipulate rules individually.\n\nThe system does not restrict the creation of groups with an overlapping set of currencies.\n\nA list of all Currency Pairs can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n![](_page_21_Picture_0.jpeg)\n\n# 3.7 Notional Amounts Groups\n\nTypically, the range of notional amounts is an important factor in determining the pricing and routing mechanisms for a product, besides others. The Notional Amounts Group allows to bucket ranges of continuous or discontinuous notional amounts within a group. This group, when created, can be used in rule definition across products, currencies, and currency pairs in ADS RFS and ADS Order Rules. The Notional Amounts Groups are not relevant for SEP Order Rules configuration.\n\nThe \"Any\" notional group value, which is set per default when creating new rules, includes all notional amount, set up as zero to unlimited, which can be modified to hold a different set of values.\n\nNotional Amount Groups can be created, removed and renamed similarly to other ADS Configuration Groups.\n\n![](_page_21_Picture_6.jpeg)\n\nFigure 23 Creation of a Notional Amount Group\n\nWithin a group, the lower and upper bounds of notional amounts can be used to establish a notional range. If there is a need to establish a discontinuous range of amounts within a group, this can be achieved by creating new rows while editing a single notional amount group. The group details can be accessed via clicking on the arrow icon.\n\nNote: The notional amounts are expressed in the home currency of the Liquidity Provider (entity using the ADS).\n\nThe \"Delete\" option can be used to remove notional ranges that are not required. The lower and upper bounds are both included in the range of notional amounts.\n\nThe system does not restrict the creation of groups with overlapping amounts as these groups could have applicability in different rules.\n\nA list of all configured notional amount ranges assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n![](_page_22_Picture_0.jpeg)\n\n# 3.8 Time Period Groups\n\nFor products involving a maturity date/tenor, it is possible to encapsulate maturity ranges within the Time Period Groups. Tenors are defined as a range of maturities, with both \"From\" and \"To\" values included. A tenor can form part of different groups to allow their use in different rules.\n\nTime periods specific for Money Market (MM) instruments (OVERNIGHT or TOMNEXT) cannot be combined with FX specific time periods (SPOT, 15 MONTHS, 21 MONTHS or 10 YEARS) within one Time Period Group.\n\nGroups to hold maturity ranges can be created, removed and renamed similarly to other Configuration Groups. The list of available Time Periods appears after typing the space key.\n\n|            | Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups >> |          |  |               |                |                      |                     |                |\n|------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|--|---------------|----------------|----------------------|---------------------|----------------|\n|            |                                                                                                                                                                                      | $\\alpha$ |  | $\\rightarrow$ |                |                      |                     |                |\n| Name       | Time Periods                                                                                                                                                                         |          |  |               | No. of Members | No. of Routing Rules | No. of Margin Rules |                |\n|            |                                                                                                                                                                                      |          |  |               |                |                      |                     | ◇ Ⅲ            |\n| Enter Name | Select To<br>Select From<br>$\\widehat{\\phantom{0}}$                                                                                                                                  | $\\vee$   |  |               |                |                      |                     | $\\times \\sqrt$ |\n|            | Q                                                                                                                                                                                    |          |  |               |                |                      |                     |                |\n|            | UNLIMITED                                                                                                                                                                            |          |  |               |                |                      |                     |                |\n|            | TODAY                                                                                                                                                                                |          |  |               |                |                      |                     |                |\n|            | OVERNIGHT                                                                                                                                                                            |          |  |               |                |                      |                     |                |\n|            | TOMORROW                                                                                                                                                                             |          |  |               |                |                      |                     |                |\n|            | TOMNEXT                                                                                                                                                                              |          |  |               |                |                      |                     |                |\n|            | SPOT                                                                                                                                                                                 |          |  |               |                |                      |                     |                |\n|            | SPOTNEXT                                                                                                                                                                             |          |  |               |                |                      |                     |                |\n|            | 1 WEEK                                                                                                                                                                               |          |  |               |                |                      |                     |                |\n|            |                                                                                                                                                                                      |          |  |               |                |                      |                     |                |\n\nFigure 24 Time Period Groups\n\nA tenor range can be defined within a group. The ability to add discontinuous tenor ranges is provided via the plus icon in the Time Period detail view.\n\nFor example, there could be a need to route tenors between 1 WEEK to 1 MONTH to a different market link group. Here the ability to create discontinuous tenor ranges comes in handy, as maturities from TODAY to 1 WEEK (and) 1 MONTH to 2 MONTHS are routed similarly.\n\n| ADS Configuration Groups Pricing Routing Rules Margin Rules | <b>Settings</b><br>Audit log                                                                                |                           |                                               |\n|-------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|---------------------------|-----------------------------------------------|\n| Requester Groups Requesters                                 | Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups |                           | Notional Amount Groups Time Period Groups >>> |\n| ← Time Period Groups > Group Market Link 1 Tenors           |                                                                                                             |                           |                                               |\n|                                                             | From                                                                                                        | $\\sim$<br>To              |                                               |\n|                                                             | <b>TODAY</b>                                                                                                | 1 WEEK                    |                                               |\n|                                                             | 1 MONTH                                                                                                     | Select To<br>$\\checkmark$ |                                               |\n|                                                             |                                                                                                             | $Q$ 2M                    |                                               |\n|                                                             |                                                                                                             | 2 MONTHS                  |                                               |\n|                                                             |                                                                                                             |                           |                                               |\n|                                                             |                                                                                                             |                           |                                               |\n\nFigure 25 Time Period Group with discontinuous time ranges.\n\nA list of all configured time ranges assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n![](_page_23_Picture_0.jpeg)\n\n# 3.9 RFS Algorithm Groups\n\nRFS Algorithm Groups are used for the route option \"Market Link\" or \"Order to RFS\" by which the conditions are defined how a price provided by a market link provider will be forwarded to the requester. Two parameters are considered: the time elapsed since the request was received and forwarded to the back-to-back market link providers, and the number of quotes returned by the market link providers.\n\n| Requester Groups Requesters Providers Provider Groups |                           | Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups |   | RFS Algorithm Groups >>> |\n|-------------------------------------------------------|---------------------------|-----------------------------------------------------------------------------------------------------------------------------|---|--------------------------|\n| ← RFS Algo Groups > Group Exotics                     |                           |                                                                                                                             |   |                          |\n|                                                       | Request Runtime (seconds) | Number of Quotes                                                                                                            |   |                          |\n|                                                       |                           |                                                                                                                             | Û |                          |\n|                                                       |                           |                                                                                                                             | û |                          |\n|                                                       |                           |                                                                                                                             |   |                          |\n\nFigure 26 Market Link Algorithm definition.\n\nIn the example above, if the Market Link Algorithm \"Exotics\" is used in a pricing rule, a request will be sent to the market link providers and waits for quotes from at least 3 different providers within the first 5 seconds before starting to stream the best quote to the requester. If after 7 seconds, there have not been 3 competitive quotes, 2 competitive quotes will be sufficient to stream the best quote to the requester.\n\nA list of all configured RFS algorithm parameters assigned to groups can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n# 3.10 Fixing Reference Groups\n\nIn case NDF and NDS requests are priced, inter alia, based on Fixing Reference conditions then the Fixing Reference Groups feature can be used. The Fixing Reference Groups are only relevant for ADS RFS or Order Rules and are not applicable to ADS SEP Rules.\n\nGroups of Fixing References can be created, removed, and renamed similarly to other ADS Configuration Groups\n\n![](_page_24_Picture_0.jpeg)\n\n|                    |                                                      | Q | $\\rightarrow$ |                |                      |                     |                                        |\n|--------------------|------------------------------------------------------|---|---------------|----------------|----------------------|---------------------|----------------------------------------|\n| Name               | <b>Fixing References</b>                             |   |               | No. of Members | No. of Routing Rules | No. of Margin Rules |                                        |\n|                    |                                                      |   |               |                |                      |                     | $\\hat{\\varphi}$ $\\widehat{\\mathbf{u}}$ |\n| <b>BRL Fixings</b> | USD/BRL BRLD1 $\\times$ (USD/BRL BRLD2 $\\times$ ) bri |   |               |                |                      |                     | $\\times$ V                             |\n|                    |                                                      |   |               |                |                      |                     |                                        |\n|                    | GBP/BRL NDF Asiatica                                 |   |               |                |                      |                     |                                        |\n|                    | $\\sqrt{}$ USD/BRL BRLO1                              |   |               |                |                      |                     |                                        |\n|                    |                                                      |   |               |                |                      |                     |                                        |\n|                    | $\\sqrt{150/8}$ RL BRL02                              |   |               |                |                      |                     |                                        |\n|                    | USD/BRL BRL03                                        |   |               |                |                      |                     |                                        |\n|                    | USD/BRL BRL10                                        |   |               |                |                      |                     |                                        |\n|                    | USD/BRL BRL11                                        |   |               |                |                      |                     |                                        |\n|                    | USD/BRL PTAX (BRL09)                                 |   |               |                |                      |                     |                                        |\n|                    | USD/BRL Pontos sobre PTAX                            |   |               |                |                      |                     |                                        |\n\nFigure 27 Fixing Reference Groups\n\nThe configured Fixing Reference Groups are then available in the \"Fixing Reference\" drop down menu when defining the rules and rules templates for NDF and NDS. The default value \"Any\" set during rule creation encompasses all fixing references.\n\nA list of all available Fixing References with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n## 3.11 Order Strategy Groups\n\nVarious order types can be grouped together in custom Order Strategy Groups.\n\nCurrently available order strategies are Market Orders, Limit Orders, Stop Orders, Fixing Orders, Loop Orders, If-Done Orders, OCO Orders, ALGO Orders and Call Orders.\n\n| Requester Groups<br>stration start | Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Croups Currency Couple Groups Notional Amount Groups Time Period Groups RPS Algorithm Groups Fixing Reference Groups |          |  |     |                | Order $\\gg$<br>Strategy        |                     |     |\n|------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|--|-----|----------------|--------------------------------|---------------------|-----|\n| Name                               | <b>Order Strategies</b>                                                                                                                                                                                       | $\\alpha$ |  | mb. | No. of Members | Groups<br>No. of Routing Rules | No. of Margin Rules |     |\n| Stop and Limit                     | $\\boxed{\\text{Stop Order } \\times}$ (Limit Order $\\times$ )                                                                                                                                                   |          |  |     |                |                                |                     | p ⊕ |\n|                                    | ALGO Order<br>Call Order<br>Fixing Order<br>If-Done Order<br>√ Limit Order<br>Loop Order<br>Market Order<br>OCO Order                                                                                         |          |  |     |                |                                |                     |     |\n|                                    | Stop Order                                                                                                                                                                                                    |          |  |     |                |                                |                     |     |\n\nFigure 28 Creation of an Order Strategy Group.\n\nWhile grouping various order types, the user should consider, if they have common pricing routes or margin types which are supported. Rule validations will only allow selection of pricing or margin rules, if they are supported for all selected order strategies, negotiations and products configured in the specific rule.\n\n![](_page_25_Picture_0.jpeg)\n\nA list of all available Order Strategies with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n# 3.12 Negotiation Groups\n\nNegotiations currently supported in ADS are RFS (Request for Streams), Orders (Single Bank Orders) or SEP (Streaming Executable Prices).\n\nPlease note, that Negotiations, Products and Order Strategies selected in pricing or margin rules are validated and only commonly supported pricing routes or margin types will be selectable.\n\n|                 | > ADS Configuration Groups Pricing Routing Rules Margin Rules Settings Audit log<br>Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups RFS Algorithm Groups Fixing Reference Groups |          |               |                |                      |                     |    |\n|-----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|---------------|----------------|----------------------|---------------------|----|\n| istration Start |                                                                                                                                                                                                                                                                                                                    | $\\alpha$ | $\\rightarrow$ |                | Groups               |                     |    |\n| Name.           | Negotiations                                                                                                                                                                                                                                                                                                       |          |               | No. of Members | No. of Routing Rules | No. of Margin Rules |    |\n|                 |                                                                                                                                                                                                                                                                                                                    |          |               |                |                      |                     | ☆面 |\n| RFS and Order   | Belect Negotiations                                                                                                                                                                                                                                                                                                |          |               |                |                      |                     |    |\n|                 | Orders<br>RFS<br>SEP                                                                                                                                                                                                                                                                                               |          |               |                |                      |                     |    |\n|                 |                                                                                                                                                                                                                                                                                                                    |          |               |                |                      |                     |    |\n\n#### Figure 29 Creation of a Negotiation Group.\n\nA list of all available Negotiation types with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or re-assigning memberships via CSV upload.\n\n## 3.13 Manual Routing Groups\n\n360T ADS facilitates the routing of client requests to physical traders who are using the 360T Trader Worksheet (TWS) to manually provide prices. Based on the conditions of the routing rules, it might be necessary to route a request to a pre-defined group of traders. These can be defined in the section \"Manual Routing Groups\". The Manual Routing Groups are not relevant for ADS SEP Rules.\n\nAll new users are automatically assigned to the \"DEFAULT\" Group. A user is not automatically removed from the Default group after reassignment to a custom group.\n\n| ADS Configuration Groups | Pricing Routing Rules Margin Rules Settings Audit log                                                                                                                                                                                     |                             |                      |        |\n|--------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------|----------------------|--------|\n|                          | Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Groups Notional Amount Groups Time Period Groups RFS Algorithm Groups Fixing Reference Groups<br>$\\Omega$<br>÷. | Manual<br>Routing<br>Groups |                      |        |\n| Name                     | Group Members                                                                                                                                                                                                                             | No. of Members              | No. of Routing Rules |        |\n| DEFAULT                  | 360TBANKAPACTrader1, 360TBANKAPACTrader2, 360TBANKAPACTrader3, 360TBANKAPACTrader5, 360TBANKAPACTrader4                                                                                                                                   |                             |                      | $\\phi$ |\n| Manual Croup 1           | 360TBANKAPAC.Trader1                                                                                                                                                                                                                      |                             |                      | ◇ 回    |\n| Enter Name               | Select Group Members                                                                                                                                                                                                                      |                             |                      |        |\n|                          | 360TBANKAPAC.Trader1<br>360TBANKAPAC.Trader2<br>360TBANKAPAC.Trader3<br>360TBANKAPAC.Trader4<br>360TBANKAPAC.TraderS                                                                                                                      |                             |                      |        |\n\nFigure 30 Manual Routing Group configuration.\n\n![](_page_26_Picture_0.jpeg)\n\nPer default, ADS re-publishes an incoming request to the Default or custom manual trading group in certain scenarios:\n\n- 1. CRM relationship was requested but not yet accepted by the provider: republish to Default Group.\n- 2. Rule is missing for a request: send to Default Group.\n- 3. Route is set to Pricing Server, but pricing server rejects or is not available, or a republish timeout is configured in ADS instance, or the Request has a Comment and a republish is configured in ADS Instance, or the Request is unsupported by the Pricing Server (e.g. trade-on-behalf request, FX Prolongation/Early Settlement, Historical Rollover): request goes to the Manual Routing Group configured for the rule.\n- 4. Route is set to Market Link and for any reason pricing is not available and a republish timeout is configured in ADS instance: request goes to the Manual Routing Group configured for the rule.\n- 5. Offline confirmations (RFS or EMS Directed Negotiations), which always go to manual intervention, are treated like the Manual intervention rule of the corresponding product.\n- 6. A request on a specific trading venue, e.g. SEF, EU MTF or UK MTF will be only routed to traders enabled for the respective trading venue.\n- 7. If none of the traders is logged in, request is not shown.\n\nA list of all Manual Traders with group membership information can be downloaded as a CSV file. This CSV file can be used for creating new groups or modifying memberships via CSV upload.\n\n# 3.14 Margin Groups\n\nA feature of the ADS is the configuration of margins which can be applied to the price received from a price source before delivering the end price to the customers.\n\nMargins can be organized and structured within the enhanced Auto Dealer Suite and denoted in different ways e.g. percent, pips, or a fixed amount.\n\nDifferent margins can be created and applied to requests, in conjunction with other conditions. For example, margin tiers can be established by requester (customer) groups and then used in rules across all products for the said customers.\n\nMargin tiers can be achieved by first creating different Margin Groups as shown in the below illustration.\n\n![](_page_27_Picture_0.jpeg)\n\n![](_page_27_Figure_2.jpeg)\n\n#### Figure 31 Margin Groups and their types.\n\nThe green plus icon enables the creation of margin tiers. The margin group name can be edited to have something meaningful that is instantly recognizable when defining rules.\n\nThe \"Delete\" option facilitates the removal of margin groups that are not needed anymore.\n\nThe enhanced ADS offers several margin types which can be categorized into either a \"constant margin\" or \"variable margin\".\n\nConstant margin means that a pre-defined margin will be added/deducted from the base price. This margin can either be expressed in pips, in percent or a fixed amount in the home currency, according to the selection in this field.\n\nVariable margin maintains the price constant while the margin changes during the price negotiation based on the market price.\n\n## 3.14.1 Creation of margin groups.\n\nFollowing margin types can be selected while creating a new margin group with margin values:\n\n- Pips\n- Percent\n- Hybrid: allows the spot bid/offer margin to be defined in pips and the forward bid/offer margin or the swap bid/offer margin to be set in percent. Hybrid margin type is available for FX Spot, Forward and Swap products.\n- Fixed Amount (Home CCY): margin is added to/deducted from the opposite amount after which it is converted into a markup/markdown in pips of the effective rate provided to the subsidiary/customer. Due to the defined precision of the exchange rate, depending on the notional amount of the trade, the fixed amount margin might have no impact at all (large notional) or lead to an abnormal effective rate (small notional).\n- Variable: allows to set fix bid and fix ask prices for Spot or Forward and applicable only to Market Link and Pricing Server routes. Minimum Variable Margin threshold can contain the value UNLIMITED or any negative or positive number (Pips).\n- Annual Percentage: allows to define percentage margin on an annual basis and differentiate the margin based on the maturity of the forward\n\n![](_page_28_Picture_0.jpeg)\n\nand/or swap transactions. The defined spot or forward percentage margin is extended with pro rata addition where the defined percentage rate is multiplied by maturity/360.\n\nDepending on the margin type selected, the system highlights margin value entries supported for this margin type in a light grey colour:\n\n|                | ADS Configuration Groups Pricing Routing Rules Margin Rules Settings Audit log |                        |                 |                 |               |               |                     |                           |                                                                                                                                                                                                                                |                              |                        |\n|----------------|--------------------------------------------------------------------------------|------------------------|-----------------|-----------------|---------------|---------------|---------------------|---------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|------------------------|\n| stration Start |                                                                                |                        |                 |                 |               |               |                     |                           | Requester Groups Requesters Providers Provider Groups Product Groups Activation Period Groups MM Currency Groups Currency Couple Groups Notional Amount Groups Time Period Groups RFS Aleorithm Groups Fixing Reference Groups | $M$ aritin $\\gg$<br>Groups   |                        |\n|                |                                                                                |                        |                 |                 |               |               |                     | $\\rightarrow$             |                                                                                                                                                                                                                                |                              |                        |\n| Name           | Margin Type                                                                    | <b>Bid Spot Margin</b> | Ask Spot Margin | Min Spot Spread | Fix Bid Price | Fix Ask Price | Min Variable Margin | <b>Bid Forward Margin</b> | Ask Forward Margin                                                                                                                                                                                                             | % Forward Margin Calculation | <b>Bid Swap Margin</b> |\n| Tier 1 Clients | Variable                                                                       |                        |                 |                 |               |               | <b>UNLIMITED</b>    |                           |                                                                                                                                                                                                                                |                              |                        |\n|                |                                                                                |                        |                 |                 |               |               |                     |                           |                                                                                                                                                                                                                                |                              |                        |\n\nFigure 32 Example of supported margin values.\n\nWhen either \"Pips\", \"Percent\" or \"Fixed Amount\" is selected from the \"Margin Type\" field, the margins for spots, forwards, swaps, futures, interest rate products and commodities can be entered. This allows the same Margin Group to be re-used across product types. Depending on the product in the incoming request, the system applies the appropriate margin.\n\nThe details of each margin which can be added per margin group are listed below:\n\nBid Spot Margin: This margin will be deducted from the spot price when the client requests a sell quote. The margin is expressed in pips based on intervals of one decimal place, in percent with a maximum of 3 decimal places or as a fixed amount in the home currency. This margin can be set up for the instrument FX spot, FX forward, NDF and Block-Trade.\n\nAsk Spot Margin: This margin will be added to the spot price when the client requests a buy quote. The margin is expressed in pips based on intervals of one decimal place in percent with a maximum of 3 decimal places or as a fixed amount in the home currency. This margin can be set up for the instrument FX spot, FX forward, NDF and Block-Trade.\n\nMin. Spot Spread: The minimum spot spread is applied to the spot bid and offer rate for two-way SEP prices. It is expressed in pips based on intervals of one decimal place. If configured, the minimum spread check is done per each band of each stream separately before applying possible margins. Note that minimum spreads are only considered for the routing rules \"Pricing Server\" and \"Market Link\" and only for SEP negotiation, it will be ignored for other negotiation types and their products.\n\nFix Bid Price: Non-changing bid price can be defined for FX Spot and FX Forward instruments. The bid price provided to the customer remains unchanged throughout the RFQ negotiation. Fix Bid Price for FX Forward instruments refers to the Forward rate while the underlying FX Spot price might fluctuate. In the case of a market link scenario, the FX Spot bid price is taken from the market link provider. If the price request is routed to the Pricing Server,\n\n![](_page_29_Picture_0.jpeg)\n\nthe FX Spot bid price offered to the customer corresponds to the FX Spot bid rate as provided by the Pricing Server.\n\nFix Offer Price: Non-changing offer price can be defined for FX Spot and FX Forward instruments. The offer price provided to the customer remains unchanged throughout the RFQ negotiation. Fix Offer Price for FX Forward instruments refers to the Forward rate while the underlying FX Spot price might fluctuate. In the case of a market link scenario, the FX Spot offer price is taken from the market link provider. If the price request is routed to the Pricing Server, the FX Spot offer price exposed to the customer corresponds to the FX Spot offer rate as provided by the Pricing Server.\n\nMin Variable Margin: A threshold margin (in PIPS) which prevents quotation if margin fluctuates below the specified amount due to varying market prices.\n\nBid Forward Margin: This margin will be deducted from the forward points when the client requests to sell forward. The margin is expressed in pips based on intervals of 3 decimal places, in percent with a maximum of 3 decimal places. This margin can be set up for the instrument FX forward, NDF and Block-Trade.\n\nAsk Forward Margin: A margin will be added to the forward points when the client requests to buy forward. The margin is expressed in pips based on intervals of 3 decimal places, in percent with a maximum of 3 decimal places. This margin can be set up for the instrument FX forward, NDF and Block-Trade.\n\n% Forward Margin Calculation: relevant only for Percent and Hybrid margin types. Allows to define, if the percentage margin is calculated based on forward points or on spot rate of a forward request.\n\nBid Swap Margin: For any swap request, ADS applies only swap margin (either bid or offer based on the side of the action). In the case of a sell swap request, a defined `bid swap margin` will be deducted from the swap points of the non-spot leg. In case both legs are non-spot, for ex. near leg= today, far leg = 1 month, then the margin will be deducted from the swap points of the far leg.\n\nThe margin can be defined in terms of a) pips based on intervals of 3 decimal places, b) in percent with a maximum of 3 decimal places or c) as a fixed amount in the home currency.\n\nNote: Spot and Forward margins (as well as other margin parameters) are not used for FX Swap requests. Similarly, swap margins are only regarded to calculate margins for swap requests and are not effective for any other instrument.\n\nAsk Swap Margin: For any swap request, ADS applies only swap margin (either bid or offer based on the side of the action). In case of a buy swap request, the defined `offer swap margin` will be deducted from the swap points of the non-spot leg. In case both legs are non-spot, for example near leg= today, far leg = 1 month, then the margin will be deducted from the swap points of the far leg.\n\n![](_page_30_Picture_0.jpeg)\n\nThe margin can be defined in terms of a) pips based on intervals of 3 decimal places, b) in percent with a maximum of 3 decimal places or c) as a fixed amount in the home currency.\n\nNote: Spot and Forward margins (as well as other margin parameters) are not used for FX Swap requests. Similarly, swap margins are only regarded to calculate margins for swap requests and are not effective for any other instrument.\n\n% Swap Margin Calculation: relevant only for Percent and Hybrid margin types. Allows to define, if the percentage margin is calculated based on swap points or on spot rate of a Swap request. Bid Swap Margins expressed in percentage terms are calculated using the spot rate.\n\nApply Spot Margin to Uneven Swaps: this option allows to apply a margin to the spot rate of uneven swaps. The margin will be applied to the side and swap leg with the higher amount. It is available for pips, percentage, and hybrid margin types.\n\n- Example 1: Client buys 3 million EUR/USD spot and sells 2 million EUR/USD in 1 month. Since the client buys the overhanging amount, the defined Offer Spot Margin will be added to the spot rate. The application of the swap point margin is unchanged, in this case the Bid Swap Margin on the all-in far rate.\n- Example: Client buys 2 million EUR/USD spot and sells 3 million EUR/USD in 1 month. Since the client sells the overhanging amount, the defined Bid Spot Margin will be subtracted from the spot rate. The application of the swap point margin is unchanged, in this case also the Bid Swap Margin on the all-in far rate.\n\nBid Future Margin: This margin will be deducted from the FX Future price when the client requests a sell quote. The margin is expressed in pips based on intervals of one decimal place, in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.\n\nAsk Future Margin: This margin will be added to the FX Future price when the client requests a buy quote. The margin is expressed in pips based on intervals of one decimal place in percent with a maximum of 3 decimal places or as a fixed amount in the home currency.\n\nInterest Margin: A margin will be added to the interest rate if the client requests a quote for a loan; or deducted if the client requests a quote for a deposit. The margin is expressed in basis points (using the selection \"pips\") or in percent. It can be set up for money market instruments Loan and Deposit.\n\nBid Commodity Margin: Available for Fixed amount and Percentage margin and applicable only to RFS Commodity Asian Swap and RFS Commodity Bullet Swap. Margin will be deducted from the unit price.\n\n![](_page_31_Picture_0.jpeg)\n\nAsk Commodity Margin: Available for Fixed amount and Percentage margin and applicable only to RFS Commodity Asian Swap and RFS Commodity Bullet Swap.\n\nIn case a specific margin field should not be needed across all margin groups, the user can simply hide it from the margin groups table. For example, Min Spot Spread applicable only to SEP negotiations can be removed from the view by right-clicking on the Min Spot Spread column header and accessing the columns options menu:\n\n| Bid Spot Margin<br>Margin Type<br>Ask Spot Margin<br>Min Spot Spread<br>Name<br>0.0<br>0.0<br>0.0<br>Tier 1 Clients<br>Hybrid<br>0.00<br>Fixed Amount (Home CCY)<br>0.00<br>0.0<br>Zero Fixed Am | Fix Bid Price<br>>> A - E<br>>> F-N<br>>> others | Fix Ask Price<br>Min Variable Margin<br>√ Apply Spot Margin to Uneven Swap<br>V Ask Commodity Margin | Bid Forward<br><b>bo</b> |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------|------------------------------------------------------------------------------------------------------|--------------------------|\n|                                                                                                                                                                                                  |                                                  |                                                                                                      |                          |\n|                                                                                                                                                                                                  |                                                  |                                                                                                      |                          |\n|                                                                                                                                                                                                  | Ø Hide This Column \"Min Spot\"<br>Spread\"         | Ask Forward Margin<br>√ Ask Future Margin<br>Ask Spot Margin<br>√ Ask Swap Margin                    |                          |\n|                                                                                                                                                                                                  |                                                  | √ Bid Commodity Margin<br>$\\sqrt{ }$ Bid Forward Margin                                              |                          |\n\nFigure 33 Margin groups: hiding margin columns.\n\nThe order of the columns can be changed by dragging and dropping a column to the desired place.\n\nOnce the groups for the different parameters are setup, as described in the previous sections, they can be used to define the routing rules describing the conditions, price source destination and margins.\n\nA list of all Margins with their corresponding group names can be downloaded as a CSV file. This CSV file can be used for creating new margin groups or modifying margin values via upload. The uploaded margin values will be validated for compatibility with the margin type defined for each margin group. Additionally, the number of decimal places for each margin will be checked, similar how it is done in the GUI.\n\n![](_page_32_Picture_0.jpeg)\n\n# 4 Pricing Routing Rules\n\nThe menu item \"Pricing Rules\" allows creation and management of various pricing routing rules across all products and negotiations using the previously created ADS Configuration Groups as rule parameters.\n\n|                | $Q \\vee$ Type to filter |                    |           | Q Search        | <b>xx</b> Save As | $\\checkmark$          | $\\widehat{\\mathbb{U}}$ |\n|----------------|-------------------------|--------------------|-----------|-----------------|-------------------|-----------------------|------------------------|\n| #              | $\\vee$ Rule ID          | Comment            | Requester | Negotiation     | Product           | <b>Order Strategy</b> |                        |\n| $\\mathbf 1$    | 2200                    | Blacklisted CCY pa | Any       | Any             | Any               | Any                   | 40 位                   |\n| $\\mathbf 2$    | 2201                    | Cut-off            | Any       | Any             | Any               | Any                   | 面<br>⊕                 |\n| $\\bar{\\rm{3}}$ | 2203                    | Manual             | Any       | Orders          | Spot, Forward     | Limit, Stop           | ⊕ □                    |\n| $\\overline{4}$ | 2208                    | auto pricing       | Any       | RFS, Order, SEP | Spot, Forward     | Limit, Stop           | 中 市                    |\n\nFigure 34 Pricing Routing Rules.\n\nA rule is a combination of:\n\n- Conditions, which define the specific criteria/constraints that trigger an action.\n- Outcome, which defines the action to be undertaken when the said criteria are met.\n\nEvery variation in a parameter that makes up a condition or an outcome results in a new rule.\n\nA list of all Pricing Routing Rules can be downloaded as a CSV file. This CSV file can be used as a template for creating new rules or updating existing rules.\n\nDownload and upload icons are available in the upper right corner of the page.\n\n![](_page_33_Picture_0.jpeg)\n\n# 4.1 Creation and Modification of Rules\n\nA new rule can be created by clicking on the green plus icon or by cloning an existing rule using the button. Multiple rules can be created or cloned before final confirmation with the \"Save\" icon. Please note that confirming with green checkbox icon does not yet save a rule, but it only allows to finish the creation mode of a single rule.\n\n| <b>Trading Venue</b><br>□ □ |\n|-----------------------------|\n|                             |\n|                             |\n| 40 10                       |\n| 40                          |\n| ⊕ □                         |\n| $\\times$                    |\n|                             |\n\n#### Figure 35 New Pricing Routing Rule.\n\nWhile creating a rule, options are offered for each parameter to select among the Configuration Groups that have been previously defined. In case a one-time exceptional value is required, which is not part of the group, individual values can be selected for certain rule parameters, such as Requester, Negotiation, Product, Order Strategy, Currency Couples, MM Currency, Fixing Reference, Manual Routing or Provider. It is however recommended to use ADS Configuration Groups to centrally manage the rules. The user can enter a text comment for each rule which helps to better understand the rule logic.\n\nThe parameters should be defined from the left side to the right side of the rules table. Pricing Route should be done after all criteria have been defined.\n\nIn case any condition of an existing rule is modified, and a specific pricing route has already been selected, not all values of the parameters will be available for selection, but only the values compatible with the selected pricing route. The route can be changed to \"No Pricing\" and adjusted as desired.\n\nFor each parameter, a drop-down is provided where a list of existent Configuration Groups or available individual values is displayed. The default setting \"Any\" is pre-selected in most of the columns. The default pricing route is \"No Pricing\". Once a rule is saved, a rule system ID is automatically generated.\n\nThe user can continue to add rule groups for the selected product group by proceeding with the \"Add Rule\" option.\n\nNote: The order (column #) of rules determines their priority top-down. The desired rule position can be adjusted via drag&drop feature. When a negotiation request meets the condition of several single rules or rules within the routing\n\n![](_page_34_Picture_0.jpeg)\n\nconfiguration, then the order dictates the priority. If a single rule should take precedence over other rules, then it must be placed above them.\n\nIf no rule is specified, the request will be sent to manual intervention to Bridge TWS to the Default Group of traders.\n\n\"Delete\" can be used to remove a rule group that is no longer needed.\n\nIf a rule must be adjusted, the user should double-click on the rule row or cell in the rule table and the edit mode will be activated.\n\nPricing Routing Rules can be created or modified via CSV upload and some of their parameters will be validated during the upload (see Table 1 Pricing Routing availability by Negotiation, Product and Order Type.). Rule ID is used as an indicator if a rule should be created (rule ID is empty or not found) or updated (rule ID is given and found).\n\nIn case of modification via CSV upload, it is recommendable to download the existing rules and simply adjust the parameters which should be changed (except from Rule ID and Position number). After upload, a results CSV file will be automatically generated and indicate in the column \"Results\" if the upload was successful. The rules will be pre-loaded in the GUI and only successful changes (Result = OK) can be either saved or discarded via \"Discard all changes\" icon. In case of any upload errors, the user can in this way discard all changes, adjust the file and repeat the upload until all rules successfully passed the validations.\n\nPlease note that when updating existing rules using the Rule ID as identifier, the rule position value will be ignored. This basically means that the order of rules cannot be modified via CSV upload. This must be done manually in the GUI by dragging and dropping the rules.\n\n# 4.2 Rule Parameters\n\nThe rule parameters for a new rule should be ideally entered from the left to the right side of the table. Certain parameters will be applied or not, depending on negotiation, product or order type. This means that in case negotiations or products are combined in Configuration Groups, the HTML ADS will only pick the values relevant for a specific request.\n\n## 4.2.1 Position number (#)\n\nRule position number is assigned automatically by the order of rule creation. It will be changed accordingly whenever it is dragged and dropped between two other rules. This is only possible if no sorting is applied on any of the rule headers. A rule with a lower rule position number will be applied before a rule with a higher rule number.\n\n![](_page_35_Picture_0.jpeg)\n\nA cloned rule will be automatically assigned the highest position number i.e. will appear at the bottom of all the rules.\n\n#### 4.2.2 The field is not mandatory for CSV upload and will be ignored. If new rules are created via CSV upload, they will be automatically appended with the highest position numbers, however in the same order as listed in the CSV file.Rule ID\n\nRule ID is a system ID granted automatically whenever a rule is created or cloned. It does not have an impact on rule application and is used for reference purposes e.g. while searching for Audit logs\n\nRule ID is used to identify the rule which should be modified via CSV upload. The value should be blank in case a new rule is created via upload.\n\n## 4.2.3 Comment\n\nComment field is only used for administrative purposes and has no impact on routing, meaning it is not a parameter as such. It can be used is search filters and for alphabetical column sorting.\n\nThis field can be left blank during CSV upload.\n\n## 4.2.4 Requester\n\nRequester means a legal entity or a parent entity with all underlying entities requesting a quote. It can be selected from previously defined Requester Groups or chosen individually from the list of all available counterparties in the drop-down. It is relevant for all rules.\n\nThe default value of this field is \"Any\" (meaning all requesters). The field must be populated in the CSV upload file.\n\n## 4.2.5 Negotiation\n\nNegotiation groups, value \"Any\" or individual values can be selected. It is one of the mains parameters used for pricing route or margin group/type validation. More information can be found in Chapter 3.12.\n\nThe field is mandatory for CSV upload.\n\n## 4.2.6 Product\n\nNegotiation groups, value \"Any\" or individual values can be selected. It is one of the mains parameters used for pricing route or margin group/type validation. More information can be found in Chapter 3.3.\n\nThe field is mandatory for CSV upload.\n\n![](_page_36_Picture_0.jpeg)\n\n## 4.2.7 Order Strategy\n\nOrder groups, value \"Any\" or individual order types can be selected. It will be used for pricing route or margin group/type validation for Orders. Order strategy value will be ignored for RFS or SEP negotiations. More information can be found in Chapter 3.11.\n\nThe field is mandatory for CSV upload.\n\n#### 4.2.8 Activation Period\n\nActivation period groups or value \"Any\" can be selected. More information can be found in Chapter 3.4. Activation period is not considered for RFS Cross Currency Portfolio product.\n\nThe field is mandatory for CSV upload.\n\n#### 4.2.9 Trading Venue\n\nThe parameter Trading Venue offers the possibility to differentiate the routing of a price request depending on the venue (EU MTF, UK MTF or OTC) where it was originated. This parameter will only be considered for the following RFS products: Fx Forward, Fx Swap, Block, Fx Time Option, Option, NDF or NDS. Default value is \"Any\".\n\nThe field is mandatory for CSV upload.\n\n![](_page_36_Figure_10.jpeg)\n\nFigure 36 Selecting a Trading Venue\n\n#### 4.2.10 Currency Couple\n\nCurrency couple can be set for all negotiations either to \"Any\" (all currency pairs) or a custom currency couple group can be chosen. This parameter will be considered for almost all negotiations and products, except from MM products and Cross Currency Portfolio.\n\nThe field is mandatory for CSV upload.\n\n![](_page_37_Picture_0.jpeg)\n\n## 4.2.11 MM Currency\n\nThis parameter (Any, custom Configuration Group, or a single value) will be considered only for Loan and Deposit requests but ignored for all other types of products. More information can be found in Chapter 3.5.\n\nThe field is mandatory for CSV upload.\n\n## 4.2.12 Requester Action\n\nRequester action (Any, Two-way, Buy or Sell) can be defined as routing condition for RFS requests for Spot, Forward, Swap, Options, NDF, NDS and Blocks. It will be ignored for other products configured within the same rule.\n\nThe field is mandatory for CSV upload.\n\n## 4.2.13 Notional Amount\n\nNotional amount (Any or custom Configuration Group) can be used as parameter for almost all RFS and Order products except from Commodity Asian Swap, Commodity Bullet Swap, Cross Currency Portfolio.\n\nThe field is mandatory for CSV upload.\n\n## 4.2.14 Overhang\n\nAdditional parameter called Overhang is available for uneven FX Swaps. Overhang is the difference between the notional of the far and the near leg which can be defined in the Notional Amount Groups, or \"Any\" value can be selected.\n\nThe field is mandatory for CSV upload.\n\n## 4.2.15 Swap/NDS Near Period\n\nRelevant only for Fx Swaps and NDS products and will be not considered for any other products configured within the same rule.\n\nThe field is mandatory for CSV upload.\n\n## 4.2.16 Time Period\n\nTime Period parameter (Any or custom Configuration Group) can be used to specify the outright period of an FX product, maturity of MM product or far leg period for Swaps and NDS. It is not relevant for Fx Futures, Metals and Commodities products or Cross Currency Portfolios. More information can be found in Chapter 3.8.\n\nThe field is mandatory for CSV upload.\n\n![](_page_38_Picture_0.jpeg)\n\n## 4.2.17 Fixing Reference\n\nFixing Reference (Any, custom Configuration Group or single value) can be defined as routing condition for NDF and NDS products for RFS and Orders negotiations. More information can be found in Chapter 3.10.\n\nThe field is mandatory for CSV upload.\n\n## 4.2.18 In Competition\n\n\"In competition\" is a criterion that can be used when the requester notifies the exclusive Liquidity Provider as part of the RFS message that the request was not submitted to any other competitor. The \"In competition\" parameter offers three different condition values (No=LP is not in competition; Yes=LP competes with other LPs, Any=no restriction).\n\nThe field is mandatory for CSV upload.\n\n# 4.3 Pricing Routing\n\nOnce rule conditions are defined, the price source must be specified through the selection of the Route option.\n\nRoutes available for combinations of selected negotiations, products and order types within a single rule will become selectable, whereas routes not supported for those combinations will be greyed-out. An overview of all pricing routes available per negotiation, product and order type can be found in Table 1. If negotiations, products, or order types are bundled in groups, only pricing routes common for the group elements will be selectable. For example, Manual Intervention route will not be available for any rule containing SEP negotiation.\n\nSome routes require additional configuration to become selectable, for example to select Pricing Server route, Pricing Server type must be selected.\n\n![](_page_38_Picture_12.jpeg)\n\nDefining Pricing Route is mandatory for CSV upload.\n\nFigure 37 Route column.\n\nThe following pricing routes are available:\n\n Manual Intervention: The incoming request is routed to the Trader Worksheet of the manual trader users (as defined in the 'Manual Routing Group' tab) for pricing. Note: If a request does not match the conditions of\n\n![](_page_39_Picture_0.jpeg)\n\nany destination rule, then the request is routed to traders who are members of Default Group (Manual Routing Groups).\n\n Pricing Server: The price basis is either the 360T price feed from the 360T Market Maker Cockpit, Infotec (market data), or a provider individual price feed through an adapter interface to the provider's pricing system. The Auto Dealer automatically sends a price back to the requester.\n\nNote: This route will only be selectable if any of the pricing servers from the column \"Pricing Server\" is defined.\n\n- Pricing Server with Acknowledgement: This route is available for Spot, Forward, Time Option and Swaps. It requires a manual trader to acknowledge the price parameters before the ADS quotes are forwarded to the requester. The trader has the option to manually adjust margins or Forward points for auto-priced quotes for the pricing side of the request.\n- No Pricing: The Auto Dealer does not forward the negotiation request to any price source. Consequently, the request is not sent to any manual traders. Therefore, quotes are not provided to the customer at all.\n- Market Link: The request is forwarded to a set of market link providers and a selected trading venue. Therefore, this rule is used in combination with the selections under Market Link Provider, Market Link Trading Venue and Market Link Algorithm (see more details below). The Auto Dealer continuously updates the pricing with the best quote available from the group of external providers. The execution of the requesting entity automatically executes a back-to-back deal with the best price available at the time of execution. Upon completion, two trades are generated: one between the requesting entity and the provider; and a second one between the provider and the external market maker (market link provider).\n\nNote: Only limited to spot and forward requests and to some defined currency pairs, requests are forwarded to the selected market link providers as two-way requests by default, irrespective of whether incoming requests are one sided (bid or ask) or two-way. Please contact 360T Client Advisory Services if you would like back-to-back requests to be routed to your Autodealer using the originating request`s selection.\n\n Forward Strip: This rule is only available for the RFS negotiation and product FX Forward and is a combination of Market Link and Pricing Server. The forward request is sent as an FX Spot request to the configured set of market link providers while the forward points are retrieved from an internal price source. The execution of the FX Forward by the requesting entity automatically executes a back-to-back FX Spot deal with the best price available at the time of execution. Upon completion, two trades are generated: an FX forward between the requesting entity and the provider; and a Spot trade between the\n\n![](_page_40_Picture_0.jpeg)\n\nproviding bank and the external market maker (market link provider). Note 1: Forward Strip requires an internal market data source to be configured. Please contact 360T Client Advisory Services if you would like to implement such a data source.\n\nNote 2: For some defined currency pairs, requests are forwarded as FX Spot to the selected market link providers as two-way requests by default, irrespective of whether incoming requests are one sided (bid or ask) or two-way. Please contact 360T Client Advisory Services if you would like back-to-back requests to be routed to your Autodealer using the originating request`s selection.\n\n- Block Strip: This rule is an extension of Forward Strips. In a block containing spots and forwards, the net FX Spot portion of the block request is sent to a configured set of market link providers, while the forward points are retrieved from an internal market data/pricing source. The execution of the block request by the requesting entity automatically executes a back-to-back FX Spot deal with the best price available at the time of execution. Upon completion, a trade is generated between the requesting entity and the provider bank for each leg contained in the block. A trade for the net spot amount is also generated between the provider bank and the external market maker (market link provider)\n- Time Option Forward: This rule is only available for the RFS product FX Time Option and is a Market Link strategy which generates two standard FX Forward requests back-to-back, picks the rate of the worst leg to automatically price the original request and when the original request is executed, executes back-to-back with the rate of the best leg. Upon completion, two trades are generated: an FX Time Option between the requesting entity and the provider bank; and a Forward trade between the provider bank and the external market maker (market link provider). The advantage is to enable completely automated pricing of Time Option Forwards without the need for the interbank market to support Time Option Forwards.\n- Order To RFS: The Market Order is forwarded to a group of providers using a request for quote. Upon completion, two trades are generated: one between the order-placing I-TEX entity and the in-house bank; and a second one between the in-house bank and the external market maker.\n- Order To Order: The order is forwarded to a provider as a new order. The execution of this order automatically executes a back-to-back deal at the moment of execution. Upon completion, two trades are generated: one between the order-placing I-TEX entity and the in-house bank; and a second one between the in-house bank and the external market maker.\n\nDepending on the pricing route selected, additional pricing routing settings are required such as:\n\n![](_page_41_Picture_0.jpeg)\n\nManual Routing Group is using the Manual Routing Groups as defined under the Configuration Groups. In case a Route \"Manual Intervention\" is defined, or in case a request that is routed to a pricing engine is republished for dealer intervention for any reason (e.g. disconnection of the external engine or reject from the external engine), then this parameter will be considered to route the request to the traders configured in the relevant Manual Routing Group. In case the traders of the Manual Routing Group are not logged into the 360T Trader Worksheet, then the request not shown to any manual trader (see also more details in Chapter 3.13).\n\nThe field is mandatory for CSV upload.\n\nProvider is used to define the bank basket to be used for the back-to-back request. You can select any group that was defined under Provider Groups. Please note that in case of orders sent back-to-back to a single provider, the Market Link Provider group must contain only one provider, or a single provider must be selected in the Provider column. Default value is \"Any\".\n\nThe field is mandatory for CSV upload.\n\nMarket Link (ML) Trading Venue is used to define whether the linked back-toback request in the context of a market link route should be executed on a specific 360T venue. It is not relevant for the product Fx Spot. Default value is \"OTC.\"\n\nThe field is mandatory for CSV upload.\n\nMarket Link Algorithm allows selecting the Algorithm group with regards to the duration of the request and number of quotes to be considered. Default value is \"Any\". The field is mandatory for CSV upload.\n\nLiquidity Limit defines the maximum limit (notional in Home Currency) of liquidity available in the Market Link route in case of SEP negotiation.\n\nPlease note that the amount defined as liquidity limit is not the maximum amount that respective SEP requester is allowed to trade. However, it determines the maximum amount the SEP requester can trade in one ticket.\n\nFor example, when the SEP provider defines liquidity limit as 2 million, the SEP requester could still receive prices for 5 million from the SEP provider. The price would be constructed by different quotes which are provided for tier sizes up to defined liquidity limit of 2 million.\n\nThis field is optional for the upload and will be per default to \"UNLIMTED\" if no value is provided in the CSV file.\n\nWholesale: Enabling this field allows the SEP provider to price streaming requests based on its market link providers` price feeds without an equivalent back-to-back trade being generated with the market. This way, SEP provider can create stream prices through their market link providers while at the same time can wholesale the position.\n\n![](_page_42_Picture_0.jpeg)\n\nPlease note that this feature is supported for the trades between a subsidiary and its parent entity i.e. when requester is a subsidiary (I-TEX) of the SEP provider.\n\nThis field is optional for the upload and will be per default to \"FALSE\" if no value is provided in the CSV file.\n\nUp To Wholesale Amount: When the wholesale option is enabled, the notional of SEP trades that do not generate a back-to-back trade with the market can be specified. For example, setting a value of 1 million would mean that any trades in the currency of the entity up to 1 million would be wholesaled while trades over this notional would be hedged via the market link. Trade notionals are calculated into the currency of the entity using the values in the \"Company Exchange Rates\" to validate the said wholesale limits.\n\nThis field is optional for the upload and will be per default to \"UNLIMITED\" if no value is provided in the CSV file.\n\nMin Execution Amount defines the minimum notional amount of the SEP execution which can be priced in the selected route.\n\nThis field is optional for the upload and will be per default to \"0,00\" if no value is provided in the CSV file.\n\nThe table below shows available pricing routes per negotiation, product and order strategy. If the parameter values are combined, only common pricing routes will be selectable. Combinations of parameters are not shown in the table.\n\n| Parameters      |                | Availability of Pricing Routes |                            |                           |                    |                        |                              |                   |\n|-----------------|----------------|--------------------------------|----------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|\n| Negotiat<br>ion | Product        | Strate<br>gy                   | Manual<br>Intervent<br>ion | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |\n| RFS             | Spot           | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Forward        | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Swap           | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Block          | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Time<br>Option | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Option         | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | NDF            | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n\n© 2024 – 360 Treasury Systems AG <sup>43</sup>\n\n![](_page_43_Picture_0.jpeg)\n\n| Parameters      |                                    | Availability of Pricing Routes |                            |                           |                    |                        |                              |                   |\n|-----------------|------------------------------------|--------------------------------|----------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|\n| Negotiat<br>ion | Product                            | Strate<br>gy                   | Manual<br>Intervent<br>ion | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |\n| RFS             | NDS                                | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Loan                               | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Deposit                            | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Metals<br>Outright<br>s            | n/a                            | Yes                        | Yes                       | No                 | No                     | No                           | Yes               |\n| RFS             | Metals<br>Quarterl<br>y Strips     | n/a                            | Yes                        | Yes                       | No                 | No                     | No                           | Yes               |\n| RFS             | Metals<br>Spreads                  | n/a                            | Yes                        | Yes                       | No                 | No                     | No                           | Yes               |\n| RFS             | Commo<br>dity Asia<br>Swap         | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Commo<br>dity<br>Bullet<br>Swap    | n/a                            | Yes                        | Yes                       | Yes                | No                     | No                           | Yes               |\n| RFS             | Future                             | n/a                            | Yes                        | No                        | Yes                | No                     | No                           | Yes               |\n| RFS             | Cross<br>Currenc<br>y<br>Portfolio | n/a                            | Yes                        | No                        | No                 | No                     | No                           | Yes               |\n| Order           | Forward                            | Algo<br>Order                  | No                         | Yes                       | No                 | No                     | No                           | Yes               |\n| Order           | NDF                                | Algo<br>Order                  | No                         | Yes                       | No                 | No                     | No                           | Yes               |\n| Order           | Spot                               | Algo<br>Order                  | No                         | Yes                       | No                 | No                     | No                           | Yes               |\n| Order           | Forward                            | Fixing<br>Order                | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |\n\n![](_page_44_Picture_0.jpeg)\n\n| Parameters      |         |                     | Availability of Pricing Routes |                           |                    |                        |                              |                   |  |  |  |\n|-----------------|---------|---------------------|--------------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|--|--|--|\n| Negotiat<br>ion | Product | Strate<br>gy        | Manual<br>Intervent<br>ion     | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |  |  |  |\n| Order           | Spot    | Fixing<br>Order     | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |\n| Order           | Forward | If<br>Done<br>Order | Yes                            | Yes                       | No                 | No                     | No                           | Yes               |  |  |  |\n| Order           | Spot    | If<br>Done<br>Order | Yes                            | Yes                       | No                 | No                     | No                           | Yes               |  |  |  |\n| Order           | Forward | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |\n| Order           | Future  | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |\n| Order           | Spot    | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |\n| Order           | Swap    | Limit<br>Order      | Yes                            | Yes                       | No                 | No                     | No                           | Yes               |  |  |  |\n| Order           | Forward | Loop<br>Order       | Yes                            | No                        | No                 | No                     | No                           | Yes               |  |  |  |\n| Order           | Spot    | Loop<br>Order       | Yes                            | No                        | No                 | No                     | No                           | Yes               |  |  |  |\n| Order           | Forward | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |\n| Order           | Future  | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |\n| Order           | Spot    | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |\n| Order           | Swap    | Market<br>Order     | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |\n| Order           | NDF     | Market<br>Orer      | Yes                            | Yes                       | No                 | Yes                    | Yes                          | Yes               |  |  |  |\n| Order           | Forward | OCO                 | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |\n| Order           | Spot    | OCO                 | Yes                            | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |  |\n\n![](_page_45_Picture_0.jpeg)\n\n| Parameters      |               | Availability of Pricing Routes |                            |                           |                    |                        |                              |                   |  |  |\n|-----------------|---------------|--------------------------------|----------------------------|---------------------------|--------------------|------------------------|------------------------------|-------------------|--|--|\n| Negotiat<br>ion | Product       | Strate<br>gy                   | Manual<br>Intervent<br>ion | Prici<br>ng<br>Serv<br>er | Mark<br>et<br>Link | Ord<br>er<br>to<br>RFS | Ord<br>er<br>to<br>Ord<br>er | No<br>Prici<br>ng |  |  |\n| Order           | Forward       | Stop<br>Order                  | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |\n| Order           | Future        | Stop<br>Order                  | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |\n| Order           | Spot          | Stop<br>Order                  | Yes                        | Yes                       | No                 | No                     | Yes                          | Yes               |  |  |\n| SEP             | Fx Spot       | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |\n| SEP             | Fx<br>Forward | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |\n| SEP             | NDF           | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |\n| SEP             | NDS           | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |\n| SEP             | Fx<br>Swap    | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |\n| SEP             | Fx<br>Future  | n/a                            | No                         | Yes                       | Yes                | No                     | No                           | Yes               |  |  |\n\nTable 1 Pricing Routing availability by Negotiation, Product and Order Type.\n\nFor Block requests HTML ADS checks the routing rules for the net amount as Spot (even if the block does not include Spot) and each leg of the block using the time period, amount and side defined on the leg. If all legs have the same routing defined, then the block is sent to that route. However, if legs have different routes, then the following priority is applied:\n\n- No pricing\n- Manual Intervention\n- Pricing Server with Acknowledgement\n- Pricing Server\n- Market Link\n- Block Strip\n\n![](_page_46_Picture_0.jpeg)\n\n# 5 Margin Rules\n\nMargin Rules creation and modification works in a similar way to Pricing Routing Rules (see Chapter 4.1). For each rule a set of conditions must be defined, which trigger the outcome. The same configuration groups can be used for margin rules, as for Pricing Routing Rules (see Chapter 4.2).\n\nMargin rules can be created or modified via CSV upload, similar to Pricing Routing Rules.\n\nNote: Margins are not yet supported for Metals Outrights, Metals Quarterly Strips, Metals Spreads, Commodity Asian Swap, Options or Cross Currency Portfolios. Therefore, those products should be excluded from any Product Configuration Group used for Margin Rules.\n\nThe default Margin Group value while creating a new rule is the value \"None\", however the system does not allow saving a rule with this value.\n\n![](_page_46_Figure_7.jpeg)\n\n#### Figure 38 Creation of Margin Rules.\n\nIn case any condition of an existing rule is modified, and a specific margin group type has already been selected, not all values of the parameters will be available for selection, but only the values compatible with the selected margin type.\n\nMargin groups of a specific margin type are available for combinations of selected negotiations, products and order types within a single rule. They will become selectable, whereas margin groups with margin types not supported for those combinations will be greyed-out in the \"Margin Group\" drop-down.\n\nAn overview of all margin types available per negotiation, product and order type can be found in Table 1. If negotiations, products, or order types are bundled in groups, only margin types/groups common for the group elements will be selectable. For example, a Fixed Amount margin will not be available for any rule containing SEP negotiation.\n\n![](_page_47_Picture_0.jpeg)\n\n| Parameters        |                                | Supported Margin Types |     |     |        |                 |          |             |  |\n|-------------------|--------------------------------|------------------------|-----|-----|--------|-----------------|----------|-------------|--|\n| Negotiation       | Order<br>Product               | Order<br>Strategy      | Pip | %   | Hybrid | Fixed<br>amount | Variable | Annual<br>% |  |\n| RFS               | Spot                           | n/a                    | Yes | Yes | Yes    | Yes             | Yes      | Yes         |  |\n| RFS               | Forward                        | n/a                    | Yes | Yes | Yes    | Yes             | Yes      | Yes         |  |\n| RFS               | Swap                           | n/a                    | Yes | Yes | Yes    | Yes             | No       | Yes         |  |\n| RFS               | Block                          | n/a                    | Yes | Yes | No     | Yes             | No       | No          |  |\n| RFS               | Time<br>Option                 | n/a                    | Yes | Yes | No     | Yes             | No       | Yes         |  |\n| RFS               | Option                         | n/a                    | No  | No  | No     | No              | No       | No          |  |\n| RFS               | NDF                            | n/a                    | Yes | Yes | Yes    | Yes             | No       | Yes         |  |\n| RFS               | NDS                            | n/a                    | Yes | Yes | Yes    | Yes             | No       | No          |  |\n| RFS               | Loan                           | n/a                    | Yes | Yes | No     | Yes             | No       | Yes         |  |\n| RFS               | Deposit                        | n/a                    | Yes | Yes | No     | Yes             | No       | Yes         |  |\n| RFS               | Metals<br>Outrights            | n/a                    | No  | No  | No     | No              | No       | No          |  |\n| RFS               | Metals<br>Quarterly<br>Strips  | n/a                    | No  | No  | No     | No              | No       | No          |  |\n| RFS               | Metals<br>Spreads              | n/a                    | No  | No  | No     | No              | No       | No          |  |\n| RFS               | Commodity<br>Asia Swap         | n/a                    | No  | Yes | No     | Yes             | No       | No          |  |\n| RFS               | Commodity<br>Bullet<br>Swap    | n/a                    | No  | Yes | No     | Yes             | No       | No          |  |\n| RFS               | Future                         | n/a                    | Yes | Yes | No     | Yes             | No       | No          |  |\n| RFS               | Cross<br>Currency<br>Portfolio | n/a                    | No  | No  | No     | No              | No       | No          |  |\n| Orders<br>Forward |                                | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders            | Forward                        | Fixing<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |\n\n![](_page_48_Picture_0.jpeg)\n\n| Parameters  |                  | Supported Margin Types |     |     |        |                 |          |             |  |\n|-------------|------------------|------------------------|-----|-----|--------|-----------------|----------|-------------|--|\n| Negotiation | Order<br>Product | Order<br>Strategy      | Pip | %   | Hybrid | Fixed<br>amount | Variable | Annual<br>% |  |\n| Orders      | Forward          | If Done<br>Order       | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Forward          | Limit<br>Order         | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Forward          | Loop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Forward          | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Forward          | OCO                    | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Forward          | Stop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | Fixing<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | If Done<br>Order       | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | Limit<br>Order         | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | Loop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | OCO                    | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Spot             | Stop<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Swap             | Limit<br>Order         | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | Swap             | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | NDF              | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | NDF              | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |\n\n![](_page_49_Picture_0.jpeg)\n\n| Parameters  |                  | Supported Margin Types |     |     |        |                 |          |             |  |\n|-------------|------------------|------------------------|-----|-----|--------|-----------------|----------|-------------|--|\n| Negotiation | Order<br>Product | Order<br>Strategy      | Pip | %   | Hybrid | Fixed<br>amount | Variable | Annual<br>% |  |\n| Orders      | Future           | Limit<br>Order         | Yes | Yes | Yes    | Yes             | No       | No          |  |\n| Orders      | Future           | Market<br>Order        | Yes | Yes | Yes    | Yes             | No       | No          |  |\n| Orders      | Future           | Stop<br>Order          | Yes | Yes | Yes    | Yes             | No       | No          |  |\n| Orders      | NDF              | Algo<br>Order          | Yes | Yes | Yes    | No              | No       | No          |  |\n| Orders      | NDF              | Market<br>Order        | Yes | Yes | Yes    | No              | No       | No          |  |\n| SEP         | Fx Spot          | n/a                    | Yes | Yes | No     | No              | No       | No          |  |\n| SEP         | Fx Forward       | n/a                    | Yes | Yes | No     | No              | No       | No          |  |\n| SEP         | NDF              | n/a                    | Yes | Yes | No     | No              | No       | No          |  |\n| SEP         | NDS              | n/a                    | Yes | Yes | No     | No              | No       | No          |  |\n| SEP         | Fx Swap          | n/a                    | Yes | Yes | No     | No              | No       | No          |  |\n| SEP         | Fx Future        | n/a                    | Yes | Yes | No     | No              | No       | No          |  |\n\nTable 2 Margin Type availability by Negotiation, Product and Order Type.\n\n# 5.1 Margin Application\n\nThe output of the margin rules conditions is application of the defined Margin Group. In addition to the margin values taken from the pre-configured Margin Groups, an additional margin multiplicator called \"% Transformation\" and a setting \"Round to Full Pips\" can be defined per each rule.\n\n![](_page_50_Picture_0.jpeg)\n\n![](_page_50_Figure_2.jpeg)\n\n#### Figure 39 Applying margin transformation.\n\nThe % margin transformation is expressed as a percentage. Negative values are allowed, which implies a reduction of the original margin.\n\nWhen a rule is applied, the margins specified within the rule are first calculated. The margin transformation is then determined and added onto or subtracted from the original margin.\n\nFor example, if the Bid Spot margin specified within a rule is 10 pips and the margin transformation is 5%, this would translate into a total margin of 10.5 pips (10 + 10 x 5%) being applied to the request.\n\nIf the Bid Spot margin was specified in percent, say 1%, and the margin transformation is -5%, this would imply a total applied margin of 0.95% (1% - 5% x 1%) on top of the supplied bid price.\n\nIf the Bid Spot margin within a rule was a fixed amount, say EUR 10 per transaction and the margin transformation is 5%, then the total margin applied on a transaction would be EUR 10.5 (10 + 10 x 5%).\n\nRound to full pips is per default set to False but can be changed to True meaning that all outgoing rates are going to be rounded to full pips. Depending on auto-dealer configuration, it can be either rounding favouring provider or mathematical rounding.\n\nApart from margin transformation and rounding to pips, it is possible to change the way margins are applied on block requests. Per default, ADS picks the spot and forward margin based on the rule that matches the furthest leg and the net amount. The same forward margin is then applied to all legs. 360T CAS can activate the new \"Split Block Margin\" logic where margin is derived separately on each leg as well as the net spot amount. If the setting is enabled for ADS, the Spot margin is derived based on the Spot margin rule of the net Spot amount of the block and the side of the net Spot. Similarly, the forward margin is derived based on the forward margin rule for each leg of the block separately i.e. considering the amount on the leg, the time period and the side of the leg to do the matching.\n\n![](_page_51_Picture_0.jpeg)\n\n# 6 Rule Search\n\nThe rule search is a useful tool to search for a specific rule that applies to an incoming request at a specific time or to have a better overview of rules for a specific requester, currency pair or any other parameter.\n\n|              |                                   |                    |           | $Q \\vee$ Product= |                         |                    |         | Q Search      | Siz Save As     | Spot EURUSD Margin Rules                 | 而                  |             |         |      |\n|--------------|-----------------------------------|--------------------|-----------|-------------------|-------------------------|--------------------|---------|---------------|-----------------|------------------------------------------|--------------------|-------------|---------|------|\n|              | Currency Couple = EURUSD $\\times$ |                    |           | Fx Swap           |                         |                    |         |               |                 |                                          |                    |             |         | €    |\n|              | Rule ID                           | Comment            | Requester | Any<br>NDF        |                         |                    |         | Period        | Currency Couple | % Transformation                         | Margin Group       | MM Currency | Request |      |\n|              | 3553                              | Special margin set | Platinium | Only Spot         |                         |                    |         | ay-2 weeks    | Majors          | 10                                       | Basis Fixed Amount | Any         | Any     | 40   |\n| $\\mathbf{r}$ | 3593                              |                    | Platinium | <b>NDS</b>        | Metals Quarterly Strips |                    |         | 114           | Majors          | 50                                       | Basis Fixed Amount | Any         | Any     | 60   |\n| 10           | 3597                              |                    | Platinium | Fx Forward        |                         |                    |         | 6M            | Majors          | 350                                      | Basis Fixed Amount | Any         | Any     | 40   |\n| 17           | 3601                              |                    | Platinium | Deposit           |                         |                    |         | 1V            | Majors          | 470                                      | Basis Fixed Amount | Any         | Any     | 40 百 |\n| 19           | 3605                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | $O-2M$  | 1Y-18M        | Majors.         | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Basis Fixed Amount | Any         | Any     | 中 市  |\n| 22           | 3558                              |                    | cold      |                   | Any                     | Spot, Forward, NDF | $O-1M$  | Today-2 weeks | <b>Majors</b>   | 950                                      | Basis Fixed Amount | Any         | Any     | 电音   |\n| 26           | 3609                              |                    | Cold      |                   | Any                     | Spot, Forward, NDF | $0-1M$  | $2W-1M$       | Majors          | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Basis Fixed Amount | Any         | Any     | 40 面 |\n| 30           | 3623                              |                    | Cold      |                   | Any                     | Spot, Forward, NDF | 0.1M    | 1M·6M         | Majors          | 200                                      | Basis Fixed Amount | Any         | Any     | 60   |\n| 35           | 3782                              |                    | cold      |                   | Any                     | Spot, Forward, NDF | $O-1M$  | Today-2 weeks | Majors          | 950                                      | Basis Fixed Amount | Any         | Any     | 电音   |\n| 39           | 3786                              |                    | Cold      |                   | Any                     | Spot, Forward, NDF | $0-1M$  | $2W-1M$       | <b>Majors</b>   | 200                                      | Basis Fixed Amount | Any         | Any     | 40 面 |\n|              | 3582                              |                    | Platinium |                   | Any                     | Only Spot          | $1M-SM$ | Today-2 Weeks | Malors.         | 170                                      | Basis Fixed Amount | Any         | Any     | 中 市  |\n|              | 3594                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | $1M-SM$ | 2W-1M         | Majors          | 200                                      | Basis Fixed Amount | Any         | Any     | 电音   |\n| 11           | 3598                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | 1M-5M   | 1M-6M         | Majors          | 400                                      | Basis Fixed Amount | Any         | Any     | 40 面 |\n| 13           | 3606                              |                    | Platinium |                   | Any                     | Spot, Forward, NDF | $1M-SM$ | 1Y-18M        | Malors          | 750                                      | Basis Fixed Amount | Any         | Any     | 中 百  |\n\n#### Figure 40 Rule Search.\n\nThe user has the option to define, apply and save individual advanced filters for pricing or margin rules using a set of operators.\n\nDepending on the rule parameter type, a search operator will automatically appear, e.g.=, ≠; ≥,>, ≤, < or other such as IN or NOT IN, which give the option to include or exclude a set of values in one data field at the same time. An additional IN condition value can be added after adding a comma to the first IN condition value added. For example, it is possible to search for the rules applicable to multiple Requester Groups. After confirming a filter by pressing the Enter key, additional filters can be added (AND relationship) to narrow down the results.\n\nA search filter can be saved under a custom name and loaded, whenever needed.\n\n|        |                |                                       | Q v Type to filter         |                          |                                     | Q Search ( $\\frac{1}{24}$ Save As ) |                          | $\\vee$ $\\overline{w}$ |                        |\n|--------|----------------|---------------------------------------|----------------------------|--------------------------|-------------------------------------|-------------------------------------|--------------------------|-----------------------|------------------------|\n|        |                | Currency Couple = EURUSD $\\times$ And | $Product = FX Spot \\times$ |                          |                                     |                                     |                          |                       |                        |\n|        | $\\vee$ Rule ID | Comment                               | Requester                  | Margin Group             | Product                             | Negotiation                         | <b>Activation Period</b> | <b>Trading Venue</b>  | <b>Currency Couple</b> |\n|        | 3819           | Test3                                 | TOD Clients                | Basis Percent            | Any                                 | Any                                 | Any                      | Any                   | Any                    |\n|        | 3582           |                                       | Platinium                  | Basis Fixed Amount       | Only Spot                           | Any                                 | Any                      | Any                   | <b>Majors</b>          |\n|        | 3583           |                                       | Platinium                  | Basis Fixed Amount       | Spot, Forward, NDF                  | Any                                 | Any                      | Any                   | Majors                 |\n|        | 3584           |                                       | <b>Platinium</b>           | Basis Percent            | Spot, Forward, NDF                  | Any                                 | Any                      | Any                   | Majors                 |\n|        | 3593           |                                       | <b>Platinium</b>           | Basis Fixed Amount       | Spot, Forward, NDF                  | Any                                 | Any                      | Any                   | Majors                 |\n|        | 3594           |                                       | Plat                       | New Name of Query        | X                                   | Any                                 | Any                      | Any                   | Majors                 |\n|        | 3595           |                                       | Plat                       | Spot EURUSD Margin Rules |                                     | Any                                 | Any                      | Any                   | Majors                 |\n|        | 3596           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |\n| 10     | 3597           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |\n| 11     | 3598           |                                       | plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |\n| 12     | 3599           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |\n| $13 -$ | 3606           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |\n| 14     | 3600           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |\n| 15     | 3603           |                                       | Plat                       |                          |                                     | Any                                 | Any                      | Any                   | Majors                 |\n| 16     | 3602           |                                       | Plat                       |                          | $(\\times$ Cancel<br>$\\checkmark$ ok | Any                                 | Any                      | Any                   | Majors                 |\n|        |                |                                       |                            |                          |                                     |                                     |                          |                       |                        |\n\nFigure 41 Saving Rule Search Filters.\n\n![](_page_52_Picture_0.jpeg)\n\n# 7 Audit Log\n\nAll changes of the ADS configuration, including their date & time, user who did the change and its details can be tracked in Audit Log.\n\n| <b>Audit log</b><br>ADS Configuration Groups<br><b>Pricing Routing Rules</b><br><b>Margin Rules</b> |                                          |                                                                                             |               |  |\n|-----------------------------------------------------------------------------------------------------|------------------------------------------|---------------------------------------------------------------------------------------------|---------------|--|\n|                                                                                                     |                                          | Q                                                                                           | $\\rightarrow$ |  |\n| Log Date & Time                                                                                     | <b>User</b>                              | Description                                                                                 |               |  |\n| Mar 19, 2024, 1:32:18                                                                               | GroupG.HybridA                           | Configuration Disabled                                                                      |               |  |\n| Mar 10, 2024, 11:34:58.                                                                             | GroupG.TreasurerA                        | Removed Routing Rule 4085 (Position:1)                                                      |               |  |\n| Mar 10, 2024, 11:34:43.                                                                             | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | Product Group(Name:Spot, Forward, NDF, Id:3571) Members Added (Fx Swap)                     |               |  |\n| Mar 10, 2024, 11:33:26.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 position from: 7 to: 1                                            |               |  |\n| Mar 10, 2024, 11:33:06.                                                                             | GroupG.Treasurer.A                       | Updated Routing Rule 4085 Requester: Any                                                    |               |  |\n| Mar 10, 2024, 11:32:01.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 Route: Pricing Server                                             |               |  |\n| Mar 10, 2024, 11:32:01.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 pricing server: Default PS (id:ax6455-a055a-gymhbes4-cqc_DEFAULT) |               |  |\n| Mar 10, 2024, 11:32:01.                                                                             | GroupG.TreasurerA                        | Updated Routing Rule 4085 Negotiation: RFS                                                  |               |  |\n| Mar 10, 2024, 11:19:37.                                                                             | GroupG.HybridA                           | Updated Routing Rule 3857 Route: Pricing Server with Ack                                    |               |  |\n| Mar 10, 2024, 11:19:04.                                                                             | GroupG.HybridA                           | Updated Routing Rule 3857 Route: Pricing Server                                             |               |  |\n| Mar 10, 2024, 11:18:55.                                                                             | GroupG.HybridA                           | Updated Routing Rule 3857 Negotiation: RFS                                                  |               |  |\n| Mar 10, 2024, 11:16:08.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Route: Pricing Server                                             |               |  |\n| Mar 10, 2024, 11:15:46.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Negotiation: RFS                                                  |               |  |\n| Mar 10, 2024, 11:14:59.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Manual routing: Any                                               |               |  |\n| Mar 10, 2024, 11:14:59.                                                                             | GroupG.HybridA                           | Updated Routing Rule 7081 Min Execution Amount: 0                                           |               |  |\n| Mar 10, 2024, 11:14:59                                                                              | GroupG.HybridA                           | Updated Routing Rule 7081 Wholesale: Disabled                                               |               |  |\n| Mar 10, 2024, 11:14:59                                                                              | GroupG.HybridA                           | Updated Routing Rule 7081 Wholesale Amount: UNLIMITED                                       |               |  |\n\n#### Figure 42 HTML ADS Audit Log.\n\nAudit Log has a text search feature which can be for example used to find all modifications of a single rule by simply searching its ID:\n\n|                        |                     |                                                       | Q Rule 7081                                              | $\\rightarrow$ |\n|------------------------|---------------------|-------------------------------------------------------|----------------------------------------------------------|---------------|\n| Log Date & Time        | <b>User</b>         | <b>Description</b>                                    |                                                          |               |\n| Mar 10, 2024, 11:16:08 | GroupG.HybridA      | Updated Routing Rule 7081 Route: Pricing Server       |                                                          |               |\n| Mar 10, 2024, 11:15:46 | GroupG.HybridA      | Updated Routing Rule 7081 Negotiation: RFS            |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Manual routing: Any         |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Min Execution Amount: 0     |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Wholesale: Disabled         |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      |                                                       | Updated Routing Rule 7081 Wholesale Amount: UNLIMITED    |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Liquidity Limit: UNLIMITED  |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Fixing reference: Any       |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      |                                                       | Updated Routing Rule 7081 Market link trading venue: OTC |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Trading venue: Any          |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      |                                                       | Updated Routing Rule 7081 Swap/NDS Near period: Any      |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Requester action: Any       |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 In competition: Any         |                                                          |               |\n| Mar 10, 2024, 11:14:59 | GroupG.HybridA      | Updated Routing Rule 7081 Overhang: Any               |                                                          |               |\n| $$                     | compared to deviate | the description of the model theoleced construct that |                                                          |               |\n\nFigure 43 Audit Log: Text Search.\n\n![](_page_53_Picture_0.jpeg)\n\n# 8 Auto Dealer Control\n\nAll functionalities described in this section work for RFS Auto Dealer, Orders Auto Dealer and SEP Auto Dealer.\n\nThe enhanced Auto Dealer Control allows the user to start and stop the Auto Dealer by changing the \"Auto Dealer Enabled\" switch to enabled (started) or disabled (stopped) as desired and as shown in Figure 38. The activation or deactivation will have immediate effect.\n\n![](_page_53_Picture_5.jpeg)\n\nFigure 44 Auto Dealer Control enabling and disabling Auto Dealer switch\n\nTo have the Auto Dealer enabled only during specific times of the day, it is possible to enable or disable the Auto Dealer Schedule by changing the \"Auto Dealer Schedule Enabled\" switch accordingly and by keying in the start and stop times, which determine when the\n\nAuto Dealer should be active. The times are expressed in UTC and the equivalent local times of the current user are displayed in brackets as shown in Figure 44.\n\n| RFS Auto Dealer Schedule Enabled | $\\vee$ Enabled |\n|----------------------------------|----------------|\n|                                  |                |\n\n#### Figure 45 Auto Dealer Control - Auto Dealer Schedule Enabled switch\n\n| RES Auto Dealer Start Time | $06:00$ $\\Box$ |\n|----------------------------|----------------|\n|                            | (07:00 CEST)   |\n| RFS Auto Dealer Stop Time  | 22:00<br>eb    |\n|                            | (23:00 CEST)   |\n\nFigure 46 Auto Dealer Control – Auto Dealer Start and Stop Times\n\n![](_page_54_Picture_0.jpeg)\n\nImportant: To start the Auto Dealer as per schedule times, the \"Auto Dealer Start Enabled\" switch must be enabled too. Otherwise, the Auto Dealer stops at the scheduled time but must be manually started.\n\n| RFS Auto Dealer Start Enabled | $\\bigcirc$ Enabled |\n|-------------------------------|--------------------|\n|                               |                    |\n\nFigure 47 Auto Dealer Control - Auto Dealer Start Enabled switch\n\nIn case the Schedule is enabled but the Start is disabled, the system displays an alert as shown in Figure 48.\n\n![](_page_54_Picture_5.jpeg)\n\nFigure 48 Auto Dealer Control – Auto Dealer Start related alert\n\nIn case a customized schedule is required, it is possible to make use of a new functionality: \"Day by Day Definition\". By changing the \"Day by Day Definition\" switch to enabled, a week schedule table is displayed and the daily time periods in which the Auto Dealer should be active can be keyed in as needed. The schedule table is displayed as shown in Figure 49.\n\n| Day by Day Definition | $\\sim$ Fnabled |\n|-----------------------|----------------|\n|                       |                |\n\nFigure 43 Auto Dealer Control - Day by Day Definition switch\n\n![](_page_55_Picture_0.jpeg)\n\n| Monday                           | 07:00 Start UTC<br>(Monday, 08:00 CEST) | Stop UTC 3 19:00<br>(Monday, 20:00 CEST) |\n|----------------------------------|-----------------------------------------|------------------------------------------|\n|                                  |                                         |                                          |\n| Tuesday<br>(Tuesday, 08:00 CEST) | 07:00 Start UTC                         | 19:00<br>Stop UTC<br>$\\Rightarrow$       |\n|                                  |                                         | (Tuesday, 20:00 CEST)                    |\n| Wednes (Wednesday, 08:00 CEST)   | 07:00 Start UTC                         | Stop UTC $\\implies$ 19:00                |\n|                                  |                                         | (Wednesday, 20:00 CEST)                  |\n| Thursday                         | 07:00 Start UTC                         | Stop UTC<br>19:00                        |\n|                                  | (Thursday, 08:00 CEST)                  | (Thursday, 20:00 CEST)                   |\n| Friday                           | $07:00$ $(3)$<br>Start UTC              | Stop UTC $\\bigcirc$ 19:00                |\n|                                  | (Friday, 08:00 CEST)                    | (Friday, 20:00 CEST)                     |\n\n#### Figure 49 Auto Dealer Control Day by Day Definition schedule table\n\nWhen \"Day by Day Definition\" is enabled, all times setup in the week schedule table overwrite the times previously setup as shown in Figure 49. In case \"Day by Day Definition\" is further disabled, the previously defined daily schedule will be valid again.\n\nImportant notes:\n\n- a) Start and Stop times can be defined by either:\n- moving the sliders through the time range on a given day of the week; or\n- typing the times directly into the Start and Stop fields; or\n- using +/- keys.\n\nOnly one start time and only one stop time are allowed on the same day:\n\nb) A time range can be removed by right-clicking on the range line:\n\n![](_page_56_Picture_0.jpeg)\n\n| Saturday<br>(Saturday, 08:00 CEST) | 07:00 Start UTC | Stop UTC ● 19:00 ●<br>(Saturday, 20:00 CEST) |\n|------------------------------------|-----------------|----------------------------------------------|\n|                                    |                 | Remove this range                            |\n\nFigure 50 Removing a time range from Day by Day Definition schedule table\n\nc) Time ranges can be inserted by clicking twice on grayed range line; first click adds start time and second click adds stop time:\n\n| O 07:00 Start UTC    |  |  |\n|----------------------|--|--|\n|                      |  |  |\n| (Friday, 08:00 CEST) |  |  |\n\nFigure 51 Adding a time range in Day by Day Definition: First click (start time)\n\n|        | O 07:00 Start UTC    | Stop UTC 3 19:00     |\n|--------|----------------------|----------------------|\n| Friday | (Friday, 08:00 CEST) | (Friday, 20:00 CEST) |\n|        |                      |                      |\n\nFigure 52 Adding a time range in Day by Day Definition: Second click (stop time) on same day\n\nd) Start time and stop time can be set on the same day as shown above or among different days as shown below:\n\n| Sunday | 22:00 Start UTC<br>(Sunday, 23:00 CEST) |                      |\n|--------|-----------------------------------------|----------------------|\n|        |                                         | Stop UTC ● 19:00 ●   |\n| Monday |                                         | (Monday, 20:00 CEST) |\n\nFigure 53 Adding a time range in Day by Day Definition: first click on one day and second click on another day\n\ne) Setting stop time further in subsequent days allows Auto Dealer to run continuously and stop on a specific weekday:\n\n![](_page_57_Picture_0.jpeg)\n\n| Sunday   | 22:00 Start UTC<br>(Sunday, 23:00 CEST) |                                            |\n|----------|-----------------------------------------|--------------------------------------------|\n| Monday   |                                         |                                            |\n| Tuesday  |                                         |                                            |\n| Wednes   |                                         |                                            |\n| Thursday |                                         |                                            |\n| Friday   |                                         | Stop UTC ● 19:00 ●<br>(Friday, 20:00 CEST) |\n| Saturday |                                         |                                            |\n\n#### Figure 54 Example of Auto Dealer Schedule setup to run continuously from Sunday to Friday\n\nSetting of the Start time in the past in Day-by-Day Definition does not activate the Auto Dealer if the Auto Dealer is already disabled at the time of setting this start time. However, if the Auto Dealer is enabled, the stop time set for the future will be applied.\n\n![](_page_58_Picture_0.jpeg)\n\n# 10 Contacting 360T\n\n#### Global Support\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n#### Germany\n\n360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### Asia Pacific South Asia\n\n#### Singapore\n\n360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756\n\n#### Middle East\n\n#### United Arab Emirates\n\n360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134\n\n#### EMEA Americas\n\n#### USA\n\n360 Trading Networks, Inc 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902\n\n#### India\n\nThreeSixty Trading Networks (India) Pvt Ltd Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437", "metadata": {"lang": "en"}}]