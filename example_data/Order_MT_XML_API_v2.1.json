[{"id": "1", "text": "![](_page_0_Picture_0.jpeg)\n\n# Order Market Taker XML API\n\nRules of Engagement\n\nAPI Version: 2.1.12 Platform Release: 4.22\n\n360 Treasury Systems AG Grüneburgweg 16-18 / Westend Carrée D-60322 Frankfurt am Main Tel: +49 69 900289 0 Fax: +49 69 900289 29 Email: <EMAIL> Commercial Register Frankfurt, No. HRB 49874 Executive Board: <PERSON>, <PERSON>, <PERSON> Supervisory Board: <PERSON>\n\n# **Table of Contents**\n\n| 1 | Introduction<br>3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |                                                                                                                                                     |  |  |  |\n|---|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|\n| 2 | Communication<br>2.1<br>Secure Connection<br>.<br>2.2<br>Firewall Configuration<br>.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | 4<br>4<br>4                                                                                                                                         |  |  |  |\n| 3 | Security<br>5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |                                                                                                                                                     |  |  |  |\n| 4 | Access Points<br>4.1<br>Trade Intention Submission<br>4.1.1<br>Side Interpretation<br>.<br>4.1.2<br>Fields<br>*******<br>Regulatory Data Fields<br>.<br>*******<br>FX Outright Fields<br>*******<br>FX Swap Fields<br>.<br>*******<br>NDF Fields<br>.<br>*******<br>NDS Fields<br>.<br>*******<br>FX Option Fields<br>.<br>*******<br>Loan Deposit Fields<br>*******<br>Metals Outright Fields<br>*******<br>Metals Spread Fields<br>.<br>********<br>Metals Quarterly Strip Fields<br>.<br>4.1.3<br>Multi-Trade Intentions<br>.<br>4.1.4<br>Fixing References<br>.<br>4.1.5<br>Custom Fields<br>.<br>4.2<br>Batch Trade Intention Submission<br>.<br>4.3<br>Trade Intention Status<br>4.4<br>Batch Trade Intention Status<br>.<br>4.5<br>Trade Intention Cancellation<br>.<br>4.6<br>Trade Intention Accepting<br>.<br>4.7<br>Fetching the Resulting Trades<br>.<br>4.8<br>Combined Status and Trades Request<br>.<br>4.9<br>Amend Request<br>4.10 Withdraw Request | 6<br>6<br>6<br>6<br>9<br>11<br>11<br>11<br>12<br>12<br>13<br>13<br>14<br>14<br>15<br>15<br>16<br>16<br>17<br>18<br>18<br>18<br>19<br>20<br>20<br>21 |  |  |  |\n| 5 | Error Handling<br>5.1<br>Authentication<br>5.2<br>Trade Intention submitting<br>.<br>5.3<br>Other Services                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | 23<br>23<br>23<br>23                                                                                                                                |  |  |  |\n| 6 | Future Considerations<br>24                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |                                                                                                                                                     |  |  |  |\n| 7 | Appendix<br>7.1<br>Cutoff Time for Options<br>.<br>7.2<br>Fixing Sources for NDF/NDS<br>.<br>7.3<br>CFI Codes                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | 25<br>25<br>26<br>29                                                                                                                                |  |  |  |\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 2\n\n| 7.4 | Soap Sample Request<br>30                         |\n|-----|---------------------------------------------------|\n| 7.5 | Sample Code<br>.<br>32                            |\n|     | 7.5.1<br>Sample Code for the REST interface<br>32 |\n|     | 7.5.2<br>Sample Code for SOAP actions<br>.<br>34  |\n| 7.6 | Web Browser Plugin Connectivity Test<br>35        |\n|     |                                                   |\n\n#### **[8 Version Log](#page-38-0) 38**\n\n# <span id=\"page-3-0\"></span>**1 Introduction**\n\nThe Order Market Taker XML API allows customers to upload orders into different applications on the 360T platform. This interface also allows the client to query state of the submitted orders and any executed trades. An optional feature that pushes order status change notifications to a customer web service is available.\n\n# <span id=\"page-4-0\"></span>**2 Communication**\n\nAll calls to the Order XML API should be made with a HTTP call over secure TCP socket. Most access points allow using both GET and POST method. It is however recommended to use POST.\n\nThe current specification allows only querying the status of the trade intention. Pushing these changes to a web service would is planned in the future.\n\nAll access points in this document reference the 360T development environment. The production environment differs only with it's ip address. When integration and UAT tests are complete, the steps described in the next section should be repeated in production as well, so that the client is granted access to the resources there. 360T integration and production environments are completely separate systems, but work in the exact same way to allow for comprehensive development and testing.\n\n# <span id=\"page-4-1\"></span>**2.1 Secure Connection**\n\nConnections coming via Internet should be secured by establishing a HTTPS connection to the 360T data center. Certificates have to be provided by 360T integration team to the network team of the client.\n\n# <span id=\"page-4-2\"></span>**2.2 Firewall Configuration**\n\nFor the connections we use the following IP addresses:\n\n| Environment     | Connection       | URL                      | IP            | Port |\n|-----------------|------------------|--------------------------|---------------|------|\n| Integration/UAT | Internet (plain) | stagingarea-int.360t.com | ************  | 6445 |\n| Production      | Internet (plain) | stagingarea.360t.com     | ************* | 6445 |\n\nTable 2.1: 360T IP addresses\n\n# <span id=\"page-5-0\"></span>**3 Security**\n\nThe communication with the Order XML API is done through a secure HTTPS connection. The clients receive a private key from 360T that identifies them and grants them access to their respective entities in the 360T system. When starting to use the XML API, these steps need to be considered:\n\n- 1. Open your company firewall for outgoing calls to IP ************ port 6445. This is the location of the development environment of the Order XML API and your system should be able to access it.\n- 2. Provide 360T with the IP address or range of addresses of your system or proxy, through which you'll be accessing the Order XML API. 360T will open their firewall respectively.\n- 3. Request a dedicated customer private key. This key will be delivered together with 360T's server certificates.\n- 4. Add the provided private key in you application's *keystore* and the 360T certificates in your *truststore*. Thus you'll be able to validate our server and will be authorized to use the resources.\n\n# <span id=\"page-6-0\"></span>**4 Access Points**\n\nThe Order XML API can use REST or SOAP interface for communication. There are several access points through which intentions can be submitted, canceled, their status checked and the resulting trades received.\n\nThe SOAP interface uses the same host and port as the REST interface, and the requests can be submitted via POST method. The SOAP action must be set in the POST request header *\"soapaction\"*. The SOAP message body must only contain the respective XML message.\n\n# <span id=\"page-6-1\"></span>**4.1 Trade Intention Submission**\n\nSubmitting trade intentions (orders) via the REST interface can be done through this access point:\n\n#### https://************:6445/tradeIntention\n\nREST requests can be sent through both GET and POST methods. In both cases the trade intention XML should be passed as value of the \"trade\" parameter.\n\nThe SOAP action for trade intention submission is:\n\n#### urn:360t:orderapi:tradeIntentionSubmit\n\n#### <span id=\"page-6-2\"></span>**4.1.1 Side Interpretation**\n\nThe side of an order is defined using the \"actionType\" field (as defined in subsection [4.1.2\\)](#page-6-3), which defines whether the client is the buyer or seller of the underlying instrument.\n\n- The order's side refers to the direction of the order from the clients perspective.\n- The side is defined in terms of the base currency in the case of FX products or to the deposit / loan currency in the case of Loan Deposits. For example, BUY for the Symbol EUR/USD, means buying EUR and selling USD.\n- For FX Swaps/Metals Spread, the side refers to the far leg.\n- The side does not refer to the notional currency. The behaviour of side interpretation is not configurable. It is not possible to upload orders with the side defined in terms of the notional currency.\n\n#### <span id=\"page-6-3\"></span>**4.1.2 Fields**\n\nThe fields of the trade intention are described in the following table. You can find the exact format of the XML messages in the example trades provided with this document.\n\n| Field Name | Mandatory | Description                                                                   | Example                     |\n|------------|-----------|-------------------------------------------------------------------------------|-----------------------------|\n| externalId | 1         | Unique key that identifies the trade intention                                | 123456                      |\n| groupId    | 0         | GroupId for multi-trade intentions                                            | group1                      |\n| editable   | 0         | Specifies if the Intention would be editable by a<br>treasurer. Default: true | false                       |\n| comment    | 0         | Allows the client to upload a comment that will<br>be shown in the 360T GUI.  | This is an example comment. |\n\n| commentInternal             | 0 | Specifies if the comment should be classified as<br>internal or external (visible to banks). Default:<br>true                                                                                                                  | false              |\n|-----------------------------|---|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|\n| individual                  | 1 | Individual, in whose name the trade intention<br>would be imported. This should be a 360T iden<br>tifier.                                                                                                                      | COMPANY.Treasurer1 |\n| orderGroupId                | 0 | Identifier of the order group into which this order<br>should be added.                                                                                                                                                        | orderGroup1        |\n| orderBook                   | 0 | Name of the order book for this trade intention<br>(only relevant for legal entities with more than<br>one order book).                                                                                                        | COMPANY1           |\n| autoExecBankBasketSizeMin   | 0 | Minimum number of banks used in negotiation.                                                                                                                                                                                   | 3                  |\n| autoExecWaitTimeMin         | 0 | Minimum wait time (in seconds) to wait before<br>execution can occur after the negotiation starts.                                                                                                                             | 3                  |\n| autoExecRfsDurationMax      | 0 | Maximum time (in seconds) before the negotia<br>tion times out.                                                                                                                                                                | 3                  |\n| autoExecNegotiationWaitTime | 0 | Time to wait before negotiation is allowed<br>to start.<br>The accepted formats are seconds,<br>'HH:MM',<br>'HH:MM:SS',<br>'DD.MM.YYYY<br>HH:MM:SS',<br>'DD.MM.YYYY<br>HH:MM',<br>'DD.MM.YYYY' and it should be in the future. | 1                  |\n| autoExecSpreadToleranceMin  | 0 | Minimum allowed distance from mid price.                                                                                                                                                                                       | 1                  |\n| autoExecSpreadToleranceMax  | 0 | Maximum allowed distance from mid price.                                                                                                                                                                                       | 4                  |\n| autoExecSpreadToleranceType | 0 | Type of spread tolerance. Possible values: AB<br>SOLUTE or PERCENT                                                                                                                                                             | ABSOLUTE           |\n| fixedFeeType                | 0 | Fee type. Can be PER TICKET or PER MIL<br>LION.                                                                                                                                                                                | PER TICKET         |\n| fixedFeeCurrency            | 0 | Fee currency.                                                                                                                                                                                                                  | EUR                |\n| fixedFeeValue               | 0 | Amount of fee.                                                                                                                                                                                                                 | 30000              |\n| fixedFeeCustodian           | 0 | The subject to whom fee is payed.                                                                                                                                                                                              |                    |\n| fixedFeeCustodianCheck      | 0 | When set to true, EMS will wait for the price<br>of the custodian when processing auto-execution<br>RFS negotiations. If no price is available from<br>the custodian, then the negotiation will timeout.<br>Default: false     | true               |\n| fxSecurityConversion        | 0 | Flag marking the order as FX Security Conver<br>sion.                                                                                                                                                                          |                    |\n| preferredProviders          | 0 | Comma-separated list of 360T legal entity names<br>of the preferred providers.                                                                                                                                                 | COMPANY1, COMPANY2 |\n| legalEntity                 | 0 | 360T Trade-as or Trade-on-Behalf legal entity<br>name                                                                                                                                                                          | COMPANY.TOB1       |\n| limitRate                   | 0 | Limit rate for a limit order. If missing an inten<br>tion could be executed as RFQ call or a market<br>order.                                                                                                                  | 0.41               |\n| stopRate                    | 0 | Stop rate for a stop order. If missing an intention<br>could be executed as RFQ call or a market order.                                                                                                                        | 0.41               |\n\n| fixingOrderDate      | 0 | Fixing date for a fixing order. If missing an in<br>tention could be executed as RFQ call or a mar<br>ket order. Time should be specified only in case<br>fixingOrderReference=NONE                                                                                                                                                                        | 2011-10-22<br>2011-10-22 10:00 |\n|----------------------|---|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|\n| fixingOrderReference | 0 | Fixing reference. Please the accepted mnemon<br>ics below as a reference of the accepted values.<br>This field is ignored if fixingOrderDate is miss<br>ing. The times defined for the selected mnemonic<br>will be added to the date specified in fixingOrder<br>Date. If NONE is selected, the time would be<br>taken from fixingOrderDate. Default: ECB | ECB                            |\n| ocoOrder             | 0 | Indicates if this order is part of an OCO multi<br>leg order. If present, this element should have a<br>boolean value. All orders with the same groupId<br>should have the same value for this element.                                                                                                                                                    | true                           |\n| rollingSpot          | 0 | If set to true, this property indicates that the or<br>der will be uploaded as a rolling spot order, if the<br>value date is equal to SPOT. If set to false, a for<br>ward order with a non-rolling value date will be<br>created. If present, this element should have a<br>boolean value. If not specified, the value defaults<br>to true.               | false                          |\n| spotRate             | 0 | Spot rate for offline order. If missing an intention<br>could be executed as RFQ call or a market order.                                                                                                                                                                                                                                                   | 0.41                           |\n| forwardPoints        | 0 | Forward points for offline order.<br>This field is<br>ignored if spotRate is missing. If specified to<br>gether with effectiveRate, the values pass valida<br>tion.<br>Otherwise the effective rate is calculated<br>based on this value.                                                                                                                  | 100.0                          |\n| effectiveRate        | 0 | Effective rate for offline order. This field is ig<br>nored if spotRate is missing.<br>If specified to<br>gether with forwardPoints, the values pass vali<br>dation. Otherwise the forward points are calcu<br>lated based on this value.                                                                                                                  | 0.42                           |\n| timeInForce          | 0 | Specifies how long the order remains in effect.<br>Supported values are:<br>• 'DAY' - Good till day<br>• 'GOOD_TILL_CANCEL' - Good till<br>cancel<br>• 'GOOD_TILL_DATE' - Good till date<br>If 'Good till date' is specified, expireDate or ex<br>pireTime must be used as well                                                                            | GOOD_TILL_DATE                 |\n| expireDate           | 0 | Required<br>if<br>timeInForce<br>is<br>set<br>to<br>GOOD_TILL_DATE<br>and<br>expireTime<br>is<br>not<br>used. Specifies the date of order expiration (the<br>order will expire at the end of the specified day).<br>The format should be YYYY-MM-DD                                                                                                        | 2014-05-07                     |\n\n| expireTime                                                  | 0           | Required<br>if<br>timeInForce<br>is<br>set<br>to<br>GOOD_TILL_DATE<br>and<br>expireDate<br>is<br>not<br>used. Specifies the date and time of order expi<br>ration.<br>Always expressed in UTC. The format<br>should be yyyy-MM-ddTHH:mm:ss.SSS | 2014-05-07T12:30:00.000 |\n|-------------------------------------------------------------|-------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|\n| tradeDate                                                   | 0           | Used to specify the date and time of an of<br>fline confirmation order.<br>Absence of this field<br>indicates that it is normal order.<br>Always ex<br>pressed in UTC. The format should be yyyy<br>MM-ddTHH:mm:ss.SSS                         | 2014-05-07T12:30:00.000 |\n| customField                                                 | 0           | Custom fields inside a Custom Field node are ad<br>ditional fields provided by the company.                                                                                                                                                    |                         |\n| regulatoryData                                              | 0           | Optional block with regulatory data (MiFID), see<br>details below                                                                                                                                                                              |                         |\n| Product elements (only one is allowed per trade intention). |             |                                                                                                                                                                                                                                                |                         |\n| fxOutright                                                  | Exactly one | Spot or Forward                                                                                                                                                                                                                                |                         |\n| fxNdf                                                       | Exactly one | Non-deliverable forward                                                                                                                                                                                                                        |                         |\n| fxSwap                                                      | Exactly one | Swap                                                                                                                                                                                                                                           |                         |\n| fxNds                                                       | Exactly one | Non-deliverable swap                                                                                                                                                                                                                           |                         |\n| fxOption                                                    | Exactly one | Option                                                                                                                                                                                                                                         |                         |\n| loanDeposit                                                 | Exactly one | Loan Deposit                                                                                                                                                                                                                                   |                         |\n| metalsOutright                                              | Exactly one | Metals Outright                                                                                                                                                                                                                                |                         |\n| metalsSpread                                                | Exactly one | Metals Spread                                                                                                                                                                                                                                  |                         |\n| metalsQuarterlyStrip                                        | Exactly one | Metals Quarterly Strip                                                                                                                                                                                                                         |                         |\n\n#### Table 4.1: Trade intention\n\n#### <span id=\"page-9-0\"></span>********* Regulatory Data Fields**\n\n| Field Name<br>Mandatory<br>Description<br>Example |\n|---------------------------------------------------|\n|---------------------------------------------------|\n\n| executionVenueType             | 0 | Defines the type of Execution Venue.                                                                                                                        | OTC                 |\n|--------------------------------|---|-------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|\n|                                |   | Possible values:                                                                                                                                            |                     |\n|                                |   | • 'OTC' - Off-facility                                                                                                                                      |                     |\n|                                |   | • 'MTF' : Upload the order to either the<br>UK MTF or EU MTF, depending on the<br>uploading company's regulatory configu<br>ration in the 360T platform.    |                     |\n|                                |   | • 'EU MTF' - 360T's EU Multilateral<br>Trading Facility (MIC: \"360T\").                                                                                      |                     |\n|                                |   | • 'UK MTF' - 360T's UK Multilateral<br>Trading Facility (MIC: \"G360\").                                                                                      |                     |\n|                                |   | • 'SG RMO' - Singapore Regulated Mar<br>ket Operator (MIC: \"S360\").                                                                                         |                     |\n|                                |   | Note:                                                                                                                                                       |                     |\n|                                |   | • When using 'MTF', the order will be re<br>jected if the uploading company is con<br>figured to use both the UK MTF and EU<br>MTF.                         |                     |\n|                                |   | • An explicit venue must be specified when<br>using such a setup to upload to an MTF,<br>e.g. 'EU MTF' or 'UK MTF'.                                         |                     |\n| investmentDecisionWithinFirm 0 |   | Parent element of decisionMakerType and deci<br>sionMakerId (see next two lines). If the parent<br>exists then both child elements have to be pro<br>vided. |                     |\n| decisionMakerType              | 0 | Defines the type of the Investment Decision<br>Maker. Allowed values are \"Person\" and \"Algo<br>rithm\"                                                       | Person              |\n| decisionMakerId                | 0 | ID of the Investment Decision Maker                                                                                                                         | COMPANY2.Trader1    |\n| orderUploader                  | 0 | Parent element of orderUploaderType and or<br>derUploaderId (see next two lines). If the parent<br>exists then both child elements have to be pro<br>vided. |                     |\n| orderUploaderType              | 0 | Defines the type of the Order Uploader. Allowed<br>values are \"Person\" and \"Algorithm\"                                                                      | Algorithm           |\n| orderUploaderId                | 0 | ID of the Order Uploader                                                                                                                                    | COMPANY1.Treasurer1 |\n| tradingCapacity                | 0 | Defines the Trading Capacity. Allowed values are<br>\"AOTC\", \"MTCH\" and \"DEAL\"                                                                               | DEAL                |\n| complexTradeComponentId        | 0 | Defines the Complex Trade Component ID                                                                                                                      | 98765               |\n\nTable 4.2: Regulatory Data\n\n## <span id=\"page-11-0\"></span>********* FX Outright Fields**\n\n| Field Name             | Mandatory | Description                                                                                                                                                                                               | Example    |\n|------------------------|-----------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|\n| actionType             | 1         | Valid values are BUYER and SELLER                                                                                                                                                                         | BUYER      |\n| currency1              | 1         | First currency                                                                                                                                                                                            | EUR        |\n| currency2              | 1         | Second currency                                                                                                                                                                                           | USD        |\n| outrightValueDate      | 1         | Value date for the outright. The format for all<br>dates is YYYY-MM-DD                                                                                                                                    | 2011-10-20 |\n| outrightSplitValueDate | 0         | Split value date for the outright. If present, refers<br>to the second currency, while the settlement date<br>(outrightValueDate) refers to the first currency.<br>The format for all dates is YYYY-MM-DD | 2011-10-21 |\n| notionalCurrency       | 1         | Notional currency                                                                                                                                                                                         | EUR        |\n| notionalAmount         | 1         | Notional amount                                                                                                                                                                                           | 1000000    |\n\nTable 4.3: Outright products\n\n#### <span id=\"page-11-1\"></span>********* FX Swap Fields**\n\n| Field Name             | Mandatory | Description                                                                                                                                                                  | Example    |\n|------------------------|-----------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|\n| fxNearLeg              | 1         | Near leg definition                                                                                                                                                          |            |\n| fxFarLeg               | 1         | Far leg definition                                                                                                                                                           |            |\n| For each leg:          |           |                                                                                                                                                                              |            |\n| actionType             | 1         | Valid values are BUYER and SELLER                                                                                                                                            | BUYER      |\n| currency1              | 1         | First currency                                                                                                                                                               | EUR        |\n| currency2              | 1         | Second currency                                                                                                                                                              | USD        |\n| outrightValueDate      | 1         | Value date with the format YYYY-MM-DD                                                                                                                                        | 2011-10-20 |\n| outrightSplitValueDate | 0         | Split value date with the format YYYY-MM-DD.<br>If present, refers to the second currency, while the<br>settlement date (outrightValueDate) refers to the<br>first currency. | 2011-10-21 |\n| notionalCurrency       | 1         | Notional currency                                                                                                                                                            | EUR        |\n| notionalAmount         | 1         | Notional amount                                                                                                                                                              | 1000000    |\n\nTable 4.4: FxSwap products\n\n#### <span id=\"page-11-2\"></span>********* NDF Fields**\n\n| Field Name        | Mandatory | Description                                                            | Example    |\n|-------------------|-----------|------------------------------------------------------------------------|------------|\n| actionType        | 1         | Valid values are BUYER and SELLER                                      | BUYER      |\n| currency1         | 1         | First currency                                                         | EUR        |\n| currency2         | 1         | Second currency                                                        | USD        |\n| outrightValueDate | 1         | Value date for the outright. The format for all<br>dates is YYYY-MM-DD | 2011-10-20 |\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 12\n\n| notionalCurrency | 1 | Notional currency                      | EUR        |\n|------------------|---|----------------------------------------|------------|\n| notionalAmount   | 1 | Notional amount                        | 1000000    |\n| fixingDate       | 0 | Fixing date with the format YYYY-MM-DD | 2011-09-01 |\n| fixingReference  | 0 | Fixing reference                       |            |\n\nTable 4.5: NDF products\n\n#### <span id=\"page-12-0\"></span>********* NDS Fields**\n\n| Field Name        | Mandatory | Description                            | Example    |  |\n|-------------------|-----------|----------------------------------------|------------|--|\n| fxNearLeg         | 1         | Near leg definition                    |            |  |\n| fxFarLeg          | 1         | Far leg definition                     |            |  |\n| For each leg:     |           |                                        |            |  |\n| actionType        | 1         | Valid values are BUYER and SELLER      | BUYER      |  |\n| currency1         | 1         | First currency                         | EUR        |  |\n| currency2         | 1         | Second currency                        | USD        |  |\n| outrightValueDate | 1         | Value date with the format YYYY-MM-DD  | 2011-10-20 |  |\n| notionalCurrency  | 1         | Notional currency                      | EUR        |  |\n| notionalAmount    | 1         | Notional amount                        | 1000000    |  |\n| fixingDate        | 0         | Fixing date with the format YYYY-MM-DD | 2011-09-01 |  |\n| fixingReference   | 0         | Fixing reference                       |            |  |\n\nTable 4.6: NDS products\n\n# <span id=\"page-12-1\"></span>********* FX Option Fields**\n\n| Field Name       | Mandatory | Description                                                   | Example        |\n|------------------|-----------|---------------------------------------------------------------|----------------|\n| actionType       | 1         | Valid values are BUYER and SELLER                             | BUYER          |\n| optionType       | 1         | Valid values are CALL and PUT                                 | PUT            |\n| optionStyle      | 1         | Valid values are EUROPEAN and AMERICAN                        | EUROPEAN       |\n| currency1        | 1         | First currency                                                | EUR            |\n| currency2        | 1         | Second currency                                               | USD            |\n| notionalCurrency | 1         | Notional currency                                             | EUR            |\n| notionalAmount   | 1         | Notional amount                                               | 1000000        |\n| strike           | 1         | The rate agreed upon in the contract                          |                |\n| expiryTime       | 1         | The expiry time of the option. Valid values in<br>section 7.1 | New York 10:00 |\n| premiumCurrency  | 1         | The premium currency.                                         | EUR            |\n| premiumDate      | 1         | Premium date with the format YYYY-MM-DD                       | 2014-02-20     |\n| deliveryDate     | 1         | Delivery date with the format YYYY-MM-DD                      | 2014-02-20     |\n| exerciseDate     | 1         | Exercise date with the format YYYY-MM-DD                      | 2014-02-18     |\n\n#### 4.1. Trade Intention Submission Chapter 4: Access Points\n\n| hedgeRate          | 0 | The hedge rate.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 1.37580 |\n|--------------------|---|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|\n| targetPremium      | 0 | The target premium for FX Option Strategies.<br>Supported values are:<br>• 'Pay'<br>• 'Receive'<br>• 'Zero'<br>• 'None'<br>For Zero Premium Risk Reversals:<br>• The targetPremium is given with \"Zero\"<br>on the first leg, along with the target<br>PremiumValue = 0.<br>• The second leg needs to be delivered<br>without targetPremium and targetPremi<br>umValue.<br>• The strike must be set to \"0\" on either the<br>first or second leg, but not both: one leg<br>must have a valid strike price defined.<br>If targetPremium should be \"None\", then it must<br>be added to the first leg and empty on the second<br>leg. | Pay     |\n| targetPremiumValue | 0 | The targetPremiumValue for FX Option Strate<br>gies.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 1.38    |\n\n#### Table 4.7: Option products\n\n#### <span id=\"page-13-0\"></span>********* Loan Deposit Fields**\n\n| Field Name       | Mandatory | Description                                                                                                                                             | Example    |\n|------------------|-----------|---------------------------------------------------------------------------------------------------------------------------------------------------------|------------|\n| actionType       | 1         | Valid values are BUYER and SELLER. BUYER<br>means counterpart borrowing notional amount<br>and SELLER means counterpart depositing no<br>tional amount. | BUYER      |\n| notionalCurrency | 1         | Notional currency                                                                                                                                       | EUR        |\n| notionalAmount   | 1         | Notional amount                                                                                                                                         | 1000000    |\n| effectiveDate    | 1         | Effective date with the format YYYY-MM-DD.<br>This represents the date when the contract be<br>comes active.                                            | 2014-03-31 |\n| maturityDate     | 1         | Maturity date with the format YYYY-MM-DD.<br>This represents the date on which the contract<br>matures.                                                 | 2014-05-07 |\n\nTable 4.8: Loan Deposit products\n\n### <span id=\"page-14-0\"></span>********* Metals Outright Fields**\n\n| Field Name        | Mandatory | Description                                                            | Example    |\n|-------------------|-----------|------------------------------------------------------------------------|------------|\n| actionType        | 1         | Valid values are BUYER and SELLER                                      | BUYER      |\n| currency1         | 1         | First currency (Metal)                                                 | CU         |\n| currency2         | 1         | Second currency (USD or EUR)                                           | USD        |\n| outrightValueDate | 1         | Value date for the outright. The format for all<br>dates is YYYY-MM-DD | 2011-10-20 |\n| notionalCurrency  | 1         | Notional currency                                                      | CU         |\n| notionalAmount    | 1         | Notional amount                                                        | 1000000    |\n\nTable 4.9: Metals Outright products\n\n#### <span id=\"page-14-1\"></span>********* Metals Spread Fields**\n\n| Field Name        | Mandatory | Description                           | Example    |\n|-------------------|-----------|---------------------------------------|------------|\n| frontLeg          | 1         | Front leg definition                  |            |\n| backLeg           | 1         | Back leg definition                   |            |\n| For each leg:     |           |                                       |            |\n| actionType        | 1         | Valid values are BUYER and SELLER     | BUYER      |\n| currency1         | 1         | First currency (Metal)                | AL         |\n| currency2         | 1         | Second currency (USD or EUR)          | EUR        |\n| outrightValueDate | 1         | Value date with the format YYYY-MM-DD | 2011-10-20 |\n| notionalCurrency  | 1         | Notional currency                     | AL         |\n| notionalAmount    | 1         | Notional amount                       | 1000000    |\n\nTable 4.10: Metals Spread products\n\n#### <span id=\"page-14-2\"></span>********** Metals Quarterly Strip Fields**\n\n| Field Name        | Mandatory | Description                                                            | Example    |\n|-------------------|-----------|------------------------------------------------------------------------|------------|\n| actionType        | 1         | Valid values are BUYER and SELLER                                      | BUYER      |\n| currency1         | 1         | First currency (Metal)                                                 | CU         |\n| currency2         | 1         | Second currency (USD or EUR)                                           | USD        |\n| outrightValueDate | 1         | Value date for the outright. The format for all dates is YYYY<br>MM-DD | 2011-10-20 |\n| notionalCurrency  | 1         | Notional currency                                                      | CU         |\n| notionalAmount    | 1         | Notional amount                                                        | 1000000    |\n\nTable 4.11: Metals Quarterly Strip products\n\nThe response of a submit message is an XML structure.\n\n```\n<tradeIntentionSubmitResponse>\n      <tradeIntention>\n            <externalId>123456</externalId>\n            <productId>TI-987654</productId>\n            <status>ACK</status>\n      </tradeIntention>\n</tradeIntentionSubmitResponse>\n```\n\nIn case of an error, the message would be in this form:\n\n```\n<generalErrorResponse>\n      <error> Could handle the request: Trade Intention with external id '123456no' already\n          exists for company 'COMPANY.Treasurer1' </error>\n</generalErrorResponse>\n```\n\n#### <span id=\"page-15-0\"></span>**4.1.3 Multi-Trade Intentions**\n\nIf a groupId is defined for one or more trade intention object, a multi-trade intention is constructed in the 360T platform. An important consideration is that the groupId should be unique from any other groupId and externalId. Appending trade intentions to already submitted multi-trade intention is also not allowed. This means that a multitrade intention can be constructed only from the trade intention objects in one message.\n\n#### <span id=\"page-15-1\"></span>**4.1.4 Fixing References**\n\nWhen creating a fixing order, the fields fixingDate and the fixing should be provided. Fixing need to be one of the following values:\n\n| API Mnemonic | Name                | Timezone         | Daily fixing time |\n|--------------|---------------------|------------------|-------------------|\n| NONE         | Custom Fixing       | GMT              | Time defined by   |\n|              |                     |                  | fixingOrderDate   |\n| BFIX_0130    | BFIX                | Europe/London    | 01:30             |\n|              |                     |                  |                   |\n| BFIX_2330    | BFIX                | Europe/London    | 23:30             |\n| BOC          | BOC Noon Rate       | America/New York | 12:00             |\n| CNHFIX       | CNHFIX              | Asia/Hong Kong   | 11:00             |\n| ECB          | ECB                 | Europe/Berlin    | 14:15             |\n| LBMA_AM      | LBMA AM             | Europe/London    | 10:30             |\n| LBMA_PM      | LBMA PM             | Europe/London    | 15:00             |\n| TKFE_09      | TKFE                | Asia/Tokyo       | 09:00             |\n|              |                     |                  |                   |\n| TKFE_17      | TKFE                | Asia/Tokyo       | 17:00             |\n| WM_AUSTRALIA | WMR Australia       | Australia/Sydney | 10:00             |\n| WM_CLOSE     | WMR Closing London  | Europe/London    | 16:00             |\n| WM_INTRA_01  | WMR Intraday London | Europe/London    | 01:00             |\n| WM_INTRA_02  | WMR Intraday London | Europe/London    | 02:00             |\n|              |                     |                  |                   |\n\n| WM_INTRA_22 | WMR Intraday London | Europe/London    | 22:00 |\n|-------------|---------------------|------------------|-------|\n| WM_INTRA_23 | WMR Intraday London | Europe/London    | 23:00 |\n| WM_NEWYORK  | WMR New York        | America/New York | 16:00 |\n\nTable 4.12: Fixing references\n\n#### <span id=\"page-16-0\"></span>**4.1.5 Custom Fields**\n\nCustom fields are a list of strings. The customFields element should be in the trade XML element of a trade intention as shown in the examples. It should be in this form:\n\n```\n<customFields>\n      <customField>\n            <Name>Custom Field 1</Name>\n            <Value>string value of the custom field 1</Value>\n      </customField>\n      <customField>\n            <Name>Custom Field 2</Name>\n            <Value>string value of the custom field 2</Value>\n      </customField>\n</customFields>\n```\n\nThe custom fields are defined on the 360T system with specific names and order. The number and names of the custom fields in the trade intention should match that of the custom field definition on the 360T system. If the number of fields or their names is wrong, the trade intention submission will fail.\n\n# <span id=\"page-16-1\"></span>**4.2 Batch Trade Intention Submission**\n\nThe trade intention submit message has a root element with the name *tradeIntentionSubmit*. It can contain many *tradeIntention* sub elements describing trade intentions. Each needs to have it's own *externalId* and define a submitting individual. Each is handled independently, so the response of a batch intention submit may include acknowledgments and errors for different intentions at the same time. Here's an example:\n\n```\n<tradeIntentionSubmitResponse>\n      <tradeIntention>\n            <externalId>123456</externalId>\n            <productId>TI-987654</productId>\n            <status>ACK</status>\n      </tradeIntention>\n      <tradeIntention>\n            <externalId>1234567</externalId>\n            <status>NACK</status>\n            <message>\n                  Trade Intention with external id '1234567' already exists for company\n                       'COMPANY'\n            </message>\n      </tradeIntention>\n      <tradeIntention>\n            <externalId>12345678</externalId>\n            <productId>**********</productId>\n            <status>ACK</status>\n      </tradeIntention>\n```\n\n```\n</tradeIntentionSubmitResponse>\n```\n\nIn case there's a general error in handling the submit – like wrong format of the request, bad authentication, etc. – would a *generalErrorResponse* be sent. Thus the submitting of all included trade intentions would have failed.\n\n# <span id=\"page-17-0\"></span>**4.3 Trade Intention Status**\n\nOnce a trade intention is submitted, one can receive it's status via the REST interface through the access point\n\nhttps://************:6445/tradeIntentionStatus\n\nAgain, the request can be sent with a POST and a GET with the trade intention's external id as a parameter. The parameter name is *\"id\"*. Alternatively, a REST call can be made in the form\n\nhttps://************:6445/tradeIntentionStatus/123456\n\nThe SOAP action for trade intention status request is:\n\nurn:360t:orderapi:tradeIntentionStatus\n\nAn extra element, \"comment\", will be part of the response, in case the trade has been rejected or canceled in the GUI and the user has entered a comment/reason. All these will deliver the current status of the trade intention in one of the following forms:\n\n```\n<tradeIntentionStatusResponse>\n      <tradeIntentionStatus>\n            <externalId>123456</externalId>\n            <productId>TI-987654</productId>\n            <status>Accepted</status>\n      </tradeIntentionStatus>\n</tradeIntentionStatusResponse>\n<tradeIntentionStatusResponse>\n      <tradeIntentionStatus>\n            <externalId>1234567</externalId>\n            <productId>**********</productId>\n            <status>Canceled</status>\n            <comment>Comment/Reason for canceling</comment>\n      </tradeIntentionStatus>\n</tradeIntentionStatusResponse>\n<tradeIntentionStatusResponse>\n      <tradeIntentionStatus>\n            <externalId>12345678</externalId>\n            <productId>**********2</productId>\n            <status>Canceled</status>\n      </tradeIntentionStatus>\n</tradeIntentionStatusResponse>\n<tradeIntentionStatusResponse>\n      <tradeIntentionStatus>\n            <externalId>123456789</externalId>\n            <productId>**********21</productId>\n            <status>Rejected</status>\n            <comment>Comment/Reason for rejecting</comment>\n      </tradeIntentionStatus>\n</tradeIntentionStatusResponse>\n```\n\nValid statuses are *Sent*, *Delivered*, *Accepted*, *Negotiating*, *Partly Executed*, *Rejected*, *Canceled*, *Expired* and *Executed*. In case of an error, the response would be in the form:\n\n```\n<tradeIntentionStatusResponse>\n      <tradeIntentionStatus>\n            <externalId>123456no</externalId>\n            <error>No trade with this ID in the registry.</error>\n      </tradeIntentionStatus>\n</tradeIntentionStatusResponse>\n```\n\n# <span id=\"page-18-0\"></span>**4.4 Batch Trade Intention Status**\n\nIt is possible to query the status of multiple trade intentions regardless if they were submitted separately or as a batch. In this case one should send the external ids of the trade intentions as a parameter list with the name *\"id\"*. Again this can be done through GET and POST. Here's an example request:\n\nhttps://************:6445/tradeIntentionStatus/?id=123456&id=1234567no\n\nThe response of this request will be similar to the following response:\n\n```\n<tradeIntentionStatusResponse>\n      <tradeIntentionStatus>\n            <externalId>123456</externalId>\n            <productId>TI-987654</productId>\n            <status>Accepted</status>\n      </tradeIntentionStatus>\n      <tradeIntentionStatus>\n            <externalId>1234567no</externalId>\n            <error>No trade with this ID in the registry.</error>\n      </tradeIntentionStatus>\n</tradeIntentionStatusResponse>\n```\n\nSimilar to the trade intention submit, it is possible that the status of some trade intentions include errors. Important note: The way trade intention status is queried is used also by Canceling, Accepting and Fetching the resulting trades. These are described in the following sections.\n\n# <span id=\"page-18-1\"></span>**4.5 Trade Intention Cancellation**\n\nIf a trade intention is not executed, expired or already canceled, a cancel request could be sent using this API. The access point is:\n\nhttps://************:6445/tradeIntentionCancel\n\nThe SOAP action for trade intention cancelation is:\n\nurn:360t:orderapi:tradeIntentionCancel\n\nThe request and response model is the same as in Status querying. The only difference is that the response root element is called *tradeIntentionCancelResponse*. If successful, the returned status is *Canceled*.\n\n# <span id=\"page-19-0\"></span>**4.6 Trade Intention Accepting**\n\nIf a trade intention is still in Delivered or Sent state, it can be Accepted using this API. This it will appear directly in the accepted box in the GUI of the traders. The access point is:\n\nhttps://************:6445/tradeIntentionAccept\n\nThe SOAP action for trade intention accepting is:\n\nurn:360t:orderapi:tradeIntentionAccept\n\nThe request and response model is the same as in Status querying. The only difference is that the response root element is called *tradeIntentionAcceptResponse*. If successful, the returned status is *Accepted*.\n\nIt is also possible to set automatic acceptance for a company. In this case, the trade intention is directly acceptor upon upload through the method in 2.1.\n\n# <span id=\"page-19-1\"></span>**4.7 Fetching the Resulting Trades**\n\nOnce a trade is executed, the resulting trades can be fetched using the access point\n\nhttps://************:6445/tradeIntentionTrades\n\nThe request and response model are similar to the Status querying, but in this case one can optionally also send the desired schema version of the returned trades via the parameter with name \"schemaVersion\". If it is omitted, all returned trades will have the latest schema version. For Base Metal products, schemaVersion needs to be at least 37. Here's an example request:\n\nhttps://************:6445/tradeIntentionTrades/?id=123456&schemaVersion=17\n\nThe SOAP action for trade query:\n\nurn:360t:orderapi:tradeIntentionTrades\n\nIf successful, the call returns a response in the form:\n\n```\n<tradeIntentionTradesResponse>\n      <tradeIntentionTrade>\n            <externalId>123456</externalId>\n            <executedTrades>\n            ...\n            </executedTrades>\n      </tradeIntentionTrade>\n</tradeIntentionTradesResponse>\n```\n\nThe *executedTrades* element contains one or more trades that resulted from this intention. If the intention is not in an *Executed* state, the *executedTrades* element will be empty. Detailed description of the trade objects can be found in the *dealconfirmation\\_11.pdf* document.\n\nIn case of an error, the response would be in the form:\n\n```\n<tradeIntentionStatusResponse>\n      <tradeIntentionTrade>\n            <externalId>123456no</externalId>\n            <error>No trade with this ID in the registry.</error>\n      </tradeIntentionTrade>\n</tradeIntentionStatusResponse>\n```\n\n# <span id=\"page-20-0\"></span>**4.8 Combined Status and Trades Request**\n\nAn additional reqeust action is supported, which fetches both the status of all specified trades and all trades in that list that have been executed. The trades whose status is not 'Executed' will not be included in the second part of the response. The access point is:\n\nhttps://************:6445/tradeIntentionStatusTrades\n\nThe request and response model are the same as the Trades one, including the optional parameter \"schemaVersion\". For Base Metal products, schemaVersion needs to be at least 37. Here's an example request:\n\nhttps://************:6445/tradeIntentionStatusTrades/?id=123456&id=123456a&schemaVersion=17\n\nThe SOAP action for the combined request is:\n\nurn:360t:orderapi:tradeIntentionStatusTrades\n\nIf successful, the call returns a response in the form:\n\n```\n<tradeIntentionStatusTradesResponse>\n   <tradeIntentionStatusResponse>\n      <tradeIntentionStatus>\n            <externalId>123456</externalId>\n            <productId>TI-987654</productId>\n            <status>Executed</status>\n      </tradeIntentionStatus>\n      <tradeIntentionStatus>\n            <externalId>1234567a</externalId>\n            <error>Accepted</error>\n      </tradeIntentionStatus>\n   </tradeIntentionStatusResponse>\n   <tradeIntentionTradesResponse>\n      <tradeIntentionTrade>\n            <externalId>123456</externalId>\n            <executedTrades>\n            ...\n            </executedTrades>\n      </tradeIntentionTrade>\n   </tradeIntentionTradesResponse>\n</tradeIntentionStatusTradesResponse>\n```\n\n# <span id=\"page-20-1\"></span>**4.9 Amend Request**\n\nA amend request can be sent to modify one or more trade intentions if they are not already executed or canceled. The request / response model is similar to the Submit request. If successful, the returned status is *Amended*. The access point for the amend request is:\n\nhttps://************:6445/tradeIntentionAmend\n\nThe SOAP action for the Amend request is:\n\nurn:360t:orderapi:tradeIntentionAmend\n\nThe request message is similar to the Submit except for the root tag. Which in this case is called *tradeIntention-AmendRequest*\n\n```\n<tradeIntentionAmendRequest>\n <tradeIntention>\n   .\n </tradeIntention>\n <tradeIntention>\n   .\n </tradeIntention>\n  ...\n```\n\n# </tradeIntentionAmendRequest>\n\n#### The call returns a response in the form:\n\n| <tradeintentionamendresponse></tradeintentionamendresponse> |\n|-------------------------------------------------------------|\n| <tradeintentionstatus></tradeintentionstatus>               |\n| <externalid>123456</externalid>                             |\n| <productid>TI-987654</productid>                            |\n| <status>Amended</status>                                    |\n|                                                             |\n| <tradeintentionstatus></tradeintentionstatus>               |\n| <externalid>1234567a</externalid>                           |\n| <error><error message=\"\"></error></error>                   |\n|                                                             |\n|                                                             |\n| <tradeintentionstatus></tradeintentionstatus>               |\n| <externalid>12345689</externalid>                           |\n| <productid>TI-987689</productid>                            |\n| <status>Amended</status>                                    |\n|                                                             |\n|                                                             |\n\n### <span id=\"page-21-0\"></span>**4.10 Withdraw Request**\n\nA withdraw request can be sent for one or more trade intentions if they are not already executed or canceled. The external ids of the trade intentions should be passed as a parameter list with the name *\"id\"*. If successful, the returned status is *Withdrawn*. Here's an example request:\n\nhttps://************:6445/tradeIntentionWithdraw/?id=123456&id=123456a\n\nThe SOAP action for the Withdraw request is:\n\nurn:360t:orderapi:tradeIntentionWithdraw/?id=123456&id=123456a\n\nThe call returns a response in the form:\n\n```\n<tradeIntentionWithdrawResponse>\n <tradeIntentionStatus>\n   <externalId>123456</externalId>\n   <productId>TI-987654</productId>\n   <status>Withdrawn</status>\n   </tradeIntentionStatus>\n <tradeIntentionStatus>\n   <externalId>1234567a</externalId>\n   <error><Error Message></error>\n   </tradeIntentionStatus>\n```\n\n![](_page_22_Picture_0.jpeg)\n\n4.10. Withdraw Request Chapter 4: Access Points\n\n...\n\n<tradeIntentionStatus> <externalId>12345689</externalId> <productId>TI-987689</productId> <status>Withdrawn</status> </tradeIntentionStatus> </tradeIntentionWithdrawResponse>\n\n# <span id=\"page-23-0\"></span>**5 Error Handling**\n\n# <span id=\"page-23-1\"></span>**5.1 Authentication**\n\nEach client is provided with a set of private key and client certificate. These should be used when submitting requests to the 360T Order XML API access points. Thus the requester will be authorized to use certain resources and submit trade intentions as a specific company entity. If such a client certificate is not provided, the services will return HTTP 401.\n\n# <span id=\"page-23-2\"></span>**5.2 Trade Intention submitting**\n\nThere are two types of errors when submitting trade intentions – generalErrorResponse object or individual error messages for each trade intention. The first case indicates a general issue with the xml message. For example:\n\n- broken xml format\n- missing fields or such with wrong values\n- entity name or individual are not part of the company entity the request is authorized to work with.\n\nIf such an error occurs all trade intentions in the message are not submitted to the 360T platform and should be assumed failed. Should the message pass the validation, each trade intention is passed to the platform. There individual trade intentions can fail. The reasons may include:\n\n- insufficient rights for the user\n- duplicate external ID for the trade intention\n- multi-trade intention with the same group id\n\nIn case one trade intention fails, an error message is returned for it specifically, but the rest are submitted successfully.\n\n# <span id=\"page-23-3\"></span>**5.3 Other Services**\n\nAll other services for accepting, polling status or the executed trade may throw errors in cases of:\n\n- unknown externalID\n- insufficient rights to access or change a trade intention\n- general platform error (please contact 360T)\n\n# <span id=\"page-24-0\"></span>**6 Future Considerations**\n\nThere are a number of features that are planned in future releases:\n\n- Pushing trade intention status changes and resulting trades. Clients will be provided with web service a specification, which they'll have to implement. This service will be then alerted when the status of an intention changes and when a new trade is executed.\n- SOAP services may also provide additional ways of client authorization.\n- If there is a requirement from customers, creating dedicated private keys at treasurer level is possible. Currently this is only possible at company level.\n\n# <span id=\"page-25-0\"></span>**7 Appendix**\n\n# <span id=\"page-25-1\"></span>**7.1 Cutoff Time for Options**\n\n| 'Almaty 11:00'           | 'Almaty 12:00'       | 'Beijing 09:15'      | 'Beijing 16:00'      |\n|--------------------------|----------------------|----------------------|----------------------|\n| 'Beijing 17:00'          | 'Berlin 14:15'       | 'Bogota 10:30'       | 'Bogota 12:30'       |\n| 'Bucharest 13:00'        | 'Budapest 12:00'     | 'Buenos Aires 12:00' | 'Buenos Aires 12:30' |\n| 'Calcutta 13:00'         | 'ECB 14:15'          | 'Frankfurt 13:00'    | 'Frankfurt 14:00'    |\n| 'Frankfurt 14:15'        | 'Hong Kong 11:00'    | 'Hong Kong 11:15'    | 'Hong Kong 16:00'    |\n| 'Istanbul 14:00'         | 'Jakarta 10:00'      | 'Jakarta 10:30'      | 'Kuala Lumpur 11:00' |\n| 'Kuala Lumpur 11:30'     | 'Lima 11:00'         | 'London 09:30'       | 'London 10:30'       |\n| 'London 11:00'           | 'London 12:00'       | 'London 15:00'       | 'London 16:00'       |\n| 'London 17:00'           | 'Manila 11:00'       | 'Manila 11:15'       | 'Manila 11:30'       |\n| 'Manila 12:00'           | 'Manila 12:30'       | 'Mexico City 12:00'  | 'Mexico City 12:30'  |\n| 'Moscow 11:00'           | 'Moscow 12:30'       | 'Moscow 13:30'       | 'Moscow 15:00'       |\n| 'Moscow 16:30'           | 'Mumbai 12:00'       | 'Mumbai 12:30'       | 'Mumbai 14:30'       |\n| 'New York 10:00'         | 'New York 10:30'     | 'New York 12:00'     | 'New York 15:00'     |\n| 'New York Bullion 09:30' | 'New York Cut 12:30' | 'Santiago 10:30'     | 'Sao Paulo 12:00'    |\n| 'Sao Paulo 12:30'        | 'Sao Paulo 13:15'    | 'Sao Paulo 18:00'    | 'Sao Paulo 20:30'    |\n| 'Seoul 09:00'            | 'Seoul 11:00'        | 'Seoul 14:00'        | 'Seoul 15:30'        |\n| 'Seoul 17:30'            | 'Shanghai 09:30'     | 'Singapore 10:00'    | 'Singapore 11:00'    |\n| 'Sydney 15:00'           | 'Taipei 11:00'       | 'Tel Aviv 12:00'     | 'Tel Aviv 15:00'     |\n| 'Tokyo 09:00'            | 'Tokyo 10:00'        | 'Tokyo 12:00'        | 'Tokyo 14:00'        |\n| 'Tokyo 15:00'            | 'Warsaw 11:00'       | 'Warsaw 12:30'       | 'Wellington 15:00'   |\n| 'Zurich 10:00'           | 'Zurich 18:00'       |                      |                      |\n\nTable 7.1: Supported cutoff time for options.\n\n# <span id=\"page-26-0\"></span>**7.2 Fixing Sources for NDF/NDS**\n\n| Symbol | List of supported fixing source values                                                                                                                                                            |  |\n|--------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n| USDARS | 'ARS01', 'ARS02', 'EMTA (ARS05)'                                                                                                                                                                  |  |\n| USDBRL | 'BRL01', 'BRL02', 'BRL03', 'BRL10', 'BRL11', 'PTAX (BRL09)', 'Pontos sobre PTAX', 'NDF Asiat<br>ica'                                                                                              |  |\n| USDCLP | 'CLP01', 'CLP02', 'CLP03', 'CLP04', 'CLP08', 'CLP09', 'CLPOBS (CLP10)'                                                                                                                            |  |\n| USDCNY | 'SAEC (CNY01)'                                                                                                                                                                                    |  |\n| USDCOP | 'COP01', 'COP TRM (COP02)', 'CO/COL3'                                                                                                                                                             |  |\n| USDCRC | 'CRREB'                                                                                                                                                                                           |  |\n| USDEGP | 'FEMF (EGP01)'                                                                                                                                                                                    |  |\n| USDGHS | 'GHS TR (GHS03)'                                                                                                                                                                                  |  |\n| USDIDR | 'JISDOR (IDR04)', 'ABSIRFIX01', 'INR01', 'RBIB'                                                                                                                                                   |  |\n| USDINR | 'FBIL'                                                                                                                                                                                            |  |\n| USDKES | 'KES TR (KES01)'                                                                                                                                                                                  |  |\n| USDKRW | 'KRW02', 'KFTC18'                                                                                                                                                                                 |  |\n| USDKZT | 'KZFXWA', 'KZT KASE (KZT01)'                                                                                                                                                                      |  |\n| USDMYR | 'MYR PPKM (MYR03)', 'MYR KL REF (MYR04)'                                                                                                                                                          |  |\n| USDNGN | 'NGN NiFEX (NGN01)', 'NGN NAFEX (NGN03)'                                                                                                                                                          |  |\n| USDPEN | 'PEN01', 'PEN02', 'PEN05'                                                                                                                                                                         |  |\n| USDPHP | 'PHP01', 'PHP02', 'PHP03', 'PHP04', 'PDSPESO', 'PHP BAPPESO (PHP06)'                                                                                                                              |  |\n| USDRSD | 'RSDFIX'                                                                                                                                                                                          |  |\n| USDRUB | 'RUB01', 'RUB02', 'RUB MOEX (RUB05)'                                                                                                                                                              |  |\n| USDTWD | 'TWD01', 'TWD02', 'TAIFX1 (TWD03)'                                                                                                                                                                |  |\n| USDUAH | 'EMTAUAHFIX', 'EMTA UAH ISR (UAH02)'                                                                                                                                                              |  |\n| USDVEF | 'VEB01'                                                                                                                                                                                           |  |\n| USDVND | 'ABSIRFIX01'                                                                                                                                                                                      |  |\n| EURARS | 'EMTA (ARS05)-BFIX EUR L080', 'EMTA (ARS05)-BFIX EUR L130', 'EMTA (ARS05)-BFIX EUR<br>L160', 'EMTA (ARS05)-WMCo 8am LDN', 'EMTA (ARS05)-WMCo 1pm LDN', 'EMTA (ARS05)-<br>WMCo 4pm LDN'            |  |\n| EURBRL | 'PTAX-BFIX EUR L080', 'PTAX-BFIX EUR L130', 'PTAX-BFIX EUR L160', 'PTAX-WMCo 8am<br>LDN', 'PTAX-WMCo 1pm LDN', 'PTAX-WMCo 4pm LDN', 'NDF Asiatica', 'Pontos sobre PTAX'                           |  |\n| EURCLP | 'CLPOBS (CLP10)-BFIX EUR L080', 'CLPOBS (CLP10)-BFIX EUR L130', 'CLPOBS (CLP10)-BFIX<br>EUR L160', 'CLPOBS (CLP10)-WMCo 8am LDN', 'CLPOBS (CLP10)-WMCo 1pm LDN', 'CLPOBS<br>(CLP10)-WMCo 4pm LDN' |  |\n| EURCNY | 'SAEC (CNY01)-BFIX EUR L080', 'SAEC (CNY01)-BFIX EUR L130', 'SAEC (CNY01)-BFIX EUR<br>L160', 'SAEC (CNY01)-WMCo 8am LDN', 'SAEC (CNY01)-WMCo 1pm LDN', 'SAEC (CNY01)-<br>WMCo 4pm LDN'            |  |\n| EURCOP | 'TRM (COP02)-BFIX EUR L080', 'TRM (COP02)-BFIX EUR L130', 'TRM (COP02)-BFIX EUR L160',<br>'TRM (COP02)-WMCo 8am LDN', 'TRM (COP02)-WMCo 1pm LDN', 'TRM (COP02)-WMCo 4pm<br>LDN'                   |  |\n| EURINR | 'FBIL', 'FBIL-BFIX EUR L080', 'FBIL-BFIX EUR L130', 'FBIL-BFIX EUR L160', 'FBIL-WMCo 8am<br>LDN', 'FBIL-WMCo 1pm LDN', 'FBIL-WMCo 4pm LDN'                                                        |  |\n\n![](_page_27_Picture_0.jpeg)\n\n| Symbol | List of supported fixing source values                                                                                                                                                                        |  |\n|--------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n| EURIDR | 'JISDOR (IDR04)-BFIX EUR L080', 'JISDOR (IDR04)-BFIX EUR L130', 'JISDOR (IDR04)-BFIX EUR<br>L160', 'JISDOR (IDR04)-WMCo 8am LDN', 'JISDOR (IDR04)-WMCo 1pm LDN', 'JISDOR (IDR04)-<br>WMCo 4pm LDN'            |  |\n| EURKRW | 'KFTC18-BFIX EUR L080', 'KFTC18-BFIX EUR L130', 'KFTC18-BFIX EUR L160', 'KFTC18-WMCo<br>8am LDN', 'KFTC18-WMCo 1pm LDN', 'KFTC18-WMCo 4pm LDN'                                                                |  |\n| EURKZT | 'KZFXWA-BFIX EUR L080', 'KZFXWA-BFIX EUR L130', 'KZFXWA-BFIX EUR L160', 'KZFXWA<br>WMCo 8am LDN', 'KZFXWA-WMCo 1pm LDN', 'KZFXWA-WMCo 4pm LDN'                                                                |  |\n| EURMYR | 'ABSIRFIX01-BFIX EUR L080', 'ABSIRFIX01-BFIX EUR L130', 'ABSIRFIX01-BFIX EUR L160',<br>'ABSIRFIX01-WMCo 8am LDN', 'ABSIRFIX01-WMCo 1pm LDN', 'ABSIRFIX01-WMCo 4pm LDN'                                        |  |\n| EURPEN | 'PEN05-BFIX EUR L080', 'PEN05-BFIX EUR L130', 'PEN05-BFIX EUR L160', 'PEN05-WMCo 8am<br>LDN', 'PEN05-WMCo 1pm LDN', 'PEN05-WMCo 4pm LDN'                                                                      |  |\n| EURPHP | 'PDSPESO-BFIX EUR L080', 'PDSPESO-BFIX EUR L130', 'PDSPESO-BFIX EUR L160', 'PDSPESO<br>WMCo 8am LDN', 'PDSPESO-WMCo 1pm LDN', 'PDSPESO-WMCo 4pm LDN'                                                          |  |\n| EURRUB | 'RUB MOEX (RUB05)-BFIX EUR L080', 'RUB MOEX (RUB05)-BFIX EUR L130', 'RUB MOEX<br>(RUB05)-BFIX EUR L160', 'RUB MOEX (RUB05)-WMCo 8am LDN', 'RUB MOEX (RUB05)-WMCo<br>1pm LDN', 'RUB MOEX (RUB05)-WMCo 4pm LDN' |  |\n| EURTWD | 'TAIFX1 (TWD03)-BFIX EUR L080', 'TAIFX1 (TWD03)-BFIX EUR L130', 'TAIFX1 (TWD03)-BFIX<br>EUR L160', 'TAIFX1 (TWD03)-WMCo 8am LDN', 'TAIFX1 (TWD03)-WMCo 1pm LDN', 'TAIFX1<br>(TWD03)-WMCo 4pm LDN'             |  |\n| GBPARS | 'EMTA (ARS05)-WMCo 8am LDN', 'EMTA (ARS05)-WMCo 1pm LDN', 'EMTA (ARS05)-WMCo 4pm<br>LDN', 'EMTA (ARS05)-BFIX GBP L080'                                                                                        |  |\n| GBPBRL | 'PTAX-WMCo 8am LDN', 'PTAX-WMCo 1pm LDN', 'PTAX-WMCo 4pm LDN', 'PTAX-BFIX GBP<br>L080', 'Pontos sobre PTAX', 'NDF Asiatica'                                                                                   |  |\n| GBPCLP | 'CLPOBS-WMCo 8am LDN', 'CLPOBS-WMCo 1pm LDN', 'CLPOBS-WMCo 4pm LDN', 'CLOPBS<br>BFIX GBP L080'                                                                                                                |  |\n| GBPCNY | 'SAEC-WMCo 8am LDN', 'SAEC-WMCo 1pm LDN', 'SAEC-WMCo 4pm LDN', 'SAEC-BFIX GBP<br>L080'                                                                                                                        |  |\n| GBPCOP | 'COP TRM-WMCo 8am LDN', 'COP TRM-WMCo 1pm LDN', 'COP TRM-WMCo 4pm LDN', 'COP<br>TRM-BFIX GBP L080'                                                                                                            |  |\n| GBPEGP | 'FEMF-WMCo 8am LDN', 'FEMF-WMCo 1pm LDN', 'FEMF-WMCo 4pm LDN', 'FEMF-BFIX GBP<br>L080'                                                                                                                        |  |\n| GBPIDR | 'JISDOR-WMCo 8am LDN', 'JISDOR-WMCo 1pm LDN', 'JISDOR-WMCo 4pm LDN', 'JISDOR-BFIX<br>GBP L080'                                                                                                                |  |\n| GBPINR | 'FBIL', 'FBIL-WMCo 8am LDN', 'FBIL-WMCo 1pm LDN', 'FBIL-WMCo 4pm LDN', 'FIBL-BFIX<br>GBP L080'                                                                                                                |  |\n| GBPKRW | 'KFTC18-WMCo 8am LDN', 'KFTC18-WMCo 1pm LDN', 'KFTC18-WMCo 4pm LDN', 'KFTC18-<br>BFIX GBP L080'                                                                                                               |  |\n| GBPKZT | 'KZFXWA-WMCo<br>8am<br>LDN',<br>'KZFXWA-WMCo<br>1pm<br>LDN',<br>'KZFXWA-WMCo<br>4pm<br>LDN',<br>'KZFXWA-BFIX GBP L080'                                                                                        |  |\n| GBPMYR | 'MYR PPKM-WMCo 8am LDN', 'MYR PPKM-WMCo 1pm LDN', 'MYR PPKM-WMCo 4pm LDN',<br>'MYR PPKM-BFIX GBP L080'                                                                                                        |  |\n| GBPPEN | 'PEN05-WMCo 8am LDN', 'PEN05-WMCo 1pm LDN', 'PEN05-WMCo 4pm LDN', 'PEN05-BFIX GBP<br>L080'                                                                                                                    |  |\n| GBPPHP | 'PDSPESO-WMCo 8am LDN', 'PDSPESO-WMCo 1pm LDN', 'PDSPESO-WMCo 4pm LDN',<br>'PDSPESO-BFIX GBP L080'                                                                                                            |  |\n\n![](_page_28_Picture_0.jpeg)\n\n| Symbol | List of supported fixing source values                                                                                                 |\n|--------|----------------------------------------------------------------------------------------------------------------------------------------|\n| GBPRSD | 'RSDFIX-WMCo 8am LDN', 'RSDFIX-WMCo 1pm LDN', 'RSDFIX-WMCo 4pm LDN', 'RSDFIX<br>BFIX GBP L080'                                         |\n| GBPRUB | 'RUB MOEX (RUB05)-WMCo 8am LDN', 'RUB MOEX (RUB05)-WMCo 1pm LDN', 'RUB MOEX<br>(RUB05)-WMCo 4pm LDN', 'RUB MOEX (RUB05)-BFIX GBP L080' |\n| GBPTWD | 'TAIFX1-WMCo 8am LDN', 'TAIFX1-WMCo 1pm LDN', 'TAIFX1-WMCo 4pm LDN', 'TAIFX1-BFIX<br>GBP L080'                                         |\n| GBPUAH | 'EMTAUAHFUIX-WMCo 8am LDN', 'EMTAUAHFUIX-WMCo 1pm LDN', 'EMTAUAHFUIX-WMCo<br>4pm LDN', 'EMTAUAHFUIX-BFIX GBP L080'                     |\n| GBPVES | 'VEB01-WMCo 8am LDN', 'VEB01-WMCo 1pm LDN', 'VEB01-WMCo 4pm LDN', 'VEB01-BFIX<br>GBP L080'                                             |\n| GBPVND | 'ABSIRFIX01-WMCo 8am LDN', 'ABSIRFIX01-WMCo 1pm LDN', 'ABSIRFIX01-WMCo 4pm LDN',<br>'ABSIRFIX01-BFIX GBP L080'                         |\n| BRL*** | 'NDF Asiatica', 'Pontos sobre PTAX'                                                                                                    |\n\nTable 7.2: Supported fixing source string for NDF/NDS.\n\n# <span id=\"page-29-0\"></span>**7.3 CFI Codes**\n\nCFI codes used are based on the ISO 10962 standard. Please note that with options, the defined CFI code is used to identify the type of the option (CALL/PUT) with relation to currency 1 as it's set in the 360T system. Due to ANNA-DSB normalization, the CFI code passed in this API may not match that of the ISIN provided.\n\n| CFI Code | Details                                                                                                                    |\n|----------|----------------------------------------------------------------------------------------------------------------------------|\n| JFTXFP   | Fx Spot, Fx Forward and Fx Option with Delta Hedge Strategy, as well as spot and forward legs of block<br>trades and swaps |\n| JFTXFC   | NDF products as well as legs of NDS                                                                                        |\n| JFRXFP   | Fx Spot and Fx Forward with CNH                                                                                            |\n| SFCXXP   | Fx Swap including forward swaps                                                                                            |\n| SFCXXC   | NDS                                                                                                                        |\n| HFTAVP   | Fx European Option with CALL on the first currency                                                                         |\n| HFTDVP   | Fx European Option with PUT on the first currency                                                                          |\n| HFTBVP   | Fx American Option with CALL on the first currency                                                                         |\n| HFTEVP   | Fx American Option with PUT on the first currency                                                                          |\n| MRCXXX   | Fx Option with Delta Hedge StrategyMoney and Market Deposit                                                                |\n\nTable 7.3: Supported CFI codes.\n\n# <span id=\"page-30-0\"></span>**7.4 Soap Sample Request**\n\nFollowing table contains example Soap envelope messages for sending requests to the Order Market Taker XML API.\n\n| Order API Action       | Sample Message                                                                                 |\n|------------------------|------------------------------------------------------------------------------------------------|\n| Trade Intention Submit |                                                                                                |\n|                        |                                                                                                |\n|                        | xml version=\"1.0\" encoding=\"UTF−8\" standalone=\"yes\"?                                           |\n|                        | <soap:envelope< td=\"\"></soap:envelope<>                                                        |\n|                        | xmlns:SOAP=\"http://schemas.xmlsoap.org/soap/envelope/\">                                        |\n|                        | <soap:header></soap:header>                                                                    |\n|                        | <soap:body></soap:body>                                                                        |\n|                        | <tradeintentionsubmitrequest xmlns=\"urn:360t:orderapi:services\"></tradeintentionsubmitrequest> |\n|                        | <tradeintention></tradeintention>                                                              |\n|                        | <externalid>\"EMS upload order30\"</externalid>                                                  |\n|                        | <editable>true</editable>                                                                      |\n|                        | <trade></trade>                                                                                |\n|                        | <individual>Req1.Treasurer1</individual>                                                       |\n|                        | <legalentity>MT.Req1</legalentity>                                                             |\n|                        | <product></product>                                                                            |\n|                        | <fxoutright></fxoutright>                                                                      |\n|                        | <actiontype>BUYER</actiontype>                                                                 |\n|                        | <currency1>EUR</currency1>                                                                     |\n|                        | <currency2>USD</currency2>                                                                     |\n|                        | <outrightvaluedate>2020−10−20</outrightvaluedate>                                              |\n|                        | <outrightsplitvaluedate>2020−10−21</outrightsplitvaluedate>                                    |\n|                        | <notionalcurrency>EUR</notionalcurrency>                                                       |\n|                        | <notionalamount>1000000</notionalamount>                                                       |\n|                        |                                                                                                |\n|                        |                                                                                                |\n|                        |                                                                                                |\n|                        |                                                                                                |\n|                        |                                                                                                |\n|                        |                                                                                                |\n|                        |                                                                                                |\n|                        |                                                                                                |\n|                        |                                                                                                |\n\n#### 7.4. Soap Sample Request Chapter 7: Appendix\n\n| Order API Action       | Sample Message                                                                                                                                                                                                                                                                                                                                                                                                                         |\n|------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Trade Intention Accept |                                                                                                                                                                                                                                                                                                                                                                                                                                        |\n|                        | xml version=\"1.0\" encoding=\"UTF−8\" standalone=\"yes\"?<br><soap:envelope<br>xmlns:SOAP=\"http://schemas.xmlsoap.org/soap/envelope/\"&gt;<br/><soap:header></soap:header><br/><soap:body><br/><tradeintentionaccept xmlns=\"urn:360t:orderapi:services\"><br/><tradeintentionids><br/><tradeintentionid>\"EMS upload order30\"</tradeintentionid><br/></tradeintentionids><br/></tradeintentionaccept><br/></soap:body><br/></soap:envelope<br> |\n| Trade Intention Cancel |                                                                                                                                                                                                                                                                                                                                                                                                                                        |\n|                        | xml version=\"1.0\" encoding=\"UTF−8\" standalone=\"yes\"?<br><soap:envelope<br>xmlns:SOAP=\"http://schemas.xmlsoap.org/soap/envelope/\"&gt;<br/><soap:header></soap:header><br/><soap:body><br/><tradeintentioncancel xmlns=\"urn:360t:orderapi:services\"><br/><tradeintentionids><br/><tradeintentionid>\"EMS upload order30\"</tradeintentionid><br/></tradeintentionids><br/></tradeintentioncancel><br/></soap:body><br/></soap:envelope<br> |\n| Trade Intention Status |                                                                                                                                                                                                                                                                                                                                                                                                                                        |\n|                        | xml version=\"1.0\" encoding=\"UTF−8\" standalone=\"yes\"?<br><soap:envelope<br>xmlns:SOAP=\"http://schemas.xmlsoap.org/soap/envelope/\"&gt;<br/><soap:header></soap:header><br/><soap:body><br/><tradeintentionstatus xmlns=\"urn:360t:orderapi:services\"><br/><tradeintentionids><br/><tradeintentionid>\"EMS upload order30\"</tradeintentionid><br/></tradeintentionids><br/></tradeintentionstatus><br/></soap:body><br/></soap:envelope<br> |\n\nTable 7.4: XML MT Order API Actions.\n\n7.5. Sample Code Chapter 7: Appendix\n\n# <span id=\"page-32-0\"></span>**7.5 Sample Code**\n\nThe following two sections present a generic way to connect to 360T's access points.\n\n#### <span id=\"page-32-1\"></span>**7.5.1 Sample Code for the REST interface**\n\n```\n2 import java.io.*;\n3 import java.net.URL;\n4 import java.security.KeyStore;\n5 import java.util.*;\n6 import javax.net.ssl.*;\n7 import org.apache.http.*;\n9 public class QuickStartGuide {\n10 private static final String TLS_PROTOCOL_NAME = \"TLS\";\n11 private static final String KEY_STORE_TYPE = \"JKS\";\n12 private static final String UTF_8_CHARSET_NAME = \"UTF−8\";\n14 private static final String HTTPS_SCHEMA_NAME = \"https\";\n15 private static final String IP_ADDRESS = \"************\";\n16 private static final String PORT = \"6445\";\n18 private static final String PASSWORD = \"<password provided by 360T>\";\n19 private static final String FILEPATH = \"<path_to_file_containing_trade_intentions>\";\n20 private static final String TI_IDS = \"<IDs of some previously submitted TI, comma separated>\";\n21 private static final String KEYSTORE_FILE_PATH = \"<path_to_keystore_file>\";\n23 private static String SEND = \"send\";\n24 private static String route = null;\n25 private static java.util.List<String> routes = new ArrayList<String>();\n26 static {\n27 routes.add(\"tradeIntention\");\n28 routes.add(\"tradeIntentionAccept\");\n29 routes.add(\"tradeIntentionCancel\");\n30 routes.add(\"tradeIntentionStatus\");\n31 routes.add(\"tradeIntentionTrades\");\n32 routes.add(\"tradeIntentionStatusTrades\");\n34 route = routes.get((new Random()).nextInt(routes.size()));\n35 }\n37 public static void main(String[] args) {\n38 try {\n39 HttpClient httpClient = createHttpClient();\n40 HttpResponse httpResponse = null;\n42 if (SEND.equalsIgnoreCase(route)) {\n43 httpResponse = httpClient.execute(createSubmitPost(route));\n44 } else {\n45 httpResponse = httpClient.execute(createSubmitGet(route));\n46 }\n47 System.out.println(\"Response \" + httpResponse.getStatusLine());\n48 System.out.println(\"Body: \" + getResponseBody(httpResponse));\n50 } catch (Exception exception) {\n51 exception.printStackTrace();\n52 }\n53 }\n55 private static HttpPost createSubmitPost(String route) throws Exception {\n56 File file = new File(FILEPATH);\n57 String urlName = HTTPS_SCHEMA_NAME + \"://\" + IP_ADDRESS + \":\" + PORT;\n58 URL url = new URL(urlName + \"/\" + route);\n59 FileEntity fileEntity = new FileEntity(file, ContentType.APPLICATION_XML);\n60 HttpPost httpPost = new HttpPost(url.toExternalForm());\n62 httpPost.setEntity(fileEntity);\n64 return httpPost;\n65 }\n67 private static HttpGet createSubmitGet(String route) throws Exception {\n68 String id = \"?id=\" + TI_IDS.replaceAll(\",\", \"&id=\");\n69 String urlName = HTTPS_SCHEMA_NAME + \"://\" + IP_ADDRESS + \":\" + PORT;\n70 URL url = new URL(urlName + \"/\" + route + \"/\" + id.replaceAll(\" \", \"%20\"));\n72 return new HttpGet(url.toExternalForm());\n73 }\n75 private static String getResponseBody(HttpResponse httpResponse) throws Exception {\n76 InputStreamReader reader = new InputStreamReader(httpResponse.getEntity().getContent(), UTF_8_CHARSET_NAME);\n77 StringBuffer buffer = new StringBuffer();\n78 for (int character = reader.read(); character != −1; character = reader.read()) {\n79 if (character != 0) {\n80 buffer.append((char) character);\n81 }\n82 }\n84 return buffer.toString();\n85 }\n87 private static HttpClient createHttpClient() throws Exception {\n88 SSLContext sslContext = SSLContext.getInstance(TLS_PROTOCOL_NAME);\n```\n\n#### 7.5. Sample Code Chapter 7: Appendix\n\n| 89  | TrustManagerFactory trustKeyManager = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());                 |\n|-----|-----------------------------------------------------------------------------------------------------------------------------------|\n| 90  | KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());                     |\n| 91  | KeyStore keyStore = KeyStore.getInstance(KEY_STORE_TYPE);                                                                         |\n| 92  | File truststore = new File(KEYSTORE_FILE_PATH);                                                                                   |\n| 93  | keyStore.load(new FileInputStream(truststore), PASSWORD.toCharArray());                                                           |\n| 94  |                                                                                                                                   |\n| 95  | trustKeyManager.init(keyStore);                                                                                                   |\n| 96  | keyManagerFactory.init(keyStore, PASSWORD.toCharArray());                                                                         |\n| 97  | sslContext.init(keyManagerFactory.getKeyManagers(), trustKeyManager.getTrustManagers(), null);                                    |\n| 98  | SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext);                                  |\n| 99  | SchemePortResolver schemePortResolver = new SchemePortResolver() {                                                                |\n| 100 |                                                                                                                                   |\n| 101 | @Override                                                                                                                         |\n| 102 | public int resolve(HttpHost host) throws UnsupportedSchemeException {                                                             |\n| 103 | String schemeName = host.getSchemeName();                                                                                         |\n| 104 | if (schemeName == HTTPS_SCHEMA_NAME) {                                                                                            |\n| 105 | return 6445;                                                                                                                      |\n| 106 | }                                                                                                                                 |\n| 107 | return 0;                                                                                                                         |\n| 108 | }                                                                                                                                 |\n| 109 | };                                                                                                                                |\n| 110 |                                                                                                                                   |\n| 111 | return HttpClientBuilder.create().setSchemePortResolver(schemePortResolver).setSSLSocketFactory(connectionSocketFactory).build(); |\n| 112 | }                                                                                                                                 |\n| 113 | }                                                                                                                                 |\n|     |                                                                                                                                   |\n\n7.5. Sample Code Chapter 7: Appendix\n\n#### <span id=\"page-34-0\"></span>**7.5.2 Sample Code for SOAP actions**\n\n```\n1 import java.io.*;\n2 import java.net.URL;\n3 import java.security.KeyStore;\n4 import java.util.*;\n5 import javax.net.ssl.*;\n6 import org.apache.http.*;\n7 import com.three60t.tex.app.util.FileUtils;\n9 public class SoapQuickStartGuide {\n10 private static final String TLS_PROTOCOL_NAME = \"TLS\";\n11 private static final String KEY_STORE_TYPE = \"JKS\";\n12 private static final String UTF_8_CHARSET_NAME = \"UTF−8\";\n13 private static final String HTTPS_SCHEMA_NAME = \"https\";\n14 private static final String IP_ADDRESS = \"************\";\n15 private static final String PORT = \"6445\";\n16 private static final String PASSWORD = \"<password provided by 360T>\";\n17 private static final String FILEPATH = \"<path_to_file_containing_soap_content>\";\n18 private static final String KEYSTORE_FILE_PATH = \"<path_to_keystore_file>\";\n20 private static String action = null;\n21 private static java.util.List<String> actions = new ArrayList<String>();\n22 static {\n23 actions.add(\"urn:360t:orderapi:tradeIntentionSubmit\");\n24 actions.add(\"urn:360t:orderapi:tradeIntentionAccept\");\n25 actions.add(\"urn:360t:orderapi:tradeIntentionCancel\");\n26 actions.add(\"urn:360t:orderapi:tradeIntentionStatus\");\n27 actions.add(\"urn:360t:orderapi:tradeIntentionTrades\");\n28 actions.add(\"urn:360t:orderapi:tradeIntentionStatusTrades\");\n30 action = actions.get((new Random()).nextInt(actions.size()));\n31 }\n33 public static void main(String[] args) {\n34 try {\n35 HttpClient httpClient = createHttpClient();\n36 HttpPost httpPost = createSubmitPost(action);\n37 HttpResponse httpResponse = httpClient.execute(httpPost);\n39 System.out.println(\"Response \" + httpResponse.getStatusLine());\n40 System.out.println(\"Body: \" + getResponseBody(httpResponse));\n41 } catch (Exception e) {\n42 e.printStackTrace();\n43 }\n45 }\n47 private static HttpClient createHttpClient() throws Exception {\n48 SSLContext sslContext = SSLContext.getInstance(TLS_PROTOCOL_NAME);\n49 TrustManagerFactory trustKeyManager = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());\n50 KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());\n51 KeyStore keyStore = KeyStore.getInstance(KEY_STORE_TYPE);\n52 File truststore = new File(KEYSTORE_FILE_PATH);\n53 keyStore.load(new FileInputStream(truststore), PASSWORD.toCharArray());\n55 trustKeyManager.init(keyStore);\n56 keyManagerFactory.init(keyStore, PASSWORD.toCharArray());\n57 sslContext.init(keyManagerFactory.getKeyManagers(), trustKeyManager.getTrustManagers(), null);\n58 SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext);\n59 SchemePortResolver schemePortResolver = new SchemePortResolver() {\n60 @Override\n61 public int resolve(HttpHost host) throws UnsupportedSchemeException {\n62 String schemeName = host.getSchemeName();\n63 return schemeName == HTTPS_SCHEMA_NAME ? PORT : 0;\n64 }\n65 };\n67 return HttpClientBuilder.create().setSchemePortResolver(schemePortResolver).setSSLSocketFactory(connectionSocketFactory).build();\n68 }\n70 private static HttpPost createSubmitPost(String action) throws Exception {\n71 String soapContent = FileUtils.getContentAsString(FILEPATH);\n72 String urlName = HTTPS_SCHEMA_NAME + \"://\" + IP_ADDRESS + \":\" + PORT;\n73 URL url = new URL(urlName);\n75 AbstractHttpEntity entity = new StringEntity(soapContent, ContentType.TEXT_XML);\n76 entity.setContentType(\"text/xml\");\n78 HttpPost httpPost = new HttpPost(url.toExternalForm());\n79 httpPost.setEntity(entity);\n80 httpPost.setHeader(\"soapaction\", action);\n82 return httpPost;\n83 }\n85 private static String getResponseBody(HttpResponse httpResponse) throws Exception {\n86 InputStreamReader reader = new InputStreamReader(httpResponse.getEntity().getContent(), UTF_8_CHARSET_NAME);\n87 StringBuffer buffer = new StringBuffer();\n88 for (int character = reader.read(); character != −1; character = reader.read()) {\n89 if (character != 0) {\n90 buffer.append((char) character);\n91 }\n92 }\n93 return buffer.toString();\n94 }\n95 }\n```\n\n# <span id=\"page-35-0\"></span>**7.6 Web Browser Plugin Connectivity Test**\n\nThe following section describes how to test the connection using the REST Client Plugin (https://addons.mozilla.org/en-US/firefox/addon/restclient/) in Mozilla Firefox.\n\nPlease follow the next steps:\n\n- 1. Install the addon in Mozilla Firefox.\n- 2. Install the .p12 Certificate in Firefox.\n  - Open 'Firefox Preferences'.\n  - Select the 'Certificates' tab.\n\n|                                                                                                   |        |                         | <b>Firefox Preferences</b>  |    |          |      |          |\n|---------------------------------------------------------------------------------------------------|--------|-------------------------|-----------------------------|----|----------|------|----------|\n| $\\frac{1}{2}$<br>$ \\times$ $\\lfloor$<br>Tabs<br>General                                           | Search | Content                 | <b>Applications Privacy</b> | 60 | Security | Sync | Advanced |\n| General Data Choices                                                                              |        | Network                 | Certificates<br>Update      |    |          |      |          |\n| When a server requests my personal certificate:<br>○ Select one automatically ● Ask me every time |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n| ● Query OCSP responder servers to confirm the current validity of certificates                    |        |                         |                             |    |          |      |          |\n| View Certificates                                                                                 |        | <b>Security Devices</b> |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n|                                                                                                   |        |                         |                             |    |          |      |          |\n| Help                                                                                              |        |                         |                             |    |          |      | Close    |\n\n• Click on the 'View Certificates' button. The 'Certificate Manager' window will appear. Click on the 'Your Certificates' tab and then on the 'Import' button to import your 360T .p12 certificate into the browser.\n\n![](_page_36_Picture_0.jpeg)\n\n|                                                     |                                                                   | <b>Certificate Manager</b> |                   |    |\n|-----------------------------------------------------|-------------------------------------------------------------------|----------------------------|-------------------|----|\n| Your Certificates People Servers Authorities Others |                                                                   |                            |                   |    |\n|                                                     | You have certificates from these organisations that identify you: |                            |                   |    |\n| Certificate Name                                    | <b>Security Device</b>                                            | <b>Serial Number</b>       | <b>Expires On</b> | œ. |\n|                                                     |                                                                   |                            |                   |    |\n|                                                     |                                                                   |                            |                   |    |\n|                                                     |                                                                   |                            |                   |    |\n|                                                     |                                                                   |                            |                   |    |\n|                                                     |                                                                   |                            |                   |    |\n|                                                     |                                                                   |                            |                   |    |\n|                                                     |                                                                   |                            |                   |    |\n| Backup<br>View                                      | Backup All<br>Import                                              | Delete                     |                   |    |\n|                                                     |                                                                   |                            |                   | OK |\n\n- Navigate to the provided .p12 file using the file navigator. When the file is selected, you should be prompted for the client password. Enter the password to complete the certificate export.\n- Once imported, the certificate will appear in the 'Certificate Manager' in the 'Your Certificates' tab. Double clicking on the certificate will open up a window in which the details of the certificates can be seen.\n\n|                                                                   |        |                                   |                                 |        |        | <b>Certificate Manager</b> |                   | ٠  | $\\mathbf{x}$ |\n|-------------------------------------------------------------------|--------|-----------------------------------|---------------------------------|--------|--------|----------------------------|-------------------|----|--------------|\n| Your Certificates                                                 |        | People Servers Authorities Others |                                 |        |        |                            |                   |    |              |\n| You have certificates from these organisations that identify you: |        |                                   |                                 |        |        |                            |                   |    |              |\n| Certificate Name                                                  |        |                                   | <b>Security Device</b>          |        |        | <b>Serial Number</b>       | <b>Expires On</b> | œ. |              |\n| $-360T$                                                           |        |                                   |                                 |        |        |                            |                   |    |              |\n| <b>Imported Certificate</b>                                       |        |                                   | <b>Software Security Device</b> |        |        | 65                         | 28/09/19          |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n| View                                                              | Backup | Backup All                        |                                 | Import | Delete |                            |                   |    |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   | OK |              |\n|                                                                   |        |                                   |                                 |        |        |                            |                   |    |              |\n\n- 3. Test Order XML API Connectivity\n  - Any of the URLs described in the previous sections can be tested with this tool. For example, the following URL can be used for retrieving the status of an uploaded TI (the number at the end can be modified to be a valid trade number, if required): https://************:6445/tradeIntentionStatus/123\n\n![](_page_37_Picture_0.jpeg)\n\n7.6. Web Browser Plugin Connectivity Test Chapter 7: Appendix\n\n| RESTClient, a debug $\\sqrt{2}$ RESTClient<br>$\\pmb{\\times}$<br>$\\Phi$<br>☆ 自<br>← ehrome://restclient/content/restclient.html<br>$\\vee$ C <sup>i</sup><br>Authentication $\\forall$ Headers $\\forall$ View $\\forall$<br>Favorite Requests =<br>Setting $\\sim$<br>$Fie =$<br>[-] Request<br>$\\checkmark$<br><b>URL</b><br>https://193.29.38.58:6445/tradeIntentionStatus/123<br>☆ v<br>GET<br>Method<br><b>Body</b><br><b>Request Body</b><br>Home   Github   Issues   Donate |                                 |\n|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | $\\ddot{\\phantom{a}}$<br>$\\circ$ |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | <b>RESTClient</b>               |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | <b>SEND</b>                     |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | Back to top                     |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 |\n\n- The tool will ask you to choose a certificate. *Note*: it will remember the selection until the next time Firefox is restarted.\n- If everything is properly configured, a valid response will appear, as shown below:\n\n|                                                                                        |                                                   |                                                       | <b>RESTClient - Mozilla Firefox</b> |                     |                | $\\pmb{\\times}$    |\n|----------------------------------------------------------------------------------------|---------------------------------------------------|-------------------------------------------------------|-------------------------------------|---------------------|----------------|-------------------|\n|                                                                                        | RESTClient, a debug $\\frac{1}{2}$ RESTClient      | $\\pmb{\\times}$                                        | $\\ddot{\\phantom{1}}$                |                     |                |                   |\n|                                                                                        | ← ) @ chrome://restclient/content/restclient.html |                                                       |                                     |                     | <b>▽ C ☆ 自</b> | $\\equiv$          |\n| Authentication ~<br>$Fie =$                                                            | Headers $=$<br>$View =$                           |                                                       |                                     | Favorite Requests = | Setting $-$    | <b>RESTClient</b> |\n| [-] Request                                                                            |                                                   |                                                       |                                     |                     |                |                   |\n| Method<br><b>GET</b>                                                                   | $\\checkmark$<br><b>URL</b>                        | https://193.29.38.58:6445/tradeIntentionStatus/123    |                                     |                     | ☆ >            | <b>SEND</b>       |\n| Body                                                                                   |                                                   |                                                       |                                     |                     |                |                   |\n| <b>Request Body</b>                                                                    |                                                   |                                                       |                                     |                     |                |                   |\n|                                                                                        |                                                   |                                                       |                                     |                     |                |                   |\n| [-] Response                                                                           |                                                   |                                                       |                                     |                     |                |                   |\n| Response Headers                                                                       | Response Body (Raw)                               | Response Body (Highlight)                             | Response Body (Preview)             |                     |                |                   |\n| <b>Status Code</b><br>1.<br>Content-Length<br>2.<br>Content-Type<br>3.<br>4.<br>Server | $: 200$ OK<br>$\\div$ 312                          | : text/xml; charset=UTF-8<br>: Jetty(8.1.4.v20120524) |                                     |                     |                |                   |\n| Home   Github   Issues   Donate                                                        |                                                   |                                                       |                                     |                     |                | Back to top       |\n\n*Note:* A similar procedure can be performed in the Google Chrome browser, as well.\n\n# <span id=\"page-38-0\"></span>**8 Version Log**\n\n| Version | Date       | Comments                                                                                                                      |\n|---------|------------|-------------------------------------------------------------------------------------------------------------------------------|\n| 0.1     | 08.10.2011 | Initial draft                                                                                                                 |\n| 0.2     | 20.10.2011 | Cancellation and Status                                                                                                       |\n| 0.3     | 08.11.2011 | Fetch trades and communication                                                                                                |\n| 0.4     | 17.11.2011 | Batch intention upload and querying                                                                                           |\n| 0.5     | 23.11.2011 | Custom fields manual                                                                                                          |\n| 0.6     | 24.11.2011 | Custom field update                                                                                                           |\n| 0.7     | 30.11.2011 | XML schema                                                                                                                    |\n| 0.8     | 01.12.2011 | Error reporting                                                                                                               |\n| 0.9     | 24.03.2012 | Fixing and stop orders                                                                                                        |\n| 1.0     | 02.05.2012 | NDF and NDS                                                                                                                   |\n| 1.1     | 01.08.2012 | Trade as/Trade on behalf and group identifiers                                                                                |\n| 1.2     | 06.10.2012 | Changes in field names and structure                                                                                          |\n| 1.2     | 12.10.2012 | Multi-trade intentions                                                                                                        |\n| 1.3     | 12.12.2013 | Schema version in trades request                                                                                              |\n| 1.4     | 21.02.2014 | Options                                                                                                                       |\n| 1.4     | 26.02.2014 | Added preferred providers                                                                                                     |\n| 1.4     | 04.03.2014 | Added BNA fixing reference                                                                                                    |\n| 1.4     | 27.03.2014 | Loan Deposits                                                                                                                 |\n| 1.4     | 03.04.2014 | Offline orders                                                                                                                |\n| 1.4     | 25.04.2014 | New request action status + trades                                                                                            |\n| 1.4     | 25.04.2014 | SOAP                                                                                                                          |\n| 1.5     | 26.09.2014 | Add flag for OCO orders<br>Add comment tag for the status response of Canceled/Rejected trade Intentions<br>Add productId tag |\n| 1.5     | 27.02.2015 | Update cutoffs                                                                                                                |\n| 2.0     | 22.06.2015 | Moved to new Order API added timeInforce                                                                                      |\n| 2.1     | 14.03.2016 | Added Amend and Withdraw request                                                                                              |\n| 2.1     | 14.12.2016 | Added WM/Reuters fixing reference                                                                                             |\n| 2.1.1   | 23.10.2018 | Added regulatory data, auto execution instructions, order book name and fee upload.                                           |\n| 2.1.2   | 23.06.2019 | Added custodian check flag.                                                                                                   |\n| 2.1.3   | 01.11.2019 | Added FX Security Conversion flag.                                                                                            |\n| 2.1.4   | 21.02.2020 | Added Order Group ID flag.                                                                                                    |\n|         |            | continued on next page                                                                                                        |\n\nChapter 8: Version Log\n\n| Version | Date       | Comments                                                                                                    |\n|---------|------------|-------------------------------------------------------------------------------------------------------------|\n| 2.1.5   | 27.09.2021 | Added new products:                                                                                         |\n|         |            | • Metals Outright                                                                                           |\n|         |            | • Metals Spread                                                                                             |\n|         |            | • Metals Quarterly Strip                                                                                    |\n|         |            | Added Split value date.                                                                                     |\n| 2.1.6   | 21.12.2021 | Added support for the rolling spot flag.                                                                    |\n| 2.1.7   | 11.02.2022 | Added support for providing comments at order upload time.                                                  |\n| 2.1.8   | 30.09.2022 | Updating available fixing references for fixing orders.                                                     |\n| 2.1.9   | 24.02.2023 | Adding support for FX Option Strategy target premium.                                                       |\n| 2.1.10  | 20.06.2023 | Adding support for uploading orders to the UK MTF and EU MTF venues.                                        |\n| 2.1.11  | 05.10.2023 | Added support for backdated trades via offline confirmations by adding trade date field<br>to order upload. |\n| 2.1.12  | 08.05.2024 | Added support for uploading orders to Singapore Regulated Market Operator (RMO)<br>venue.                   |\n\nTable 8.1: Version history", "metadata": {"lang": "en"}}]