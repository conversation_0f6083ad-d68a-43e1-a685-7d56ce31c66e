[{"id": "1", "text": "![](_page_0_Picture_0.jpeg)\n\n# **US<PERSON> GUIDE BRIDGE ADMINISTRATION COUNTERPART RELATIONSHIP MANAGEMENT**\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\n**User Guide 360T Bridge Administration: Counterpart Relationship Management for Market Maker**\n\nRelease 4.17 (March 2023)\n\n© 360 Treasury Systems AG, 2023 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG\n\n![](_page_1_Picture_0.jpeg)\n\n# **CONTENTS**\n\n| 1 | Introduction                                               | 4  |\n|---|------------------------------------------------------------|----|\n| 2 | Getting Started                                            | 4  |\n| 3 | Counterpart Relationship Management                        | 6  |\n|   | 3.1<br>Opening the counterpart relationship configuration  | 6  |\n|   | 3.2<br>Notification about incoming requested relationships | 7  |\n|   | 3.3<br>Accepting or rejecting a relationship               | 8  |\n| 4 | Filter, search and sorting options                         | 11 |\n|   | 4.1<br>Filter & search                                     | 11 |\n|   | 4.2<br>Sort                                                | 12 |\n| 5 | Download and upload counterparts                           | 13 |\n|   | 5.1<br>Download counterparts                               | 13 |\n|   | 5.2<br>Upload counterparts                                 | 13 |\n| 6 | Audit log                                                  | 14 |\n| 7 | Contacting 360T                                            | 19 |\n\n![](_page_2_Picture_0.jpeg)\n\n# **FIGURES**\n\n| Figure 1 Header Bar4                                               |  |\n|--------------------------------------------------------------------|--|\n| Figure 2 Bridge Administration: Homepage<br>5                      |  |\n| Figure 3 Quick Navigation Toolbar<br>5                             |  |\n| Figure 4 Bridge Administration CRM<br>6                            |  |\n| Figure 5 Opening Counterpart Relationships Configuration7          |  |\n| Figure 6 Notifications7                                            |  |\n| Figure 7 Taker relationships<br>8                                  |  |\n| Figure 8 Trader Worksheet: Relationship not accepted warning10     |  |\n| Figure 9 Accepting a relationship<br>10                            |  |\n| Figure 10 Rejecting a relationship<br>11                           |  |\n| Figure 11 Selecting Multiple Relationships<br>11                   |  |\n| Figure 12 Searching relationships<br>12                            |  |\n| Figure 13 Filtered relationships<br>12                             |  |\n| Figure 14 Sorting relationships<br>12                              |  |\n| Figure 15 Download counterparts13                                  |  |\n| Figure 16 Relationships csv file13                                 |  |\n| Figure 17 Upload counterparts<br>13                                |  |\n| Figure 18 Updated relationships14                                  |  |\n| Figure 19 Audit Log14                                              |  |\n| Figure 20 Audit Log criteria selection15                           |  |\n| Figure 21 Audit Log Category selection15                           |  |\n| Figure 22 Audit Log Company selection<br>16                        |  |\n| Figure 23 Audit Log CRM results16                                  |  |\n| Figure 24 -<br>Audit Log Event Date -<br>pre defined periods<br>17 |  |\n| Figure 25 -<br>Audit Log Event Date -<br>calendars17               |  |\n| Figure 26 Audit Log<br>CRM results and details18                   |  |\n| Figure 27 Audit Log change details<br>18                           |  |\n\n![](_page_3_Picture_0.jpeg)\n\n# **User Guide 360T Bridge Counterpart Relationship Management**\n\n# <span id=\"page-3-0\"></span>**1 Introduction**\n\nThis user manual describes the functionality of the Counterpart Relationship Management from a market maker's perspective.\n\nThe Counterpart Relationship Management allows market takers and market makers to manage their relationships. All changes made become effective immediately.\n\nThe Bridge Administration tool is available only to users who have administrator rights. As part of the onboarding process, a sub-set of administrator users to whom the tool is to be accessible must be defined. These users are then responsible for entering the information as described in the following chapters. Additionally, it is possible to grant read-only access to certain administrator users, however, only viewing and downloading CRM information will be possible with this role. <NAME_EMAIL> or your customer relationship manager for setting up the administrator rights.\n\n# <span id=\"page-3-1\"></span>**2 Getting Started**\n\nThe Bridge Administration tool can be accessed via the menu option \"Administration\" in the screen header of the Bridge application. Several features may be visible in the left menu depending on user permissions.\n\n![](_page_3_Picture_9.jpeg)\n\n**Figure 1 Header Bar**\n\n<span id=\"page-3-2\"></span>The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools and actions for the user.\n\n![](_page_4_Picture_0.jpeg)\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\n![](_page_4_Figure_3.jpeg)\n\n<span id=\"page-4-0\"></span>**Figure 2 Bridge Administration: Homepage**\n\n![](_page_4_Figure_5.jpeg)\n\n<span id=\"page-4-1\"></span>**Figure 3 Quick Navigation Toolbar**\n\n![](_page_5_Picture_0.jpeg)\n\n# <span id=\"page-5-0\"></span>**3 Counterpart Relationship Management**\n\nThe \"CRM\" quick link, shown in Figure 2 [Bridge Administration: Homepage,](#page-4-0) opens a navigation panel which contains an institution tree. The tree includes a list of all entities configured under the main entity.\n\n![](_page_5_Picture_4.jpeg)\n\n**Figure 4 Bridge Administration CRM**\n\n## <span id=\"page-5-2\"></span><span id=\"page-5-1\"></span>**3.1 Opening the counterpart relationship configuration**\n\nClicking on the entity name in the quick navigation tool bar, shown in [Figure 3](#page-4-1) Quick [Navigation Toolbar,](#page-4-1) opens the counterpart relationships panels. Relationships with counterparts can be established for the different trading methods RFS, Orders, SEP, MmFund, ECP, TripartyRepo and MidMatch. For each of these products there is a different tab on the screen, as shown in Figure 5 [Opening Counterpart Relationships](#page-6-1)  [Configuration.](#page-6-1)\n\nIn \"Taker Relationships\" section, the market maker can review existing and incoming requested relationships as described ahead in chapter [3.3.](#page-7-0)\n\n![](_page_6_Picture_0.jpeg)\n\n|                         |                             | <b>BRIDGE ADMINISTRATION</b><br><b>TRADER WORKSHEET</b> | $+$                                           | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\mathbb{D}^1$ $\\circledast$ AA $-\\circledast \\times$ |                                                           |    |\n|-------------------------|-----------------------------|---------------------------------------------------------|-----------------------------------------------|-------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|----|\n|                         |                             | $\\rightarrow$<br>$\\alpha$                               | RFS Orders SEP                                |                                                                                                                   |                                                           | 土土 |\n|                         | 合                           | $\\mathbf{\\hat{m}}$ > BankP                              | Taker Relationships Auto Accept Configuration |                                                                                                                   |                                                           |    |\n|                         | $\\mathcal{L}_{\\mathcal{T}}$ | $\\underline{\\hat{\\pi}}$ BankP                           |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         | $\\ensuremath{\\boxdot}$      |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         | $\\circledcirc$              |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         | $\\overline{\\text{vir}}$     |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         | $\\overline{d}$              |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n|                         |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n| $\\zeta_{\\rm s}^{\\rm H}$ |                             |                                                         |                                               |                                                                                                                   |                                                           |    |\n| $\\mathbb C$<br>$\\circ$  |                             |                                                         |                                               |                                                                                                                   | Discard all Changes Construction                          |    |\n| Θ                       |                             |                                                         | Bank $P \\times$                               |                                                                                                                   |                                                           |    |\n|                         |                             | 1 BankP.TraderA, BankP // DEMO                          | $\\overline{\\phantom{a}}$                      |                                                                                                                   | Fri, 23. Apr 2021, 13:47:55 GMT // Connected [FFM] • DEMO |    |\n\n<span id=\"page-6-1\"></span>**Figure 5 Opening Counterpart Relationships Configuration**\n\n## <span id=\"page-6-0\"></span>**3.2 Notification about incoming requested relationships**\n\nIn case a market taker requests a relationship with a market maker, the market maker's counterpart relationship management administrator will be notified about this request.\n\nThe notification is displayed by clicking on the bell icon present in the upper-right side of the screen as shown in Figure 6 [Notifications.](#page-6-2)\n\n| Preferences | $\\vee$ Administration | $\\vee$ Help                                                             | ◑                                                                                                                                                                                             | Aа |   | 吊 |\n|-------------|-----------------------|-------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----|---|---|\n|             |                       |                                                                         | Notifications (0/1)                                                                                                                                                                           |    | ロ |   |\n|             |                       | <b>Information</b><br>counterpart relationships.<br>4/23/21, 4:11:44 PM | There are changes in the company<br>Please review the changes in the<br>Counterpart Relationships tool.<br>Please confirm by saving the configuration<br>to avoid this message in the future. |    |   | 侕 |\n|             |                       |                                                                         |                                                                                                                                                                                               |    |   |   |\n\n## <span id=\"page-6-2\"></span>**Figure 6 Notifications**\n\n![](_page_7_Picture_0.jpeg)\n\nIf the administrator is not logged in with an open counterpart relationship management tool at the time of an incoming requested relationship, the notification will be available at the next login.\n\n## <span id=\"page-7-0\"></span>**3.3 Accepting or rejecting a relationship**\n\nWhen a market taker requests a relationship with a market maker, the market maker's counterpart relationship management administrator will see a new entry on the Taker Relationships tab. This entry shows initially the Counterparty Status as \"Accepted\" and My Status as \"Undefined\", indicating that this relationship is yet to be accepted or rejected by the market maker. The relationship can be accepted or rejected either directly in the GUI or via csv upload (see chapter [5.2\\)](#page-12-2).\n\nAdditionally, market maker can be enabled for Auto-Accept Configuration, which allows to automatically accept requests from daughter entities of selected requesters. Please contact [<EMAIL>](mailto:<EMAIL>) to enable this feature.\n\nThe refresh button displayed on the upper-left side of the screen can be used to update the statuses or new incoming requests while the administrator is logged in as shown in Figure 7 [Taker relationships.](#page-7-1)\n\n|                                                                         | <b>TRADER WORKSHEET</b>                   | $+$<br><b>BRIDGE ADMINISTRATION</b>                               |                                    |                      |                                 |                                        | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Diamond$ $\\Diamond$ $\\Diamond$ AA $ \\ominus$ X |                                                           |\n|-------------------------------------------------------------------------|-------------------------------------------|-------------------------------------------------------------------|------------------------------------|----------------------|---------------------------------|----------------------------------------|-------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|\n| 合                                                                       | Q<br>$\\hat{\\mathsf{m}}$ > BankP           | RFS Orders SEP<br>$\\rightarrow$<br><b>Taker Relationships (1)</b> | Auto Accept Configuration          |                      |                                 |                                        |                                                                                                       | 土土                                                        |\n| $\\sqrt{2}$<br>$\\ensuremath{\\boxdot}$                                    | <b>血 BankP</b>                            |                                                                   |                                    |                      | $\\alpha$                        | $\\rightarrow$                          |                                                                                                       |                                                           |\n| $\\circledast$                                                           |                                           | Counterpart<br>BankClientP                                        | <b>Legal Entity</b><br>BankClientP | Long Name<br>\"Empty\" | My Status<br><b>O</b> Undefined | <b>Counterparty Status</b><br>Accepted |                                                                                                       | $\\checkmark$ $\\times$                                     |\n|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |\n|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |\n|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |\n|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |\n|                                                                         |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       |                                                           |\n| $\\zeta^{\\star}_{\\mathbf{z}^{\\star}}$<br>$\\mathbb C$<br>$\\bigcirc$       |                                           |                                                                   |                                    |                      |                                 |                                        |                                                                                                       | Save<br>Discard all Changes                               |\n| $\\qquad \\qquad \\qquad \\qquad \\qquad \\qquad \\qquad \\qquad \\qquad \\qquad$ |                                           | BankP $\\times$                                                    |                                    |                      |                                 |                                        |                                                                                                       |                                                           |\n|                                                                         | <sup>1</sup> BankP.TraderA, BankP // DEMO |                                                                   |                                    |                      | <b>EBCT</b>                     |                                        |                                                                                                       | Fri, 23. Apr 2021, 14:44:59 GMT // Connected [FFM] · DEMO |\n\n## <span id=\"page-7-1\"></span>**Figure 7 Taker relationships**\n\nFor counterpart relationships the following statuses exist:\n\n- Initial status undefined displayed as\n- Status accepted displayed as\n- Status rejected displayed as\n\nThe initial status of a new relationship is undefined.\n\n![](_page_8_Picture_0.jpeg)\n\n![](_page_9_Picture_0.jpeg)\n\nIn case the administrator is not logged in while a relationship request comes in, and the counterpart sends a RFS request, it will be routed for manual pricing as shown in Figure 8 [Trader Worksheet: Relationship not accepted warning.](#page-9-0) The traders of the market maker company will see that the relationship with that requester has not been accepted. The trader should then contact the company's counterpart relationship management administrator in order to finalize the relationship. The trader can price the requests for quote of the company.\n\nAll other deal types can only be done after the market maker has explicitly accepted the relationship.\n\n|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 | $\\vee$ Preferences                                  | v Administration<br>$\\vee$ Help                                                                  |                                                                                                                       |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|------------------------------|--------------------|-------------|---------|-------------------|---------|-----------------|-----------------------------------------------------|--------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------|\n|                                                                                                                                                                                                                                                                    | <b>TRADER WORKSHEET</b>                                                                                                                                                          |          | <b>BRIDGE ADMINISTRATION</b> |                    | $+$         |         |                   |         |                 |                                                     |                                                                                                  |                                                                                                                       |\n|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     |                                                                                                  | 章 宣                                                                                                                   |\n| $Q \\pm$<br>Dealer Intervention (1)<br><b>Fixing Orders Intervention (0)</b><br>Market Orders Intervention (0)                                                                                                                                                      |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         | • Request Offer | $Q \\equiv$                                          |                                                                                                  |                                                                                                                       |\n|                                                                                                                                                                                                                                                                    | <b>FX Forward</b><br><b>Effective C</b><br>Reference $# \\vee$ Timeout<br><b>Legal Entity</b><br><b>Currencies</b><br>Notional A<br>Type<br>Requester C<br>Requester A<br>Product |          |                              |                    |             |         |                   |         |                 |                                                     |                                                                                                  |                                                                                                                       |\n|                                                                                                                                                                                                                                                                    | <b>RFS</b>                                                                                                                                                                       | ******** | 00:37                        | <b>BankClientP</b> | BankClientP | Two Way | <b>FX Forward</b> | EUR/USD |                 | 1,000.00 EUR   Tue, 04. Mi (D  <br>$ \\times $ $\\in$ |                                                                                                  | $\\bigcirc$ 00:37<br>EUR / USD // 1,000.00 EUR                                                                         |\n| Your relationship with 'BankClientP' trading entity(s) of 'BankClientP' does<br>not have the status ACCEPTED yet. You are able to trade but please inform<br>your counterpart relationship administrator or call 360T CAS in order to<br>finalize the relationship |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     |                                                                                                  |                                                                                                                       |\n|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     | Bid<br>$.20549$ $\\bigcirc$<br>⊜<br>Margin (pips)<br>۸<br>$0.0\\,$ $\\circledcirc$                  | Offer<br>$\\bullet$ 1.20555 $\\bullet$ $\\circ$<br>Margin (pips)<br>$\\bullet$<br>$0.0$ $\\circledcirc$                    |\n|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     | <b>Bid Points</b><br>04. May 21<br>1.620<br>Margin (pips)<br>⊝<br>$0.000$ (D)<br><b>Bid Rate</b> | Offer Points<br>$\\bullet$<br>$1.650$ $\\bigoplus$ $\\bigoplus$<br>Margin (pips)<br>$\\circ$<br>$0.000$ $@$<br>Offer Rate |\n|                                                                                                                                                                                                                                                                    |                                                                                                                                                                                  |          |                              |                    |             |         |                   |         |                 |                                                     | 1.2056520                                                                                        | 1.20E<br>150                                                                                                          |\n\n<span id=\"page-9-0\"></span>**Figure 8 Trader Worksheet: Relationship not accepted warning**\n\nA relationship is accepted by clicking on the button on the right side of the Taker Relationships table as shown in Figure 9 [Accepting a relationship.](#page-9-1)\n\n| RFS<br>Orders SEP              |                                  |                       |                                              |                            |                            | $\\mathcal{F}\\ \\mathcal{F}$ |\n|--------------------------------|----------------------------------|-----------------------|----------------------------------------------|----------------------------|----------------------------|----------------------------|\n| <b>Taker Relationships (1)</b> | <b>Auto Accept Configuration</b> |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       | $\\begin{array}{c} \\n\\boxed{Q} \\n\\end{array}$ | $\\rightarrow)$             |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n| Counterpart                    | <b>Legal Entity</b>              | Long Name             | My Status                                    | <b>Counterparty Status</b> |                            | $\\nabla \\times$            |\n| BankClientP                    | BankClientP                      | $\\mathsf{``Empty\"''}$ | $\\bullet$ Accepted                           | Accepted                   |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            |                            |                            |\n|                                |                                  |                       |                                              |                            | <b>Discard all Changes</b> | Save*                      |\n|                                |                                  |                       |                                              |                            |                            |                            |\n\n<span id=\"page-9-1\"></span>**Figure 9 Accepting a relationship**\n\n![](_page_10_Picture_0.jpeg)\n\nA relationship is rejected by clicking on the button on the right side of the Taker Relationships table as shown in Figure 10 [Rejecting a relationship.](#page-10-2)\n\n| <b>RFS</b><br>Orders<br><b>SEP</b> |                                  |           |                    |                            |                            | 土土                                   |\n|------------------------------------|----------------------------------|-----------|--------------------|----------------------------|----------------------------|--------------------------------------|\n| <b>Taker Relationships</b>         | <b>Auto Accept Configuration</b> |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           | Q)                 | $\\rightarrow$              |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n| Counterpart                        | <b>Legal Entity</b>              | Long Name | My Status          | <b>Counterparty Status</b> |                            |                                      |\n| BankClientP                        | BankClientP                      | \"Empty\"   | $\\bullet$ Rejected | • Accepted                 |                            | $\\overline{\\mathbb{R}^{\\mathbb{N}}}$ |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            |                            |                                      |\n|                                    |                                  |           |                    |                            | <b>Discard all Changes</b> | Save*                                |\n\n### <span id=\"page-10-2\"></span>**Figure 10 Rejecting a relationship**\n\nThe change must be saved, by clicking on \"Save\" button to become effective.\n\nAfter saving the change, the updated status become available to the market taker who has requested the relationship.\n\nIt is possible to accept or reject multiple relationships at the same time by doing CTRL+click or Shift+click as shown in Figure 11 [Selecting Multiple Relationships](#page-10-3).\n\n| Counterpart  | <b>Legal Entity</b> | <b>Long Name</b> | My Status          | <b>Counterparty Status</b> |                              |\n|--------------|---------------------|------------------|--------------------|----------------------------|------------------------------|\n| <b>BankB</b> | BankB               | \"Empty\"          | • Accepted         | • Accepted                 | / X                          |\n| BankC        | BankC               | \"Empty\"          | Accepted           | <b>O</b> Undefined         | VX                           |\n| <b>BankD</b> | BankD               | \"Empty\"          | <b>O</b> Undefined | Undefined                  | <b>TXL</b><br>V.             |\n| BankE        | BankE               | \"Empty\"          | <b>Undefined</b>   | <b>Undefined</b>           | $\\checkmark$                 |\n| <b>BankG</b> | <b>BankG</b>        | \"Empty\"          | <b>Undefined</b>   | Undefined                  | $\\mathbb{R}^{\\times}$<br>l v |\n| BankH        | BankH               | BankH Plc        | <b>Undefined</b>   | <b>Undefined</b>           | $\\checkmark$                 |\n| Bankl        | Bankl               | \"Empty\"          | • Accepted         | <b>O</b> Undefined         | VX                           |\n| BankJ        | BankJ               | \"Empty\"          | Accepted           | <b>O</b> Undefined         | $\\vee$ X                     |\n|              |                     |                  |                    |                            |                              |\n\n<span id=\"page-10-3\"></span>**Figure 11 Selecting Multiple Relationships**\n\n# <span id=\"page-10-0\"></span>**4 Filter, search and sorting options**\n\n## <span id=\"page-10-1\"></span>**4.1 Filter & search**\n\nIt is possible to search and filter a specific Taker or reduced list of Takers by typing the Counterpart or letters contained in the Counterpart and clicking on the arrow on the right, as shown in Figure 12 [Searching relationships.](#page-11-1) The search parameter is applied to the column Counterpart which refers to the Market Taker.\n\n![](_page_11_Picture_0.jpeg)\n\n| <b>RFS</b><br><b>SEP</b><br>Orders |                                  |                  |                    |                            | 土土                    |\n|------------------------------------|----------------------------------|------------------|--------------------|----------------------------|-----------------------|\n| <b>Taker Relationships (6)</b>     | <b>Auto Accept Configuration</b> |                  |                    |                            |                       |\n|                                    |                                  |                  | $Q$ Client         | $\\rightarrow$              |                       |\n| Counterpart                        | <b>Legal Entity</b>              | <b>Long Name</b> | My Status          | <b>Counterparty Status</b> |                       |\n| BankClientA                        | <b>BankClientA</b>               | \"Empty\"          | <b>O</b> Undefined | Accepted                   | $\\sqrt{\\chi}$         |\n| <b>BankClientB</b>                 | <b>BankClientB</b>               | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\checkmark$ $\\times$ |\n| <b>BankClientC</b>                 | BankClientC                      | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\sqrt{\\chi}$         |\n| BankClientD                        | BankClientD                      | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\checkmark$ $\\times$ |\n| BankClientE                        | BankClientE                      | \"Empty\"          | <b>Undefined</b>   | Accepted                   | V X                   |\n| BankClientP                        | BankClientP                      | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\checkmark$ $\\times$ |\n\n## <span id=\"page-11-1\"></span>**Figure 12 Searching relationships**\n\nThe list of relationships is then filtered based on what was typed as shown in [Figure](#page-11-2)  13 [Filtered relationships.](#page-11-2)\n\n| RFS<br><b>SEP</b><br>Orders    |                                  |                  |                      |                            | 土土                    |\n|--------------------------------|----------------------------------|------------------|----------------------|----------------------------|-----------------------|\n| <b>Taker Relationships (1)</b> | <b>Auto Accept Configuration</b> |                  |                      |                            |                       |\n|                                |                                  |                  | $\\mathbb{Q}$ ClientP | $\\rightarrow$              |                       |\n|                                |                                  |                  |                      |                            |                       |\n|                                |                                  |                  |                      |                            |                       |\n| Counterpart                    | <b>Legal Entity</b>              | <b>Long Name</b> | My Status            | <b>Counterparty Status</b> |                       |\n| BankClientP                    | BankClientP                      | \"Empty\"          | <b>O</b> Undefined   | Accepted                   | $\\checkmark$ $\\times$ |\n|                                |                                  |                  |                      |                            |                       |\n|                                |                                  |                  |                      |                            |                       |\n\n## <span id=\"page-11-2\"></span>**Figure 13 Filtered relationships**\n\nIt is possible to go back to the original list display by clearing the search field and clicking on the arrow on the right.\n\n## <span id=\"page-11-0\"></span>**4.2 Sort**\n\nThe Taker Relationships table still offers sorting capabilities (ascending or descending ) by clicking on the column headers as shown in [Figure 14](#page-11-3) Sorting [relationships.](#page-11-3)\n\n| Counterpart        | $\\vee$ Legal Entity | <b>Long Name</b> | <b>My Status</b>   | <b>Counterparty Status</b> |                       |\n|--------------------|---------------------|------------------|--------------------|----------------------------|-----------------------|\n| BankClientP        | BankClientP         | \"Empty\"          | <b>O</b> Undefined | Accepted                   | $\\vee$ X              |\n| <b>BankClientE</b> | <b>BankClientE</b>  | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\vee$ X              |\n| BankClientD        | BankClientD         | \"Empty\"          | <b>O</b> Undefined | Accepted                   | $\\vee$ X              |\n| <b>BankClientC</b> | <b>BankClientC</b>  | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\checkmark$ $\\times$ |\n| BankClientB        | <b>BankClientB</b>  | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\vee$ X              |\n| <b>BankClientA</b> | <b>BankClientA</b>  | \"Empty\"          | <b>Undefined</b>   | Accepted                   | $\\checkmark$ X        |\n\n## <span id=\"page-11-3\"></span>**Figure 14 Sorting relationships**\n\n![](_page_12_Picture_0.jpeg)\n\n# <span id=\"page-12-0\"></span>**5 Download and upload counterparts**\n\n## <span id=\"page-12-1\"></span>**5.1 Download counterparts**\n\nIt is possible to download a csv file containing information about all the relationships defined (or undefined) for the market maker for all configured products by clicking on the symbol indicated in Figure 15 [Download counterparts.](#page-12-3)\n\n<span id=\"page-12-3\"></span>**Figure 15 Download counterparts**\n\nAfter saving the csv file, it can be opened, edited and used as desired. A sample file is shown in Figure 16 [Relationships csv file.](#page-12-4)\n\n|                                                        | File<br>Home              | Page Layout<br>Insert                                                        | Formulas                                                                     | Data<br>Review                                                        | View                                                                  | Help               |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    | <b>B</b> Share              | <b>□</b> Comments       |\n|--------------------------------------------------------|---------------------------|------------------------------------------------------------------------------|------------------------------------------------------------------------------|-----------------------------------------------------------------------|-----------------------------------------------------------------------|--------------------|------------------|--------------------|-----------------|--------------------------------|---------------------------------------------------|----------------------|-------------------------|---|----------------------------------------------------|-------------------------------------------------------|--------------------------------|------------------------------------|-----------------------------|-------------------------|\n|                                                        | X Cut<br>Ĥ<br>$\\Box$ Copy | Calibri<br>$B$ $I$<br>Format Painter                                         | $~\\vee$ 11<br>$\\underline{\\mathsf{U}}$ $\\vee$ $\\overline{\\mathsf{H}}$ $\\vee$ | $\\vee$ A <sup><math>\\circ</math></sup><br>$A^{\\prime}$<br>$A \\cdot A$ | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\\frac{1}{2}$<br>퇴목 공 표 표 | 광 Wrap Text        | Merge & Center v | General<br>图 > % 9 |                 | $\\frac{10}{68}$ $\\frac{0}{10}$ | ш<br>Conditional<br>Formatting v Table v Styles v | 34<br>Format as Cell | $\\mathbb{Z}$            | 钾 | 囯<br>₩<br>Insert Delete Format<br>$\\sim$<br>$\\sim$ | $\\sqrt{2}$ Fill $\\sim$<br>$\\diamondsuit$ Clear $\\sim$ | $\\sum$ AutoSum<br>žy           | Sort & Find &<br>Filter Y Select Y | <b>Q</b><br>Analyze<br>Data | S<br>Sensitivity        |\n|                                                        | Clipboard                 | $\\overline{N}$                                                               | Font                                                                         | $\\overline{5}$                                                        |                                                                       | Alignment          |                  | $\\overline{u}$     | Number          | $\\overline{2}$                 |                                                   | Styles               |                         |   | Cells                                              |                                                       | Editing                        |                                    | Analysis                    | Sensitivity             |\n| 119                                                    |                           | $\\mathbf{v}$ i $\\times$<br>$f_x$<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        | $\\mathsf{A}$              | B                                                                            |                                                                              | D                                                                     |                                                                       |                    |                  |                    | $\\vert G \\vert$ | H                              |                                                   |                      |                         |   | K                                                  |                                                       |                                | м                                  |                             | N                       |\n|                                                        | <b>MY ENTIT</b>           | <b>MY ENTITY LONG NAME</b>                                                   | <b>REQUESTER</b>                                                             | <b>REQUESTER LONG NAM</b>                                             |                                                                       | <b>EGAL ENTI</b>   | I FGAI-FNT       | Y LONG NAME        | LEI             | <b>RFS MY STATUS</b>           |                                                   | <b>RFS CP STATUS</b> | <b>Orders MY STATUS</b> |   |                                                    |                                                       | Orders CP STATUS SEP MY STATUS | <b>SEP CP STATUS</b>               |                             | <b>MmFund MY STATUS</b> |\n|                                                        | <b>BankP</b>              | *Empty*                                                                      | BankA                                                                        | *Empty*                                                               |                                                                       | <b>BankClientA</b> | *Empty*          |                    |                 | Accepted                       | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        | <b>BankP</b>              | *Empty*                                                                      | <b>BankB</b>                                                                 | *Empty*                                                               |                                                                       | <b>BankClientB</b> | *Empty*          |                    |                 | <b>Undefined</b>               | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        | <b>BankP</b>              | *Empty*                                                                      | BankClientC *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | <b>Undefined</b>               | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        | <b>BankP</b>              | *Empty*                                                                      | BankClientD *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | Undefined                      | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        | 6 BankP                   | *Empty*                                                                      | BankClientE *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | <b>Undefined</b>               | Accepted                                          |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n| $\\overline{7}$                                         | <b>BankP</b>              | *Empty*                                                                      | BankClientP *Empty*                                                          |                                                                       |                                                                       |                    |                  |                    |                 | <b>Undefined</b>               | Accepted                                          |                      | Accepted                |   | Accepted                                           |                                                       |                                |                                    |                             |                         |\n|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n| $\\begin{array}{c}\\n\\cdot \\\\ 8 \\\\ 9 \\\\ 10\\n\\end{array}$ |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n|                                                        |                           |                                                                              |                                                                              |                                                                       |                                                                       |                    |                  |                    |                 |                                |                                                   |                      |                         |   |                                                    |                                                       |                                |                                    |                             |                         |\n\n<span id=\"page-12-4\"></span>**Figure 16 Relationships csv file**\n\n## <span id=\"page-12-2\"></span>**5.2 Upload counterparts**\n\nTaker relationships may also be updated (accepted or rejected) by uploading a csv file as shown in Figure 17 [Upload counterparts.](#page-12-5)\n\n| RFS <sup>1</sup><br>Orders SEP |                                  |  |\n|--------------------------------|----------------------------------|--|\n| <b>Taker Relationships (6)</b> | <b>Auto Accept Configuration</b> |  |\n\n## <span id=\"page-12-5\"></span>**Figure 17 Upload counterparts**\n\nThe template for csv upload must be the file downloaded from the \"Download counterparts\" functionality described in [5.1.](#page-12-1) The structure of the template must not be changed, otherwise the file fails to upload. This means that column headers must not be included or excluded. The changes must be limited to the content of the cells in columns labelled as \"% MY STATUS\". Ex: RFS MY STATUS, Orders MY STATUS, etc. They refer to the market maker status. The \"% CP STATUS\" columns (RFS CP STATUS, Orders CP STATUS, etc. refer to the market taker status. Changes in the content of these columns are not applied.\n\nA sample edited file is shown in Figure 16 [Relationships csv file.](#page-12-4) The cell indicated in blue font was changed from Undefined to Accepted.\n\nOnly \"Accepted\" and \"Rejected\" statuses edited in correspondent \"% MY STATUS\" columns in csv file are reflected in the platform. Statuses modified to \"Undefined\" are not applied.\n\n![](_page_13_Picture_0.jpeg)\n\n| <b>RFS</b><br><b>SEP</b><br>Orders |                                  |           |                    |                            | 土土                    |\n|------------------------------------|----------------------------------|-----------|--------------------|----------------------------|-----------------------|\n| <b>Taker Relationships (6)</b>     | <b>Auto Accept Configuration</b> |           |                    |                            |                       |\n|                                    |                                  |           |                    |                            |                       |\n|                                    |                                  | Q         |                    | $\\rightarrow$              |                       |\n|                                    |                                  |           |                    |                            |                       |\n| Counterpart                        | $\\wedge$ Legal Entity            | Long Name | My Status          | <b>Counterparty Status</b> |                       |\n|                                    |                                  |           |                    |                            |                       |\n| BankClientA                        | BankClientA                      | \"Empty\"   | Accepted           | Accepted                   | $\\mathcal{N} \\times$  |\n| <b>BankClientB</b>                 | <b>BankClientB</b>               | \"Empty\"   | <b>Undefined</b>   | Accepted                   | $\\checkmark$ $\\times$ |\n|                                    |                                  |           |                    |                            |                       |\n| BankClientC                        | BankClientC                      | \"Empty\"   | <b>O</b> Undefined | Accepted                   | $\\sqrt{\\chi}$         |\n| BankClientD                        | <b>BankClientD</b>               | \"Empty\"   | <b>O</b> Undefined | Accepted                   | $\\checkmark$ $\\times$ |\n| BankClientE                        | BankClientE                      | \"Empty\"   | <b>O</b> Undefined | Accepted                   | $\\vee$ X              |\n| <b>BankClientP</b>                 | BankClientP                      | \"Empty\"   | <b>Undefined</b>   | Accepted                   | $\\sqrt{\\chi}$         |\n\n<span id=\"page-13-1\"></span>**Figure 18 Updated relationships**\n\n# <span id=\"page-13-0\"></span>**6 Audit log**\n\nThe Audit Log tool is available in the Bridge Administration homepage to the administrators who have the corresponding rights. <NAME_EMAIL> or your customer relationship manager for setting up the audit log rights if needed.\n\n![](_page_13_Picture_6.jpeg)\n\n**Figure 19 Audit Log**\n\n<span id=\"page-13-2\"></span>Through the audit log functionality, it is possible to track all changes related to the CRM tool described in this user guide.\n\nFirstly, corresponding category and company must be selected as shown in following Figures.\n\n![](_page_14_Picture_0.jpeg)\n\n|                                        |                                       | <b>BRIDGE ADMINISTRATION</b><br><b>TRADER WORKSHEET</b>  | $+$          |                       |                      | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\begin{array}{c ccc} & \\blacktriangleright^1 & \\circledast & \\mathsf{A}\\mathsf{A} & \\multimap & \\varnothing & \\times \\end{array}$ |                                                           |\n|----------------------------------------|---------------------------------------|----------------------------------------------------------|--------------|-----------------------|----------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|\n|                                        | 合<br>$\\mathcal{G}$                    |                                                          |              |                       | Audit Log            |                                                                                                                                                                                         |                                                           |\n|                                        | $\\ensuremath{\\boxdot}$                | Select Category<br>$\\checkmark$<br>Select Company        | $\\checkmark$ |                       |                      |                                                                                                                                                                                         | $\\mathord{\\hookrightarrow}$                               |\n|                                        | $\\circledbullet$<br>$\\eta_{\\rm 1D}^2$ | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>$\\checkmark$ |              | Type to Filter Events | ○ ○ Change Request と |                                                                                                                                                                                         |                                                           |\n|                                        | $\\frac{1}{600}$                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n|                                        |                                       |                                                          |              |                       | No Category Selected |                                                                                                                                                                                         |                                                           |\n|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n|                                        |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n| $\\frac{1}{2\\sqrt{3}}$                  |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n| $\\mathbb C$<br>$\\overline{\\circ}$<br>Đ |                                       |                                                          |              |                       |                      |                                                                                                                                                                                         |                                                           |\n|                                        |                                       | 1 BankP.TraderA, BankP // DEMO                           |              |                       | $\\blacksquare$       |                                                                                                                                                                                         | Fri, 23. Apr 2021, 16:23:05 GMT // Connected [FFM] · DEMO |\n\n<span id=\"page-14-0\"></span>**Figure 20 Audit Log criteria selection**\n\n| Select Category    |              |\n|--------------------|--------------|\n|                    |              |\n| Institutions       |              |\n| RegulatoryData     |              |\n| <b>BankBaskets</b> |              |\n| Counterparts       |              |\n|                    |              |\n|                    |              |\n|                    |              |\n|                    |              |\n|                    |              |\n|                    |              |\n| <b>Select All</b>  | <b>Apply</b> |\n\n<span id=\"page-14-1\"></span>**Figure 21 Audit Log Category selection**\n\n![](_page_15_Picture_0.jpeg)\n\n| Select Company |\n|----------------|\n|                |\n| <b>BankP</b>   |\n|                |\n|                |\n|                |\n|                |\n|                |\n|                |\n| Apply          |\n\n## <span id=\"page-15-0\"></span>**Figure 22 Audit Log Company selection**\n\nAll related changes are then displayed as shown in Figure 23 [Audit Log CRM results.](#page-15-1)\n\n|                          |                                                                      | <b>TRADER WORKSHEET</b>                                   | $\\left  + \\right $<br><b>BRIDGE ADMINISTRATION</b>      |                     |                       |                                                   |               | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\sum_{i=1}^{n}$ (i) AA - $\\oplus \\times$ |                      |  |\n|--------------------------|----------------------------------------------------------------------|-----------------------------------------------------------|---------------------------------------------------------|---------------------|-----------------------|---------------------------------------------------|---------------|------------------------------------------------------------------------------------------------|----------------------|--|\n|                          | 合                                                                    |                                                           | $\\boxed{\\smile}$ $\\boxed{\\smile}$ Counterparts $\\times$ |                     |                       | <b>Audit Log</b>                                  |               |                                                                                                |                      |  |\n|                          | $\\mathcal{L}_{\\mathcal{T}}$<br>$\\qquad \\qquad \\boxdot \\qquad \\qquad$ | Select Category<br>O 0 Show all companies (Select Company |                                                         |                     | $\\ominus$             |                                                   |               |                                                                                                |                      |  |\n|                          | Œ                                                                    | Event Date (All                                           | $\\frac{1}{2}$<br>$\\checkmark$                           | Search <sup>3</sup> | Type to Filter Events | ○ O Change Request と                              |               |                                                                                                |                      |  |\n|                          | $\\frac{d}{d\\theta}$                                                  | <b>DB Revision</b>                                        | Timestamp (GMT)                                         | Target              | Company               | <b>Event Name</b>                                 | Changed By    | Field                                                                                          | Value                |  |\n|                          |                                                                      | 168348                                                    | 23.04.2021 / 16:18:08                                   | BankP               | BankP                 | Counterpart Relation Maker Status _ BankP.TraderA |               | <b>Maker Status</b>                                                                            | <b>ACCEPTED</b>      |  |\n|                          | $\\frac{1}{600}$                                                      | 168328                                                    | 23.04.2021 / 15:26:43                                   | BankClientP         | BankP                 | Auto accept enabled                               | BankP.TraderA | Old Maker Status                                                                               | <b>UNDEFINED</b>     |  |\n|                          |                                                                      | 168310                                                    | 23.04.2021 / 13:44:48                                   | BankP               | BankP                 | Counterpart Relation Maker Status __   ********   |               | Taker<br>Maker                                                                                 | BankClientA<br>BankP |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               | <b>Negotiation Type</b>                                                                        | <b>RFS</b>           |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n| $\\zeta_{\\rm c}^{\\rm sc}$ |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n| O                        |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n| $\\bigcirc$               |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n| Θ                        |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      |                                                           |                                                         |                     |                       |                                                   |               |                                                                                                |                      |  |\n|                          |                                                                      | <sup>1</sup> BankP.TraderA, BankP // DEMO                 |                                                         |                     |                       | $\\blacksquare$                                    |               | Fri, 23. Apr 2021, 16:27:59 GMT // Connected [FFM] · DEMO                                      |                      |  |\n\n## <span id=\"page-15-1\"></span>**Figure 23 Audit Log CRM results**\n\nThe table of results contain the following data:\n\n- DB Revision: internal ID which identifies each entry in the database\n- Timestamp GMT: date and time in which the change was made\n- Target/Name: impacted entity\n\n![](_page_16_Picture_0.jpeg)\n\n- Event Name: description of the change\n- Changed by: name of the individual administrator who made de change. Please note that the name is not available for the changes made by a 360T administrator, but only those made by the market maker administrators.\n\nIt is also possibe to restrict the audit logs search to a specific period of time, using either the pre-defined periods or calendars available in the Event Date field as shown in Figure 24 - [Audit Log Event Date -](#page-16-0) pre defined periods and Figure 25 - [Audit Log](#page-16-1)  [Event Date -](#page-16-1) calendars.\n\n| <b>Event Date</b>  | All             | ᄉ | $-111$<br>Ш     |\n|--------------------|-----------------|---|-----------------|\n| <b>DB Revision</b> | Today<br>7 days |   | Timestamp (GMT) |\n|                    | 30 days         |   |                 |\n|                    | 90 days         |   |                 |\n|                    | 180 days        |   |                 |\n|                    | 1 year          |   |                 |\n\n<span id=\"page-16-0\"></span>**Figure 24 - Audit Log Event Date - pre defined periods**\n\n| G          | <b>Event Date</b><br>Custom |     |             |                      |                |             |          | ▽ 21.03.2021 - 28.03.2021 |                                          |                                          |                   |                   |             |       |            |\n|------------|-----------------------------|-----|-------------|----------------------|----------------|-------------|----------|---------------------------|------------------------------------------|------------------------------------------|-------------------|-------------------|-------------|-------|------------|\n| Sunday     |                             |     |             | <b>March 2021</b>    |                |             |          |                           |                                          |                                          | <b>April 2021</b> |                   |             |       | Sunday     |\n| <b>MAR</b> | s                           | M   | $\\top$      | w                    | $T$ F          |             | $\\sim$ S | S                         | M                                        | $\\top$                                   | w                 | - 1 -             | $-F$        | - S   | <b>MAR</b> |\n|            |                             |     | $1 \\t2 \\t3$ |                      | $\\overline{4}$ | 5 6         |          |                           |                                          |                                          |                   |                   | $1 \\t2 \\t3$ |       |            |\n|            |                             | - 8 | 9           |                      |                | 10 11 12 13 |          | 4                         | 5 6                                      |                                          | $7^{\\circ}$       | - 8               | 9           | 10    |            |\n|            | 14                          |     |             | 15 16 17 18          |                | 19 20       |          |                           |                                          | 11 12 13 14                              |                   | $-15$             | - 16        | $-17$ |            |\n|            |                             |     |             | 21 22 23 24 25 26 27 |                |             |          | 18                        | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                   | 21 22 23 24       |             |       |            |\n|            | 28                          |     | 29 30 31    |                      |                |             |          |                           |                                          |                                          |                   | 25 26 27 28 29 30 |             |       |            |\n| 2021       |                             |     |             |                      |                |             |          |                           |                                          |                                          |                   |                   |             |       | 2021       |\n\n<span id=\"page-16-1\"></span>**Figure 25 - Audit Log Event Date - calendars**\n\nWhen the administrator clicks on any of the listed entries, the details of the change are shown in another table located at the right side of the screen as shown in [Figure](#page-17-1)  27 [Audit Log change details.](#page-17-1)\n\nIn the following example, the Maker Status was changed from UNDEFINED to ACCEPTED in the relationship with Taker BankClientA for Negotiation Type RFS.\n\nPlease note that the changes made via Upload counterparts functionality are displayed in the same way as the changes made directly in the Maker Relationships screen. The Audit Log does not differentiate them as the output is the same.\n\n![](_page_17_Picture_0.jpeg)\n\n|                                                  |                                                  | <b>TRADER WORKSHEET</b>                                                   | $\\Box$<br><b>BRIDGE ADMINISTRATION</b>     |               |                       |                                               |               | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\mathbb{D}^1$ (0 AA - $\\varpi \\times$ |                                                                     |\n|--------------------------------------------------|--------------------------------------------------|---------------------------------------------------------------------------|--------------------------------------------|---------------|-----------------------|-----------------------------------------------|---------------|----------------------------------------------------------------------------------------------------|---------------------------------------------------------------------|\n|                                                  | 合<br>$\\mathcal{L}_{\\mathcal{T}}$                 | Select Category                                                           | $\\vee$   Counterparts $\\times$             |               |                       | <b>Audit Log</b>                              |               |                                                                                                    | $\\ominus$                                                           |\n|                                                  | $\\qquad \\qquad \\boxdot \\qquad$<br>$\\circledcirc$ | O 0 Show all companies (Select Company<br>Event Date (All<br>$\\checkmark$ | $\\vee$   (BankP $\\times$ )<br>$\\Box$ (iii) | Search        | Type to Filter Events | ○ 0 Change Request と                          |               |                                                                                                    |                                                                     |\n|                                                  | $\\overline{\\text{tr}}$                           | <b>DB Revision</b>                                                        | Timestamp (GMT)                            | <b>Target</b> | Company               | <b>Event Name</b>                             | Changed By    | Field                                                                                              | Value                                                               |\n|                                                  |                                                  | 168348                                                                    | 23.04.2021 / 16:18:08                      | BankP         | BankP                 | Counterpart Relation Maker Status             | BankP.TraderA | <b>Maker Status</b>                                                                                | <b>ACCEPTED</b>                                                     |\n|                                                  | $\\frac{1}{600}$                                  | 168328                                                                    | 23.04.2021 / 15:26:43                      | BankClientP   | BankP                 | Auto accept enabled                           | BankP.TraderA | <b>Old Maker Status</b>                                                                            | <b>UNDEFINED</b>                                                    |\n|                                                  |                                                  | 168310                                                                    | 23.04.2021 / 13:44:48                      | BankP         | BankP                 | Counterpart Relation Maker Status    ******** |               | Taker                                                                                              | BankClientA                                                         |\n|                                                  |                                                  |                                                                           |                                            |               |                       |                                               |               | Maker<br><b>Negotiation Type</b>                                                                   | BankP<br><b>RFS</b>                                                 |\n| $\\zeta_{\\rm c}^{\\rm sc}$<br>O<br>$\\bigcirc$<br>œ |                                                  |                                                                           |                                            |               |                       |                                               |               |                                                                                                    |                                                                     |\n|                                                  |                                                  | <b>1</b> BankP.TraderA, BankP // DEMO                                     |                                            |               |                       | $\\blacksquare$ $\\blacksquare$ $\\blacksquare$  |               |                                                                                                    | Fri, 23. Apr 2021, 16:27:59 GMT // Connected [FFM] ·<br><b>DEMO</b> |\n\n#### <span id=\"page-17-0\"></span>**Figure 26 Audit Log CRM results and details**\n\n| Field                   | Value              |  |  |  |  |\n|-------------------------|--------------------|--|--|--|--|\n| <b>Maker Status</b>     | <b>ACCEPTED</b>    |  |  |  |  |\n| <b>Old Maker Status</b> | <b>UNDEFINED</b>   |  |  |  |  |\n| <b>Taker</b>            | <b>BankClientA</b> |  |  |  |  |\n| Maker                   | <b>BankP</b>       |  |  |  |  |\n| <b>Negotiation Type</b> | <b>RFS</b>         |  |  |  |  |\n|                         |                    |  |  |  |  |\n|                         |                    |  |  |  |  |\n\n#### <span id=\"page-17-1\"></span>**Figure 27 Audit Log change details**\n\n![](_page_18_Picture_0.jpeg)\n\n# <span id=\"page-18-0\"></span>**7 Contacting 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\nGermany 360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n### **Asia Pacific South Asia**\n\nSingapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756\n\n### **Middle East**\n\nUnited Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134\n\n### **EMEA Americas**\n\n## USA\n\n360 Trading Networks, Inc 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902\n\nIndia ThreeSixty Trading Networks (India) Pvt Ltd Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437", "metadata": {"lang": "en"}}]