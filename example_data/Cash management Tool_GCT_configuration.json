[{"id": "1", "text": "## **CASH MANAGEMENT TOOL – GCT MONEY MARKET CONFIGURATION**\n\n![](_page_0_Picture_1.jpeg)\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\n# **INTERNAL DOCUMENT ONLY**\n\n© 360 TREASURY SYSTEMS AG, 2013 THIS FILE CONTAINS PROPRIETARY AND CONFID<PERSON><PERSON>AL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG\n\n### **Table of Contents**\n\n| 1 |                | INTRODUCTION3                                                                                  |         |\n|---|----------------|------------------------------------------------------------------------------------------------|---------|\n| 2 |                | CONFIGURATION OF MONEY MARKET PRODUCTS3                                                        |         |\n|   | 2.1            | MONEY MARKET FUNDS<br>                                                                         | 3       |\n|   | 2.1.1<br>2.1.2 | Provider Settings<br>Requester Settings<br>                                                    | 4<br>6  |\n|   | 2.2            | COMMERCIAL PAPERS<br>                                                                          | 8       |\n|   | 2.2.1<br>2.2.2 | Commercial Paper setup and configuration<br><br>Configuration of commercial paper provider<br> | 8<br>13 |\n|   | 2.2.3          | CSV file format for onboarding and updating commercial papers                                  | 14      |\n| 3 |                | CONFIGURATION OF COUNTERPARTIES<br>15                                                          |         |\n|   | 3.1            | COMPANY SETTINGS –<br>COUNTERPART RELATIONSHIPS                                                | 15      |\n|   | 3.2            | USER RIGHTS<br>                                                                                | 17      |\n| 4 |                | CONTACT 360T<br>18                                                                             |         |\n\n| Figure 1: MM Fund configuration3                                                   |  |\n|------------------------------------------------------------------------------------|--|\n| Figure 2: Enabled provider set up for MM Funds4                                    |  |\n| Figure 3: Assigning Agent Swift Code, Regions and Share Classes to an MM Fund<br>5 |  |\n| Figure 4: Select autodealer instance type5                                         |  |\n| Figure 5: MM Fund configuration for requester side<br>6                            |  |\n| Figure 6: Enabled requester set up for MM Funds7                                   |  |\n| Figure 7: Applying account numbers for MM Funds<br>7                               |  |\n| Figure 8: General commercial paper configuration<br>9                              |  |\n| Figure 9: On-board new papers on the platform<br>9                                 |  |\n| Figure 10: Business errors noticed during the paper import10                       |  |\n| Figure 11: Import preview in on-boarding process11                                 |  |\n| Figure 12: Import preview in update process11                                      |  |\n| Figure 13: Manually create a primary paper<br>12                                   |  |\n| Figure 14: Manually create a secondary paper13                                     |  |\n| Figure 15: Configuration of commercial paper provider<br>14                        |  |\n| Figure 16: Set up of counterpart relationships<br>16                               |  |\n| Figure 17: Enabling MM Requester or Provider module<br>16                          |  |\n| Figure 18: Configuring counterpart relationships per product<br>17                 |  |\n| Figure 19: Set Administration rights17                                             |  |\n\n## <span id=\"page-2-0\"></span>**1 INTRODUCTION**\n\nThis manual describes the necessary company and product configurations in the General Configuration Tool (GCT) for the Cash Management Tool – both for the requester and provider side. **This manual is for 360T internal use only.** It describes the configuration of Money Market (MM) products, the setting of counterpart relationships and user rights. For more information about the Cash Management Tool please read the respective manuals for the requester and the provider side which can be found in the same folder as this manual.\n\n### <span id=\"page-2-1\"></span>**2 CONFIGURATION OF MONEY MARKET PRODUCTS**\n\nDifferent Money Market products require specific configurations in the GCT before they can be used by the requester and provider side. Therefore the following sections describe the settings for the different MM products.\n\n### <span id=\"page-2-2\"></span>**2.1 Money Market Funds**\n\nA money market fund (also known as money market mutual fund) is an open-ended mutual fund that invests in short-term debt securities. Money market funds are widely (though not necessarily accurately) regarded as being as safe as bank deposits yet providing a higher yield.\n\nTo configure the product in the GCT, the funds have to be imported first for the respective provider company. This is done via a csv file that includes static information like fund name, paper identification (e.g. ISIN), issuer, ratings, etc. The import is currently done in the IT (INT) department. A respective Jira issue has to be created for this task. Dynamic values for the funds are updated on a daily basis over fund provider specific interfaces, usually over SFTP.\n\nOnce the funds have been imported, the configuration can be done in the *MM Fund* tab of the GCT. [Figure 1](#page-2-3) shows the MM Funds tab on first set up. Depending on the role of the company either the *Provider* or *Requester* setting has to be configured.\n\n| Fort General Configuration Tool - BETA - 360T.Prahl                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | an Ori<br>$\\mathbf{x}$                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |  |  |  |  |  |  |  |\n|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|\n| Run Audit Diff Window                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |  |  |  |  |\n| $Q$ $\\overline{P}$<br>Search                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | 함 무 図<br>ACT.TEST                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |  |  |  |  |  |  |\n| Q-<br>AAREALCP.TEST<br>₽<br>Aargauische Kantonalbank.TEST<br>AB.TEST<br>÷.<br><b>ABB.TEST</b><br>₿⊹<br>Abbott.TEST<br>÷.<br><b>ARRSUR1.TEST</b><br>ABBSUB2.TEST<br><b>E ABBSUB3.TEST</b><br>由 - LE ABC.BANK<br><b>ABIS</b><br>ABN AMRO.DEMO<br>且<br><b>ABN.TEST</b><br>à £<br><b>E-E ABNCOMP.TEST</b><br>в<br><b>ACAM</b><br><b>D</b> Acergy.TEST<br>Acergy.TEST.TAS.A1<br>Acergy.TEST.TAS.A2<br>AcergySUB.TEST<br>≑ €<br>且<br><b>ACT.TEST</b><br>÷.<br><b>ADCR.TEST</b><br>÷.<br>由一直<br>Adecco.TEST<br>AdeccoSub1.TAS1.TEST<br>AdeccoSub1.TEST<br>₽<br>AdeccoSub2.TEST<br><b>Adeo Services.TEST</b><br>ADIA.TEST<br>à £<br>ADIC.TEST | ∣▲<br>rpart Relationships   Basic Baskets   Trading Limits   General   Custom Fields   Trader Spreads   AutoDealer Instance   MM Fund<br>$\\boxplus$<br>$\\blacktriangle$<br>Provider Requester<br>Save Reload Discard Changes Audit Log<br>It seems that this functionality is used for the first time.<br>Please press create to enable this feature with the default configuration.<br><b>ATTENTION</b><br>This is a normal process and has to be done only the first time.<br>Create |  |  |  |  |  |  |  |\n| 由 具 ADICCUST1.TEST<br>Completed                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | $\\overline{\\phantom{0}}$<br>$\\overline{\\phantom{a}}$<br><b>B</b> 360TGROUP   <b>B</b> BLB.TEST   <b>B</b> ICD.TEST   <b>B</b> ICDCOMP.TEST   <b>B</b> ACT.TEST                                                                                                                                                                                                                                                                                                                         |  |  |  |  |  |  |  |\n\n<span id=\"page-2-3\"></span>Figure 1: MM Fund configuration\n\n#### <span id=\"page-3-0\"></span>**2.1.1 Provider Settings**\n\nThis section describes the set up for a provider company. As can be seen in [Figure 1](#page-2-3) the *Provider* tab has to be selected. On initial configuration the functionality has to be activated. This is done by clicking the \"Create\" button which will creates a default configuration and enables the feature. [Figure 2](#page-3-1) shows the enabled MM Fund set up.\n\n| ኔ ⊕ | <b>BLACKROCK.TEST</b>                                                                                                                                                                                |  |  |  |  |  |  |  |  |  |  |\n|-----|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|\n|     | utoDealer   Mapping   User Roles   Counterpart Relationships   Bank Baskets   Collateral Baskets   Trading Limits   General   Custom Fields   Trader Spreads   AutoDealer Instance<br><b>MM Fund</b> |  |  |  |  |  |  |  |  |  |  |\n|     | Save Reload Discard Changes Audit Log                                                                                                                                                                |  |  |  |  |  |  |  |  |  |  |\n|     | Provider Requester                                                                                                                                                                                   |  |  |  |  |  |  |  |  |  |  |\n|     | Save Reload Discard Changes Audit Log                                                                                                                                                                |  |  |  |  |  |  |  |  |  |  |\n|     | <b>E</b> -MM Fund Provider<br><b>E</b> -Regions<br><b>E-Share Classes</b><br><b>A</b> -- I Data<br><b>D</b> E Agent Swift Code<br><b>E</b> MM Funds                                                  |  |  |  |  |  |  |  |  |  |  |\n\n<span id=\"page-3-1\"></span>Figure 2: Enabled provider set up for MM Funds\n\nThis is the same view a MM Fund provider with MM Fund Administration rights will see. In this screen Regions, Share Classes, general data like account settings and email notification, agent swift code and the imported Funds can be viewed and configured.\n\n*Region:* Contains a list of countries which can be moved into different groups. This configuration allows providing MM Funds only for certain groups or countries. Groups can be added, renamed or removed by the respective buttons on the right side. Every buy-side customer is assigned a country in the central 360T institution administration.\n\n*Share Classes:* Every MM fund has to be assigned to a Share Class. They are used to be associated to every buy-side customer wishing to trade MM Funds, in order to display only the funds that should be offered to each customer. Share classes can be added, renamed or removed by the respective buttons on the right side.\n\n*Data:* This section is used to configure additional information. For instance, if the buyside customer should always provide an account number when creating an execution request, the \"Account number required\" field must be checked. The same behaviour is valid if the buy-side customer should provide a SWIFT Code prior to start trading. This information is obligatory in case the automated ordering and trade execution should be done over the Swift network. In case these fields are checked, the buyside customer will not be able to place orders as long as he has not provided account number and Swift code. Whether he has done this or not is managed server-side by 360T. Finally, it is possible to define whether an e-mail notification should be send when a new execution request has been posted. The respective email address is to be defined in the \"Execution Request Notification Address\" field.\n\n*Agent Swift Code:* When the mm fund provider is trading via the Swift network, an agent swift code must be configured for this provider entity. The agent swift code it applied to the funds of the provider. It is possible to configure more than one swift code. Normally the swift adapter from integration team has this information already configured for existing providers. The GCT configuration will overwrite the adapter settings.\n\n*MM Funds:* This section allows assigning agent swift codes, regions and share classes to each MM Fund. Only funds assigned to a region and a share class are visible and executable for the buy side customer. This menu entry also shows the ISIN of the respective MM Fund.\n\nBy choosing the respective MM Fund and doing a right mouse click, a menu is shown which allows applying a pre-defined agent swift code, region and share class. Per fund, only one agent swift code, region and share class can be assigned.\n\nAgent swift code, region and share class can be associated in bulk to a group of funds. Select the respective funds with Ctrl + left mouse click or Ctrl + A (for all funds) so that the funds are marked in green. On right mouse click select apply for the relevant parameter and then select the respective agent swift code, region or share class to apply in bulk to the selection of funds.\n\n[Figure 3](#page-4-0) shows the MM Funds with assigned Regions and Share Classes.\n\n| <b>BLACKROCK.TEST</b>                            |                                                                                                          |                           |                       |                         |                              |                       |                       |                                       |  |  |  |\n|--------------------------------------------------|----------------------------------------------------------------------------------------------------------|---------------------------|-----------------------|-------------------------|------------------------------|-----------------------|-----------------------|---------------------------------------|--|--|--|\n| <b>User Roles</b><br><b>utoDealer</b><br>Mapping | <b>Bank Baskets</b><br><b>Counterpart Relationships</b>                                                  | <b>Collateral Baskets</b> | <b>Trading Limits</b> | General                 | <b>Custom Fields</b>         | <b>Trader Spreads</b> |                       | AutoDealer Instance<br><b>MM Fund</b> |  |  |  |\n| Save Reload Discard Changes Audit Log            |                                                                                                          |                           |                       |                         |                              |                       |                       |                                       |  |  |  |\n| Requester<br><b>Provider</b>                     |                                                                                                          |                           |                       |                         |                              |                       |                       |                                       |  |  |  |\n| Save Reload Discard Changes Audit Log            |                                                                                                          |                           |                       |                         |                              |                       |                       |                                       |  |  |  |\n| <b>⊟</b> <sup>™</sup> MM Fund Provider           | <b>Fund Name</b>                                                                                         | <b>ISIN</b>               |                       | <b>Agent Swift Code</b> |                              | <b>Regions</b>        |                       |                                       |  |  |  |\n| <b>E</b> -Regions                                | <b>Institutional Canadian Dollar Liquidity</b>                                                           | IE00B3ROVL14              |                       |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n| <b>E-Share Classes</b>                           | Institutional Canadian Dollar Liquidity                                                                  | IE00B3T17586              |                       |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n| <b>Data</b>                                      | Institutional Canadian Dollar Liquidity                                                                  | IE1234567890              |                       | ٠                       |                              | World                 |                       | <b>Standard Group</b>                 |  |  |  |\n| <b>D</b> E Agent Swift Code                      | Institutional Euro Government Liquidity                                                                  | IE00B39VC974              | ÷                     |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n| <b>E</b> E MM Funds                              | Institutional Euro Government Liquidity                                                                  | IE00B39VCB93              |                       |                         |                              | World                 |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | <b>Institutional Euro Liquidity</b>                                                                      | IE0005023910              |                       |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | <b>Institutional Euro Liquidity</b>                                                                      | IE0005023803              |                       |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | <b>Institutional Euro Ultra Short Bond</b>                                                               | IE00B3PXT077              |                       | World                   |                              |                       | <b>Standard Group</b> |                                       |  |  |  |\n|                                                  | Institutional Euro Ultra Short Bond                                                                      | IE00B1B82251              |                       |                         | World<br>v l                 |                       | ▼                     | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional Sterling Governr Apply Region                                                              | Ctrl-A                    |                       | World                   |                              |                       | <b>Standard Group</b> |                                       |  |  |  |\n|                                                  | <b>Institutional Sterling Governr</b>                                                                    |                           |                       |                         | World<br>۰                   |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional Sterling Liquidity 2 Apply Agent Swift Code Ctrl-A                                         |                           |                       |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional Sterling Liquidity Apply Share Class                                                       | Ctrl-A                    |                       |                         | World<br>۰                   |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional Sterling Ultra Shore pond<br>novel Applies the same agent swift code to all MM Funds. Irld |                           |                       |                         |                              |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional Sterling Ultra Short Bond                                                                  | IE00B05LZG85              |                       |                         | <b>TWorld</b>                |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional US Dollar Liquidity                                                                        | IE0004810143              |                       |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional US Dollar Liquidity                                                                        | IE0004809582              |                       |                         |                              | World                 |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | Institutional US Dollar Ultra Short Bond                                                                 | IE00B3WVN351              |                       |                         |                              | World                 |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | <b>Institutional US Treasury</b>                                                                         | IE00B3KDBK68              |                       |                         | World                        |                       |                       | <b>Standard Group</b>                 |  |  |  |\n|                                                  | <b>Institutional US Treasury</b>                                                                         | IE00B39VC867              |                       |                         | <b>World</b><br>$\\checkmark$ |                       | ▼                     | <b>Standard Group</b>                 |  |  |  |\n\n<span id=\"page-4-0\"></span>Figure 3: Assigning Agent Swift Code, Regions and Share Classes to an MM Fund\n\n### **2.1.1.1 Autodealer Settings**\n\nMM funds are normally processed via the SWIFT network. This means that MM Fund requests are send to a swift adapter that translates the request into a SWIFT message. In a next step the adapter is pushing the SWIFT message onto the SWIFT network where the message is validated and sent to the Transfer agent of the provider. Execution or rejection of the request is also done via SWIFT processing. The provider side would only login to the Cash Module as a fallback option in case technical issues prevent the Swift processing. In order to configure the SWIFT processing the *AutoDealerInstance* tab must be activated and configured for the provider. The Autodealer type must be MM\\_FUND and can be selected from the list. [Figure 4](#page-4-1) shows the required autodealer instance type.\n\n| Mapping   User Roles  <br>AutoDealer  |  | Counterpart Relationships   Bank Baskets   Collateral Baskets   Trading Limits |  |  |  | General | <b>Custom Fields</b> | Trader Spreads    | <b>AutoDealer Instance</b> |  |\n|---------------------------------------|--|--------------------------------------------------------------------------------|--|--|--|---------|----------------------|-------------------|----------------------------|--|\n| Save Reload Discard Changes Audit Log |  |                                                                                |  |  |  |         |                      |                   |                            |  |\n| AutoDealer Instance Config            |  | selected members                                                               |  |  |  |         |                      | available members |                            |  |\n| <b>E</b> AutoDealer Instances         |  | MM FUND                                                                        |  |  |  |         | >                    | <b>RFS</b>        |                            |  |\n| <del>்</del> ஃ MM_FUND                |  |                                                                                |  |  |  |         | $\\prec$              | <b>ORDER</b>      |                            |  |\n| <b>E</b> General                      |  |                                                                                |  |  |  |         |                      | <b>SEP</b>        |                            |  |\n| <b>E- Pricing Server Rou</b>          |  |                                                                                |  |  |  |         |                      | ECP               |                            |  |\n|                                       |  |                                                                                |  |  |  |         |                      |                   |                            |  |\n\n<span id=\"page-4-1\"></span>Figure 4: Select autodealer instance type\n\nIn *General* the Autodealer must be enabled and the Pricing Server Routing defines the name of the service. If SWIFT processing is required the routing needs to be configured as:\n\nDEFAULT and SwiftFundService\n\nICD as MMfund provider is also having an automated processing. However they are not using the SWIFT network. As they have their own service running the configuration needs to be configured to:\n\nDEFAULT and ICDFundService\n\nOtherwise if not automated processing is used then the AutoDealer Instance tab is not configured.\n\n#### <span id=\"page-5-0\"></span>**2.1.2 Requester Settings**\n\nThis section describes the set up for a requester company. As can be seen in [Figure](#page-5-1)  [5](#page-5-1) the *Requester* tab has to be selected. On initial configuration the functionality has to be activated. This is done by clicking the \"Create\" button which will enable the feature with the default configuration.\n\nMake sure that the requester entity has the Company Segment filled in Insti Admin. Otherwise the GCT will show you a respective message. If the company segment has not been set correctly, the requester individual won't see any MM Fund in the Cash module.\n\n![](_page_5_Picture_7.jpeg)\n\nFigure 5: MM Fund configuration for requester side\n\n<span id=\"page-5-1\"></span>[Figure 6](#page-6-0) shows the enabled MM Fund set up. This is the same view an MM Fund requester with MM Fund Administration rights will see. In this screen account numbers and SWIFT CODE can be configured. Additionally the configured account numbers can be applied to each fund enabled in the fund providers offering to the requestor.\n\n![](_page_6_Picture_1.jpeg)\n\nFigure 6: Enabled requester set up for MM Funds\n\n<span id=\"page-6-0\"></span>Depending on the configured counterpart relationships (which are described in section [3\\)](#page-14-0) one or more MM Fund provider are listed under the entry MM Funds which can be seen in [Figure 7.](#page-6-1) For each MM Fund provider the available funds are listed. Per default no account number is selected.\n\nBy choosing the respective MM Fund and doing a right mouse click, a menu is shown which allows applying a pre-defined account number. The account number can also be set for more than one or all listed MM Funds of a provider. To accomplish that select the respective funds with Ctrl + left mouse or Ctrl + A (for all funds) so that the funds are marked in green. On right mouse click the account number can be applied for all selected funds. This step has to be done for every configured MM Fund provider.\n\nIt is possible to select more than one account number per fund. The product definition window in Cash module will provide a drop down list to select the appropriate account number in this case. Per default the first account number is shown in the Cash Module.\n\n| <b>BLACKROCK.COMP.TEST</b>                       |                                                         |                                          |                                                          |                     |                      |                          |                     |                |            |  |  |\n|--------------------------------------------------|---------------------------------------------------------|------------------------------------------|----------------------------------------------------------|---------------------|----------------------|--------------------------|---------------------|----------------|------------|--|--|\n| <b>utoDealer</b><br><b>User Roles</b><br>Mapping | <b>Counterpart Relationships</b><br><b>Bank Baskets</b> | <b>Collateral Baskets</b>                | <b>Trading Limits</b>                                    | General             | <b>Custom Fields</b> | <b>Trader Spreads</b>    | AutoDealer Instance | <b>MM Fund</b> | Comm       |  |  |\n| Save Reload Discard Changes Audit Log            |                                                         |                                          |                                                          |                     |                      |                          |                     |                |            |  |  |\n| Provider Requester                               |                                                         |                                          |                                                          |                     |                      |                          |                     |                |            |  |  |\n| Save Reload Discard Changes Audit Log            |                                                         |                                          |                                                          |                     |                      |                          |                     |                |            |  |  |\n| □ MM Fund Requester                              | <b>Fund Name</b>                                        |                                          |                                                          | <b>ISIN</b>         |                      |                          | Provider            |                |            |  |  |\n| E Account Numbers                                | Institutional Canadian Dollar Liquidity                 | IE00B3ROVL14                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n| 由一三 Data                                         | Institutional Canadian Dollar Liquidity                 | IE00B3T17586                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n| <b>白…IE MM Funds</b>                             | Institutional Canadian Dollar Liquidity                 | IE1234567890                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                |            |  |  |\n| <b>DELACKROCK.TEST</b>                           | <b>Institutional Euro Government Liquidity</b>          | IE00B39VCB93                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | Institutional Euro Government Liquidity                 | IE00B39VC974                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional Euro Liquidity</b>                     | IE0005023803                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional Euro Liquidity</b>                     | IE0005023910                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | Institutional Euro Ultra Short Bond                     | IE00B1B82251                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional Euro Ultra Short Bond</b>              | IE00B3PXT077                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | Institutional Sterling Government Liquidity             | IE00B40G6S53                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | Institutional Sterling Government Liquidity             | IE00B3X84Y31                             |                                                          |                     |                      | <b>BLACKROCK.TEST</b>    |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional Sterling Liquidity</b>                 | IE0004807107                             |                                                          |                     |                      | $\\overline{\\mathbf{x}}$  |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional Sterling Liquidity</b>                 | IE0004806687                             | CCT                                                      |                     |                      |                          |                     |                | 360A100001 |  |  |\n|                                                  | Institutional Sterling Ultra Short Bond                 | IE00B05LZG85                             |                                                          |                     |                      |                          |                     |                | 360A100001 |  |  |\n|                                                  | Institutional Sterling Ultra Short Bond                 | IE00B518R393                             |                                                          |                     |                      |                          |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional US Dollar Liquidity</b>                | IE0004809582                             |                                                          |                     |                      | <b>TRADING NETWORKS</b>  |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional US Dollar Liquidity</b>                |                                          | IE0004810143<br>Which account numbers should be applied? |                     |                      |                          |                     |                | 360A100001 |  |  |\n|                                                  | Institutional US Dollar Ultra Short Bond                | IE00B3WVN351                             |                                                          |                     |                      |                          |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional US Treasury</b>                        | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                                                          | 12GB33708           |                      | $\\overline{\\phantom{0}}$ |                     |                | 360A100001 |  |  |\n|                                                  | <b>Institutional US Treasury</b>                        | <b>IF00B39VC867</b>                      |                                                          | 653454B06           |                      |                          |                     |                | 360A100001 |  |  |\n|                                                  |                                                         |                                          |                                                          | $\\sqrt{12}$ GB33708 |                      |                          |                     |                |            |  |  |\n|                                                  |                                                         |                                          |                                                          | 32456433            |                      |                          |                     |                |            |  |  |\n|                                                  |                                                         |                                          |                                                          | 360A100001          |                      |                          |                     |                |            |  |  |\n|                                                  |                                                         |                                          |                                                          |                     |                      |                          |                     |                |            |  |  |\n|                                                  |                                                         |                                          |                                                          |                     |                      |                          |                     |                |            |  |  |\n|                                                  |                                                         |                                          |                                                          |                     | OK                   | Cancel                   |                     |                |            |  |  |\n|                                                  |                                                         |                                          |                                                          |                     |                      |                          |                     |                |            |  |  |\n\n<span id=\"page-6-1\"></span>Figure 7: Applying account numbers for MM Funds\n\nIf no account number is configured, this information can also be provided in the execution request by the requester side. Please note that some MM Fund providers require the customer to provide an account number. If no account is pre-configured, the account field in the execution request product definition will be displayed in yellow as mandatory field, and no execution request can be sent until an account number was entered.\n\n### <span id=\"page-7-0\"></span>**2.2 Commercial Papers**\n\nCommercial Papers are unsecured obligations issued by a corporation or bank to finance its short-term credit needs, such as accounts receivable and inventory. Maturities typically range from 2 to 270 days. Commercial papers are available in a wide range of denominations, can be either discounted or interest-bearing. Commercial papers are usually issued by companies with high credit ratings, meaning that the investment is almost always relatively low risk.\n\nTo configure the product in the GCT, the ECP / CP static data has to be imported or created first. This is done in GCT through different ways. The static data include information like paper name, paper identification (e.g. ISIN), issuer, ratings, etc. The import is done via the GCT menu \\* 360T PLATFORM \\*. Dynamic values for the papers are updated on a daily or more frequent basis. This is done with a pricing adapter for example by sending csv files to our SFTP server. The way the current price information is send to 360T depends on the setup of the provider entity.\n\n#### <span id=\"page-7-1\"></span>**2.2.1 Commercial Paper setup and configuration**\n\nThe following sections describe how commercial papers are imported, created, removed or exported in GCT. The commercial paper configuration can be found in the entity list in GCT. Select the entry \\* 360T PLATFORM \\* and go to the tab called Commercial Papers to open the general paper configuration. [Figure 8](#page-8-0) shows how the configuration menu can be found.\n\nOn opening the menu you'll see two entries – one called Repository and the other called Import. The repository gives you an overview of all existing papers on the platform and provides further options to maintain the papers. The import entry is used for uploading new papers or updating existing papers. The following sections will explain the differences.\n\n**In general the commercial paper setup and configuration might take a heavy load on the config service in GCT depending on the amount of papers to be validated and uploaded. Therefore the configuration should be done before or after trading hours if possible. This will also help in preventing that papers are removed or changed that are currently used in a negotiation between buy-side customer and provider. We will add steps to prevent those occurrences and also optimize the process in general but for now this reminder stands as it is.** \n\n| oct General Configuration Tool - INT - 360T.Prahl     |                        |                                             |                                                                                              |\n|-------------------------------------------------------|------------------------|---------------------------------------------|----------------------------------------------------------------------------------------------|\n| Audit Diff Window<br>Run                              |                        |                                             |                                                                                              |\n| む 中<br>Search                                         |                        | *360T PLATFORM *                            |                                                                                              |\n| $Q -$                                                 |                        | SEP EQC Commercial Papers<br><b>General</b> |                                                                                              |\n| ₿<br>* 360T PLATFORM *<br><b>U</b> 0029 DUP CHINA LTD | $\\frac{1}{\\mathbb{R}}$ | Save Reload Discard Changes Audit Log       |                                                                                              |\n| <b>■ 0030 bps</b>                                     |                        | <b>E</b> -Commercial Paper Management       | ONBOARDING -                                                                                 |\n| $0040$ bps<br><b>■ 0050 bps</b>                       |                        | Repository<br>Import                        | $\\triangledown$ GS.TEST<br>BayernLB.TEST<br>RABOBANK.TEST<br><b>UBS.TEST</b><br>Invesco.TEST |\n| <b>B</b> 0055 PT Du Pont Agro                         |                        |                                             |                                                                                              |\n| $0060$ bps<br><b>■ 0063 DuP Canada Grainex</b>        |                        |                                             |                                                                                              |\n| 0063 TMH LOGISTICS INC.                               |                        |                                             |                                                                                              |\n| <b>■ 0070 bps</b>                                     |                        |                                             | Select All<br>Deselect All                                                                   |\n| <b>■ 0075 bps</b><br><b>■ 0080 bps</b>                |                        |                                             | <b>Commercial Papers Table Filter Field</b>                                                  |\n| <b>■ 0090 bps</b>                                     |                        |                                             | Q-                                                                                           |\n| $\\sqrt{2}$ 0100 bps                                   |                        |                                             | <b>Filtered Commercial Papers Table</b>                                                      |\n| $\\sqrt{2}$ 0110 bps<br><b>■ 0120 bps</b>              |                        |                                             | 0 out of 0 Commen                                                                            |\n| $\\sqrt{2}$ 0125 bps                                   |                        |                                             | Selected Paper Id Bloomberg Id ISIN Issuer Type Start Date End Date Amount C<br>Paper        |\n| $\\sqrt{2}$ 0130 bps                                   |                        |                                             |                                                                                              |\n| <b>U 0133 DUP PERF ELAST PTE L</b>                    |                        |                                             |                                                                                              |\n\n<span id=\"page-8-0\"></span>Figure 8: General commercial paper configuration\n\n#### <span id=\"page-8-2\"></span>********* Import of new papers for one or more provider (ONBOARDING)**\n\nThe import (ONBOARDING) is used to upload new papers for either one or more provider. This operation is mostly used when a new CP/ECP provider is on-boarded on 360T. To upload commercial papers you need to create a csv file in correct format. Section [2.2.3](#page-13-0) will describe the csv format in more detail. Once you have selected the appropriate file, you need to select between ONBOARDING and UPDATING. Select ONBOARDING for this section. Additionally you can select one or more provider for which the papers should be uploaded. GCT will already give you a list of providers that have a valid ECP / CP configuration. If your respective provider is not among them you need to configure the provider first. The configuration steps for a provider are explained in section [2.2.2.](#page-12-0) If a provider is selected in the onboarding process the papers will automatically be assigned to that provider. In that way you safe the process of manually assigning papers to a provider later on. After that, click on the upload button to import the new papers to the platform. [Figure 9](#page-8-1) shows that part of the process.\n\n| *360T PLATFORM *                                       |                                                                                                              |                |              |                         |  | <b>卤 早 図</b>               |  |\n|--------------------------------------------------------|--------------------------------------------------------------------------------------------------------------|----------------|--------------|-------------------------|--|----------------------------|--|\n| General SEP EQC Commercial Papers                      |                                                                                                              |                |              |                         |  |                            |  |\n| Save Reload Discard Changes Audit Log                  |                                                                                                              |                |              |                         |  |                            |  |\n| E Commercial Paper Management<br>-Repository<br>Import | ONBOARDING V   L:\\Projects\\Cash Module\\ECP\\Commercial Paper Lists on 360T\\ECP List\\int\\int_ecp.csv<br>$\\sim$ |                |              |                         |  |                            |  |\n|                                                        | BayernLB.TEST                                                                                                | $\\vee$ GS.TEST | Invesco.TEST | RABOBANK.TEST VIBS.TEST |  | Upload papers and validate |  |\n|                                                        | Select All<br><b>Commercial Papers Table Filter Field</b>                                                    | Deselect All   |              |                         |  |                            |  |\n\n<span id=\"page-8-1\"></span>Figure 9: On-board new papers on the platform\n\nDepending on the syntax and content of your csv file as well as existing papers on the platform, the upload process may take a while and forces you to correct entries in your file. First the syntax of your file is checked and if it's not in correct form, it will be rejected with an appropriate error message. You need to correct the file and repeat the step to upload.\n\nIn the next iteration(s) the content of your file is checked. If business errors like wrong ratings, outdated values etc. are noticed you'll receive an error message as well. [Figure 10](#page-9-0) should an example where the maturity dates of several secondary papers are outdated. We will improve the error message in future versions of the application. As for the syntax errors, nothing is uploaded. The file needs to be corrected before a new upload can be initiated.\n\n| <b>GCT</b> Error reading import file                                                                                                                                                                                                             | ΣΖ                    |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------|\n| <b>TRADING NETWORKS</b>                                                                                                                                                                                                                          |                       |\n| Line Number: 9<br>Line Content: ECP, SECONDARY, NORDEA BANK AB, NORBAB, XS0703527456, 360T56U9NAHO2, 2,5, NORDEA BANK AB, A-1+, P-1,,,,,,GBP, 03.11.2011,28                                                                                      |                       |\n| Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]                                                                                                                                            |                       |\n| Line Number: 18<br>Line Content: ECP,SECONDARY,LLOYDS TSB BANK PLC,LLOYDS,XS0730661583,,360T53A9YCBX3,27,8,LLOYDS TSB BANK PLC,,A-1,P-1,,,,,,EUR,09.01.2                                                                                         |                       |\n| Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]                                                                                                                                            |                       |\n| Line Number: 29<br>Line Content: ECP,SECONDARY,RABOBANK NEDERLAND AUST,RABOAU,XS0708692768,,360T75D24P9A2,117,5,RABOBANK NEDERLAND AUST,,A-1+                                                                                                    |                       |\n| Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]                                                                                                                                            |                       |\n| Line Number: 34<br>Line Content: ECP,SECONDARY,BANK OF CHINR LONDON,BKCHIN,XS0740600050,,360T47VO4O2C9,59,5,BANK OF CHINR LONDON,,A-1,P-1,,,,,USD                                                                                                |                       |\n| Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]                                                                                                                                            |                       |\n| Line Number: 35<br>Line Content: ECP,SECONDARY,LLOYDS TSB BANK PLC,LLOYDS,XS0733573900,,360T54X3BHCG7,2,5,LLOYDS TSB BANK PLC,,A-1,P-1,,,,,,EUR,16.01.20                                                                                         |                       |\n| Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]                                                                                                                                            |                       |\n| Line Number: 41<br>Line Content: ECP,SECONDARY,NORDEA BANK AB,NORBAB,XS0707713904,,360T57A5TZWM4,18,8,NORDEA BANK AB,,A-1+,P-1,,,,,EUR,16.11.2011,2                                                                                              |                       |\n| Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]                                                                                                                                            |                       |\n| Line Number: 51<br>Line Content: ECP,SECONDARY,SKANDINRV ENSKILDA BANK,SEB,XS0731066212,,360T787RYOPA8,38,5,SKANDINRV ENSKILDA BANK,,A-1,P-1,,,,,EUR                                                                                             |                       |\n| Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]                                                                                                                                            |                       |\n| Line Number: 54                                                                                                                                                                                                                                  |                       |\n| Line Content: ECP,SECONDARY,ING BANK NV,INTNC,XS0738836526,,360T5268JND32,19,3,ING BANK NV,,A-1,P-1,,,,,EUR,25.01.2012,24.04.2014,ACT/3<br>Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range] |                       |\n| Line Number: 65                                                                                                                                                                                                                                  |                       |\n| Line Content: ECP,SECONDARY,RABOBANK NEDERLAND,RABOFR,FR0120311933,,360T65Y1SGPH6,1,5,RABOBANK NEDERLAND,,A-1+,P-1,,,,,,EUR,23.<br>Line Error(s): [Unclassified error: java.lang.IllegalArgumentException: Column index 22 out of range]         |                       |\n|                                                                                                                                                                                                                                                  | $\\blacktriangleright$ |\n|                                                                                                                                                                                                                                                  | οк                    |\n|                                                                                                                                                                                                                                                  |                       |\n\n<span id=\"page-9-0\"></span>Figure 10: Business errors noticed during the paper import\n\nOnce all errors in the file are removed, the upload process runs through the validation of papers. In this steps the paper IDs (Bloomberg ID, ISIN and 360T ID) are checked against existing once to prevent the import of double entries. When this process is finished you will receive a kind of preview that shows you, which papers will be imported and which won't. [Figure 11](#page-10-0) shows the preview screen for the papers.\n\n*Add:* List all papers that will be uploaded to the platform after saving the changes.\n\n*Update:* Lists all papers the will be updated because previous entries have been found. E.g. same paper exists but ratings are different and will be overwritten.\n\n*Conflict*: This entry lists all papers were a conflict in IDs is noticed. For the example in [Figure 11](#page-10-0) four papers are tried to import that all have the same ISIN. In the current version we don't have the option to directly decide what is to be done with the papers or correct the conflicts. So for now those four papers would not be imported in this step. The corrected version can either be imported again or the respective paper can be added manually later on.\n\n*Not found:* Not relevant for on-boarding process\n\n| - -<br>ONBOARDING -<br>U:\\SVN Repository\\documentation\\Support\\ProductDocumentation\\Cash Module\\list of all ECPs on BETA.csv<br>$\\sim$ |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n|----------------------------------------------------------------------------------------------------------------------------------------|-------------------------|-----------------|---------------------|------------------------------|-------------------------|--------------------------------------|---------------------------------------------|------------------|--------------------|--------------------------|---------------------------|--|\n| $\\nabla$ RBS.TEST<br>$\\nabla$ RABO.TEST                                                                                                |                         | $\\vee$ UBS.TEST |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n|                                                                                                                                        |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n|                                                                                                                                        |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n|                                                                                                                                        |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n| <b>Commercial Papers Table Filter Field</b>                                                                                            |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n|                                                                                                                                        |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n| Q-                                                                                                                                     |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n| <b>Filtered Commercial Papers Table</b>                                                                                                |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n|                                                                                                                                        |                         |                 |                     | 8 out of 8 Commercial Papers |                         |                                      |                                             |                  |                    |                          |                           |  |\n| Paper                                                                                                                                  | Selected                | Paper Id        | <b>Bloomberg Id</b> | <b>ISIN</b>                  | <b>Issuer</b>           | <b>Type</b>                          | Start Date End Date Amount Currency Day Cou |                  |                    |                          |                           |  |\n| - Add                                                                                                                                  | $\\overline{\\mathbf{v}}$ |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n| <b>Update</b>                                                                                                                          | □                       |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n| <b>Conflict</b>                                                                                                                        | П                       | 360T83P7ROD     |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n| new secondary commercial paper                                                                                                         | П                       | 360T82IELM8J9   |                     | DE0001111110                 | new secon               | Secondary CP                         | 6/30/13                                     | 7/3/13<br>8/9/13 | 1,000,0            | <b>EUR</b><br><b>EUR</b> | ACT/360<br><b>ACT/360</b> |  |\n| new secondary commercial paper                                                                                                         | с<br>П                  | 360T81YEFU3     |                     | DE0001111110<br>DE0001111110 | new secon               | <b>Secondary ECP</b>                 | 7/7/13                                      | 7/3/13           | 1,000,0            | <b>GBP</b>               |                           |  |\n| new secondary commercial paper<br>outdated paper                                                                                       | □                       | 360T84PMVU      |                     | DE0001111110                 | new secon<br>outdated p | <b>Secondary ECP</b><br>Secondary CP | 7/3/13<br>5/2/13                            | 7/2/14           | 2,888,8<br>1,000,0 | <b>EUR</b>               | ACT/360<br>ACT/360        |  |\n| <b>Not found</b>                                                                                                                       |                         |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n|                                                                                                                                        | г                       |                 |                     |                              |                         |                                      |                                             |                  |                    |                          |                           |  |\n\n<span id=\"page-10-0\"></span>Figure 11: Import preview in on-boarding process\n\nIf you are satisfied with the preview, click \"save\" to actually import the papers to the platform. Please note that saving process might take a while depending on the amount of papers you upload. After saving the preview windows is cleared and your papers can be found in the repository.\n\n#### ********* Import of papers for one or more providers (UPDATING)**\n\nThe import (UPDATING) is used to change values of existing data. This feature is quite useful when ratings of multiple papers are to be updated at once. Not all values can be changes. Essential data like ISIN or paper type should not be changed.\n\nThe steps for this import method are similar to the ones explained in section [*******.](#page-8-2) Please to this section for more information.\n\nThe update process also features a paper preview prior to the import of papers which can be seen in [Figure 12.](#page-10-1)\n\n*Add:* Not relevant for updating process\n\n*Update:* all papers that will be updated are listed. In this version we have no marker for the values that will change. This feature will be added in a future version.\n\n*Conflict:* any conflicts e.g. when IDs are doubled will be listed here\n\n*Not found:* if there are papers in the csv file where IDs cannot be matched, they are considered as not existing on the platform. Therefore an update of those papers is not possible. All papers fitting that profile will be listed in this section and are not uploaded.\n\n| 8 out of 8 Commercial Papers   |          |               |              |              |            |                      |                                             |        |                    |            |                |\n|--------------------------------|----------|---------------|--------------|--------------|------------|----------------------|---------------------------------------------|--------|--------------------|------------|----------------|\n| Paper                          | Selected | Paper Id      | Bloomberg Id | <b>ISIN</b>  | Issuer     | <b>Type</b>          | Start Date End Date Amount Currency Day Cou |        |                    |            |                |\n| $-$ Add                        |          |               |              |              |            |                      |                                             |        |                    |            |                |\n| <b>⊕</b> Update                | V        |               |              |              |            |                      |                                             |        |                    |            |                |\n| – Conflict                     |          |               |              |              |            |                      |                                             |        |                    |            |                |\n| - Not found                    |          |               |              |              |            |                      |                                             |        |                    |            |                |\n| new secondary commercial paper |          | 360T83P7ROD   |              | DE0001111110 | new secon  | Secondary CP         | 6/30/13                                     | 7/3/13 | 1,000,0            | <b>EUR</b> | ACT/360        |\n| new secondary commercial paper |          | 360T82IELM8J9 |              | DE0001111110 | new secon  | Secondary ECP        | 7/7/13                                      |        | $8/9/13$   1,000,0 | <b>EUR</b> | <b>ACT/360</b> |\n| new secondary commercial paper |          | 360T81YEFU3   |              | DE0001111110 | new secon  | <b>Secondary ECP</b> | 7/3/13                                      | 7/3/13 | 2,888,8            | <b>GBP</b> | <b>ACT/360</b> |\n| outdated paper                 |          | 360T84PMVU    |              | DE0001111110 | outdated p | Secondary CP         | 5/2/13                                      | 7/2/14 | 1.000.0            | eur        | ACT/360        |\n|                                |          |               |              |              |            |                      |                                             |        |                    |            |                |\n\n<span id=\"page-10-1\"></span>Figure 12: Import preview in update process\n\n#### <span id=\"page-10-2\"></span>**2.2.1.3 Manual adding of a primary paper**\n\nIf only one or a few primary commercial papers are to be added to the repository it is easier to manually create them instead of creating a valid csv file and go through the import and validation routine.\n\nTo manually create a primary paper, go to the repository of all papers. Check if the paper you want to add is already existing e.g. with a slightly different name. If the paper does not exist so far, click on the button *Add Primary* on the right navigation pane. [Figure 13](#page-11-0) shows the pop window that opens for creating a primary paper. Fields like the Paper ID are generated by the system and can therefore not be filled. As we don't accept all ratings which the market is using, the ratings are drop down list to pick an acceptable value. When all required information has been provided, the paper can be added to the repository. You will see, that the paper id is automatically added.\n\n|                                                         |                             | te Enc | Add Primary |\n|---------------------------------------------------------|-----------------------------|--------|-------------|\n|                                                         | <b>TRADING NETWORKS</b>     |        | Add Seconda |\n| 團針 画 哱啓                                                 |                             |        | Open        |\n| $\\blacksquare$ General                                  |                             |        | Remove      |\n| Paper                                                   | new commercial paper        |        | Export      |\n| Paper Id                                                |                             |        |             |\n| <b>Bloomberg Id</b>                                     | <b>NEWPAPERID</b>           |        | Export all  |\n| <b>ISIN</b>                                             |                             |        |             |\n| Issuer                                                  | new commercial paper issuer |        |             |\n| Type                                                    | ECP                         |        |             |\n| Currency                                                | <b>HKD</b>                  |        |             |\n| $\\blacksquare$ Term                                     |                             |        |             |\n| <b>Start Date</b>                                       | 23.07.2013                  |        |             |\n| <b>End Date</b>                                         | ▬                           |        |             |\n| Day Count                                               | ACT/360                     |        |             |\n| <b>Business Adjustment</b>                              | <b>MODFOLLOW</b>            |        |             |\n| $\\Box$ Rating                                           |                             |        |             |\n| <b>Short Term Rating Fitch</b>                          |                             |        |             |\n| Short Term Rating SnP                                   |                             |        |             |\n| <b>Short Term Rating Moodys</b>                         | $P-1$                       |        |             |\n| <b>Short Term Rating Provider</b>                       |                             |        |             |\n| Long Term Rating Fitch                                  | BBB+                        |        |             |\n| Long Term Rating SnP                                    | AAA                         |        |             |\n| Long Term Rating Moodys                                 |                             |        |             |\n| Long Term Rating Provider                               |                             |        |             |\n| <b>End Date</b><br>The end date of the Commercial Paper |                             |        |             |\n|                                                         |                             |        |             |\n\n<span id=\"page-11-0\"></span>Figure 13: Manually create a primary paper\n\n#### **2.2.1.4 Manual adding of a secondary paper**\n\nThe manual adding of secondary papers is basically the same as adding primary papers. For further details also refer to section [2.2.1.3.](#page-10-2) To add a secondary paper, click on the button *Add Secondary* on the right navigation pane of the repository section. As can be seen in [Figure 14](#page-12-1) the pop window almost looks the same as for primary papers. The difference is that an ISIN, a start and end date as well as the amount has to be provided.\n\n| $\\blacksquare$ General             |                                                       |  |  |\n|------------------------------------|-------------------------------------------------------|--|--|\n| Paper                              | new secondary commercial paper                        |  |  |\n| Paper Id                           |                                                       |  |  |\n| <b>Bloomberg Id</b><br><b>ISIN</b> |                                                       |  |  |\n|                                    | DE0001111110                                          |  |  |\n| Issuer                             | new secondary commercial paper issuer<br>Secondary CP |  |  |\n| Type<br>Amount                     | 1.000.000,00                                          |  |  |\n| Currency                           | <b>EUR</b>                                            |  |  |\n| $\\blacksquare$ Term                |                                                       |  |  |\n| <b>Start Date</b>                  | 23.07.2013                                            |  |  |\n| <b>End Date</b>                    | 23.07.2013                                            |  |  |\n| Day Count                          | ACT/360                                               |  |  |\n| <b>Business Adjustment</b>         | MODFOLLOW                                             |  |  |\n| $\\Box$ Rating                      |                                                       |  |  |\n| Short Term Rating Fitch            |                                                       |  |  |\n| Short Term Rating SnP              |                                                       |  |  |\n| <b>Short Term Rating Moodys</b>    |                                                       |  |  |\n| Short Term Rating Provider         |                                                       |  |  |\n| Long Term Rating Fitch             |                                                       |  |  |\n| Long Term Rating SnP               |                                                       |  |  |\n| Long Term Rating Moodys            |                                                       |  |  |\n| Long Term Rating Provider          |                                                       |  |  |\n| (Name)<br>(Description)            |                                                       |  |  |\n\n<span id=\"page-12-1\"></span>Figure 14: Manually create a secondary paper\n\n#### <span id=\"page-12-2\"></span>********* Export of one or multiple commercial papers in the repository**\n\nThe repository offers the feature to export one or multiple commercial papers. This option is very helpful when a new provider is to be on-boarded. In this case the complete list of papers can be exported and send to the provider. He can check this list and pick the ones which he also wants to trade. It will also force the provider to send his additional papers in our required format right away. That way we reduce the effort for the on-boarding / updating process.\n\n#### ********* Removal of one or multiple commercial papers**\n\nThe removal of papers is currently a feature that should not be used if it can be helped. In this version of the tool we don't have any checks in place if the paper is assigned to a provider or if a running negotiation with respective papers is currently taking place. This could lead to corrupted trades and entity configuration. If a paper needs to be removed, then it has to be manually checked if any provider is offering this paper. We will improve the tool to automatically check assignment and usage of the paper.\n\n#### <span id=\"page-12-0\"></span>**2.2.2 Configuration of commercial paper provider**\n\nOnce the papers have been imported and the general paper configuration has been done, the configuration for each provider can be done. Select a provider and open it's configuration in GCT. Go to the tab *Commercial Paper.* If the provider is a new commercial paper provider, you might need to create the configuration first. Afterward you see the configuration for respective provider – see [Figure 15.](#page-13-1) This is basically the same view a user with ECP administration rights has.\n\n| UBS.TEST                                |                                                                                                        |                         |                     |                           |                       |                                             |                                          |                      |         |         |    |                         |\n|-----------------------------------------|--------------------------------------------------------------------------------------------------------|-------------------------|---------------------|---------------------------|-----------------------|---------------------------------------------|------------------------------------------|----------------------|---------|---------|----|-------------------------|\n| Mapping   User Roles<br>AutoDealer      | <b>Counterpart Relationships</b>                                                                       |                         | <b>Bank Baskets</b> | <b>Collateral Baskets</b> | <b>Trading Limits</b> | General<br>Custom Fields                    | <b>Trader Spreads</b>                    | AutoDealer Instance  |         | MM Fund |    | <b>Commercial Paper</b> |\n| Save Reload Discard Changes Audit Log   |                                                                                                        |                         |                     |                           |                       |                                             |                                          |                      |         |         |    |                         |\n|                                         | <b>E</b> . Commercial Paper Configuration Commercial Papers Table Filter Field                         |                         |                     |                           |                       |                                             |                                          |                      |         |         |    |                         |\n| Assigned<br>— All                       | Q-                                                                                                     |                         |                     |                           |                       |                                             |                                          |                      |         |         |    |                         |\n| <b>Filtered Commercial Papers Table</b> |                                                                                                        |                         |                     |                           |                       |                                             |                                          |                      |         |         |    |                         |\n|                                         | 80 out of 80 Commercial Papers                                                                         |                         |                     |                           |                       |                                             |                                          |                      |         |         |    |                         |\n|                                         | $\\mathbf{A}^1$<br>Selected<br><b>Bloomberg Id</b><br><b>ISIN</b><br>Paper Id<br>Paper<br><b>Issuer</b> |                         |                     |                           |                       | Type                                        | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                      |         |         |    |                         |\n|                                         | #N/A Field Not Ap                                                                                      | $\\checkmark$            | 360T11YO858E9       | CXSAMI                    |                       | #N/A Field Not Applicable                   |                                          | <b>ECP</b>           |         |         |    | <b>EUR</b>              |\n|                                         | <b>Abbey NRtioNRI T</b>                                                                                | $\\overline{\\mathbf{v}}$ | 360T01A8YUJY4       | <b>ABBEY</b>              |                       | Abbey NRtioNRI Treasury Services PLC/London |                                          | ECP                  |         |         |    | <b>EUR</b>              |\n|                                         | <b>ABN AMRO BANK</b>                                                                                   | $\\checkmark$            | 360T46XJ8OKR8       | <b>ABNRMR</b>             | XS0735550781          | ABN AMRO BANK NV                            |                                          | <b>Secondary ECP</b> | 1/18/12 | 4/18/14 | 24 | <b>EUR</b>              |\n|                                         | Achmea Hypothee                                                                                        | $\\checkmark$            | 360T02XKTFTK8       | <b>ACHMEA</b>             |                       | Achmea Hypotheekbank NV                     |                                          | ECP                  |         |         |    | <b>EUR</b>              |\n|                                         | Akademiska Hus AB                                                                                      | $\\checkmark$            | 360T03CK4DSR0       | <b>AKHUS</b>              |                       | Akademiska Hus AB                           |                                          | ECP                  |         |         |    | <b>USD</b>              |\n|                                         | <b>Allianz SE</b>                                                                                      | $\\blacktriangledown$    | 360T043OUJ262       | <b>ALVGR</b>              |                       | <b>Allianz SE</b>                           |                                          | ECP                  |         |         |    | <b>EUR</b>              |\n\n<span id=\"page-13-1\"></span>Figure 15: Configuration of commercial paper provider\n\nThe configuration menu show all assigned papers to this provider in entry *Assigned* and all papers on the platform in entry *All*. You can assign papers to respective provider in menu \"All\". Both menu entries allow un-assigning papers for this provider. For assigning and un-assigning of papers just tick the checkboxes in column *Selected*. Likewise both menu entries allow exporting papers to a csv file. For more information on how the export-function works please refer to section [*******.](#page-12-2)\n\n#### <span id=\"page-13-0\"></span>**2.2.3 CSV file format for onboarding and updating commercial papers**\n\nThis section describes the csv format which will be accepted both for the onboarding and updating import of commercial papers. The following date has to be provided in the csv files:\n\n| Header                | Description                                                                                               | Values                |\n|-----------------------|-----------------------------------------------------------------------------------------------------------|-----------------------|\n| Paper Type            | Defines if it's a European<br>or US commercial paper                                                      | ECP, CP               |\n| Market Type           | Defines if it is a primary or<br>secondary paper                                                          | PRIMARY,<br>SECONDARY |\n| Commercial Paper Name | Name of the paper                                                                                         | STRING                |\n| Bloomberg Identifier  | Paper identifier on<br>bloomberg                                                                          | STRING                |\n| Isin                  | International identifier, only<br>required for secondary<br>papers, has fixed length<br>and id validation | STRING                |\n| Cusip                 | US identifier, only required<br>for secondary papers<br>has<br>fixed length and id<br>validation          | STRING                |\n| 360T Id               | Unique 360T ID, generated<br>automatically by the<br>system<br>has fixed length<br>and id validation      | STRING                |\n| Amount                | Amount, only required for<br>secondary papers                                                             | DECIMAL               |\n\n| Issuer                     | Name of the issuer                                                                                                                                                             | STRING               |\n|----------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------|\n| Short Term Rating FITCH    | Rating information                                                                                                                                                             |                      |\n| Short Term Rating S&P      | Rating information                                                                                                                                                             |                      |\n| Short Term Rating Moody    | Rating information                                                                                                                                                             |                      |\n| Short Term Rating Provider | Rating information                                                                                                                                                             |                      |\n| Long Term Rating FITCH     | Rating information                                                                                                                                                             |                      |\n| Long Term<br>Rating S&P    | Rating information                                                                                                                                                             |                      |\n| Long Term Rating Moody     | Rating information                                                                                                                                                             |                      |\n| Long Term Rating Provider  | Rating information                                                                                                                                                             |                      |\n| Currency                   | Currency in which the<br>paper is issues and priced,<br>we only configure one ccy<br>for each paper, the pricing<br>adapters can stream for<br>other currencies for a<br>paper | ICO Code             |\n| Start Date                 | Issue date of the paper,<br>only required for secondary<br>paper                                                                                                               | Date                 |\n| End Date                   | Maturity date of the paper,<br>only required for secondary<br>paper                                                                                                            | Date                 |\n| Day Count                  | Day count conventions<br>according to currency                                                                                                                                 | STRING               |\n| Business Adjustment        |                                                                                                                                                                                | FOLLOW,<br>MODFOLLOW |\n\nTable 1: csv file format for commercial papers\n\n### <span id=\"page-14-0\"></span>**3 CONFIGURATION OF COUNTERPARTIES**\n\nFor requester and provider companies to trade MM products, special company and user settings have to be applied in addition to the MM product configurations. For more information about MM product configuration please refer to section [2.](#page-2-1)\n\n### <span id=\"page-14-1\"></span>**3.1 Company Settings – Counterpart relationships**\n\nThis section describes the necessary configurations for MM products between requester and provider side. Only when the relationships are correctly configured can the requester search for MM products of a provider company and vice versa can the provider receive and answer MM request.\n\nTo set the counterpart relationships between provider and requester go to the tab Counterpart Relationships as marked in [Figure 16.](#page-15-0) Depending on the type of the company to be configured select Requesters or Providers in the Administration entry and chose the respective counterparties.\n\n| CCT General Configuration Tool - DEMO - 360T.Prahl                                                                  |                                                                                                                          |                      |                                                  |  |  |  |  |  |  |\n|---------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------|----------------------|--------------------------------------------------|--|--|--|--|--|--|\n| Audit Diff Window<br>Run                                                                                            |                                                                                                                          |                      |                                                  |  |  |  |  |  |  |\n| 다 다<br>360TGROUP<br>Search                                                                                          |                                                                                                                          |                      |                                                  |  |  |  |  |  |  |\n| Q-360TGROU                                                                                                          | User Roles Counterpart Relationships<br><b>Bank Baskets</b><br>Mapping<br><b>Trading Limits</b><br>General<br>AutoDealer | <b>Custom Fields</b> | <b>Trader Spreads</b><br>AutoDealer Instance   M |  |  |  |  |  |  |\n| 8- E<br>360TGROUF <sup>A</sup><br>Save Reload Discard Changes Audit Log<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                                                                                                                          |                      |                                                  |  |  |  |  |  |  |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                            | $@$ 360TGROUP<br>$\\blacktriangle$<br>selected members                                                                    |                      | available members                                |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | Administration<br>le.<br>360TBANK.TEST                                                                                   | $\\rightarrow$        | 360T Institutional.TEST                          |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 88 360TGROUP -> Requesters<br><b>Barclays BARX.DEMO</b>                                                                  | <<                   | 360T.AMERICAS                                    |  |  |  |  |  |  |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                            | 860TGROUP -> Providers<br><b>BAYLABA.TEST</b>                                                                            |                      | 360T.APAC                                        |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | <b>CONSCITEROUD FOLLOWED -&gt; Pro</b><br><b>BLACKROCK.TEST</b>                                                          |                      | 360T.GROUP TRAINING                              |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.A2 -> Pro<br><b>BLB.TEST</b>                                                                            |                      | 360TAPAC.SUBSIDIARY                              |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.A3 -> Prd<br><b>BNPP.DEMO</b>                                                                           |                      | 360TBENELUX.TEST                                 |  |  |  |  |  |  |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                            | 8360TGROUP.FOND.B1 -> Pra<br><b>BNY Mellon AM.TEST</b>                                                                   |                      | 360TCHAT.TEST                                    |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.B2 -> Pro<br><b>BOAL.DEMO</b>                                                                           |                      | 360TFFM.TEST                                     |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.B3 -> Pro<br>CITIBANK.DEMO                                                                              |                      | 360TFRANCE.TEST                                  |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.C1 -> Prd<br>COBA.DEMO                                                                                  |                      | 360TFRANCECLIENT1.TEST                           |  |  |  |  |  |  |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                                                            | 8 360TGROUP.FOND.C2 -> Prd<br><b>COBA.FRA DRESDNER.DEMO</b>                                                              |                      | 360TGROUP2                                       |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.C3 -> Prd<br><b>COBA.TEST</b>                                                                           |                      | 360TIBERIA.TEST                                  |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.D1 -> Prd<br>Credit Suisse.DEMO                                                                         |                      | 360TITALY.TEST                                   |  |  |  |  |  |  |\n| 360TGROUP                                                                                                           | 8 360TGROUP.FOND.D2 -> Prd<br><b>DB.DEMO</b>                                                                             |                      | 360TNORDIC.TEST                                  |  |  |  |  |  |  |\n\n<span id=\"page-15-0\"></span>Figure 16: Set up of counterpart relationships\n\nTo be able to set the correct counterpart relationship in the next part, it has to be defined before if the company will use the requester or provider Cash Management Module. In the *User Role* tab the respective MM Fund and / or ECP module has to be enabled. [Figure 17](#page-15-1) shows an example where the company is enabled as MM Fund requester.\n\n![](_page_15_Picture_5.jpeg)\n\nFigure 17: Enabling MM Requester or Provider module\n\n<span id=\"page-15-1\"></span>When the requester and provider companies are added and enabled as described above, the relationships can be set per MM product and requester / provider. For MM Funds and Commercial Papers separate relationships can be set, which can be seen in [Figure 18.](#page-16-1)\n\n![](_page_16_Figure_2.jpeg)\n\n<span id=\"page-16-1\"></span>Figure 18: Configuring counterpart relationships per product\n\n### <span id=\"page-16-0\"></span>**3.2 User Rights**\n\nWhen the general company settings have been done, the users of those companies have to be configured. In the *User roles* tab select the respective user. ECP and / or MM Fund Requester / Provider module has to be enabled.\n\nAdditional rights can be set in the Administration entry, which is shown in [Figure 19.](#page-16-2) If needed, the user can receive the administration rights for ECPs and MM Funds.\n\n| CCT General Configuration Tool - DEMO - 360T.Prahl                                                |                                                                                                   |                                                      |  |\n|---------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------|------------------------------------------------------|--|\n| Audit Diff Window<br><b>Run</b>                                                                   |                                                                                                   |                                                      |  |\n| Q 무<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br><b>Search</b>                                  |                                                                                                   |                                                      |  |\n| !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>AutoDealer<br>Mappino                                 | <b>Counterpart Relationships</b><br>Bank Baskets   Trading Limits<br><b>User Roles</b><br>General | <b>Custom Fields</b><br><b>Trader Spreads</b><br>Aut |  |\n| 峊<br>360TGROUP <sup>A</sup><br>o-<br>Save Reload Discard Changes Addit Log<br><b>■ 360TGROUP图</b> |                                                                                                   |                                                      |  |\n| - Sv 360T.Niyassi<br>田<br>В<br>360TGROUPIS                                                        | ▲<br>Enable Module V<br>$\\odot$ 360T.Osorio<br>宙                                                  |                                                      |  |\n| 具 360TGROUPI图<br>360T.Palova<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                          | <b>Attribute</b>                                                                                  | On/Off                                               |  |\n| 8 360T.Petroff<br>宙<br><b>夏 360TGROUFI関</b>                                                       | $\\Box$ 1. General                                                                                 |                                                      |  |\n| 360T.Pfeiffer<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                         | <b>Role Administrator</b>                                                                         |                                                      |  |\n| 360T.Prahl<br>360TGROUP                                                                           | Counterpart Relationship Administrator                                                            | ⊽                                                    |  |\n| & Standard Role<br>Ġ<br><b>LE 360TGROUPIBO</b>                                                    | <b>Counterpart Relationship Change Notification</b>                                               |                                                      |  |\n| Administration<br>7<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                   | <b>Company Bank Basket</b>                                                                        | ▽                                                    |  |\n| $-7$ ECP Requirement<br>具 360TGROUP图                                                              | <b>Trader Spreads</b>                                                                             |                                                      |  |\n| <b>MMFund Reque</b><br>360TGROUPIS                                                                | Show Update FX Reference Rates                                                                    | $\\blacktriangledown$                                 |  |\n| <sup>7</sup> Order Provider<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                           | <b>Allow Post Trade Allocations</b>                                                               | $\\blacktriangledown$                                 |  |\n| <sup>▼</sup> Order Requester<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                          | <b>Show Indicative Quote</b>                                                                      | ▽                                                    |  |\n| <b>7</b> RFQ Provider<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                 | <b>Position Aggregator Configuration</b>                                                          | ▽                                                    |  |\n| <b>7</b> RFQ Requester<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                | Show Trade Ticket Popups (LDRFQ only)                                                             | ▽                                                    |  |\n| <b>Y</b> SEP Provider<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                 | Show Trade Ticket Popups for Unacknowledged Trades (LDRFQ only)                                   | $\\blacktriangledown$                                 |  |\n| <sup>7</sup> SEP Requester<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                            | Automatically Print Tickets to Default Printer                                                    |                                                      |  |\n| <sup>7</sup> Staging Area<br>360TGROUPI腰                                                          | <b>Allow Slippage</b>                                                                             | ▽                                                    |  |\n| 由 <b>7</b> TradeBlotter<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                               | <b>Show Audit Log</b>                                                                             |                                                      |  |\n| 360T.Richardson<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                                       | <b>MM Fund Administrator</b>                                                                      | <b>M<sub>2</sub></b>                                 |  |\n| 360T.Riesling<br>ن -<br>由<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                             | <b>ECP Administrator</b>                                                                          | $\\overline{\\mathbf{v}}$                              |  |\n| 360T.Schorno<br>. .<br>360TGROUP                                                                  | $\\equiv$ 2. Limits                                                                                |                                                      |  |\n|                                                                                                   |                                                                                                   | $\\overline{\\phantom{a}}$                             |  |\n\n<span id=\"page-16-2\"></span>Figure 19: Set Administration rights\n\n## <span id=\"page-17-0\"></span>**4 CONTACT 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n#### **Germany**\n\n*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **United Arab Emirates**\n\n*360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 491 5134\n\n#### **EMEA Americas**\n\n#### **USA**\n\n*360 Trading Networks, Inc* 708 Third Avenue, 7th Floor New York, NY 10017 Phone: ****** 776 2900 Fax: ****** 776 2902\n\n#### **Middle East Asia Pacific**\n\n**Singapore** *360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "metadata": {"lang": "en"}}]