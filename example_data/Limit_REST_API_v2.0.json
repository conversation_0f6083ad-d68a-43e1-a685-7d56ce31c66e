[{"id": "1", "text": "![](_page_0_Picture_0.jpeg)\n\n# 360T Limit REST API\n\nAPI Version v2 Document Version 2.0.5\n\n360 Treasury Systems AG Grüneburgweg 16-18 / Westend Carrée D-60322 Frankfurt am Main Tel: +49 69 900289 0 Fax: +49 69 900289 29 Email: <EMAIL> Commercial Register Frankfurt, No. HRB 49874 Executive Board: <PERSON>, <PERSON> Supervisory Board: <PERSON>\n\n## **Table of Contents**\n\n| 1 |                                                             | Introduction                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |                                                                                                          |  |  |  |\n|---|-------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------|--|--|--|\n| 2 | 2.1<br>2.2<br>2.3                                           | Communication<br>Secure Connection<br>.<br>Firewall Configuration<br>.<br>Availability<br>.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 4<br>4<br>4<br>4                                                                                         |  |  |  |\n| 3 |                                                             | Security                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | 5                                                                                                        |  |  |  |\n| 4 | 4.1<br>4.2<br>4.3<br>4.4                                    | Interacting With The Api<br>Status Codes<br>Making Requests<br>.<br>CORS<br>Swagger Specification<br>.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | 6<br>6<br>6<br>6<br>6                                                                                    |  |  |  |\n| 5 |                                                             | REST Operations Overview                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | 7                                                                                                        |  |  |  |\n| 6 | 6.1<br>6.2<br>6.3<br>6.4<br>6.5<br>6.6<br>6.7<br>6.8<br>6.9 | Risk Portfolio Rule<br>Risk Portfolio Rule Operations<br>6.1.1<br>Add a new Risk Portfolio Rule<br>6.1.2<br>List all Risk Portfolio Rules<br>.<br>6.1.3<br>Get Portfolio Rule<br>.<br>6.1.4<br>Update Risk Portfolio Rule<br>6.1.4.1<br>Partial Update<br>.<br>6.1.5<br>Update Limit value of a Config Rule<br>6.1.6<br>Update Limits of a Config Rule<br>.<br>6.1.7<br>Delete a Risk Portfolio Rule<br>.<br>Risk Portfolio Rule Model<br>.<br>Limit Value Model<br>.<br>Limits Row Model<br>.<br>Single or Group Dealer Model<br>.<br>Single or Group Counterpart Model<br>.<br>Single or Group Legal Entity Model<br>.<br>Single or Group Time Period Model<br>.<br>Single or Group Execution Method Model | 12<br>12<br>12<br>13<br>15<br>17<br>18<br>19<br>19<br>20<br>20<br>21<br>22<br>22<br>22<br>22<br>23<br>23 |  |  |  |\n| 7 | 7.1                                                         | Active Rule<br>Active Rule Operations<br>7.1.1<br>List all Active Rules<br>.<br>7.1.2<br>List all Exceptional Limits of an Active Rule<br>.<br>7.1.3<br>Get Active Rule<br>7.1.4<br>Get Limit value of an Active Rule<br>.<br>7.1.5<br>Get Exceptional Limit value of an Active Rule for a given date<br>7.1.6<br>Get Utilization value of an Active Rule<br>.<br>7.1.7<br>Get Utilization value of an Active Rule for a given date<br>7.1.8<br>Update Limit value of an Active Rule<br>.                                                                                                                                                                                                                    | 24<br>24<br>25<br>26<br>26<br>27<br>27<br>28<br>28<br>28                                                 |  |  |  |\n\n|   |     | 7.1.9<br>Update Limits of an Active Rule                                   | 29 |\n|---|-----|----------------------------------------------------------------------------|----|\n|   |     | 7.1.10<br>Set Exceptional Limit value of an Active Rule                    | 29 |\n|   |     |                                                                            |    |\n|   |     | 7.1.11<br>Set Exceptional Limits of an Active Rule<br>.                    | 30 |\n|   |     | 7.1.12<br>Delete all Exceptional Limits of an Active Rule                  | 30 |\n|   |     | 7.1.13<br>Delete Exceptional Limit of an Active Rule for a given date<br>. | 31 |\n|   | 7.2 | Active Rule Model<br>.                                                     | 31 |\n|   | 7.3 | Exceptional Limit Model                                                    | 32 |\n|   |     |                                                                            |    |\n|   | 7.4 | Exceptional Limit Row Model<br>.                                           | 32 |\n|   | 7.5 | Utilizaton Value Model                                                     | 33 |\n|   |     |                                                                            |    |\n| 8 |     | Portfolio                                                                  | 34 |\n|   | 8.1 | Portfolio Operations                                                       | 34 |\n|   |     | 8.1.1<br>Add a Portfolio                                                   | 34 |\n|   |     |                                                                            |    |\n|   |     | 8.1.2<br>List All Portolios                                                | 35 |\n|   |     | 8.1.3<br>Update a Portfolio<br>.                                           | 36 |\n|   |     | 8.1.4<br>Get a Portfolio<br>.                                              | 37 |\n|   |     | 8.1.5<br>Delete a Portfolio                                                | 37 |\n|   | 8.2 | Portfolio Model                                                            | 38 |\n|   |     |                                                                            |    |\n|   | 8.3 | Portfolio Element Model<br>.                                               | 38 |\n|   | 8.4 | Single Or Group Product Model<br>.                                         | 38 |\n|   | 8.5 | Currency Couple Model<br>.                                                 | 39 |\n|   | 8.6 | Single Or Group Currency Couple Model<br>.                                 | 39 |\n|   |     |                                                                            |    |\n| 9 |     | Algorithm                                                                  | 40 |\n|   |     |                                                                            |    |\n|   | 9.1 | Algorithm Operations                                                       | 40 |\n|   |     |                                                                            |    |\n|   |     | 9.1.1<br>Get Available Algorithms<br>.                                     | 40 |\n|   | 9.2 | Algorithm Model<br>.                                                       | 40 |\n|   |     |                                                                            |    |\n|   |     | 10 Execution Method                                                        | 41 |\n|   |     |                                                                            |    |\n|   |     | 10.1 Execution Method Operation                                            | 41 |\n|   |     | 10.1.1<br>Get Available Execution Methods<br>.                             | 41 |\n|   |     | 10.2 ExecutionMethod Model<br>.                                            | 41 |\n|   |     |                                                                            |    |\n|   |     | 11 Execution Method Group                                                  | 42 |\n|   |     | 11.1 Execution Method GroupOperations<br>.                                 | 42 |\n|   |     | 11.1.1<br>Add a Execution Method Group<br>.                                | 42 |\n|   |     |                                                                            |    |\n|   |     | 11.1.2<br>List All Execution Method Group<br>.                             | 43 |\n|   |     | 11.1.3<br>Update a Execution Method Group<br>.                             | 43 |\n|   |     | 11.1.4<br>Add Execution Method to Execution Method Group<br>.              | 44 |\n|   |     | 11.1.5<br>Get a Execution Method Group<br>.                                | 44 |\n|   |     | 11.1.6<br>Delete a Execution Method Group                                  | 45 |\n|   |     | 11.2 Execution Method GroupModel<br>.                                      | 45 |\n|   |     |                                                                            |    |\n|   |     | 12 Product Group                                                           | 46 |\n|   |     | 12.1 Product Group Operations<br>.                                         | 46 |\n|   |     |                                                                            |    |\n|   |     | 12.1.1<br>Add a Product Group                                              | 46 |\n|   |     | 12.1.2<br>List All Product Groups<br>.                                     | 47 |\n|   |     | 12.1.3<br>Update a Product Group<br>.                                      | 47 |\n|   |     | 12.1.4<br>Add a Product to Product Group                                   | 48 |\n|   |     | 12.1.5<br>Get a Product Group<br>.                                         | 48 |\n\n|         | 12.2 Product Group Model                             | 49 |  |  |  |\n|---------|------------------------------------------------------|----|--|--|--|\n|         | 13 Product<br>50                                     |    |  |  |  |\n|         | 13.1 Product Operations<br>.                         | 50 |  |  |  |\n|         | 13.1.1<br>Get Available Products                     | 50 |  |  |  |\n|         | 13.2 Product Model<br>.                              | 50 |  |  |  |\n|         |                                                      |    |  |  |  |\n|         | 14 Currency Couple Group                             | 51 |  |  |  |\n|         | 14.1 Currency Couple Group Operations<br>.           | 51 |  |  |  |\n|         | 14.1.1<br>Add a Currency Couple Group                | 51 |  |  |  |\n|         | 14.1.2<br>List All Currency Couple Groups<br>.       | 52 |  |  |  |\n|         | 14.1.3<br>Update a Currency Couple Group<br>.        | 52 |  |  |  |\n|         | 14.1.4<br>Get a Currency Couple Group<br>.           | 53 |  |  |  |\n|         | 14.1.5<br>Delete a Currency Couple Group             | 54 |  |  |  |\n|         | 14.2 Currency Couple Group Model                     | 54 |  |  |  |\n|         | 14.3 Currency Couple Model<br>.                      | 54 |  |  |  |\n|         |                                                      |    |  |  |  |\n|         | 15 Iso Currency                                      | 56 |  |  |  |\n|         | 15.1 Iso Currency Operations<br>.                    | 56 |  |  |  |\n|         | 15.1.1<br>Get Available Iso Currencies               | 56 |  |  |  |\n|         | 15.2 IsoCurrency Model<br>.                          | 56 |  |  |  |\n|         |                                                      |    |  |  |  |\n|         | 16 Time Period Group                                 | 57 |  |  |  |\n|         | 16.1 Time Period Group Operations                    | 57 |  |  |  |\n|         | 16.1.1<br>Add a Time Period Group<br>.               | 57 |  |  |  |\n|         | 16.1.2<br>List All Time Period Group<br>.            | 58 |  |  |  |\n|         | 16.1.3<br>Update a TimePeriod Group<br>.             | 58 |  |  |  |\n|         | 16.1.4<br>Get a Time Period Group                    | 59 |  |  |  |\n|         | 16.1.5<br>Delete a Time Period Group<br>.            | 59 |  |  |  |\n|         | 16.2 Time Period Group Model<br>.                    | 60 |  |  |  |\n|         | 16.3 Time Period Model<br>.                          | 60 |  |  |  |\n| 17 Time |                                                      | 61 |  |  |  |\n|         | 17.1 Time Operations<br>.                            | 61 |  |  |  |\n|         | 17.1.1<br>List Available Times<br>.                  | 61 |  |  |  |\n|         | 17.2 Time Model                                      | 61 |  |  |  |\n|         |                                                      |    |  |  |  |\n|         | 18 Dealer Group                                      | 63 |  |  |  |\n|         | 18.1 Dealer Group Operations                         | 63 |  |  |  |\n|         | 18.1.1<br>List All Dealer Groups                     | 63 |  |  |  |\n|         | 18.1.2<br>Get a Dealer Group                         | 63 |  |  |  |\n|         | 18.1.3<br>Add a Dealer Group<br>.                    | 64 |  |  |  |\n|         | 18.1.4<br>Add a Dealer to Existing Dealer Group<br>. | 64 |  |  |  |\n|         | 18.1.5<br>Update a Dealer Group                      | 65 |  |  |  |\n|         | 18.1.6<br>Delete a Dealer Group<br>.                 | 65 |  |  |  |\n|         | 18.2 Dealer Group Model<br>.                         | 66 |  |  |  |\n|         |                                                      |    |  |  |  |\n|         | 19 Dealer                                            | 67 |  |  |  |\n|         | 19.1 Dealer Operations                               | 67 |  |  |  |\n|         | 19.1.1<br>List Available Dealers<br>.                | 67 |  |  |  |\n|         | 19.2 Dealer Model<br>.                               | 67 |  |  |  |\n\n| 20 Counterpart Group                                                | 68       |\n|---------------------------------------------------------------------|----------|\n| 20.1 Counterpart Group Operations<br>.                              | 68       |\n| 20.1.1<br>Add a Counterpart Group                                   | 68       |\n| 20.1.2<br>List All Counterpart Groups<br>.                          | 69       |\n| 20.1.3<br>Update a Counterpart Group                                | 69       |\n| 20.1.4<br>Get a Counterpart Group                                   | 70       |\n| 20.1.5<br>Delete a Counterpart Group<br>.                           | 70       |\n| 20.2 Counterpart Group Model                                        | 71       |\n|                                                                     |          |\n| 21 Counterpart                                                      | 72       |\n| 21.1 Counterpart Operations                                         | 72       |\n| 21.1.1<br>List Available Counterparts<br>.                          | 72       |\n| 21.2 Counterpart Model<br>.                                         | 72       |\n|                                                                     |          |\n| 22 Legal Entity Group                                               | 73       |\n| 22.1 Legal Entity Group Operations                                  | 73       |\n| 22.1.1<br>Add a Legal Entity Group<br>.                             | 73       |\n| 22.1.2<br>List All Legal Entities Groups<br>.                       | 74       |\n| 22.1.3<br>Update a Legal Entity Group                               | 74       |\n| 22.1.4<br>Add a Legal Entity to Existing Legal Entity Group<br>.    | 75       |\n| 22.1.5<br>Get a Legal Entity Group                                  | 75       |\n| 22.1.6<br>Delete a Legal Entity Group<br>.                          | 75       |\n| 22.2 Legal Entity Group Model<br>.                                  | 76       |\n| 23 Legal Entity                                                     | 77       |\n| 23.1 Legal Entity Operations                                        | 77       |\n| 23.1.1<br>List Available Legal Entities                             | 77       |\n| 23.2 Legal Entity Model<br>.                                        | 77       |\n|                                                                     |          |\n| 24 PFE Table                                                        | 78       |\n| 24.1 PFE Table Operations                                           |          |\n|                                                                     | 78       |\n| 24.1.1<br>Add a Currency Couple to PFE Table<br>.                   |          |\n| 24.1.2<br>List All PFE Entries<br>.                                 | 78<br>79 |\n| 24.1.3<br>Update PFE Entry on PFE Table                             | 81       |\n| 24.1.4<br>Update Default PFE Entry on PFE Table                     | 82       |\n| 24.1.5<br>Delete a Currency Couple from PFE Table<br>.              | 83       |\n| 24.1.6<br>Add a Tenor to PFE Table<br>.                             | 83       |\n| 24.1.7<br>Delete a Tenor from PFE Table                             | 83       |\n| 24.2 PFE Table Row Model<br>.                                       | 84       |\n| 24.3 PFE Table Column Model<br>.                                    | 84       |\n| 24.4 Tenor Model                                                    | 84       |\n|                                                                     |          |\n| 25 Error Handling                                                   | 85       |\n| 25.1 Authentication                                                 | 85       |\n| 26 Version Log                                                      | 86       |\n|                                                                     |          |\n| 27 Appendix                                                         | 87       |\n| 27.1 How To Manage Risk Portfolio From Bridge Admin<br>.            |          |\n| 27.1.1<br>Product Group<br>.                                        | 87<br>87 |\n| 27.1.2<br>Currency Couple Group<br>.<br>27.1.3<br>Time Period Group | 87<br>88 |\n\n#### Table of Contents Table of Contents\n\n| 27.1.4 | Portfolio<br>.<br>88         |\n|--------|------------------------------|\n| 27.1.5 | Legal Entity Group<br>88     |\n| 27.1.6 | Counterparts Group<br>89     |\n| 27.1.7 | Execution Method Group<br>89 |\n| 27.1.8 | Risk Portfolio Rule<br>89    |\n| 27.1.9 | Active Rule<br>90            |\n|        | 27.1.10 PFE<br>90            |\n\n## <span id=\"page-6-0\"></span>**1 Introduction**\n\nThis document presents the 360T Limit REST API which provides operations that allow clients to configure rules of the 360T Risk Portfolio. It contains an overview of the general workflow, as well as [screenshots](#page-91-1) that demonstrate how to configure the Risk Portfolio using the 360T Bridge Admin application.\n\nDetailed specifications of the utilized JSON messages are also included. The API is implemented to meet the OpenAPI 2.0 standard.\n\nThe target audience of this document is the development team integrating their risk management system into the 360T Risk Portfolio.\n\n## <span id=\"page-7-0\"></span>**2 Communication**\n\nAll calls to the REST API should be made with a HTTP call over secure TCP socket.\n\nThe current specification allows these operations: querying, updating, deleting or adding any risk portfolio configuration parameters.\n\nAll access points in this document refer to the 360T development environment. The production environment differs only with its IP address. When acceptance tests are completed, the steps described in the following sections should be repeated in production as well, so that the client is granted access to the resources there. 360T integration and production environments are completely separate systems, but work in the exact same way to allow for comprehensive development and testing.\n\n### <span id=\"page-7-1\"></span>**2.1 Secure Connection**\n\nConnections coming via Internet should be secured by establishing a HTTPS connection to the 360T data center. Certificates will be be provided by 360T integration team to the network team of the client.\n\n### <span id=\"page-7-2\"></span>**2.2 Firewall Configuration**\n\n| Environment | Connection      | URL                                | IP                 | Port | Description                                                                |\n|-------------|-----------------|------------------------------------|--------------------|------|----------------------------------------------------------------------------|\n| INT         | Internet(plain) | https://apigateway<br>int.360t.com | ************       | 7060 | This server can be<br>used<br>for<br>testing<br>and development<br>purpose |\n| PROD        | Internet(plain) | https://apigateway.360t.com        | ************* 7060 |      | This<br>server<br>is<br>used for produc<br>tion                            |\n\nFor the connections, 360T uses below IP addresses and ports.\n\n### <span id=\"page-7-3\"></span>**2.3 Availability**\n\nThe 360T Production environment is available between Sunday 6 pm America/New York and Friday 5 pm America/New York. Every day during the week the environment has a maintenance window between 5 and 6 pm America/New York during which the application may not be available. The non-availablity of the 360T platform does not mean that REST API will be unreachable from the client for the entire duration of the maintenance window. If the REST API is reachable during the maintenance window, the functionality of the API will not be available.\n\n## <span id=\"page-8-0\"></span>**3 Security**\n\nThe communication with the Limit REST API is over a secure HTTPS connection. The clients receive a private key from 360T that identifies them and grants them access to the 360T system. When starting to use the Limit REST API, the following steps need to be considered:\n\n- 1. Client's firewall should be opened for outgoing calls to the IP addresses defined in Section 2.2.\n- 2. 360T to provide a dedicated customer private key along with server certificates.\n- 3. Client to add the private key in their application's keystore and server certificate in their truststore. This will allow client to validate 360T server and be authenticated to use the Limit REST API resources.\n\n## <span id=\"page-9-0\"></span>**4 Interacting With The Api**\n\n### <span id=\"page-9-1\"></span>**4.1 Status Codes**\n\n- 200 OK Successful request\n- 201 Created New object saved\n- 400 Bad Request Returns JSON with the error message\n- 401 Unauthorized Couldn't authenticate your request\n- 403 Invalid scope User hasn't authorized necessary scope\n- 404 Not Found No such object\n- 406 Not Acceptable Not Acceptable request\n- 409 Conflict Data Already Exists\n- 429 Too Many Requests\n- 500 Internal Server Error Something went wrong in api level\n- 503 Service Unavailable The service is down for maintenance\n\n### <span id=\"page-9-2\"></span>**4.2 Making Requests**\n\nAs per RESTful design patterns, 360T Limit REST API implements following HTTP verbs:\n\n- GET Read resources\n- POST Create new resources\n- PUT Modify existing resources\n- DELETE Remove resources\n\nWhen making requests, arguments can be passed as parameters or JSON with correct Content-Type header.\n\nUsing any HTTP operation on any resource requires authentication. For any unauthenticated request would therefore fail with 401 error code. For more details on authentication, please refer to section 3.\n\n### <span id=\"page-9-3\"></span>**4.3 CORS**\n\n360T Limit REST API does not support cross-origin HTTP requests which is commonly referred as CORS.\n\n### <span id=\"page-9-4\"></span>**4.4 Swagger Specification**\n\nSwagger Specification is an API description format for REST APIs. 360T Limit REST API Swagger Specification in JSON format can be fetched via the following request.\n\n```\nGET https://apigateway-int.360t.com:7060/limitapi/v2/\n```\n\n## <span id=\"page-10-0\"></span>**5 REST Operations Overview**\n\nThe following table shows the summary of the REST operations that can be done.\n\n<span id=\"page-10-1\"></span>\n\n| Resource            | Operation                    | Description                                                                                                  | Http<br>Method/Path                       |\n|---------------------|------------------------------|--------------------------------------------------------------------------------------------------------------|-------------------------------------------|\n| Risk Portfolio Rule |                              |                                                                                                              |                                           |\n|                     | List                         | List all Risk Portfolio Rules                                                                                | GET /configRule                           |\n|                     | Get                          | Fetch a single Risk Portfolio<br>Rule by given name                                                          | GET /configRule/{name}                    |\n|                     | Add                          | Add a Risk Portfolio Rule                                                                                    | POST /configRule                          |\n|                     | Update                       | Update a Risk Portfolio Rule<br>by given name                                                                | PUT /configRule/{name}                    |\n|                     | Update Limit                 | Update Limit of a Risk Port<br>folio<br>Rule<br>by<br>given<br>Rule<br>name                                  | PUT /configRule/{name}/limit              |\n|                     | Update Limits                | Batch<br>operation<br>to<br>update<br>Limits<br>of<br>Risk<br>Portfolio<br>Rules by given Rule names         | PUT /configRule/limits                    |\n|                     | Delete                       | Delete a Risk Portfolio Rule<br>by given name                                                                | DELETE /configRule/{name}                 |\n| Active Rule         |                              |                                                                                                              |                                           |\n|                     | List                         | List all Active Rules                                                                                        | GET /activeRule                           |\n|                     | List                         | List Exceptional Limits of<br>an<br>Active<br>Rule<br>by<br>given<br>Rule name                               | GET /activeRule/{name}/limit/all          |\n|                     | Get                          | Fetch a single Active Rule<br>by given Rule name                                                             | GET /activeRule/{name}                    |\n|                     | Get Limit                    | Get Limit of an Active Rule<br>by given Rule name                                                            | GET /activeRule/{name}/limit              |\n|                     | Get<br>Excep<br>tional Limit | Get Exceptional Limit of an<br>Active Rule by given Rule<br>name and date (in a format<br>\"yyyyMMdd\")        | GET /activeRule/{name}/limit/{date}       |\n|                     | Get<br>Utiliza<br>tion       | Get Utilization of an Active<br>Rule by given Rule name                                                      | GET /activeRule/{name}/utilization        |\n|                     | Get<br>Utiliza<br>tion       | Get Utilization of an Ac<br>tive<br>Rule<br>by<br>given<br>Rule<br>name and date (in a format<br>\"yyyyMMdd\") | GET /activeRule/{name}/utilization/{date} |\n|                     | Update Limit                 | Update Limit of an Active<br>Rule by given Rule name                                                         | PUT /activeRule/{name}/limit              |\n\n|                              | Update Limits                     | Batch<br>operation<br>to<br>update<br>Limits of Active Rules by<br>given Rule names                                                       | PUT /activeRule/limits                 |\n|------------------------------|-----------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|\n|                              | Set<br>Excep<br>tional Limit      | Set Exceptional Limit of an<br>Active Rule by given Rule<br>name and date (in a format<br>\"yyyyMMdd\")                                     | PUT /activeRule/{name}/limit/{date}    |\n|                              | Set<br>Excep<br>tional Limits     | Batch operation to set Ex<br>ceptional Limits of Active<br>Rules by given Rule names<br>and<br>dates<br>(in<br>a<br>format<br>\"yyyyMMdd\") | PUT /activeRule/limit/all              |\n|                              | Delete Excep<br>tional Limits     | Delete all Exceptional Lim<br>its<br>of<br>an<br>Active<br>Rule<br>by<br>given Rule name                                                  | DELETE /activeRule/{name}/limit/all    |\n|                              | Delete Excep<br>tional Limit      | Delete Exceptional Limit of<br>an<br>Active<br>Rule<br>by<br>given<br>Rule name and date (in a for<br>mat \"yyyyMMdd\")                     | DELETE /activeRule/{name}/limit/{date} |\n| Algorithm                    |                                   |                                                                                                                                           |                                        |\n|                              | List                              | List available Algorithms                                                                                                                 | GET /algo                              |\n| Execution<br>Method<br>Group |                                   |                                                                                                                                           |                                        |\n|                              | List                              | List all Execution Method<br>Group                                                                                                        | GET /executionMethodGroup              |\n|                              | Find One                          | List<br>a<br>Execution<br>Method<br>Groupby given name                                                                                    | GET /executionMethodGroup/{name}       |\n|                              | Add                               | Add<br>a<br>Execution<br>Method<br>Group                                                                                                  | POST /executionMethodGroup             |\n|                              | Add<br>New<br>Execution<br>Method | Add a Currency to specific<br>Execution Method Group                                                                                      | POST /executionMethodGroup/{name}      |\n|                              | Update                            | Update<br>given<br>Execution<br>Method<br>Groupby<br>given<br>name                                                                        | PUT /executionMethodGroup/{name}       |\n|                              | Delete                            | Delete a Execution Method<br>Groupby given name                                                                                           | DELETE /executionMethodGroup/{name}    |\n| Execution Method             |                                   |                                                                                                                                           |                                        |\n|                              | List                              | List<br>available<br>Execution<br>Methods                                                                                                 | GET /executionMethod                   |\n| Portfolio                    |                                   |                                                                                                                                           |                                        |\n|                              | List                              | List All Portfolios                                                                                                                       | GET /portfolio                         |\n\n|                             | Find One               | Get<br>a<br>Portfolio<br>by<br>given<br>name                        | GET /portfolio/{name}              |  |\n|-----------------------------|------------------------|---------------------------------------------------------------------|------------------------------------|--|\n|                             | Add                    | Add a new Portfolios                                                | POST /portfolio                    |  |\n|                             | Update                 | Update a Portfolio                                                  | PUT /portfolio/{name}              |  |\n|                             | Delete                 | Delete a Portfolio                                                  | DELETE /portfolio/{name}           |  |\n| Product Group               |                        |                                                                     |                                    |  |\n|                             | List                   | List All Product Groups                                             | GET /productGroup                  |  |\n|                             | Find One               | Get<br>a<br>Product<br>Group<br>by<br>given name                    | GET /productGroup/{name}           |  |\n|                             | Add                    | Add a new Product Group                                             | POST /productGroup                 |  |\n|                             | Add Product            | Add a Product to specific<br>Product Group                          | POST /productGroup/{name}          |  |\n|                             | Update                 | Update a Product Group                                              | PUT /productGroup/{name}           |  |\n|                             | Delete                 | Delete a Product Group                                              | DELETE /productGroup/{name}        |  |\n| FX Product                  |                        |                                                                     |                                    |  |\n|                             | List                   | List available Products                                             | GET /product                       |  |\n| Currency<br>Couple<br>Group |                        |                                                                     |                                    |  |\n|                             | List                   | List<br>All<br>Currency<br>Couple<br>Groups                         | GET /currencyCoupleGroup           |  |\n|                             | Find One               | Get<br>a<br>Currency<br>Couple<br>Group by given name               | GET /currencyCoupleGroup/{name}    |  |\n|                             | Add                    | Add a new Currency Couple<br>Group                                  | POST /currencyCoupleGroup          |  |\n|                             | Add Currency<br>Couple | Add a Currency Couple to<br>specific<br>Currency<br>Couple<br>Group | POST /currencyCoupleGroup/{name}   |  |\n|                             | Update                 | Update a Currency Couple<br>Group                                   | PUT /currencyCoupleGroup/{name}    |  |\n|                             | Delete                 | Delete a Currency Couple<br>Group                                   | DELETE /currencyCoupleGroup/{name} |  |\n| Iso Currency                |                        |                                                                     |                                    |  |\n|                             | List                   | List available ISO Curren<br>cies                                   | GET /isoCurrency                   |  |\n| Time Period Group           |                        |                                                                     |                                    |  |\n|                             | List                   | List All Time Period Groups                                         | GET /timePeriodGroup               |  |\n|                             | Find One               | Get a Time Period Group by<br>given name                            | GET /timePeriodGroup/{name}        |  |\n|                             | Add                    | Add<br>a<br>new<br>Time<br>Period<br>Group                          | POST /timePeriodGroup              |  |\n\n| Add             |     | Add a new currency couple<br>to PFE Table           | POST /pfe                                                             |\n|-----------------|-----|-----------------------------------------------------|-----------------------------------------------------------------------|\n| Add<br>tenor    | PFE | Add a PFE tenor                                     | POST /pfe/tenor                                                       |\n| Update          |     | Update PFE Entry                                    | PUT /pfe                                                              |\n| Delete          |     | Delete<br>a<br>currency<br>couple<br>from PFE Table | DELETE<br>/pfe?baseCurrency={currency1}<br>&quoteCurrency={currency2} |\n| Delete<br>tenor | PFE | Delete a PFE tenor                                  | PUT /pfe/tenor/{tenor}                                                |\n\n## <span id=\"page-15-0\"></span>**6 Risk Portfolio Rule**\n\nRisk portfolio rule is set of parameters that can be defined by client to set their limits on. It includes counterpart, time period, currency pair, product, algorithm, limit and execution method. For an overview please see the [screenshot](#page-93-2) that demonstrates how to define Risk Portfolio Rule from Bridge Admin application.\n\n### <span id=\"page-15-1\"></span>**6.1 Risk Portfolio Rule Operations**\n\n| Operation     | Http<br>Method | Description                                                                     | Resource<br>path         |\n|---------------|----------------|---------------------------------------------------------------------------------|--------------------------|\n| List          | GET            | List all Risk Portfolio Rules                                                   | /configRule              |\n| Get           | GET            | Fetch a single Risk Portfolio Rule by given name                                | /configRule/{name}       |\n| Add           | POST           | Add a Risk Portfolio Rule                                                       | /configRule              |\n| Update        | PUT            | Update a Risk Portfolio Rule by given name                                      | /configRule/{name}       |\n| Update Limit  | PUT            | Update Limit of a Risk Portfolio Rule by given<br>Rule name                     | /configRule/{name}/limit |\n| Update Limits | PUT            | Batch operation to update Limits of Risk Portfolio<br>Rules by given Rule names | /configRule/limits       |\n| Delete        | DELETE         | Delete a Risk Portfolio Rule by given name                                      | /configRule/{name}       |\n\nThe followings are the operations which can be done on Risk Portfolio Rule.\n\nPlease note that any change (update, delete or add) done on a risk portfolio rule will be valid after day rollover which happens every day at New York 5 PM.\n\n### <span id=\"page-15-2\"></span>**6.1.1 Add a new Risk Portfolio Rule**\n\nTo add a new Risk Portfolio Rule into 360T's Limit Monitor System, client's application needs to post all the required parameters of a risk portfolio rule. To see the required parameters, please see the [Risk Portfolio Rule](#page-23-1) [Model](#page-23-1) table.\n\nRisk portfolio rule identifier (parameter 'name') is used in endpoints which is explained in details in relevant sub-sections for each operation.\n\nPlease note, parameters of a risk portfolio rule can also have null values. Sending the parameters except Algorithm with null value would mean that the value can be any of all available values (for Limit it would mean 0). Sending Algorithm with null value would mean that \"NET\\_DAILY\\_SETTLEMENT\\_LIMIT \" will be used as default.\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/configRule\n\n### Example Request\n\n16\n\n```\n{\n   \"name\": \"RiskPortfolioRule-Test\",\n   \"active\": true,\n   \"algo\": {\n      \"name\": \"Net Daily Settlement Limit\"\n   },\n   \"singleOrGroupDealer\": {\n      \"dealers\": [\n         {\n            \"name\": \"MT.Treasurer1\"\n         }\n      ],\n      \"name\": \"DealerGrp-Test\"\n   },\n   \"singleOrGroupCounterpart\": {\n      \"institutionGroup\": {\n         \"name\": \"CounterpartGrp-Test\"\n      },\n      \"isSingleInstitution\": false\n   },\n   \"singleOrGroupLegalEntity\": {\n      \"singleInstitution\": {\n         \"name\": \"360T.INT_LIMAPI.TEST\"\n      },\n      \"isSingleInstitution\": true\n   },\n   \"limit\": 50000,\n   \"portfolio\": {\n      \"name\": \"Portfolio-Test\"\n   },\n   \"singleOrGroupTimePeriod\": {\n      \"timePeriodGroup\": {\n         \"name\": \"TimePeriodGrp-Test\"\n      },\n      \"isSingleTimePeriod\": false\n   },\n   \"executionMethod\": {\n      \"name\": \"SEP\"\n   }\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-16-0\"></span>**6.1.2 List all Risk Portfolio Rules**\n\nList all [Risk Portfolio Rules.](#page-23-1)\n\nHTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/configRule\n\n#### Example Response\n\n[\n\n```\n{\n   \"active\": true,\n   \"algo\": {\n      \"name\": \"Net Daily Settlement Limit\"\n   },\n   \"singleOrGroupDealer\": {\n         \"dealers\": [\n            {\n               \"name\": \"MT.Treasurer1\"\n            }\n         ],\n         \"name\": \"DealerGrp-Test\"\n   },\n   \"singleOrGroupCounterpart\": {\n      \"institutionGroup\": {\n         \"id\": \"9037612\",\n         \"name\": \"RiskPortfolioRule-Test\",\n         \"institutions\": [\n            {\n               \"id\": \"MT.Bank3\",\n               \"name\": \"MT.Bank3\"\n            }\n         ]\n      },\n      \"isSingleInstitution\": false\n   },\n   \"name\": \"RiskPortfolioRule-Test\",\n   \"id\": \"7077188\",\n   \"singleOrGroupLegalEntity\": {\n      \"singleInstitution\": {\n         \"id\": \"360T.INT_LIMAPI.TEST\",\n         \"name\": \"360T.INT_LIMAPI.TEST\"\n      },\n      \"isSingleInstitution\": true\n   },\n   \"limit\": 1000,\n   \"portfolio\": {\n      \"id\": \"2014231\",\n      \"name\": \"Portfolio-Test\",\n      \"portfolioElement\": {\n         \"currencyCoupleGroup\": {\n            \"singleCurrencyCouple\": {\n               \"baseCurrency\": {\n                   \"isoCode\": \"***\"\n               },\n               \"quoteCurrency\": {\n                   \"isoCode\": \"***\"\n               }\n            },\n```\n\n18\n\n```\n\"isSingleCurrencyCouple\": true\n         },\n         \"productGroup\": {\n             \"productGroup\": {\n                \"id\": \"1883301\",\n                \"name\": \"ProductGrp-Test\",\n                \"products\": [\n                   {\n                      \"name\": \"Fx Forward\"\n                   },\n                   {\n                      \"name\": \"Fx Spot\"\n                   }\n                ]\n             },\n             \"isSingleProduct\": false\n         }\n      }\n   },\n   \"singleOrGroupTimePeriod\": {\n      \"timePeriodGroup\": {\n         \"id\": \"5754708\",\n         \"name\": \"TimePeriodGrp-Test\",\n         \"timePeriod\": {\n             \"from\": {\n                \"longName\": \"TODAY\",\n                \"shortName\": \"TD\"\n             },\n             \"to\": {\n                \"longName\": \"UNLIMITED\",\n                \"shortName\": \"UL\"\n             }\n         }\n      },\n      \"isSingleTimePeriod\": false\n   },\n   \"executionMethod\": {\n      \"name\": \"SEP\"\n   }\n}\n```\n\n### <span id=\"page-18-0\"></span>**6.1.3 Get Portfolio Rule**\n\nFetch a single [Risk Portfolio Rule](#page-23-1) by name.\n\n#### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test\n```\n\n#### Example Response\n\n{\n\n]\n\n```\n\"active\": true,\n\"algo\": {\n   \"name\": \"Net Daily Settlement Limit\"\n},\n\"singleOrGroupDealer\": {\n      \"dealers\": [\n         {\n            \"name\": \"MT.Treasurer1\"\n         }\n      ],\n      \"name\": \"DealerGrp-Test\"\n},\n\"singleOrGroupCounterpart\": {\n   \"institutionGroup\": {\n      \"id\": \"9037612\",\n      \"name\": \"CounterpartGrp-Test\",\n      \"institutions\": [\n         {\n            \"id\": \"MT.Bank3\",\n            \"name\": \"MT.Bank3\"\n         }\n      ]\n   },\n   \"isSingleInstitution\": false\n},\n\"name\": \"RiskPortfolioRule-Test\",\n\"id\": \"7077188\",\n\"singleOrGroupLegalEntity\": {\n   \"singleInstitution\": {\n      \"id\": \"360T.INT_LIMAPI.TEST\",\n      \"name\": \"360T.INT_LIMAPI.TEST\"\n   },\n   \"isSingleInstitution\": true\n},\n\"limit\": 1000,\n\"portfolio\": {\n   \"id\": \"2014231\",\n   \"name\": \"Portfolio-Test\",\n   \"portfolioElement\": {\n      \"currencyCoupleGroup\": {\n         \"singleCurrencyCouple\": {\n            \"baseCurrency\": {\n               \"isoCode\": \"***\"\n            },\n            \"quoteCurrency\": {\n               \"isoCode\": \"***\"\n            }\n         },\n         \"isSingleCurrencyCouple\": true\n      },\n      \"productGroup\": {\n         \"productGroup\": {\n            \"id\": \"1883301\",\n            \"name\": \"ProductGrp-Test\",\n            \"products\": [\n```\n\n```\n{\n                   \"name\": \"Fx Forward\"\n                },\n                {\n                   \"name\": \"Fx Spot\"\n                }\n             ]\n         },\n         \"isSingleProduct\": false\n      }\n   }\n},\n\"singleOrGroupTimePeriod\": {\n   \"timePeriodGroup\": {\n      \"id\": \"5754708\",\n      \"name\": \"TimePeriodGrp-Test\",\n      \"timePeriod\": {\n         \"from\": {\n             \"longName\": \"TODAY\",\n             \"shortName\": \"TD\"\n         },\n         \"to\": {\n             \"longName\": \"UNLIMITED\",\n             \"shortName\": \"UL\"\n         }\n      }\n   },\n   \"isSingleTimePeriod\": false\n},\n\"executionMethod\": {\n   \"name\": \"SEP\"\n}\n```\n\n### <span id=\"page-20-0\"></span>**6.1.4 Update Risk Portfolio Rule**\n\nUpdate [Risk Portfolio Rule](#page-23-1) by name.\n\n#### HTTP Request\n\n}\n\n{\n\n```\nPUT\n   https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test\n```\n\n#### Example Request\n\n```\n\"active\": true,\n\"algo\": {\n   \"name\": \"Net Daily Settlement Limit\"\n},\n\"singleOrGroupDealer\": {\n      \"dealers\": [\n         {\n            \"name\": \"MT.Treasurer1\"\n```\n\n```\n}\n      ],\n      \"name\": \"DealerGrp-Test\"\n},\n\"singleOrGroupLegalEntity\": {\n   \"singleInstitution\": {\n      \"name\": \"360T.INT_LIMAPI.TEST\"\n   },\n   \"isSingleInstitution\": true\n},\n\"limit\": 12000,\n\"portfolio\": {\n   \"name\": \"Portfolio-Test\"\n},\n\"singleOrGroupTimePeriod\": {\n   \"timePeriodGroup\": {\n      \"name\": \"TimePeriodGrp-Test\"\n   },\n   \"isSingleTimePeriod\": false\n},\n\"executionMethod\": {\n   \"name\": \"SEP\"\n}\n```\n\n#### Example Response\n\n}\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-21-0\"></span>**6.1.4.1 Partial Update**\n\n1. Update only [Risk Portfolio Rule'](#page-23-1)s limit by name. This allows clients to update only limit of the rule.\n\n#### HTTP Request\n\n```\nPUT\n   https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test\n```\n\nExample Request\n\n```\n{\n   \"limit\":1200000000\n}\n```\n\n#### Example Response\n\n{\n\n}\n\n```\n\"code\": 200,\n\"message\": \"Response\"\n```\n\n#### 2. Deactivate [Risk Portfolio Rule](#page-23-1) by name.\n\nThis allows client to activate or deactivate the rule.\n\n#### HTTP Request\n\n```\nPUT\n   https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test\n```\n\n#### Example Request\n\n```\n{\n   \"active\": false\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-22-0\"></span>**6.1.5 Update Limit value of a Config Rule**\n\nUpdate [Limit'](#page-25-0)s value by [Risk Portfolio Rule'](#page-23-1)s name. HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test/limit\n\n#### Example Request\n\n{\n\n}\n\n```\n\"value\": 11111\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-22-1\"></span>**6.1.6 Update Limits of a Config Rule**\n\nBatch operation to update [Limits](#page-25-1) by [Risk Portfolio Rule'](#page-23-1) names. HTTP Request\n\nPUT\n\n```\nhttps://apigateway-int.360t.com:7060/limitapi/v2/configRule/limits\n```\n\n#### Example Request\n\n```\n[\n   {\n      \"ruleName\": \"RiskPortfolioRule-Test\",\n      \"limit\": 11111\n   },\n   {\n      \"ruleName\": \"RiskPortfolioRule-Test\",\n      \"limit\": 22222\n   },\n   {\n      \"ruleName\": \"RiskPortfolioRule-Test-2\",\n      \"limit\": 33333\n   }\n]\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-23-0\"></span>**6.1.7 Delete a Risk Portfolio Rule**\n\nDelete [Risk Portfolio Rule](#page-23-1) by name.\n\n#### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/configRule/RiskPortfolioRule-Test\n```\n\n#### Example Response\n\n{\n\n}\n\n```\n\"code\": 200,\n\"message\": \"Response\"\n```\n\n# <span id=\"page-23-1\"></span>**6.2 Risk Portfolio Rule Model**\n\n| Field<br>Name | Type   | Description                            | Example |\n|---------------|--------|----------------------------------------|---------|\n| id            | String | Unique and unmodifiable Id of Risk     | 7077188 |\n|               |        | Portfolio Rule.<br>360T generates this |         |\n|               |        | Id once a new Risk Portfolio Rule is   |         |\n|               |        | added.                                 |         |\n\n| name                                | String                                      | Unique Name of Risk Portfolio Rule.<br>This field is used in Risk Portfolio<br>Rule's endpoints. This field is required<br>when adding a new Risk Portfolio Rule.<br>The value of this field must not be mod<br>ified.<br>This field will default to 'id'<br>parameter's value if no user value pro<br>vided originally. | RiskPortfolioRule<br>Test |\n|-------------------------------------|---------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|\n| algo                                | Algorithm Model                             | Any<br>Algorithm<br>which<br>is<br>fetched<br>from<br>Get<br>Available<br>Algorithms<br>can<br>be<br>used<br>to<br>define<br>Risk<br>Port<br>folio<br>Rule.<br>If<br>not<br>provided,<br>NET_DAILY_SETTLEMENT_LIMIT<br>will be used as default                                                                           |                           |\n| singleOrGroupDealer                 | Single<br>or<br>Group<br>Dealer Model       | Any Dealer Group which is fetched<br>from List All Dealer Groups can be<br>used.<br>If not provided, 'any' will be<br>used as default                                                                                                                                                                                    |                           |\n| singleOrGroupCounterpart            | Single<br>or<br>Group<br>Counterpart Model  | Any<br>Counterpart<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Counterpart<br>Groups can be used.<br>If not provided,<br>'any' will be used as default                                                                                                                                                  |                           |\n| singleOrGroupLegalEntity            | Single or Group Le<br>gal Entity Model      | Any<br>Legal<br>Entity<br>Group<br>which<br>is<br>fetched from List All Legal Entities<br>Groups can be used.<br>If not provided,<br>'any' will be used as default                                                                                                                                                       |                           |\n| portfolio                           | Portfolio Model                             | Any Portfolio which is fetched from<br>List All Portolios can be used.<br>If not<br>provided, 'any' will be used as default                                                                                                                                                                                              |                           |\n| singleOrGroupTimePeriod             | Single<br>or<br>Group<br>Time Period Model  | Any<br>Time<br>Period<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Time<br>Period<br>Group can be used.<br>If not provided,<br>'any' will be used as default                                                                                                                                             |                           |\n| singleOrGroupExecutionMethod Single | or<br>Group<br>Execution<br>Method<br>Model | Execution method refers to how the<br>trade is executed and is part of risk<br>portfolio model to allow defining lim<br>its differently based on how trade is ne<br>gotiated and executed.If not provided,<br>'any' will be used as default                                                                              |                           |\n| limit                               | Long                                        | Amount that needs to be used as limit.<br>If not provided, '0' will be used as de<br>fault                                                                                                                                                                                                                               | 1000000                   |\n\n| active | Boolean | Flag that shows if the rule is enabled.     | true |\n|--------|---------|---------------------------------------------|------|\n|        |         | If the rule is just added and active flag   |      |\n|        |         | is true, the rule will be activated with    |      |\n|        |         | day rollover and will be appeared in ac     |      |\n|        |         | tive rules. If not provided, 'true' will be |      |\n|        |         | used as default                             |      |\n\n### <span id=\"page-25-0\"></span>**6.3 Limit Value Model**\n\n| Field<br>Name | Type | Description                           | Example |\n|---------------|------|---------------------------------------|---------|\n| value         | Long | Amount that needs to be used as limit | 1000000 |\n\n### <span id=\"page-25-1\"></span>**6.4 Limits Row Model**\n\n| Field<br>Name | Type   | Description                                                                                                                                                                                   | Example         |\n|---------------|--------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|\n| ruleName      | String | Unique Name of Active Rule.<br>This<br>field is used in Active Rule's endpoints.<br>This field is required when adding a<br>new Active Rule. The value of this field<br>must not be modified. | ActiveRule-Test |\n| limit         | Long   | Amount that needs to be used as excep<br>tional limit                                                                                                                                         | 1000000         |\n\n### <span id=\"page-25-2\"></span>**6.5 Single or Group Dealer Model**\n\n| Field<br>Name  | Type               | Description                                                                                                                                                                            | Example |\n|----------------|--------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|\n| singleDealer   | Dealer Model       | Any which is fetched from List Avail<br>able Dealers can be used                                                                                                                       |         |\n| dealerGroup    | Dealer Group Model | Any Dealer Group which is fetched<br>from List All Dealer Groups can be<br>used                                                                                                        |         |\n| isSingleDealer | Boolean            | Flag represents if singleDealer is pro<br>vided.<br>If isSingleDealer is true, then<br>singleDealer must be provided. If isS<br>ingleDealer is false, dealerGroup must<br>be provided. |         |\n\n### <span id=\"page-25-3\"></span>**6.6 Single or Group Counterpart Model**\n\n| Field<br>Name     | Type              | Description                             | Example |\n|-------------------|-------------------|-----------------------------------------|---------|\n| singleInstitution | Counterpart Model | Any Counterpart which is fetched from   |         |\n|                   |                   | List Available Counterparts can be used |         |\n\n| institutionGroup    | Counterpart<br>Group<br>Model | Any<br>Counterpart<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Counterpart<br>Groups can be used                                                                                                        |  |\n|---------------------|-------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n| isSingleInstitution | Boolean                       | Flag represents if singleInstitution is<br>provided. If isSingleInstitution is true,<br>then<br>singleInstitution<br>must<br>be<br>pro<br>vided. If isSingleInstitution is false, in<br>stitutionGroup must be provided. |  |\n\n### <span id=\"page-26-0\"></span>**6.7 Single or Group Legal Entity Model**\n\n| Field<br>Name       | Type                              | Description                                                                                                                                                                                                              | Example |\n|---------------------|-----------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|\n| singleInstitution   | Legal Entity Model                | Any Legal Entity which is fetched from<br>List Available Legal Entities can be<br>used                                                                                                                                   |         |\n| institutionGroup    | Legal<br>Entity<br>Group<br>Model | Any<br>Legal<br>Entity<br>Group<br>which<br>is<br>fetched from List All Legal Entities<br>Groups can be used                                                                                                             |         |\n| isSingleInstitution | Boolean                           | Flag represents if singleInstitution is<br>provided. If isSingleInstitution is true,<br>then<br>singleInstitution<br>must<br>be<br>pro<br>vided. If isSingleInstitution is false, in<br>stitutionGroup must be provided. |         |\n\n### <span id=\"page-26-1\"></span>**6.8 Single or Group Time Period Model**\n\n| Field<br>Name      | Type                             | Description                                                                                                                                                                                                                 | Example |\n|--------------------|----------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|\n| singleTimePeriod   | Time Period Model                | Please see more details in Time Period<br>Model                                                                                                                                                                             |         |\n| timePeriodGroup    | Time<br>Period<br>Group<br>Model | Any<br>Time<br>Period<br>Group<br>which<br>is<br>fetched<br>from<br>List<br>All<br>Time<br>Period<br>Group can be used                                                                                                      |         |\n| isSingleTimePeriod | Boolean                          | Flag represents if singleTimePeriod is<br>provided.<br>If<br>isSingleTimePeriod<br>is<br>true, then singleTimePeriod must be<br>provided.<br>If<br>isSingleTimePeriod<br>is<br>false, timePeriodGroup must be pro<br>vided. | false   |\n\n### <span id=\"page-26-2\"></span>**6.9 Single or Group Execution Method Model**\n\n| Field<br>Name | Type | Description | Example |\n|---------------|------|-------------|---------|\n|---------------|------|-------------|---------|\n\n![](_page_27_Picture_0.jpeg)\n\n| executionMethod       | ExecutionMethod<br>Model          | Any<br>Execution<br>Method<br>which<br>is<br>fetched from Get Available Execution<br>Methods can be used                                                                                                                                             |  |\n|-----------------------|-----------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n| executionMethodGroup  | Execution<br>Method<br>GroupModel | Any Execution Method Group which<br>is<br>fetched<br>from<br>List<br>All<br>Execution<br>Method Group can be used                                                                                                                                    |  |\n| singleExecutionMethod | Boolean                           | Flag<br>represents<br>if<br>singleExecution<br>Method is provided.<br>If singleExecu<br>tionMethod is true, then singleExecu<br>tionMethod must be provided.<br>If sin<br>gleExecutionMethod is false,<br>execu<br>tionMethodGroup must be provided. |  |\n\n## <span id=\"page-28-0\"></span>**7 Active Rule**\n\nAcive Rule operations allow clients to list the current active risk portfolio rules and their corresponding 'Utilization' amount. For an overview please see the [screenshot](#page-94-0) that demonstrates Active Rules view from Bridge Admin application.\n\n## <span id=\"page-28-1\"></span>**7.1 Active Rule Operations**\n\n| Operation                        | Http<br>Method | Description                                                                                                               | resource<br>path                      |\n|----------------------------------|----------------|---------------------------------------------------------------------------------------------------------------------------|---------------------------------------|\n| List                             | GET            | List all Active Rules                                                                                                     | /activeRule                           |\n| List                             | GET            | List Exceptional Limits of an Ac<br>tive Rule by given Rule name                                                          | /activeRule/{name}/limit/all          |\n| Get                              | GET            | Fetch a single Active Rule by given<br>Rule name                                                                          | /activeRule/{name}                    |\n| Get Limit                        | GET            | Get Limit of an Active Rule by<br>given Rule name                                                                         | /activeRule/{name}/limit              |\n| Get<br>Exceptional<br>Limit      | GET            | Get Exceptional Limit of an Active<br>Rule by given Rule name and date<br>(in a format \"yyyyMMdd\")                        | /activeRule/{name}/limit/{date}       |\n| Get Utilization                  | GET            | Get Utilization of an Active Rule by<br>given Rule name                                                                   | /activeRule/{name}/utilization        |\n| Get Utilization                  | GET            | Get Utilization of an Active Rule by<br>given Rule name and date (in a for<br>mat \"yyyyMMdd\")                             | /activeRule/{name}/utilization/{date} |\n| Update Limit                     | PUT            | Update Limit of an Active Rule by<br>given Rule name                                                                      | /activeRule/{name}/limit              |\n| Update Limits                    | PUT            | Batch operation to update Limits of<br>Active Rules by given Rule names                                                   | /activeRule/limits                    |\n| Set<br>Exceptional<br>Limit      | PUT            | Set Exceptional Limit of an Active<br>Rule by given Rule name and date<br>(in a format \"yyyyMMdd\")                        | /activeRule/{name}/limit/{date}       |\n| Set<br>Exceptional<br>Limits     | PUT            | Batch operation to set Exceptional<br>Limits of Active Rules by given<br>Rule names and dates (in a format<br>\"yyyyMMdd\") | /activeRule/limit/all                 |\n| Delete<br>Excep<br>tional Limits | DELETE         | Delete all Exceptional Limits of an<br>Active Rule by given Rule name                                                     | /activeRule/{name}/limit/all          |\n\nThe followings are the operations which can be done on Active Rules.\n\n| Delete       | Excep | DELETE | Delete Exceptional Limit of an Ac | /activeRule/{name}/limit/{date} |\n|--------------|-------|--------|-----------------------------------|---------------------------------|\n| tional Limit |       |        | tive Rule by given Rule name and  |                                 |\n|              |       |        | date (in a format \"yyyyMMdd\")     |                                 |\n\n### <span id=\"page-29-0\"></span>**7.1.1 List all Active Rules**\n\nList all [Active Rules.](#page-35-1) HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/activeRule\n\n### Example Response\n\n[\n\n```\n{\n   \"algo\": {\n      \"name\": \"Net Daily Settlement Limit\"\n   },\n   \"name\": \"ActiveRule-Test\",\n   \"id\": \"5268107\",\n   \"singleOrGroupDealer\": {\n         \"dealers\": [\n            {\n                \"name\": \"MT.Treasurer1\"\n            }\n         ],\n         \"name\": \"DealerGrp-Test\"\n   },\n   \"singleOrGroupLegalEntity\": {\n      \"singleInstitution\": {\n         \"id\": \"360T.INT_LIMAPI.TEST\",\n         \"name\": \"360T.INT_LIMAPI.TEST\"\n      },\n      \"isSingleInstitution\": true\n   },\n   \"limit\": 11111,\n   \"singleOrGroupTimePeriod\": {\n      \"singleTimePeriod\": {\n         \"from\": {\n            \"longName\": \"SPOT\",\n            \"shortName\": \"SP\"\n         },\n         \"to\": {\n            \"longName\": \"1 WEEK\",\n            \"shortName\": \"1W\"\n         }\n      },\n      \"isSingleTimePeriod\": true\n   },\n   \"executionMethod\": {\n      \"name\": \"SEP\"\n   }\n   \"utilization\": 0\n```\n\n]\n\n}\n\n### <span id=\"page-30-0\"></span>**7.1.2 List all Exceptional Limits of an Active Rule**\n\nList all [Exceptional Limits](#page-36-0) by [Active Rule'](#page-35-1)s name. HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/all\n\n### Example Response\n\n```\n[\n   {\n      \"date\": \"20210520\",\n      \"limit\": 11111\n   },\n   {\n      \"date\": \"20210521\",\n      \"limit\": 22222\n   }\n]\n```\n\n### <span id=\"page-30-1\"></span>**7.1.3 Get Active Rule**\n\nFetch a single [Active Rule](#page-35-1) by name. HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test\n```\n\n```\n{\n   \"algo\": {\n      \"name\": \"Net Daily Settlement Limit\"\n   },\n   \"name\": \"ActiveRule-Test\",\n   \"id\": \"5268107\",\n   \"singleOrGroupDealer\": {\n         \"dealers\": [\n            {\n               \"name\": \"MT.Treasurer1\"\n            }\n         ],\n         \"name\": \"DealerGrp-Test\"\n   },\n   \"singleOrGroupLegalEntity\": {\n      \"singleInstitution\": {\n         \"id\": \"360T.INT_LIMAPI.TEST\",\n```\n\n```\n\"name\": \"360T.INT_LIMAPI.TEST\"\n   },\n   \"isSingleInstitution\": true\n},\n\"limit\": 11111,\n\"singleOrGroupTimePeriod\": {\n   \"singleTimePeriod\": {\n      \"from\": {\n         \"longName\": \"SPOT\",\n         \"shortName\": \"SP\"\n      },\n      \"to\": {\n         \"longName\": \"1 WEEK\",\n         \"shortName\": \"1W\"\n      }\n   },\n   \"isSingleTimePeriod\": true\n},\n\"executionMethod\": {\n   \"name\": \"SEP\"\n}\n\"utilization\": 0\n```\n\n### <span id=\"page-31-0\"></span>**7.1.4 Get Limit value of an Active Rule**\n\nGet [Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name. HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit\n```\n\n#### Example Response\n\n}\n\n{\n\n}\n\n{\n\n}\n\n```\n\"value\": 11111\n```\n\n#### <span id=\"page-31-1\"></span>**7.1.5 Get Exceptional Limit value of an Active Rule for a given date**\n\nGet [Exceptional Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name and date in a format \"yyyyMMdd\". HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520\n```\n\n```\n\"value\": 11111\n```\n\n### <span id=\"page-32-0\"></span>**7.1.6 Get Utilization value of an Active Rule**\n\nGet [Utilizaton'](#page-37-0)s value by [Active Rule'](#page-35-1)s name. HTTP Request\n\n```\nGET\n```\n\n{\n\n}\n\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/utilization\n\n### Example Response\n\n```\n\"value\": 11111\n```\n\n### <span id=\"page-32-1\"></span>**7.1.7 Get Utilization value of an Active Rule for a given date**\n\nGet [Utilizaton'](#page-37-0)s value by [Active Rule'](#page-35-1)s name and date in a format \"yyyyMMdd\". HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/utilization/20210520\n```\n\n#### Example Response\n\n```\n{\n   \"value\": 11111\n}\n```\n\n### <span id=\"page-32-2\"></span>**7.1.8 Update Limit value of an Active Rule**\n\nUpdate [Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name.\n\nIMPORTANT: Please note that the change done on active limit is valid until the day rollover at New York 5 PM. After day rollover, the generic limit defined on Risk Portfolio rule will be valid. HTTP Request\n\nPUT\n\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit\n\n### Example Request\n\n```\n\"value\": 11111\n```\n\n}\n\n{\n\n{\n\n```\n\"code\": 200,\n\"message\": \"Response\"\n```\n\n}\n\n### <span id=\"page-33-0\"></span>**7.1.9 Update Limits of an Active Rule**\n\nBatch operation to update [Limits](#page-25-1) by [Active Rules'](#page-35-1) names. HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limits\n\n### Example Request\n\n```\n[\n   {\n      \"ruleName\": \"ActiveRule-Test\",\n      \"limit\": 11111\n   },\n   {\n      \"ruleName\": \"ActiveRule-Test\",\n      \"limit\": 22222\n   },\n   {\n      \"ruleName\": \"ActiveRule-Test-2\",\n      \"limit\": 33333\n   }\n]\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-33-1\"></span>**7.1.10 Set Exceptional Limit value of an Active Rule**\n\nSet [Exceptional Limit'](#page-25-0)s value by [Active Rule'](#page-35-1)s name and date in a format \"yyyyMMdd\". HTTP Request\n\n```\nPUT\n```\n\n```\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520\n```\n\n#### Example Request\n\n{\n\n}\n\n```\n\"value\": 11111\n```\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-34-0\"></span>**7.1.11 Set Exceptional Limits of an Active Rule**\n\nBatch operation to set [Exceptional Limits](#page-36-1) by [Active Rules'](#page-35-1) names and dates in a format \"yyyyMMdd\". HTTP Request\n\nPUT\n\n```\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/limit/all\n```\n\n### Example Request\n\n```\n[\n   {\n      \"ruleName\": \"ActiveRule-Test\",\n      \"date\": \"20210520\",\n      \"limit\": 11111\n   },\n   {\n      \"ruleName\": \"ActiveRule-Test\",\n      \"date\": \"20210521\",\n      \"limit\": 22222\n   },\n   {\n      \"ruleName\": \"ActiveRule-Test-2\",\n      \"date\": \"20210520\",\n      \"limit\": 33333\n   }\n]\n```\n\n### Example Response\n\n{ \"code\": 200, \"message\": \"Response\" }\n\n### <span id=\"page-34-1\"></span>**7.1.12 Delete all Exceptional Limits of an Active Rule**\n\nDelete all [Exceptional Limits](#page-36-0) by [Active Rule'](#page-35-1)s name. HTTP Request\n\nDELETE https://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/all\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-35-0\"></span>**7.1.13 Delete Exceptional Limit of an Active Rule for a given date**\n\nDelete [Exceptional Limit](#page-25-0) by [Active Rule'](#page-35-1)s name and date in a format \"yyyyMMdd\". HTTP Request\n\nDELETE\n\n```\nhttps://apigateway-int.360t.com:7060/limitapi/v2/activeRule/ActiveRule-Test/limit/20210520\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-35-1\"></span>**7.2 Active Rule Model**\n\n| Field<br>Name            | Type                                  | Description                                                                                                                                                                                        | Example                                     |\n|--------------------------|---------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------|\n| id                       | String                                | Unique and unmodifiable Id of Ac<br>tive Rule.<br>360T generates this Id<br>once an existing Risk Portfolio Rule<br>is rolled over                                                                 | fxpgp3-c0a815a5-<br>k5zby2j6-l7_act20200210 |\n| name                     | String                                | Unique Name of Active Rule. This<br>field is used in Active Rule's end<br>points. This field is required when<br>adding a new Active Rule.<br>The<br>value of this field must not be mod<br>ified. | ActiveRule-Test                             |\n| algo                     | Algorithm Model                       | Algorithm which is defined in an<br>existing Risk Portfolio Rule                                                                                                                                   |                                             |\n| singleOrGroupDealer      | Single or Group Dealer<br>Model       | Dealer Group or Dealer which is<br>defined in an existing Risk Portfo<br>lio Rule                                                                                                                  |                                             |\n| singleOrGroupCounterpart | Single or Group Coun<br>terpart Model | Counterpart Group or Counterpart<br>which is defined in an existing Risk<br>Portfolio Rule                                                                                                         |                                             |\n\n![](_page_36_Picture_0.jpeg)\n\n| singleOrGroupLegalEntity                           | Single or Group Legal<br>Entity Model | Legal Entity Group or Legal Entity<br>which is defined in an existing Risk<br>Portfolio Rule                                                                                                                                                  |         |\n|----------------------------------------------------|---------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|\n| portfolio                                          | Portfolio Model                       | Portfolio which is defined in an ex<br>isting Risk Portfolio Rule                                                                                                                                                                             |         |\n| singleOrGroupTimePeriod                            | Time<br>Period<br>Group<br>Model      | Time Period Group or Time Period<br>which is defined in an existing Risk<br>Portfolio Rule                                                                                                                                                    |         |\n| singleOrGroupExecutionMethod Single or Group Execu | tion Method Model                     | Execution method refers to how the<br>trade is executed and is part of<br>risk portfolio model to allow defin<br>ing limits differently based on how<br>trade is negotiated and executed.If<br>not provided, 'any' will be used as<br>default |         |\n| limit                                              | Long                                  | Amount that needs to be used as<br>limit                                                                                                                                                                                                      | 1000000 |\n| utilization                                        | Long                                  | Amount that is used so far                                                                                                                                                                                                                    | 10000   |\n\n### <span id=\"page-36-0\"></span>**7.3 Exceptional Limit Model**\n\n| Field<br>Name | Type   | Description                                                                 | Example  |\n|---------------|--------|-----------------------------------------------------------------------------|----------|\n| date          | String | Date<br>in<br>a<br>format<br>\"yyyyMMdd\"<br>to<br>which the limit is applied | 20210520 |\n| limit         | Long   | Amount that needs to be used as excep<br>tional limit                       | 1000000  |\n\n### <span id=\"page-36-1\"></span>**7.4 Exceptional Limit Row Model**\n\n| Field<br>Name | Type   | Description                                                                                                                                                                                   | Example         |\n|---------------|--------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|\n| ruleName      | String | Unique Name of Active Rule.<br>This<br>field is used in Active Rule's endpoints.<br>This field is required when adding a<br>new Active Rule. The value of this field<br>must not be modified. | ActiveRule-Test |\n| date          | String | Date<br>in<br>a<br>format<br>\"yyyyMMdd\"<br>to<br>which the limit is applied                                                                                                                   | 20210520        |\n| limit         | Long   | Amount that needs to be used as excep<br>tional limit                                                                                                                                         | 1000000         |\n\n### <span id=\"page-37-0\"></span>**7.5 Utilizaton Value Model**\n\n| Field<br>Name | Type | Description                | Example |\n|---------------|------|----------------------------|---------|\n| value         | Long | Amount that is used so far | 1000000 |\n\n## <span id=\"page-38-0\"></span>**8 Portfolio**\n\nPortfolio consists of [Product Group](#page-50-1) and Currency Couple Group. For an overview please see the [screenshot](#page-92-1) that demonstrates how to define Portfolio from Bridge Admin application.\n\n### <span id=\"page-38-1\"></span>**8.1 Portfolio Operations**\n\nThe followings are the operations which can be done on Portfolio.\n\n| Operation | Http<br>Method | Description                                                     | resource<br>path  |\n|-----------|----------------|-----------------------------------------------------------------|-------------------|\n| List      | GET            | List all Portfolios                                             | /portfolio        |\n| Find One  | GET            | List a Portfolio by given name                                  | /portfolio/{name} |\n| Add       | POST           | Add a Portfolio                                                 | /portfolio        |\n| Update    | PUT            | Update given Portfolio. Update works<br>according to given name | /portfolio        |\n| Delete    | DELETE         | Delete a Portfolio by given name                                | /portfolio/{name} |\n\n### <span id=\"page-38-2\"></span>**8.1.1 Add a Portfolio**\n\nAdd a new [Portfolio](#page-42-0)\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/portfolio\n\n### Example Request\n\n```\n{\n   \"name\": \"Portfolio-Test\",\n   \"portfolioElement\": {\n      \"currencyCoupleGroup\": {\n         \"singleCurrencyCouple\": {\n            \"baseCurrency\": {\n               \"isoCode\": \"EUR\"\n            },\n            \"quoteCurrency\": {\n               \"isoCode\": \"***\"\n            }\n         },\n         \"isSingleCurrencyCouple\": true\n      },\n      \"productGroup\": {\n         \"productGroup\": {\n            \"name\": \"ProductGrp-Test\",\n            \"products\": [\n```\n\n### 8.1. Portfolio Operations Portfolio\n\n```\n{\n                    \"name\": \"Fx Forward\"\n                 },\n                 {\n                    \"name\": \"Fx Spot\"\n                 }\n             ]\n          },\n          \"isSingleProduct\": false\n      }\n   }\n}\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-39-0\"></span>**8.1.2 List All Portolios**\n\nList all [Portfolios.](#page-42-0)\n\n### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/portfolio\n\n### Example Response\n\n[\n\n```\n{\n   \"id\": \"2014231\",\n   \"name\": \"Portfolio-Test\",\n   \"portfolioElement\": {\n      \"singleOrGroupCurrencyCouple\": {\n         \"singleCurrencyCouple\": {\n            \"baseCurrency\": {\n                \"isoCode\": \"***\"\n            },\n            \"quoteCurrency\": {\n                \"isoCode\": \"***\"\n            }\n         },\n         \"isSingleCurrencyCouple\": true\n      },\n      \"singleOrGroupProduct\": {\n         \"productGroup\": {\n            \"id\": \"1883301\",\n            \"name\": \"ProductGrp-Test\",\n            \"products\": [\n                {\n                   \"name\": \"Fx Forward\"\n                },\n```\n\n### 8.1. Portfolio Operations Portfolio\n\n```\n{\n                         \"name\": \"Fx Spot\"\n                     }\n                 ]\n              },\n              \"isSingleProduct\": false\n          }\n       }\n   }\n]\n```\n\n### <span id=\"page-40-0\"></span>**8.1.3 Update a Portfolio**\n\nUpdate existing [Portfolio.](#page-42-0)\n\n### HTTP Request\n\n```\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/portfolio/Portfolio-Test\n```\n\n### Example Request\n\n```\n{\n   \"name\": \"Portfolio-Test\",\n   \"portfolioElement\": {\n      \"currencyCoupleGroup\": {\n         \"singleCurrencyCouple\": {\n             \"baseCurrency\": {\n                \"isoCode\": \"***\"\n             },\n             \"quoteCurrency\": {\n                \"isoCode\": \"USD\"\n             }\n         },\n         \"isSingleCurrencyCouple\": true\n      },\n      \"productGroup\": {\n         \"productGroup\": {\n             \"name\": \"ProductGrp-Test\",\n             \"products\": [\n                {\n                   \"name\": \"Fx Forward\"\n                },\n                {\n                   \"name\": \"Fx Spot\"\n                }\n             ]\n         },\n         \"isSingleProduct\": false\n      }\n   }\n}\n```\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-41-0\"></span>**8.1.4 Get a Portfolio**\n\nGet a [Portfolio](#page-42-0) by name.\n\n#### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/portfolio/Portfolio-Test\n```\n\n#### Example Response\n\n```\n{\n   \"id\": \"2014231\",\n   \"name\": \"Portfolio-Test\",\n   \"portfolioElement\": {\n      \"currencyCoupleGroup\": {\n         \"singleCurrencyCouple\": {\n             \"baseCurrency\": {\n                \"isoCode\": \"***\"\n             },\n             \"quoteCurrency\": {\n                \"isoCode\": \"***\"\n             }\n         },\n         \"isSingleCurrencyCouple\": true\n      },\n      \"productGroup\": {\n         \"productGroup\": {\n             \"id\": \"1883301\",\n             \"name\": \"ProductGrp-Test\",\n             \"products\": [\n                {\n                   \"name\": \"Fx Forward\"\n                },\n                {\n                   \"name\": \"Fx Spot\"\n                }\n             ]\n         },\n         \"isSingleProduct\": false\n      }\n   }\n}\n```\n\n### <span id=\"page-41-1\"></span>**8.1.5 Delete a Portfolio**\n\nRemove a Portfolio by name.\n\n### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/portfolio/Portfolio-Test\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-42-0\"></span>**8.2 Portfolio Model**\n\n| Field<br>Name    | Type                          | Description                                                                                                                                                                                                                                                                 | Example     |\n|------------------|-------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|\n| id               | String                        | Unique and unmodifiable Id of Portfo<br>lio. 360T generates this Id once a new<br>Portfolio is added.                                                                                                                                                                       | 2014231     |\n| name             | String                        | The name must be unique.<br>It is bet<br>ter to define logical names to easily<br>recognize which portfolio elements this<br>group has. This field is required when<br>adding a new or updating the existing<br>Portfolio. The value of this field must<br>not be modified. | Portfolio 1 |\n| portfolioElement | Portfolio<br>Element<br>Model | List of Portfolio Element can be speci<br>fied                                                                                                                                                                                                                              |             |\n\n### <span id=\"page-42-1\"></span>**8.3 Portfolio Element Model**\n\n| Field<br>Name               | Type                                      | Description                                                       | Example |\n|-----------------------------|-------------------------------------------|-------------------------------------------------------------------|---------|\n| singleOrGroupProduct        | Single Or Group Product Model             | A<br>single<br>Product<br>or<br>Product<br>Group can be specified |         |\n| singleOrGroupCurrencyCouple | Single Or Group Currency Cou<br>ple Model | A Currency Couple or Currency<br>Couple Group can be specified    |         |\n\n### <span id=\"page-42-2\"></span>**8.4 Single Or Group Product Model**\n\n| Field<br>Name | Type                      | Description                                                                       | Example |\n|---------------|---------------------------|-----------------------------------------------------------------------------------|---------|\n| singleProduct | Product Model             | Any Product which is fetched from Get<br>Available Products can be used           |         |\n| productGroup  | Product<br>Group<br>Model | Any Product Group which is fetched<br>from List All Product Groups can be<br>used |         |\n\n| isSingleProduct | Boolean | Flag represents if singleProduct is pro<br>vided. If isSingleProduct is true, then<br>singleProduct must be provided.<br>If |  |\n|-----------------|---------|-----------------------------------------------------------------------------------------------------------------------------|--|\n|                 |         | isSingleProduct is false, productGroup                                                                                      |  |\n|                 |         | must be provided.                                                                                                           |  |\n\n### <span id=\"page-43-0\"></span>**8.5 Currency Couple Model**\n\n| Field<br>Name | Type                            | Description                                                                                            | Example |\n|---------------|---------------------------------|--------------------------------------------------------------------------------------------------------|---------|\n| baseCurrency  | IsoCurrency Model               | Base currency represents currenyc1 in<br>Trade.<br>E.g EUR/USD, EUR is base<br>currency in this case   |         |\n|               | quoteCurrency IsoCurrency Model | Quote currency represents currency2 in<br>Trade.<br>E.g EUR/USD, USD is quote<br>currency in this case |         |\n\n### <span id=\"page-43-1\"></span>**8.6 Single Or Group Currency Couple Model**\n\n| Field<br>Name          | Type                              | Description                                                                                                                                                                                                                            | Example |\n|------------------------|-----------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|\n| singleCurrencyCouple   | Currency<br>Couple<br>Model       | Please see Currency Couple Model<br>for more details                                                                                                                                                                                   |         |\n| currencyCoupleGroup    | Currency<br>Couple<br>Group Model | Any Currency Couple Group which<br>is fetched from List All Currency<br>Couple Groups can be used                                                                                                                                      |         |\n| isSingleCurrencyCouple | Boolean                           | Flag represents if singleCurrency<br>Couple is provided. If isSingleCur<br>rencyCouple is true,<br>then single<br>CurrencyCouple must be provided.<br>If isSingleCurrencyCouple is false,<br>currencyCoupleGroup must be pro<br>vided. |         |\n\n## <span id=\"page-44-0\"></span>**9 Algorithm**\n\nAlgorithm refers to the parameter of any risk portfolio rule which would determine how to calculate the utilization amount of portfolio. Only GET operation can be used and it returns available Algorithms.\n\n### <span id=\"page-44-1\"></span>**9.1 Algorithm Operations**\n\n### <span id=\"page-44-2\"></span>**9.1.1 Get Available Algorithms**\n\nList available [Algorithms](#page-44-3).\n\n### HTTP Request\n\n[\n\n]\n\n```\nGET https://apigateway-int.360t.com:7060/limitapi/v2/algo\n```\n\n### Example Response\n\n```\n{\n \"name\": \"Net Daily Settlement Limit\"\n},\n{\n \"name\": \"Gross Daily Settlement Limit\"\n},\n{\n \"name\": \"Aggregate Gross Settlement Limit\"\n},\n{\n \"name\": \"Aggregate Net Settlement Limit\"\n},\n{\n \"name\": \"Potential Future Exposure\"\n},\n{\n \"name\": \"Externally Managed Limit\"\n},\n{\n \"name\": \"Daily Net Trading Limit\"\n}\n```\n\n### <span id=\"page-44-3\"></span>**9.2 Algorithm Model**\n\n| Field<br>Name | Type   | Description       | Example                   |\n|---------------|--------|-------------------|---------------------------|\n| name          | String | Name of Algorithm | POTENTIAL_FUTURE_EXPOSURE |\n\n## <span id=\"page-45-0\"></span>**10 Execution Method**\n\nExecution method refers to how the trade is executed and is part of risk portfolio model to allow defining limits differently based on how trade is negotiated and executed.\n\n### <span id=\"page-45-1\"></span>**10.1 Execution Method Operation**\n\n### <span id=\"page-45-2\"></span>**10.1.1 Get Available Execution Methods**\n\nList available [Execution Methods](#page-45-3) in 360T system.\n\n### HTTP Request\n\n```\nGET https://apigateway-int.360t.com:7060/limitapi/v2/executionMethod\n```\n\n### Example Response\n\n```\n[\n   {\n      \"name\": \"SEP\"\n   },\n   {\n      \"name\": \"RFS\"\n   },\n   {\n      \"name\": \"OMT\"\n   },\n   {\n      \"name\": \"MidMatch\"\n   },\n   {\n      \"name\": \"HST Engine\"\n   },\n   {\n      \"name\": \"HST OrderBook\"\n   },\n   {\n      \"name\": \"GTX CLOB\"\n   }\n]\n```\n\n### <span id=\"page-45-3\"></span>**10.2 ExecutionMethod Model**\n\n| Field<br>Name | Type   | Description              | Example |\n|---------------|--------|--------------------------|---------|\n| name          | String | Name of execution method | SEP     |\n\n## <span id=\"page-46-0\"></span>**11 Execution Method Group**\n\nExecution Method Group is used to group execution methods. For an overview please see the [screenshot](#page-93-1) that demonstrates how to define Execution Method Groups from Bridge Admin application.\n\n### <span id=\"page-46-1\"></span>**11.1 Execution Method GroupOperations**\n\n| Operation                         | Http<br>Method | Description                                                         | resource<br>path                    |\n|-----------------------------------|----------------|---------------------------------------------------------------------|-------------------------------------|\n| List                              | GET            | List<br>all<br>Execution<br>Method Group                            | GET /executionMethodGroup           |\n| Find One                          | GET            | List<br>a<br>Execution<br>Method<br>Groupby<br>given name           | GET /executionMethodGroup/{name}    |\n| Add                               | POST           | Add<br>a<br>Execution<br>Method Group                               | POST /executionMethodGroup          |\n| Add<br>New<br>Execution<br>Method | POST           | Add<br>a<br>Currency<br>to<br>specific<br>Execution<br>Method Group | POST /executionMethodGroup/{name}   |\n| Update                            | PUT            | Update<br>given<br>Ex<br>ecution<br>Method<br>Groupby given name    | PUT /executionMethodGroup/{name}    |\n| Delete                            | DELETE         | Delete<br>a<br>Execution<br>Method<br>Groupby<br>given name         | DELETE /executionMethodGroup/{name} |\n\nThe followings are the operations which can be done on Execution Method Group.\n\n### <span id=\"page-46-2\"></span>**11.1.1 Add a Execution Method Group**\n\nAdd a new [Execution Method Group](#page-49-1) to 360T system.\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup\n\n### Example Response\n\n{\n\n```\n\"executionMethods\": [\n   {\n      \"name\": \"RFS\"\n   },\n   {\n      \"name\": \"OMT\"\n```\n\n} ], \"name\": \"Grp-Test\"\n\n#### Example Response\n\n}\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-47-0\"></span>**11.1.2 List All Execution Method Group**\n\nList all [Execution Method Groups.](#page-49-1)\n\n### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup\n\n### Example Response\n\n```\n[\n   {\n      \"executionMethods\": [\n          {\n             \"name\": \"RFS\"\n          },\n          {\n             \"name\": \"OMT\"\n          }\n      ],\n      \"id\": \"7227449\",\n      \"name\": \"Grp-Test\"\n   },\n   {\n      \"executionMethods\": [],\n      \"id\": \"3490945\",\n      \"name\": \"ExecutionMethodGrp-Test\"\n   }\n]\n```\n\n### <span id=\"page-47-1\"></span>**11.1.3 Update a Execution Method Group**\n\nUpdate given [Execution Method Group.](#page-49-1)\n\n### HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test\n\nExample Request\n\n```\n{\n      \"executionMethods\": [\n          {\n             \"name\": \"RFS\"\n          },\n          {\n             \"name\": \"OMT\"\n          }\n      ],\n      \"name\": \"Grp-Test\"\n}\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-48-0\"></span>**11.1.4 Add Execution Method to Execution Method Group**\n\nAdd new [Execution Method](#page-45-3) to existing Execution Method Group.\n\n### HTTP Request\n\n```\nPOST\nhttps://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test\n```\n\n#### Example Request\n\n```\n{\n \"name\": \"SEP\"\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-48-1\"></span>**11.1.5 Get a Execution Method Group**\n\nGet a [Execution Method Group](#page-49-1) by name.\n\n#### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test\n```\n\n```\n{\n   \"executionMethods\": [\n      {\n          \"name\": \"RFS\"\n      },\n      {\n          \"name\": \"OMT\"\n      }\n   ],\n   \"id\": \"7227449\",\n   \"name\": \"Grp-Test\"\n```\n\n}\n\n### <span id=\"page-49-0\"></span>**11.1.6 Delete a Execution Method Group**\n\nDelete Execution Method Group by name.\n\n### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/executionMethodGroup/Grp-Test\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-49-1\"></span>**11.2 Execution Method GroupModel**\n\n| Field<br>Name    | Type                                                    | Description                                                                                                                                                                                                                                                                           | Example                  |\n|------------------|---------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------|\n| id               | String                                                  | Unique and unmodifiable Id of Execu<br>tion Method Group.<br>360T generates<br>this Id once a new Execution Method<br>Group is added.                                                                                                                                                 | lov5ea-7f011-k8ag9hay-18 |\n| name             | String                                                  | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which Execution Methodss this<br>group has. This field is required when<br>adding a new or updating the existing<br>Execution Method Group. The value of<br>this field must not be modified. | Streaming                |\n| executionMethods | List <executionmethod<br>Model&gt;</executionmethod<br> | List of Execution Methods which are<br>fetched from Get Available Execution<br>Methods can be used                                                                                                                                                                                    |                          |\n\n## <span id=\"page-50-0\"></span>**12 Product Group**\n\nProduct Group consists of several Fx Product. Product Group is used to define [Portfolio.](#page-50-1) And Portfolio is used to define [Risk Portfolio Rule.](#page-23-1) For an overview please see the [screenshot](#page-91-2) that demonstrates how to define Product Group from Bridge Admin application.\n\n### <span id=\"page-50-1\"></span>**12.1 Product Group Operations**\n\nThe followings are the operations which can be done on Product Group.\n\n| Operation | Http<br>Method | Description                                                            | resource<br>path     |\n|-----------|----------------|------------------------------------------------------------------------|----------------------|\n| List      | GET            | List all product groups                                                | /productGroup        |\n| Find One  | GET            | List a Product Group by given name                                     | /productGroup/{name} |\n| Add       | POST           | Add a Product Group                                                    | /productGroup        |\n| Update    | PUT            | Update given product group.<br>Update<br>works according to given name | /productGroup/{name} |\n| Delete    | DELETE         | Delete a Product Group by given name                                   | /productGroup/{name} |\n\n### <span id=\"page-50-2\"></span>**12.1.1 Add a Product Group**\n\nAdd a new [Product Group](#page-53-1) to 360T Risk Portfolio System.\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/productGroup\n\n### Example Request\n\n```\n[\n   {\n      \"name\": \"ProductGrp-Test\",\n      \"products\": [\n          {\n             \"name\": \"Fx Forward\"\n          },\n          {\n             \"name\": \"NDF\"\n          }\n      ]\n   }\n]\n```\n\n### Example Response\n\n{\n\n```\n\"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-51-0\"></span>**12.1.2 List All Product Groups**\n\nList all [Product Groups.](#page-53-1)\n\n#### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/productGroup\n\n#### Example Response\n\n```\n[\n   {\n      \"id\": \"1883301\",\n      \"name\": \"ProductGrp-Test\",\n      \"products\": [\n          {\n             \"name\": \"Fx Forward\"\n          },\n          {\n             \"name\": \"Fx Spot\"\n          }\n      ]\n   }\n]\n```\n\n#### <span id=\"page-51-1\"></span>**12.1.3 Update a Product Group**\n\nUpdate given [Product Group.](#page-53-1)\n\n#### HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test\n\n### Example Request\n\n```\n{\n   \"name\": \"ProductGrp-Test\",\n   \"products\": [\n      {\n          \"name\": \"Fx Forward\"\n      },\n      {\n          \"name\": \"Fx Swap\"\n      }\n   ]\n}\n```\n\n#### Example Response\n\n#### 52\n\n©360 Treasury Systems AG, 2019, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-52-0\"></span>**12.1.4 Add a Product to Product Group**\n\nAdd a new [Product](#page-54-3) to existing Product Group.\n\n#### HTTP Request\n\n```\nPOST\nhttps://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test\n```\n\n#### Example Request\n\n```\n{\n \"name\": \"Block-Trade\"\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-52-1\"></span>**12.1.5 Get a Product Group**\n\nGet a [Product Group](#page-53-1) by name.\n\n#### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test\n```\n\n```\n{\n   \"id\": \"1883301\",\n   \"name\": \"ProductGrp-Test\",\n   \"products\": [\n      {\n          \"name\": \"Fx Forward\"\n      },\n      {\n          \"name\": \"Fx Spot\"\n      }\n   ]\n}\n```\n\n### <span id=\"page-53-0\"></span>**12.1.6 Delete a Product Group**\n\nDelete Product Group by name.\n\n### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/productGroup/ProductGrp-Test\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-53-1\"></span>**12.2 Product Group Model**\n\n| Field<br>Name | Type                                    | Description                                                                                                                                                                                                                                                        | Example       |\n|---------------|-----------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------|\n| id            | String                                  | Unique and unmodifiable Id of Product<br>Group.<br>360T generates this Id once a<br>new Product Group is added.                                                                                                                                                    | 1883301       |\n| name          | String                                  | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which products this group has.<br>This field is required when adding a<br>new or updating the existing Product<br>Group. The value of this field must not<br>be modified. | Main Products |\n| products      | List <product<br>Model&gt;</product<br> | A Product or List of Product which are<br>fetched from Get Available Products<br>can be used                                                                                                                                                                       |               |\n\n## <span id=\"page-54-0\"></span>**13 Product**\n\nThis chapter explains the Product model and operations. Product refers to the product type of any trade intention such as FX Spot, FX Swap etc. and is part of 'Portfolio' paramater of a risk portfolio rule. Only GET operation can be used which would return the all available products.\n\n### <span id=\"page-54-1\"></span>**13.1 Product Operations**\n\n### <span id=\"page-54-2\"></span>**13.1.1 Get Available Products**\n\nList all supported [Products.](#page-54-3)\n\n### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/product\n\n### Example Response\n\n```\n[\n   {\n      \"name\": \"Fx Spot\"\n   },\n   {\n      \"name\": \"Fx Forward\"\n   },\n   {\n      \"name\": \"Fx Swap\"\n   },\n   {\n      \"name\": \"NDF\"\n   },\n   {\n      \"name\": \"NDS\"\n   },\n   {\n      \"name\": \"Block-Trade\"\n   },\n   {\n      \"name\": \"Fx Time Option\"\n   }\n]\n```\n\n### <span id=\"page-54-3\"></span>**13.2 Product Model**\n\n| Field<br>Name | Type   | Description     | Example |\n|---------------|--------|-----------------|---------|\n| name          | String | Name of Product | Fx Swap |\n\n## <span id=\"page-55-0\"></span>**14 Currency Couple Group**\n\nCurrency Couple Group is a component of a 'Portfolio' which is a required parameter of any risk portfolio rule. A currency couple group can contain multiple currency couple. In order to add a currency couple into a group, clients can either use the single currency group that they previously defined in 360T's Limit Monitor System or add currency pairs customly by using Iso Currencies. For an overview please see the [screenshot](#page-91-3) that demonstrates how to define Currency Couple Group from Bridge Admin application.\n\n### <span id=\"page-55-1\"></span>**14.1 Currency Couple Group Operations**\n\n| Operation              | Http<br>Method | Description                                                         | resource<br>path                   |\n|------------------------|----------------|---------------------------------------------------------------------|------------------------------------|\n| List                   | GET            | List<br>all<br>Currency<br>Couple<br>Groups                         | GET /currencyCoupleGroup           |\n| Find One               | GET            | List<br>a<br>Currency<br>Couple<br>Group by given name              | GET /currencyCoupleGroup/{name}    |\n| Add                    | POST           | Add<br>a<br>Currency<br>Couple<br>Group                             | POST /currencyCoupleGroup          |\n| Add Currency<br>Couple | POST           | Add a Currency Couple to<br>specific<br>Currency<br>Couple<br>Group | POST /currencyCoupleGroup/{name}   |\n| Update                 | PUT            | Update given Currency Cou<br>ple Group by given name                | PUT /currencyCoupleGroup/{name}    |\n| Delete                 | DELETE         | Delete a Currency Couple<br>Group by given name                     | DELETE /currencyCoupleGroup/{name} |\n\nThe followings are the operations which can be done on Currency Couple Group.\n\n### <span id=\"page-55-2\"></span>**14.1.1 Add a Currency Couple Group**\n\nAdd a new [Currency Couple Group](#page-58-1) to 360T system.\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup\n\n### Example Request\n\n{\n\n```\n\"currencyCouples\": [\n   {\n      \"baseCurrency\": {\n         \"isoCode\": \"GBP\"\n      },\n      \"quoteCurrency\": {\n```\n\n56\n\n```\n\"isoCode\": \"CHF\"\n          }\n      }\n   ],\n   \"name\": \"CcyCoupleGrp-Test\"\n}\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-56-0\"></span>**14.1.2 List All Currency Couple Groups**\n\nList all [Currency Couple Groups.](#page-58-1)\n\n### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup\n\n### Example Response\n\n```\n[\n   {\n      \"currencyCouples\": [\n          {\n             \"baseCurrency\": {\n                \"isoCode\": \"AUD\"\n             },\n             \"quoteCurrency\": {\n                \"isoCode\": \"CAD\"\n             }\n          },\n          {\n             \"baseCurrency\": {\n                \"isoCode\": \"GBP\"\n             },\n             \"quoteCurrency\": {\n                \"isoCode\": \"CHF\"\n             }\n          }\n      ],\n      \"id\": \"9214996\",\n      \"name\": \"CcyCoupleGrp-Test\"\n   }\n]\n```\n\n### <span id=\"page-56-1\"></span>**14.1.3 Update a Currency Couple Group**\n\nUpdate given [Currency Couple Group.](#page-58-1)\n\n#### HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup/CcyCoupleGrp-Test\n\n#### Example Request\n\n```\n{\n   \"currencyCouples\": [\n      {\n         \"baseCurrency\": {\n             \"isoCode\": \"GBP\"\n         },\n         \"quoteCurrency\": {\n             \"isoCode\": \"CHF\"\n         }\n      }\n   ],\n   \"name\": \"CcyCoupleGrp-Test\"\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-57-0\"></span>**14.1.4 Get a Currency Couple Group**\n\nGet a [Currency Couple Group](#page-58-1) by name.\n\n#### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup/CcyCoupleGrp-Test\n```\n\n```\n{\n   \"currencyCouples\": [\n      {\n         \"baseCurrency\": {\n             \"isoCode\": \"AUD\"\n         },\n         \"quoteCurrency\": {\n             \"isoCode\": \"CAD\"\n         }\n      },\n      {\n         \"baseCurrency\": {\n             \"isoCode\": \"GBP\"\n         },\n         \"quoteCurrency\": {\n```\n\n```\n\"isoCode\": \"CHF\"\n         }\n      }\n   ],\n   \"id\": \"9214996\",\n   \"name\": \"CcyCoupleGrp-Test\"\n}\n```\n\n### <span id=\"page-58-0\"></span>**14.1.5 Delete a Currency Couple Group**\n\nDelete Currency Couple Group by name.\n\n### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/currencyCoupleGroup/CcyCoupleGrp-Test\n```\n\nExample Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-58-1\"></span>**14.2 Currency Couple Group Model**\n\n| Field<br>Name   | Type                                                  | Description                                                                                                                                                                                                                                                                        | Example        |\n|-----------------|-------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|\n| id              | String                                                | Unique and unmodifiable Id of Cur<br>rency Couple Group.<br>360T generates<br>this Id once a new Currency Couple<br>Group is added.                                                                                                                                                | 9214996        |\n| name            | String                                                | The name must be unique. It is better<br>to define logical names to easily recog<br>nize which currency couples this group<br>has. This field is required when adding<br>a new or updating the existing Currency<br>Couple Group. The value of this field<br>must not be modified. | G10 currencies |\n| currencyCouples | List <currency<br>Cou<br/>ple Model&gt;</currency<br> | Currency Couple Group can contain<br>list of currency couple                                                                                                                                                                                                                       |                |\n\n### <span id=\"page-58-2\"></span>**14.3 Currency Couple Model**\n\n| Field | Type | Description | Example |\n|-------|------|-------------|---------|\n| Name  |      |             |         |\n\n| baseCurrency | IsoCurrency Model               | Base currency represents currenyc1 in<br>Trade.<br>E.g EUR/USD, EUR is base<br>currency in this case   |  |\n|--------------|---------------------------------|--------------------------------------------------------------------------------------------------------|--|\n|              | quoteCurrency IsoCurrency Model | Quote currency represents currency2 in<br>Trade.<br>E.g EUR/USD, USD is quote<br>currency in this case |  |\n\n## <span id=\"page-60-0\"></span>**15 Iso Currency**\n\nSupported Iso Currencies by 360T. Only GET method can be used and it returns available currencies in 360T system.\n\n### <span id=\"page-60-1\"></span>**15.1 Iso Currency Operations**\n\n### <span id=\"page-60-2\"></span>**15.1.1 Get Available Iso Currencies**\n\nList available [Iso Currencies](#page-60-3) in 360T system.\n\n### HTTP Request\n\n```\nGET https://apigateway-int.360t.com:7060/limitapi/v2/isoCurrency\n```\n\n### Example Response\n\n```\n[\n {\n   \"isoCode\": \"***\"\n },\n {\n   \"isoCode\": \"EUR\"\n },\n {\n   \"isoCode\": \"USD\"\n },\n {\n   \"isoCode\": \"GBP\"\n },\n {\n   \"isoCode\": \"JPY\"\n }\n]\n```\n\n### <span id=\"page-60-3\"></span>**15.2 IsoCurrency Model**\n\n| Field<br>Name | Type   | Description          | Example |\n|---------------|--------|----------------------|---------|\n| isoCode       | String | ISO code of currency | EUR     |\n\n## <span id=\"page-61-0\"></span>**16 Time Period Group**\n\nTime Period Group is used to define a period of time. E.g from TODAY to 1 MONTH later. Time Period is part of a Risk Portfolio Rule to determine which trade intentions to be captured based on the effective date of the deals. For an overview please see the [screenshot](#page-92-0) that demonstrates how to define Time Period Group from Bridge Admin application.\n\n### <span id=\"page-61-1\"></span>**16.1 Time Period Group Operations**\n\n| Operation | Http<br>Method | Description                                     | resource<br>path        |\n|-----------|----------------|-------------------------------------------------|-------------------------|\n| List      | GET            | List all Time Period Groups                     | /timePeriodGroup        |\n| Find One  | GET            | List a Time Period Group by given<br>name       | /timePeriodGroup/{name} |\n| Add       | POST           | Add a Time Period Group                         | /timePeriodGroup        |\n| Update    | PUT            | Update given Time Period Group by<br>given name | /timePeriodGroup/{name} |\n| Delete    | DELETE         | Delete a Time Period Group by given<br>name     | /timePeriodGroup/{name} |\n\nThe followings are the operations which can be done on Time Period Group.\n\n### <span id=\"page-61-2\"></span>**16.1.1 Add a Time Period Group**\n\nAdd a new [Time Period Group](#page-64-0) to 360T system.\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/timePeriod\n\n### Example Request\n\n```\n[\n   {\n      \"name\": \"TimePeriodGrp-Test\",\n      \"timePeriod\": {\n         \"from\": {\n             \"longName\": \"TOMORROW\",\n             \"shortName\": \"TM\"\n         },\n         \"to\": {\n             \"longName\": \"1 WEEK\",\n             \"shortName\": \"1W\"\n         }\n      }\n   }\n```\n\n]\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-62-0\"></span>**16.1.2 List All Time Period Group**\n\nList all [Time Period Group.](#page-64-0)\n\n#### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/timePeriodGroup\n\n#### Example Response\n\n```\n[\n   {\n      \"id\": \"5754708\",\n      \"name\": \"TimePeriodGrp-Test\",\n      \"timePeriod\": {\n         \"from\": {\n             \"longName\": \"TODAY\",\n             \"shortName\": \"TD\"\n         },\n         \"to\": {\n             \"longName\": \"UNLIMITED\",\n             \"shortName\": \"UL\"\n         }\n      }\n   }\n]\n```\n\n#### <span id=\"page-62-1\"></span>**16.1.3 Update a TimePeriod Group**\n\nUpdate given [Time Period Group.](#page-64-0)\n\n#### HTTP Request\n\n```\nPUT\n   https://apigateway-int.360t.com:7060/limitapi/v2/timePeriod/TimePeriodGrp-Test\n```\n\n#### Example Request\n\n{\n\n```\n\"name\": \"TimePeriodGrp-Test\",\n\"timePeriod\": {\n   \"from\": {\n      \"longName\": \"TODAY\",\n      \"shortName\": \"TD\"\n```\n\n63\n\n```\n},\n      \"to\": {\n          \"longName\": \"UNLIMITED\",\n          \"shortName\": \"UL\"\n      }\n   }\n}\n```\n\nExample Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-63-0\"></span>**16.1.4 Get a Time Period Group**\n\nGet a [Time Period Group](#page-64-0) by given time period group Id.\n\n#### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/timePeriodGroup/TimePeriodGrp-Test\n\n#### Example Response\n\n```\n{\n   \"id\": \"5754708\",\n   \"name\": \"TimePeriodGrp-Test\",\n   \"timePeriod\": {\n      \"from\": {\n         \"longName\": \"TODAY\",\n         \"shortName\": \"TD\"\n      },\n      \"to\": {\n         \"longName\": \"UNLIMITED\",\n         \"shortName\": \"UL\"\n      }\n   }\n}\n```\n\n### <span id=\"page-63-1\"></span>**16.1.5 Delete a Time Period Group**\n\nDelete Time Period Group by given time period group Id.\n\n#### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/timePeriodGroup/TimePeriodGrp-Test\n```\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-64-0\"></span>**16.2 Time Period Group Model**\n\n| Field<br>Name | Type              | Description                                                                                                                                                                                                                                                                 | Example    |\n|---------------|-------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|\n| id            | String            | Unique and unmodifiable Id of Time<br>Period Group.<br>360T generates this<br>Id once a new Time Period Group is<br>added.                                                                                                                                                  | 5754708    |\n| name          | String            | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which time periods this group<br>has. This field is required when adding<br>a new or updating the existing Time Pe<br>riod Group. The value of this field must<br>not be modified. | TodTomSpot |\n| timePeriod    | Time Period Model | Time Period                                                                                                                                                                                                                                                                 |            |\n\n### <span id=\"page-64-1\"></span>**16.3 Time Period Model**\n\n| Field<br>Name | Type       | Description                                                                                                                                                                         | Example |\n|---------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|\n| from          | Time Model | This specifies the start time. Start time<br>and end time defines Time Period. E.g<br>Start Time:<br>TODAY, End Time:<br>1<br>MONTH. That means time from today<br>to 1 Month later |         |\n| to            | Time Model | This specifies the end time. Start time<br>and end time defines Time Period. E.g<br>Start Time:<br>TODAY, End Time:<br>1<br>MONTH. That means time from today<br>to 1 Month later   |         |\n\n## <span id=\"page-65-0\"></span>**17 Time**\n\nIt is possible to use pre-defined tenors in 360T Risk Portfolio. By using GET operation as described below, all available pre-defined tenors can be listed.\n\n### <span id=\"page-65-1\"></span>**17.1 Time Operations**\n\n### <span id=\"page-65-2\"></span>**17.1.1 List Available Times**\n\nList all available [Time Model](#page-65-3) in 360T system.\n\n### HTTP Request\n\n[\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/time\n\n### Example Response\n\n```\n{\n   \"longName\": \"TODAY\",\n   \"shortName\": \"TD\"\n},\n{\n   \"longName\": \"TOMORROW\",\n   \"shortName\": \"TM\"\n},\n{\n   \"longName\": \"SPOT\",\n   \"shortName\": \"SP\"\n},\n{\n   \"longName\": \"SPOTNEXT\",\n   \"shortName\": \"SN\"\n},\n{\n   \"longName\": \"1 WEEK\",\n   \"shortName\": \"1W\"\n},\n{\n   \"longName\": \"2 WEEKS\",\n   \"shortName\": \"2W\"\n}\n```\n\n### <span id=\"page-65-3\"></span>**17.2 Time Model**\n\n]\n\n| Field<br>Type<br>Description<br>Example<br>Name |\n|-------------------------------------------------|\n|-------------------------------------------------|\n\n| shortName | String | Short name of Time Period | TD    |\n|-----------|--------|---------------------------|-------|\n| longName  | String | Long name of Time Period  | TODAY |\n\n## <span id=\"page-67-0\"></span>**18 Dealer Group**\n\nYou can group several Dealers under one Dealer Group, then defined Dealer Group is used to create rules.\n\n### <span id=\"page-67-1\"></span>**18.1 Dealer Group Operations**\n\nThe followings are the operations which can be done on Dealer Group.\n\n| Operation  | Http<br>Method | Description                                 | resource<br>path    |\n|------------|----------------|---------------------------------------------|---------------------|\n| List       | GET            | List all dealer groups                      | /dealerGroup        |\n| Find One   | GET            | List a Dealer Group by given name           | /dealerGroup/{name} |\n| Add        | POST           | Add a Dealer Group                          | /dealerGroup        |\n| Add dealer | POST           | Add Dealer to Dealer Group by given<br>name | /dealerGroup/{name} |\n| Update     | PUT            | Update a Dealer Group by given name         | /dealerGroup/{name} |\n| Delete     | DELETE         | Delete a Dealer Group by given name         | /dealerGroup/{name} |\n\n### <span id=\"page-67-2\"></span>**18.1.1 List All Dealer Groups**\n\nList all [Dealer Group.](#page-70-0)\n\n### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup\n\n### Example Request\n\n```\n[\n   {\n      \"id\": \"123456\",\n      \"dealers\": [\n          {\n             \"id\": \"MT.Treasurer1\",\n             \"name\": \"MT.Treasurer1\"\n          }\n      ],\n      \"name\": \"DealerGrp-Test\"\n   }\n]\n```\n\n### <span id=\"page-67-3\"></span>**18.1.2 Get a Dealer Group**\n\n[Dealer Group](#page-70-0) by name.\n\n#### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test\n```\n\n#### Example Response\n\n```\n{\n   \"id\": \"123456\",\n   \"dealers\": [\n      {\n         \"id\": \"MT.Treasurer1\",\n         \"name\": \"MT.Treasurer1\"\n      }\n   ],\n   \"name\": \"DealerGrp-Test\"\n}\n```\n\n#### <span id=\"page-68-0\"></span>**18.1.3 Add a Dealer Group**\n\nAdd a new [Dealer Group](#page-70-0) to 360T system.\n\n#### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup\n\n#### Example Request\n\n```\n{\n   \"dealers\": [\n      {\n          \"name\": \"MT.Treasurer1\"\n      }\n   ],\n   \"name\": \"DealerGrp-Test\"\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-68-1\"></span>**18.1.4 Add a Dealer to Existing Dealer Group**\n\nAdd a new [Dealer](#page-71-3) to existing Dealer Group.\n\n#### HTTP Request\n\n```\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test\n```\n\n#### Example Request\n\n```\n{\n   \"dealer\": {\n      \"name\": \"MT.Treasurer1\"\n   }\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-69-0\"></span>**18.1.5 Update a Dealer Group**\n\nUpdate given [Dealer Group.](#page-70-0)\n\n#### HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test\n\n#### Example Request\n\n```\n{\n   \"dealers\": [\n      {\n          \"name\": \"MT.Treasurer1\"\n      }\n   ],\n   \"name\": \"DealerGrp-Test\"\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-69-1\"></span>**18.1.6 Delete a Dealer Group**\n\nDelete [Dealer Group](#page-70-0) by name.\n\n#### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/dealerGroup/DealerGrp-Test\n```\n\n#### Example Response\n\n```\n{\n```\n\n\"code\": 200,\n\n### 18.2. Dealer Group Model Dealer Group\n\n```\n\"message\": \"Response\"\n```\n\n}\n\n## <span id=\"page-70-0\"></span>**18.2 Dealer Group Model**\n\n| Field<br>Name | Type                            | Description                                                                                                                                                                                                                                                              | Example        |\n|---------------|---------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|\n| id            | String                          | Unique and unmodifiable Id of Dealer<br>Group.<br>360T generates this Id once a<br>new Dealer Group is added.                                                                                                                                                            | 123456         |\n| name          | String                          | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which legal entitiess this group<br>has. This field is required when adding<br>a new or updating the existing Dealer<br>Group. The value of this field must not<br>be modified. | DealerGrp-Test |\n| dealers       | List <dealer model=\"\"></dealer> | Group can contain a list of Dealers                                                                                                                                                                                                                                      |                |\n\n## <span id=\"page-71-0\"></span>**19 Dealer**\n\nDealer is an essential parameter of a risk portfolio rule which includes all dealer users plus the dealer groups that are pre-defined by client via Dealer Group operations.\n\n### <span id=\"page-71-1\"></span>**19.1 Dealer Operations**\n\n### <span id=\"page-71-2\"></span>**19.1.1 List Available Dealers**\n\nList available [Dealers](#page-71-3) of the client.\n\n### HTTP Request\n\n```\nGET https://apigateway-int.360t.com:7060/limitapi/v2/dealer\n```\n\n### Example Response\n\n```\n[\n   {\n      \"id\": \"MT.Treasurer1\",\n      \"name\": \"MT.Treasurer1\"\n   }\n]\n```\n\n### <span id=\"page-71-3\"></span>**19.2 Dealer Model**\n\n| Field<br>Name | Type   | Description                                                                  | Example       |\n|---------------|--------|------------------------------------------------------------------------------|---------------|\n| id            | String | Unique ID of Dealer in 360T system.<br>360T uses name of Dealer as unique Id | MT.Treasurer1 |\n| name          | String | Name of Dealer                                                               | MT.Treasurer1 |\n\n## <span id=\"page-72-0\"></span>**20 Counterpart Group**\n\nCounterpart refers to the 360T system name of the entities clients have permissioned trading relationship with. It is an essential parameter of a Risk Portfolio rule which determines to check the limit of based on the counterpart who client intends to trade. Counterpart group allows clients to group their counterparts so that they can define a risk portfolio rule which captures the risk exposure of multiple counterparts.\n\nPlease note that this parameter will also allow clients to map 360T system names of counterparts with their internal systems by creating counterpart group with their internal naming and add the corresponding counterpart.\n\nFor an overview please see the [screenshot](#page-93-0) that demonstrates how to define Counterpart Group from Bridge Admin application.\n\n### <span id=\"page-72-1\"></span>**20.1 Counterpart Group Operations**\n\n| Operation | Http<br>Method | Description                                              | resource<br>path          |\n|-----------|----------------|----------------------------------------------------------|---------------------------|\n| List      | GET            | List all Counterpart Groups                              | /counterpartsGroup        |\n| Find One  | GET            | List<br>a<br>Counterpart<br>Group<br>by<br>given<br>name | /counterpartsGroup/{name} |\n| Add       | POST           | Add a Counterpart Group                                  | /counterpartsGroup        |\n| Update    | PUT            | Update Counterpart Group by given<br>name                | /counterpartsGroup/{name} |\n| Delete    | DELETE         | Delete a Counterpart Group by given<br>name              | /counterpartsGroup/{name} |\n\nThe followings are the operations which can be done on Counterpart Group.\n\n### <span id=\"page-72-2\"></span>**20.1.1 Add a Counterpart Group**\n\nAdd a new [Counterpart Group](#page-75-0) to 360T system.\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup\n\n### Example Request\n\n{\n\n```\n\"name\": \"CounterpartGrp-Test\",\n\"institutions\": [\n   {\n      \"name\": \"MT.Bank3\"\n   },\n   {\n```\n\n\"name\": \"MT.Bank4\" } ] }\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-73-0\"></span>**20.1.2 List All Counterpart Groups**\n\nList all [Counterpart Group.](#page-75-0)\n\n#### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup\n\n#### Example Response\n\n```\n[\n   {\n      \"id\": \"9037612\",\n      \"institutions\": [\n          {\n             \"id\": \"MT.Bank3\",\n             \"name\": \"MT.Bank3\"\n          },\n          {\n             \"id\": \"MT.Bank4\",\n             \"name\": \"MT.Bank4\"\n          }\n      ],\n      \"name\": \"CounterpartGrp-Test\"\n   }\n]\n```\n\n#### <span id=\"page-73-1\"></span>**20.1.3 Update a Counterpart Group**\n\nThe followings can be updated with this operation.\n\n• Counterpart list of Counterpart Group (deleting or adding institution)\n\n#### HTTP Request\n\nPut\n\nhttps://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup/CounterpartGrp-Test\n\nExample Request\n\n```\n{\n   \"name\": \"CounterpartGrp-Test\",\n   \"institutions\": [\n      {\n          \"name\": \"MT.Bank3\"\n      },\n      {\n          \"name\": \"MT.Bank5\"\n      }\n   ]\n}\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-74-0\"></span>**20.1.4 Get a Counterpart Group**\n\nGet a [Counterpart Group](#page-75-0) by name.\n\n#### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup/CounterpartGrp-Test\n```\n\n#### Example Response\n\n```\n{\n   \"id\": \"9037612\",\n   \"institutions\": [\n      {\n         \"id\": \"MT.Bank3\",\n         \"name\": \"MT.Bank3\"\n      },\n      {\n         \"id\": \"MT.Bank4\",\n         \"name\": \"MT.Bank4\"\n      }\n   ],\n   \"name\": \"CounterpartGrp-Test\"\n}\n```\n\n#### <span id=\"page-74-1\"></span>**20.1.5 Delete a Counterpart Group**\n\nDelete Counterpart Group by name.\n\n#### HTTP Request\n\nDELETE\n\nhttps://apigateway-int.360t.com:7060/limitapi/v2/counterpartsGroup/CounterpartGrp-Test\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n```\n\n}\n\n### <span id=\"page-75-0\"></span>**20.2 Counterpart Group Model**\n\n| Field<br>Name | Type                                            | Description                                                                                                                                                                                                                                                                 | Example            |\n|---------------|-------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|\n| id            | String                                          | Unique and unmodifiable Id of Coun<br>terpart Group.<br>360T generates this<br>Id once a new Counterpart Group is<br>added.                                                                                                                                                 | 9037612            |\n| name          | String                                          | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which counterparts this group<br>has. This field is required when adding<br>a new or updating the existing Counter<br>part Group. The value of this field must<br>not be modified. | Counterpart Group1 |\n| institutions  | List <counterpart<br>Model&gt;</counterpart<br> | Group can contain a list of Counterpart.                                                                                                                                                                                                                                    |                    |\n\n## <span id=\"page-76-0\"></span>**21 Counterpart**\n\nCounterpart is an essential parameter of a risk portfolio rule and include all requester and/or provider companies a credit entity has trading relationship with plus the counterpart groups that are pre-defined by client via Counterpart Group operations.\n\n### <span id=\"page-76-1\"></span>**21.1 Counterpart Operations**\n\n### <span id=\"page-76-2\"></span>**21.1.1 List Available Counterparts**\n\nList available [Counterparts](#page-76-3) of the client.\n\n### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/counterpart\n\n### Example Response\n\n```\n[\n   {\n      \"id\": \"MT.Bank3\",\n      \"name\": \"MT.Bank3\"\n   }\n]\n```\n\n### <span id=\"page-76-3\"></span>**21.2 Counterpart Model**\n\n| Field<br>Name | Type   | Description                                                                                | Example   |\n|---------------|--------|--------------------------------------------------------------------------------------------|-----------|\n| id            | String | Unique ID of Counterpart in 360T sys<br>tem. 360T uses name of Counterpart as<br>unique Id | BANK.TEST |\n| name          | String | Name of counterpart entity                                                                 | BANK.TEST |\n\n## <span id=\"page-77-0\"></span>**22 Legal Entity Group**\n\nYou can group several legal entities under one Legal Entity Group, then defined Legal Entity Group is used to create rules. For an overview please see the [screenshot](#page-92-2) that demonstrates how to define Legal Entity Group from Bridge Admin application.\n\n### <span id=\"page-77-1\"></span>**22.1 Legal Entity Group Operations**\n\n| Operation | Http<br>Method | Description                                  | resource<br>path           |\n|-----------|----------------|----------------------------------------------|----------------------------|\n| List      | GET            | List all product groups                      | /legalEntitiesGroup        |\n| Find One  | GET            | List a Legal Entity Group by given<br>name   | /legalEntitiesGroup/{name} |\n| Add       | POST           | Add a Legal Entity Group                     | /legalEntitiesGroup        |\n| Update    | PUT            | Update a Legal Entity Group by given<br>name | /legalEntitiesGroup/{name} |\n| Delete    | DELETE         | Delete a Legal Entity Group by given<br>name | /legalEntitiesGroup/{name} |\n\nThe followings are the operations which can be done on Legal Entity Group.\n\n### <span id=\"page-77-2\"></span>**22.1.1 Add a Legal Entity Group**\n\nAdd a new Legal Entity Group to 360T system.\n\n### HTTP Request\n\n```\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup\n```\n\n### Example Request\n\n```\n{\n   \"institutions\": [\n      {\n         \"name\": \"360T.INT_LIMAPI.TEST\"\n      }\n   ],\n   \"name\": \"LegalEntityGrp-Test\"\n}\n```\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-78-0\"></span>**22.1.2 List All Legal Entities Groups**\n\nList all Legal Entity Group.\n\n#### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup\n\n#### Example Request\n\n```\n[\n   {\n      \"id\": \"4684994\",\n      \"institutions\": [\n         {\n             \"id\": \"360T.INT_LIMAPI.TEST\",\n             \"name\": \"360T.INT_LIMAPI.TEST\"\n         }\n      ],\n      \"name\": \"LegalEntityGrp-Test\"\n   }\n]\n```\n\n#### <span id=\"page-78-1\"></span>**22.1.3 Update a Legal Entity Group**\n\nUpdate given Legal Entity Group.\n\n#### HTTP Request\n\n```\nPUT\n   https://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test\n```\n\n#### Example Request\n\n```\n{\n   \"institutions\": [\n      {\n         \"name\": \"360T.INT_LIMAPI.TEST\"\n      }\n   ],\n   \"name\": \"LegalEntityGrp-Test\"\n}\n```\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-79-0\"></span>**22.1.4 Add a Legal Entity to Existing Legal Entity Group**\n\nAdd a new Institution to existing Legal Entity Group.\n\n#### HTTP Request\n\nPOST\n\n{\n\n}\n\nhttps://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test\n\n### Example Request\n\n\"name\": \"360T.INT\\_LIMAPI.TEST\"\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-79-1\"></span>**22.1.5 Get a Legal Entity Group**\n\nLegal Entity Group by name.\n\n### HTTP Request\n\n```\nGET\nhttps://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test\n```\n\n### Example Response\n\n```\n{\n   \"id\": \"4684994\",\n   \"institutions\": [\n      {\n         \"id\": \"360T.INT_LIMAPI.TEST\",\n         \"name\": \"360T.INT_LIMAPI.TEST\"\n      }\n   ],\n   \"name\": \"LegalEntityGrp-Test\"\n}\n```\n\n### <span id=\"page-79-2\"></span>**22.1.6 Delete a Legal Entity Group**\n\nDelete Legal Entity Group by name.\n\n### HTTP Request\n\n```\nDELETE\nhttps://apigateway-int.360t.com:7060/limitapi/v2/legalEntitiesGroup/LegalEntityGrp-Test\n```\n\n### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-80-0\"></span>**22.2 Legal Entity Group Model**\n\n| Field<br>Name | Type                                           | Description                                                                                                                                                                                                                                                                       | Example             |\n|---------------|------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|\n| id            | String                                         | Unique and unmodifiable Id of Coun<br>terpart Group.<br>360T generates this<br>Id once a new Counterpart Group is<br>added.                                                                                                                                                       | 4684994             |\n| name          | String                                         | The name must be unique. It is better<br>to define logical names to easily rec<br>ognize which legal entitiess this group<br>has. This field is required when adding<br>a new or updating the existing Legal<br>Entity Group.<br>The value of this field<br>must not be modified. | LegalEntityGrp-Test |\n| institutions  | List <legal<br>Entity<br/>Model&gt;</legal<br> | Group can contain a list of Legal Entity                                                                                                                                                                                                                                          |                     |\n\n## <span id=\"page-81-0\"></span>**23 Legal Entity**\n\nLegal entity refers to the available legal entities of the client (credit entity). Legal Entity is another parameter of risk portfolio rule which allows clients to differentiate their risk portfolios based on which of their legal entity they are trading for.\n\n### <span id=\"page-81-1\"></span>**23.1 Legal Entity Operations**\n\nThe followings are the operations which can be done on Legal Entity.\n\n| Operation | Http<br>Method | Description                                        | resource<br>path |\n|-----------|----------------|----------------------------------------------------|------------------|\n| List      | GET            | List all available Legal Entities of the<br>client | /legalEntity     |\n\n### <span id=\"page-81-2\"></span>**23.1.1 List Available Legal Entities**\n\nList available Institution in 360T system.\n\n### HTTP Request\n\n```\nGET https://apigateway-int.360t.com:7060/limitapi/v2/legalEntity\n```\n\n### Example Response\n\n```\n[\n   {\n      \"id\": \"360T.INT_LIMAPI.TEST\",\n      \"name\": \"360T.INT_LIMAPI.TEST\"\n   }\n]\n```\n\n### <span id=\"page-81-3\"></span>**23.2 Legal Entity Model**\n\n| Field<br>Name | Type   | Description                                                                                     | Example   |\n|---------------|--------|-------------------------------------------------------------------------------------------------|-----------|\n| id            | String | Unique ID of Legal Entity in 360T sys<br>tem.<br>360T uses name of Legal Entity<br>as unique Id | BANK.TEST |\n| name          | String | Name of Institution                                                                             | BANK.TEST |\n\n## <span id=\"page-82-0\"></span>**24 PFE Table**\n\nPFE Table can be used to define the PFE factors per currency pair and tenor. For an overview please see the [screenshot](#page-94-1) that demonstrates how to define PFE from Bridge Admin application.\n\n### <span id=\"page-82-1\"></span>**24.1 PFE Table Operations**\n\n**Operation Http Method Description resource path** List GET List All PFE entries /pfe Add POST Add a new currency couple to PFE Table /pfe Add Tenor POST Add a PFE tenor /pfe/tenor Update PUT Update PFE entry /pfe Delete DELETE Delete a currency couple from PFE Table /pfe?baseCurrency= {currency1} &quoteCurrency= {currency2} Delete Tenor DELETE Delete a PFE tenor /pfe/tenor/{tenor}\n\nThe followings are the operations which can be done on PFE Table.\n\n### <span id=\"page-82-2\"></span>**24.1.1 Add a Currency Couple to PFE Table**\n\nAdd a new currency to [PFE Table.](#page-88-0) \"Default\" represents default tenor. If default tenor is not specified, factor for default tenor will be 1 as default.\n\n### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/pfe\n\n### Example Request\n\n{\n\n```\n\"baseCurrency\": {\n   \"isoCode\": \"EUR\"\n},\n\"quoteCurrency\": {\n   \"isoCode\": \"GBP\"\n},\n\"factors\": [\n   {\n      \"factor\": 0.4,\n      \"tenor\": {\n         \"tenor\": \"6\"\n```\n\n```\n}\n   },\n   {\n      \"factor\": 1,\n      \"tenor\": {\n          \"tenor\": \"8\"\n      }\n   },\n   {\n      \"factor\": 1.6,\n      \"tenor\": {\n          \"tenor\": \"20\"\n      }\n   },\n   {\n      \"factor\": 3,\n      \"tenor\": {\n          \"tenor\": \"Default\"\n      }\n   }\n]\n```\n\n### Example Response\n\n}\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-83-0\"></span>**24.1.2 List All PFE Entries**\n\nList all [PFE Entries.](#page-88-0) \"\\*\\*\\*\" will represent default currency.\n\n### HTTP Request\n\nGET https://apigateway-int.360t.com:7060/limitapi/v2/pfe\n\n```\n[\n   {\n      \"baseCurrency\": {\n          \"isoCode\": \"***\"\n      },\n      \"quoteCurrency\": {\n          \"isoCode\": \"***\"\n      },\n      \"factors\": [\n          {\n             \"factor\": 1,\n             \"tenor\": {\n                \"tenor\": \"6\"\n             }\n```\n\n```\n},\n      {\n          \"factor\": 1,\n          \"tenor\": {\n             \"tenor\": \"8\"\n          }\n      },\n      {\n          \"factor\": 1,\n          \"tenor\": {\n             \"tenor\": \"20\"\n          }\n      },\n      {\n          \"factor\": 1,\n          \"tenor\": {\n             \"tenor\": \"Default\"\n          }\n      }\n   ]\n},\n{\n   \"baseCurrency\": {\n      \"isoCode\": \"CAD\"\n   },\n   \"quoteCurrency\": {\n      \"isoCode\": \"AUD\"\n   },\n   \"factors\": [\n      {\n          \"factor\": 1,\n          \"tenor\": {\n             \"tenor\": \"6\"\n          }\n      },\n      {\n          \"factor\": 1,\n          \"tenor\": {\n             \"tenor\": \"8\"\n          }\n      },\n      {\n          \"factor\": 1,\n          \"tenor\": {\n             \"tenor\": \"20\"\n          }\n      },\n      {\n          \"factor\": 1,\n          \"tenor\": {\n             \"tenor\": \"Default\"\n          }\n      }\n   ]\n}\n```\n\n]\n\n### <span id=\"page-85-0\"></span>**24.1.3 Update PFE Entry on PFE Table**\n\nUpdate given [PFE Entry.](#page-88-0)\n\n#### HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/pfe\n\n#### Example Request\n\n```\n{\n      \"baseCurrency\": {\n          \"isoCode\": \"CAD\"\n      },\n      \"quoteCurrency\": {\n          \"isoCode\": \"AUD\"\n      },\n      \"factors\": [\n          {\n             \"factor\": 0.4,\n             \"tenor\": {\n                 \"tenor\": \"6\"\n             }\n          },\n          {\n             \"factor\": 1,\n             \"tenor\": {\n                 \"tenor\": \"8\"\n             }\n          },\n          {\n             \"factor\": 1.6,\n             \"tenor\": {\n                 \"tenor\": \"20\"\n             }\n          },\n          {\n             \"factor\": 3,\n             \"tenor\": {\n                 \"tenor\": \"Default\"\n             }\n          }\n      ]\n}\n```\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-86-0\"></span>**24.1.4 Update Default PFE Entry on PFE Table**\n\nIn order to update default currency couple, \"\\*\\*\\*\" needs to be sent as isoCode.\n\n#### HTTP Request\n\nPUT https://apigateway-int.360t.com:7060/limitapi/v2/pfe\n\n#### Example Request\n\n```\n{\n      \"baseCurrency\": {\n          \"isoCode\": \"***\"\n      },\n      \"quoteCurrency\": {\n          \"isoCode\": \"***\"\n      },\n      \"factors\": [\n          {\n             \"factor\": 1,\n             \"tenor\": {\n                 \"tenor\": \"6\"\n             }\n          },\n          {\n             \"factor\": 1.5,\n             \"tenor\": {\n                 \"tenor\": \"8\"\n             }\n          },\n          {\n             \"factor\": 1.2,\n             \"tenor\": {\n                 \"tenor\": \"20\"\n             }\n          },\n          {\n             \"factor\": 2,\n             \"tenor\": {\n                 \"tenor\": \"Default\"\n             }\n          }\n      ]\n}\n```\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-87-0\"></span>**24.1.5 Delete a Currency Couple from PFE Table**\n\nDelete a currency [PFE Entry](#page-88-0) from PFE Table by id. Default configuration cannot be deleted. Therefore if you try to make a request as following \"v1/pfe?baseCurrency=\\*\\*\\*quoteCurrency=\\*\\*\\*\", you will receive an error message.\n\n#### HTTP Request\n\n```\nDELETE\n   https://apigateway-int.360t.com:7060/limitapi/v2/pfe?baseCurrency=CAD&quoteCurrency=AUD\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-87-1\"></span>**24.1.6 Add a Tenor to PFE Table**\n\nAdd a tenor to PFE Table. Default tenor cannot be added. Only Integer value is accepted.\n\n#### HTTP Request\n\nPOST https://apigateway-int.360t.com:7060/limitapi/v2/pfe/tenor\n\n#### Example Request\n\n```\n{\n \"tenor\": 4\n}\n```\n\n#### Example Response\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n#### <span id=\"page-87-2\"></span>**24.1.7 Delete a Tenor from PFE Table**\n\nDelete a tenor from PFE Table.\n\n#### HTTP Request\n\nDELETE https://apigateway-int.360t.com:7060/limitapi/v2/pfe/tenor/5\n\n```\n{\n   \"code\": 200,\n   \"message\": \"Response\"\n}\n```\n\n### <span id=\"page-88-0\"></span>**24.2 PFE Table Row Model**\n\n| Field<br>Name | Type                                             | Description                                                                          | Example |\n|---------------|--------------------------------------------------|--------------------------------------------------------------------------------------|---------|\n| baseCurrency  | IsoCurrency Model                                | Any IsoCurrency which is fetched from<br>Get Available Iso Currencies can be<br>used |         |\n|               | quoteCurrency IsoCurrency Model                  | Any IsoCurrency which is fetched from<br>Get Available Iso Currencies can be<br>used |         |\n| factors       | List <pfe col<br=\"\" table=\"\">umn Model&gt;</pfe> | List of factors refers to tenor-factor<br>couple                                     |         |\n\n### <span id=\"page-88-1\"></span>**24.3 PFE Table Column Model**\n\n| Field<br>Name | Type        | Description                                                   | Example |\n|---------------|-------------|---------------------------------------------------------------|---------|\n| tenor         | Tenor Model | Tenor value which refers to number of<br>actual calendar days |         |\n| factor        | Integer     | Factor value                                                  | 0.35    |\n\n### <span id=\"page-88-2\"></span>**24.4 Tenor Model**\n\n| Field<br>Name | Type   | Description                                                                                                                | Example      |\n|---------------|--------|----------------------------------------------------------------------------------------------------------------------------|--------------|\n| tenor         | String | Tenor value which refers to number of<br>actual calendar days or this value can<br>be \"Default\" to represent default tenor | 2 or Default |\n\n## <span id=\"page-89-0\"></span>**25 Error Handling**\n\n### <span id=\"page-89-1\"></span>**25.1 Authentication**\n\nEach client is provided with a set of private key and client certificate. These should be used when submitting requests to the 360T Limit REST API access point. Thus the requester will be authorized to use certain resources. If such a client certificate is not provided, the services will return HTTP 401.\n\n## <span id=\"page-90-0\"></span>**26 Version Log**\n\n| Version | Date       | Comments                                                                                                                                                                                                                 |\n|---------|------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 0.1     | 30.04.2019 | Initial draft.                                                                                                                                                                                                           |\n| 0.2     | 30.04.2019 | Added new status code 409.                                                                                                                                                                                               |\n| 0.2     | 19.06.2019 | Updated ConfigRule Model.<br>Updated Portfolio Model.                                                                                                                                                                    |\n| 0.3     | 28.11.2019 | Added PFE Overview to overview list.<br>Added PFE Operations.<br>Updated Bridge Admin screenshots.                                                                                                                       |\n| 0.3     | 10.12.2019 | Added \"how to update default configurations\" to PFE Operations.                                                                                                                                                          |\n| 0.3     | 23.12.2019 | Updated Time Period Group Model.<br>Updated examples under Time Period Group Operations.                                                                                                                                 |\n| 0.3     | 30.12.2019 | Updated Portfolio Model.<br>Updated examples under Portfolio Operations.<br>Updated definitions under PFE Overview.<br>Updated definitions under PFE Operations.<br>Added test environment (INT) information to Servers. |\n| 0.4     | 30.12.2019 | Added new section Active Rule.                                                                                                                                                                                           |\n| 0.4     | 28.03.2020 | Updated INT server.<br>Updated base url.                                                                                                                                                                                 |\n| 1.0     | 18.09.2020 | New version has been created.                                                                                                                                                                                            |\n| 2.0     | 19.09.2020 | New version has been created.<br>Changed unique identifier of group from id to name.<br>Added Execution Method Operations.                                                                                               |\n| 2.0     | 21.10.2020 | Added Execution Method Group Operations.                                                                                                                                                                                 |\n| 2.0.1   | 27.05.2021 | Removing the single currency group.                                                                                                                                                                                      |\n| 2.0.2   | 28.05.2021 | Updated Risk Portfolio Rule, Active Rule and REST Operations Overview sections.                                                                                                                                          |\n| 2.0.3   | 14.07.2021 | Updated JSON examples in Currency Couple Group Operations.                                                                                                                                                               |\n| 2.0.4   | 09.11.2021 | Added Exceptional Limits to Active Rule.                                                                                                                                                                                 |\n| 2.0.5   | 29.12.2021 | Added new sections: Dealer Operations, Dealer Group Operations.<br>Added Single or Group Dealer to Config Rule and Active Rule.                                                                                          |\n\nTable 26.1: Version history\n\n## <span id=\"page-91-0\"></span>**27 Appendix**\n\n### <span id=\"page-91-1\"></span>**27.1 How To Manage Risk Portfolio From Bridge Admin**\n\n<span id=\"page-91-2\"></span>**27.1.1 Product Group**\n\n![](_page_91_Picture_4.jpeg)\n\n**27.1.2 Currency Couple Group**\n\n<span id=\"page-91-3\"></span>![](_page_91_Figure_6.jpeg)\n\n### <span id=\"page-92-0\"></span>**27.1.3 Time Period Group**\n\n![](_page_92_Picture_4.jpeg)\n\n### <span id=\"page-92-1\"></span>**27.1.4 Portfolio**\n\n![](_page_92_Picture_6.jpeg)\n\n### <span id=\"page-92-2\"></span>**27.1.5 Legal Entity Group**\n\n![](_page_92_Picture_8.jpeg)\n\n### <span id=\"page-93-0\"></span>**27.1.6 Counterparts Group**\n\n![](_page_93_Picture_4.jpeg)\n\n### <span id=\"page-93-1\"></span>**27.1.7 Execution Method Group**\n\n![](_page_93_Picture_6.jpeg)\n\n### <span id=\"page-93-2\"></span>**27.1.8 Risk Portfolio Rule**\n\n![](_page_93_Picture_8.jpeg)\n\n![](_page_94_Picture_0.jpeg)\n\n### <span id=\"page-94-0\"></span>**27.1.9 Active Rule**\n\n![](_page_94_Figure_4.jpeg)\n\n### <span id=\"page-94-1\"></span>**27.1.10 PFE**\n\n![](_page_94_Picture_6.jpeg)", "metadata": {"lang": "en"}}]