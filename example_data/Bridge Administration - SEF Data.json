[{"id": "1", "text": "![](_page_0_Picture_0.jpeg)\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\n## **User Guide 360T Bridge Administration SEF Enablement**\n\nRelease 4.15 (July 2022)\n\n© 360 Treasury Systems AG, 2022 This file contains proprietary and confidential information including trade secrets and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG\n\n![](_page_1_Picture_0.jpeg)\n\n# **CONTENTS**\n\n| 1 | Introduction                                         |                                                                                          | 4                       |\n|---|------------------------------------------------------|------------------------------------------------------------------------------------------|-------------------------|\n| 2 | Getting Started                                      |                                                                                          | 4                       |\n| 3 |                                                      | Company Admin Tool                                                                       | 6                       |\n|   | 3.1                                                  | Institution Details                                                                      | 6                       |\n|   | 3.2<br>SEF                                           |                                                                                          | 6                       |\n|   | 3.2.1<br>3.2.2<br>3.2.3<br>3.2.4<br>3.3<br>Audit Log | Company SEF Details<br>Company SEF Overview<br>Trader SEF Overview<br>Trader SEF Details | 6<br>7<br>8<br>10<br>11 |\n|   | 3.4                                                  | Download and Upload                                                                      | 11                      |\n|   | 3.4.1<br>3.4.2                                       | Download<br>Upload                                                                       | 12<br>12                |\n| 4 | Contacting 360T                                      |                                                                                          | 14                      |\n\n![](_page_2_Picture_0.jpeg)\n\n# **FIGURES**\n\n| Figure 1 Header Bar5                                |  |\n|-----------------------------------------------------|--|\n| Figure 2 Bridge Administration: Homepage<br>5       |  |\n| Figure 3<br>Company Admin Control: Company Details6 |  |\n| Figure 4 SEF: Company SEF Details<br>7              |  |\n| Figure 5 SEF: Different SEF status8                 |  |\n| Figure 6 SEF: Navigating to related entity<br>8     |  |\n| Figure 7 SEF: Search for related entity8            |  |\n| Figure 8 SEF: Trader SEF Overview user status9      |  |\n| Figure 9 SEF: Navigating to user9                   |  |\n| Figure 10 SEF: Search for user9                     |  |\n| Figure 11 SEF: Adding individual SEF data10         |  |\n| Figure 12 Company Admin Control: Audit Log11        |  |\n| Figure 13<br>SEF: Company SEF Overview12            |  |\n| Figure 14 SEF: Download entities<br>12              |  |\n| Figure 15 SEF: Uploading users12                    |  |\n\n![](_page_3_Picture_0.jpeg)\n\n# <span id=\"page-3-0\"></span>**User Guide 360T SEF Enablement 1 Introduction**\n\nThis user manual describes the Swap Execution Facility (SEF) Admin features of the 360T Bridge Administration tool and the methodology of static data collection resulting of requirements in the United States by the Dodd-Frank Wall Street Reform and Consumer Protection Act.\n\nThe Bridge Administration tool is available only to the users who were defined as SEF administrators during the onboarding process. These users are then responsible for entering the information as described in the following chapters as well as to deliver any additional required documentation such as a [Schedule 4](http://training.360t.com/SEF_PA_Schedule_4.pdf) for the SEF enablement of new users. Please contact [<EMAIL>](mailto:<EMAIL>) or your customer relationship manager for setting up the administrator rights.\n\nPlease note: Institutions and users can only be enabled for the SEF by the 360T CAS team as part of the 360T SEF Onboarding process. Per default, all entities and users are OTC enabled (off SEF) for Dodd-Frank relevant products: non-MTF NDF, non-MTF NDS and non-MTF Options.\n\n# <span id=\"page-3-1\"></span>**2 Getting Started**\n\nThe Bridge Administration tool can be accessed via the menu option \"Administration\" in the screen header of the Bridge application. Several features may be visible in the left menu depending on the user permissions.\n\n![](_page_4_Picture_0.jpeg)\n\n|                               |                                                    |                                                      |                                                 | $\\vee$ Preferences<br>$\\wedge$ Administration   | $\\bullet$ AA - $\\bullet$ X<br>$\\vee$ Help                                                                  |\n|-------------------------------|----------------------------------------------------|------------------------------------------------------|-------------------------------------------------|-------------------------------------------------|------------------------------------------------------------------------------------------------------------|\n|                               | <b>Bridge Administration</b>                       |                                                      |                                                 |                                                 | $\\times$                                                                                                   |\n|                               | Sell USD<br><b>Buy USD</b>                         | sell USD<br><b>Buy USD</b>                           | Sell USD<br><b>Buy USD</b>                      | Sell EUR                                        | <b>Buy EUR</b>                                                                                             |\n|                               | 4.70110<br>4.70400                                 | 1229.430<br>1227.430                                 | 76.3288<br>76.3324                              | $5.08$ $O$ $O$ 1<br>5.08502                     |                                                                                                            |\n|                               | Spot // 19.04.2022                                 | Spot // 19.04.2022                                   | Spot // 19.04.2022                              | Spot // 20.04.2022                              |                                                                                                            |\n|                               | 4.7089000<br>4.7124000                             | 1229.43380<br>1227.42880                             | <b>RFS</b><br><b>RFS</b>                        | 5.0893450<br>5.0951930                          |                                                                                                            |\n|                               | 78,000<br>84,000<br>1 Week // 26.04.2022           | $-0.120$<br>0.380<br>1 Week // 26.04.2022            | 1 Week // 26.04.2022                            | 93.350<br>101.730<br>1 Week // 27.04.2022       |                                                                                                            |\n|                               | 4.74 29 500<br>4.7464000                           | 1229.43510<br>1227 43430                             | 76.43130<br>76.44990                            | $5.13$ O 5 530<br>5.1364710                     |                                                                                                            |\n|                               | 424.000<br>418,500                                 | 0.510<br>0.430                                       | 10.250<br>11.750                                | 505,430<br>514.510                              |                                                                                                            |\n|                               | $1$ Month // 19.05.2022<br>4.9559000<br>4.96 14500 | $1$ Month // 19.05.2022<br>1227 39 300<br>1229.43300 | $1$ Month // 19.05.2022<br>77.67630<br>77.69990 | 1 Month // 20.05.2022<br>5.4082270<br>5.4176040 |                                                                                                            |\n|                               |                                                    |                                                      |                                                 |                                                 |                                                                                                            |\n|                               | Executed (0)<br>All (0)<br>Cancelled (0)           | Rejected (0)<br>Expired (0)                          | Done (0) $Q \\psi$                               |                                                 |                                                                                                            |\n|                               | Product<br>Type<br>Reference #                     | Requester C Provider Co Requester A Notional A       | Ouote                                           |                                                 | Base Curren Ouote Curre Effective Date Effective Pe Maturity Date Maturity Pe                              |\n|                               |                                                    |                                                      |                                                 |                                                 |                                                                                                            |\n| $\\frac{1}{2}$<br>$\\circ$<br>Ð |                                                    |                                                      |                                                 |                                                 |                                                                                                            |\n|                               | SEFCLIENT.Treasurer1, SEF Client.TEST // DEMO      |                                                      | <b>EEU FI</b>                                   |                                                 | Fri, 15. Apr 2022, 17:39:17 GMT (Fri, 15. Apr 2022, 13:39 EDT) // Connected [FFM] $\\bullet$<br><b>DEMO</b> |\n\n<span id=\"page-4-0\"></span>**Figure 1 Header Bar**\n\nThe Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools and actions for a particular user.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\n<span id=\"page-4-1\"></span>![](_page_4_Picture_6.jpeg)\n\n**Figure 2 Bridge Administration: Homepage**\n\n![](_page_5_Picture_0.jpeg)\n\n# <span id=\"page-5-0\"></span>**3 Company Admin Tool**\n\nThe \"Company Admin Control\" quick link opens a navigation panel which contains an institution tree. The tree includes a list of all trade-as, trade-on-behalf, funds or I-TEX entities configured under the main entity.\n\nThe selection of the main institution is done with a single-click on the institution name in the institution tree on the left-hand side of the screen.\n\n## <span id=\"page-5-1\"></span>**3.1 Institution Details**\n\nSelecting an entity from the institution tree displays a Company Details tab and SEF tab. The Company Details tab is not editable. The values from the Company Details are exported from the Institution Category.\n\n|                                                             |                                                                                                                                |                                        |                                                                                                                       |                          | $\\vee$ Preferences $\\vee$ Administration                                            | $\\vee$ Help  | D'I                        | $\\bullet$ AA $ \\bullet$ X |\n|-------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|-----------------------------------------------------------------------------------------------------------------------|--------------------------|-------------------------------------------------------------------------------------|--------------|----------------------------|---------------------------|\n|                                                             | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                           | $+$                                    |                                                                                                                       |                          |                                                                                     |              |                            |                           |\n| 合<br>∽<br>$\\begin{array}{c} \\square \\\\ \\square \\end{array}$ | $\\rightarrow$<br>Q<br>SEF Client.TEST<br>FRICITE SEF Client TAS.TEST<br>$\\underline{\\widehat{\\mathbf{m}}}$ SEF Cliet TAS2.TEST | $SET \\equiv$<br><b>Company Details</b> | NOTE: This institution cannot be edited in this tool right now! Please use the Institution Managment tool<br>instead. |                          |                                                                                     |              |                            |                           |\n| τńγ                                                         |                                                                                                                                |                                        | Company Name*                                                                                                         | SEF Client.TEST          |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        | <b>ECN ID</b>                                                                                                         | 18320                    |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        | <b>Description</b>                                                                                                    | SEF Client.TEST          |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        | <b>Phone Number</b>                                                                                                   |                          |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        | <b>Fax Number</b>                                                                                                     |                          |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        | Country*                                                                                                              | United States of America |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        | Currency                                                                                                              | USD                      |                                                                                     | $\\checkmark$ |                            |                           |\n|                                                             |                                                                                                                                |                                        | LEI                                                                                                                   | 5299001T4DEDEH9I0252     |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        | Status*                                                                                                               | Institution active       |                                                                                     | $\\checkmark$ |                            |                           |\n|                                                             |                                                                                                                                |                                        |                                                                                                                       |                          |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        |                                                                                                                       |                          |                                                                                     |              |                            |                           |\n|                                                             |                                                                                                                                |                                        |                                                                                                                       |                          | <b>Create Change Request</b>                                                        |              | <b>Discard all Changes</b> | Save                      |\n|                                                             |                                                                                                                                | SEF Client.TEST $\\times$               |                                                                                                                       |                          |                                                                                     |              |                            |                           |\n| J.                                                          | SEFCLIENT.Treasurer1, SEF Client.TEST // DEMO                                                                                  |                                        | FEET                                                                                                                  |                          | Fri, 15. Apr 2022, 17:12:09 GMT (Fri, 15. Apr 2022, 13:12 EDT) // Connected [FFM] · |              |                            | <b>DEMO</b>               |\n\n<span id=\"page-5-4\"></span>**Figure 3 Company Admin Control: Company Details**\n\n## <span id=\"page-5-2\"></span>**3.2 SEF**\n\nClicking on the \"SEF\" tab displays an additional set of tabs: Company SEF Details, Company SEF Overview, Trader SEF Overview and Trader SEF Details.\n\n## <span id=\"page-5-3\"></span>**3.2.1 Company SEF Details**\n\nThe Company SEF Details tab allow the SEF admin to enter SEF related details for the company. The required fields are denoted with an asterisk (\\*). The Tax Id field foresees the last 4 digits of the company's Tax ID or Registry ID.\n\n![](_page_6_Picture_0.jpeg)\n\n|                                 | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                            | $^{+}$                                               |                                                                     | $\\vee$ Administration<br>$\\odot$<br>$\\vee$ Preferences<br>$\\vee$ Help<br>$AA - CD X$                       |  |\n|---------------------------------|---------------------------------------------------------------------------------|------------------------------------------------------|---------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------|--|\n| ⋒<br>⊖                          | $\\rightarrow$<br>$\\Omega$<br><b> ◇ SEF Client.TEST</b><br>> SEF Client TAS.TEST | <b>Company Details</b><br><b>Company SEF Details</b> | E<br><b>SEF</b><br><b>Company SEF Overview</b>                      | <b>Trader SEF Overview</b><br><b>Trader SEF Details</b>                                                    |  |\n| Refresh<br>$\\Box$               | <b>III</b> SEF Client TAS.TEST<br>THE SEF Cliet TAS2.TEST                       |                                                      | <b>Company Name</b><br>Tax Id *                                     | SEF Client.TEST<br>1234                                                                                    |  |\n| ú                               |                                                                                 |                                                      | Address 1*<br><b>Address 2</b>                                      | 521 Fifth Ave                                                                                              |  |\n|                                 |                                                                                 |                                                      | <b>Address 3</b><br>City *<br>State *                               | <b>NY</b><br><b>NY</b>                                                                                     |  |\n|                                 |                                                                                 |                                                      | Postal Code *<br><b>Country</b>                                     | 10175<br><b>United States of America</b>                                                                   |  |\n|                                 |                                                                                 |                                                      | <b>Phone Number*</b><br><b>Fax Number</b><br><b>Email Address *</b> | **************<br><EMAIL>                                                                             |  |\n|                                 |                                                                                 |                                                      | Start Date *<br><b>End Date</b>                                     | 15.04.2022                                                                                                 |  |\n| $\\ddot{\\Omega}$<br>$\\mathbb{C}$ |                                                                                 |                                                      | <b>SEF Enabled</b>                                                  | $\\overline{\\mathcal{L}}$<br><b>Create Change Request</b><br><b>Discard all Changes</b><br><b>Save</b>      |  |\n| O<br>$\\ominus$                  | SEFCLIENT.Treasurer1, SEF Client.TEST // DEMO                                   | SEF Client.TEST $\\times$                             |                                                                     | <b>DEMO</b><br>Tri, 15. Apr 2022, 18:21:58 GMT (Fri, 15. Apr 2022, 14:21 EDT) // Connected [FFM] $\\bullet$ |  |\n\n<span id=\"page-6-1\"></span>**Figure 4 SEF: Company SEF Details**\n\nOnce all required data has been added, the admin is required to save the settings by clicking on the Save button at the bottom right-hand side of the screen. Additionally, the SEF Admin is requested to send an email to [<EMAIL>](mailto:<EMAIL>) once this information is saved. The 360T CAS team will execute on the final enablement if all required documentation is in place. An entity will only be enabled for SEF if 360T has completed the final approval step.\n\n#### <span id=\"page-6-0\"></span>**3.2.2 Company SEF Overview**\n\nThe Company SEF Overview tab gives the SEF admin an overview of all related entities in the setup as well as their individual SEF enablement status. The SEF Enablement status is either true (enabled for SEF trading) or false (disabled for SEF trading).\n\n|                                                                         |                                                         | <b>Bemp</b> | <b>DEUTSCHE BORS</b><br><b>GROUP</b> |         |\n|-------------------------------------------------------------------------|---------------------------------------------------------|-------------|--------------------------------------|---------|\n| <b>Company Details</b><br><b>SEF</b><br><b>Users</b><br><b>Daughter</b> | – ≡<br><b>Order Management Groups</b>                   |             |                                      |         |\n| <b>Company SEF Details</b><br><b>Company SEF Overview</b>               | <b>Trader SEF Overview</b><br><b>Trader SEF Details</b> |             |                                      |         |\n|                                                                         |                                                         |             |                                      |         |\n|                                                                         | $\\Omega$                                                |             | $\\rightarrow$                        |         |\n|                                                                         | <b>Name</b>                                             |             | <b>SEF Enabled</b>                   |         |\n|                                                                         | <b>SEF Client TASTEST</b>                               |             | false                                | $\\beta$ |\n|                                                                         | <b>SEF Client.TEST</b>                                  |             | true                                 | $\\beta$ |\n|                                                                         | <b>SEF Cliet TAS2.TEST</b>                              |             | false                                | $\\beta$ |\n\n<span id=\"page-7-1\"></span>**Figure 5 SEF: Different SEF status**\n\nIt is possible to navigate directly to a related entity by clicking on the arrow icon next to the entity in question. This feature will open the entity in question, landing on the entity's Company SEF Details tab.\n\n| 三<br>SEF<br><b>Company Details</b> |                             |                            |                           |  |               |                    |    |\n|------------------------------------|-----------------------------|----------------------------|---------------------------|--|---------------|--------------------|----|\n| <b>Company SEF Details</b>         | <b>Company SEF Overview</b> | <b>Trader SEF Overview</b> | <b>Trader SEF Details</b> |  |               |                    |    |\n|                                    |                             |                            |                           |  |               |                    |    |\n|                                    |                             |                            |                           |  | $\\rightarrow$ |                    |    |\n|                                    |                             |                            | <b>Name</b>               |  |               | <b>SEF Enabled</b> |    |\n|                                    |                             |                            | <b>SEF Client TASTEST</b> |  |               | false              | a. |\n|                                    |                             |                            |                           |  |               |                    |    |\n\n#### <span id=\"page-7-2\"></span>**Figure 6 SEF: Navigating to related entity**\n\nIt is possible to search for related entities via the free text field at the top of the list of entities. To perform a search, it is required to enter the search value and either click on the arrow in the search field or the Enter key on the keyboard.\n\n| 目<br>SEF.<br><b>Company Details</b> |                             |                            |                           |            |               |                    |   |\n|-------------------------------------|-----------------------------|----------------------------|---------------------------|------------|---------------|--------------------|---|\n| <b>Company SEF Details</b>          | <b>Company SEF Overview</b> | <b>Trader SEF Overview</b> | <b>Trader SEF Details</b> |            |               |                    |   |\n|                                     |                             |                            |                           |            |               |                    |   |\n|                                     |                             |                            |                           | $Q$ client | $\\rightarrow$ |                    |   |\n|                                     |                             |                            | <b>Name</b>               |            |               | <b>SEF Enabled</b> |   |\n|                                     |                             |                            | <b>SEF Client TASTEST</b> |            |               | false              | Ŵ |\n|                                     |                             |                            |                           |            |               |                    |   |\n\n<span id=\"page-7-3\"></span>**Figure 7 SEF: Search for related entity**\n\n#### <span id=\"page-7-0\"></span>**3.2.3 Trader SEF Overview**\n\nThe Trader SEF Overview tab gives an overview of the active market facing users (user types: Treasurer, Trader, Hybrid, AutoDealer, API) and their individual SEF status.\n\n| <b>Company SEF Details</b><br><b>Company SEF Overview</b><br><b>Trader SEF Details</b><br><b>Trader SEF Overview</b><br>$\\rightarrow$<br><b>SEF Active</b><br><b>SEF Enabled</b><br>Name<br>$\\beta$<br>SEFCLIENT.Treasurer1<br>true<br>true<br>$\\beta$<br>false<br>false<br>SEFCLIENT.Treasurer2 | <b>Order Management Groups</b><br>SEF.<br><b>Company Details</b><br><b>Users</b><br><b>Daughter</b> | E. | 36C15 | DEUISCHE BORSI<br><b>GROUP</b> |  |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------|----|-------|--------------------------------|--|\n|                                                                                                                                                                                                                                                                                                  |                                                                                                     |    |       |                                |  |\n|                                                                                                                                                                                                                                                                                                  |                                                                                                     |    |       |                                |  |\n|                                                                                                                                                                                                                                                                                                  |                                                                                                     |    |       |                                |  |\n|                                                                                                                                                                                                                                                                                                  |                                                                                                     |    |       |                                |  |\n|                                                                                                                                                                                                                                                                                                  |                                                                                                     |    |       |                                |  |\n\n<span id=\"page-8-0\"></span>**Figure 8 SEF: Trader SEF Overview user status**\n\nThe view differentiates between the two statuses. The status can either be true or false.\n\n- SEF Enabled: The user is SEF-onboarded and enabled for SEF trading by 360T.\n- SEF Active: The user is activated or deactivated for SEF trading.\n\nA user must be both SEF Enabled and SEF Active in order to trade via SEF.\n\nIt is possible to navigate directly to a user by clicking on the arrow icon next to the user in question. This feature will open the user in question, landing on the user's Trader SEF Details tab.\n\n| <b>SEF</b><br><b>Company Details</b><br><b>Daughter</b><br><b>Users</b> | 日<br><b>Order Management Groups</b>                     |                                                          |\n|-------------------------------------------------------------------------|---------------------------------------------------------|----------------------------------------------------------|\n| <b>Company SEF Details</b><br><b>Company SEF Overview</b>               | <b>Trader SEF Details</b><br><b>Trader SEF Overview</b> |                                                          |\n|                                                                         |                                                         |                                                          |\n|                                                                         |                                                         | $\\rightarrow$                                            |\n|                                                                         | <b>Name</b>                                             | <b>SEF Active</b><br><b>SEF Enabled</b>                  |\n|                                                                         | SEFCLIENT.Treasurer1                                    | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br>true<br>true |\n|                                                                         | SEFCLIENT.Treasurer2                                    | $\\phi$<br>false<br>false                                 |\n\n<span id=\"page-8-1\"></span>**Figure 9 SEF: Navigating to user**\n\nIt is possible to search for users via the free text field at the top of the list of entities. In order to perform a search, it is required to enter the search value and either click on the arrow in the search field or the Enter key on the keyboard.\n\n| <b>Company Details</b><br><b>SEF</b><br><b>Users</b><br><b>Daughter</b> | <b>Order Management Groups</b> | ≡                         |                    |                   |   |\n|-------------------------------------------------------------------------|--------------------------------|---------------------------|--------------------|-------------------|---|\n| <b>Company SEF Details</b><br><b>Company SEF Overview</b>               | <b>Trader SEF Overview</b>     | <b>Trader SEF Details</b> |                    |                   |   |\n|                                                                         |                                |                           |                    |                   |   |\n|                                                                         |                                | $Q$ client                | $\\rightarrow$      |                   |   |\n|                                                                         |                                | <b>Name</b>               | <b>SEF Enabled</b> | <b>SEF Active</b> |   |\n|                                                                         |                                | SEFCLIENT.Treasurer1      | true               | true              | Þ |\n|                                                                         |                                | SEFCLIENT.Treasurer2      | false              | false             | Þ |\n\n<span id=\"page-8-2\"></span>**Figure 10 SEF: Search for user**\n\n![](_page_9_Picture_0.jpeg)\n\n### <span id=\"page-9-0\"></span>**3.2.4 Trader SEF Details**\n\nThe Trader SEF Details tab allows the admin to see, add and amend SEF related data for individual users on the 360T platform. Some values in the Trader SEF Details tab are not editable. These values (user's first name, last name, country, phone number and email address) are sourced from the Institution Category within the Bridge Administration tool. Should a change to these values be required, admins are asked to raise a Change Request from the Institution Category with this request.\n\nThe required SEF data fields that must be entered by the SEF admin are denoted with an asterisk (\\*). In order to open a specific user, admins are asked to open the Trader SEF Overview tab and navigate to the user via the arrow icon next to the user's SEF Active status column as described above.\n\n| SEF <sup>1</sup><br><b>Order Management Groups</b><br><b>Company Details</b><br><b>Users</b><br><b>Daughter</b> | Ξ                         |                             |\n|-----------------------------------------------------------------------------------------------------------------|---------------------------|-----------------------------|\n| <b>Company SEF Details</b><br><b>Company SEF Overview</b><br><b>Trader SEF Overview</b>                         | <b>Trader SEF Details</b> |                             |\n|                                                                                                                 |                           |                             |\n|                                                                                                                 | <b>User Name</b>          | SEFCLIENT.Treasurer1        |\n|                                                                                                                 | <b>SSN</b>                |                             |\n|                                                                                                                 | <b>Date of Birth</b>      |                             |\n|                                                                                                                 | First Name*               | Client                      |\n|                                                                                                                 | Last Name*                | SEF                         |\n|                                                                                                                 | Address 1 *               | 521 Fifth Ave               |\n|                                                                                                                 | <b>Address 2</b>          |                             |\n|                                                                                                                 | <b>Address 3</b>          |                             |\n|                                                                                                                 | City*                     | <b>NY</b>                   |\n|                                                                                                                 | State *                   | <b>NY</b>                   |\n|                                                                                                                 | Postal Code *             | 10175                       |\n|                                                                                                                 | <b>Country</b>            | United States of America    |\n|                                                                                                                 | <b>Phone Number*</b>      |                             |\n|                                                                                                                 | <b>Fax Number</b>         |                             |\n|                                                                                                                 | <b>Email Address *</b>    | <EMAIL>          |\n|                                                                                                                 | Start Date *              | 20.10.2015                  |\n|                                                                                                                 | <b>End Date</b>           |                             |\n|                                                                                                                 | <b>SEF Enabled</b>        | $\\checkmark$                |\n|                                                                                                                 | <b>SEF Active</b>         | $\\checkmark$                |\n|                                                                                                                 | SEF Trader Type *         | <b>NATS</b><br>$\\checkmark$ |\n|                                                                                                                 |                           |                             |\n\n<span id=\"page-9-1\"></span>**Figure 11 SEF: Adding individual SEF data**\n\nOnce all required data has been added, the admin is required to save the settings by clicking on the Save button at the bottom right-hand side of the screen. Additionally, the SEF Admin is requested to send an email to [<EMAIL>](mailto:<EMAIL>) once the information is saved. The 360T CAS team will execute on the final enablement if all required documentation is in place. A user will only be enabled for SEF if 360T has completed the final approval step.\n\n![](_page_10_Picture_0.jpeg)\n\nPlease note: In order to activate a user for SEF trading, it is required that 360T is provided with a filled in and signed [Schedule 4](http://training.360t.com/SEF_PA_Schedule_4.pdf) form for the user. The form can be sent as a scanned copy to [<EMAIL>.](mailto:<EMAIL>)\n\nFor the AutoDealer user, details must be entered for a contact within the company as required by the NFA. It is not possible to user generic information, such as a group email address.\n\n## <span id=\"page-10-0\"></span>**3.3 Audit Log**\n\nEach entity and user tab has an Audit Log which tracks all saved changes. The audit log is located in the row next to the Company Details and SEF tabs at the very upper area of the screen.\n\n![](_page_10_Picture_5.jpeg)\n\n**Figure 12 Company Admin Control: Audit Log**\n\n## <span id=\"page-10-2\"></span><span id=\"page-10-1\"></span>**3.4 Download and Upload**\n\nIt is possible to download a complete list of entered SEF data both for related entities and individual users from the Company Admin Control tool in Bridge Administration. This download CSV file can also be used as a template for uploading SEF data for other entities and users.\n\n![](_page_11_Picture_0.jpeg)\n\n|                                              |                                    | <b>RFS REQUESTER</b><br><b>BRIDGE ADMINISTRATION</b>                                                                             | $+$                                                  |                                                                                                  | $\\vee$ Preferences         | $\\vee$ Administration                                                                       | $\\vee$ Help                                  | $\\bullet$<br>⊳                              | $\\Box$<br>AA | $\\mathsf{X}$ |\n|----------------------------------------------|------------------------------------|----------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|--------------------------------------------------------------------------------------------------|----------------------------|---------------------------------------------------------------------------------------------|----------------------------------------------|---------------------------------------------|--------------|--------------|\n|                                              | 合<br>$\\mathcal{G}$<br>$\\mathbf{r}$ | Q<br>$\\rightarrow$<br>SEF Client.TEST<br>> SEF Client TAS.TEST<br><b>ID SEF Client TASTEST</b><br><b>III</b> SEF Cliet TAS2.TEST | <b>Company Details</b><br><b>Company SEF Details</b> | $SET$ $\\equiv$<br><b>Company SEF Overview</b>                                                    | <b>Trader SEF Overview</b> | <b>Trader SEF Details</b>                                                                   | $\\rightarrow$                                |                                             | $+1$         |              |\n|                                              | τ'n                                |                                                                                                                                  |                                                      | <b>Name</b><br><b>SEF Client TASTEST</b><br><b>SEF Client TEST</b><br><b>SEF Cliet TAS2 TEST</b> |                            |                                                                                             | <b>SEF Enabled</b><br>false<br>true<br>false | $\\Rightarrow$<br>$\\rightarrow$<br>$\\approx$ |              |              |\n| $\\ddot{\\omega}$<br>$\\Box$<br>$\\bigcirc$<br>Θ |                                    |                                                                                                                                  | SEF Client.TEST $\\times$                             |                                                                                                  |                            | <b>Create Change Request</b>                                                                |                                              | <b>Discard all Changes</b>                  | Save         |              |\n|                                              |                                    | SEFCLIENT.Treasurer1, SEF Client.TEST // DEMO                                                                                    |                                                      | Fest                                                                                             |                            | Fri, 15. Apr 2022, 18:38:05 GMT (Fri, 15. Apr 2022, 14:38 EDT) // Connected [FFM] $\\bullet$ |                                              |                                             |              | <b>DEMO</b>  |\n\n<span id=\"page-11-2\"></span>**Figure 13 SEF: Company SEF Overview**\n\n### <span id=\"page-11-0\"></span>**3.4.1 Download**\n\nThe download icon is found in the upper right-hand corner of the Company SEF Overview and Trader SEF Overview tabs.\n\n| <b>Company Details</b><br>Users Daughter SEF<br><b>Order Management Groups</b> $\\equiv$ |                            |                    |    | 土土 |\n|-----------------------------------------------------------------------------------------|----------------------------|--------------------|----|----|\n| Company SEF Details Company SEF Overview Trader SEF Overview Trader SEF Details         |                            |                    |    |    |\n|                                                                                         |                            |                    |    |    |\n|                                                                                         |                            |                    |    |    |\n|                                                                                         | Name                       | <b>SEF Enabled</b> |    |    |\n|                                                                                         | <b>SEF Client TASTEST</b>  | false              | ∣ఉ |    |\n|                                                                                         | <b>SEF Client TEST</b>     | true               | â  |    |\n|                                                                                         | <b>SEF Cliet TAS2 TEST</b> | false              | ∣ఉ |    |\n\n<span id=\"page-11-3\"></span>**Figure 14 SEF: Download entities**\n\n### <span id=\"page-11-1\"></span>**3.4.2 Upload**\n\nThe upload icon is found in the upper right-hand corner of the Company SEF Overview and Trader SEF Overview tabs.\n\n| Users Daughter SEF<br>Order Management Groups $\\equiv$<br><b>Company Details</b> |                           |                               |                   | $\\overline{ }$<br>رت |\n|----------------------------------------------------------------------------------|---------------------------|-------------------------------|-------------------|----------------------|\n| Company SEF Overview Trader SEF Overview<br><b>Company SEF Details</b>           | <b>Trader SEF Details</b> |                               |                   |                      |\n|                                                                                  |                           |                               |                   |                      |\n|                                                                                  | $Q$ client                |                               |                   |                      |\n|                                                                                  | Name                      | <b>SEF Enabled SEF Active</b> |                   |                      |\n|                                                                                  | SEFCLIENT.Treasurer1      | true<br>true                  | $\\leftrightarrow$ |                      |\n|                                                                                  | SEFCLIENT.Treasurer2      | false<br>false                | '≉                |                      |\n\n<span id=\"page-11-4\"></span>**Figure 15 SEF: Uploading users**\n\nThe required data for the upload is denoted with an asterisk (\\*) in the downloaded template. It is not possible to change data that is not denoted with an asterisk (\\*) in\n\n![](_page_12_Picture_0.jpeg)\n\nthe file with this upload feature. The upload can either be done via a CSV file or via the clipboard.\n\n![](_page_13_Picture_0.jpeg)\n\n## <span id=\"page-13-0\"></span>**4 Contacting 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\nGermany 360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main, Germany Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Asia Pacific South Asia**\n\nSingapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756\n\n#### **Middle East**\n\nUnited Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036, Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\nUSA 360 Trading Networks, Inc 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 Fax: ****** 776 2902\n\nIndia ThreeSixty Trading Networks (India) Pvt Ltd Suite 9, Vatika Business Centre Trade Centre, Bandra Kurla Complex, Mumbai – 400 051, India Phone: +91 22 4077 1437\n\n![](_page_14_Picture_0.jpeg)", "metadata": {"lang": "en"}}]