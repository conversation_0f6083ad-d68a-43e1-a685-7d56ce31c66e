[{"id": "1", "text": "## **INSTITUTION CONFIGURATION (BRIDGE ADMINISTRATION)**\n\n![](_page_0_Picture_1.jpeg)\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\nUSER GUIDE 360T BRIDGE ADMINISTRATION:\n\nINSTITUTION CONFIGURATION\n\n© 360 TREASURY SYSTEMS AG, 2018 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR\n\nWRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG\n\n| 1 |       | INTRODUCTION                           | 4  |\n|---|-------|----------------------------------------|----|\n| 2 |       | GETTING STARTED<br>                    | 4  |\n|   | 2.1   | COMMON ICONS                           | 5  |\n|   | 2.1.1 | Navigation Panel Icons                 | 5  |\n|   | 2.1.2 | Institution Icon<br>                   | 6  |\n|   | 2.1.3 | User Icons                             | 6  |\n|   | 2.2   | COMMON FEATURES                        | 7  |\n|   | 2.2.1 | Institution Tree<br>                   | 7  |\n|   | 2.2.2 | Live Audit Log<br>                     | 9  |\n| 3 |       | INSTITUTION CONFIGURATION              | 9  |\n|   | 3.1   | COMPANY DATA AND CONFIGURATIONS        | 9  |\n|   | 3.1.1 | Company Details                        | 9  |\n|   | 3.1.2 | Users                                  | 10 |\n|   | 3.1.3 | Daughters                              | 11 |\n|   | 3.1.4 | ITEX<br>                               | 12 |\n|   | 3.1.5 | Legal Entities                         | 13 |\n|   | 3.1.6 | Legal Entity Groups<br>                | 14 |\n|   | 3.1.7 | TAS/TOB Groups                         | 14 |\n|   | 3.1.8 | Deal Tracking Groups                   | 15 |\n|   | 3.2   | INDIVIDUAL DATA AND CONFIGURATIONS<br> | 16 |\n|   | 3.2.1 | Individual Details                     | 16 |\n|   | 3.2.2 | Individual TAS/TOB Groups              | 17 |\n|   | 3.2.3 | User Deal Tracking<br>                 | 18 |\n| 4 |       | CONTACTING 360T                        | 20 |\n\n## **TABLE OF FIGURES**\n\n| Figure 1 Header Bar                                       | 4  |\n|-----------------------------------------------------------|----|\n| Figure 2 Bridge Administration: Homepage                  | 5  |\n| Figure 3 Bridge Administration: Navigation panel icons    | 5  |\n| Figure 4 Bridge Administration: Individual user types<br> | 7  |\n| Figure 5 Bridge Administration: Institution tree<br>      | 8  |\n| Figure 6 Bridge Administration: Active task               | 8  |\n| Figure 7 Bridge Administration: Live Audit Log<br>        | 9  |\n| Figure 8 Institution: Company Details                     | 10 |\n| Figure 9 Institution: Users<br>                           | 11 |\n| Figure 10 Institution: Daughters                          | 12 |\n| Figure 11 Institution: ITEX<br>                           | 13 |\n| Figure 12 Institution: Legal Entities                     | 13 |\n| Figure 13 Institution: Legal Entity Groups<br>            | 14 |\n| Figure 14 Institution: TAS/TOB Groups<br>                 | 15 |\n| Figure 15 Institution: Deal Tracking Groups<br>           | 15 |\n| Figure 16 Institution: Deal Tracking Members              | 16 |\n| Figure 17 Institution: Individual Details<br>             | 17 |\n| Figure 18 Institution: Individual TAS/TOB Groups<br>      | 18 |\n| Figure 19 Institution: User Deal Tracking<br>             | 19 |\n\n## <span id=\"page-3-0\"></span>**1 INTRODUCTION**\n\nThis user manual describes the Institution feature of the 360T Bridge Administration tool. The Institution feature allows real-time visibility of an entity's structure on the 360T platform and provides the ability to search for and access company- and user-level data, including:\n\nUsers:\n\n- Status (active / inactive)\n- Individual details (phone number, email adddress etc.)\n- Position (front/back office)\n- Trading rights (trade-as or trade-on-behalf companies assigned to a user)\n- Deal Tracking rights (visibility of trades for each user)\n\nCompanies:\n\n- Status (active / inactive)\n- Company details (system short name, legal entity name, LEI etc.)\n- Related entities (trade-as, trade-on-behalf or ITEX etc.)\n\nAccess to entity structures is primarily available to a user logging in via a single main TEX entity with views of users and related entities. Clients with complex global structures of multiple main TEX entities may also designate a single main entity in order to create a hierarchical structure for administrative purposes. <NAME_EMAIL> or your customer relationship manager for information regarding possible administrative structures.\n\n#### **Please note:**\n\nOnly users with corresponding user rights are able to access the tool. <NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.\n\n## <span id=\"page-3-1\"></span>**2 GETTING STARTED**\n\nThe Institution feature is found within the Bridge Administration tool. Bridge Administration can be accessed either via the menu option \"Administration\" in the screen header of the Bridge application or as a standalone feature from your starter applet.\n\n![](_page_3_Picture_19.jpeg)\n\nFigure 1 Header Bar\n\n<span id=\"page-3-2\"></span>The Bridge Administration feature opens to a homepage with available shortcuts to different categories of configuration tools and actions for the particular user.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\n![](_page_4_Picture_2.jpeg)\n\nFigure 2 Bridge Administration: Homepage\n\n## <span id=\"page-4-2\"></span><span id=\"page-4-0\"></span>**2.1 Common Icons**\n\nClicking on one of the configuration quick links opens a navigation panel which contains various icons and an institution tree.\n\n### <span id=\"page-4-1\"></span>**2.1.1 Navigation Panel Icons**\n\nA set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel.\n\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          | $\\vee$ Preferences $\\vee$ Administration | $\\vee$ Help                | $A = 2 \\times$                                 |\n|------------|------------------|------------------------------------------------------------------------------------------------------|-------------------------------------|-----------------------|------------------------------------------------|------------------------------------------|--------------------------|------------------------------------------|----------------------------|------------------------------------------------|\n|            |                  | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                      | <b>BRIDGE ADMINISTRATION</b>        |                       | $^{+}$                                         |                                          |                          |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n|            | ĥ                | 이 卷<br>1 <sup>2</sup>                                                                                | <b>Company Details</b><br>Users (5) | Daughter (4)          |                                                | Legal Entities   Legal Entity Groups (2) | <b>TAS/TOB Groups</b>    | <b>Deal Tracking Groups (1)</b>          |                            | $\\text{and}\\quad \\mathbb{R} \\equiv \\mathbb{R}$ |\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n|            | $\\mathcal{G}$    | $\\Lambda \\triangleq$ TradeAsE                                                                        |                                     | Company Name *        |                                                |                                          | TradeAsE                 |                                          |                            |                                                |\n|            |                  | <sup> TradeAsE.TAS.E1</sup>                                                                          |                                     | Description           |                                                |                                          |                          |                                          |                            |                                                |\n|            | $\\mathbb{F}_{Q}$ | <sup> TradeAsE.TAS.E2</sup>                                                                          |                                     | <b>Phone Number</b>   |                                                |                                          |                          |                                          |                            |                                                |\n|            | tú               | <sup> TradeAsE.TAS.E3</sup>                                                                          |                                     | <b>Fax Number</b>     |                                                |                                          |                          |                                          |                            |                                                |\n|            |                  | TradeAsE.TradeAsE                                                                                    |                                     | Country *             |                                                |                                          | Germany                  | $\\checkmark$                             |                            |                                                |\n|            | 厚                | TradeAsE.BackOfficeA                                                                                 |                                     | Currency              |                                                | <b>EUR</b>                               |                          | $\\checkmark$                             |                            |                                                |\n|            |                  | <b>L</b> TradeAsE.TreasurerA<br>TradeAsE.TreasurerB                                                  |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n|            | $\\ddot{\\sim}$    | TradeAsE.TreasurerC                                                                                  |                                     | LEI                   |                                                |                                          |                          |                                          |                            |                                                |\n|            | భీర్షి           | FradeAsE.TreasurerD                                                                                  |                                     | Status *              |                                                |                                          | Institution active       | $\\checkmark$                             |                            |                                                |\n|            |                  |                                                                                                      |                                     | Is Test               |                                                |                                          | O Disabled               |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     | Provider Role         |                                                |                                          | O (c) Disabled           |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     | <b>Requestor Role</b> |                                                |                                          | $\\sqrt{\\bullet}$ Enabled |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     | <b>Prime Broker</b>   |                                                |                                          | O Disabled               |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     |                       | High Frequency Trader                          |                                          | <b>O</b> O Disabled      |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     | <b>Prefix</b>         |                                                |                                          |                          |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     | TradeAsE              |                                                |                                          |                          |                                          |                            |                                                |\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          | Create new prefix                        |                            |                                                |\n|            |                  |                                                                                                      |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n| ☆          |                  |                                                                                                      | Create Change Request               |                       |                                                |                                          |                          |                                          | <b>Discard All Changes</b> | Save                                           |\n| $\\cup$     |                  |                                                                                                      |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n| $\\bigcirc$ |                  |                                                                                                      | TradeAsE $\\times$                   |                       | TradeAsE.BackOfficeA ×   TradeAsE.TreasurerA × |                                          |                          |                                          |                            |                                                |\n|            |                  | TradeAsE.TreasurerA, TradeAsE // INT<br>FECT<br>Wed, 18. Jul 2018, 13:35:09 GMT // Connected [FFM] · |                                     |                       |                                                |                                          |                          |                                          |                            |                                                |\n\n<span id=\"page-4-3\"></span>Figure 3 Bridge Administration: Navigation panel icons\n\n- **Search** : A search field will open and the user can type in an alphanumeric value in order to find the desired institution or individual. The dynamic search can be limited to individuals or institutions if one of the corresponding \"Show individuals\" or \"Show institutions\" icons is additionally selected. The search field can be hidden by clicking on the search icon again.\n- **Scroll from source** : This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel. Jumping to the selected tree item (active individual or institution in the taskbar) is possible when clicking scroll from source.\n- **Show individuals** view toggle : Displays only individuals in the navigation panels. It can be activated in order to limit search results to individual users only.\n- **Show institutions** view toggle : Displays only institutions in the navigation panels. It can be activated in order to limit search results to entities only.\n\nThe navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.\n\nLists of entities and users in the institutuion tree can be expanded or collapsed.\n\n- **Collapse** : Collapses and hides the list of entities and/or users under a main entity.\n- **Expand** : Expands and reveals the list of entities and/or users under a main entity.\n\n#### <span id=\"page-5-0\"></span>**2.1.2 Institution Icon**\n\nEntities can be identified via the **Institution** icon . An institution name will be displayed next to this icon.\n\n#### <span id=\"page-5-1\"></span>**2.1.3 User Icons**\n\nIndividual user types can be identified by color:\n\n- **Treasurer** (blue)\n- **Trader** (red)\n- **Backoffice** (yellow)\n- **AutoDealer** (green)\n- **Hybrid** (half red/blue)\n- **MT API** (purple)\n- **External** (orange)\n\n|                    |                           |                                                            |                              |                  |                                          | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\Diamond$ AA $ \\Box$ $\\times$ |                                                      |  |  |\n|--------------------|---------------------------|------------------------------------------------------------|------------------------------|------------------|------------------------------------------|--------------------------------------------------------------------------------------------|------------------------------------------------------|--|--|\n|                    |                           | <b>ORDER MANAGEMENT</b><br><b>RFS REQUESTER</b>            | <b>BRIDGE ADMINISTRATION</b> | $\\color{red}{+}$ |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           | Q<br>上立                                                    |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    | 合                         |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    | $\\mathcal{G}$             |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           | $\\wedge \\hat{m}$ TradeAsE                                  |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    | <b>ADD</b>                | TradeAsE.TAS.E1<br><sup><sup>1</sup></sup> TradeAsE.TAS.E2 |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    | ú                         | <sup> TradeAsE.TAS.E3</sup>                                |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           | <b><u></u></b> TradeAsE.TradeAsE                           |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           | TradeAsE.BackOfficeA                                       |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    | $\\overline{\\overline{z}}$ | TradeAsE.TreasurerA                                        |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           | TradeAsE.TreasurerB                                        |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    | 冬                         | TradeAsE.TreasurerC                                        |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           | FradeAsE.TreasurerD                                        |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  | No Individuals/Institutions are selected |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n| $\\frac{1}{\\alpha}$ |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           |                                                            |                              |                  |                                          |                                                                                            |                                                      |  |  |\n|                    |                           | TradeAsE.TreasurerA, TradeAsE // INT                       |                              | $\\blacksquare$   |                                          |                                                                                            | Wed, 18. Jul 2018, 13:24:03 GMT // Connected [FFM] · |  |  |\n\n<span id=\"page-6-2\"></span>Figure 4 Bridge Administration: Individual user types\n\nBoth active and deactivated users are displayed in the list. Active users will appear in full color.\n\nThere are two types of deactivated users which are displayed differently:\n\n- **Inactive users** (in the case of temporary leave) will appear grey.\n- **Deleted users** will appear grey and with a strikethrough.\n\nBoth inactive and deleted users cannot log on to the 360T platform. Making a user inactive retains various configurations for that user. Deleting a user removes the user from TAS/TOB groups and viewing rights of Deal Tracking groups.\n\n### <span id=\"page-6-0\"></span>**2.2 Common Features**\n\n#### <span id=\"page-6-1\"></span>**2.2.1 Institution Tree**\n\nAn Institution Tree can be found under the navigation panel icons. Depending on the configuration chosen, the tree may contain only entities or entities and users.\n\nThe entities displayed will depend on the setup type. The tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or I-TEX entities configured under the main entity.\n\n|                                  |                                                                 |                                                                                                                                                                                                                |                              |        |                                          | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ AA $ \\Box$ X |  |  |\n|----------------------------------|-----------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|--------|------------------------------------------|--------------------------------------------------------------------------|--|--|\n|                                  |                                                                 | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                | <b>BRIDGE ADMINISTRATION</b> | $^{+}$ |                                          |                                                                          |  |  |\n|                                  |                                                                 |                                                                                                                                                                                                                |                              |        |                                          |                                                                          |  |  |\n| $\\frac{1}{\\alpha}$<br>$\\bigcirc$ | 合<br>$\\mathcal{G}$<br>气<br>ú<br>厚<br>$\\ddot{\\approx}$<br>భీర్షి | Q影<br>上血<br>$\\land \\hat{m}$ TradeAsE<br>TradeAsE.TAS.E1<br>TradeAsE.TAS.E2<br>TradeAsE.TAS.E3<br>TradeAsE.TradeAsE<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>TradeAsE.TreasurerD |                              |        | No Individuals/Institutions are selected |                                                                          |  |  |\n| $\\sim$ -                         |                                                                 |                                                                                                                                                                                                                |                              | ----   |                                          |                                                                          |  |  |\n\n<span id=\"page-7-0\"></span>Figure 5 Bridge Administration: Institution tree\n\nThe selection of the individuals or institutions is done by single-click within the institution tree which opens a new form/sheet with the available details of that user or entity. The selected item is highlighted as an active task inside the taskbar.\n\n|                                                                                                                          | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                     | $^{+}$<br><b>BRIDGE ADMINISTRATION</b>                                                                                                                                                                                               | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\Diamond$ AA $ \\Box$ $\\times$                                                                                         |\n|--------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 合<br>$\\mathcal{G}$<br>晒<br>$\\frac{1}{2}$<br>貝<br>$\\mathcal{D}_{\\mathcal{C}}$<br>$\\mathfrak{G}_{\\mathrm{Q}}^{\\mathrm{G}}$ | Q 豪<br>  1 血<br>$\\overline{\\phantom{a}}$<br>$\\wedge \\hat{m}$ TradeAsE<br>TradeAsE.TAS.E1<br>TradeAsE.TAS.E2<br><sup> TradeAsE.TAS.E3</sup><br>TradeAsE.TradeAsE<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD | <b>Individual Details</b><br><b>TAS/TOB Groups</b><br><b>User Deal Tracking</b><br>Login Name *<br>Last Name *<br>First Name *<br><b>Description</b><br>Email *<br>Phone Number *<br><b>Fax Number</b><br>Salutation *<br>Position * | $\\text{and}\\quad \\mathbb{R} \\equiv \\mathbb{R}^n$<br>$\\vee$ ). (TreasurerA<br>TradeAsE<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerA<br><EMAIL><br>$MR \\vee$<br>Tront Office |\n| $\\frac{1}{2}$<br>$\\bigcirc$                                                                                              | TradeAsE.TreasurerA, TradeAsE // INT                                                                                                                                                                                                                                                | Country *<br>Create Change Request<br>TradeAsE X   TradeAsE.BackOfficeA ><br>$\\label{thm:rad} {\\sf TradeASE}.\\textsf{Treasure} \\textsf{A} \\;\\; \\times \\;\\;$<br><b>asson</b>                                                          | Back Office<br>Germany<br>$\\checkmark$<br>* Mandatory<br><b>Discard All Changes</b><br>Save<br>Wed, 18. Jul 2018, 13:31:20 GMT // Connected [FFM] ·                                |\n\n<span id=\"page-7-1\"></span>Figure 6 Bridge Administration: Active task\n\n#### <span id=\"page-8-0\"></span>**2.2.2 Live Audit Log**\n\nEach entity or user tab has a **Live Audit Log** which tracks all unsaved changes. Individual unsaved changes can be reverted by clicking on the arrow icon. Clicking on the \"Discard all changed\" button will revert all unsaved changes.\n\nUnsaved changes are indicated on individual tasks in the taskbar with a star \\* symbol and homepage icons on the left side navigation toolbar.\n\n|                               | RFS REQUESTER $\\checkmark$<br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                               | <b>BRIDGE ADMINISTRATION</b>                                                                                                                                                   | $+$                                                                                                                                                                                            |                                                             | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ AA $\\Box$ $\\Box$ X                                                                                                                                                                                                                                                                                                                                               |\n|-------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 侖<br>⊙<br>一覧面<br>(見<br>భీర్షి | Q 涨<br>上直<br>◟<br>$\\wedge \\hat{m}$ TradeAsE<br><b>血 TradeAsE.TAS.E1</b><br>TradeAsE.TAS.E2<br><sup>m</sup> TradeAsE.TAS.E3<br>TradeAsE.TradeAsE<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD | <b>Individual Details</b><br>Login Name *<br>Last Name *<br>First Name *<br><b>Description</b><br>Email *<br><b>Phone Number</b> *<br>Fax Number<br>Salutation *<br>Position * | TAS/TOB Groups   User Deal Tracking<br>TradeAsE<br>TreasurerA<br>$\\vee$ 1<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerA<br><EMAIL><br>+49 123456<br>$MR \\vee$<br><b>O</b> Front Office | <b>Target</b><br>TradeAsE.TreasurerA                        | $\\begin{picture}(20,20) \\put(0,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1,0){10}} \\put(15,0){\\line(1$<br><b>Live Audit Log</b><br><b>Event Name</b><br><b>Individual Email</b> |\n| ☆<br>D<br>$\\bigcirc$          |                                                                                                                                                                                                                                                                     | Country *<br><b>Create Change Request</b><br>TradeAsE $\\times$   TradeAsE.BackOfficeA $\\times$                                                                                 | Back Office<br>Germany<br>TradeAsE.TreasurerA * X                                                                                                                                              | $\\vee$<br>* Mandatory<br><b>Old Value</b><br><EMAIL> | <b>New Value</b><br><EMAIL><br>Save<br><b>Discard All Changes</b>                                                                                                                                                                                                                                                                                                                                                    |\n|                               | TradeAsE.TreasurerA, TradeAsE // INT                                                                                                                                                                                                                                |                                                                                                                                                                                | <b>ascin</b>                                                                                                                                                                                   |                                                             | Wed, 18. Jul 2018, 13:41:32 GMT // Connected [FFM] ·                                                                                                                                                                                                                                                                                                                                                                         |\n\n<span id=\"page-8-4\"></span>Figure 7 Bridge Administration: Live Audit Log\n\n#### **Please note:**\n\nThe Live Audit Log will only reflect changes if a user has admin rights for editing.\n\n## <span id=\"page-8-1\"></span>**3 INSTITUTION CONFIGURATION**\n\nThe \"Institution\" quick link from the homepage opens a navigation panel and institution tree with entities and users. Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or I-TEX entities configured under the main entity.\n\n## <span id=\"page-8-2\"></span>**3.1 Company Data and Configurations**\n\n#### <span id=\"page-8-3\"></span>**3.1.1 Company Details**\n\nSelecting an entity from the institution tree displays a Company Details tab with the following possible additional data and configuration tabs: Users, Daughter, ITEX, Legal Entities, Legal Entity Groups, TAS/TOB Groups and Deal Tracking.\n\n| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                  | $+$<br><b>BRIDGE ADMINISTRATION</b>                                                                                                                                                                                        | $\\Box$ $\\times$<br>$\\vee$ Preferences $\\vee$ Administration $\\vee$ Help<br>$\\triangle$ AA $-$                                                                                                                                                                                                       |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Q ※<br>上血<br>≺                                                                                                                                                                                                                                                                                                                   | <b>Company Details</b><br>Users $(5)$ Daughter $(4)$                                                                                                                                                                       | $\\textcolor{black}{\\textcolor{black}{\\textbf{M}}} \\textcolor{black}{\\textcolor{black}{\\textbf{N}}} \\textcolor{black}{\\mathop{\\mathbb{R}}} \\textcolor{black}{\\mathop{\\mathbb{E}}}$<br><b>ITEX</b> Legal Entities Legal Entity Groups (2)<br><b>TAS/TOB Groups</b><br><b>Deal Tracking Groups (1)</b> |\n| 合<br>$\\wedge \\hat{m}$ TradeAsE<br>$\\mathcal{G}$<br><b><sup>血</sup> TradeAsE.TAS.E1</b><br><b>血 TradeAsE.TAS.E2</b><br>龜<br><sup> TradeAsE.TAS.E3</sup><br>TradeAsE.TradeAsE<br>ú<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>見<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>$\\frac{1}{2}$<br>FradeAsE.TreasurerD<br>øå | Company Name *<br>Description<br><b>Phone Number</b><br>Fax Number<br>Country *<br>Currency<br>LEI<br>Status *<br>Is Test<br><b>Provider Role</b><br><b>Requestor Role</b><br><b>Prime Broker</b><br>High Frequency Trader | TradeAsE<br>Germany<br>$\\checkmark$<br><b>EUR</b><br>$\\checkmark$<br>Institution active<br>$\\vee$<br>$\\bullet$ $\\circ$ $\\circ$ Disabled<br>O Disabled<br>$\\vee$ Enabled<br>O (C) Disabled<br><b>O</b> O Disabled                                                                                    |\n|                                                                                                                                                                                                                                                                                                                                  | Prefix<br>TradeAsE                                                                                                                                                                                                         | Create new prefix                                                                                                                                                                                                                                                                                   |\n| $\\ddot{\\Omega}$<br>$\\cup$<br>$\\bigcirc$                                                                                                                                                                                                                                                                                          | <b>Create Change Request</b><br>TradeAsE.BackOfficeA ×   TradeAsE.TreasurerA ×  <br>TradeAsE $\\times$                                                                                                                      | Save<br><b>Discard All Changes</b>                                                                                                                                                                                                                                                                  |\n| TradeAsE.TreasurerA, TradeAsE // INT                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                            | <b>ascr</b><br>Thu, 19. Jul 2018, 14:17:41 GMT // Connected [FFM] ·                                                                                                                                                                                                                                 |\n\n<span id=\"page-9-1\"></span>Figure 8 Institution: Company Details\n\nThe Company Details tab contains the following fields and information:\n\n- Company Name (360T TEX system name / Short name)\n- Description (Legal name / Long name)\n- Phone Number\n- Fax Number\n- Country\n- Currency\n- LEI (Legal Entity Identifier)\n- Status (Active / Inactive)\n- Is Test\n- Provider Role (provides prices)\n- Requestor Role (requests prices)\n- Prime Broker\n- High Frequency Trader\n- Prefix (for user IDs)\n- TAS/TOB Groups (if applicable)\n- Current ITEX Group Treasury (if applicable)\n\n#### <span id=\"page-9-0\"></span>**3.1.2 Users**\n\nThe Users tab is located next to the Company Details tab. All users created under a particular entity are listed on this tab with their user ID and status.\n\nThree status types are possible:\n\n- Active: The user may log in and use the platform.\n- Inactive: The user may not log in, but all configured settings are saved (sometimes used for temporary leave).\n- Deleted: The user may not log in and all configured settings are removed.\n\n|                                               | RFS REQUESTER                                                                                                                                                                                                                                                                                                | <b>ORDER MANAGEMENT</b><br><b>BRIDGE ADMINISTRATION</b> | $+$                                                                                                                                           |             |                                                 | $\\vee$ Preferences $\\vee$ Administration                                                                | $\\vee$ Help                                                                                                                                                                | $A = 2 \\times$                                       |\n|-----------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------|-------------|-------------------------------------------------|---------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|\n|                                               | Q 张<br>1 鱼<br>合<br>$\\wedge \\hat{m}$ TradeAsE<br>G<br>血 TradeAsE.TAS.E1<br>血 TradeAsE.TAS.E2<br><b>TO</b><br>film TradeAsE.TAS.E3<br>m TradeAsE.TradeAsE<br>the<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>厚<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>$z_{\\rm A}$<br>FradeAsE.TreasurerD<br>ಥೆ | <b>Company Details   Users (5)</b><br>自工业               | Daughter (4)<br>Users (5)<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD |             | ITEX   Legal Entities   Legal Entity Groups (2) | <b>TAS/TOB Groups</b><br><b>Status</b><br><b>Active</b><br>Active<br>Active<br>Active<br><b>Deleted</b> | Deal Tracking Groups (1)<br>$\\circledcirc$<br>$\\overline{\\mathbf{v}}$<br>而<br>$\\overline{\\mathcal{C}}$<br>$\\overline{\\mathbf{v}}$<br>面<br>$\\bigcirc$<br><b>Create User</b> | $\\Omega \\cap \\Omega$                                 |\n| $\\ddot{\\Omega}$<br>$\\mathbb{C}$<br>$\\bigcirc$ |                                                                                                                                                                                                                                                                                                              | <b>Create Change Request</b><br>TradeAsE $\\times$       | TradeAsE.BackOfficeA $\\times$   TradeAsE.TreasurerA $\\times$                                                                                  |             |                                                 |                                                                                                         | <b>Discard All Changes</b>                                                                                                                                                 | Save                                                 |\n|                                               | 1/ TradeAsE.TreasurerA, TradeAsE // INT                                                                                                                                                                                                                                                                      |                                                         |                                                                                                                                               | <b>Beck</b> |                                                 |                                                                                                         |                                                                                                                                                                            | Mon, 13. Aug 2018, 12:52:05 GMT // Connected [FFM] · |\n\n<span id=\"page-10-1\"></span>Figure 9 Institution: Users\n\nThe displayed list of users can be extracted and downloaded in CSV format using the download icon .\n\nThe CSV file contains the user's Salutation, User ID, Last Name, First Name, Email Address, Access Rights (front/back office), Country, Telephone Number and Status (Active/Inactive/Deteled).\n\n#### <span id=\"page-10-0\"></span>**3.1.3 Daughters**\n\nDepending on the setup, the Daughter tab may or may not be visible.\n\nA single TEX entity setup will not have Daughters. Trade-as, Trade-on-behalf, ITEX and complex administrative setups will see a list of entities related to the main TEX entity.\n\nAn entity listed as a \"Daughter\" can be administrated by an admin user at the main TEX entity.\n\nA trading relationship (trade-as, trade-on-behalf or ITEX) may be assigned to a Daughter, but is not necessary.\n\n|                                 | RFS REQUESTER V                                                                                                             | <b>ORDER MANAGEMENT</b>                                        | <b>BRIDGE ADMINISTRATION</b>                          | $+$                                                              |                                          |                       | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\ \\wedge A$ AA $-$ 0 X |                                                |\n|---------------------------------|-----------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------|-------------------------------------------------------|------------------------------------------------------------------|------------------------------------------|-----------------------|------------------------------------------------------------------------------|------------------------------------------------|\n| <mark>⋒</mark><br>$\\mathcal{G}$ | Q ※<br>1 鱼<br>$\\wedge \\hat{m}$ TradeAsE<br><sup> TradeAsE.TAS.E1</sup>                                                      | <b>Company Details</b><br>$\\overline{\\left( \\right. }%$<br>自工区 | Daughter (4)<br>Users (5)<br>Daughters (4)            | <b>ITEX</b>                                                      | Legal Entities   Legal Entity Groups (2) | <b>TAS/TOB Groups</b> | Deal Tracking Groups (1)                                                     | $\\text{and}\\quad \\mathbb{R} \\equiv \\mathbb{R}$ |\n| $\\mathbb{F}_{Q}^{n}$<br>túp     | TradeAsE.TAS.E2<br><sup> TradeAsE.TAS.E3</sup><br><b>盒 TradeAsE.TradeAsE</b><br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA |                                                                | TradeAsE.TAS.E1<br>TradeAsE.TAS.E2<br>TradeAsE.TAS.E3 | TradeAsE.TradeAsE                                                |                                          |                       | Û<br>û.<br>Û<br>Û.                                                           |                                                |\n| 見<br>$z_{\\rm A}$<br>ঞ্চী        | TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD                                                           |                                                                |                                                       |                                                                  | <b>Create Institution</b>                |                       | dd / Remove External Institutions $\\vee$                                     |                                                |\n|                                 |                                                                                                                             |                                                                |                                                       |                                                                  |                                          |                       |                                                                              |                                                |\n|                                 |                                                                                                                             |                                                                |                                                       |                                                                  |                                          |                       |                                                                              |                                                |\n|                                 |                                                                                                                             |                                                                |                                                       |                                                                  |                                          |                       |                                                                              |                                                |\n| ₿<br>Ù                          |                                                                                                                             | <b>Create Change Request</b>                                   |                                                       |                                                                  |                                          |                       | <b>Discard All Changes</b>                                                   | Save                                           |\n| $\\bigcirc$                      | 1/ TradeAsE.TreasurerA, TradeAsE // INT                                                                                     | TradeAsE $\\times$                                              |                                                       | TradeAsE.BackOfficeA ×   TradeAsE.TreasurerA ×  <br><b>oscon</b> |                                          |                       | Mon, 13. Aug 2018, 13:07:48 GMT // Connected [FFM] ·                         |                                                |\n\n<span id=\"page-11-1\"></span>Figure 10 Institution: Daughters\n\nThe displayed list of entities can be extracted and downloaded in CSV format using the download icon .\n\n#### <span id=\"page-11-0\"></span>**3.1.4 ITEX**\n\nDepending on the setup, the ITEX tab may or may not be visible.\n\nRelated entities which can be configured as ITEX will be visible in the list of Available entities. Entities configured as ITEX will be listed in the Selected area.\n\n| 1 鱼<br>Daughter (5)   ITEX   Legal Entities<br>Legal Entity Groups (2)   TAS/TOB Groups  <br>Users (5)<br>Q 嶽<br><b>Company Details</b><br>≺<br>合<br>$\\wedge \\hat{m}$ TradeAsE<br>∽<br>film TradeAsE.ITEX.E1<br>Selected (1)<br>Available (4)<br><sup> TradeAsE.TAS.E1</sup><br>TradeAsE.ITEX.E1<br>TradeAsE.TAS.E1<br>$(\\Sigma$<br>4<br><sup> TradeAsE.TAS.E2</sup><br>TradeAsE TAS.E2<br><sup> TradeAsE.TAS.E3</sup><br>tip<br>$\\infty$<br>TradeAsE.TAS.E3<br>TradeAsE.TradeAsE<br>TradeAsE.TradeAsE<br>TradeAsE.BackOfficeA<br>$(\\Sigma$<br>見<br>TradeAsE.TreasurerA<br>(R)<br>TradeAsE.TreasurerB<br>$\\mathbf{z}$<br><b>L</b> TradeAsE.TreasurerC<br>థిర్మ<br>FradeAsE.TreasurerD<br>Add / Remove External Institutions $\\sqrt{}$ | <b>ORDER MANAGEMENT</b> | <b>NEW VIEW</b> | <b>BRIDGE ADMINISTRATION</b> | $\\vee$ Preferences | $\\vee$ Help<br>$\\vee$ Administration | $A$ AA $-$ CO $\\times$                    |\n|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|-----------------|------------------------------|--------------------|--------------------------------------|-------------------------------------------|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | <b>RFS REQUESTER</b>    |                 |                              | $^{+}$             |                                      | Deal Track $\\gg$ $\\land$ $\\land$ $\\equiv$ |\n| ☆<br><b>Discard All Changes</b><br>Save<br><b>Create Change Request</b><br>Ù<br>$\\circ$<br>TradeAsE $\\times$<br>TradeAsE.TreasurerA, TradeAsE // INT<br><b>acun</b><br>Fri, 24. Aug 2018, 11:52:42 GMT // Connected [FFM] ·                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |                         |                 |                              |                    |                                      |                                           |\n\n<span id=\"page-12-1\"></span>Figure 11 Institution: ITEX\n\n#### <span id=\"page-12-0\"></span>**3.1.5 Legal Entities**\n\nDepending on the setup, the Legal Entities tab may or may not be visible.\n\nRelated entities which can be configured as TAS/TOB entities will be visible in the list of Available entities. Entities configured as TAS/TOB will be listed in the Selected area.\n\n|                                                                                                                                  | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                               | <b>BRIDGE ADMINISTRATION</b> | $^{+}$                                                                                  |                                                  | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ AA $\\Box$ $\\Box$ X                                                                                    |                                                      |\n|----------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|-----------------------------------------------------------------------------------------|--------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------|\n| 合                                                                                                                                | Q ※<br><u>  1 全</u>                                                                                                                                                                                                                                           | Company Details              | <b>ITEX</b> Legal Entities Legal Entity Groups (2)<br>Users $(5)$ Daughter $(4)$        |                                                  | TAS/TOB Groups<br>Deal Tracking Groups (1)                                                                                                                        | $\\text{and}\\quad \\mathbb{R} \\equiv \\mathbb{R}$       |\n| ∽<br>LO<br>Ŵ<br>厚<br>$\\mathcal{L}_{\\mathcal{N}}$<br>$\\mathfrak{G}_{\\mathbb{Q}}^{\\mathfrak{G}}$<br>$\\ddot{\\Omega}$<br>$\\mathbb C$ | $\\wedge \\hat{m}$ TradeAsE<br><sup> TradeAsE.TAS.E1</sup><br>TradeAsE.TAS.E2<br><sup>1</sup> TradeAsE.TAS.E3<br><b>盒 TradeAsE.TradeAsE</b><br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD | Create Change Request        | Available (0)                                                                           | $\\odot$<br>⊙<br>$\\circledcirc$<br>$^{\\circledR}$ | Selected (4)<br>TradeAsE.TAS.E1<br>TradeAsE.TAS.E2<br>TradeAsE.TAS.E3<br>TradeAsE.TradeAsE<br>Add / Remove External Institutions $\\sqrt{}$<br>Discard All Changes | Save                                                 |\n| Q                                                                                                                                | TradeAsE.TreasurerA, TradeAsE // INT                                                                                                                                                                                                                          | TradeAsE $\\times$            | TradeAsE.BackOfficeA ×   TradeAsE.TreasurerA ×  <br>$\\equiv$ $\\equiv$ $\\equiv$ $\\equiv$ |                                                  |                                                                                                                                                                   | Mon, 13. Aug 2018, 13:30:14 GMT // Connected [FFM] · |\n\n<span id=\"page-12-2\"></span>Figure 12 Institution: Legal Entities\n\n#### <span id=\"page-13-0\"></span>**3.1.6 Legal Entity Groups**\n\nCompanies selected as Legal Entities may be added to various Legal Entity Groups. These groups can later be used to assign different user trading rights (trade-as or trade-on-behalf).\n\nIt is possible to create multiple Legal Entity Groups with different entities in each group.\n\nEntities shown in the Selected list are included in the group. Entities shown as Available are not included in the group.\n\n![](_page_13_Picture_6.jpeg)\n\nFigure 13 Institution: Legal Entity Groups\n\n#### <span id=\"page-13-2\"></span><span id=\"page-13-1\"></span>**3.1.7 TAS/TOB Groups**\n\nUser trading rights for various Legal Entity Groups are visible on the TAS/TOB Groups tab.\n\nTAS users are users configured with trade-as rights. TOB users are users configured with trade-on-behalf rights. A single user may only have trade-as or trade-on-behalf rights.\n\nUsers who are not configured for trade-as or trade-on-behalf rights will not be visible. These could include back office users or users who only trade in the name of the main TEX entity.\n\nTo view the users assigned to a particular Legal Entity Group click on the name of the group displayed underneath the data tabs. The selected Legal Entity Group will be underlined.\n\n|                                    | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                               | <b>BRIDGE ADMINISTRATION</b>                            | $+$                                                                    |                                                                           | $\\vee$ Preferences $\\vee$ Administration<br>$\\vee$ Help                              | AA<br>$a \\times$<br>$\\Delta$                         |\n|------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------|------------------------------------------------------------------------|---------------------------------------------------------------------------|--------------------------------------------------------------------------------------|------------------------------------------------------|\n| ⋒<br>$\\mathcal{G}$<br>4            | Q ※<br><b>1</b> 盒<br>$\\wedge \\hat{m}$ TradeAsE<br><sup> TradeAsE.TAS.E1</sup><br><sup> TradeAsE.TAS.E2</sup>                                                                  | <b>Company Details</b><br>TradeAsE.TAS<br>TradeAsE.TAS2 | Users $(5)$ Daughter $(4)$<br><b>ITEX</b><br><b>TAS Users</b>          |                                                                           | Legal Entities   Legal Entity Groups (2)   TAS/TOB Groups   Deal Tracking Groups (1) | $\\text{and}\\quad \\mathbb{R} \\equiv \\mathbb{R}^n$     |\n| ú<br>見<br>$\\mathbb{Z}^k$<br>థిర్మి | <sup> TradeAsE.TAS.E3</sup><br><b>盒 TradeAsE.TradeAsE</b><br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD |                                                         | Available<br>FradeAsE.TreasurerD                                       | ( )<br>$(\\zeta)$<br>$(\\Sigma$<br>(K                                       | <b>Selected</b><br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC |                                                      |\n|                                    |                                                                                                                                                                               |                                                         | <b>TOB Users</b>                                                       |                                                                           |                                                                                      |                                                      |\n|                                    |                                                                                                                                                                               |                                                         | <b>Available</b>                                                       | $\\odot$<br>$\\odot$<br>$\\circledS$<br>$\\left(\\overline{\\mathbb{R}}\\right)$ | <b>Selected</b>                                                                      |                                                      |\n| $\\frac{Q}{D}$<br>۰                 |                                                                                                                                                                               | Create Change Request                                   |                                                                        |                                                                           | <b>Discard All Changes</b>                                                           | Save                                                 |\n| O                                  | TradeAsE.TreasurerA, TradeAsE // INT                                                                                                                                          | TradeAsE $\\times$                                       | TradeAsE.BackOfficeA $\\times$   TradeAsE.TreasurerA $\\times$  <br>FECT |                                                                           |                                                                                      | Mon, 13. Aug 2018, 13:44:11 GMT // Connected [FFM] ● |\n\n<span id=\"page-14-1\"></span>Figure 14 Institution: TAS/TOB Groups\n\nUsers shown in the Selected list are allowed to trade for the indicated group of Legal Entities. Users shown as Available are not allowed to trade for the indicated group of Legal Entities.\n\n#### <span id=\"page-14-0\"></span>**3.1.8 Deal Tracking Groups**\n\nDeal Tracking Groups are used to allow or restrict visibility to a particular user's or group of users' executed trades.\n\n|   |                   | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>   |                        | $^{+}$<br><b>BRIDGE ADMINISTRATION</b>                                                               |                | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ AA $\\Box$ $\\Box$ X |                                 |                                                      |\n|---|-------------------|---------------------------------------------------|------------------------|------------------------------------------------------------------------------------------------------|----------------|--------------------------------------------------------------------------------|---------------------------------|------------------------------------------------------|\n|   | 合                 | $\\alpha$<br>磤<br>上血<br>$\\land \\text{ m}$ TradeAsE | <b>Company Details</b> | Daughter (4) ITEX Legal Entities Legal Entity Groups (2)<br>Users (5)<br><b>Deal Tracking Groups</b> |                | <b>TAS/TOB Groups</b>                                                          | <b>Deal Tracking Groups (1)</b> | のの言                                                  |\n|   | €                 | <b>Im</b> TradeAsE.TAS.E1                         |                        | <b>题</b> TradeAsE.Deals                                                                              |                |                                                                                | aI面                             |                                                      |\n|   | 龜                 | TradeAsE.TAS.E2<br><sup>m</sup> TradeAsE.TAS.E3   |                        |                                                                                                      |                |                                                                                | Create Groups                   |                                                      |\n|   | up                | TradeAsE.TradeAsE                                 |                        | <b>Members</b>                                                                                       |                |                                                                                |                                 |                                                      |\n|   |                   | TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA       |                        | Available                                                                                            |                | <b>Selected</b>                                                                |                                 |                                                      |\n|   | 厚                 | TradeAsE.TreasurerB                               |                        | TradeAsE.BackOfficeA                                                                                 | $\\odot$        | TradeAsE.TreasurerA                                                            |                                 |                                                      |\n|   |                   | TradeAsE.TreasurerC                               |                        |                                                                                                      |                | TradeAsE.TreasurerB                                                            |                                 |                                                      |\n|   | $\\mathbb{Z}_\\chi$ | TradeAsE.TreasurerD                               |                        |                                                                                                      | $\\circledcirc$ | TradeAsE.TreasurerC<br>FradeAsE.TreasurerD                                     |                                 |                                                      |\n|   | ಥೆ                |                                                   |                        |                                                                                                      | $\\circledcirc$ |                                                                                |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      |                |                                                                                |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      | $^{\\circledR}$ |                                                                                |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      |                |                                                                                |                                 |                                                      |\n|   |                   |                                                   |                        | <b>Viewers</b>                                                                                       |                |                                                                                |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      |                |                                                                                |                                 |                                                      |\n|   |                   |                                                   |                        | Available                                                                                            |                | <b>Selected</b>                                                                |                                 |                                                      |\n|   |                   |                                                   |                        | FradeAsE.TreasurerD                                                                                  | $\\odot$        | TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA                                    |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      | $\\circledcirc$ | TradeAsE.TreasurerB                                                            |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      |                | TradeAsE.TreasurerC                                                            |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      | $\\circledcirc$ |                                                                                |                                 |                                                      |\n|   |                   |                                                   |                        |                                                                                                      |                |                                                                                |                                 |                                                      |\n| ☆ |                   |                                                   | Create Change Request  |                                                                                                      |                |                                                                                | <b>Discard All Changes</b>      | save                                                 |\n| Ù |                   |                                                   |                        |                                                                                                      |                |                                                                                |                                 |                                                      |\n| O |                   |                                                   | TradeAsE $\\times$      | TradeAsE.BackOfficeA X   TradeAsE.TreasurerA X                                                       |                |                                                                                |                                 |                                                      |\n|   |                   | TradeAsE.TreasurerA, TradeAsE // INT              |                        | <b>SECT</b>                                                                                          |                |                                                                                |                                 | Mon, 13. Aug 2018, 14:02:41 GMT // Connected [FFM] . |\n\n<span id=\"page-14-2\"></span>Figure 15 Institution: Deal Tracking Groups\n\nMembers of a Deal Tracking Group are visible in the Selected list.\n\nViewers selected for a particular Deal Tracking Group can view the Members' executed trades.\n\nIf a user is not added as a Viewer to any Deal Tracking Group this user will only see his/her own trades. The user would not see any other user's trades.\n\nDeleted or inactive users will appear in Deal Tracking Groups so that historical trades executed by these users can be queried.\n\n![](_page_15_Picture_6.jpeg)\n\nFigure 16 Institution: Deal Tracking Members\n\n<span id=\"page-15-2\"></span>It is possible to create more than one Deal Tracking Group to separate various groups of users which may be needed for regulatory or compliance reasons.\n\nTo view the users assigned as Members or Viewers of a particular Deal Tracking Group click on the name of the Deal Tracking Group displayed underneath the data tabs. The selected Deal Tracking Group will be highlighted.\n\n### <span id=\"page-15-0\"></span>**3.2 Individual Data and Configurations**\n\n#### <span id=\"page-15-1\"></span>**3.2.1 Individual Details**\n\nSelecting an individual from the institution tree displays an Individual Details tab with the following possible additional data and configuration tabs: TAS/TOB Groups and User Deal Tracking.\n\n| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                                                            | <b>NEW VIEW</b><br><b>BRIDGE ADMINISTRATION</b>                                                                                                                                                                                                                                                     | $\\triangle$ Aa $-$<br>$Q \\times$<br>$\\vee$ Preferences<br>$\\vee$ Administration<br>$\\vee$ Help<br>$\\pm$                                                                                                                                                                       |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| Q <del>\\</del><br><b>1</b> 鱼<br>$\\overline{\\left( \\right. }%$<br><mark>⋒</mark><br>$\\wedge \\hat{m}$ TradeAsE<br>G<br>TradeAsE.TAS.E1<br>TradeAsE.TAS.E2<br><b>ID</b><br>film TradeAsE.TAS.E3<br>TradeAsE.TradeAsE<br>túp<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>厚<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD<br>భీర్షి<br>☆<br>Ù<br>O | <b>Individual Details</b><br><b>TAS/TOB Groups</b><br><b>User Deal Tracking</b><br>Login Name *<br>Last Name *<br>First Name *<br><b>Description</b><br>Email *<br>Phone Number *<br>Fax Number<br>Salutation *<br>Position *<br>Country *<br><b>Create Change Request</b><br>TradeAsE.TreasurerA X | $\\Omega \\cap \\Omega$<br>$\\vee$ ). TreasurerA<br>TradeAsE<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerA<br><EMAIL><br>+49 123456<br>$MR \\vee$<br>(a) Front Office<br><b>Back Office</b><br>Germany<br>$\\checkmark$<br>* Mandatory<br><b>Discard All Changes</b><br>Save |\n| 1/ TradeAsE.TreasurerA, TradeAsE // INT                                                                                                                                                                                                                                                                                                                                    | <b>EECH</b>                                                                                                                                                                                                                                                                                         | Wed, 22. Aug 2018, 07:51:16 GMT // Connected [FFM] ·                                                                                                                                                                                                                          |\n\n<span id=\"page-16-1\"></span>Figure 17 Institution: Individual Details\n\nThe Individual Details tab contains the following fields and information for TEX users:\n\n- Login Name (PREFIX.Lastname)\n- Last Name\n- First Name\n- Description (free text field)\n- Email\n- Phone Number\n- Fax Number\n- Salutation\n- Position (Front Office with trading rights; Back Office with viewing or admin rights)\n- Country\n\nMandatory fields are indicated with a star \\*. Optional fields appear without a star \\*.\n\n### <span id=\"page-16-0\"></span>**3.2.2 Individual TAS/TOB Groups**\n\nDepending on the setup, the TAS/TOB Groups tab may or may not be visible.\n\nUser trading rights for various Legal Entity Groups are visible on the TAS/TOB Groups tab. The trading type of a user is displayed on this tab and can be configured as only one of the following options:\n\n- **Plain TEX user** (may only trade in the name of a main TEX entity)\n- **Trade-as User** (trade-as rights for other Legal entities)\n- **Trade-on-behalf** User (trade-on-behalf rights of other Legal entities)\n\n|                   |                    | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                            |                                            | <b>NEW VIEW</b>                              | <b>BRIDGE ADMINISTRATION</b> | $\\vee$ Preferences<br>$+$                           | $\\vee$ Administration | $\\vee$ Help                                          | $A = \\Box \\times$ |\n|-------------------|--------------------|--------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------|----------------------------------------------|------------------------------|-----------------------------------------------------|-----------------------|------------------------------------------------------|-------------------|\n|                   | m                  | 1 血<br>發<br>Q<br>K<br><b>血 TradeAsE</b><br>Administration Start<br>TradeAsE.TAS.E1<br>TradeAsE.TAS.E2                                      | <b>Individual Details</b>                  | <b>TAS/TOB Groups</b><br><b>Trading Type</b> | <b>User Deal Tracking</b>    | <b>Trade-as User</b>                                |                       | $\\checkmark$                                         | つつ 目              |\n|                   | 龜                  | TradeAsE.TAS.E3                                                                                                                            |                                            |                                              | Available                    |                                                     |                       | Selected                                             |                   |\n|                   | ú<br>厚<br>Ŗ.<br>øå | film TradeAsE.TradeAsE<br>TradeAsE.BackOfficeA<br>TradeAsE.TreasurerA<br>TradeAsE.TreasurerB<br>TradeAsE.TreasurerC<br>FradeAsE.TreasurerD |                                            | TradeAsE.TAS2                                |                              | ( ><br>$\\circledS$<br>$\\circledcirc$<br>$\\circledR$ | TradeAsE.TAS          |                                                      |                   |\n| ☆<br>$\\mathbb{C}$ |                    |                                                                                                                                            | Create Change Request<br>TradeAsE $\\times$ | TradeAsE.BackOfficeA ×                       | TradeAsE.TreasurerA X        |                                                     |                       | <b>Discard All Changes</b>                           | Save              |\n| $\\circ$           |                    |                                                                                                                                            |                                            |                                              |                              |                                                     |                       |                                                      |                   |\n|                   |                    | TradeAsE.TreasurerA, TradeAsE // INT                                                                                                       |                                            |                                              | <b>Sech</b>                  |                                                     |                       | Wed, 22. Aug 2018, 12:49:33 GMT // Connected [FFM] @ |                   |\n\n<span id=\"page-17-1\"></span>Figure 18 Institution: Individual TAS/TOB Groups\n\nA list of possible Legal Entity Groups will be visible in the Available or Selected windows.\n\nThe user may trade-as or on-behalf of any entity in the selected Legal Entity Group(s).\n\n#### <span id=\"page-17-0\"></span>**3.2.3 User Deal Tracking**\n\nDeal Tracking Groups are used to allow or restrict visibility to a particular user's or group of users' executed trades.\n\nUsers may be members or viewers of multiple groups. The User Deal Tracking tab allows oversight of a particular user's Membership and Viewing rights for all Deal Tracking Groups.\n\n|         |        |                                                                               |                           |                              |                              | $\\vee$ Preferences                 | $\\vee$ Administration | $\\vee$ Help                                          | $\\triangle$ AA $-$ CD $\\times$  |\n|---------|--------|-------------------------------------------------------------------------------|---------------------------|------------------------------|------------------------------|------------------------------------|-----------------------|------------------------------------------------------|---------------------------------|\n|         |        | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                               |                           | <b>NEW VIEW</b>              | <b>BRIDGE ADMINISTRATION</b> | $^{+}$                             |                       |                                                      |                                 |\n|         | 合      | 1 鱼<br>Q 撥<br>$\\wedge \\hat{m}$ TradeAsE                                       | <b>Individual Details</b> | <b>TAS/TOB Groups</b>        | <b>User Deal Tracking</b>    |                                    |                       |                                                      | $\\sqrt{2}$ $\\sqrt{2}$ $\\approx$ |\n|         | G      | <sup><sup>1</sup></sup> TradeAsE.TAS.E1                                       |                           | <b>Deal Tracking Members</b> |                              |                                    |                       |                                                      |                                 |\n|         | 1      | <sup> TradeAsE.TAS.E2</sup>                                                   |                           |                              | Available                    |                                    |                       | <b>Selected</b>                                      |                                 |\n|         | ú      | <sup> TradeAsE.TAS.E3</sup><br>film TradeAsE.TradeAsE<br>TradeAsE.BackOfficeA |                           |                              |                              | $(\\Sigma)$<br>$\\left(\\zeta\\right)$ | <b>TradeAsE.Deals</b> |                                                      |                                 |\n|         |        | TradeAsE.TreasurerA                                                           |                           |                              |                              |                                    |                       |                                                      |                                 |\n|         | 厚      | TradeAsE TreasurerB<br>TradeAsE.TreasurerC                                    |                           |                              |                              | $(\\gg)$                            |                       |                                                      |                                 |\n|         | Ŗ.     | FradeAsE.TreasurerD                                                           |                           |                              |                              | $(\\overline{\\mathbb{R}})$          |                       |                                                      |                                 |\n|         | థిర్మి |                                                                               |                           |                              |                              |                                    |                       |                                                      |                                 |\n|         |        |                                                                               |                           |                              |                              |                                    |                       |                                                      |                                 |\n|         |        |                                                                               |                           | <b>Deal Tracking Viewers</b> |                              |                                    |                       |                                                      |                                 |\n|         |        |                                                                               |                           |                              | Available                    |                                    |                       | <b>Selected</b>                                      |                                 |\n|         |        |                                                                               |                           |                              |                              | $($ >                              | <b>TradeAsE.Deals</b> |                                                      |                                 |\n|         |        |                                                                               |                           |                              |                              | $(\\left\\langle \\right\\rangle )$    |                       |                                                      |                                 |\n|         |        |                                                                               |                           |                              |                              |                                    |                       |                                                      |                                 |\n|         |        |                                                                               |                           |                              |                              | $( \\otimes$                        |                       |                                                      |                                 |\n|         |        |                                                                               |                           |                              |                              | $\\left(\\widehat{\\kappa}\\right)$    |                       |                                                      |                                 |\n|         |        |                                                                               |                           |                              |                              |                                    |                       |                                                      |                                 |\n| ☆       |        |                                                                               |                           |                              |                              |                                    |                       |                                                      |                                 |\n| $\\Box$  |        |                                                                               | Create Change Request     |                              |                              |                                    |                       | <b>Discard All Changes</b>                           | Save                            |\n| $\\circ$ |        |                                                                               | TradeAsE $\\times$         | TradeAsE.BackOfficeA ×       | TradeAsE.TreasurerA ×        |                                    |                       |                                                      |                                 |\n|         |        | 1/ TradeAsE.TreasurerA, TradeAsE // INT                                       |                           |                              | Fect                         |                                    |                       | Wed, 22. Aug 2018, 12:51:07 GMT // Connected [FFM] · |                                 |\n\n<span id=\"page-18-0\"></span>Figure 19 Institution: User Deal Tracking\n\n## <span id=\"page-19-0\"></span>**4 CONTACTING 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n#### **Germany**\n\n*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n#### **Middle East Asia Pacific**\n\n### **United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134\n\n#### **EMEA Americas**\n\n**USA** *360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900\n\n#### **Singapore**\n\n*360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "metadata": {"lang": "en"}}]