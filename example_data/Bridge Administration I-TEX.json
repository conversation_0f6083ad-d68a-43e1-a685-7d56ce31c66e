[{"id": "1", "text": "# **I-TEX (BRIDGE ADMINISTRATION)**\n\n![](_page_0_Picture_1.jpeg)\n\n# **TEX MULTIDEALER TRADING SYSTEM**\n\nUSER GUIDE 360T BRIDGE ADMINISTRATION\n\nI-TEX: INSTITUTION ADMINISTRATION\n\n© 360 TREASURY SYSTEMS AG, 2020\n\nTHIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG\n\n| 2<br>GETTING STARTED<br><br>3<br>I-TEX ENTITY CREATION<br>4<br>I-TEX USER CREATION<br>5<br>PASSWORD AND PIN CREATION<br><br>5.1<br>CREATE PASSWORD<br>5.2<br>CREATE PIN<br><br>6<br>PASSWORD AND PIN ENTRY<br>7<br>I-TEX ENTITY REMOVAL<br>8<br>I-TEX USER REMOVAL<br><br>9<br>TECHNICAL INFORMATION<br>10<br>CONTACTING 360T | 1 | INTRODUCTION | 4        |\n|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---|--------------|----------|\n|                                                                                                                                                                                                                                                                                                                               |   |              | 4        |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 7        |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 7        |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 13       |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 13<br>14 |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 15       |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 17       |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 19       |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 20       |\n|                                                                                                                                                                                                                                                                                                                               |   |              | 21       |\n\n## **TABLE OF FIGURES**\n\n| Figure 1<br>Header Bar4                                                 |  |\n|-------------------------------------------------------------------------|--|\n| Figure 2 Bridge Administration: Homepage<br>5                           |  |\n| Figure 3 Bridge Administration: Institution tree<br>6                   |  |\n| Figure 4<br>Bridge Administration: Expanded tree<br>7                   |  |\n| Figure 5 Bridge Administration: Users tab<br>8                          |  |\n| Figure 6 Bridge Administration: User Creation Wizard (1)<br>8           |  |\n| Figure 7 Bridge Administration: User Creation Wizard (2)<br>9           |  |\n| Figure 8 Bridge Administration: User Creation Wizard (3)<br>10          |  |\n| Figure 9 Bridge Administration: User Creation Wizard (4)<br>10          |  |\n| Figure 10 Bridge Administration: User Creation Wizard (6)<br>11         |  |\n| Figure 11 Bridge Administration: User creation change request comment11 |  |\n| Figure 12 Bridge Administration: Change Requests Action icon.<br>12     |  |\n| Figure 13 Bridge Administration: Change Request Management.<br>12       |  |\n| Figure 14 Bridge Administration: Users<br>tab<br>13                     |  |\n| Figure 15 Institution: Password confirmation popup<br>14                |  |\n| Figure 16 Institution: Password popup14                                 |  |\n| Figure 17 Institution: PIN confirmation popup15                         |  |\n| Figure 18 Institution: PIN popup<br>15                                  |  |\n| Figure 19 Institution: Password entry16                                 |  |\n| Figure 20 Institution: Change password popup16                          |  |\n| Figure 21 Institution: Please add PIN popup17                           |  |\n| Figure 22 Company Details: Institution Status<br>18                     |  |\n| Figure 23 Company Details: Institution deactivation change request18    |  |\n| Figure 24 List of users19                                               |  |\n|                                                                         |  |\n\n## <span id=\"page-3-0\"></span>**1 INTRODUCTION**\n\nThis user manual describes the handling of I-TEX entities, the I-TEX user creation and removal process as well as the password and PIN functionality, captured within the 360T Bridge Administration – Institution feature.\n\nOnly users with corresponding administrative rights are able to access the required I-TEX features. These rights can be separately assigned (remove I-TEX entities; create and delete I-TEX users; administrate I-TEX Password, administrate I-TEX PIN or both). <NAME_EMAIL> or your customer relationship manager for setting up the relevant administrative rights.\n\n## <span id=\"page-3-1\"></span>**2 GETTING STARTED**\n\nTo access the I-TEX administration functionalities, open the 360T Bridge Administration – Institution feature.\n\nThe Institution feature is located within the Bridge Administration tool. Bridge Administration can be accessed either via menu option \"Administration\" from the screen header of the Bridge application, or as a standalone feature from your starter applet.\n\n|                                                                  |                                                          |                                                    | $\\vee$ Preferences<br>■ Administration                        | $\\Box$ $\\times$<br>$\\vee$ Help<br>$AA -$ |\n|------------------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------|---------------------------------------------------------------|------------------------------------------|\n| > Bridge Administration                                          |                                                          |                                                    |                                                               | ×                                        |\n|                                                                  |                                                          |                                                    |                                                               |                                          |\n| <b>JOH LUIV</b><br><b>DOA FAM</b>                                | <b>DEIL ADI</b><br>ana nni                               | <b>Jeil Luiv</b><br><b>DOA FAIL</b>                | JEIL AND<br><b>DOA APR</b>                                    |                                          |\n| 1.17890<br>117 <sub>O</sub><br><b>ブノ</b> の<br>Spot // 21.11.2017 | 13237<br>$1.32$ <sup>3</sup><br>36<br>Spot // 21.11.2017 | $0.89 - 1$<br>0.89084<br>- 4<br>Spot // 21.11.2017 | 0.99184<br>$0.99 - 1$<br>9 <sub>3</sub><br>Spot // 21.11.2017 |                                          |\n\n<span id=\"page-3-2\"></span>Figure 1 Header Bar\n\nUpon opening Bridge Administration, shortcuts to different categories of configuration tools and actions for the particular user will be on display.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\n![](_page_4_Picture_2.jpeg)\n\nFigure 2 Bridge Administration: Homepage\n\n<span id=\"page-4-0\"></span>Upon clicking on the Institution quick links, the navigation panel which showcases various icons and an institution tree will become visible. The Institution tree includes the full list of users and entities configured under the main entity. Users belonging to the Main entity will be displayed by default.\n\n|                                                                                                                                                                                                                                                                                                                                                                                             | $\\vee$ Preferences           | $\\vee$ Administration                    | $\\vee$ Help                                          | $\\bullet$ | $AA - CD \\times$ |             |\n|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|------------------------------------------|------------------------------------------------------|-----------|------------------|-------------|\n| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                                                                             | <b>BRIDGE ADMINISTRATION</b> | $+$                                      |                                                      |           |                  |             |\n|                                                                                                                                                                                                                                                                                                                                                                                             |                              |                                          |                                                      |           |                  |             |\n| 亘<br>Q<br>$\\overline{\\left\\langle \\right\\rangle }$<br>合<br>$\\underline{\\widehat{\\mathbf{m}}}$ GroupE<br>$\\mathcal{L}$<br>$\\vee$ $\\frac{1}{111}$ SubsidiaryE<br>CroupE.AutoDealer<br>CroupE.TraderA<br>工<br>CroupE.TraderB<br>$\\overline{\\text{un}}$<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB<br>CroupE.TreasurerC<br>☆<br>$\\mathbb C$<br>$\\bigcirc$<br>$\\leftrightarrow$ |                              | No Individuals/Institutions are selected |                                                      |           |                  |             |\n| C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                                                                                                         | <b>EECT</b>                  |                                          | Tue, 03. Nov 2020, 18:46:50 GMT // Connected [FFM] · |           |                  | <b>BETA</b> |\n\n<span id=\"page-5-0\"></span>![](_page_5_Figure_3.jpeg)\n\nThe full content of the Institution tree wil be visible by clicking at the expansion icon .\n\n|                                                                                                                                                                                                                                                                                                                                                                       | $\\vee$ Preferences                     | $\\vee$ Administration                    | $\\vee$ Help                                          | $\\bullet$ AA - $\\bullet$ X |             |\n|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|------------------------------------------|------------------------------------------------------|----------------------------|-------------|\n| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                                                       | $^{+}$<br><b>BRIDGE ADMINISTRATION</b> |                                          |                                                      |                            |             |\n| Q ※<br>上血                                                                                                                                                                                                                                                                                                                                                             |                                        |                                          |                                                      |                            |             |\n| $\\langle$<br>合<br>$\\wedge \\hat{\\mathbf{\\underline{m}}}$ GroupE<br>÷<br>$\\mathcal{G}$<br>I ∧ <u>Im</u> SubsidiaryE<br>u Li In<br>SubsidiaryE.TreasurerA  <br>1<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC  <br>WILL<br>CroupE.AutoDealer<br>CroupE.TraderA<br>CroupE.TraderB<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB<br>CroupE.TreasurerC |                                        | No Individuals/Institutions are selected |                                                      |                            |             |\n| ☆<br>$\\cup$<br>$\\bigcirc$                                                                                                                                                                                                                                                                                                                                             |                                        |                                          |                                                      |                            |             |\n| $\\ominus$                                                                                                                                                                                                                                                                                                                                                             |                                        |                                          |                                                      |                            |             |\n| C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                                                                                   | <b>EECT</b>                            |                                          | Tue, 03. Nov 2020, 18:48:30 GMT // Connected [FFM] · |                            | <b>BETA</b> |\n\n<span id=\"page-6-2\"></span>Figure 4 Bridge Administration: Expanded tree\n\n## <span id=\"page-6-0\"></span>**3 I-TEX ENTITY CREATION**\n\nI-TEX entity creation is not currently supported by 360T Bridge Administration – Institution. In case you would like to create a new I-TEX entity, please contact the 360T CAS team via the contact details provided under chapter [10](#page-20-0) Contacting 360T.\n\n## <span id=\"page-6-1\"></span>**4 I-TEX USER CREATION**\n\nCreation of Change Request and approval by a Supervisor (different user) are mandatory steps for ITEX user creation.\n\n### **Administrator:**\n\nA new user creation change request process is intitiated by the Administrator. The corresponding I-TEX entity must be first selected from the Institution tree. This opens a Company Details tab containing the available details of the entity. The \"Create Internal User\" button is available from the Users tab.\n\n|                             |                                                         |                              |                                              | $\\vee$ Preferences     | $\\vee$ Administration | $\\vee$ Help                 | $A$ $A$ - $B$ $X$<br>$\\odot$                                                                        |             |\n|-----------------------------|---------------------------------------------------------|------------------------------|----------------------------------------------|------------------------|-----------------------|-----------------------------|-----------------------------------------------------------------------------------------------------|-------------|\n|                             | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>         |                              | $+$<br><b>BRIDGE ADMINISTRATION</b>          |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n| 合                           | Q ※<br>上 血<br>$\\langle$                                 | <b>Company Details</b>       | <b>Deal Tracking Groups (1)</b><br>Users (3) |                        |                       |                             | $\\textcolor{black}{\\curvearrowleft} \\hspace{0.1cm} \\mathbb{R} \\hspace{0.1cm} \\equiv \\hspace{0.1cm}$ |             |\n|                             | へ 血 GroupE                                              | 自立也                          |                                              |                        |                       |                             |                                                                                                     |             |\n| $\\mathcal{L}_{\\mathcal{F}}$ | $\\land \\overline{\\underline{\\mathfrak{m}}}$ SubsidiaryE |                              | Users (3)                                    |                        |                       | <b>Status</b>               |                                                                                                     |             |\n|                             | SubsidiaryE.TreasurerA                                  |                              | SubsidiaryE.TreasurerA                       | <b>Create Password</b> | <b>Create PIN</b>     | $\\bullet$<br>Active         | 一面                                                                                                  |             |\n| $\\overline{\\mathbb{F}_Q}$   | SubsidiaryE.TreasurerB                                  |                              | SubsidiaryE.TreasurerB                       | <b>Create Password</b> | <b>Create PIN</b>     | $\\bullet$<br>Active         | û                                                                                                   |             |\n| villy                       | SubsidiaryE.TreasurerC                                  |                              | SubsidiaryE.TreasurerC                       | <b>Create Password</b> | <b>Create PIN</b>     | <b>1 1 1</b><br>Active      |                                                                                                     |             |\n|                             | CroupE.AutoDealer                                       |                              |                                              |                        |                       | <b>Create Internal User</b> |                                                                                                     |             |\n|                             | CroupE.TraderA                                          |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             | CroupE.TraderB                                          |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             | CroupE.TraderC                                          |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             | CroupE.TreasurerA                                       |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             | GroupE.TreasurerB                                       |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             | CroupE.TreasurerC                                       |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n|                             |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n| ☆                           |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n| D                           |                                                         | <b>Create Change Request</b> |                                              |                        |                       |                             | Discard All Changes<br>Save                                                                         |             |\n| $\\bigcirc$                  |                                                         |                              |                                              |                        |                       |                             |                                                                                                     |             |\n| $\\ominus$                   |                                                         | SubsidiaryE $\\times$         |                                              |                        |                       |                             |                                                                                                     |             |\n|                             | C GroupE.TreasurerC, GroupE // BETA                     |                              | FEET                                         |                        |                       |                             | Tue, 03. Nov 2020, 18:05:45 GMT // Connected [FFM] ·                                                | <b>BETA</b> |\n\n<span id=\"page-7-0\"></span>Figure 5 Bridge Administration: Users tab\n\nA Help Wizard with the following mandatory fields will be populated initially:\n\n- a. Login Name: User's name or name synonym\n- b. Postion: Front or Back Office user\n\nPlease note: The email address and the phone number, which must both reference the main entity and without disclosing any personal details of the ITEX user, are predefined by the 360T CAS team when the I-TEX entity is initally set up.\n\n| $\\left.\\right\\rangle$ 4 $\\left.\\right\\rangle$ 5 $\\left.\\right\\rangle$<br>3 <sup>1</sup> | $\\checkmark$             | Create an Individual                                                  |              |                |\n|-----------------------------------------------------------------------------------------|--------------------------|-----------------------------------------------------------------------|--------------|----------------|\n|                                                                                         |                          | Please fill in the Individual details                                 |              |                |\n|                                                                                         | Login Name*              | SubsidiaryE<br>$\\checkmark$<br>Name does not match the correct format |              |                |\n|                                                                                         | Description              |                                                                       |              |                |\n|                                                                                         | Email *                  | <EMAIL>                                         | $\\checkmark$ |                |\n|                                                                                         | Phone Number *           | +49 123 456 789                                                       | $\\checkmark$ |                |\n|                                                                                         | Position *               | <b>O</b> Front Office                                                 |              |                |\n|                                                                                         |                          | <b>Back Office</b>                                                    |              |                |\n|                                                                                         | Expose data to providers | Disabled<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                  |              |                |\n|                                                                                         |                          |                                                                       | * Mandatory  |                |\n| Previous                                                                                |                          |                                                                       |              | Next<br>Cancel |\n\n<span id=\"page-7-1\"></span>Figure 6 Bridge Administration: User Creation Wizard (1)\n\nThe second wizard tab (2) will appear after clicking on the icon: . The field \"Trading Type\" allows the specification of one of three available trading permissions:\n\n- Plain TEX User: should be selected when user trades for the I-TEX entity-only\n- Trade-as User: should be selected when requests are sent by the user in the name of related entities via 360T's Trade-As functionality\n- Trade-on-behalf User: should be selected when requests are sent by the user in the name of related entities via 360T's Trade-on-Behalf functionality\n\n| Trading Type  | Trade-as User        |  |\n|---------------|----------------------|--|\n|               | Trade-as User        |  |\n| Available     | Trade-on-behalf User |  |\n| TradeAsE TAS  | Plain TEX User       |  |\n| TradeAsE.TAS2 |                      |  |\n|               | ĸ                    |  |\n|               | ---                  |  |\n|               | $\\gg$                |  |\n|               | $\\propto$            |  |\n|               |                      |  |\n\n<span id=\"page-8-0\"></span>Figure 7 Bridge Administration: User Creation Wizard (2)\n\nPlease note: The selection of the Trading Type depends on the particular set-up, however \"Plain TEX User\" is the default option. In case trade-as or trade-on-behalf rights need to be assigned, the legal entities can be assigned immediately to the user, by transferring a Legal Entity Group from Available to Selected as seen above.\n\nIn Step No. 3), the user can be added as a Member of a Deal Tracking group by transferring the group from Available to Selected.\n\nPlease note: Viewers of a Deal Tracking group (see tab (4)) can see the Members' executed trades. Back Office users do not need to be added as \"Members\" to a Deal Tracking group as they do not trade.\n\n| $\\left(\\begin{array}{c}1\\end{array}\\right)$ $\\left(\\begin{array}{c}2\\end{array}\\right)$ $\\left(\\begin{array}{c}3\\end{array}\\right)$ $\\left(\\begin{array}{c}4\\end{array}\\right)$ $\\left(\\begin{array}{c}5\\end{array}\\right)$ $\\sqrt{2}$ |                              |                                                   | Create an Individual                                                                                                                                                                                                                                                                                                                                         |          |                       |\n|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------|---------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|-----------------------|\n|                                                                                                                                                                                                                                        |                              | Configure Deal Tracking membership and viewership |                                                                                                                                                                                                                                                                                                                                                              |          |                       |\n|                                                                                                                                                                                                                                        | <b>Deal Tracking Members</b> |                                                   |                                                                                                                                                                                                                                                                                                                                                              |          |                       |\n|                                                                                                                                                                                                                                        | SubsidiaryE                  | Available                                         | $\\gg$<br>$\\begin{picture}(20,20) \\put(0,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1,0){15}} \\put(15,0){\\line(1$ | Selected |                       |\n| Previous                                                                                                                                                                                                                               |                              |                                                   |                                                                                                                                                                                                                                                                                                                                                              |          | <b>Next</b><br>Cancel |\n\n<span id=\"page-9-0\"></span>Figure 8 Bridge Administration: User Creation Wizard (3)\n\nUser Guide Bridge Administration: I-TEX Insitution Administration\n\nIn the next tab (4), the user can be added as a Viewer of a Deal Tracking group by transferring the group from Available to Selected.\n\n| $4 \\rightarrow 5$<br>$\\checkmark$ |           | Create an Individual       |                                                   |                       |\n|-----------------------------------|-----------|----------------------------|---------------------------------------------------|-----------------------|\n|                                   |           |                            | Configure Deal Tracking membership and viewership |                       |\n| <b>Deal Tracking Viewers</b>      |           |                            |                                                   |                       |\n| SubsidiaryE                       | Available | $\\gg$<br>$\\langle \\langle$ | Selected                                          |                       |\n| Previous                          |           |                            |                                                   | <b>Next</b><br>Cancel |\n\n<span id=\"page-9-1\"></span>Figure 9 Bridge Administration: User Creation Wizard (4)\n\nPlease note: Viewers of a Deal Tracking group have view-only access to the executed trades of Members of the same Deal Tracking Group.\n\nThe last tab (5) of the Wizard presents an overview of all pending changes. The Administrator has the possibility to review and modify if necessary by reverting to previous tabs.\n\n|          |                             | Create an Individual                 |                  |\n|----------|-----------------------------|--------------------------------------|------------------|\n|          |                             | Overview: Please review your changes |                  |\n|          | Individual Name             | SubsidiaryE.TreasurerD               |                  |\n|          | Description                 |                                      |                  |\n|          | Email                       | <EMAIL>        |                  |\n|          | Phone Number                | +49 123 456 789                      |                  |\n|          | Fax Number                  |                                      |                  |\n|          | Position                    | Treasurer                            |                  |\n|          |                             |                                      |                  |\n|          | Trading Type                | Plain TEX User                       |                  |\n|          | TAS/TOB Groups              |                                      |                  |\n|          | Deal Tracking Member        | SubsidiaryE                          |                  |\n|          | <b>Deal Tracking Viewer</b> | SubsidiaryE                          |                  |\n|          |                             |                                      |                  |\n|          |                             |                                      |                  |\n| Previous |                             |                                      | Create<br>Cancel |\n\n<span id=\"page-10-0\"></span>Figure 10 Bridge Administration: User Creation Wizard (6)\n\nUpon clicking at \"Create\" entries will be saved and the Change Request process is now initiated, with the requirement to provide a comment. This comment will be available to the Supervisor while the change request is being approved.\n\n| Your text here |  |  |\n|----------------|--|--|\n|                |  |  |\n|                |  |  |\n|                |  |  |\n\n<span id=\"page-10-1\"></span>Figure 11 Bridge Administration: User creation change request comment.\n\n#### **Supervisor (Change Request Approval):**\n\nThe user with Supervisor permissions is able to approve the user creation change request submitted by the Administrator.\n\nThe Change Request can be accessed via the Bridge Administration Homepage.\n\n| Configurations        | <b>Administration Start</b> |                        |\n|-----------------------|-----------------------------|------------------------|\n| Institution           | <b>Bank Baskets</b>         |                        |\n| Actions               |                             |                        |\n| <b>Change Request</b> | Wizards                     | <b>Evaluator Tools</b> |\n\n<span id=\"page-11-0\"></span>Figure 12 Bridge Administration: Change Requests Action icon.\n\nBy turning on the icon populates all pending Change Requests, which can be individually reviewed, approved or rejected using the action icons:\n\n|                                                      |                              |                              | Change Requests (1/1) |                   |               | $\\bullet$                                                   | Show all          |\n|------------------------------------------------------|------------------------------|------------------------------|-----------------------|-------------------|---------------|-------------------------------------------------------------|-------------------|\n| Select Category<br>Select Company<br>Filter by CR id | $\\checkmark$<br>$\\checkmark$ | 000.CRM.STRESS.TEST $\\times$ |                       |                   |               |                                                             | $\\trianglelefteq$ |\n| Id                                                   | Institution                  | <b>Comment</b>               | Submitted at          | Submitted by      | <b>Status</b> |                                                             |                   |\n| CR-2681                                              | 000.CRM.STRESS.TEST          | test                         | Oct 30, 2020 4:10:2   | CRMST.SuperAdmin1 | $\\bullet$ 0/1 | $\\circledcirc \\mathbb{E} \\vee \\times \\triangleright \\equiv$ |                   |\n|                                                      |                              |                              |                       |                   |               |                                                             |                   |\n|                                                      |                              |                              |                       |                   |               |                                                             |                   |\n|                                                      |                              |                              |                       |                   |               |                                                             |                   |\n|                                                      |                              |                              |                       |                   |               |                                                             |                   |\n|                                                      |                              |                              |                       |                   |               |                                                             |                   |\n\n<span id=\"page-11-1\"></span>Figure 13 Bridge Administration: Change Request Management.\n\n## <span id=\"page-12-0\"></span>**5 PASSWORD AND PIN CREATION**\n\nThe Password and PIN feature is available in the \"Users\" tab of the selected entity from the Insitution tree:\n\n|           |                           |                                                                                                                          |                       |                                     |                          | $\\vee$ Preferences     | $\\vee$ Administration | $\\vee$ Help             |                                                      |                         | $\\mathbb{D}^1$ O AA - $\\mathbb{D}$ X |             |\n|-----------|---------------------------|--------------------------------------------------------------------------------------------------------------------------|-----------------------|-------------------------------------|--------------------------|------------------------|-----------------------|-------------------------|------------------------------------------------------|-------------------------|--------------------------------------|-------------|\n|           |                           | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                          |                       | <b>BRIDGE ADMINISTRATION</b>        | $^{+}$                   |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           | 合                         | Q 张   1 童<br>$\\langle$                                                                                                   | Company Details       | Users (3)<br>ı                      | Deal Tracking Groups (1) |                        |                       |                         |                                                      |                         | の 心 重                                |             |\n|           | $\\mathcal{L}$             | $\\land \\overline{\\hat{\\underline{\\bf m}}}$ GroupE<br>$\\land \\overline{\\text{m}}$ SubsidiaryE<br>& SubsidiaryE.TreasurerA | 自立出                   | Users (3)<br>SubsidiaryE.TreasurerA |                          | <b>Create Password</b> | <b>Create PIN</b>     | <b>Status</b><br>Active | $\\bullet$                                            | 一面                      |                                      |             |\n|           |                           | & SubsidiaryE.TreasurerB                                                                                                 |                       | SubsidiaryE.TreasurerB              |                          | <b>Create Password</b> | <b>Create PIN</b>     | Active                  | $\\sigma$                                             | ŵ                       |                                      |             |\n|           | $\\mathbb{F}_{\\mathbb{Q}}$ | SubsidiaryE.TreasurerC                                                                                                   |                       | SubsidiaryE.TreasurerC              |                          | <b>Create Password</b> | <b>Create PIN</b>     | Active                  | $\\bullet$                                            | $\\widehat{\\mathbb{m}}$  |                                      |             |\n|           | $\\overline{\\text{unif}}$  | CroupE.AutoDealer                                                                                                        |                       |                                     |                          |                        |                       |                         | <b>Create Internal User</b>                          |                         |                                      |             |\n|           |                           | CroupE.TraderA                                                                                                           |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           | CroupE.TraderB                                                                                                           |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           | CroupE.TraderC                                                                                                           |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           | CroupE.TreasurerA                                                                                                        |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           | CroupE.TreasurerB                                                                                                        |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           | CroupE.TreasurerC                                                                                                        |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n| ₿         |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n| Ď         |                           |                                                                                                                          | Create Change Request |                                     |                          |                        |                       |                         |                                                      | Discard All Changes ) ( | Save                                 |             |\n| $\\circ$   |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n| $\\ominus$ |                           |                                                                                                                          | SubsidiaryE $\\times$  |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           |                                                                                                                          |                       |                                     |                          |                        |                       |                         |                                                      |                         |                                      |             |\n|           |                           | C GroupE.TreasurerC, GroupE // BETA                                                                                      |                       |                                     | <b>EEUT</b>              |                        |                       |                         | Tue, 03. Nov 2020, 18:05:45 GMT // Connected [FFM] . |                         |                                      | <b>BETA</b> |\n\n<span id=\"page-12-2\"></span>Figure 14 Bridge Administration: Users tab\n\nUser may require a password, PIN, or both, depending on several scenarios:\n\n- A new 360T user would like to login for the first time: both Password and PIN are required\n- An existing user has forgotten their passowrd: Password only\n- The JKS certificate of a user has expired: PIN only\n- An existing user has a new PC: PIN only\n\n## <span id=\"page-12-1\"></span>**5.1 Create Password**\n\nThe \"Create Password\" button is available next to the individual user name. When selected, a window will pop up requesting the confirmation of the password creation action:\n\n| Please confirm:                                          |              |\n|----------------------------------------------------------|--------------|\n| Create / Reset password for user SubsidiaryE.TreasurerA. |              |\n|                                                          | Cancel<br>OK |\n\n<span id=\"page-13-1\"></span>Figure 15 Institution: Password confirmation popup\n\nAfter clicking \"OK\", a second pop-up window will appear with the user's new password.\n\n| SubsidiaryE.TreasurerA Password |    |\n|---------------------------------|----|\n| J7Yw8kdomO                      |    |\n|                                 |    |\n|                                 |    |\n|                                 | OK |\n\n<span id=\"page-13-2\"></span>Figure 16 Institution: Password popup\n\nThe created text must be copied and sent to the user e.g. via email. Please click at \"OK\" after the text has been copied to close the window.\n\nPlease note: Creating a new password will reset the user's existing password.\n\n### <span id=\"page-13-0\"></span>**5.2 Create PIN**\n\nClicking on \"Create PIN\" button adjacent to the affected user triggers the pop-up of PIN creation window:\n\n| Please confirm:                                     |\n|-----------------------------------------------------|\n| Create / Reset PIN for user SubsidiaryE.TreasurerA. |\n|                                                     |\n|                                                     |\n| Cancel<br>OK                                        |\n\n<span id=\"page-14-1\"></span>Figure 17 Institution: PIN confirmation popup\n\nAfter clicking \"OK\", a second pop-up window will appear with the user's new PIN.\n\n| SubsidiaryE.TreasurerA PIN |        |    |\n|----------------------------|--------|----|\n|                            | 553711 |    |\n|                            |        |    |\n|                            |        |    |\n|                            |        | OK |\n\n<span id=\"page-14-2\"></span>Figure 18 Institution: PIN popup\n\nThe created text must be copied and sent to the user e.g. via email. Please click at \"OK\" once the text has been copied to close the window.\n\nPlease note: Creating a new PIN will reset the user's existing security certificate. The new PIN needs to be entered upon the user's next login.\n\n## <span id=\"page-14-0\"></span>**6 PASSWORD AND PIN ENTRY**\n\nUsers may be prompted to enter a password, PIN, or both, if these have been reset, or created for the first time.\n\nA first/reset password should be entered in the starter applet:\n\n|             | <b>DEUTSCHE BÖRSE</b><br><b>EEMT</b><br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |                |\n|-------------|----------------------------------------------------------------------------------|----------------|\n| <b>BETA</b> |                                                                                  | Version: 4.4.1 |\n|             | SubsidiaryE.TreasurerA<br>$\\checkmark$<br>Password                               |                |\n|             | Login                                                                            |                |\n| Help        | <b>Change Password</b>                                                           |                |\n\n<span id=\"page-15-0\"></span>Figure 19 Institution: Password entry\n\nA user will be prompted to set a new password in the Change Password pop-up window.\n\n| <b>Change Password</b>                                                           |                                                |  |\n|----------------------------------------------------------------------------------|------------------------------------------------|--|\n|                                                                                  |                                                |  |\n| New Password                                                                     |                                                |  |\n| Repeat Password                                                                  |                                                |  |\n| Minimum numeric characters<br>Minimum password length<br>Maximum password length | คิ<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |  |\n| Cancel<br>OK                                                                     |                                                |  |\n\n<span id=\"page-15-1\"></span>Figure 20 Institution: Change password popup\n\nA user will be prompted to add their PIN in a separate pop-up window.\n\n![](_page_16_Picture_2.jpeg)\n\nFigure 21 Institution: Please add PIN popup\n\n<span id=\"page-16-1\"></span>After the Password, PIN, or both have been correctly inserted the user will be logged in to the 360T platform.\n\n## <span id=\"page-16-0\"></span>**7 I-TEX ENTITY REMOVAL**\n\nRemoving an I-TEX entity requires a Change Request to be created by an Administrator, and thus approved by a Supervisor (different user).\n\nPlease note: An I-TEX entity can only be removed if all the related users are either in a deactivated or a deleted status.\n\nAdministrator can submit a change request to remove an I-TEX entity by changing the status of the entity from \"Institution active\" to \"Institution inactive\" in the \"Company Details\" of the ITEX entity.\n\n#### User Guide Bridge Administration: I-TEX Insitution Administration\n\n|                                                                                                                                                                                                                                                                                                                                                                                                                          | $\\vee$ Preferences                                                                                                                                                                                                                                                                                                                                                 | $AA - CD X$<br>$\\odot$<br>$\\vee$ Administration<br>ぺ<br>$\\vee$ Help                                                                                                                                                |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                                                                                                                          | <b>BRIDGE ADMINISTRATION</b><br>$^{+}$                                                                                                                                                                                                                                                                                                                             |                                                                                                                                                                                                                    |\n| 撥<br>$\\alpha$<br>上 血<br>$\\langle$<br>合<br>$\\wedge \\overline{m}$ GroupE<br>$\\mathcal{L}$<br>$\\gamma$ $\\hat{m}$ SubsidiaryE $\\blacksquare$<br>SubsidiaryE.TreasurerA<br><b>T</b> O<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC<br>$\\sqrt{117}$<br>SubsidiaryE.TreasurerD<br>CroupE.AutoDealer<br>CroupE.TraderA<br>CroupE.TraderB<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB<br>CroupE.TreasurerC | <b>Company Details</b><br>Users (4)<br><b>Deal Tracking Groups (1)</b><br><b>Overview</b><br><b>Prefixes</b><br>Internal trades only<br>Company Name *<br>Description<br>Phone Number<br>Fax Number<br>Country *<br>US Person *<br><b>EUR</b><br>Currency<br>LEI<br>Status *<br>Provider Role<br>Requestor Role<br>$\\vee$<br>Prime Broker<br>High Frequency Trader | $\\Omega \\quad \\Omega \\quad \\equiv$<br>SubsidiaryE<br>Germany<br>$\\checkmark$<br>O 0 Disabled<br>$\\checkmark$<br><b>Institution active</b><br>$\\checkmark$<br>O 0 Disabled<br>Enabled<br>○ Disabled<br>O 0 Disabled |\n| ⇔□○<br>$\\bigoplus$<br>C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                                                                                                                | Current ITEX Group Treasury<br>Create Change Request<br>SubsidiaryE $\\times$<br><b>Geor</b>                                                                                                                                                                                                                                                                        | Discard All Changes ) (<br>Save<br>Tue, 03. Nov 2020, 19:03:07 GMT // Connected [FFM] ·<br><b>BETA</b>                                                                                                             |\n\n<span id=\"page-17-0\"></span>Figure 22 Company Details: Institution Status\n\nOnce the Status is updated, the Administrator can submit the deactivation request by clicking at the \"Created Change Request\" icon:\n\n|                                                                                                                    |                                                           | $\\vee$ Preferences                                                     | $\\nabla^e$<br>$\\odot$<br>$AA - CD \\times$<br>$\\vee$ Administration<br>$\\vee$ Help |\n|--------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------------------------------------------------|\n|                                                                                                                    | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>           | $+$<br><b>BRIDGE ADMINISTRATION</b>                                    |                                                                                   |\n|                                                                                                                    |                                                           |                                                                        |                                                                                   |\n| 侖                                                                                                                  | Q<br>幾<br>上 血<br>$\\overline{\\left\\langle \\right\\rangle }$ | <b>Company Details</b><br>Users (4)<br><b>Deal Tracking Groups (1)</b> | の № 亖                                                                             |\n| $\\mathcal{L}_{\\mathcal{F}}$                                                                                        | $\\wedge \\hat{\\mathbb{m}}$ GroupE                          | <b>Overview</b><br><b>Prefixes</b><br>Internal trades only             |                                                                                   |\n|                                                                                                                    | ∧ 血 SubsidiaryE<br>SubsidiaryE.TreasurerA                 |                                                                        |                                                                                   |\n| <b>武</b>                                                                                                           | SubsidiaryE.TreasurerB                                    | Company Name*                                                          | SubsidiaryE                                                                       |\n|                                                                                                                    | SubsidiaryE.TreasurerC                                    | Description                                                            |                                                                                   |\n| viiy                                                                                                               | SubsidiaryE.TreasurerD                                    | Phone Number                                                           |                                                                                   |\n|                                                                                                                    | CroupE.AutoDealer                                         | Fax Number                                                             |                                                                                   |\n|                                                                                                                    | CroupE.TraderA                                            | Country *                                                              | Germany                                                                           |\n|                                                                                                                    | CroupE.TraderB                                            | US Person *                                                            | O 0 Disabled                                                                      |\n|                                                                                                                    | CroupE.TraderC                                            | Currency                                                               | <b>EUR</b><br>$\\checkmark$                                                        |\n|                                                                                                                    | CroupE.TreasurerA                                         |                                                                        |                                                                                   |\n|                                                                                                                    | CroupE.TreasurerB<br>CroupE.TreasurerC                    | LEI                                                                    |                                                                                   |\n|                                                                                                                    |                                                           | Status *                                                               | Institution inact                                                                 |\n|                                                                                                                    |                                                           | Provider Role                                                          | ○ 0 Disabled                                                                      |\n|                                                                                                                    |                                                           | Requestor Role                                                         | <b>VO</b> Enabled                                                                 |\n|                                                                                                                    |                                                           | Prime Broker                                                           | O 0 Disabled                                                                      |\n|                                                                                                                    |                                                           | High Frequency Trader                                                  | O 0 Disabled                                                                      |\n|                                                                                                                    |                                                           |                                                                        |                                                                                   |\n| ☆                                                                                                                  |                                                           |                                                                        |                                                                                   |\n| D                                                                                                                  |                                                           | <b>Create Change Request</b>                                           | <b>Discard All Changes</b><br>Save                                                |\n| $\\circ$                                                                                                            |                                                           |                                                                        |                                                                                   |\n| $\\ominus$                                                                                                          |                                                           | SubsidiaryE * X                                                        |                                                                                   |\n| C GroupE.TreasurerC, GroupE // BETA<br>Fech<br>Tue, 03. Nov 2020, 19:04:50 GMT // Connected [FFM] ●<br><b>BETA</b> |                                                           |                                                                        |                                                                                   |\n\n<span id=\"page-17-1\"></span>Figure 23 Company Details: Institution deactivation change request.\n\nBy selecting \"Create Change Request\", changes will be saved and the Change Request process is initiated, a comment is now required to be added. This comment will be visible to the Supervisor upon approving the change request.\n\n#### **Supervisor**\n\nSimilar to the approval of the Change Request for user creation (see Chapter 4), approval of the Change Request to remove the I-TEX entity is accessed via the Bridge Administration Homepage, from the Change Request window.\n\nPlease note: By removing the I-TEX entity will also remove the 360T Counterparty Relationship Management (CRM) tool relationship between the TEX and I-TEX entity.\n\n## <span id=\"page-18-0\"></span>**8 I-TEX USER REMOVAL**\n\nRemoving an I-TEX user requires a Change Request to be created by an Administrator, and thus approved by a Supervisor (different user).\n\n#### **Administrator:**\n\nThe process of the user removal change request is intitiated by the Administrator. The corresponding I-TEX entity must be first selected from the Institution tree, which leads to the opening of the Company Details tab containing the available details of the entity. The full list of Users with their status is shown in the \"Users\" tab.\n\n|                                       |                                                                                                                                                                                                                                                                                                                   | $\\vee$ Preferences                                                                                                           | $\\vee$ Administration                                                                                | $\\vee$ Help                                                                                                                               | D <sub>e</sub><br>$\\odot$<br>$AA - CD X$                                                                        |             |\n|---------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------|-------------|\n|                                       | <b>RFS REQUESTER</b><br><b>ORDER MANAGEMENT</b>                                                                                                                                                                                                                                                                   | <b>BRIDGE ADMINISTRATION</b>                                                                                                 | $\\pm$                                                                                                |                                                                                                                                           |                                                                                                                 |             |\n| 合                                     | Q<br>邀<br>上血<br>$\\overline{\\left( \\right. }%$                                                                                                                                                                                                                                                                     | Company Details   Users (4)                                                                                                  | <b>Deal Tracking Groups (1)</b>                                                                      |                                                                                                                                           | $\\Omega \\quad \\Omega \\quad \\equiv$                                                                              |             |\n| $\\mathcal{L}_{\\mathcal{F}}$<br>4<br>W | $\\wedge \\hat{\\mathbb{m}}$ GroupE<br>$\\land \\frac{\\hat{\\pi}}{\\mathbf{u}}$ SubsidiaryE<br>SubsidiaryE.TreasurerA<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC<br>SubsidiaryE.TreasurerD<br>CroupE.AutoDealer<br>CroupE.TraderA<br>CroupE.TraderB<br>CroupE.TraderC<br>CroupE.TreasurerA<br>CroupE.TreasurerB | Ê<br>土土<br>Users (4)<br>SubsidiaryE.TreasurerA<br>SubsidiaryE.TreasurerB<br>SubsidiaryE.TreasurerC<br>SubsidiaryE.TreasurerD | <b>Create Password</b><br><b>Create Password</b><br><b>Create Password</b><br><b>Create Password</b> | <b>Status</b><br><b>Create PIN</b><br>Active<br><b>Create PIN</b><br>Active<br><b>Create PIN</b><br>Active<br><b>Create PIN</b><br>Active | û<br>$\\bullet$<br>û<br>Œ<br>$\\widehat{\\mathbb{U}}$<br>$\\bullet$<br>û<br>$\\sigma$<br><b>Create Internal User</b> |             |\n| ☆<br>D<br>$\\bigcirc$<br>$\\ominus$     | CroupE.TreasurerC                                                                                                                                                                                                                                                                                                 | Create Change Request<br>SubsidiaryE $\\times$                                                                                |                                                                                                      |                                                                                                                                           | Discard All Changes ) (<br>Save                                                                                 |             |\n|                                       | C GroupE.TreasurerC, GroupE // BETA                                                                                                                                                                                                                                                                               | <b>new T</b>                                                                                                                 |                                                                                                      | Tue, 03. Nov 2020, 18:54:08 GMT // Connected [FFM] ●                                                                                      |                                                                                                                 | <b>BETA</b> |\n\n<span id=\"page-18-1\"></span>Figure 24 List of users.\n\nThe user can be removed either:\n\n- permanently (new User status: Deleted) or\n- temporarily (new User status: Inactive)\n\nIn both cases the user will be removed and log in will be prohibited. Deleting a user removes the view access to the deal tracking groupand TAS/TOB rights, while making an user inactive retains these settings.\n\n#### **Supervisor**\n\nThe Change Request to remove the I-TEX user can be accessed by the Supervisor via the Bridge Administration Homepage, similar to the approval of the user creation change request (see Chapter [4\\)](#page-6-1).\n\n## <span id=\"page-19-0\"></span>**9 TECHNICAL INFORMATION**\n\n360T requires a private certificate (.JKS file) for a user to login to the platform. With the entry of the password and PIN or PIN only (in case of an existing, valid password) an automatic download of the JKS file is triggered.\n\nSome local PC settings may prevent the automatic download. In case of any issues, please download the folowing .pdf file containing technical requirements for the use of the system:\n\n[https://download.360t.com/documents/Technical\\\\_Requirements\\\\_TEX.pdf](https://download.360t.com/documents/Technical_Requirements_TEX.pdf)\n\nIf necessary, this should be reviewed by your system administrator regarding your proxy and firewall settings.\n\nIn case of any technical questions, please contact the Client Advisory Services <NAME_EMAIL> or +49 69 900289 19.\n\n## <span id=\"page-20-0\"></span>**10 CONTACTING 360T**\n\n#### **Global Support**\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n#### **Germany**\n\n*360T AG* Grueneburgweg 16-18 D-60322 Frankfurt am Main Phone: +49 69 900289-0 Fax: +49 69 900289-29\n\n### **Middle East Asia Pacific**\n\n#### **United Arab Emirates** *360 Trading Networks LLC* Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134\n\n### **EMEA Americas**\n\n#### **USA** *360 Trading Networks, Inc* 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900\n\n## **Singapore**\n\n*360T Asia Pacific Pte. Ltd.* 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "metadata": {"lang": "en"}}]