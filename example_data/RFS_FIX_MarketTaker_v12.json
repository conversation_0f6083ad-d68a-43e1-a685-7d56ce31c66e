[{"id": "1", "text": "<span id=\"page-0-0\"></span>![](_page_0_Picture_0.jpeg)\n\n# RFS Market Taker API\n\nFIX Rules of Engagement\n\nAPI Version: 12.14 Platform Release: 4.22\n\nImplements FIX Protocol Version 4.4\n\n360 Treasury Systems AG Grüneburgweg 16-18 / Westend Carrée D-60322 Frankfurt am Main Tel: +49 69 900289 0 Fax: +49 69 900289 29 Email: <EMAIL> Commercial Register Frankfurt, No. HRB 49874 Executive Board: <PERSON>, <PERSON>, <PERSON> Supervisory Board: <PERSON>\n\n## <span id=\"page-1-0\"></span>**1 Overview**\n\nThe purpose of this document is to provide an overview of the FIX services offered by 360 Treasury Systems for FX products. It focuses on the technical aspects and is intended to provide clients with a clear understanding of how a successful connection could be made.\n\nIt contains an overview of the general workflow, as well as detailed specifications of the utilized FIX messages. The API is implemented to meet FIX 4.4 standards.\n\n## **Table of Contents**\n\n| 1 | Overview                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 2                                                                         |\n|---|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------|\n| 2 | Product Offering<br>2.1<br>Products currently supported by API<br>2.2<br>Market data<br>.                                                                                                                                                                                                                                                                                                                                                                 | 6<br>6<br>6                                                               |\n| 3 | Important Disclaimer for API Clients                                                                                                                                                                                                                                                                                                                                                                                                                      | 7                                                                         |\n| 4 | Connecting to 360T<br>4.1<br>Network Connectivity<br>4.1.1<br>Radianz<br>4.1.2<br>Stunnel<br>.<br>4.1.3<br>Cross Connect<br>.<br>4.1.4<br>N7<br>4.1.5<br>Plain Internet<br>4.2<br>Connection and Firewall Configuration<br>4.3<br>FIX Engine Compatibility Testing<br>.<br>4.4<br>FIX Protocol Levels<br>4.4.1<br>User Credentials<br>.<br>4.4.2<br>FIX Session Reset<br>.<br>4.5<br>Availability<br>.<br>4.6<br>Trading Hours<br>4.7<br>Timeout handling | 8<br>8<br>8<br>8<br>8<br>8<br>8<br>8<br>9<br>9<br>9<br>9<br>9<br>10<br>10 |\n| 5 | Utilization of the FIX Protocol<br>5.1<br>System Messages<br>.<br>5.2<br>Business Messages<br>.<br>5.3<br>360T customization details<br>.<br>5.3.1<br>Side<br>5.3.2<br>Quote Cancels and Business Rejections<br>.<br>5.4<br>General Workflow                                                                                                                                                                                                              | 11<br>11<br>11<br>12<br>12<br>12<br>12                                    |\n| 6 | System Messages<br>6.1<br>Notes on important details<br>.<br>6.2<br>Message Header<br>6.3<br>Message Footer<br>.<br>6.4<br>Logon [A]<br>6.5<br>Heartbeat [0]<br>.<br>6.6<br>TestRequest [1]<br>.<br>6.7<br>ResendRequest [2]<br>.<br>6.8<br>Reject [3]<br>.<br>6.9<br>SequenceReset [4]<br>.<br>6.10 Logout [5]                                                                                                                                           | 14<br>14<br>14<br>14<br>15<br>15<br>15<br>16<br>16<br>17<br>18            |\n| 7 | Business Messages<br>7.1<br>News [B]<br>.                                                                                                                                                                                                                                                                                                                                                                                                                 | 19<br>19                                                                  |\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 3\n\n|   | 7.2 |           | QuoteRequest [R]<br>20                        |\n|---|-----|-----------|-----------------------------------------------|\n|   | 7.3 |           | QuoteRequestReject [AG]<br>.<br>29            |\n|   | 7.4 |           | BusinessMessageReject [j]<br>.<br>30          |\n|   | 7.5 | Quote [S] | .<br>31                                       |\n|   | 7.6 |           | QuoteCancel [Z]<br>.<br>34                    |\n|   |     | 7.6.1     | Customer → 360T<br>.<br>34                    |\n|   |     | 7.6.2     | Customer ← 360T<br>.<br>34                    |\n|   | 7.7 |           | New Order Single [D]<br>36                    |\n|   | 7.8 |           | New Order Multileg [AB]<br>40                 |\n|   | 7.9 |           | Execution Report [8]<br>.<br>42               |\n|   |     |           | 7.10 SecurityDefinitionRequest [c]<br>.<br>51 |\n|   |     |           | 7.11 SecurityDefinition [d]<br>52             |\n| 8 |     |           | Example Messages<br>54                        |\n|   | 8.1 | News [B]  | .<br>54                                       |\n|   | 8.2 |           | QuoteRequest [R]<br>55                        |\n|   |     | 8.2.1     | Tradeable Quotes: Spot<br>55                  |\n|   |     | 8.2.2     | Tradeable Quotes: Forward<br>55               |\n|   |     | 8.2.3     | Tradeable Quotes: FX Time Options<br>56       |\n|   |     | 8.2.4     | Tradeable Quotes: Swap<br>.<br>56             |\n|   |     | 8.2.5     | Tradeable Quotes: NDF<br>.<br>57              |\n|   |     | 8.2.6     | Tradeable Quotes: NDS<br>.<br>58              |\n|   |     | 8.2.7     | Tradeable Quotes: FX Block Trade<br>.<br>58   |\n|   |     | 8.2.8     | Tradeable Quotes: NDF Block Trade<br>.<br>59  |\n|   |     | 8.2.9     | Tradeable Quotes: Loan/Deposit<br>60          |\n|   |     | 8.2.10    | Request for Market data (FORWARD)<br>61       |\n|   | 8.3 |           | QuoteRequestReject [AG]<br>.<br>62            |\n|   |     |           |                                               |\n|   | 8.4 |           | BusinessMessageReject [j]<br>.<br>62          |\n|   | 8.5 | Quote [S] | .<br>63                                       |\n|   |     | 8.5.1     | Spot Quote - Buy<br>63                        |\n|   |     | 8.5.2     | Forward Quote - Two-Way<br>63                 |\n|   |     | 8.5.3     | FX Time Options quote - Two-Way<br>.<br>64    |\n|   |     | 8.5.4     | Swap Quote - Two-Way<br>.<br>64               |\n|   |     | 8.5.5     | NDF quote - Two-Way<br>65                     |\n|   |     | 8.5.6     | NDS quote - Two-Way<br>65                     |\n|   |     | 8.5.7     | Market Data quote - Forward, Two-Way<br>65    |\n|   | 8.6 |           | QuoteCancel [Z]<br>.<br>66                    |\n|   |     | 8.6.1     | Customer → 360<br>.<br>66                     |\n|   |     | 8.6.2     | Customer ← 360T<br>.<br>66                    |\n|   | 8.7 |           | NewOrderSingle [D]<br>.<br>66                 |\n|   |     | 8.7.1     | Previously Quoted - Forward<br>66             |\n|   |     | 8.7.2     | Previously Quoted - FX Time Option<br>.<br>67 |\n|   |     | 8.7.3     | Previously Quoted - Swap<br>.<br>68           |\n|   |     | 8.7.4     | Previously Quoted - NDF<br>.<br>68            |\n|   |     | 8.7.5     | Previously Quoted - NDS<br>.<br>69            |\n|   |     | 8.7.6     | Market order - Forward<br>70                  |\n|   |     | 8.7.7     | Limit order - Forward<br>.<br>70              |\n|   | 8.8 |           | NewOrderMultileg [AB]<br>.<br>71              |\n|   |     | 8.8.1     | Previously Quoted - FX Block Trade<br>71      |\n|   |     | 8.8.2     | Market order - NDF Block Trade<br>.<br>72     |\n\n|                      | 8.9<br>ExecutionReport [8]<br>8.9.1<br>Execution report - Order received<br>8.9.2<br>Execution report - Order executed<br>8.10 SecurityDefinitionRequest [c]<br>8.11 SecurityDefinition [d] | .<br>.<br>. | 73<br>73<br>74<br>74<br>75 |  |  |\n|----------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|----------------------------|--|--|\n| 9                    | Firewall Configuration                                                                                                                                                                      |             | 77                         |  |  |\n| 10 FIX Session Reset |                                                                                                                                                                                             |             | 78                         |  |  |\n|                      | 11 Appendix<br>11.1 CFI Codes<br>11.2 Fixing Sources for NDF/NDS                                                                                                                            | .           | 79<br>79<br>79             |  |  |\n|                      | 12 Version Log                                                                                                                                                                              |             | 83                         |  |  |\n\n## **List of Tables**\n\n| 6.1 | Standard Header for FIX Messages<br>14                                                                             |  |\n|-----|--------------------------------------------------------------------------------------------------------------------|--|\n| 6.2 | Standard Footer for FIX Messages<br>.<br>14                                                                        |  |\n| 6.3 | Logon message<br>.<br>15                                                                                           |  |\n| 6.4 | Heartbeat message<br>.<br>15                                                                                       |  |\n| 6.5 | TestRequest message<br>.<br>16                                                                                     |  |\n| 6.6 | ResendRequest message<br>.<br>16                                                                                   |  |\n| 6.7 | Reject message<br>.<br>17                                                                                          |  |\n| 6.8 | SequenceReset message<br>18                                                                                        |  |\n| 6.9 | Logout message<br>18                                                                                               |  |\n|     |                                                                                                                    |  |\n| 7.1 | News message<br>19                                                                                                 |  |\n| 7.2 | QuoteRequest message<br>.<br>28                                                                                    |  |\n| 7.3 | QuoteRequestReject message<br>29                                                                                   |  |\n| 7.4 | BusinessMessageReject message<br>30                                                                                |  |\n| 7.5 | Quote message<br>.<br>33                                                                                           |  |\n| 7.6 | QuoteCancel message from customer to 360T<br>34                                                                    |  |\n| 7.7 | QuoteCancel message from 360T to customer<br>35                                                                    |  |\n| 7.8 | NewOrderSingle message<br>39                                                                                       |  |\n| 7.9 | NewOrderMultileg message<br>.<br>41                                                                                |  |\n|     | 7.10 ExecutionReport message<br>50                                                                                 |  |\n|     | 7.11 SecurityDefinitionRequest message<br>.<br>51                                                                  |  |\n|     | 7.12 SecurityDefinition message<br>52                                                                              |  |\n|     | 7.13 FX tenors<br>.<br>53                                                                                          |  |\n|     |                                                                                                                    |  |\n| 8.1 | Example News message<br>54                                                                                         |  |\n| 8.2 | Example News message<br>54                                                                                         |  |\n| 8.3 | Example QuoteRequest for Spot quotes<br>.<br>55                                                                    |  |\n| 8.4 | Example QuoteRequest for Forward quotes<br>.<br>56                                                                 |  |\n| 8.5 | Example QuoteRequest for Forward quotes<br>.<br>56                                                                 |  |\n| 8.6 | Example QuoteRequest for Swap quotes<br>57                                                                         |  |\n| 8.7 | Example QuoteRequest for NDF quotes<br>.<br>58                                                                     |  |\n| 8.8 | Example QuoteRequest for NDS quotes<br>.<br>58                                                                     |  |\n| 8.9 | Example QuoteRequest for FX Block Trade quotes<br>.<br>59                                                          |  |\n|     | 8.10 Example QuoteRequest for NDF Block Trade quotes<br>.<br>60                                                    |  |\n|     |                                                                                                                    |  |\n|     |                                                                                                                    |  |\n|     | 8.11 Example QuoteRequest for Loan/Deposit quotes<br>61                                                            |  |\n|     | 8.12 Example QuoteRequest for Spot Market data quotes<br>62                                                        |  |\n|     | 8.13 Example BusinessMessageReject message<br>62                                                                   |  |\n|     | 8.14 Example BusinessMessageReject message<br>62                                                                   |  |\n|     | 8.15 Example Spot quote<br>63                                                                                      |  |\n|     | 8.16 Example Forward quote<br>64                                                                                   |  |\n|     | 8.17 Example Forward quote<br>64                                                                                   |  |\n|     | 8.18 Example Forward quote<br>65                                                                                   |  |\n|     | 8.19 Example Forward Market data quote<br>66                                                                       |  |\n|     | 8.20 Example QuoteCancel message from customer<br>.<br>66<br>8.21 Example QuoteCancel message from 360T<br>.<br>66 |  |\n\n|     | 8.22 Example NewOrderSingle message (forward)<br>.                  | 67 |\n|-----|---------------------------------------------------------------------|----|\n|     | 8.23 Example NewOrderSingle message (forward)<br>.                  | 68 |\n|     | 8.24 Example NewOrderSingle message (swap)                          | 68 |\n|     | 8.25 Example NewOrderSingle message (NDF)                           | 69 |\n|     | 8.26 Example NewOrderSingle message (swap)                          | 70 |\n|     | 8.27 Example NewOrderSingle message (Market order)<br>.             | 70 |\n|     | 8.28 Example NewOrderSingle message (Limit order)<br>.              | 71 |\n|     | 8.29 Example NewOrderMultileg message (FX Block Trade)              | 72 |\n|     | 8.30 Example NewOrderMultileg message (NDF Block Trade)             | 73 |\n|     | 8.31 Example ExecutionReport message (confirmation of receipt)<br>. | 73 |\n|     | 8.32 Example ExecutionReport message (execution confirmation)<br>.  | 74 |\n|     | 8.33 Example SecurityDefinitionRequest message<br>.                 | 75 |\n|     | 8.34 Example SecurityDefinition message                             | 76 |\n|     |                                                                     |    |\n| 9.1 | 360T IP addresses                                                   | 77 |\n|     |                                                                     |    |\n|     | 10.1 360T FIX Session<br>.                                          | 78 |\n|     |                                                                     |    |\n|     | 11.1 Supported CFI codes.                                           | 79 |\n|     | 11.2 Supported fixing source string for NDF/NDS.<br>.               | 82 |\n\n![](_page_7_Picture_0.jpeg)\n\n## **List of Figures**\n\n| 5.1 | Basic RFS Message Flow | 13 |\n|-----|------------------------|----|\n|-----|------------------------|----|\n\n## <span id=\"page-8-0\"></span>**2 Product Offering**\n\n360T's FIX offering acts as an intermediary connecting Banks and other market participants. Request For Quote is available through a FIX connection. The aim is to provide sub-second latency from a client's FIX engine to the 360T FX marketplace. During normal operation, the link from the client to the system is entirely automatic (i.e. no human intervention).\n\n## <span id=\"page-8-1\"></span>**2.1 Products currently supported by API**\n\n- 1. FX Spot\n- 2. FX Forward\n- 3. FX NDF\n- 4. FX Swap\n- 5. FX NDS\n- 6. FX Time Options\n- 7. FX Block Trade\n- 8. NDF Block Trade\n- 9. Metals Outright\n- 10. Metals Spread\n- 11. Metals Quarterly Strip\n- 12. MM Loan\n- 13. MM Deposit\n\n## <span id=\"page-8-2\"></span>**2.2 Market data**\n\nThe FIX RFS Market Taker service can also be used to request market data for all the products mentioned above.\n\n## <span id=\"page-9-0\"></span>**3 Important Disclaimer for API Clients**\n\nWhen executing trades over the API, the client has to consider that the counterparties to each trade are the client and the respective provider. Between the trading parties, the client is solely responsible for the performance, accuracy, reliability, completeness, timelines and enforcement of the trade. The client acknowledges that 360T is not a counterparty to the trade and is neither responsible for the clients nor any third partys use of any information transmitted through the API nor is able to negotiate the details of any trade.\n\nThis raises the following issues for clients of the API:\n\n- The client has to ensure that every execution request sent to the API receives a timely and definitive response (either executed or not executed) from the API and is solely responsible for implementing any suitable technical procedures which protects the client against all technical problems that can result in an unclear trade status, including, but not limited to, network disconnections, implementation bugs and incorrect protocol implementations.\n- In addition the client is solely responsible for reconciling trades executed over the API with the respective providers and for contacting the providers directly in the event of any issues relating to specific trades.\n\nIn the event of a trade discrepancy, the client should check the Deal Blotter to determine the status of the trade on the 360T platform. The 360T Technical Support Help will be able to assist if a specific trade cannot be found in the Deal Blotter or if a specific trade initiated over the API needs to be reconciled with a trade shown in the Deal Blotter.\n\n360T IS NOT ABLE TO CANCEL OR EXECUTE A TRADE. However, if requested in writing by both counterparties of the trade, the 360T Technical Support Help Desk is able to change the status displayed in the Deal Blotter for a trade from executed to cancelled. This will also change the status of the trade for the purposes of reporting and statistics. However, for the avoidance of doubt, this action does not cancel the executed trade legally or effectively.\n\n## <span id=\"page-10-0\"></span>**4 Connecting to 360T**\n\nOnce interest has been established in connecting a client to 360T's FIX services, a project manager will be assigned. The project manager will manage the process of connecting the FIX engines of the two companies, performing FIX engine tests, and bringing the connection live.\n\n## <span id=\"page-10-1\"></span>**4.1 Network Connectivity**\n\nOnly incoming connections are supported, i.e. the customer always initiates connections to 360T. Clients may connect to 360T's electronic FX trading system using one of the following options:\n\n### <span id=\"page-10-2\"></span>**4.1.1 Radianz**\n\nIf a client is already connected to Radianz, lead-time to establish connectivity between the client and 360T is ten days, taking into account change control procedures. If the client is not already connected, the lead-time will be advised at the time the order is placed. Radianz will quote a low network latency and will offer a highlevel of encryption for traffic passing through its network. This is the preferred connectivity option. For more information see [http://www.radianz.com](http://www.radianz.com/)\n\n### <span id=\"page-10-3\"></span>**4.1.2 Stunnel**\n\nConnections via Internet should be secured by establishing a Stunnel connection to the 360T data center. Certificates have to be negotiated between the network teams of the client and 360T.\n\n### <span id=\"page-10-4\"></span>**4.1.3 Cross Connect**\n\nCross-connections inside the Equinix London LD4/LD5, Equinix Tokyo TY3 and Equinix New York NY4 data centers are available for enhanced network performance. In London we recommend to order cross-connects to both sites in order to have redundancy in case of failure on one site.\n\n### <span id=\"page-10-5\"></span>**4.1.4 N7**\n\nConnectivity to 360T is available from Deutsche Börse's N7 global exchange network. N7 provides thousands of connections in 32 countries across Europe, North America and Asia. It is built to deliver speed, reliability and performance.\n\n### <span id=\"page-10-6\"></span>**4.1.5 Plain Internet**\n\nA plain internet connection is only available on our testing environments.\n\n## <span id=\"page-10-7\"></span>**4.2 Connection and Firewall Configuration**\n\nSee appendix [9](#page-79-0) [Firewall Configuration](#page-79-0) for the necessary firewall rules to access this 360T service.\n\n## <span id=\"page-11-0\"></span>**4.3 FIX Engine Compatibility Testing**\n\nWe offer clients the ability to test against our FIX engine by connecting over the Internet. This allows compatibility issues to be discovered early in the process. The process for going live has three stages:\n\n### 1. Initial Development & Testing\n\nDuring this stage the client connection will be linked to the 360T development system.\n\n### 2. Conformance Testing\n\nOnce a client is ready to go live, 360T will conduct a series of conformance tests with the client to certify the connection is compatible with the production environment. Conformance testing takes place over the test network connection.\n\n### 3. Switch to Live System\n\nDue to the need to make firewall changes and changes to the proxy servers the switch to the live system will take at least a week. This is because changes to these services can only be performed out of business hours, i.e., at the weekend.\n\n## <span id=\"page-11-1\"></span>**4.4 FIX Protocol Levels**\n\nFIX is the communications protocol 360T and its clients will both have to support to fully communicate with each other. 360T systems are compliant with FIX version 4.4. In addition, some systems support transport layer FIXT.1.1 and application layer FIX.5.0.\n\nOnly the messages and fields relevant for the communication between 360T and its clients are included in this document. Each message described here lists 360T required fields and their supported values plus any fields that result in rejection. Additional fields will be ignored and unsupported field values will be rejected. The official FIX specification, available on [http://www.fixprotocol.org,](http://www.fixprotocol.org) should be consulted for in-depth descriptions.\n\n### <span id=\"page-11-2\"></span>**4.4.1 User Credentials**\n\n360T will provide the values for the fields SenderCompID[49] and TargetCompID[56].[1](#page-11-5) 360T will also provide a password that must be used in the Logon message for the client to be authenticated if needed.\n\n### <span id=\"page-11-3\"></span>**4.4.2 FIX Session Reset**\n\nFor established connections, a FIX session reset is performed according to the schedule detailed in appendix [10](#page-80-0) [FIX Session Reset.](#page-80-0)\n\n## <span id=\"page-11-4\"></span>**4.5 Availability**\n\n- The 360T Production environment is available between Sunday 6 pm America/New York and Friday 5 pm America/New York.\n- Every day during the week the environment has a maintenance window between 5 pm and 6 pm America/New York during which the application may not be available.\n- The non-availability of the 360T platform does not mean that the client will be disconnected from their FIX session(s) for the entire duration of the maintenance window. If the client remains connected during the maintenance window, the functionality of the API will not be available.\n\n<span id=\"page-11-5\"></span><sup>1</sup>Sender/TargetSubID[50/57] or Sender/TargetLocationID[142/143] will be defined if needed\n\n## <span id=\"page-12-0\"></span>**4.6 Trading Hours**\n\nAs defined in the MTF Rulebook: trading hours are between 7 AM Monday (New Zealand/Auckland) and 5 PM Friday (America/New York).\n\nAfter 5 PM Friday (America/New York), the RFS MTF will not accept any new RFS requests.\n\n## <span id=\"page-12-1\"></span>**4.7 Timeout handling**\n\nShould the client not receive an execution report - either rejected or confirmed - for an order that was submitted, it is the responsibility of the client to contact 360T support to clarify the status of the order. This includes cases when the fix connection goes down due to network connectivity or issues either on 360T or on client's side, when the client disconnects shortly after sending an order or any other cases that prevent 360T to send a execution report to the client at all or within the agreed timeout period.\n\n## <span id=\"page-13-0\"></span>**5 Utilization of the FIX Protocol**\n\n## <span id=\"page-13-1\"></span>**5.1 System Messages**\n\nThe following administrative system messages are supported as prescribed by the FIX standard. The supported message types are:\n\n- Logon [A]\n- Heartbeat [0] (interval configured to 30 seconds on 360T side)\n- Test Request [1]\n- Resend Request [2]\n- Reject [3]\n- Sequence Reset [4]\n- Logout [5]\n\n### <span id=\"page-13-2\"></span>**5.2 Business Messages**\n\nThe business messages are used as prescribed by the FIX standard and interpreted in a way that all business relevant actions for RFS can be communicated. The supported message types are:\n\n- News [B]\n- QuoteRequest [R]\n- QuoteRequestReject [AG]\n- Business Message Reject [j]\n- Quote [S]\n- QuoteCancel [Z]\n- NewOrderSingle [D]\n- Execution Report [8]\n- SecurityDefinitionRequest [c]\n- SecurityDefinition [d]\n\n### <span id=\"page-14-0\"></span>**5.3 360T customization details**\n\n### <span id=\"page-14-1\"></span>**5.3.1 Side**\n\nIn the 360t platform sides are relative to base currency (currency 1). For swaps the side is relative to the base currency of the far leg. This hold true by default for all market taker sessions.\n\nThere is a possibility to configure a session to allow all Side tags in the communication - quote requests, quotes, orders and execution reports - to be relative to the notional currency. Please note that in such a case for swaps the side will still be relative to the far side. Also, the side of the product in the 360T platform will still be shown as relative to currency 1. This special configuration aims better interoperability. Please inform 360T if you need this configured during the setup and testing process.\n\n### <span id=\"page-14-2\"></span>**5.3.2 Quote Cancels and Business Rejections**\n\n360T will send a BusinessMessageReject message whenever there is an issue with the previous request, when the platform is unavailable or if there is an issue with the business setup in terms of rights or quote routing.\n\nQuoteCancel messages are sent when a specific quote is canceled, when the quote request expires or as a confirmation of a quote cancel. When a single quote is canceled, the quoteId would be defined in the message. When the whole quote request is canceled, quoteId will be 0.\n\n360T will confirm a QuoteCancel message sent by the client with a QuoteCancel message with quoteId=0. When 360T initiates a quote cancel for a single quote or for the whole stream (upon quote request expiration), the client must not confirm by sending back a QuoteCancel. Doing this will result in a business rejection.\n\n### <span id=\"page-14-3\"></span>**5.4 General Workflow**\n\nThe general workflow for trading through the FIX API is shown in the following diagram\n\n5.4. General Workflow Chapter 5: Utilization of the FIX Protocol\n\n<span id=\"page-15-0\"></span>![](_page_15_Figure_4.jpeg)\n\nFigure 5.1: Basic RFS Message Flow\n\n## <span id=\"page-16-0\"></span>**6 System Messages**\n\n## <span id=\"page-16-1\"></span>**6.1 Notes on important details**\n\n- The customer connects to 360T.\n- All timestamps used use time in UTC.\n\n## <span id=\"page-16-2\"></span>**6.2 Message Header**\n\nEach message is preceded by a standard header. It identifies the message type, length, destination, sequence number, origination point and time.\n\n<span id=\"page-16-4\"></span>\n\n| Tag  | Name          | Type         | Req. | Description                                                                                    |\n|------|---------------|--------------|------|------------------------------------------------------------------------------------------------|\n| 8    | BeginString   | String       | Y    | FIX.4.4 (must be first field in message)                                                       |\n| 9    | BodyLength    | Length       | Y    | must be second field in message                                                                |\n| 35   | MsgType       | String       | Y    | Shown in individual message specifications in this<br>document, must be third field in message |\n| 34   | MsgSeqNum     | SeqNum       | Y    | Message Sequence Number (incremented on 360T<br>side)                                          |\n| 49   | SenderCompID  | String       | Y    | Set to an agreed value                                                                         |\n| 50   | SenderSubID   | String       | Y    | Set to an agreed value. Used to identify the specific<br>message originator.                   |\n| 52   | SendingTime   | UTCTimestamp | Y    | Time of message transmission                                                                   |\n| 56   | TargetCompID  | String       | Y    | Set to an agreed value                                                                         |\n| 1129 | CstmApplVerID | String       | N    | specifies internal API version, defaults to '1'                                                |\n\nTable 6.1: Standard Header for FIX Messages\n\n## <span id=\"page-16-3\"></span>**6.3 Message Footer**\n\nThis footer will be used in all messages sent between 360T and the customer system.\n\n<span id=\"page-16-5\"></span>\n\n| Tag | Name     | Type   | Req. | Description                                                                                                               |\n|-----|----------|--------|------|---------------------------------------------------------------------------------------------------------------------------|\n| 10  | CheckSum | String | Y    | Standard FIX value. Always defined as three charac<br>ters (unencrypted). Must be the last field in every FIX<br>message. |\n\nTable 6.2: Standard Footer for FIX Messages\n\n## <span id=\"page-17-0\"></span>**6.4 Logon [A]**\n\nThe customer starts the communication by sending a Login message. 360T checks the supplied credentials and answers with a Logon reply if successful.\n\n<span id=\"page-17-3\"></span>\n\n| Tag                             | Name                            | Type    | Req.                                                                                                     | Description              |\n|---------------------------------|---------------------------------|---------|----------------------------------------------------------------------------------------------------------|--------------------------|\n|                                 | <messageheader></messageheader> |         | Y                                                                                                        | MsgType <35> = A         |\n| 98                              | EncryptMethod                   | int     | Y                                                                                                        | 0 = 'always unencrypted' |\n| 108                             | HeartBtInt                      | int     | Y                                                                                                        | should be set to 30      |\n| 141                             | ResetSeqNumFlag                 | Boolean | Y                                                                                                        | should be set to 'Y'     |\n| 554<br>Password<br>String       |                                 | Y       | Will be provided by 360T.<br>Note: Without transport-level-encryption, only mini<br>mal security exists. |                          |\n| <messagefooter></messagefooter> |                                 |         | Y                                                                                                        |                          |\n\nTable 6.3: Logon message\n\n## <span id=\"page-17-1\"></span>**6.5 Heartbeat [0]**\n\nThe Heartbeat monitors the status of the communication link and identifies when the last of a string of messages was not received.\n\nWhen either end of a FIX connection has not sent any data for HeartBtInt <108> seconds, it will transmit a Heartbeat message. When either end of the connection has not received any data for (HeartBtInt <108> + \"some reasonable transmission time\") seconds, it will transmit a Test Request message. If there is still no Heartbeat message received after (HeartBtInt <108> + \"some reasonable transmission time\") seconds then the connection is considered lost.\n\n<span id=\"page-17-4\"></span>\n\n| Tag                             | Name                            | Type | Req.                                                                      | Description      |\n|---------------------------------|---------------------------------|------|---------------------------------------------------------------------------|------------------|\n|                                 | <messageheader></messageheader> |      | Y                                                                         | MsgType <35> = 0 |\n| 112<br>TestReqID<br>String      |                                 | N    | Required if the Heartbeat is a response to a TestRe<br>quest [1] message. |                  |\n| <messagefooter></messagefooter> |                                 |      | Y                                                                         |                  |\n\nTable 6.4: Heartbeat message\n\n## <span id=\"page-17-2\"></span>**6.6 TestRequest [1]**\n\nThe Test Request message forces a heartbeat from the opposing application. The Test Request message checks sequence numbers or verifies communication line status. The opposite application responds to the Test Request with a Heartbeat containing the TestReqID <112>.\n\n<span id=\"page-17-5\"></span>\n\n| Tag | Name                            | Type | Req. | Description      |                        |\n|-----|---------------------------------|------|------|------------------|------------------------|\n|     | <messageheader></messageheader> |      | Y    | MsgType <35> = 1 |                        |\n|     |                                 |      |      |                  | Continued on next page |\n\n6.7. ResendRequest [2] Chapter 6: System Messages\n\n| Tag                             | Name      | Type   | Req. | Description                                |\n|---------------------------------|-----------|--------|------|--------------------------------------------|\n| 112                             | TestReqID | String | Y    | Identifier that must be used in the reply. |\n| <messagefooter></messagefooter> |           |        | Y    |                                            |\n\nTable 6.5: TestRequest message\n\n## <span id=\"page-18-0\"></span>**6.7 ResendRequest [2]**\n\nThe resend request is sent by the receiving application to initiate the retransmission of messages. This function is utilized if a sequence number gap is detected, if the receiving application lost a message, or as a function of the initialization process.\n\n<span id=\"page-18-2\"></span>\n\n| Tag                             | Name       | Type | Req.             | Description |\n|---------------------------------|------------|------|------------------|-------------|\n| <messageheader></messageheader> |            | Y    | MsgType <35> = 2 |             |\n| 7                               | BeginSeqNo | int  | Y                |             |\n| 16<br>EndSeqNo<br>int           |            | Y    |                  |             |\n| <messagefooter></messagefooter> |            |      | Y                |             |\n\nTable 6.6: ResendRequest message\n\n- To request a single message: BeginSeqNo <7> = EndSeqNo <16>\n- To request a range of messages: BeginSeqNo <7> = first message of range, EndSeqNo <16> = last message of range\n- To request all messages subsequent to a particular message: BeginSeqNo <7> = first message of range, EndSeqNo <16> = 0 (represents infinity) .\n\n## <span id=\"page-18-1\"></span>**6.8 Reject [3]**\n\nThe Reject message is issued when a message is received but cannot be properly processed due to a session-level rule violation. An example of when a reject may be appropriate would be the receipt of a message with invalid basic data which successfully passes de-encryption, CheckSum <10> and BodyLength <9> checks.\n\n<span id=\"page-18-3\"></span>\n\n| Tag                             | Name       | Type   | Req.             | Description                                      |\n|---------------------------------|------------|--------|------------------|--------------------------------------------------|\n| <messageheader></messageheader> |            | Y      | MsgType <35> = 3 |                                                  |\n| 45                              | RefSeqNum  | int    | Y                | MsgSeqNum <34> of the rejected message           |\n| 58                              | Text       | String | N                | Reason why the message can't be processed        |\n| 371                             | RefTagID   | int    | N                | The tag number of the FIX field being referenced |\n| 372                             | RefMsgType | String | N                | The MsgType of the FIX message being referenced  |\n| Continued on next page          |            |        |                  |                                                  |\n\n6.9. SequenceReset [4] Chapter 6: System Messages\n\n| Tag                             | Name                    | Type | Req. | Description                                               |\n|---------------------------------|-------------------------|------|------|-----------------------------------------------------------|\n| 373                             | SessionRejectReason int |      | N    | Session-level reject reason. Values:                      |\n|                                 |                         |      |      | • '0' = Invalid tag number                                |\n|                                 |                         |      |      | • '1' = Required tag missing                              |\n|                                 |                         |      |      | • '2' = Tag not defined for this message type             |\n|                                 |                         |      |      | • '3' = Undefined Tag                                     |\n|                                 |                         |      |      | • '4' = Tag specified without a value                     |\n|                                 |                         |      |      | • '5' = Value is incorrect (out of range) for this<br>tag |\n|                                 |                         |      |      | • '6' = Incorrect data format for value                   |\n|                                 |                         |      |      | • '7' = Decryption problem                                |\n|                                 |                         |      |      | • '8' = Signature problem                                 |\n|                                 |                         |      |      | • '9' = CompID problem                                    |\n|                                 |                         |      |      | • '10' = SendingTime accuracy problem                     |\n|                                 |                         |      |      | • '11' = Invalid MsgType                                  |\n|                                 |                         |      |      | • '12' = XML Validation error                             |\n|                                 |                         |      |      | • '13' = Tag appears more than once                       |\n|                                 |                         |      |      | • '14' = Tag specified out of required order              |\n|                                 |                         |      |      | • '15' = Repeating group fields out of order              |\n|                                 |                         |      |      | • '16' = Incorrect NumInGroup count                       |\n|                                 |                         |      |      | • '17' = Non \"Data\" value includes field delim<br>iter    |\n|                                 |                         |      |      | • '99' = Other                                            |\n| <messagefooter></messagefooter> |                         | Y    |      |                                                           |\n\nTable 6.7: Reject message\n\n## <span id=\"page-19-0\"></span>**6.9 SequenceReset [4]**\n\n<span id=\"page-19-1\"></span>\n\n| Tag | Name                            | Type | Req. | Description      |                        |\n|-----|---------------------------------|------|------|------------------|------------------------|\n|     | <messageheader></messageheader> |      | Y    | MsgType <35> = 4 |                        |\n|     |                                 |      |      |                  | Continued on next page |\n\n6.10. Logout [5] Chapter 6: System Messages\n\n| Tag                             | Name     | Type | Req. | Description                                                         |\n|---------------------------------|----------|------|------|---------------------------------------------------------------------|\n| 36                              | NewSeqNo | int  | Y    | Sequence number which is expected to be sent in the<br>next message |\n| <messagefooter></messagefooter> |          | Y    |      |                                                                     |\n\nTable 6.8: SequenceReset message\n\n## <span id=\"page-20-0\"></span>**6.10 Logout [5]**\n\nThe Logout message initiates or confirms the termination of a FIX session. Disconnection without the exchange of Logout messages should be interpreted as an abnormal condition.\n\n<span id=\"page-20-1\"></span>\n\n| Tag                             | Name           | Type | Req.             | Description                                                                                    |\n|---------------------------------|----------------|------|------------------|------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                | Y    | MsgType <35> = 5 |                                                                                                |\n| 58                              | Text<br>String |      | N                | This field is used in confirmations only to specify why<br>the message could not be processed. |\n| <messagefooter></messagefooter> |                |      | Y                |                                                                                                |\n\nTable 6.9: Logout message\n\n## <span id=\"page-21-0\"></span>**7 Business Messages**\n\n## <span id=\"page-21-1\"></span>**7.1 News [B]**\n\n### (360T → customer)\n\nThis News message is initialized by customer to request for available providers (aka Bank Basket). The request News message sent from customer should contain the list of requesters who are interested in receiving the Bank Baskets. All requesters are listed in the LinesOfText group.\n\n360T sends a News message back to the customer to inform about the list of available providers. All available providers are listed in the LinesOfText group and the NoRoutingIDs contains the target requester. For each supported product and each requester, an individual News message is sent (e.g. if Spots, Forwards and Swaps are supported, 360T will send three different messages for each requester). The messages are sent to the customer only when requested.\n\n<span id=\"page-21-2\"></span>\n\n| Tag   | Name                            | Type       | Req. | Description                                                                                                                                                                                    |\n|-------|---------------------------------|------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n|       | <messageheader></messageheader> |            | Y    | MsgType <35> = B                                                                                                                                                                               |\n| 148   | Headline                        | String     | Y    | Is set to the name of the product if sent by 360T. Pos<br>sible values are 'Fx Spot', 'Fx Forward', 'Fx Swap',<br>'NDF', 'NDS' and 'Block trade'.<br>Is set to any string if sent by customer. |\n| 33    | LinesOfText                     | NumInGroup | Y    | Number of parties delivered in the group.                                                                                                                                                      |\n| → 58  | Text                            | String     | Y    | Short name of the available provider if sent by 360T.<br>Short name of the requester if sent by customer.                                                                                      |\n| 215   | NoRoutingIDs                    | NumInGroup | N    | Number of News Requestors delivered in the group.<br>For the moment only support 1 requestor. Used only<br>in the message sent by 360T.                                                        |\n| → 216 | RoutingType                     | int        | N    | Indicate the type of Routing.<br>Possible values:<br>• '1' = Target Firm (The value of the RoutingID<br>is sent to one receiver only)                                                          |\n| → 217 | RoutingID                       | String     | N    | Short name of the requestor who expects to receive<br>available providers.                                                                                                                     |\n|       | <messagefooter></messagefooter> |            | Y    |                                                                                                                                                                                                |\n\nTable 7.1: News message\n\n## <span id=\"page-22-0\"></span>**7.2 QuoteRequest [R]**\n\n### (Customer → 360T)\n\nThe QuoteRequest message is sent by the customer to the 360T. Normally it requests the banks to start sending tradeable quotes for the instrument specified in this message.\n\nQuoteRequest is also used to request market data for a specific currency pair and product. Those requests are distinguished by the QuoteType<537> field which can be either '1' to request tradable quotes or '0' to request market data.\n\nNote: The default number of parallel requests is 20. Please contact 360T support if you require a higher number.\n\n<span id=\"page-22-1\"></span>\n\n| Tag    | Name                            | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                       |\n|--------|---------------------------------|--------------|------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n|        | <messageheader></messageheader> |              | Y    | MsgType <35> = R                                                                                                                                                                                                                                                                                                                                                                  |\n| 131    | QuoteReqID                      | String       | Y    | Unique identifier for this request. This field must<br>not be longer than 50 characters.                                                                                                                                                                                                                                                                                          |\n| 146    | NoRelatedSym                    | NumInGroup   | Y    | Number of related symbols in request. Value must<br>always be '1'                                                                                                                                                                                                                                                                                                                 |\n| → 55   | Symbol                          | String       | Y    | Defines the currency pair for FX products, speci<br>fied using two 3-letter ISO 4217 codes separated<br>by a slash ('/') delimiter.<br>For Base Metals,<br>the Symbol<55> field con                                                                                                                                                                                               |\n|        |                                 |              |      | tains the chemical element symbol of the metal<br>(e.g. \"AL\" for aluminium, \"CU\" for copper etc.),<br>apart from \"Iron Ore 100 ton\" and \"Iron Ore 100<br>ton\", which use \"FE1\" and \"FE5\" respectively.                                                                                                                                                                            |\n|        |                                 |              |      | For<br>Money<br>Market<br>(MM)<br>products,<br>the<br>Symbol<55> field defines the currency, specified<br>using one 3-letter ISO 4217 code.                                                                                                                                                                                                                                       |\n| → 541  | MaturityDate                    | LocalMktDate | C    | Defines the Fixing Date for an NDF (or the near<br>leg for an NDS).                                                                                                                                                                                                                                                                                                               |\n|        |                                 |              |      | For<br>Loan/Deposit<br>products,<br>the<br>Maturity<br>Date<541> field defines the end of the loan or<br>deposit's term (required for this product).                                                                                                                                                                                                                              |\n| → 7541 | MaturityDate2                   | LocalMktDate | N    | Defines the far leg Fixing Date for an NDS.                                                                                                                                                                                                                                                                                                                                       |\n| → 106  | Issuer                          | String       | N    | To be populated with the name of a provider that<br>is in the bank basket and from whom quotes are<br>requested.<br>If multiple providers should be in<br>cluded, this field must be a comma-separated list<br>of providers.<br>If not present, all providers from the bank basket<br>are included.<br>When market data is requested (QuoteType = '0'),<br>this field is ignored. |\n|        |                                 |              |      | Continued on next page                                                                                                                                                                                                                                                                                                                                                            |\n\n| Tag   | Name      | Type | Req. | Description                                                                                                                                                          |\n|-------|-----------|------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| → 537 | QuoteType | int  | Y    | Identifies the type of quote.<br>Possible values:<br>• '0' = indicative (request market data. Trad<br>ing will not be possible on those quotes)<br>• '1' = tradeable |\n|       |           |      |      | Continued on next page                                                                                                                                               |\n\n| Tag  | Name | Type | Req. | Description                                                                                                                                                                                                                                |\n|------|------|------|------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| → 54 | Side | char | C    | Defines if the Market Taker is intending to buy<br>or sell the given symbol for FX and Base Metals<br>products.                                                                                                                            |\n|      |      |      |      | • The Side<54> field defines the side of the<br>first currency in the Symbol<55> field (the<br>base currency) and not of the notional cur<br>rency.                                                                                        |\n|      |      |      |      | • For<br>FX<br>Swap<br>and<br>NDS<br>products,<br>the<br>Side<54> field indicates the side of the far<br>leg.                                                                                                                              |\n|      |      |      |      | • If the Side<54> field is not present, then the<br>request will be for a two-sided quote.                                                                                                                                                 |\n|      |      |      |      | For Money Market (MM) products, the Side<54><br>field is mandatory and defines if the client wants<br>to lend or borrow money.                                                                                                             |\n|      |      |      |      | Possible values:                                                                                                                                                                                                                           |\n|      |      |      |      | • '1' = Buy                                                                                                                                                                                                                                |\n|      |      |      |      | • '2' = Sell                                                                                                                                                                                                                               |\n|      |      |      |      | • 'F' = Lend (MM Deposit only)                                                                                                                                                                                                             |\n|      |      |      |      | • 'G' = Borrow (MM Loan only)                                                                                                                                                                                                              |\n|      |      |      |      | • 'B' = As Defined (Block Trades only)                                                                                                                                                                                                     |\n|      |      |      |      | Note:<br>For FX and NDF Block Trades, the Side<54> field<br>is mandatory and must be set to 'B' (As Defined)<br>or its value must match the overall side of the net<br>ted leg quantities (defined using LegSide<624><br>and LegQty<687>). |\n|      |      |      |      | • When 'B' (As Defined) is used, the top<br>level side of the block trade will be auto<br>matically set to the overall side of the net<br>ted legs, or to sell if the netted quantity of<br>the legs is zero.                              |\n|      |      |      |      | • If required, the customer can choose the<br>top-level side ('1' = Buy or '2' = Sell) for<br>zero-netted block trades by providing the<br>desired side in the Side<54> field.                                                             |\n|      |      |      |      | Continued on next page                                                                                                                                                                                                                     |\n\n| Tag     | Name            | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |\n|---------|-----------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| → 38    | OrderQty        | Qty          | Y    | Order quantity (for swaps of near leg). If market<br>data is requested, send '0'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |\n| → 64    | SettlDate       | LocalMktDate | Y    | Trade settlement date. For swaps this is the near<br>leg settlement date. For block trades this should<br>match the RefSpotDate<7070>.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |\n| → 14101 | SplitSettlDate  | LocalMktDate | N    | Split settlement date. For swaps this is the near leg split<br>settlement date. This field refers to the second currency.<br>The SettlDate<64> field (which refers to the first cur<br>rency) must be provided if this tag is used.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |\n| → 193   | SettlDate2      | LocalMktDate | N    | Far settlement date. If present, this must be inter<br>preted as a swap. In the 360T platform the tenor<br>on the far leg is relative to the near leg.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |\n| → 14102 | SplitSettlDate2 | LocalMktDate | N    | Far split settlement date. If present, this must be inter<br>preted as a swap. This field refers to the second cur<br>rency. The SettlDate2<193> field (which refers to the<br>first currency) must be provided if this tag is used.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |\n| → 192   | OrderQty2       | Qty          | N    | Order quantity of far leg for swaps. Mandatory if<br>tradeable quotes for swaps are requested.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |\n| → 15    | Currency        | Currency     | N    | Currency that OrderQty is expressed in. Manda<br>tory if tradeable quotes are requested.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |\n| → 1     | Account         | String       | Y    | If the sender is trading for another legal entity<br>(Trade-As), this field defines the name of this en<br>tity. Otherwise this is the same value as in Sender<br>CompID                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |\n| → 126   | ExpireTime      | UTCTimestamp | C    | The time when this request will expire.<br>• Mandatory field if the QuoteRequest is for<br>tradeable quotes (QuoteType<537> = '1').<br>Expiry<br>times<br>for<br>tradeable<br>quote<br>re<br>quests are limited depending on the product<br>(Spot/Forward/Swap/NDF).<br>This<br>limit<br>is<br>by default 1 minute for Spot and 5 minutes<br>for all other products, but can be configured<br>per customer and product.<br>• Optional field if the QuoteRequest is for<br>indicative quotes (QuoteType<537> = '0').<br>If<br>not<br>provided,<br>the<br>value<br>defaults<br>to<br>the system/account defaults.<br>The expiry<br>time of market data requests is not restricted<br>(long running requests are permitted). |\n\n| 7.2. OuoteRequest [R] |  |\n|-----------------------|--|\n\n| Tag                                                                           | Name                                                                          | Type         | Req. | Description                                                                                                                                  |  |\n|-------------------------------------------------------------------------------|-------------------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------|--|\n| → 78                                                                          | NoAllocs                                                                      | NumInGroup   | N    | Repeating group of allocation instructions. In case<br>of swaps, only one allocation per leg is needed as<br>uneven swaps are not supported. |  |\n| → → 79                                                                        | AllocAccount                                                                  | String       | Y    | Entity for which an amount gets allocated                                                                                                    |  |\n| → → 80                                                                        | AllocQty                                                                      | Qty          | Y    | Amount allocated. Can be negative to indicate in<br>verse to the side given in field 54.                                                     |  |\n| →→ 539                                                                        | NoNestedPartyIDs                                                              | NumInGroup   | N    | Repeating group for MiFID properties of the allocation.                                                                                      |  |\n|                                                                               | NestedPartyID Repeating Group for Investment Decision within Firm (Algorithm) |              |      |                                                                                                                                              |  |\n| →→→ 524                                                                       | NestedPartyID                                                                 | String       | N    | Algorithm-Identifier                                                                                                                         |  |\n| →→→ 525                                                                       | NestedPartyIDSource                                                           | char         | N    | 'D': Proprietary custom code.                                                                                                                |  |\n| →→→ 538                                                                       | NestedPartyRole                                                               | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |\n| →→→ 2384                                                                      | NestedParty<br>RoleQualifier                                                  | int          | N    | '22' = Algorithm                                                                                                                             |  |\n|                                                                               | NestedPartyID Repeating Group for Investment Decision within Firm (Person)    |              |      |                                                                                                                                              |  |\n| →→→ 524                                                                       | NestedPartyID                                                                 | String       | N    | 360T Username                                                                                                                                |  |\n| →→→ 525                                                                       | NestedPartyIDSource                                                           | char         | N    | 'P': Short code identifier                                                                                                                   |  |\n| →→→ 538                                                                       | NestedPartyRole                                                               | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |\n| →→→ 2384                                                                      | NestedParty<br>RoleQualifier                                                  | int          | N    | '24' = Natural Person                                                                                                                        |  |\n| → 555                                                                         | NoLegs                                                                        | NumInGroup   | N    | Repeating group of leg instructions. Used only for non<br>swap multi-leg instruments.                                                        |  |\n| →→ 600                                                                        | LegSymbol                                                                     | String       | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2                                                                               |  |\n| →→ 611                                                                        | LegMaturityDate                                                               | LocalMktDate | N    | Defines the Fixing Date for an NDF Block trade leg.                                                                                          |  |\n| →→ 624                                                                        | LegSide                                                                       | char         | Y    | Defines if the customer wants to buy or sell the given<br>symbol on this leg. Possible values:                                               |  |\n|                                                                               |                                                                               |              |      | • '1' = Buy                                                                                                                                  |  |\n|                                                                               |                                                                               |              |      | • '2' = Sell                                                                                                                                 |  |\n| →→ 687                                                                        | LegQty                                                                        | char         | Y    | The notional amount to be dealt on one leg                                                                                                   |  |\n| →→ 670                                                                        | NoLegAllocs                                                                   | NumInGroup   | Y    | Repeating group of allocations per leg. Only one is ac<br>cepted per leg.                                                                    |  |\n| →→→ 671                                                                       | LegAllocAccount                                                               | String       | Y    | Entity for which an amount gets allocated                                                                                                    |  |\n| →→→ 673                                                                       | LegAllocQty                                                                   | Qty          | Y    | Amount allocated.                                                                                                                            |  |\n| →→→ 539                                                                       | NoNestedPartyIDs                                                              | NumInGroup   | N    | Repeating group for MiFID properties of the leg level<br>allocation.                                                                         |  |\n| NestedPartyID Repeating Group for Investment Decision within Firm (Algorithm) |                                                                               |              |      |                                                                                                                                              |  |\n| →→→→ 524                                                                      | NestedPartyID                                                                 | String       | N    | Algorithm-Identifier                                                                                                                         |  |\n| →→→→ 525                                                                      | NestedPartyIDSource                                                           | char         | N    | 'D': Proprietary custom code.                                                                                                                |  |\n| Continued on next page                                                        |                                                                               |              |      |                                                                                                                                              |  |\n\n| Tag       | Name                                                                       | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                |\n|-----------|----------------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| →→→→ 538  | NestedPartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                                                                                                                                                                                                                                                          |\n| →→→→ 2384 | NestedParty<br>RoleQualifier                                               | int          | N    | '22' = Algorithm                                                                                                                                                                                                                                                                                                                                                           |\n|           | NestedPartyID Repeating Group for Investment Decision within Firm (Person) |              |      |                                                                                                                                                                                                                                                                                                                                                                            |\n| →→→→ 524  | NestedPartyID                                                              | String       | N    | 360T Username                                                                                                                                                                                                                                                                                                                                                              |\n| →→→→ 525  | NestedPartyIDSource                                                        | char         | N    | 'P': Short code identifier                                                                                                                                                                                                                                                                                                                                                 |\n| →→→→ 538  | NestedPartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                                                                                                                                                                                                                                                          |\n| →→→→ 2384 | NestedParty<br>RoleQualifier                                               | int          | N    | '24' = Natural Person                                                                                                                                                                                                                                                                                                                                                      |\n| →→ 654    | LegRefID                                                                   | String       | Y    | Unique reference id of the leg                                                                                                                                                                                                                                                                                                                                             |\n| →→ 588    | LegSettlDate                                                               | LocalMktDate | Y    | Settlement date of one leg                                                                                                                                                                                                                                                                                                                                                 |\n| 453       | NoPartyIDs                                                                 | NumInGroup   | N    | Repeating group containing the participants of the<br>trade. As we have one provider and one requester this<br>will at least 2. If requested we can also send the en<br>tity for which the trade is exported with PartyRole 'In<br>terested Party'. This can be useful in a ITEX scenario<br>where both sides of an internal deal are exported to the<br>same FIX session. |\n| → 448     | PartyID                                                                    | String       | N    | 360T-Individualname or for ALGOs a custom identi<br>fier of the Executing Trader and Invenstment Decision<br>Maker                                                                                                                                                                                                                                                         |\n| → 447     | PartyIDSource                                                              | char         | N    | It can contain one of the following values:<br>• 'D': Proprietary custom code<br>• 'P': Short code identifier                                                                                                                                                                                                                                                              |\n| → 452     | PartyRole                                                                  | int          | N    | Possible values:<br>• '4' = 'Clearing Firm'<br>• '12' = 'Executing Trader'<br>• '122' = 'Investment decision maker'<br>• '63' = 'Systematic Internaliser (SI)'                                                                                                                                                                                                             |\n| → 2376    | PartyRoleQualifier                                                         | int          | N    | '24' = Natural Person                                                                                                                                                                                                                                                                                                                                                      |\n|           | PartyID Repeating Group for Clearing Firm                                  |              |      |                                                                                                                                                                                                                                                                                                                                                                            |\n| → 448     | PartyID                                                                    | String       | N    | Which Clearing house (DCO) this trade will be cleared<br>at.                                                                                                                                                                                                                                                                                                               |\n| → 447     | PartyIDSource                                                              | char         | N    | 'D': Proprietary custom code.                                                                                                                                                                                                                                                                                                                                              |\n| → 452     | PartyRole                                                                  | int          | N    | '4' = Clearing Firm                                                                                                                                                                                                                                                                                                                                                        |\n|           | PartyID Repeating Group for Investment Decision within Firm (Algorithm)    |              |      |                                                                                                                                                                                                                                                                                                                                                                            |\n| → 448     | PartyID                                                                    | String       | N    | Algorithm-Identifier                                                                                                                                                                                                                                                                                                                                                       |\n| → 447     | PartyIDSource                                                              | char         | N    | 'D': Proprietary custom code.                                                                                                                                                                                                                                                                                                                                              |\n|           |                                                                            |              |      | Continued on next page                                                                                                                                                                                                                                                                                                                                                     |\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 28\n\n| Tag                                                        | Name                                                                 | Type         | Req. | Description                                                                                                                                  |  |\n|------------------------------------------------------------|----------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------|--|\n| → 452                                                      | PartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |\n| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '22' = Algorithm                                                                                                                             |  |\n|                                                            | PartyID Repeating Group for Investment Decision within Firm (Person) |              |      |                                                                                                                                              |  |\n| → 448                                                      | PartyID                                                              | String       | N    | 360T Username                                                                                                                                |  |\n| → 447                                                      | PartyIDSource                                                        | char         | N    | 'P': Short code identifier                                                                                                                   |  |\n| → 452                                                      | PartyRole                                                            | int          | N    | '122' = Investment Decision Maker                                                                                                            |  |\n| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '24' = Natural Person                                                                                                                        |  |\n|                                                            | PartyID Repeating Group for Execution within Firm (Algorithm)        |              |      |                                                                                                                                              |  |\n| → 448                                                      | PartyID                                                              | String       | N    | Algorithm-Identifier                                                                                                                         |  |\n| → 447                                                      | PartyIDSource                                                        | char         | N    | 'D': Proprietary custom code.                                                                                                                |  |\n| → 452                                                      | PartyRole                                                            | int          | N    | '12' = Executing Trader                                                                                                                      |  |\n| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '22' = Algorithm                                                                                                                             |  |\n| PartyID Repeating Group for Execution within Firm (Person) |                                                                      |              |      |                                                                                                                                              |  |\n| → 448                                                      | PartyID                                                              | String       | N    | 360T Username                                                                                                                                |  |\n| → 447                                                      | PartyIDSource                                                        | char         | N    | 'P': Short code identifier                                                                                                                   |  |\n| → 452                                                      | PartyRole                                                            | int          | N    | '12' = Executing Trader                                                                                                                      |  |\n| → 2376                                                     | PartyRoleQualifier                                                   | int          | N    | '24' = Natural Person                                                                                                                        |  |\n|                                                            | PartyID Repeating Group for SI-Requests                              |              |      |                                                                                                                                              |  |\n| → 448                                                      | PartyID                                                              | String       | N    | MIC of Systematic Internaliser / Requester                                                                                                   |  |\n| → 447                                                      | PartyIDSource                                                        | char         | N    | 'G': MIC                                                                                                                                     |  |\n| → 452                                                      | PartyRole                                                            | int          | N    | '63' = Systematic Internaliser (SI)                                                                                                          |  |\n| 2593                                                       | NoOrderAttributes                                                    | NumInGroup   | N    | OrderAttributes for Commodity-Derivatives                                                                                                    |  |\n| → 2594                                                     | OrderAttributeType                                                   | int          | N    | '3' = Risk reduction order                                                                                                                   |  |\n| → 2595                                                     | OrderAttributeValue                                                  | String       | N    | Possible values:                                                                                                                             |  |\n|                                                            |                                                                      |              |      | • 'Y' = Risk Decreasing                                                                                                                      |  |\n|                                                            |                                                                      |              |      | • 'N' = Risk Increasing                                                                                                                      |  |\n| 9515                                                       | OptionDate                                                           | LocalMktDate | N    | Defines the end date of the Time Option (for FX Time<br>Options).                                                                            |  |\n| 7070                                                       | RefSpotDate                                                          | LocalMktDate | N    | Defines the spot date in the 360T financial calender.<br>This value is used to clarify if both sides have the same<br>definition for a spot. |  |\n|                                                            |                                                                      |              |      | Continued on next page                                                                                                                       |  |\n\n| Tag    | Name                            | Type       | Req. | Description                                                                                |\n|--------|---------------------------------|------------|------|--------------------------------------------------------------------------------------------|\n| 7071   | ProductType                     | String     | Y    | Custom field defining the requested product.                                               |\n|        |                                 |            |      | Possible values:                                                                           |\n|        |                                 |            |      | • 'FX-STD': Spot, Forward, Swap, NDF and NDS                                               |\n|        |                                 |            |      | • 'FX-BT': Block trade                                                                     |\n|        |                                 |            |      | • 'Metals Outright': Metals Outright                                                       |\n|        |                                 |            |      | • 'Metals Spread': Metals Spread                                                           |\n|        |                                 |            |      | • 'Metals Quarterly Strip': Metals Quarterly Strip                                         |\n|        |                                 |            |      | • 'MM': Loan/Deposit                                                                       |\n| 7611   | ExecutionVenueType              | String     | N    | Used to determine the trading venue on which the re<br>quest will be processed.            |\n|        |                                 |            |      | • '2' = OFF facility / OTC (default)                                                       |\n|        |                                 |            |      | • '3' = MTF                                                                                |\n|        |                                 |            |      | • '7' = SG RMO                                                                             |\n| 29     | LastCapacity                    | int        | N    | Tradingcapacity - Values are:                                                              |\n|        |                                 |            |      | • '1' = AOTC - Any other capacity                                                          |\n|        |                                 |            |      | • '3' = MTCH - Matched principal                                                           |\n|        |                                 |            |      | • '4' = DEAL - Dealing on own account                                                      |\n| 7072   | DayCount                        | String     | C    | Defines the day count convention of the interest rate for                                  |\n|        |                                 |            |      | Loan/Deposit requests (required field for this product).                                   |\n|        |                                 |            |      | Possible values:                                                                           |\n|        |                                 |            |      | • 1/1                                                                                      |\n|        |                                 |            |      | • ACT/365F                                                                                 |\n|        |                                 |            |      | • ACT/360                                                                                  |\n|        |                                 |            |      | • ACT/ACT                                                                                  |\n|        |                                 |            |      | • 30/360                                                                                   |\n|        |                                 |            |      | • 30E/360                                                                                  |\n|        |                                 |            |      | • BUS/252                                                                                  |\n| 7075   | FixingReference                 | String     | N    | Fixing reference for NDF, NDS or NDF block (if prod<br>uct supported in this API version). |\n| 7546   | NoCustomFields                  | NumInGroup | N    | Number of custom fields (group).                                                           |\n| → 7547 | CustomFieldName                 | String     | C    | Name of this custom field (required when custom fields<br>are used).                       |\n| → 7548 | CustomFieldValue                | String     | C    | Value of this custom field (required when custom fields<br>are used).                      |\n|        | <messagefooter></messagefooter> |            | Y    |                                                                                            |\n|        |                                 |            |      | Continued on next page                                                                     |\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 30\n\n| Tag | Name | Type | Req. | Description |\n|-----|------|------|------|-------------|\n|     |      |      |      |             |\n\nTable 7.2: QuoteRequest message\n\n### 7.3. QuoteRequestReject [AG] Chapter 7: Business Messages\n\n## <span id=\"page-31-0\"></span>**7.3 QuoteRequestReject [AG]**\n\n### (360T → customer)\n\nThe QuoteRequestReject message is sent by the 360T to the customer to indicate that a QuoteRequest was rejected. In case of message-level issues and certain field validation error a BusinessMessageReject [j] may be sent instead.\n\n<span id=\"page-31-1\"></span>\n\n| Tag                             | Name                         | Type       | Req. | Description                                                                         |\n|---------------------------------|------------------------------|------------|------|-------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                              |            | Y    | MsgType <35> = AG                                                                   |\n| 131                             | QuoteReqID                   | String     | Y    | Unique identifier for the rejected request                                          |\n| 658                             | QuoteRequest<br>RejectReason | int        | Y    | Reason why the quote request from 360T was re<br>jected. Will always be '99': Other |\n| 146                             | NoRelatedSym                 | NumInGroup | Y    | Number of related symbols. Always '1'                                               |\n| →55                             | Symbol                       | String     | Y    | Contains the requested symbol.                                                      |\n| →537                            | QuoteType                    | int        | Y    | Identifies the requested type of quote.                                             |\n| →15                             | Currency                     | String     | N    | Contains the requested notional currency.                                           |\n| 58                              | Text                         | String     | Y    | Reason why the request was rejected as text                                         |\n| <messagefooter></messagefooter> |                              |            | Y    |                                                                                     |\n\nTable 7.3: QuoteRequestReject message\n\n## <span id=\"page-32-0\"></span>**7.4 BusinessMessageReject [j]**\n\n### (360T → customer)\n\nA Business Message Reject can be sent from 360T to the customer to inform that a received message was semantically not correct and therefore couldn't get processed.\n\n<span id=\"page-32-1\"></span>\n\n| Tag                             | Name                       | Type   | Req.             | Description                                                                                                          |\n|---------------------------------|----------------------------|--------|------------------|----------------------------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                            | Y      | MsgType <35> = j |                                                                                                                      |\n| 45                              | RefSeqNum                  | SeqNum | Y                | MsgSeqNum of rejected message.                                                                                       |\n| 372                             | RefMsgType                 | String | Y                | The MsgType of the FIX message being referenced.                                                                     |\n| 379                             | BusinessRejectRefID String |        | N                | The value of the business-level 'ID' field on the mes<br>sage being referenced.                                      |\n| 380                             | BusinessReject<br>Reason   | int    | Y                | Code to identify reason for a BusinessMessageReject<br>message:<br>• '0' = Other<br>• '3' = Unsupported message type |\n|                                 |                            |        |                  | • '5' = Conditionally required field missing                                                                         |\n| 58                              | Text                       | String | N                | Reason why business message can't be processed                                                                       |\n| <messagefooter></messagefooter> |                            |        | Y                |                                                                                                                      |\n\nTable 7.4: BusinessMessageReject message\n\n## <span id=\"page-33-0\"></span>**7.5 Quote [S]**\n\n(360T → customer)\n\nThis message is sent from 360T to the customer to answer a quote request\n\n<span id=\"page-33-1\"></span>\n\n| Tag   | Name                            | Type         | Req. | Description                                                                                                                                                            |\n|-------|---------------------------------|--------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n|       | <messageheader></messageheader> |              | Y    | MsgType <35> = S                                                                                                                                                       |\n| 131   | QuoteReqID                      | String       | Y    | Unique ID which refers back to the customer's re<br>quest                                                                                                              |\n| 117   | QuoteID                         | String       | Y    | Unique ID set by the quote's originator for each<br>quote. Its length would be not more than 15 char<br>acters longer than the length of the referenced<br>QuoteReqID. |\n| 537   | QuoteType                       | int          | N    | Identifies the type of quote. Defaults to 1 (Trade<br>able) if missing.<br>• '0' = Indicative (no trading possible)                                                    |\n|       |                                 |              |      | • '1' = Tradeable                                                                                                                                                      |\n| 55    | Symbol                          | String       | Y    | Contains the requested symbol.                                                                                                                                         |\n| 106   | Issuer                          | String       | Y    | Specifies the TEX name of the bank that sent this<br>quote or 'MarketData' if the quote request was a<br>market data request.                                          |\n| 38    | OrderQty                        | Qty          | Y    | Requested size, for swaps of near leg. If the quote<br>type is indicative, this will be '0'                                                                            |\n| 192   | OrderQty2                       | Qty          | N    | Requested size of far leg. Mandatory for swaps if<br>the quote type is 'tradeable'                                                                                     |\n| 15    | Currency                        | Currency     | N    | Currency that the OrderQty<38> is expressed in.<br>Mandatory for tradeable quotes.                                                                                     |\n| 1     | Account                         | String       | C    | Echoed<br>from<br>the<br>QuoteRequest<br>for<br>tradable<br>quotes.                                                                                                    |\n| 555   | NoLegs                          | NumInGroup   | N    | Repeating group of leg instructions. Used only for<br>non-swap multi-leg instruments.                                                                                  |\n| →600  | LegSymbol                       | String       | Y    | Contains the delivered currency pair in the format:<br>CC1/CC2                                                                                                         |\n| → 611 | LegMaturityDate                 | LocalMktDate | N    | Defines the Fixing Date for an NDF Block trade<br>leg.                                                                                                                 |\n| →687  | LegQty                          | Qty          | Y    | The notional amount to be dealt on the leg.                                                                                                                            |\n| →588  | LegSettlDate                    | LocalMktDate | N    | The settlement date for the leg.                                                                                                                                       |\n| →681  | LegBidPx                        | Price        | N    | Defines the All-In-Bid-rate for the leg.                                                                                                                               |\n| →684  | LegOfferPx                      | String       | N    | Defines the All-In-Offer-rate for the leg.                                                                                                                             |\n|       |                                 |              |      | Continued on next page                                                                                                                                                 |\n\n7.5. Quote [S] Chapter 7: Business Messages\n\n| Tag    | Name                          | Type         | Req. | Description                                                                                                                                                                                                                                     |\n|--------|-------------------------------|--------------|------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| →654   | LegRefID                      | String       | Y    | Unique reference id of the leg. Echoed from the<br>quote request.                                                                                                                                                                               |\n| → 7652 | LegMidPx                      | String       | N    | The midprice for this leg                                                                                                                                                                                                                       |\n| → 6050 | BidPx2                        | Price        | N    | Defines the far leg bid forward rate for an FX Swap.                                                                                                                                                                                            |\n| → 6051 | OfferPx2                      | Price        | N    | Defines the far leg offer forward rate for an FX Swap.                                                                                                                                                                                          |\n| 132    | BidPx                         | Price        | N    | Defines the bid forward rate for forwards.<br>For<br>swaps this field defines the composite near leg<br>rate to an offer rate in the far leg. So if OfferPx2<br>is set, this value must be set too.                                             |\n|        |                               |              |      | For<br>a<br>Deposit,<br>this<br>field<br>defines<br>the<br>Interest<br>Rate that the provider (the borrower) will bid.<br>The interest rate may have up to 4 decimal places.                                                                    |\n| 133    | OfferPx                       | Price        | N    | Defines the offer forward rate for forwards.<br>For<br>swaps this field defines the composite near leg<br>rate to a bid rate in the far leg. So if BidPx2 is set,<br>this value must be set too.                                                |\n|        |                               |              |      | For<br>a<br>Loan,<br>this<br>field<br>defines<br>the<br>Interest<br>Rate that the provider (the lender) will offer. The<br>interest rate may have up to 4 decimal places.                                                                       |\n| 188    | BidSpotRate                   | Price        | N    | Defines the bid reference spot rate.<br>For Swaps,<br>this value defines the reference spot rate of the<br>near leg rate to an offer rate in the far leg.<br>So<br>if OfferPx2 and BidPx are set, this value must be<br>set, too.               |\n| 190    | OfferSpotRate                 | Price        | N    | Defines the offer reference spot rate. For Swaps<br>this value defines the reference spot rate of the<br>near leg to a bid rate in the far leg. So if BidPx2<br>and OfferPx are set, this value must be set, too.                               |\n| 631    | MidPx                         | Price        | N    | MidPrice (near leg of Swaps)                                                                                                                                                                                                                    |\n| 7650   | MidSpotRate                   | Price        | N    | Mid spot rate                                                                                                                                                                                                                                   |\n| 7651   | MidPx2                        | Price        | N    | MidPrice of far leg (Swaps)                                                                                                                                                                                                                     |\n| 7084   | BidInterestAtMaturity         | Price        | N    | Amount of interest (i.e.<br>lump-sum) at maturity to be<br>paid by the bank to the requester. Populated for Deposit<br>only.                                                                                                                    |\n| 7085   | OfferInterestAtMaturity Price |              | N    | Amount of interest (i.e.<br>lump-sum) at maturity to be<br>paid by the client to the bank. Populated for Loan only.                                                                                                                             |\n| 62     | ValidUntilTime                | UTCTimestamp | N    | The<br>time<br>UTC<br>with<br>format<br>'yyyyMMdd<br>HH:mm:ss' when the quote will expire.<br>If this<br>field is not set then the Quote will expire at the<br>end of RFS or the QuoteRequest's expire time.<br>Only sent for tradeable quotes. |\n|        |                               |              |      | Continued on next page                                                                                                                                                                                                                          |\n\n![](_page_35_Picture_0.jpeg)\n\n### 7.5. Quote [S] Chapter 7: Business Messages\n\n| Tag                             | Name         | Type         | Req. | Description                                                                                                                                                                                                                                                               |  |\n|---------------------------------|--------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n| 7071                            | ProductType  | String       | N    | Custom field defining the requested product.<br>Possible values:<br>• 'FX-STD': Spot, Forward, Swap, NDF and NDS<br>Note:<br>• This field is only populated for indicative quotes.<br>• Block trades and base metals products are not<br>supported for indicative quotes. |  |\n| 9514                            | OptionPeriod | String       | N    | Option Period (for FX Time Option)                                                                                                                                                                                                                                        |  |\n| 9515                            | OptionDate   | LocalMktDate | N    | Option Date (for FX Time Option)                                                                                                                                                                                                                                          |  |\n| 7070                            | RefSpotDate  | LocalMktDate | N    | Defines the spot date in the 360T financial calender.<br>This value is used to clarify if both sides have the same<br>definition for a spot. Note: This field is only populated<br>for tradeable quotes.                                                                  |  |\n| <messagefooter></messagefooter> |              |              | Y    |                                                                                                                                                                                                                                                                           |  |\n\nTable 7.5: Quote message\n\n## <span id=\"page-36-0\"></span>**7.6 QuoteCancel [Z]**\n\nThe QuoteCancel message can be sent both from 360T to the customer and also from the customer to 360T.\n\n### <span id=\"page-36-1\"></span>**7.6.1 Customer** → **360T**\n\nThe customer sends a QuoteCancel message to cancel a previous QuoteRequest. The quote request message to be canceled is specified by the QuoteRequestID.\n\n<span id=\"page-36-3\"></span>\n\n| Tag                             | Name            | Type   | Req.             | Description                                                                                                                                                                                                                                                                                                               |\n|---------------------------------|-----------------|--------|------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                 | Y      | MsgType <35> = Z |                                                                                                                                                                                                                                                                                                                           |\n| 117                             | QuoteID         | String | Y                | Always set to '0' as always the complete quote re<br>quest will be withdrawn.                                                                                                                                                                                                                                             |\n| 131                             | QuoteReqID      | String | Y                | Unique ID that refers back to the original quote re<br>quest.                                                                                                                                                                                                                                                             |\n| 298                             | QuoteCancelType | int    | Y                | Always set to '4': Cancel all quotes for the request.                                                                                                                                                                                                                                                                     |\n| 7071                            | ProductType     | String | Y                | Custom field defining the requested product.<br>Possible values:<br>• 'FX-STD': Spot, Forward, Swap, NDF and NDS<br>• 'FX-BT': Block trade<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip':<br>Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit |\n| <messagefooter></messagefooter> |                 | Y      |                  |                                                                                                                                                                                                                                                                                                                           |\n\nTable 7.6: QuoteCancel message from customer to 360T\n\n### <span id=\"page-36-2\"></span>**7.6.2 Customer** ← **360T**\n\n360T sends a QuoteCancel message to the customer to indicate that a previously sent quote is revoked. This quote is specified by the corresponding QuoteID field. If the value of QuoteID is 0, then the whole request is canceled. If the value of QuoteID is \"MARKET-DATA\", then this message is an indication that indicative quotes are temporarily not available. In this case the request is not canceled.\n\n<span id=\"page-36-4\"></span>\n\n| Tag                             | Name                   | Type   | Req.             | Description                                                                                                                                                                                                  |  |\n|---------------------------------|------------------------|--------|------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n| <messageheader></messageheader> |                        | Y      | MsgType <35> = Z |                                                                                                                                                                                                              |  |\n| 117                             | QuoteID                | String | Y                | ID<br>of<br>the<br>specific<br>quote<br>that<br>is<br>canceled.<br>'0'<br>means that the complete quote request is canceled.<br>'MARKET-DATA' means that indicative quotes are<br>temporarily not available. |  |\n|                                 | Continued on next page |        |                  |                                                                                                                                                                                                              |  |\n\n7.6. QuoteCancel [Z] Chapter 7: Business Messages\n\n| Tag                             | Name            | Type   | Req. | Description                                                                                                                                                                                                                                                                                                                                                    |\n|---------------------------------|-----------------|--------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 131                             | QuoteReqID      | String | Y    | Unique ID that refers back to the original quote re<br>quest.                                                                                                                                                                                                                                                                                                  |\n| 298                             | QuoteCancelType | int    | Y    | Always set to '1': Cancel for Symbol.                                                                                                                                                                                                                                                                                                                          |\n| 7071                            | ProductType     | String | Y    | Custom field defining the requested product.<br>Possible values:<br>• 'FX-STD': FX Spot, FX Forward and FX Swap<br>• 'FX-NDF': NDF<br>• 'FX-NDS': NDS<br>• 'FX-BT': Block trade<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip':<br>Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit |\n| <messagefooter></messagefooter> |                 |        | Y    |                                                                                                                                                                                                                                                                                                                                                                |\n\nTable 7.7: QuoteCancel message from 360T to customer\n\n## <span id=\"page-38-0\"></span>**7.7 New Order Single [D]**\n\n### (Customer → 360T)\n\nThe customer sends a New Order Single message to execute a price. A NewOrderSingle message is limit-based with reference to the limit-rate of the referenced quote. 360T responds within 10 seconds with an Execution Report message to inform about the final status of the execution.\n\n### Note: If you don't receive an ExecutionReport with state Filled or Rejected within 10 seconds, you must immediately contact 360T support to clarify the deal status.\n\n<span id=\"page-38-1\"></span>\n\n| Tag                             | Name           | Type         | Req.             | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |\n|---------------------------------|----------------|--------------|------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                | Y            | MsgType <35> = D |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |\n| 11                              | ClOrdID        | String       | Y                | Unique identifier designated by the customer and sent<br>to 360T.                                                                                                                                                                                                                                                                                                                                                                                                                                                           |\n| 1                               | Account        | String       | Y                | If the sender is trading for another legal entity (Trade<br>As), this field defines the name of this entity. Other<br>wise this is the same value as in SenderCompID.                                                                                                                                                                                                                                                                                                                                                       |\n| 64                              | SettlDate      | LocalMktDate | Y                | Trade settlement date (for swaps of near leg)                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |\n| 14101                           | SplitSettlDate | LocalMktDate | N                | Split settlement date.<br>Must match the value in the<br>QuoteRequest <r>.</r>                                                                                                                                                                                                                                                                                                                                                                                                                                              |\n| 55                              | Symbol         | String       | Y                | Defines the currency pair for FX products, specified<br>using two 3-letter ISO 4217 codes separated by a<br>slash ('/') delimiter.<br>For Base Metals,<br>the Symbol<55> field contains<br>the chemical element symbol of the metal (e.g. \"AL\"<br>for aluminium, \"CU\" for copper etc.), apart from<br>\"Iron Ore 100 ton\" and \"Iron Ore 100 ton\", which use<br>\"FE1\" and \"FE5\" respectively.<br>For Money Market (MM) products, the Symbol<55><br>field defines the currency, specified using one 3-letter<br>ISO 4217 code. |\n| 541                             | MaturityDate   | LocalMktDate | C                | Must be the same date as in the corresponding quote<br>request. Defines the Fixing Date for an NDF (or the<br>near leg for an NDS).<br>For Loan/Deposit products, the MaturityDate<541><br>field defines the end of the loan or deposit's term<br>(required for this product).                                                                                                                                                                                                                                              |\n| 7541                            | MaturityDate2  | LocalMktDate | N                | Far leg maturity date if the order is an NDS. Must be the<br>same date as in the corresponding quote request.                                                                                                                                                                                                                                                                                                                                                                                                               |\n| 106                             | Issuer         | String       | N                | Issuer of the original quote that is supposed to be ex<br>ecuted.                                                                                                                                                                                                                                                                                                                                                                                                                                                           |\n| Continued on next page          |                |              |                  |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |\n\n| Tag | Name         | Type         | Req. | Description                                                                                                                                      |\n|-----|--------------|--------------|------|--------------------------------------------------------------------------------------------------------------------------------------------------|\n| 54  | Side         | char         | Y    | Defines if the Market Taker is intending to buy or sell<br>the given symbol for FX and Base Metals products.                                     |\n|     |              |              |      | • The Side<54> field defines the side of the first<br>currency in the Symbol<55> field (the base cur<br>rency) and not of the notional currency. |\n|     |              |              |      | • For FX Swap and NDS products, the Side<54><br>field indicates the side of the far leg.                                                         |\n|     |              |              |      | For Money Market (MM) products, the Side<54><br>field is mandatory and defines if the client wants to<br>lend or borrow money.                   |\n|     |              |              |      | Possible values:                                                                                                                                 |\n|     |              |              |      | • '1' = Buy                                                                                                                                      |\n|     |              |              |      | • '2' = Sell                                                                                                                                     |\n|     |              |              |      | • 'F' = Lend (MM Deposit only)                                                                                                                   |\n|     |              |              |      | • 'G' = Borrow (MM Loan only)                                                                                                                    |\n| 60  | TransactTime | UTCTimestamp | Y    | Time this order request was initiated/released by the<br>trader, trading system, or intermediary.                                                |\n| 38  | OrderQty     | Qty          | Y    | Defines the notional amount in the notional currency,<br>for which the customer wants to execute.                                                |\n| 40  | OrdType      | char         | Y    | Defines type of order. Possible values:                                                                                                          |\n|     |              |              |      | • 'D' = Previously quoted                                                                                                                        |\n|     |              |              |      | • '1' = Market order                                                                                                                             |\n|     |              |              |      | • '2' = Limit order (only for FX Spot and For<br>ward)                                                                                           |\n| 44  | Price        | Price        | C    | Price for which the spot, forward or near leg of a swap<br>has to be executed. If OrdType = '2' (Limit order), the<br>field is mandatory.        |\n| 15  | Currency     | Currency     | Y    | Notional currency, in which the OrdQty <38> is de<br>fined.                                                                                      |\n|     |              |              |      | Continued on next page                                                                                                                           |\n|     |              |              |      |                                                                                                                                                  |\n\n7.7. New Order Single [D] Chapter 7: Business Messages\n\n| Tag                    | Name            | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                   |\n|------------------------|-----------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 117                    | QuoteID         | String       | Y    | Mandatory field. For Limit and Market orders, it has<br>to be populated with QuoteRequestId <131> of the re<br>ferred quote request. For Previously Quoted orders, it<br>contains the QuoteID <117> of the referenced quote.<br>If Issuer <106> is not defined, then only the rate is<br>taken from the referenced quote, which would not<br>guarantee execution with the provider from whom it<br>came from. |\n| 193                    | SettlDate2      | LocalMktDate | N    | Far settlement date.<br>If present, this must be inter<br>preted as a swap. In the 360T platform the tenor on<br>the far leg is relative to the near leg.                                                                                                                                                                                                                                                     |\n| 14102                  | SplitSettlDate2 | LocalMktDate | N    | Far split settlement date.<br>Must match the value in the<br>QuoteRequest <r>.</r>                                                                                                                                                                                                                                                                                                                            |\n| 192                    | OrderQty2       | Qty          | N    | Defines the notional amount in the notional currency,<br>for which the customer wants to execute. (FarLeg for<br>Swaps)                                                                                                                                                                                                                                                                                       |\n| 7071                   | ProductType     | String       | C    | Custom field defining the requested product.<br>Possible values:<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip':<br>Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit<br>Note:<br>• Required for Money Market and Base Metals prod<br>ucts.                                                                                         |\n| 7072                   | DayCount        | String       | C    | Defines the day count convention of the interest rate for<br>Loan/Deposit requests (required field for this product).<br>Possible values:<br>• 1/1<br>• ACT/365F<br>• ACT/360<br>• ACT/ACT<br>• 30/360<br>• 30E/360<br>• BUS/252                                                                                                                                                                              |\n| 7075                   | FixingReference | String       | N    | Fixing reference for NDF, NDS or NDF block (if product<br>supported in this API version).                                                                                                                                                                                                                                                                                                                     |\n| Continued on next page |                 |              |      |                                                                                                                                                                                                                                                                                                                                                                                                               |\n\n7.7. New Order Single [D] Chapter 7: Business Messages\n\n| Tag                             | Name       | Type         | Req. | Description                                                                                                                              |\n|---------------------------------|------------|--------------|------|------------------------------------------------------------------------------------------------------------------------------------------|\n| 9515                            | OptionDate | LocalMktDate | N    | Option Date, it is the end date of Time Option. Must be the<br>same date as in the corresponding quote request. (for FX<br>Time Option). |\n| <messagefooter></messagefooter> |            |              | Y    |                                                                                                                                          |\n\nTable 7.8: NewOrderSingle message\n\n## <span id=\"page-42-0\"></span>**7.8 New Order Multileg [AB]**\n\n### (Customer → 360T)\n\nThe customer must use the NewOrderMultileg<AB> message to execute a block trade price. As with the New Order Single message, the NewOrderMultileg message is limit-based with reference to the limit-rate of the referenced quote. 360T responds within 10 seconds with an Execution Report message to inform about the final status of the execution.\n\n### Note: If you don't receive an ExecutionReport with state Filled or Rejected within 10 seconds, you must immediately contact 360T support to clarify the deal status.\n\n<span id=\"page-42-1\"></span>\n\n| Tag                             | Name            | Type         | Req.              | Description                                                                                                                                                                                                                                  |\n|---------------------------------|-----------------|--------------|-------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                 | Y            | MsgType <35> = AB |                                                                                                                                                                                                                                              |\n| 11                              | ClOrdID         | String       | Y                 | Unique identifier designated by the customer and sent<br>to 360T.                                                                                                                                                                            |\n| 1                               | Account         | String       | Y                 | If the sender is trading for another legal entity (Trade<br>As), this field defines the name of this entity. Other<br>wise this is the same value as in SenderCompID. For<br>multi-allocations, this would be the parent entity.             |\n| 54                              | Side            | char         | Y                 | Defines if the Market Taker is intending to buy or sell<br>the given symbol.2 Possible values:                                                                                                                                               |\n|                                 |                 |              |                   | • '1' = Buy                                                                                                                                                                                                                                  |\n|                                 |                 |              |                   | • '2' = Sell                                                                                                                                                                                                                                 |\n|                                 |                 |              |                   | • 'B' = As Defined (for use with block trades)                                                                                                                                                                                               |\n|                                 |                 |              |                   | For block trades, the Side<54> must either be set to<br>'B' = As Defined or its value must match the overall<br>side of the netted leg quantities (defined in each leg<br>using LegSide<624> and LegQty<687>).                               |\n|                                 |                 |              |                   | For zero-netted block trades, if the customer specified<br>the top-level side to use in the QuoteRequest <r><br/>(either '1' = Buy or '2' = Sell), then the same side<br/>must be provided in the NewOrderMultileg<ab><br/>message.</ab></r> |\n| 55                              | Symbol          | String       | Y                 | Contains the delivered currency pair in the format:<br>CC1/CC2.                                                                                                                                                                              |\n| 106                             | Issuer          | String       | N                 | Issuer of the original quote that is supposed to be ex<br>ecuted.                                                                                                                                                                            |\n| 555                             | NoLegs          | NumInGroup   | Y                 | Repeating group containing individual amounts on<br>legs.                                                                                                                                                                                    |\n| → 600                           | LegSymbol       | String       | Y                 | Contains the delivered currency pair in the format:<br>CC1/CC2                                                                                                                                                                               |\n| → 611                           | LegMaturityDate | LocalMktDate | N                 | Defines the Fixing Date for an NDF Block trade leg.                                                                                                                                                                                          |\n| Continued on next page          |                 |              |                   |                                                                                                                                                                                                                                              |\n\n<span id=\"page-42-2\"></span><sup>2</sup>The Side<54> tag defines the direction of the Symbol, i.e. the side of the first currency in the symbol and not of the notional currency.\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 43\n\n7.8. New Order Multileg [AB] Chapter 7: Business Messages\n\n| Tag                             | Name            | Type         | Req. | Description                                                                                                                                                                                                                                                                                  |\n|---------------------------------|-----------------|--------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| → 624                           | LegSide         | char         | Y    | Defines if the customer wants to buy or sell the given<br>symbol on this leg. Possible values:                                                                                                                                                                                               |\n|                                 |                 |              |      | • '1' = Buy                                                                                                                                                                                                                                                                                  |\n|                                 |                 |              |      | • '2' = Sell                                                                                                                                                                                                                                                                                 |\n| → 687                           | LegQty          | char         | Y    | The notional amount to be dealt on one leg                                                                                                                                                                                                                                                   |\n| → 588                           | LegSettlDate    | LocalMktDate | Y    | Settlement date of one leg                                                                                                                                                                                                                                                                   |\n| → 566                           | LegPrice        | Price        | Y    | Price to be executed for this leg.                                                                                                                                                                                                                                                           |\n| → 654                           | LegRefID        | String       | Y    | Unique reference id of the leg from the QuoteRequest.                                                                                                                                                                                                                                        |\n| 60                              | TransactTime    | UTCTimestamp | Y    | Time this order request was initiated/released by the<br>trader, trading system, or intermediary.                                                                                                                                                                                            |\n| 38                              | OrderQty        | Qty          | Y    | Defines the netted notional amount in the notional<br>currency, for which the customer wants to execute.                                                                                                                                                                                     |\n| 40                              | OrdType         | char         | Y    | Defines type of order. Possible values:                                                                                                                                                                                                                                                      |\n|                                 |                 |              |      | • 'D' = Previously quoted                                                                                                                                                                                                                                                                    |\n| 15                              | Currency        | Currency     | Y    | Notional currency, in which the OrdQty <38> is de<br>fined.                                                                                                                                                                                                                                  |\n| 117                             | QuoteID         | String       | Y    | Mandatory field.<br>For Previously Quoted orders, it<br>contains the QuoteID <117> of the referenced quote.<br>If Issuer <106> is not defined, then only the rate is<br>taken from the referenced quote, which would not<br>guarantee execution with the provider from whom it<br>came from. |\n| 7071                            | ProductType     | String       | Y    | Custom field defining the requested product.<br>Possible values:<br>• 'FX-BT': Block trade                                                                                                                                                                                                   |\n| 7075                            | FixingReference | String       | N    | Fixing reference for NDF, NDS or NDF block (if product<br>supported in this API version).                                                                                                                                                                                                    |\n| <messagefooter></messagefooter> |                 |              | Y    |                                                                                                                                                                                                                                                                                              |\n\nTable 7.9: NewOrderMultileg message\n\n## <span id=\"page-44-0\"></span>**7.9 Execution Report [8]**\n\n### (360T → customer)\n\nWhen the customer sends a NewOrderSingle message, 360T responds with two different ExecutionReport messages: Immediately after receiving the message, a NewOrderSingle message is sent with OrdStatus '0' (New), to notify about the successful reception. After the order has been processed, 360T sends a second ExecutionReport message to inform about its final status.\n\nThe Regulatory ID group would contain TVTIC, UTI or USI IDs for the main trade or the allocation tickets, in case such exist.\n\n### Note: If you don't receive an ExecutionReport with state Filled or Rejected within 10 seconds, you must immediately contact 360T support to clarify the deal status.\n\n<span id=\"page-44-1\"></span>\n\n| Tag                             | Name          | Type       | Req. | Description                                                                                                                                                                                                                                                                                                                                                                |\n|---------------------------------|---------------|------------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |               |            | Y    | MsgType <35> = 8                                                                                                                                                                                                                                                                                                                                                           |\n| 37                              | OrderID       | String     | Y    | Trade reference ID generated by 360T                                                                                                                                                                                                                                                                                                                                       |\n| 11                              | ClOrdID       | String     | Y    | Unique<br>order<br>identifier<br>from<br>referenced<br>NewOrderSingle [D] message.                                                                                                                                                                                                                                                                                         |\n| 453                             | NoPartyIDs    | NumInGroup | N    | Repeating group containing the participants of the<br>trade. As we have one provider and one requester this<br>will at least 2. If requested we can also send the en<br>tity for which the trade is exported with PartyRole 'In<br>terested Party'. This can be useful in a ITEX scenario<br>where both sides of an internal deal are exported to the<br>same FIX session. |\n| → 448                           | PartyID       | String     | N    | 360T company name or MIC of the participant (re<br>quester or provider)                                                                                                                                                                                                                                                                                                    |\n| → 447                           | PartyIDSource | char       | N    | It can contain one of the following values:<br>• 'D': Proprietary custom code<br>• 'G': MIC (for PartyRole=MTF/SI)<br>• 'N': LegalEntityIdentifier<br>• 'P': Short code identifier                                                                                                                                                                                         |\n|                                 |               |            |      | Continued on next page                                                                                                                                                                                                                                                                                                                                                     |\n\n| Tag   | Name                                                                      | Type   | Req. | Description                                                                                                                                                                                  |\n|-------|---------------------------------------------------------------------------|--------|------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| → 452 | PartyRole                                                                 | int    | N    | Possible values:                                                                                                                                                                             |\n|       |                                                                           |        |      | • '1' = 'Executing Firm' for the requester                                                                                                                                                   |\n|       |                                                                           |        |      | • '4' = 'Clearing Firm'                                                                                                                                                                      |\n|       |                                                                           |        |      | • '35' = 'Liquidity provider' for the provider side                                                                                                                                          |\n|       |                                                                           |        |      | • '63' = Systematic Internaliser (SI) (the MIC of<br>the SI)                                                                                                                                 |\n|       |                                                                           |        |      | • '64' = Multilateral Trading Facility (MTF) (the<br>MIC of the 360T MTF)                                                                                                                    |\n|       |                                                                           |        |      | • '78' = 'Allocation Entity'                                                                                                                                                                 |\n|       |                                                                           |        |      | • '116' = 'Reporting Party' - The LEI of reporting<br>party                                                                                                                                  |\n|       |                                                                           |        |      | If a party has a LegalEntityIdentifier available, we'll<br>provide this as well by duplicating the group instance<br>and send the LEI with the same PartyRole and PartyID<br>Source<447>='N' |\n|       | PartyID Repeating Group for Clearing Firm                                 |        |      |                                                                                                                                                                                              |\n| → 448 | PartyID                                                                   | String | N    | Which Clearing house (DCO) this trade will be cleared<br>at.                                                                                                                                 |\n| → 447 | PartyIDSource                                                             | char   | N    | 'D': Proprietary custom code.                                                                                                                                                                |\n| → 452 | PartyRole                                                                 | int    | N    | '4' = Clearing Firm                                                                                                                                                                          |\n|       | PartyID Repeating Group for ExecutionVenue = MTF                          |        |      |                                                                                                                                                                                              |\n| → 448 | PartyID                                                                   | String | N    | MIC of 360T MTF                                                                                                                                                                              |\n| → 447 | PartyIDSource                                                             | char   | N    | 'G': MIC                                                                                                                                                                                     |\n| → 452 | PartyRole                                                                 | int    | N    | '64' = Multilateral Trading Facility (MTF)                                                                                                                                                   |\n|       | PartyID Repeating Group for Requester(LEI)                                |        |      |                                                                                                                                                                                              |\n| → 448 | PartyID                                                                   | String | N    | LEI of the requester                                                                                                                                                                         |\n| → 447 | PartyIDSource                                                             | char   | N    | 'N': LEI                                                                                                                                                                                     |\n| → 452 | PartyRole                                                                 | int    | N    | '1' = 'Executing Firm'                                                                                                                                                                       |\n|       | PartyID Repeating Group for Requester(MIC) - in case requester is an SI   |        |      |                                                                                                                                                                                              |\n| → 448 | PartyID                                                                   | String | N    | MIC of the requester                                                                                                                                                                         |\n| → 447 | PartyIDSource                                                             | char   | N    | 'G': MIC                                                                                                                                                                                     |\n| → 452 | PartyRole                                                                 | int    | N    | '1' = 'Executing Firm'                                                                                                                                                                       |\n|       | PartyID Repeating Group for Provider(LEI)                                 |        |      |                                                                                                                                                                                              |\n| → 448 | PartyID                                                                   | String | N    | LEI of the provider                                                                                                                                                                          |\n| → 447 | PartyIDSource                                                             | char   | N    | 'N': LEI                                                                                                                                                                                     |\n| → 452 | PartyRole                                                                 | int    | N    | '35' = 'Liquidity Provider'                                                                                                                                                                  |\n|       | PartyID Repeating Group for Provider(MIC) - in case the provider is an SI |        |      |                                                                                                                                                                                              |\n| → 448 | PartyID                                                                   | String | N    | MIC of the provider                                                                                                                                                                          |\n|       |                                                                           |        |      | Continued on next page                                                                                                                                                                       |\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 46\n\n| Tag   | Name                                 | Type         | Req. | Description                                                                                                                                                                                                                                    |\n|-------|--------------------------------------|--------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| → 447 | PartyIDSource                        | char         | N    | 'G': MIC                                                                                                                                                                                                                                       |\n| → 452 | PartyRole                            | int          | N    | '35' = 'Liquidity Provider'                                                                                                                                                                                                                    |\n|       | PartyID Repeating Group for Provider |              |      |                                                                                                                                                                                                                                                |\n| → 448 | PartyID                              | String       | N    | 360T company name of provider                                                                                                                                                                                                                  |\n| → 447 | PartyIDSource                        | char         | N    | 'D': Proprietary/Custom code                                                                                                                                                                                                                   |\n| → 452 | PartyRole                            | int          | N    | '35' = 'Liquidity Provider'                                                                                                                                                                                                                    |\n| 17    | ExecID                               | String       | Y    | Unique execution identifier assigned by 360T.                                                                                                                                                                                                  |\n| 150   | ExecType                             | char         | Y    | Describes the current state of the order. Possible<br>values:                                                                                                                                                                                  |\n|       |                                      |              |      | • '0' = New                                                                                                                                                                                                                                    |\n|       |                                      |              |      | • '8' = Rejected                                                                                                                                                                                                                               |\n|       |                                      |              |      | • 'F' = Trade                                                                                                                                                                                                                                  |\n| 39    | OrdStatus                            | char         | Y    | Describes the current state of the order. Possible<br>values:                                                                                                                                                                                  |\n|       |                                      |              |      | • '0' = New                                                                                                                                                                                                                                    |\n|       |                                      |              |      | • '2' = Filled                                                                                                                                                                                                                                 |\n|       |                                      |              |      | • '8' = Rejected                                                                                                                                                                                                                               |\n| 1     | Account                              | String       | Y    | Legal entity of the issuer of this Execution report.<br>Refers back to the same value from the executed<br>quote.                                                                                                                              |\n| 64    | SettlDate                            | LocalMktDate | N    | Settlement Date of the order. Near leg Date of a<br>swap. Not provided for block trades.                                                                                                                                                       |\n| 14101 | SplitSettlDate                       | LocalMktDate | N    | Split settlement date. For swaps this is the near leg split<br>settlement date.                                                                                                                                                                |\n| 55    | Symbol                               | String       | Y    | Contains the delivered symbol.                                                                                                                                                                                                                 |\n| 48    | SecurityID                           | String       | C    | Contains the product level ISIN code.<br>The value of<br>this field is subject to availability. For leg level ISINS<br>for Swaps please refer to the group NoSecurityAltID<br><454>. This field will not be set to report for Block<br>trades. |\n| 22    | SecurityIDSource                     | String       | C    | Should be '4'=ISIN if SecurityID<48> is set.                                                                                                                                                                                                   |\n| 454   | NoSecurityAltID                      | NumInGroup   | C    | Should be 2 in case of Swap and NDS if ISINs are<br>available. The first group lists the ISIN of the near<br>leg and the second group - the ISIN of the far leg.                                                                               |\n| → 455 | SecurityAltID                        | String       | C    | The ISIN of the respective leg.                                                                                                                                                                                                                |\n| → 456 | SecurityAltIDSource                  | String       | C    | Should be '4'=ISIN if SecurityID<48> is set.                                                                                                                                                                                                   |\n|       |                                      |              |      | Continued on next page                                                                                                                                                                                                                         |\n\n| Tag  | Name          | Type         | Req. | Description                                                                                                                                                                                                                                                                                                                          |\n|------|---------------|--------------|------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 461  | CFICode       | String       | N    | CFI-Code of the traded instrument.                                                                                                                                                                                                                                                                                                   |\n| 2891 | UPICode 3     | String       | N    | Unique Product Identifier (UPI) of the traded instru<br>ment (near leg for Swaps). When there are allocations,<br>this field contains the UPI of each allocation.                                                                                                                                                                    |\n| 7891 | UPICode2 3    | String       | N    | Unique Product Identifier (UPI) of the traded instru<br>ment (far leg for Swaps). When there are allocations,<br>this field contains the UPI of each allocation.                                                                                                                                                                     |\n| 541  | MaturityDate  | LocalMktDate | C    | Defines the Fixing Date for an NDF (or the near<br>leg for an NDS).                                                                                                                                                                                                                                                                  |\n|      |               |              |      | For<br>Loan/Deposit<br>products,<br>the<br>Maturity<br>Date<541> field defines the end of the loan or<br>deposit's term.                                                                                                                                                                                                             |\n| 7541 | MaturityDate2 | LocalMktDate | N    | Far leg Maturity date if the order was an NDS.                                                                                                                                                                                                                                                                                       |\n| 54   | Side          | char         | Y    | Defines if the client has to bought or sold the<br>given symbol for FX and Base Metals products.<br>For Money Market (MM) products, the Side<54><br>field defines if the client has lent or borrowed<br>money.<br>Possible values:<br>• '1' = Buy<br>• '2' = Sell<br>• 'F' = Lend (MM Deposit only)<br>• 'G' = Borrow (MM Loan only) |\n| 38   | OrderQty      | Qty          | Y    | Defines the notional amount in the notional cur<br>rency, for which the customer tried to execute.<br>Netted amount in case of a block trade.                                                                                                                                                                                        |\n| 40   | OrdType       | char         | Y    | Depending on the value sent in the order request:<br>• 'D' = Previously quoted<br>• '1' = Market order<br>• '2' = Limit order                                                                                                                                                                                                        |\n| 44   | Price         | Price        | Y    | Price which has been set by the customer in the<br>NewOrderSingle message. For block trades it will<br>be set to 0.                                                                                                                                                                                                                  |\n|      |               |              |      | Continued on next page                                                                                                                                                                                                                                                                                                               |\n\n<span id=\"page-47-0\"></span><sup>3</sup>These fields are only available if support for sending UPI identifiers has been enabled for the FIX session. Please speak to your 360T representative if you require this feature.\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 48\n\n| Tag    | Name            | Type         | Req. | Description                                                                                                                                       |\n|--------|-----------------|--------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------|\n| 15     | Currency        | Currency     | Y    | Notional currency, in which the quantities are de<br>fined.                                                                                       |\n| 32     | LastQty         | Price        | Y    | Amount that has been executed for a spot or for<br>ward or the near leg of a swap. For block trades it<br>will have the netted amount.            |\n| 31     | LastPx          | Price        | Y    | Rate that has been executed for a spot or forward<br>or the near leg of a swap. For block trades it will<br>be set to 0.                          |\n| 194    | LastSpotRate    | Price        | Y    | The reference spot rate.                                                                                                                          |\n| 151    | LeavesQty       | Qty          | Y    | Always set to '0'.                                                                                                                                |\n| 14     | CumQty          | Qty          | Y    | Has the value of the executed amount of a spot or<br>forward or of the near leg of a swap.<br>For block<br>trades it will have the netted amount. |\n| 6      | AvgPx           | Price        | Y    | Always set to '0'.                                                                                                                                |\n| 631    | MidPx           | Price        | N    | MidPrice (near leg of Swaps).                                                                                                                     |\n| 7650   | MidSpotRate     | Price        | N    | Mid spot rate                                                                                                                                     |\n| 7651   | MidPx2          | Price        | N    | MidPrice of far leg (Swaps)                                                                                                                       |\n| 60     | TransactTime    | UTCTimestamp | Y    | Time when the trade was executed. This field is<br>set in millisecond precision.                                                                  |\n| 58     | Text            | String       | N    | contains additional information                                                                                                                   |\n| 193    | SettlDate2      | LocalMktDate | N    | Farleg Date of a swap. In the 360T platform the<br>tenor on the far leg is relative to the near leg.                                              |\n| 14102  | SplitSettlDate2 | LocalMktDate | N    | Far split settlement date. If present, this must be inter<br>preted as a swap.                                                                    |\n| 192    | OrderQty2       | Qty          | N    | Defines the notional amount in the notional cur<br>rency, which has been executed in the far leg for a<br>swap.                                   |\n| 640    | Price2          | Price        | N    | Price which was set in the order for the far leg of<br>a Swap.                                                                                    |\n| 78     | NoAllocs        | NumInGroup   | N    | Repeating group of allocations and related identi<br>fiers                                                                                        |\n| → 79   | AllocAccount    | String       | N    | Entity for which an amount gets allocated. Echoed<br>back from the quote request.                                                                 |\n| → 80   | AllocQty        | Qty          | N    | Amount allocated. Can be negative to indicate in<br>verse to the side given in field 54. Echoed back<br>from the quote request.                   |\n| → 7605 | USIPrefix       | String       | N    | Unique Swap Identifier Prefix for allocation (near<br>leg for Swaps).                                                                             |\n| → 7606 | USIID           | String       | N    | Unique Swap Identifier for allocation (near leg for<br>Swaps).                                                                                    |\n|        |                 |              |      | Continued on next page                                                                                                                            |\n\n| Tag                    | Name                                                                    | Type         | Req. | Description                                                                                  |\n|------------------------|-------------------------------------------------------------------------|--------------|------|----------------------------------------------------------------------------------------------|\n| → 7607                 | USIPrefix2                                                              | String       | N    | Unique Swap Identifier Prefix for allocation (far<br>leg for Swaps only).                    |\n| → 7608                 | USIID2                                                                  | String       | N    | Unique Swap Identifier for allocation (far leg for<br>Swaps).                                |\n| → 7653                 | UTIID                                                                   | String       | N    | Unique Trade Identifier for allocation (near leg for<br>Swaps).                              |\n| → 7654                 | UTIID2                                                                  | String       | N    | Unique Trade Identifier for allocation (far leg for<br>Swaps).                               |\n| → 539                  | NoNestedPartyIDs                                                        | NumInGroup   | N    | Repeating group for MiFID properties of the allo<br>cation. One is accepted per allocation   |\n| 7546                   | NoCustomFields                                                          | NumInGroup   | N    | Number of custom fields (group). These are only pro<br>vided if the order has been executed. |\n| → 7547                 | CustomFieldName                                                         | String       | C    | Name of this custom field (required when custom fields<br>are used).                         |\n| → 7548                 | CustomFieldValue                                                        | String       | C    | Value of this custom field (required when custom fields<br>are used).                        |\n|                        | NestedPartyID Repeating Group for LEI of a TAS/TOB requester allocation |              |      |                                                                                              |\n| →→ 524                 | NestedPartyID                                                           | String       | N    | LEI of a TAS/TOB requester allocation account                                                |\n| →→ 525                 | NestedPartyIDSource                                                     | char         | N    | 'N': LEI                                                                                     |\n| →→ 538                 | NestedPartyRole                                                         | int          | N    | '78' = 'Allocation Entity'                                                                   |\n| 555                    | NoLegs                                                                  | NumInGroup   | N    | Repeating group of legs - only used to define<br>ISIN/CFI-Codes for Multileg-Products        |\n| → 600                  | LegSymbol                                                               | String       | N    | Symbol of the QuoteRequest                                                                   |\n| → 611                  | LegMaturityDate                                                         | LocalMktDate | N    | Defines the Fixing Date for an NDF Block trade<br>leg.                                       |\n| → 602                  | LegSecurityID                                                           | String       | N    | ISIN of this leg                                                                             |\n| → 603                  | LegSecurityIDSource String                                              |              | N    | '4'=ISIN                                                                                     |\n| → 608                  | LegCFICode                                                              | String       | N    | CFI-Code of this leg.                                                                        |\n| → 2893                 | LegUPICode 3                                                            | String       | N    | Unique Product Identifier (UPI) of this leg.                                                 |\n| → 588                  | LegSettlDate                                                            | LocalMktDate | Y    | Settlement date of this leg.                                                                 |\n| → 566                  | LegPrice                                                                | Price        | Y    | Execution price of this leg.                                                                 |\n| → 654                  | LegRefID                                                                | String       | Y    | Unique<br>reference<br>id<br>of<br>the<br>leg<br>from<br>the<br>QuoteRequest.                |\n| → 7652                 | LegMidPx                                                                | Price        | N    | This mid price for this leg.                                                                 |\n| → 670                  | NoLegAllocs                                                             | NumInGroup   | Y    | Repeating group of allocations per leg. Only one<br>is accepted per leg.                     |\n| →→ 671                 | AllocAccount                                                            | String       | Y    | Entity for which an amount gets allocated                                                    |\n| →→ 673                 | AllocQty                                                                | Qty          | Y    | Amount allocated.                                                                            |\n| Continued on next page |                                                                         |              |      |                                                                                              |\n\n| Tag                                                                           | Name                                                       | Type       | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |  |  |\n|-------------------------------------------------------------------------------|------------------------------------------------------------|------------|------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|\n| →→ 539                                                                        | NoNestedPartyIDs                                           | NumInGroup | N    | Repeating group for MiFID properties of the allocation.<br>One is accepted per allocation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |  |  |\n| NestedPartyID Repeating Group for Investment Decision within Firm (Algorithm) |                                                            |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |\n| →→ → 524                                                                      | NestedPartyID                                              | String     | N    | LEI of a TAS/TOB requester allocation account                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |  |  |\n| →→ → 525                                                                      | NestedPartyIDSource                                        | char       | N    | 'N': LEI                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |  |  |\n| →→ → 538                                                                      | NestedPartyRole                                            | int        | N    | '78' = 'Allocation Entity'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |  |  |\n| 1907                                                                          | NoRegulatoryTradeIDs                                       | NumInGroup | N    | Repeating Group containing regulatory IDs.<br>In case of single-leg products with no allocations, one<br>group would be expected.<br>In the case of swaps with<br>no allocations, two or three groups would be sent - first<br>for the near leg, second for the far leg and optionally the<br>third one - product level ISIN. The third group is subject<br>to availability of the TVTIC code on product level.<br>In case of single-leg products with allocations, these<br>groups would match the number and order of legs of<br>the NoAllocs group. In case of a swap with allocations,<br>these groups would match the number of allocations<br>times two plus ontionally one. Namely: the first group<br>- regulatory code for allocation 1 of the near leg; the<br>second group - allocation 1 of the far leg; thrid group -<br>allocation 2 of the near leg; etc. After all the allocation<br>TVTIC there may optionally be a last group indicating<br>the TVTIC for product level for the swap. This value is<br>is subject to availability of the TVTIC code on product<br>level.<br>In case of swaps with allocations, the RegulatoryLe<br>gRefID<2411> will match the NoLegs group this Regu<br>latoryTradeID is refering to. The optional product-level<br>TVTIC won't have a RegulatoryLegRefID<2411> set. |  |  |\n|                                                                               | RegulatoryTrade Repeating Group for TVTIC, USI, UTI or RTN |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |\n| → 1903                                                                        | RegulatoryTradeID                                          | String     | N    | Regulatory ID                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |  |  |\n| → 1906                                                                        | RegulatoryTradeIDType int                                  |            | N    | Regulatory ID type:                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |  |\n|                                                                               |                                                            |            |      | • '0' = Unique Transaction Identifier (UTI) or<br>Unique Swap Identifier (USI)<br>• '5' = Trading venue transaction identification<br>code (TVTIC)<br>• '6' = Report Tracking Number (RTN) 4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |  |\n| → 2411                                                                        | RegulatoryLegRefID                                         | String     | N    | This field will be missing in case of single-leg prod<br>ucts. In case of non-swap multileg products, this id will<br>match LegRefId of the respective leg it references.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |  |  |\n| 2668                                                                          | NoTrdRegPublications                                       | NumInGroup | N    | Repeating Group containing Waivers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |  |\n| TrdRegPublication Repeating Group for Waiver                                  |                                                            |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |\n| Continued on next page                                                        |                                                            |            |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |  |\n\n<span id=\"page-50-0\"></span><sup>4</sup>These fields are only available if support for sending RTN identifiers has been enabled for the FIX session. Please speak to your 360T representative if you require this feature.\n\n©360 Treasury Systems AG, 2025, This file contains proprietary and confidential information and may not be divulged to any third party without prior written approval from 360 Treasury Systems AG 51\n\n| Tag                    | Name                        | Type   | Req. | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |  |\n|------------------------|-----------------------------|--------|------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|\n| → 2669                 | TrdRegPublicationType int   |        | N    | '0' = Pre-trade transparency waiver                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |  |\n| → 2670                 | TrdRegPublication<br>Reason | int    | N    | • '0'='No preceding order in book as transaction<br>price set within average spread of a liquid instru<br>ment'<br>• '1'='No preceding order in book as transaction<br>price depends on system-set reference price for<br>an illiquid instrument'<br>• '2'='No preceding order in book as transac<br>tion price is for transaction subject to conditions<br>other than current market price'<br>• '3'='No public price for preceding order as pub<br>lic reference price was used for matching orders'<br>• '4'='No public price quoted as instrument is<br>illiquid'<br>• '5'='No public price quoted due to \"Size\"'<br>• '9'='No public price and/or size quoted as trans |  |\n|                        |                             |        |      | action is \"large in scale\"'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |  |\n| 828                    | TrdType                     | int    | N    | Post Trade Indicator '65' = TPAC (Package Trade)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |\n| 6160                   | LastPx2                     | Price  | N    | Rate that has been executed for the far leg of a swap.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |\n| 7071                   | ProductType                 | String | N    | Custom field defining the requested product.<br>Possible values:<br>• 'Metals Outright': Base Metals Outright<br>• 'Metals Spread': Base Metals Spread<br>• 'Metals Quarterly Strip': Base Metals Quarterly<br>Strip<br>• 'MM': Loan/Deposit<br>Note:<br>• Only populated for Base Metals products.                                                                                                                                                                                                                                                                                                                                                                         |  |\n| 7075                   | FixingReference             | String | N    | Fixing reference for NDF, NDS or NDF block (if<br>product supported in this API version).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |  |\n| 7653                   | UTIID                       | String | N    | Unique Trade Identifier for allocation (near leg for<br>Swaps). When there are allocations this identifies the<br>parent ticket.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |\n| 7654                   | UTIID2                      | String | N    | Unique Trade Identifier for allocation (far leg for<br>Swaps). When there are allocations this identifies the<br>parent ticket.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |  |\n| 7605                   | USIPrefix                   | String | N    | Unique Swap Identifier Prefix (near leg for Swaps).<br>When there are allocations this identifies the parent<br>ticket.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |\n| Continued on next page |                             |        |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |  |\n\n| Tag                             | Name         | Type         | Req. | Description                                                                                                                 |\n|---------------------------------|--------------|--------------|------|-----------------------------------------------------------------------------------------------------------------------------|\n| 7606                            | USIID        | String       | N    | Unique Swap Identifier (near leg for Swaps).<br>When<br>there are allocations this identifies the parent ticket.            |\n| 7607                            | USIPrefix2   | String       | N    | Unique Swap Identifier Prefix (far leg for Swaps only).<br>When there are allocations this identifies the parent<br>ticket. |\n| 7608                            | USIID2       | String       | N    | Unique Swap Identifier (far leg for Swaps). When there<br>are allocations this identifies the parent ticket.                |\n| 9514                            | OptionPeriod | String       | N    | Option Period (for FX Time Option)                                                                                          |\n| 9515                            | OptionDate   | LocalMktDate | N    | Option Date (for FX Time Option)                                                                                            |\n| <messagefooter></messagefooter> |              |              | Y    |                                                                                                                             |\n\nTable 7.10: ExecutionReport message\n\n## <span id=\"page-53-0\"></span>**7.10 SecurityDefinitionRequest [c]**\n\n(Customer → 360T)\n\nThe SecurityDefinitionRequest message is used by the customer to request 360T financial calendar information (tenor symbols and value dates) for a particular currency pair. The currency pair must be specified in the Symbol field of the SecurityDefinitionRequest message.\n\nIn response to each SecurityDefinitionRequest sent by the client, 360T will reply with a SecurityDefinition message containing a list of tenor symbols and value dates for the given currency pair.\n\n<span id=\"page-53-1\"></span>\n\n| Tag                             | Name                | Type   | Req. | Description                                                                                                                       |\n|---------------------------------|---------------------|--------|------|-----------------------------------------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                     |        | Y    | MsgType <35> = c                                                                                                                  |\n| 320                             | SecurityReqID       | String | Y    | Unique identifier for this request. This field must not<br>be longer than 50 characters.                                          |\n| 321                             | SecurityRequestType | int    | Y    | Defines the type of SecurityDefinitionRequest. Value<br>provided must be '3' = Request List Securities.                           |\n| 55                              | Symbol              | String | Y    | The currency pair for which financial calendar infor<br>mation is being requested.<br>Must be provided in the<br>format: CC1/CC2. |\n| <messagefooter></messagefooter> |                     |        | Y    |                                                                                                                                   |\n\nTable 7.11: SecurityDefinitionRequest message\n\n## <span id=\"page-54-0\"></span>**7.11 SecurityDefinition [d]**\n\n### (360T → customer)\n\nThis message is sent by 360T to the customer in response to a SecurityDefinitionRequest. The message contains a list of tenor symbols and value dates, stored in the Underlyings group.\n\nNote: the value dates are defined for the current trade date, which is determined when the SecurityDefinitionRequest is received by 360T. The value dates are only valid until the value date roll-over time of the given currency pair.\n\n<span id=\"page-54-1\"></span>\n\n| Tag                             | Name                   | Type         | Req. | Description                                                                                                                         |\n|---------------------------------|------------------------|--------------|------|-------------------------------------------------------------------------------------------------------------------------------------|\n| <messageheader></messageheader> |                        |              | Y    | MsgType <35> = d                                                                                                                    |\n| 320                             | SecurityReqID          | String       | Y    | Unique identifier for original request.<br>This field must not be longer than 50 characters.                                        |\n| 322                             | SecurityResponseID     | String       | Y    | Unique identifier for this response.<br>This field must not be longer than 50 characters.                                           |\n| 323                             | SecurityResponseType   | int          | Y    | Defines the type of SecurityDefinitionResponse.<br>Value<br>will always be set to '4' = List of securities returned per<br>request. |\n| 711                             | NoUnderlyings          | NumInGroup   | Y    | Number of FX value dates delivered in the group.                                                                                    |\n| → 311                           | UnderlyingSymbol       | String       | Y    | The currency pair for which date information pertains.<br>Provided in the format: CC1/CC2.                                          |\n| → 309                           | UnderlyingSecurityID   | String       | Y    | FX tenor short name.                                                                                                                |\n| → 542                           | UnderlyingMaturityDate | LocalMktDate | Y    | Value date of FX tenor in the 360T financial calender.                                                                              |\n| → 307                           | UnderlyingSecurityDesc | String       | Y    | FX tenor long name.                                                                                                                 |\n| <messagefooter></messagefooter> |                        |              | Y    |                                                                                                                                     |\n\nTable 7.12: SecurityDefinition message\n\nThe table below details the FX tenor symbols for which value dates will be provided in the SecurityDefinition message.\n\nNote: the tenors included in the message may be subject to change in future, i.e. tenors may be added or removed without notice.\n\n<span id=\"page-54-2\"></span>\n\n| Tenor<br>Short<br>Name | Tenor<br>Long<br>Name |\n|------------------------|-----------------------|\n| TD                     | TODAY                 |\n| TM                     | TOMORROW              |\n| SP                     | SPOT                  |\n| SN                     | SPOTNEXT              |\n| 1W                     | 1 WEEK                |\n| 2W                     | 2 WEEKS               |\n| 3W                     | 3 WEEKS               |\n| 1M                     | 1 MONTH               |\n| 2M                     | 2 MONTHS              |\n\n| 3M  | 3 MONTHS  |\n|-----|-----------|\n| 4M  | 4 MONTHS  |\n| 5M  | 5 MONTHS  |\n| 6M  | 6 MONTHS  |\n| 7M  | 7 MONTHS  |\n| 8M  | 8 MONTHS  |\n| 9M  | 9 MONTHS  |\n| 1Y  | 1 YEAR    |\n| 18M | 18 MONTHS |\n| 2Y  | 2 YEARS   |\n| 3Y  | 3 YEARS   |\n| IM  | MAR IMM   |\n| IJ  | JUN IMM   |\n| IS  | SEP IMM   |\n| ID  | DEC IMM   |\n\nTable 7.13: FX tenors\n\n## <span id=\"page-56-0\"></span>**8 Example Messages**\n\n## <span id=\"page-56-1\"></span>**8.1 News [B]**\n\n<span id=\"page-56-2\"></span>\n\n| Tag                           | Attribute   | Value          |\n|-------------------------------|-------------|----------------|\n| <message header=\"\"></message> |             |                |\n| 35                            | MsgType     | B              |\n| 148                           | Headline    | Bank<br>Basket |\n| 33                            | LinesOfText | 1              |\n| 58                            | Text        | ACME           |\n\n<Message Trailer>\n\nAn example New message sent from customer to 360T asking for available providers.\n\n### Table 8.1: Example News message\n\nThis is an example News message sent from 360T to the customer. It states that the bank basket for \"Fx Spot\" trades consists of 5 providers. Each provider is sent in a single Text tag.\n\n<span id=\"page-56-3\"></span>\n\n| Tag                            | Attribute                     | Value                 |  |\n|--------------------------------|-------------------------------|-----------------------|--|\n|                                | <message header=\"\"></message> |                       |  |\n| 35                             | MsgType                       | B                     |  |\n| 148                            | Headline                      | Fx<br>Spot            |  |\n| 33                             | LinesOfText                   | 5                     |  |\n| 58                             | Text                          | BOAL.DEMO             |  |\n| 58                             | Text                          | RBS.LND.DEMO          |  |\n| 58                             | Text                          | SEB.FRA.DEMO          |  |\n| 58                             | Text                          | CITIBANK.DEMO         |  |\n| 58                             | Text                          | Barclays<br>BARX.DEMO |  |\n| 215                            | NoRoutingIDs                  | 1                     |  |\n| 216                            | RoutingType                   | 1                     |  |\n| 217                            | RoutingID                     | ACME                  |  |\n| <message trailer=\"\"></message> |                               |                       |  |\n\nTable 8.2: Example News message\n\n## <span id=\"page-57-0\"></span>**8.2 QuoteRequest [R]**\n\n### <span id=\"page-57-1\"></span>**8.2.1 Tradeable Quotes: Spot**\n\nThis is an example QuoteRequest message. The client wants to buy 1 million EUR against USD. As RefSpotDate is SettlDate this is request for SPOT quotes.\n\n<span id=\"page-57-3\"></span>\n\n| Tag                            | Attribute                     | Value              |  |\n|--------------------------------|-------------------------------|--------------------|--|\n|                                | <message header=\"\"></message> |                    |  |\n| 35                             | MsgType                       | R                  |  |\n| 131                            | QuoteReqID                    | MyQuoteReqID-12345 |  |\n| 7070                           | RefSpotDate                   | ********           |  |\n| 7071                           | ProductType                   | FX-STD             |  |\n| 146                            | NoRelatedSym                  | 1                  |  |\n| 55                             | Symbol                        | EUR/USD            |  |\n| 537                            | QuoteType                     | 1                  |  |\n| 54                             | Side                          | 1                  |  |\n| 38                             | OrderQty                      | 1000000            |  |\n| 64                             | SettlDate                     | ********           |  |\n| 15                             | Currency                      | EUR                |  |\n| 1                              | Account                       | ACME               |  |\n| 126                            | ExpireTime                    | ********-12:13:55  |  |\n| <message trailer=\"\"></message> |                               |                    |  |\n\nTable 8.3: Example QuoteRequest for Spot quotes\n\n### <span id=\"page-57-2\"></span>**8.2.2 Tradeable Quotes: Forward**\n\nThis is an example QuoteRequest message. The client wants to buy 1 million EUR against USD. Since the settlement date is different from RefSpotDate, this request is for forward quotes.\n\n<span id=\"page-57-4\"></span>\n\n| Tag                           | Attribute    | Value                  |\n|-------------------------------|--------------|------------------------|\n| <message header=\"\"></message> |              |                        |\n| 35                            | MsgType      | R                      |\n| 131                           | QuoteReqID   | MyQuoteReqID-12345     |\n| 7070                          | RefSpotDate  | ********               |\n| 7071                          | ProductType  | FX-STD                 |\n| 146                           | NoRelatedSym | 1                      |\n| 55                            | Symbol       | EUR/USD                |\n| 537                           | QuoteType    | 1                      |\n| 54                            | Side         | 1                      |\n|                               |              | continued on next page |\n\n### 8.2. QuoteRequest [R] Chapter 8: Example Messages\n\n| Tag                            | Attribute  | Value             |\n|--------------------------------|------------|-------------------|\n| 38                             | OrderQty   | 1000000           |\n| 64                             | SettlDate  | ********          |\n| 15                             | Currency   | EUR               |\n| 1                              | Account    | ACME              |\n| 126                            | ExpireTime | ********-12:13:55 |\n| <message trailer=\"\"></message> |            |                   |\n\nTable 8.4: Example QuoteRequest for Forward quotes\n\n### <span id=\"page-58-0\"></span>**8.2.3 Tradeable Quotes: FX Time Options**\n\nThis is an example QuoteRequest message. The client wants to buy 1 million EUR against USD. Since the OptionDate are supplied, this request is for FX Time Options quotes.\n\n<span id=\"page-58-2\"></span>\n\n| Tag                            | Attribute    | Value                         |\n|--------------------------------|--------------|-------------------------------|\n|                                |              | <message header=\"\"></message> |\n| 35                             | MsgType      | R                             |\n| 131                            | QuoteReqID   | MyQuoteReqID-12345            |\n| 7070                           | RefSpotDate  | ********                      |\n| 7071                           | ProductType  | FX-STD                        |\n| 146                            | NoRelatedSym | 1                             |\n| 55                             | Symbol       | EUR/USD                       |\n| 537                            | QuoteType    | 1                             |\n| 54                             | Side         | 1                             |\n| 38                             | OrderQty     | 1000000                       |\n| 64                             | SettlDate    | ********                      |\n| 15                             | Currency     | EUR                           |\n| 1                              | Account      | ACME                          |\n| 126                            | ExpireTime   | ********-12:13:55             |\n| 9515                           | OptionDate   | ********                      |\n| <message trailer=\"\"></message> |              |                               |\n\nTable 8.5: Example QuoteRequest for Forward quotes\n\n### <span id=\"page-58-1\"></span>**8.2.4 Tradeable Quotes: Swap**\n\nThis is an example QuoteRequest message. The client wants to buy 1 million EUR against USD on the near leg and sell the same amount on the far leg. Swaps always have SettlDate2 and OrderQty2 tags set.\n\n<span id=\"page-59-1\"></span>\n\n| Tag  | Attribute                     | Value                          |  |\n|------|-------------------------------|--------------------------------|--|\n|      | <message header=\"\"></message> |                                |  |\n| 35   | MsgType                       | R                              |  |\n| 131  | QuoteReqID                    | MyQuoteReqID-12345             |  |\n| 7070 | RefSpotDate                   | ********                       |  |\n| 7071 | ProductType                   | FX-STD                         |  |\n| 146  | NoRelatedSym                  | 1                              |  |\n| 55   | Symbol                        | EUR/USD                        |  |\n| 537  | QuoteType                     | 1                              |  |\n| 54   | Side                          | 2                              |  |\n| 38   | OrderQty                      | 1000000                        |  |\n| 64   | SettlDate                     | ********                       |  |\n| 193  | SettlDate2                    | ********                       |  |\n| 192  | OrderQty2                     | 1000000                        |  |\n| 15   | Currency                      | EUR                            |  |\n| 1    | Account                       | ACME                           |  |\n| 126  | ExpireTime                    | ********-12:13:55              |  |\n|      |                               | <message trailer=\"\"></message> |  |\n\n### Table 8.6: Example QuoteRequest for Swap quotes\n\n### <span id=\"page-59-0\"></span>**8.2.5 Tradeable Quotes: NDF**\n\nThis is an example QuoteRequest message for a Non-Deliverable-Forward. The client wants to buy 1 million EUR against RUB. The Fixing date (sent in the field MaturityDate) is 2 days before the settlement date.\n\n<span id=\"page-59-2\"></span>\n\n| Tag  | Attribute                     | Value                  |  |  |\n|------|-------------------------------|------------------------|--|--|\n|      | <message header=\"\"></message> |                        |  |  |\n| 35   | MsgType                       | R                      |  |  |\n| 131  | QuoteReqID                    | MyQuoteReqID-12345     |  |  |\n| 7070 | RefSpotDate                   | ********               |  |  |\n| 7071 | ProductType                   | FX-STD                 |  |  |\n| 146  | NoRelatedSym                  | 1                      |  |  |\n| 55   | Symbol                        | EUR/RUB                |  |  |\n| 541  | MaturityDate                  | 20110808               |  |  |\n| 537  | QuoteType                     | 1                      |  |  |\n| 54   | Side                          | 1                      |  |  |\n| 38   | OrderQty                      | 1000000                |  |  |\n| 64   | SettlDate                     | ********               |  |  |\n|      |                               | continued on next page |  |  |\n\n| Tag                            | Attribute  | Value             |\n|--------------------------------|------------|-------------------|\n| 15                             | Currency   | EUR               |\n| 1                              | Account    | ACME              |\n| 126                            | ExpireTime | ********-12:13:55 |\n| <message trailer=\"\"></message> |            |                   |\n\nTable 8.7: Example QuoteRequest for NDF quotes\n\n### <span id=\"page-60-0\"></span>**8.2.6 Tradeable Quotes: NDS**\n\nThis is an example QuoteRequest message for a Non-Deliverable-Swap. The client wants to buy 1 million EUR against RUB on the near leg and sell the same amount in the far leg. The Fixing dates (sent in the MaturityDate fields) is 2 days before the settlement dates for both of the legs.\n\n<span id=\"page-60-2\"></span>\n\n| Tag  | Attribute                     | Value                          |  |\n|------|-------------------------------|--------------------------------|--|\n|      | <message header=\"\"></message> |                                |  |\n| 35   | MsgType                       | R                              |  |\n| 131  | QuoteReqID                    | MyQuoteReqID-12345             |  |\n| 7070 | RefSpotDate                   | ********                       |  |\n| 7071 | ProductType                   | FX-STD                         |  |\n| 146  | NoRelatedSym                  | 1                              |  |\n| 55   | Symbol                        | EUR/RUB                        |  |\n| 541  | MaturityDate                  | 20110808                       |  |\n| 7541 | MaturityDate                  | ********                       |  |\n| 537  | QuoteType                     | 1                              |  |\n| 54   | Side                          | 1                              |  |\n| 38   | OrderQty                      | 1000000                        |  |\n| 64   | SettlDate                     | ********                       |  |\n| 193  | SettlDate2                    | ********                       |  |\n| 192  | OrderQty2                     | 1000000                        |  |\n| 15   | Currency                      | EUR                            |  |\n| 1    | Account                       | ACME                           |  |\n| 126  | ExpireTime                    | ********-12:13:55              |  |\n|      |                               | <message trailer=\"\"></message> |  |\n\nTable 8.8: Example QuoteRequest for NDS quotes\n\n### <span id=\"page-60-1\"></span>**8.2.7 Tradeable Quotes: FX Block Trade**\n\n<span id=\"page-61-1\"></span>\n\n| Tag  | Attribute       | Value                                |\n|------|-----------------|--------------------------------------|\n|      |                 | <message header=\"\"></message>        |\n| 8    | BeginString     | FIX.4.4                              |\n| 9    | BodyLength      | 382                                  |\n| 35   | MsgType         | R                                    |\n| 131  | QuoteReqID      | 4c5c455c-a2cc-4eef-8387-53f74e384454 |\n| 7070 | RefSpotDate     | ********                             |\n| 7071 | ProductType     | FX-BT                                |\n| 146  | NoRelatedSym    | 1                                    |\n| 55   | Symbol          | USD/BRL                              |\n| 537  | QuoteType       | 1                                    |\n| 54   | Side            | 1                                    |\n| 38   | OrderQty        | 11000                                |\n| 64   | SettlDate       | ********                             |\n| 15   | Currency        | USD                                  |\n| 1    | Account         | GroupE                               |\n| 126  | ExpireTime      | ********-11:38:03.142                |\n| 555  | NoLegs          | 2                                    |\n| 600  | LegSymbol       | USD/BRL                              |\n| 624  | LegSide         | 1                                    |\n| 687  | LegQty          | 5000                                 |\n| 670  | NoLegAllocs     | 1                                    |\n| 671  | LegAllocAccount | GroupE                               |\n| 673  | LegAllocQty     | 5000                                 |\n| 654  | LegRefID        | 39b7001a-4508-4365-b139-f77c6c858959 |\n| 588  | LegSettlDate    | ********                             |\n| 600  | LegSymbol       | USD/BRL                              |\n| 624  | LegSide         | 1                                    |\n| 687  | LegQty          | 6000                                 |\n| 670  | NoLegAllocs     | 1                                    |\n| 671  | LegAllocAccount | GroupE                               |\n| 673  | LegAllocQty     | 6000                                 |\n| 654  | LegRefID        | 53dcd8e7-092a-4ba1-bcc3-62fe3b815711 |\n| 588  | LegSettlDate    | ********                             |\n\nTable 8.9: Example QuoteRequest for FX Block Trade quotes\n\n### <span id=\"page-61-0\"></span>**8.2.8 Tradeable Quotes: NDF Block Trade**\n\n<span id=\"page-62-1\"></span>\n\n| Tag  | Attribute       | Value                                |\n|------|-----------------|--------------------------------------|\n|      |                 | <message header=\"\"></message>        |\n| 35   | MsgType         | R                                    |\n| 131  | QuoteReqID      | c63f07a1b8d4                         |\n| 7070 | RefSpotDate     | ********                             |\n| 7071 | ProductType     | FX-BT                                |\n| 146  | NoRelatedSym    | 1                                    |\n| 55   | Symbol          | USD/BRL                              |\n| 106  | Issuer          | JPMORGAN.DEMO                        |\n| 537  | QuoteType       | 1                                    |\n| 54   | Side            | 1                                    |\n| 38   | OrderQty        | 11000                                |\n| 64   | SettlDate       | ********                             |\n| 15   | Currency        | USD                                  |\n| 1    | Account         | GroupE                               |\n| 555  | NoLegs          | 2                                    |\n| 600  | LegSymbol       | USD/BRL                              |\n| 611  | LegMaturityDate | ********                             |\n| 624  | LegSide         | 1                                    |\n| 687  | LegQty          | 5000                                 |\n| 670  | NoLegAllocs     | 1                                    |\n| 671  | LegAllocAccount | GroupE                               |\n| 673  | LegAllocQty     | 5000                                 |\n| 654  | LegRefID        | e5b9f2d4-26fb-4c37-af32-52a6193b1097 |\n| 588  | LegSettlDate    | ********                             |\n| 600  | LegSymbol       | USD/BRL                              |\n| 611  | LegMaturityDate | ********                             |\n| 624  | LegSide         | 1                                    |\n| 687  | LegQty          | 6000                                 |\n| 670  | NoLegAllocs     | 1                                    |\n| 671  | LegAllocAccount | GroupE                               |\n| 673  | LegAllocQty     | 6000                                 |\n| 654  | LegRefID        | 467dd1e3-8446-4b5a-b380-ebb04c8e13e2 |\n| 588  | LegSettlDate    | ********                             |\n\nTable 8.10: Example QuoteRequest for NDF Block Trade quotes\n\n### <span id=\"page-62-0\"></span>**8.2.9 Tradeable Quotes: Loan/Deposit**\n\n<span id=\"page-63-1\"></span>\n\n| Tag  | Attribute                     | Value        |  |\n|------|-------------------------------|--------------|--|\n|      | <message header=\"\"></message> |              |  |\n| 35   | MsgType                       | R            |  |\n| 131  | QuoteReqID                    | c63f07a1b8d4 |  |\n| 7071 | ProductType                   | MM           |  |\n| 7072 | DayCount                      | ACT/360      |  |\n| 146  | NoRelatedSym                  | 1            |  |\n| 55   | Symbol                        | EUR          |  |\n| 537  | QuoteType                     | 1            |  |\n| 54   | Side                          | G            |  |\n| 38   | OrderQty                      | 11000        |  |\n| 64   | SettlDate                     | ********     |  |\n| 541  | MaturityDate                  | ********     |  |\n| 15   | Currency                      | EUR          |  |\n| 1    | Account                       | GroupE       |  |\n\nTable 8.11: Example QuoteRequest for Loan/Deposit quotes\n\n### <span id=\"page-63-0\"></span>**8.2.10 Request for Market data (FORWARD)**\n\nQuote Requests for Market data are simpler that those for tradeable quotes. QuoteType is '0', OrderQty is 0 and Currency is not needed. This market data feed will expire at the time specified in ExpireTime.\n\n<span id=\"page-63-2\"></span>\n\n| Tag                            | Attribute                     | Value              |  |\n|--------------------------------|-------------------------------|--------------------|--|\n|                                | <message header=\"\"></message> |                    |  |\n| 35                             | MsgType                       | R                  |  |\n| 131                            | QuoteReqID                    | MyQuoteReqID-12345 |  |\n| 7070                           | RefSpotDate                   | ********           |  |\n| 7071                           | ProductType                   | FX-STD             |  |\n| 146                            | NoRelatedSym                  | 1                  |  |\n| 55                             | Symbol                        | EUR/RUB            |  |\n| 537                            | QuoteType                     | 0                  |  |\n| 54                             | Side                          | 1                  |  |\n| 38                             | OrderQty                      | 0                  |  |\n| 64                             | SettlDate                     | ********           |  |\n| 1                              | Account                       | ACME               |  |\n| 126                            | ExpireTime                    | ********-18:00:00  |  |\n| <message trailer=\"\"></message> |                               |                    |  |\n| continued on next page         |                               |                    |  |\n\n8.3. QuoteRequestReject [AG] Chapter 8: Example Messages\n\n|  | Tag | Attribute | Value |\n|--|-----|-----------|-------|\n|--|-----|-----------|-------|\n\nTable 8.12: Example QuoteRequest for Spot Market data quotes\n\nMarket Data requests for other products are similar to this one. Note that for swap market data requests, OrderQty2 should be set to 0, too.\n\n## <span id=\"page-64-0\"></span>**8.3 QuoteRequestReject [AG]**\n\nA QuoteRequestReject message might look like this:\n\n<span id=\"page-64-2\"></span>\n\n| Tag                            | Attribute                | Value                      |\n|--------------------------------|--------------------------|----------------------------|\n| <message header=\"\"></message>  |                          |                            |\n| 35                             | QuoteReqID               | AG                         |\n| 131                            | QuoteReqID               | MyQuoteReqID-12345         |\n| 658                            | QuoteRequestRejectReason | 99                         |\n| 146                            | NoRelatedSym             | 1                          |\n| 55                             | Symbol                   | EUR/USD                    |\n| 15                             | Currency                 | EUR                        |\n| 7071                           | ProductType              | FX-STD                     |\n| 58                             | Text                     | ExpireTime is in the past. |\n| <message trailer=\"\"></message> |                          |                            |\n\nTable 8.13: Example BusinessMessageReject message\n\n### <span id=\"page-64-1\"></span>**8.4 BusinessMessageReject [j]**\n\nA BusinessMessageReject message might look like this:\n\n<span id=\"page-64-3\"></span>\n\n| Tag                            | Attribute            | Value                                   |\n|--------------------------------|----------------------|-----------------------------------------|\n| <message header=\"\"></message>  |                      |                                         |\n| 35                             | MsgType              | j                                       |\n| 45                             | RefSeqNr             | 45                                      |\n| 58                             | Text                 | Exception while processing the request. |\n| 372                            | RefMsgType           | R                                       |\n| 379                            | BusinessRejectRefID  | MyQuoteReqID-12345                      |\n| 380                            | BusinessRejectReason | 0                                       |\n| <message trailer=\"\"></message> |                      |                                         |\n\nTable 8.14: Example BusinessMessageReject message\n\n## <span id=\"page-65-0\"></span>**8.5 Quote [S]**\n\n### <span id=\"page-65-1\"></span>**8.5.1 Spot Quote - Buy**\n\n<span id=\"page-65-3\"></span>\n\n| Tag                            | Attribute                     | Value                   |  |\n|--------------------------------|-------------------------------|-------------------------|--|\n|                                | <message header=\"\"></message> |                         |  |\n| 35                             | MsgType                       | S                       |  |\n| 1                              | Account                       | ACME                    |  |\n| 15                             | Currency                      | EUR                     |  |\n| 38                             | OrderQty                      | 1000000                 |  |\n| 55                             | Symbol                        | EUR/USD                 |  |\n| 106                            | Issuer                        | Barclays BARX.DEMO      |  |\n| 117                            | QuoteID                       | MyQuoteReq-98765-000001 |  |\n| 131                            | QuoteReqID                    | MyQuoteReq-98765        |  |\n| 190                            | OfferSpotRate                 | 1.4051                  |  |\n| 7070                           | RefSpotDate                   | ********                |  |\n| <message trailer=\"\"></message> |                               |                         |  |\n\n### Table 8.15: Example Spot quote\n\n### <span id=\"page-65-2\"></span>**8.5.2 Forward Quote - Two-Way**\n\nForward Quotes differ from Spot quotes by having OfferPx/BidPx set in addition to OfferSpotRate/BidSpotRate.\n\n<span id=\"page-65-4\"></span>\n\n| Tag  | Attribute                     | Value                   |  |\n|------|-------------------------------|-------------------------|--|\n|      | <message header=\"\"></message> |                         |  |\n| 35   | MsgType                       | S                       |  |\n| 1    | Account                       | ACME                    |  |\n| 15   | Currency                      | EUR                     |  |\n| 38   | OrderQty                      | 1000000                 |  |\n| 55   | Symbol                        | EUR/USD                 |  |\n| 106  | Issuer                        | Barclays BARX.DEMO      |  |\n| 117  | QuoteID                       | MyQuoteReq-98765-000002 |  |\n| 131  | QuoteReqID                    | MyQuoteReq-98765        |  |\n| 132  | BidPx                         | 1.3995                  |  |\n| 133  | OfferPx                       | 1.4070                  |  |\n| 188  | BidSpotRate                   | 1.4007                  |  |\n| 190  | OfferSpotRate                 | 1.4051                  |  |\n| 7070 | RefSpotDate                   | ********                |  |\n|      |                               | continued on next page  |  |\n\n8.5. Quote [S] Chapter 8: Example Messages\n\n| Tag                            | Attribute | Value |\n|--------------------------------|-----------|-------|\n| <message trailer=\"\"></message> |           |       |\n\nTable 8.16: Example Forward quote\n\n### <span id=\"page-66-0\"></span>**8.5.3 FX Time Options quote - Two-Way**\n\nFX Time Options Quotes differ from Forward quotes by having OptionDate and optionally OptionPeriod in addition.\n\n<span id=\"page-66-2\"></span>\n\n| Tag                            | Attribute     | Value                   |\n|--------------------------------|---------------|-------------------------|\n| <message header=\"\"></message>  |               |                         |\n| 35                             | MsgType       | S                       |\n| 1                              | Account       | ACME                    |\n| 15                             | Currency      | EUR                     |\n| 38                             | OrderQty      | 1000000                 |\n| 55                             | Symbol        | EUR/USD                 |\n| 106                            | Issuer        | Barclays BARX.DEMO      |\n| 117                            | QuoteID       | MyQuoteReq-98765-000003 |\n| 131                            | QuoteReqID    | MyQuoteReq-98765        |\n| 132                            | BidPx         | 1.3995                  |\n| 133                            | OfferPx       | 1.4070                  |\n| 188                            | BidSpotRate   | 1.4007                  |\n| 190                            | OfferSpotRate | 1.4051                  |\n| 7070                           | RefSpotDate   | ********                |\n| 9515                           | OptionDate    | ********                |\n| <message trailer=\"\"></message> |               |                         |\n\n### Table 8.17: Example Forward quote\n\n### <span id=\"page-66-1\"></span>**8.5.4 Swap Quote - Two-Way**\n\nIn Addition to Forward quotes, Swap quotes contain data for the far leg, too.\n\n<span id=\"page-66-3\"></span>\n\n| Tag                           | Attribute | Value                  |\n|-------------------------------|-----------|------------------------|\n| <message header=\"\"></message> |           |                        |\n| 35                            | MsgType   | S                      |\n| 1                             | Account   | ACME                   |\n| 15                            | Currency  | EUR                    |\n| 38                            | OrderQty  | 1000000                |\n|                               |           | continued on next page |\n\n| Tag                            | Attribute     | Value                   |\n|--------------------------------|---------------|-------------------------|\n| 55                             | Symbol        | EUR/USD                 |\n| 106                            | Issuer        | Barclays BARX.DEMO      |\n| 117                            | QuoteID       | MyQuoteReq-98765-000004 |\n| 131                            | QuoteReqID    | MyQuoteReq-98765        |\n| 132                            | BidPx         | 1.416989                |\n| 133                            | OfferPx       | 1.1.417064              |\n| 188                            | BidSpotRate   | 1.4172                  |\n| 190                            | OfferSpotRate | 1.4173                  |\n| 6050                           | BidPx2        | 1.4168577               |\n| 6051                           | OfferPx       | 1.4169421               |\n| 7070                           | RefSpotDate   | ********                |\n| <message trailer=\"\"></message> |               |                         |\n\nTable 8.18: Example Forward quote\n\n### <span id=\"page-67-0\"></span>**8.5.5 NDF quote - Two-Way**\n\nNDF quotes look exactly like Forward quotes.\n\n### <span id=\"page-67-1\"></span>**8.5.6 NDS quote - Two-Way**\n\nNDS quotes look exactly like Swap quotes.\n\n### <span id=\"page-67-2\"></span>**8.5.7 Market Data quote - Forward, Two-Way**\n\nMarketData quotes are marked by QuoteType '0'. They always have OrderQty set to 0 and the constant string \"MarketData\" as Issuer. Also the field Currency is not set.\n\n<span id=\"page-67-3\"></span>\n\n| Tag                           | Attribute     | Value                   |\n|-------------------------------|---------------|-------------------------|\n| <message header=\"\"></message> |               |                         |\n| 35                            | MsgType       | S                       |\n| 38                            | OrderQty      | 0                       |\n| 55                            | Symbol        | EUR/USD                 |\n| 106                           | Issuer        | MarketData              |\n| 117                           | QuoteID       | MyQuoteReq-98765-000005 |\n| 131                           | QuoteReqID    | MyQuoteReq-98765        |\n| 132                           | BidPx         | 1.416989                |\n| 133                           | OfferPx       | 1.1.417064              |\n| 188                           | BidSpotRate   | 1.4172                  |\n| 190                           | OfferSpotRate | 1.4173                  |\n|                               |               | continued on next page  |\n\n| Tag                            | Attribute   | Value    |\n|--------------------------------|-------------|----------|\n| 537                            | QuoteType   | 0        |\n| 7070                           | RefSpotDate | ******** |\n| <message trailer=\"\"></message> |             |          |\n\nTable 8.19: Example Forward Market data quote\n\n## <span id=\"page-68-0\"></span>**8.6 QuoteCancel [Z]**\n\n### <span id=\"page-68-1\"></span>**8.6.1 Customer** → **360**\n\n<span id=\"page-68-5\"></span>\n\n| Tag                            | Attribute       | Value                    |\n|--------------------------------|-----------------|--------------------------|\n| <message header=\"\"></message>  |                 |                          |\n| 35                             | MsgType         | Z                        |\n| 117                            | QuoteID         | 0                        |\n| 131                            | QuoteReqID      | LO-EUR/JPY91312851372795 |\n| 298                            | QuoteCancelType | 4                        |\n| 7071                           | ProductType     | FX-STD                   |\n| <message trailer=\"\"></message> |                 |                          |\n\nTable 8.20: Example QuoteCancel message from customer\n\n### <span id=\"page-68-2\"></span>**8.6.2 Customer** ← **360T**\n\n<span id=\"page-68-6\"></span>\n\n| Tag                            | Attribute       | Value                                           |\n|--------------------------------|-----------------|-------------------------------------------------|\n| <message header=\"\"></message>  |                 |                                                 |\n| 35                             | MsgType         | Z                                               |\n| 117                            | QuoteID         | fd8d67a1-8eae-4195-946b-d1306bcaa2c4-<br>000001 |\n| 131                            | QuoteReqID      | fd8d67a1-8eae-4195-946b-d1306bcaa2c4            |\n| 298                            | QuoteCancelType | 1                                               |\n| 7071                           | ProductType     | FX-STD                                          |\n| <message trailer=\"\"></message> |                 |                                                 |\n\nTable 8.21: Example QuoteCancel message from 360T\n\n## <span id=\"page-68-3\"></span>**8.7 NewOrderSingle [D]**\n\n### <span id=\"page-68-4\"></span>**8.7.1 Previously Quoted - Forward**\n\nCustomer wants to buy 100000 EUR against JPY as a forward on a quote from RBS.LND which he received earlier.\n\n<span id=\"page-69-1\"></span>**Tag Attribute Value** <Message Header> MsgType D Account ACME ClOrdID LO-0190CM00011208L0000002009 Currency EUR OrderQty 100000 OrdType D Side 1 Symbol EUR/JPY TransactTime ********-14:13:54.710 SettlDate ******** Issuer RBS.LND QuoteID q77t54rg[2364]a05l\\$94\n\nTable 8.22: Example NewOrderSingle message (forward)\n\n<Message Trailer>\n\n### <span id=\"page-69-0\"></span>**8.7.2 Previously Quoted - FX Time Option**\n\nCustomer wants to buy 100000 EUR against JPY as a FX Time Option on a quote from RBS.LND which he received earlier.\n\n<span id=\"page-69-2\"></span>\n\n| Tag                            | Attribute                     | Value                        |  |\n|--------------------------------|-------------------------------|------------------------------|--|\n|                                | <message header=\"\"></message> |                              |  |\n| 35                             | MsgType                       | D                            |  |\n| 1                              | Account                       | ACME                         |  |\n| 11                             | ClOrdID                       | LO-0190CM00011208L0000002009 |  |\n| 15                             | Currency                      | EUR                          |  |\n| 38                             | OrderQty                      | 100000                       |  |\n| 40                             | OrdType                       | D                            |  |\n| 54                             | Side                          | 1                            |  |\n| 55                             | Symbol                        | EUR/JPY                      |  |\n| 60                             | TransactTime                  | ********-14:13:54.710        |  |\n| 64                             | SettlDate                     | ********                     |  |\n| 106                            | Issuer                        | RBS.LND                      |  |\n| 117                            | QuoteID                       | q77t54rg[2364]a05l\\$94       |  |\n| 9515                           | OptionDate                    | ********                     |  |\n| <message trailer=\"\"></message> |                               |                              |  |\n|                                |                               | continued on next page       |  |\n\n8.7. NewOrderSingle [D] Chapter 8: Example Messages\n\n| Tag<br>Attribute<br>Value |\n|---------------------------|\n|---------------------------|\n\nTable 8.23: Example NewOrderSingle message (forward)\n\n### <span id=\"page-70-0\"></span>**8.7.3 Previously Quoted - Swap**\n\nCustomer wants to buy EUR against JPY as a swap. On the near leg he sells 100000 EUR and buys them again on the far leg. Thus OrderQty2 and SettlDate2 must be set. Side is 1 as the client buys on the far leg.\n\n<span id=\"page-70-2\"></span>\n\n| Tag                           | Attribute                      | Value                  |  |\n|-------------------------------|--------------------------------|------------------------|--|\n| <message header=\"\"></message> |                                |                        |  |\n| 35                            | MsgType                        | D                      |  |\n| 1                             | Account                        | ACME                   |  |\n| 11                            | ClOrdID                        | jhfsadf-fsa            |  |\n| 15                            | Currency                       | EUR                    |  |\n| 38                            | OrderQty                       | 100000                 |  |\n| 40                            | OrdType                        | D                      |  |\n| 54                            | Side                           | 1                      |  |\n| 55                            | Symbol                         | EUR/JPY                |  |\n| 60                            | TransactTime                   | ********-04:56:12.441  |  |\n| 64                            | SettlDate                      | ********               |  |\n| 106                           | Issuer                         | RBS.LND                |  |\n| 117                           | QuoteID                        | q77t54rg[2364]a05l\\$94 |  |\n| 192                           | OrderQty2                      | 100000                 |  |\n| 193                           | SettleDate2                    | 20110901               |  |\n|                               | <message trailer=\"\"></message> |                        |  |\n\nTable 8.24: Example NewOrderSingle message (swap)\n\n### <span id=\"page-70-1\"></span>**8.7.4 Previously Quoted - NDF**\n\nCustomer wants to buy EUR against JPY as an NDF. Therefore the maturity date must be sent with the order message.\n\n<span id=\"page-70-3\"></span>\n\n| Tag                           | Attribute | Value                  |  |\n|-------------------------------|-----------|------------------------|--|\n| <message header=\"\"></message> |           |                        |  |\n| 35                            | MsgType   | D                      |  |\n| 1                             | Account   | ACME                   |  |\n| 11                            | ClOrdID   | jhfsadf-fsa            |  |\n| 15                            | Currency  | EUR                    |  |\n|                               |           | continued on next page |  |\n\n### 8.7. NewOrderSingle [D] Chapter 8: Example Messages\n\n| Tag                            | Attribute    | Value                  |\n|--------------------------------|--------------|------------------------|\n| 38                             | OrderQty     | 100000                 |\n| 40                             | OrdType      | D                      |\n| 54                             | Side         | 1                      |\n| 55                             | Symbol       | EUR/JPY                |\n| 541                            | MaturityDate | ********               |\n| 60                             | TransactTime | ********-08:11:33.071  |\n| 64                             | SettlDate    | ********               |\n| 106                            | Issuer       | RBS.LND                |\n| 117                            | QuoteID      | q77t54rg[2364]a05l\\$94 |\n| <message trailer=\"\"></message> |              |                        |\n\nTable 8.25: Example NewOrderSingle message (NDF)\n\n### <span id=\"page-71-0\"></span>**8.7.5 Previously Quoted - NDS**\n\nCustomer wants to buy EUR against JPY as an NDS. Therefore the maturity dates must be sent with the order message.\n\n<span id=\"page-71-1\"></span>\n\n| Tag                           | Attribute     | Value                          |  |\n|-------------------------------|---------------|--------------------------------|--|\n| <message header=\"\"></message> |               |                                |  |\n| 35                            | MsgType       | D                              |  |\n| 1                             | Account       | ACME                           |  |\n| 11                            | ClOrdID       | jhfsadf-fsa                    |  |\n| 15                            | Currency      | EUR                            |  |\n| 38                            | OrderQty      | 100000                         |  |\n| 40                            | OrdType       | D                              |  |\n| 54                            | Side          | 1                              |  |\n| 55                            | Symbol        | EUR/RUB                        |  |\n| 541                           | MaturityDate  | ********                       |  |\n| 7541                          | MaturityDate2 | ********                       |  |\n| 60                            | TransactTime  | ********-04:56:12.441          |  |\n| 64                            | SettlDate     | ********                       |  |\n| 106                           | Issuer        | RBS.LND                        |  |\n| 117                           | QuoteID       | q77t54rg[2364]a05l\\$94         |  |\n| 192                           | OrderQty2     | 100000                         |  |\n| 193                           | SettleDate2   | 20110901                       |  |\n|                               |               | <message trailer=\"\"></message> |  |\n|                               |               | continued on next page         |  |\n\n8.7. NewOrderSingle [D] Chapter 8: Example Messages\n\n| Tag<br>Attribute | Value |\n|------------------|-------|\n|------------------|-------|\n\nTable 8.26: Example NewOrderSingle message (swap)\n\n### <span id=\"page-72-0\"></span>**8.7.6 Market order - Forward**\n\nCustomer wants to buy 100000 EUR against JPY as a market order (OrdType 1). Instead of a QuoteID, the QuotRequestID is sent.\n\n<span id=\"page-72-2\"></span>\n\n| Tag                            | Attribute                     | Value                        |  |  |\n|--------------------------------|-------------------------------|------------------------------|--|--|\n|                                | <message header=\"\"></message> |                              |  |  |\n| 35                             | MsgType                       | D                            |  |  |\n| 1                              | Account                       | ACME                         |  |  |\n| 11                             | ClOrdID                       | LO-0190CM00011208L0000002009 |  |  |\n| 15                             | Currency                      | EUR                          |  |  |\n| 38                             | OrderQty                      | 100000                       |  |  |\n| 40                             | OrdType                       | 1                            |  |  |\n| 54                             | Side                          | 1                            |  |  |\n| 55                             | Symbol                        | EUR/JPY                      |  |  |\n| 60                             | TransactTime                  | ********-14:13:54.710        |  |  |\n| 64                             | SettlDate                     | ********                     |  |  |\n| 117                            | QuoteID                       | 321423-43241-55432-12342     |  |  |\n| <message trailer=\"\"></message> |                               |                              |  |  |\n\nTable 8.27: Example NewOrderSingle message (Market order)\n\n### <span id=\"page-72-1\"></span>**8.7.7 Limit order - Forward**\n\nCustomer wants to buy 100000 EUR against USD as a Limit order. The limit price is 1.4.\n\n<span id=\"page-72-3\"></span>\n\n| Tag                           | Attribute | Value                        |  |\n|-------------------------------|-----------|------------------------------|--|\n| <message header=\"\"></message> |           |                              |  |\n| 35                            | MsgType   | D                            |  |\n| 1                             | Account   | ACME                         |  |\n| 11                            | ClOrdID   | LO-0190CM00011208L0000002009 |  |\n| 15                            | Currency  | EUR                          |  |\n| 38                            | OrderQty  | 100000                       |  |\n| 40                            | OrdType   | 2                            |  |\n| 44                            | Price     | 1.4                          |  |\n| 54                            | Side      | 1                            |  |\n|                               |           | continued on next page       |  |\n\n| Tag                            | Attribute    | Value                    |\n|--------------------------------|--------------|--------------------------|\n| 55                             | Symbol       | EUR/USD                  |\n| 60                             | TransactTime | ********-14:13:54.710    |\n| 64                             | SettlDate    | ********                 |\n| 117                            | QuoteID      | 321423-43241-55432-12342 |\n| <message trailer=\"\"></message> |              |                          |\n\nTable 8.28: Example NewOrderSingle message (Limit order)\n\n## <span id=\"page-73-0\"></span>**8.8 NewOrderMultileg [AB]**\n\n### <span id=\"page-73-1\"></span>**8.8.1 Previously Quoted - FX Block Trade**\n\n<span id=\"page-73-2\"></span>\n\n| Tag  | Attribute    | Value                                       |\n|------|--------------|---------------------------------------------|\n|      |              | <message header=\"\"></message>               |\n| 35   | MsgType      | AB                                          |\n| 1    | Account      | GroupE                                      |\n| 11   | ClOrdID      | b7gt95-v                                    |\n| 15   | Currency     | USD                                         |\n| 38   | OrderQty     | 11000                                       |\n| 40   | OrdType      | D                                           |\n| 54   | Side         | 1                                           |\n| 55   | Symbol       | USD/BRL                                     |\n| 60   | TransactTime | ********-11:37:05.635                       |\n| 64   | SettlDate    | ********                                    |\n| 106  | Issuer       | Morgan Stanley.TEST                         |\n| 117  | QuoteID      | 4c5c455c-a2cc-4eef-8387-53f74e384454-000003 |\n| 7071 | ProductType  | FX-BT                                       |\n| 555  | NoLegs       | 2                                           |\n| 600  | LegSymbol    | USD/BRL                                     |\n| 624  | LegSide      | 1                                           |\n| 687  | LegQty       | 5000.0                                      |\n| 588  | LegSettlDate | ********                                    |\n| 566  | LegPrice     | 5.1954                                      |\n| 654  | LegRefID     | 39b7001a-4508-4365-b139-f77c6c858959        |\n| 600  | LegSymbol    | USD/BRL                                     |\n| 624  | LegSide      | 1                                           |\n|      |              | continued on next page                      |\n\n| Tag | Attribute    | Value                                |\n|-----|--------------|--------------------------------------|\n| 687 | LegQty       | 6000.0                               |\n| 588 | LegSettlDate | ********                             |\n| 566 | LegPrice     | 5.2194                               |\n| 654 | LegRefID     | 53dcd8e7-092a-4ba1-bcc3-62fe3b815711 |\n\nTable 8.29: Example NewOrderMultileg message (FX Block Trade)\n\n### <span id=\"page-74-0\"></span>**8.8.2 Market order - NDF Block Trade**\n\n<span id=\"page-74-1\"></span>\n\n| Tag  | Attribute       | Value                                |\n|------|-----------------|--------------------------------------|\n|      |                 | <message header=\"\"></message>        |\n| 35   | MsgType         | AB                                   |\n| 1    | Account         | GroupE                               |\n| 11   | ClOrdID         | Test-snangarath-9dff46273b45         |\n| 15   | Currency        | USD                                  |\n| 38   | OrderQty        | 11000                                |\n| 40   | OrdType         | D                                    |\n| 54   | Side            | 1                                    |\n| 55   | Symbol          | USD/BRL                              |\n| 60   | TransactTime    | ********-11:37:05.635                |\n| 64   | SettlDate       | ********                             |\n| 106  | Issuer          | JPMORGAN.DEMO                        |\n| 117  | QuoteID         | c63f07a1b8d4                         |\n| 7071 | ProductType     | FX-BT                                |\n| 555  | NoLegs          | 2                                    |\n| 600  | LegSymbol       | USD/BRL                              |\n| 611  | LegMaturityDate | ********                             |\n| 624  | LegSide         | 1                                    |\n| 687  | LegQty          | 5000                                 |\n| 588  | LegSettlDate    | ********                             |\n| 566  | LegPrice        | 5.1954                               |\n| 654  | LegRefID        | e5b9f2d4-26fb-4c37-af32-52a6193b1097 |\n| 600  | LegSymbol       | USD/BRL                              |\n| 611  | LegMaturityDate | ********                             |\n| 624  | LegSide         | 1                                    |\n| 687  | LegQty          | 6000                                 |\n|      |                 | continued on next page               |\n\n| Tag | Attribute    | Value                                |\n|-----|--------------|--------------------------------------|\n| 588 | LegSettlDate | ********                             |\n| 566 | LegPrice     | 5.2194                               |\n| 654 | LegRefID     | 467dd1e3-8446-4b5a-b380-ebb04c8e13e2 |\n\nTable 8.30: Example NewOrderMultileg message (NDF Block Trade)\n\n## <span id=\"page-75-0\"></span>**8.9 ExecutionReport [8]**\n\n### <span id=\"page-75-1\"></span>**8.9.1 Execution report - Order received**\n\nThis is an example execution report that is sent when 360T has received the order message. Note that the Price is still 0.\n\n<span id=\"page-75-2\"></span>\n\n| Tag | Attribute                      | Value                                    |  |\n|-----|--------------------------------|------------------------------------------|--|\n|     | <message header=\"\"></message>  |                                          |  |\n| 35  | MsgType                        | 8                                        |  |\n| 1   | Account                        | ACME                                     |  |\n| 6   | AvgPx                          | 0                                        |  |\n| 11  | ClOrdID                        | LO-0190CM00011208L0000002009             |  |\n| 14  | CumQty                         | 0                                        |  |\n| 15  | Currency                       | EUR                                      |  |\n| 17  | ExecID                         | 0                                        |  |\n| 32  | LastQty                        | 0                                        |  |\n| 37  | OrderID                        | **********                               |  |\n| 38  | OrderQty                       | 100000                                   |  |\n| 39  | OrdStatus                      | 0                                        |  |\n| 40  | OrdType                        | D                                        |  |\n| 44  | Price                          | 0                                        |  |\n| 54  | Side                           | 1                                        |  |\n| 55  | Symbol                         | EUR/JPY                                  |  |\n| 58  | Text                           | NewOrderSingle message has been received |  |\n| 64  | SettlDate                      | ********                                 |  |\n| 150 | ExecType                       | 0                                        |  |\n| 151 | LeavesQty                      | 0                                        |  |\n|     | <message trailer=\"\"></message> |                                          |  |\n\nTable 8.31: Example ExecutionReport message (confirmation of receipt)\n\n### <span id=\"page-76-0\"></span>**8.9.2 Execution report - Order executed**\n\nShortly after the received message, an execution message is sent to the customer with OrdStatus \"Filled\" and ExecType \"Trade\".\n\n<span id=\"page-76-2\"></span>\n\n| Tag | Attribute                      | Value                        |  |  |\n|-----|--------------------------------|------------------------------|--|--|\n|     | <message header=\"\"></message>  |                              |  |  |\n| 35  | MsgType                        | 8                            |  |  |\n| 1   | Account                        | ACME                         |  |  |\n| 6   | AvgPx                          | 0                            |  |  |\n| 11  | ClOrdID                        | LO-0190CM00011208L0000002009 |  |  |\n| 14  | CumQty                         | 100000                       |  |  |\n| 15  | Currency                       | EUR                          |  |  |\n| 17  | ExecID                         | 9w3ffq-a0734-gr45t8ks-1dp    |  |  |\n| 31  | LastPx                         | 108.85574                    |  |  |\n| 32  | LastQty                        | 0                            |  |  |\n| 37  | OrderID                        | **********                   |  |  |\n| 38  | OrderQty                       | 100000                       |  |  |\n| 39  | OrdStatus                      | 2                            |  |  |\n| 40  | OrdType                        | D                            |  |  |\n| 54  | Side                           | 1                            |  |  |\n| 55  | Symbol                         | EUR/JPY                      |  |  |\n| 60  | TransactTime                   | 20110805-16:10:32.804        |  |  |\n| 64  | SettlDate                      | ********                     |  |  |\n| 150 | ExecType                       | F                            |  |  |\n| 151 | LeavesQty                      | 0                            |  |  |\n| 194 | LastSpotRate                   | 104.23                       |  |  |\n| 453 | NoPartyIDs                     | 1                            |  |  |\n| 448 | PartyID                        | RBS.LND                      |  |  |\n| 447 | PartyIDSource                  | D                            |  |  |\n| 452 | PartyRole                      | 35                           |  |  |\n|     | <message trailer=\"\"></message> |                              |  |  |\n\nTable 8.32: Example ExecutionReport message (execution confirmation)\n\n## <span id=\"page-76-1\"></span>**8.10 SecurityDefinitionRequest [c]**\n\nAn example SecurityDefinitionRequest message sent from customer to 360T asking for financial calender information for the EUR/USD currency pair.\n\n<span id=\"page-77-1\"></span>\n\n| Tag                            | Attribute           | Value               |\n|--------------------------------|---------------------|---------------------|\n| <message header=\"\"></message>  |                     |                     |\n| 35                             | MsgType             | c                   |\n| 320                            | SecurityReqID       | MySecDefReqID-12345 |\n| 321                            | SecurityRequestType | 3                   |\n| 55                             | Symbol              | EUR/USD             |\n| <message trailer=\"\"></message> |                     |                     |\n\nTable 8.33: Example SecurityDefinitionRequest message\n\n## <span id=\"page-77-0\"></span>**8.11 SecurityDefinition [d]**\n\nAn example SecurityDefinition message sent from 360T to the customer. Note: the FX tenors featured in this example are not exhaustive - a smaller selection is used to reduce the length of the example message.\n\n<span id=\"page-77-2\"></span>\n\n| Tag | Attribute                     | Value                         |  |\n|-----|-------------------------------|-------------------------------|--|\n|     | <message header=\"\"></message> |                               |  |\n| 35  | MsgType                       | d                             |  |\n| 320 | SecurityReqID                 | MySecDefReqID-12345           |  |\n| 322 | SecurityResponseID            | SD-aa9slo-a074c-j0m5o2fa-26n1 |  |\n| 321 | SecurityRequestType           | 4                             |  |\n| 711 | NoUnderlyings                 | 5                             |  |\n| 311 | UnderlyingSymbol              | EUR/USD                       |  |\n| 309 | UnderlyingSecurityID          | TD                            |  |\n| 542 | UnderlyingMaturityDate        | 20170322                      |  |\n| 307 | UnderlyingSecurityDesc        | TODAY                         |  |\n| 311 | UnderlyingSymbol              | EUR/USD                       |  |\n| 309 | UnderlyingSecurityID          | TM                            |  |\n| 542 | UnderlyingMaturityDate        | 20170323                      |  |\n| 307 | UnderlyingSecurityDesc        | TOMORROW                      |  |\n| 311 | UnderlyingSymbol              | EUR/USD                       |  |\n| 309 | UnderlyingSecurityID          | SP                            |  |\n| 542 | UnderlyingMaturityDate        | 20170324                      |  |\n| 307 | UnderlyingSecurityDesc        | SPOT                          |  |\n| 311 | UnderlyingSymbol              | EUR/USD                       |  |\n| 309 | UnderlyingSecurityID          | 1W                            |  |\n| 542 | UnderlyingMaturityDate        | 20170331                      |  |\n| 307 | UnderlyingSecurityDesc        | 1<br>WEEK                     |  |\n|     |                               | continued on next page        |  |\n\n| Tag                            | Attribute              | Value     |\n|--------------------------------|------------------------|-----------|\n| 311                            | UnderlyingSymbol       | EUR/USD   |\n| 309                            | UnderlyingSecurityID   | 1Y        |\n| 542                            | UnderlyingMaturityDate | 20180326  |\n| 307                            | UnderlyingSecurityDesc | 1<br>YEAR |\n| <message trailer=\"\"></message> |                        |           |\n\nTable 8.34: Example SecurityDefinition message\n\n## <span id=\"page-79-0\"></span>**9 Firewall Configuration**\n\nFor the connections we use the following IP addresses:\n\n<span id=\"page-79-1\"></span>\n\n| Environment/<br>Production |                 | Integration |                |      |\n|----------------------------|-----------------|-------------|----------------|------|\n| Connection                 | IP              | Port        | IP             | Port |\n| Internet<br>(plain)        | n/a             | n/a         | ************   | 7001 |\n| Stunnel                    | *************   | 7001        | ************   | 7001 |\n| Radianz                    | *************** | 7001        | ************** | 7001 |\n\nTable 9.1: 360T IP addresses\n\n## <span id=\"page-80-0\"></span>**10 FIX Session Reset**\n\nBy default, a FIX session reset is performed according to the schedule defined in the following table.\n\n<span id=\"page-80-1\"></span>\n\n| Setting             | Description                                                    | Default<br>Value |\n|---------------------|----------------------------------------------------------------|------------------|\n| Timezone            | Time zone to be used for the session schedule.                 | America/New York |\n| Start Time          | Time of day that this FIX session becomes activated.           | 17:01:00         |\n| End Time            | Time of day that this FIX session becomes deactivated.         | 17:00:00         |\n| Start Day           | Starting day of week for the session.                          | Saturday         |\n| End Day             | Ending day of week for the session.                            | Saturday         |\n| Reset On Logon      | Sequence number is reset when recieving a logon request.       | Yes              |\n| Reset On Logout     | Sequence number is reset to 1 after normal logout termination. | No               |\n| Reset On Disconnect | Sequence number is reset to 1 after abnormal termination.      | Yes              |\n\nTable 10.1: 360T FIX Session\n\n## <span id=\"page-81-0\"></span>**11 Appendix**\n\n## <span id=\"page-81-1\"></span>**11.1 CFI Codes**\n\nCFI codes used are based on the ISO 10962 standard. Please note that with options, the defined CFI code is used to identify the type of the option (CALL/PUT) with relation to currency 1 as it's set in the 360T system. Due to ANNA-DSB normalization, the CFI code passed in this API may not match that of the ISIN provided.\n\n<span id=\"page-81-3\"></span>\n\n| CFI<br>Code | Details                                                                                                                    |\n|-------------|----------------------------------------------------------------------------------------------------------------------------|\n| JFTXFP      | Fx Spot, Fx Forward and Fx Option with Delta Hedge Strategy, as well as spot and<br>forward legs of block trades and swaps |\n| JFTXFC      | NDF products as well as legs of NDS                                                                                        |\n| JFRXFP      | Fx Spot and Fx Forward with CNH                                                                                            |\n| SFCXXP      | Fx Swap including forward swaps                                                                                            |\n| SFCXXC      | NDS                                                                                                                        |\n| HFTAVP      | Fx European Option with CALL on the first currency                                                                         |\n| HFTDVP      | Fx European Option with PUT on the first currency                                                                          |\n| HFTBVP      | Fx American Option with CALL on the first currency                                                                         |\n| HFTEVP      | Fx American Option with PUT on the first currency                                                                          |\n| MRCXXX      | Fx Option with Delta Hedge StrategyMoney and Market Deposit                                                                |\n\nTable 11.1: Supported CFI codes.\n\n## <span id=\"page-81-2\"></span>**11.2 Fixing Sources for NDF/NDS**\n\n<span id=\"page-81-4\"></span>\n\n| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                               |\n|--------|-----------------------------------------------------------------------------------------------------|\n| USDARS | 'ARS01', 'ARS02', 'EMTA (ARS05)'                                                                    |\n| USDBRL | 'BRL01', 'BRL02', 'BRL03', 'BRL10', 'BRL11', 'PTAX (BRL09)', 'Pontos sobre<br>PTAX', 'NDF Asiatica' |\n| USDCLP | 'CLP01', 'CLP02', 'CLP03', 'CLP04', 'CLP08', 'CLP09', 'CLPOBS (CLP10)'                              |\n| USDCNY | 'SAEC (CNY01)'                                                                                      |\n| USDCOP | 'COP01', 'COP TRM (COP02)', 'CO/COL3'                                                               |\n| USDCRC | 'CRREB'                                                                                             |\n| USDEGP | 'FEMF (EGP01)'                                                                                      |\n| USDGHS | 'GHS TR (GHS03)'                                                                                    |\n| USDIDR | 'JISDOR (IDR04)', 'ABSIRFIX01', 'INR01', 'RBIB'                                                     |\n| USDINR | 'FBIL'                                                                                              |\n\n| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                                                                                                                                                     |\n|--------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| USDKES | 'KES TR (KES01)'                                                                                                                                                                                                          |\n| USDKRW | 'KRW02', 'KFTC18'                                                                                                                                                                                                         |\n| USDKZT | 'KZFXWA', 'KZT KASE (KZT01)'                                                                                                                                                                                              |\n| USDMYR | 'MYR PPKM (MYR03)', 'MYR KL REF (MYR04)'                                                                                                                                                                                  |\n| USDNGN | 'NGN NiFEX (NGN01)', 'NGN NAFEX (NGN03)'                                                                                                                                                                                  |\n| USDPEN | 'PEN01', 'PEN02', 'PEN05'                                                                                                                                                                                                 |\n| USDPHP | 'PHP01', 'PHP02', 'PHP03', 'PHP04', 'PDSPESO', 'PHP BAPPESO (PHP06)'                                                                                                                                                      |\n| USDRSD | 'RSDFIX'                                                                                                                                                                                                                  |\n| USDRUB | 'RUB01', 'RUB02', 'RUB MOEX (RUB05)'                                                                                                                                                                                      |\n| USDTWD | 'TWD01', 'TWD02', 'TAIFX1 (TWD03)'                                                                                                                                                                                        |\n| USDUAH | 'EMTAUAHFIX', 'EMTA UAH ISR (UAH02)'                                                                                                                                                                                      |\n| USDVEF | 'VEB01'                                                                                                                                                                                                                   |\n| USDVND | 'ABSIRFIX01'                                                                                                                                                                                                              |\n| EURARS | 'EMTA (ARS05)-BFIX EUR L080', 'EMTA (ARS05)-BFIX EUR L130', 'EMTA<br>(ARS05)-BFIX EUR L160', 'EMTA (ARS05)-WMCo 8am LDN', 'EMTA (ARS05)-<br>WMCo 1pm LDN', 'EMTA (ARS05)-WMCo 4pm LDN'                                    |\n| EURBRL | 'PTAX-BFIX EUR L080', 'PTAX-BFIX EUR L130', 'PTAX-BFIX EUR L160',<br>'PTAX-WMCo 8am LDN', 'PTAX-WMCo 1pm LDN', 'PTAX-WMCo 4pm LDN',<br>'NDF Asiatica', 'Pontos sobre PTAX'                                                |\n| EURCLP | 'CLPOBS<br>(CLP10)-BFIX<br>EUR<br>L080',<br>'CLPOBS<br>(CLP10)-BFIX<br>EUR<br>L130',<br>'CLPOBS (CLP10)-BFIX EUR L160',<br>'CLPOBS (CLP10)-WMCo 8am LDN',<br>'CLPOBS (CLP10)-WMCo 1pm LDN', 'CLPOBS (CLP10)-WMCo 4pm LDN' |\n| EURCNY | 'SAEC (CNY01)-BFIX EUR L080', 'SAEC (CNY01)-BFIX EUR L130', 'SAEC<br>(CNY01)-BFIX EUR L160', 'SAEC (CNY01)-WMCo 8am LDN', 'SAEC (CNY01)-<br>WMCo 1pm LDN', 'SAEC (CNY01)-WMCo 4pm LDN'                                    |\n| EURCOP | 'TRM (COP02)-BFIX EUR L080',<br>'TRM (COP02)-BFIX EUR L130',<br>'TRM<br>(COP02)-BFIX EUR L160', 'TRM (COP02)-WMCo 8am LDN', 'TRM (COP02)-<br>WMCo 1pm LDN', 'TRM (COP02)-WMCo 4pm LDN'                                    |\n| EURINR | 'FBIL', 'FBIL-BFIX EUR L080', 'FBIL-BFIX EUR L130', 'FBIL-BFIX EUR L160',<br>'FBIL-WMCo 8am LDN', 'FBIL-WMCo 1pm LDN', 'FBIL-WMCo 4pm LDN'                                                                                |\n| EURIDR | 'JISDOR (IDR04)-BFIX EUR L080', 'JISDOR (IDR04)-BFIX EUR L130', 'JIS<br>DOR (IDR04)-BFIX EUR L160', 'JISDOR (IDR04)-WMCo 8am LDN', 'JISDOR<br>(IDR04)-WMCo 1pm LDN', 'JISDOR (IDR04)-WMCo 4pm LDN'                        |\n| EURKRW | 'KFTC18-BFIX EUR L080', 'KFTC18-BFIX EUR L130', 'KFTC18-BFIX EUR<br>L160',<br>'KFTC18-WMCo 8am LDN', 'KFTC18-WMCo 1pm LDN', 'KFTC18-<br>WMCo 4pm LDN'                                                                     |\n| EURKZT | 'KZFXWA-BFIX EUR L080', 'KZFXWA-BFIX EUR L130', 'KZFXWA-BFIX EUR<br>L160', 'KZFXWA-WMCo 8am LDN', 'KZFXWA-WMCo 1pm LDN', 'KZFXWA<br>WMCo 4pm LDN'                                                                         |\n\n| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                                                                                                                                                  |\n|--------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| EURMYR | 'ABSIRFIX01-BFIX EUR L080', 'ABSIRFIX01-BFIX EUR L130', 'ABSIRFIX01-<br>BFIX EUR L160', 'ABSIRFIX01-WMCo 8am LDN', 'ABSIRFIX01-WMCo 1pm<br>LDN', 'ABSIRFIX01-WMCo 4pm LDN'                                             |\n| EURPEN | 'PEN05-BFIX EUR L080', 'PEN05-BFIX EUR L130', 'PEN05-BFIX EUR L160',<br>'PEN05-WMCo 8am LDN', 'PEN05-WMCo 1pm LDN', 'PEN05-WMCo 4pm LDN'                                                                               |\n| EURPHP | 'PDSPESO-BFIX EUR L080', 'PDSPESO-BFIX EUR L130', 'PDSPESO-BFIX<br>EUR<br>L160',<br>'PDSPESO-WMCo<br>8am<br>LDN',<br>'PDSPESO-WMCo<br>1pm<br>LDN',<br>'PDSPESO-WMCo 4pm LDN'                                           |\n| EURRUB | 'RUB MOEX (RUB05)-BFIX EUR L080', 'RUB MOEX (RUB05)-BFIX EUR<br>L130', 'RUB MOEX (RUB05)-BFIX EUR L160', 'RUB MOEX (RUB05)-WMCo<br>8am LDN', 'RUB MOEX (RUB05)-WMCo 1pm LDN', 'RUB MOEX (RUB05)-<br>WMCo 4pm LDN'      |\n| EURTWD | 'TAIFX1<br>(TWD03)-BFIX<br>EUR<br>L080',<br>'TAIFX1<br>(TWD03)-BFIX<br>EUR L130',<br>'TAIFX1 (TWD03)-BFIX EUR L160',<br>'TAIFX1 (TWD03)-WMCo 8am LDN',<br>'TAIFX1 (TWD03)-WMCo 1pm LDN', 'TAIFX1 (TWD03)-WMCo 4pm LDN' |\n| GBPARS | 'EMTA (ARS05)-WMCo 8am LDN', 'EMTA (ARS05)-WMCo 1pm LDN', 'EMTA<br>(ARS05)-WMCo 4pm LDN', 'EMTA (ARS05)-BFIX GBP L080'                                                                                                 |\n| GBPBRL | 'PTAX-WMCo 8am LDN', 'PTAX-WMCo 1pm LDN', 'PTAX-WMCo 4pm LDN',<br>'PTAX-BFIX GBP L080', 'Pontos sobre PTAX', 'NDF Asiatica'                                                                                            |\n| GBPCLP | 'CLPOBS-WMCo 8am LDN', 'CLPOBS-WMCo 1pm LDN', 'CLPOBS-WMCo<br>4pm LDN', 'CLOPBS-BFIX GBP L080'                                                                                                                         |\n| GBPCNY | 'SAEC-WMCo 8am LDN', 'SAEC-WMCo 1pm LDN', 'SAEC-WMCo 4pm LDN',<br>'SAEC-BFIX GBP L080'                                                                                                                                 |\n| GBPCOP | 'COP TRM-WMCo 8am LDN', 'COP TRM-WMCo 1pm LDN', 'COP TRM-WMCo<br>4pm LDN', 'COP TRM-BFIX GBP L080'                                                                                                                     |\n| GBPEGP | 'FEMF-WMCo 8am LDN', 'FEMF-WMCo 1pm LDN', 'FEMF-WMCo 4pm LDN',<br>'FEMF-BFIX GBP L080'                                                                                                                                 |\n| GBPIDR | 'JISDOR-WMCo 8am LDN', 'JISDOR-WMCo 1pm LDN', 'JISDOR-WMCo 4pm<br>LDN', 'JISDOR-BFIX GBP L080'                                                                                                                         |\n| GBPINR | 'FBIL', 'FBIL-WMCo 8am LDN', 'FBIL-WMCo 1pm LDN', 'FBIL-WMCo 4pm<br>LDN', 'FIBL-BFIX GBP L080'                                                                                                                         |\n| GBPKRW | 'KFTC18-WMCo 8am LDN', 'KFTC18-WMCo 1pm LDN', 'KFTC18-WMCo 4pm<br>LDN', 'KFTC18-BFIX GBP L080'                                                                                                                         |\n| GBPKZT | 'KZFXWA-WMCo 8am LDN', 'KZFXWA-WMCo 1pm LDN', 'KZFXWA-WMCo<br>4pm LDN', 'KZFXWA-BFIX GBP L080'                                                                                                                         |\n| GBPMYR | 'MYR PPKM-WMCo 8am LDN', 'MYR PPKM-WMCo 1pm LDN', 'MYR PPKM<br>WMCo 4pm LDN', 'MYR PPKM-BFIX GBP L080'                                                                                                                 |\n| GBPPEN | 'PEN05-WMCo 8am LDN', 'PEN05-WMCo 1pm LDN', 'PEN05-WMCo 4pm<br>LDN', 'PEN05-BFIX GBP L080'                                                                                                                             |\n| GBPPHP | 'PDSPESO-WMCo 8am LDN', 'PDSPESO-WMCo 1pm LDN', 'PDSPESO-WMCo<br>4pm LDN', 'PDSPESO-BFIX GBP L080'                                                                                                                     |\n| GBPRSD | 'RSDFIX-WMCo 8am LDN', 'RSDFIX-WMCo 1pm LDN', 'RSDFIX-WMCo 4pm<br>LDN', 'RSDFIX-BFIX GBP L080'                                                                                                                         |\n\n![](_page_84_Picture_0.jpeg)\n\n| Symbol | List<br>of<br>supported<br>fixing<br>source<br>values                                                                                     |\n|--------|-------------------------------------------------------------------------------------------------------------------------------------------|\n| GBPRUB | 'RUB MOEX (RUB05)-WMCo 8am LDN', 'RUB MOEX (RUB05)-WMCo 1pm<br>LDN', 'RUB MOEX (RUB05)-WMCo 4pm LDN', 'RUB MOEX (RUB05)-BFIX<br>GBP L080' |\n| GBPTWD | 'TAIFX1-WMCo 8am LDN', 'TAIFX1-WMCo 1pm LDN', 'TAIFX1-WMCo 4pm<br>LDN', 'TAIFX1-BFIX GBP L080'                                            |\n| GBPUAH | 'EMTAUAHFUIX-WMCo<br>8am<br>LDN',<br>'EMTAUAHFUIX-WMCo<br>1pm<br>LDN',<br>'EMTAUAHFUIX-WMCo 4pm LDN', 'EMTAUAHFUIX-BFIX GBP L080'         |\n| GBPVES | 'VEB01-WMCo 8am LDN', 'VEB01-WMCo 1pm LDN', 'VEB01-WMCo 4pm<br>LDN', 'VEB01-BFIX GBP L080'                                                |\n| GBPVND | 'ABSIRFIX01-WMCo<br>8am<br>LDN',<br>'ABSIRFIX01-WMCo<br>1pm<br>LDN',<br>'ABSIRFIX01-WMCo 4pm LDN', 'ABSIRFIX01-BFIX GBP L080'             |\n| BRL*** | 'NDF Asiatica', 'Pontos sobre PTAX'                                                                                                       |\n\nTable 11.2: Supported fixing source string for NDF/NDS.\n\n## <span id=\"page-85-0\"></span>**12 Version Log**\n\n| Version | Date       | Comments                                                                                                                                                                                                                                                                                                                                                                                     |\n|---------|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 1.0     | 2010-09-27 | Initial Version                                                                                                                                                                                                                                                                                                                                                                              |\n| 1.1     | 2010-11-11 | Fixed tag number for QuoteType in QuoteRequest.                                                                                                                                                                                                                                                                                                                                              |\n| 1.2     | 2010-11-30 | Removed Direct Line connection option                                                                                                                                                                                                                                                                                                                                                        |\n| 1.3     | 2010-12-15 | Added additional Fields (Prices/Dates) in all messages                                                                                                                                                                                                                                                                                                                                       |\n| 1.4     | 2011-01-20 | Send individual News messages for each product, removed point fields in<br>Quote messages, added 'New' status for ExecutionReport message, added val<br>ues for ExecType field in ExecutionReport                                                                                                                                                                                            |\n| 1.5     | 2011-02-03 | Added Limit and Market orders.                                                                                                                                                                                                                                                                                                                                                               |\n| 1.6     | 2011-02-28 | Added NDFs.                                                                                                                                                                                                                                                                                                                                                                                  |\n| 1.7     | 2011-05-25 | Noted that Password field is mandatory for Logon message, and removed un<br>used Username field                                                                                                                                                                                                                                                                                              |\n| 1.8     | 2011-07-18 | multiple changes:<br>• clarified Side<54> for swaps<br>• added NDF references to several field descriptions<br>• added MaturityDate<541> to several messages<br>• QuoteRequestID<131> may be 50 characters in size<br>• ExpireTime<126> may not be more than 5 minutes in the future<br>• OrdType<40><br>in<br>Execution<br>report<br>reflects<br>the<br>one<br>sent<br>in<br>NewOrderSingle |\n| 1.9     | 2011-07-28 | added MarketData functionality                                                                                                                                                                                                                                                                                                                                                               |\n| 1.10    | 2011-08-09 | Added example messages                                                                                                                                                                                                                                                                                                                                                                       |\n| 1.11    | 2011-08-11 | added note regarding responsibility for timeouts                                                                                                                                                                                                                                                                                                                                             |\n| 1.12    | 2011-10-28 | added missing field to ExecutionReport, small layout changes,                                                                                                                                                                                                                                                                                                                                |\n| 1.13    | 2012-04-26 | removed tag 40 (OrdType) from QuoteRequest                                                                                                                                                                                                                                                                                                                                                   |\n| 1.14    | 2012-06-12 | Added Important disclaimer for API clients                                                                                                                                                                                                                                                                                                                                                   |\n| 1.15    | 2012-10-18 | Renamed to RFS API                                                                                                                                                                                                                                                                                                                                                                           |\n|         |            | continued on next page                                                                                                                                                                                                                                                                                                                                                                       |\n\n| Version | Date       | Comments                                                                                                                         |\n|---------|------------|----------------------------------------------------------------------------------------------------------------------------------|\n| 3.0     | 2013-01-17 | multiple changes:                                                                                                                |\n|         |            | • added BusinessRejectRefID in BusinessMessageReject message                                                                     |\n|         |            | • Moved provider name to Parties component in ExecutionReport                                                                    |\n|         |            | • Layout changes                                                                                                                 |\n|         |            |                                                                                                                                  |\n| 3.1     | 2013-05-08 | moved MaturityDate (tag 541) after Symbol (tag 55)                                                                               |\n| 4.0     | 2013-07-08 | Multiple changes:                                                                                                                |\n|         |            | • Pre-trade allocations can be set on the QuoteRequest message with<br>NoAllocs (tag 78) and NoLegAllocs (tag 670) nested groups |\n|         |            | • ExecutionReport contains reference spot rate in field LastSpotRate (tag<br>194)                                                |\n|         |            | • Support of NewOrderMultileg message as an alternative for FX Swap<br>orders.                                                   |\n| 5.0     | 2013-07-08 | add Unique Swap Identifier fields and MidRates                                                                                   |\n| 5.1     | 2013-08-07 | Updated description of tag 37 in ExecutionReport message and examples.                                                           |\n| 5.2     | 2013-08-20 | RefSpotDate is no longer mandatory in QuoteRequest message                                                                       |\n| 5.3     | 2013-08-27 | Fixed a bug in spec (Quote message contains RefSpotDate<7070> rather than<br>ProductType<7071>                                   |\n| 6.0     | 2013-09-04 | Multiple changes:                                                                                                                |\n|         |            | • The field Side in business messages can be configured in the session on<br>360T side to be relative to notional currency       |\n|         |            | • QuoteCancel messages will be sent upon quote request expiration and as<br>confirmation of a client QuoteCancel message.        |\n| 6.1     | 2014-03-19 | add Firewall configuration                                                                                                       |\n| 7.0     | 2014-03-24 | Send<br>Bank<br>Basket<br>for<br>each<br>TAS(TradeAsEntity)<br>and<br>TOB(TradeOnBehalfEntity)                                   |\n| 7.1     | 2014-03-24 | UTI fields and NDS support                                                                                                       |\n| 7.2     | 2014-03-28 | Add support for FX Time Options                                                                                                  |\n| 7.3     | 2014-06-04 | Update the location of OptionDate in QuoteRequest in relation to FX Time<br>Options                                              |\n| 7.4     | 2014-06-17 | Timeout instructions                                                                                                             |\n| 7.5     | 2014-09-08 | Add allocation USI/UTI in Execution Report                                                                                       |\n| 7.6     | 2014-09-21 | Update QuoteCancel messages                                                                                                      |\n| 7.7     | 2014-09-23 | Improved USI/UTI documentation                                                                                                   |\n|         |            | continued on next page                                                                                                           |\n\nChapter 12: Version Log\n\n| Version | Date       | Comments                                                                                                                                                                                                                                                                                                                                |\n|---------|------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 7.8     | 2014-09-23 | Updated the sequence diagram                                                                                                                                                                                                                                                                                                            |\n| 7.9     | 2015-03-04 | Update definition for 'SIDE'                                                                                                                                                                                                                                                                                                            |\n| 7.10    | 2015-04-06 | Corrected QuoteType definition in Quote                                                                                                                                                                                                                                                                                                 |\n| 7.11    | 2015-04-06 | Corrected field order in header and several example messages                                                                                                                                                                                                                                                                            |\n| 7.12    | 2015-06-26 | Corrected NewOrderMultileg MsgType to 35=AB                                                                                                                                                                                                                                                                                             |\n| 7.13    | 2016-02-01 | Updated the ExpireTime specification                                                                                                                                                                                                                                                                                                    |\n| 7.14    | 2016-03-01 | Fix quote fields                                                                                                                                                                                                                                                                                                                        |\n| 7.15    | 2017-03-24 | Added financial calender query messages (SecurityDefinitionRequest and<br>SecurityDefinition)                                                                                                                                                                                                                                           |\n| 8.0     | 2017-09-19 | MIFID draft                                                                                                                                                                                                                                                                                                                             |\n| 8.1     | 2018-01-19 | MIFID final                                                                                                                                                                                                                                                                                                                             |\n| 9.0     | 2018-02-21 | Added support for block trades.                                                                                                                                                                                                                                                                                                         |\n| 9.1     | 2018-03-09 | Corrections on the order of fields for block trades.                                                                                                                                                                                                                                                                                    |\n| 9.2     | 2018-12-12 | Clarification on quote id usage in orders and quote id length in quote messages.                                                                                                                                                                                                                                                        |\n| 10.0    | 2018-12-19 | Added<br>Clearing<br>Firm<br>to<br>QuoteRequest<br>and<br>ExecutionReport.<br>Updated<br>ISIN<br>and<br>regulatory<br>trade<br>id<br>structure<br>for<br>Swap<br>and<br>NDS<br>requests<br>in<br>ExecutionReport. ExecType<150> in ExecutionReport will be 'F' (Trade) for<br>executed trades. Updated structure of QuoteRequestReject. |\n| 11.0    | 2019-10-21 | Updated how ISIN and TVTICs are sent for trades under MTF.                                                                                                                                                                                                                                                                              |\n| 11.1    | 2019-12-06 | Updated <3> Reject message to include tags <371> RefTagID and <373> Ses<br>sionRejectReason.                                                                                                                                                                                                                                            |\n| 11.2    | 2020-03-19 | Updated SettlDate2 description.                                                                                                                                                                                                                                                                                                         |\n| 11.3    | 2020-06-11 | Added Trading Hours section that details the enforced MTF closing hours.                                                                                                                                                                                                                                                                |\n| 12.0    | 2020-09-18 | Added NoCustomFields <7546> group to QuoteRequest and ExecutionReport.                                                                                                                                                                                                                                                                  |\n| 12.1    | 2021-05-04 | Added missing ProductType<7071> field to Quote <s> message. Added miss<br/>ing RefMsgType&lt;372&gt; field to Reject&lt;3&gt; message.</s>                                                                                                                                                                                              |\n| 12.2    | 2023-02-16 | Added required ProductType<7071> field to the NewOrderMultileg <ab><br/>message (field was already present but undocumented).</ab>                                                                                                                                                                                                      |\n| 12.3    | 2023-02-21 | Added<br>LegMaturityDate<611><br>to<br>QuoteRequest <r><br/>and<br/>NewOrderMultileg<ab> messages for uploading NDF Block trades.</ab></r>                                                                                                                                                                                              |\n| 12.4    | 2023-02-22 | Added Side<54> field value 'B' = As Defined for block trades.                                                                                                                                                                                                                                                                           |\n| 12.5    | 2023-02-21 | Added example messages for FX Block trades and NDF Block trades.                                                                                                                                                                                                                                                                        |\n| 12.6    | 2023-03-10 | Removing support for using NewOrderMultileg <ab> message to execute FX<br/>Swap quotes.</ab>                                                                                                                                                                                                                                            |\n| 12.7    | 2023-06-14 | Adding support for Base Metals.                                                                                                                                                                                                                                                                                                         |\n| 12.8    | 2023-10-30 | Adding support for Unique Product Identifier (UPI) and Reporting Tracking<br>Number (RTN) to the ExecutionReport<8> message.                                                                                                                                                                                                            |\n| 12.9    | 2024-02-15 | Adding support for Loan/Deposit.                                                                                                                                                                                                                                                                                                        |\n|         |            | continued on next page                                                                                                                                                                                                                                                                                                                  |\n\n![](_page_88_Picture_0.jpeg)\n\nChapter 12: Version Log\n\n| Version | Date       | Comments                                                                                                                                                                                                       |\n|---------|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n| 12.10   | 2024-05-13 | Added support for the Singapore Regulated Market Operator (RMO) venue to<br>the ExecutionVenueType<7611> field.                                                                                                |\n| 12.12   | 2024-06-04 | Adding FixingReference<7075> to QuoteRequest <r>, NewOrderSingle<d>,<br/>NewOrderMultileg<ab> and ExecutionReport&lt;8&gt; messages.</ab></d></r>                                                              |\n| 12.11   | 2024-06-10 | Removing<br>obsolete<br>RegulatoryTradeIDs<1907><br>group<br>from<br>QuoteRequest <r> message.</r>                                                                                                             |\n| 12.13   | 2024-06-27 | Reordering fields in QuoteRequest <r>,<br/>Quote<s>,<br/>NewOrderSingle<d>,<br/>NewOrderMultileg<ab> and ExecutionReport&lt;8&gt; messages according to<br/>their position in FIX dictionary.</ab></d></s></r> |\n| 12.14   | 2024-10-15 | Adding<br>support<br>for<br>Split<br>Settlement<br>Date<br>to<br>NewOrderSingle <d><br/>and<br/>ExecutionReport&lt;8&gt; messages.</d>                                                                         |", "metadata": {"lang": "en"}}]