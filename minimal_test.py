#!/usr/bin/env python3
print("Starting minimal test...")

from pathlib import Path
base_dir = Path("import/pdf_dataset")
print(f"Base directory exists: {base_dir.exists()}")

# List files
vector_dir = base_dir / "vector_index"
print(f"Vector index dir exists: {vector_dir.exists()}")

if not vector_dir.exists():
    vector_dir.mkdir(exist_ok=True)
    print("Created vector_index directory")

print("Minimal test completed successfully")