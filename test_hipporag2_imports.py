#!/usr/bin/env python3
"""
Test HippoRAG2 Pipeline Imports
Quick test to verify all imports and basic initialization work correctly.
"""

def test_imports():
    """Test that all required imports work correctly."""
    
    print("🧪 Testing HippoRAG2 Pipeline Imports...")
    print("=" * 50)
    
    try:
        print("📦 Testing basic imports...")
        import os
        import time
        from datetime import datetime
        print("✅ Standard library imports successful")
        
        print("📦 Testing setup modules...")
        from setup_llm_generator_direct import setup_gemini_llm_generator_direct
        from setup_embedding_model import setup_qwen_embedding_model
        from setup_processing_config import create_processing_config, create_extraction_pipeline, create_output_directories
        print("✅ Setup modules imported successfully")
        
        print("📦 Testing Atlas-RAG imports...")
        from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
        from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
        from atlas_rag.retriever.inference_config import InferenceConfig
        print("✅ Atlas-RAG modules imported successfully")
        
        print("📦 Testing pipeline functions...")
        from pdf_kg_extraction_pipeline import (
            run_knowledge_extraction_pipeline,
            setup_hipporag2_retriever, 
            hipporag2_interrogation,
            run_complete_hipporag2_pipeline
        )
        print("✅ Pipeline functions imported successfully")
        
        print("\n🎉 All imports successful!")
        print("✅ Enhanced PDF pipeline is ready to use")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_config_availability():
    """Test that required configuration files exist."""
    
    import os
    
    print("\n🔧 Testing Configuration Availability...")
    print("-" * 40)
    
    # Check config.ini
    if os.path.exists('config.ini'):
        print("✅ config.ini found")
    else:
        print("❌ config.ini missing")
        return False
    
    # Check example_data directory
    if os.path.exists('example_data'):
        data_files = [f for f in os.listdir('example_data') if f.endswith(('.json', '.txt'))]
        print(f"✅ example_data directory found with {len(data_files)} files")
    else:
        print("⚠️  example_data directory not found (will be needed for processing)")
    
    # Check import directory exists or can be created
    os.makedirs('import', exist_ok=True)
    print("✅ import directory ready")
    
    return True

def show_usage_examples():
    """Show usage examples for the enhanced pipeline."""
    
    print("\n💡 Usage Examples:")
    print("-" * 40)
    print("1. Run complete HippoRAG2 pipeline:")
    print("   python pdf_kg_extraction_pipeline.py")
    print("   # Choose option 2 when prompted")
    print()
    print("2. Run just knowledge extraction:")
    print("   python pdf_kg_extraction_pipeline.py") 
    print("   # Choose option 1 when prompted")
    print()
    print("3. Run HippoRAG2 Q&A only (if extraction already done):")
    print("   python pdf_kg_extraction_pipeline.py")
    print("   # Choose option 3 when prompted")
    print()
    print("4. Programmatic usage:")
    print("   from pdf_kg_extraction_pipeline import run_complete_hipporag2_pipeline")
    print("   retriever = run_complete_hipporag2_pipeline('my_dataset', run_qa=False)")
    print("   # Then use retriever for custom Q&A")

if __name__ == "__main__":
    print("🎯 HippoRAG2 Pipeline Import Test")
    print("Testing enhanced PDF pipeline functionality")
    print()
    
    # Test imports
    imports_ok = test_imports()
    
    # Test configuration
    if imports_ok:
        config_ok = test_config_availability()
        
        if config_ok:
            show_usage_examples()
            print("\n✅ All tests passed! Pipeline is ready to use.")
        else:
            print("\n⚠️  Configuration issues detected. Please address before running.")
    else:
        print("\n❌ Import issues detected. Please check dependencies.")