#!/usr/bin/env python3
"""
Manual script to create vector_index files by converting CSV embeddings to NPY format
and creating FAISS indexes.
"""

import pandas as pd
import numpy as np
from ast import literal_eval
import os
from pathlib import Path
import faiss

def parse_embedding(embed_str):
    """Convert embedding string to numpy array"""
    return np.array(literal_eval(embed_str), dtype=np.float32)

def convert_csv_to_npy_manual(csv_path, npy_path, embedding_col="embedding:STRING", chunksize=100000):
    """Convert CSV embeddings to NPY format."""
    print(f"Converting {csv_path} to {npy_path}")
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(npy_path), exist_ok=True)
    
    # Initialize empty file
    with open(npy_path, "wb") as f:
        pass
    
    total_embeddings = 0
    
    # Process CSV in chunks to avoid memory issues
    for chunk_idx, df_chunk in enumerate(pd.read_csv(csv_path, chunksize=chunksize, usecols=[embedding_col])):
        # Parse embeddings
        embeddings = np.stack(df_chunk[embedding_col].apply(parse_embedding).values)
        
        total_embeddings += embeddings.shape[0]
        
        # Append to .npy file
        with open(npy_path, "ab") as f:
            np.save(f, embeddings.astype(np.float32))
        
        print(f"  Processed chunk {chunk_idx + 1} ({embeddings.shape[0]} embeddings)")
    
    print(f"  ✅ Converted {total_embeddings} embeddings")
    return total_embeddings

def build_faiss_from_npy_manual(npy_path, index_path, index_type="HNSW,Flat"):
    """Build FAISS index from NPY file."""
    print(f"Building FAISS index: {index_path}")
    
    # Load embeddings from NPY file
    embeddings_list = []
    with open(npy_path, "rb") as f:
        try:
            while True:
                embeddings = np.load(f)
                embeddings_list.append(embeddings)
        except:
            pass  # End of file
    
    # Combine all embeddings
    all_embeddings = np.vstack(embeddings_list)
    print(f"  Loaded {all_embeddings.shape[0]} embeddings with dimension {all_embeddings.shape[1]}")
    
    # Create FAISS index
    dimension = all_embeddings.shape[1]
    index = faiss.index_factory(dimension, index_type, faiss.METRIC_INNER_PRODUCT)
    
    # Add embeddings to index
    if index_type.startswith("IVF"):
        # For IVF indexes, train first
        index.train(all_embeddings)
    
    index.add(all_embeddings)
    
    # Save index
    faiss.write_index(index, index_path)
    print(f"  ✅ Created FAISS index with {index.ntotal} vectors")

def main():
    """Main function to create vector_index files."""
    
    base_dir = Path("import/pdf_dataset")
    vector_index_dir = base_dir / "vector_index"
    triples_csv_dir = base_dir / "triples_csv"
    concept_csv_dir = base_dir / "concept_csv"
    
    print("🚀 Manually creating vector_index files for pdf_dataset")
    print("=" * 70)
    
    # Ensure vector_index directory exists
    vector_index_dir.mkdir(exist_ok=True)
    
    # Define conversions with empty filename pattern (creating double underscores)
    filename_pattern = ""
    
    conversions = [
        {
            'name': 'Triple Nodes',
            'csv': triples_csv_dir / f"triple_nodes_{filename_pattern}_from_json_with_emb.csv",
            'npy': vector_index_dir / f"triple_nodes_{filename_pattern}_from_json_with_emb.npy",
            'index': vector_index_dir / f"triple_nodes_{filename_pattern}_from_json_with_emb_non_norm.index"
        },
        {
            'name': 'Text Nodes', 
            'csv': triples_csv_dir / f"text_nodes_{filename_pattern}_from_json_with_emb.csv",
            'npy': vector_index_dir / f"text_nodes_{filename_pattern}_from_json_with_emb.npy",
            'index': vector_index_dir / f"text_nodes_{filename_pattern}_from_json_with_emb_non_norm.index"
        },
        {
            'name': 'Triple Edges',
            'csv': triples_csv_dir / f"triple_edges_{filename_pattern}_from_json_with_concept_with_emb.csv", 
            'npy': vector_index_dir / f"triple_edges_{filename_pattern}_from_json_with_concept_with_emb.npy",
            'index': vector_index_dir / f"triple_edges_{filename_pattern}_from_json_with_concept_with_emb_non_norm.index"
        }
    ]
    
    # Process each conversion
    for i, conversion in enumerate(conversions, 1):
        print(f"\n🔧 Task {i}/3: {conversion['name']}")
        
        # Check if CSV file exists
        if not conversion['csv'].exists():
            print(f"  ❌ CSV file not found: {conversion['csv']}")
            continue
        
        # Check if outputs already exist
        if conversion['npy'].exists() and conversion['index'].exists():
            print(f"  ✅ Files already exist, skipping")
            continue
        
        try:
            # Convert CSV to NPY
            if not conversion['npy'].exists():
                convert_csv_to_npy_manual(conversion['csv'], conversion['npy'])
            
            # Create FAISS index
            if not conversion['index'].exists():
                build_faiss_from_npy_manual(conversion['npy'], conversion['index'])
            
            print(f"  ✅ Successfully processed {conversion['name']}")
            
        except Exception as e:
            print(f"  ❌ Error processing {conversion['name']}: {str(e)}")
            continue
    
    # Verify all files were created
    print(f"\n📁 Verification - Files in {vector_index_dir}:")
    if vector_index_dir.exists():
        for file in sorted(vector_index_dir.iterdir()):
            size = file.stat().st_size
            print(f"  ✅ {file.name} ({size:,} bytes)")
    
    print(f"\n✅ Vector index creation completed!")

if __name__ == "__main__":
    try:
        main()
        print("\n🎉 Successfully created vector_index files!")
    except Exception as e:
        print(f"\n❌ Failed to create vector_index files: {str(e)}")
        import traceback
        traceback.print_exc()