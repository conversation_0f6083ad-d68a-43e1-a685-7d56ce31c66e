"""
Error<PERSON><PERSON>ler - Centralized error management and recovery for knowledge graph extraction pipeline.

Provides retry logic, circuit breaker patterns, error categorization, and recovery strategies
for robust pipeline operation in the face of transient and persistent failures.
"""

import time
import logging
import traceback
import json
import smtplib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import threading


class ErrorCategory(Enum):
    """Categories of errors that can occur during pipeline processing."""
    API_RATE_LIMIT = "api_rate_limit"
    API_TIMEOUT = "api_timeout"
    API_ERROR = "api_error"
    NETWORK_ERROR = "network_error"
    FILE_IO_ERROR = "file_io_error"
    PARSING_ERROR = "parsing_error"
    MEMORY_ERROR = "memory_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY_WITH_BACKOFF = "retry_with_backoff"
    RETRY_IMMEDIATE = "retry_immediate"
    SKIP_AND_CONTINUE = "skip_and_continue"
    FAIL_FAST = "fail_fast"
    CIRCUIT_BREAKER = "circuit_breaker"
    REDUCE_LOAD = "reduce_load"


@dataclass
class ErrorConfig:
    """Configuration for error handling behavior."""
    max_retries: int = 3
    base_delay: float = 1.0  # Base delay in seconds
    max_delay: float = 300.0  # Maximum delay in seconds
    backoff_multiplier: float = 2.0
    jitter: bool = True  # Add random jitter to delays
    circuit_breaker_threshold: int = 5  # Failures before circuit opens
    circuit_breaker_timeout: float = 300.0  # Seconds before trying to close circuit
    enable_notifications: bool = False
    notification_email: str = ""
    smtp_server: str = ""
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""


@dataclass
class ErrorRecord:
    """Record of an error occurrence."""
    timestamp: float
    error_category: ErrorCategory
    error_message: str
    context: Dict[str, Any]
    stack_trace: str
    retry_count: int = 0
    recovery_strategy: Optional[RecoveryStrategy] = None
    resolved: bool = False
    resolution_time: Optional[float] = None


@dataclass
class CircuitBreakerState:
    """State of a circuit breaker for a specific operation."""
    name: str
    state: str = "closed"  # "closed", "open", "half_open"
    failure_count: int = 0
    last_failure_time: float = 0
    last_success_time: float = 0
    opened_at: Optional[float] = None


class ErrorHandler:
    """Centralized error handling and recovery system."""
    
    def __init__(self, config: ErrorConfig = None, logger: logging.Logger = None):
        """
        Initialize the error handler.
        
        Args:
            config: Error handling configuration
            logger: Logger instance for error reporting
        """
        self.config = config or ErrorConfig()
        self.logger = logger or self._setup_default_logger()
        
        # Error tracking
        self.error_records: List[ErrorRecord] = []
        self.error_counts: Dict[ErrorCategory, int] = {cat: 0 for cat in ErrorCategory}
        self.circuit_breakers: Dict[str, CircuitBreakerState] = {}
        
        # Recovery strategies mapping
        self.recovery_strategies = {
            ErrorCategory.API_RATE_LIMIT: RecoveryStrategy.RETRY_WITH_BACKOFF,
            ErrorCategory.API_TIMEOUT: RecoveryStrategy.RETRY_WITH_BACKOFF,
            ErrorCategory.API_ERROR: RecoveryStrategy.RETRY_WITH_BACKOFF,
            ErrorCategory.NETWORK_ERROR: RecoveryStrategy.RETRY_WITH_BACKOFF,
            ErrorCategory.FILE_IO_ERROR: RecoveryStrategy.RETRY_IMMEDIATE,
            ErrorCategory.PARSING_ERROR: RecoveryStrategy.SKIP_AND_CONTINUE,
            ErrorCategory.MEMORY_ERROR: RecoveryStrategy.REDUCE_LOAD,
            ErrorCategory.VALIDATION_ERROR: RecoveryStrategy.SKIP_AND_CONTINUE,
            ErrorCategory.UNKNOWN_ERROR: RecoveryStrategy.RETRY_WITH_BACKOFF
        }
        
        # Thread safety
        self._lock = threading.Lock()
        
        self.logger.info("🛡️ ErrorHandler initialized with robust recovery strategies")
    
    def _setup_default_logger(self) -> logging.Logger:
        """Setup default logger if none provided."""
        logger = logging.getLogger("error_handler")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-8s | ErrorHandler | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def categorize_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorCategory:
        """
        Categorize an error based on its type and context.
        
        Args:
            exception: The exception that occurred
            context: Additional context about the error
            
        Returns:
            ErrorCategory enum value
        """
        error_message = str(exception).lower()
        exception_type = type(exception).__name__.lower()
        
        # API rate limiting
        if ("rate limit" in error_message or "quota exceeded" in error_message or 
            "too many requests" in error_message or "429" in error_message):
            return ErrorCategory.API_RATE_LIMIT
        
        # API timeouts
        if ("timeout" in error_message or "timed out" in error_message or
            exception_type in ["timeout", "timeouterror", "connectiontimeout"]):
            return ErrorCategory.API_TIMEOUT
        
        # API errors
        if ("api" in error_message or "http" in error_message or
            exception_type in ["httperror", "requestexception", "connectionerror"]):
            return ErrorCategory.API_ERROR
        
        # Network errors
        if ("connection" in error_message or "network" in error_message or
            "dns" in error_message or exception_type in ["connectionerror", "networkerror"]):
            return ErrorCategory.NETWORK_ERROR
        
        # File I/O errors
        if ("file" in error_message or "directory" in error_message or
            exception_type in ["filenotfounderror", "ioerror", "oserror", "permissionerror"]):
            return ErrorCategory.FILE_IO_ERROR
        
        # Parsing errors
        if ("json" in error_message or "parse" in error_message or "decode" in error_message or
            exception_type in ["jsondecodeerror", "valueerror", "keyerror"]):
            return ErrorCategory.PARSING_ERROR
        
        # Memory errors
        if ("memory" in error_message or exception_type in ["memoryerror", "outofmemoryerror"]):
            return ErrorCategory.MEMORY_ERROR
        
        # Validation errors
        if ("validation" in error_message or "invalid" in error_message or
            exception_type in ["validationerror", "assertionerror"]):
            return ErrorCategory.VALIDATION_ERROR
        
        return ErrorCategory.UNKNOWN_ERROR
    
    def record_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorRecord:
        """
        Record an error occurrence.
        
        Args:
            exception: The exception that occurred
            context: Additional context about the error
            
        Returns:
            ErrorRecord instance
        """
        with self._lock:
            category = self.categorize_error(exception, context)
            
            error_record = ErrorRecord(
                timestamp=time.time(),
                error_category=category,
                error_message=str(exception),
                context=context or {},
                stack_trace=traceback.format_exc(),
                recovery_strategy=self.recovery_strategies.get(category)
            )
            
            self.error_records.append(error_record)
            self.error_counts[category] += 1
            
            # Log the error
            self.logger.error(f"🚨 Error recorded: {category.value}")
            self.logger.error(f"   Message: {error_record.error_message}")
            if context:
                self.logger.error(f"   Context: {context}")
            
            return error_record
    
    def should_retry(self, error_record: ErrorRecord) -> bool:
        """
        Determine if an operation should be retried based on error record.
        
        Args:
            error_record: The error record to evaluate
            
        Returns:
            True if should retry, False otherwise
        """
        if error_record.retry_count >= self.config.max_retries:
            return False
        
        strategy = error_record.recovery_strategy
        
        if strategy == RecoveryStrategy.FAIL_FAST:
            return False
        elif strategy == RecoveryStrategy.SKIP_AND_CONTINUE:
            return False
        elif strategy in [RecoveryStrategy.RETRY_WITH_BACKOFF, RecoveryStrategy.RETRY_IMMEDIATE]:
            return True
        elif strategy == RecoveryStrategy.CIRCUIT_BREAKER:
            circuit_name = error_record.context.get('operation', 'default')
            return not self._is_circuit_open(circuit_name)
        
        return True
    
    def calculate_retry_delay(self, error_record: ErrorRecord) -> float:
        """
        Calculate the delay before retrying an operation.
        
        Args:
            error_record: The error record to calculate delay for
            
        Returns:
            Delay in seconds
        """
        strategy = error_record.recovery_strategy
        
        if strategy == RecoveryStrategy.RETRY_IMMEDIATE:
            return 0.0
        
        # Exponential backoff with jitter
        delay = min(
            self.config.base_delay * (self.config.backoff_multiplier ** error_record.retry_count),
            self.config.max_delay
        )
        
        # Add jitter to prevent thundering herd
        if self.config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        # Special handling for rate limits
        if error_record.error_category == ErrorCategory.API_RATE_LIMIT:
            # Longer delays for rate limits
            delay = max(delay, 60.0)  # Minimum 1 minute for rate limits
        
        return delay
    
    def execute_with_retry(self, operation: Callable, context: Dict[str, Any] = None, 
                          operation_name: str = "unknown") -> Any:
        """
        Execute an operation with automatic retry logic.
        
        Args:
            operation: The operation to execute (callable)
            context: Additional context for error handling
            operation_name: Name of the operation for tracking
            
        Returns:
            Result of the operation
            
        Raises:
            Exception: If all retries are exhausted
        """
        context = context or {}
        context['operation'] = operation_name
        
        last_error_record = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # Check circuit breaker
                if self._is_circuit_open(operation_name):
                    raise Exception(f"Circuit breaker is open for operation: {operation_name}")
                
                # Execute the operation
                result = operation()
                
                # Mark success
                self._record_success(operation_name)
                
                # Mark previous errors as resolved
                if last_error_record:
                    last_error_record.resolved = True
                    last_error_record.resolution_time = time.time()
                
                return result
                
            except Exception as e:
                # Record the error
                error_record = self.record_error(e, context)
                error_record.retry_count = attempt
                last_error_record = error_record
                
                # Update circuit breaker
                self._record_failure(operation_name)
                
                # Check if we should retry
                if attempt < self.config.max_retries and self.should_retry(error_record):
                    delay = self.calculate_retry_delay(error_record)
                    
                    self.logger.warning(f"🔄 Retrying {operation_name} in {delay:.1f}s (attempt {attempt + 1}/{self.config.max_retries})")
                    
                    if delay > 0:
                        time.sleep(delay)
                    
                    continue
                else:
                    # All retries exhausted or should not retry
                    self.logger.error(f"💥 All retries exhausted for {operation_name}")
                    
                    # Send notification if enabled
                    if self.config.enable_notifications:
                        self._send_error_notification(error_record, operation_name)
                    
                    raise e
    
    def _is_circuit_open(self, circuit_name: str) -> bool:
        """Check if a circuit breaker is open."""
        if circuit_name not in self.circuit_breakers:
            return False
        
        circuit = self.circuit_breakers[circuit_name]
        
        if circuit.state == "closed":
            return False
        elif circuit.state == "open":
            # Check if we should try to close the circuit
            if time.time() - circuit.opened_at > self.config.circuit_breaker_timeout:
                circuit.state = "half_open"
                self.logger.info(f"🔄 Circuit breaker for {circuit_name} moved to half-open")
                return False
            return True
        elif circuit.state == "half_open":
            return False
        
        return False
    
    def _record_success(self, operation_name: str):
        """Record a successful operation."""
        if operation_name in self.circuit_breakers:
            circuit = self.circuit_breakers[operation_name]
            circuit.failure_count = 0
            circuit.last_success_time = time.time()
            
            if circuit.state == "half_open":
                circuit.state = "closed"
                self.logger.info(f"✅ Circuit breaker for {operation_name} closed after successful operation")
    
    def _record_failure(self, operation_name: str):
        """Record a failed operation."""
        if operation_name not in self.circuit_breakers:
            self.circuit_breakers[operation_name] = CircuitBreakerState(name=operation_name)
        
        circuit = self.circuit_breakers[operation_name]
        circuit.failure_count += 1
        circuit.last_failure_time = time.time()
        
        # Open circuit if threshold exceeded
        if (circuit.failure_count >= self.config.circuit_breaker_threshold and 
            circuit.state != "open"):
            circuit.state = "open"
            circuit.opened_at = time.time()
            self.logger.warning(f"⚠️ Circuit breaker opened for {operation_name} after {circuit.failure_count} failures")
    
    def _send_error_notification(self, error_record: ErrorRecord, operation_name: str):
        """Send email notification for critical errors."""
        if not self.config.notification_email or not self.config.smtp_server:
            return
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.config.smtp_username
            msg['To'] = self.config.notification_email
            msg['Subject'] = f"Pipeline Error Alert: {operation_name}"
            
            # Create email body
            body = f"""
            Pipeline Error Alert
            
            Operation: {operation_name}
            Error Category: {error_record.error_category.value}
            Error Message: {error_record.error_message}
            Timestamp: {datetime.fromtimestamp(error_record.timestamp)}
            Retry Count: {error_record.retry_count}
            
            Context:
            {json.dumps(error_record.context, indent=2)}
            
            Stack Trace:
            {error_record.stack_trace}
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(self.config.smtp_server, self.config.smtp_port)
            server.starttls()
            server.login(self.config.smtp_username, self.config.smtp_password)
            text = msg.as_string()
            server.sendmail(self.config.smtp_username, self.config.notification_email, text)
            server.quit()
            
            self.logger.info(f"📧 Error notification sent for {operation_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to send error notification: {e}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get comprehensive error summary."""
        with self._lock:
            total_errors = len(self.error_records)
            resolved_errors = len([r for r in self.error_records if r.resolved])
            
            # Recent errors (last hour)
            recent_cutoff = time.time() - 3600
            recent_errors = [r for r in self.error_records if r.timestamp > recent_cutoff]
            
            # Most common errors
            category_counts = {cat.value: count for cat, count in self.error_counts.items() if count > 0}
            
            # Circuit breaker status
            circuit_status = {}
            for name, circuit in self.circuit_breakers.items():
                circuit_status[name] = {
                    'state': circuit.state,
                    'failure_count': circuit.failure_count,
                    'last_failure': datetime.fromtimestamp(circuit.last_failure_time) if circuit.last_failure_time else None
                }
            
            return {
                'total_errors': total_errors,
                'resolved_errors': resolved_errors,
                'unresolved_errors': total_errors - resolved_errors,
                'recent_errors_count': len(recent_errors),
                'error_categories': category_counts,
                'circuit_breakers': circuit_status,
                'recovery_success_rate': (resolved_errors / total_errors * 100) if total_errors > 0 else 100
            }
    
    def reset_circuit_breaker(self, operation_name: str):
        """Manually reset a circuit breaker."""
        if operation_name in self.circuit_breakers:
            circuit = self.circuit_breakers[operation_name]
            circuit.state = "closed"
            circuit.failure_count = 0
            circuit.opened_at = None
            self.logger.info(f"🔄 Circuit breaker for {operation_name} manually reset")
    
    def clear_error_history(self):
        """Clear error history (useful for testing or fresh starts)."""
        with self._lock:
            self.error_records.clear()
            self.error_counts = {cat: 0 for cat in ErrorCategory}
            self.circuit_breakers.clear()
            self.logger.info("🧹 Error history cleared")
    
    def create_retry_decorator(self, operation_name: str = None, max_retries: int = None):
        """
        Create a decorator for automatic retry handling.
        
        Args:
            operation_name: Name of the operation (defaults to function name)
            max_retries: Override default max retries
            
        Returns:
            Decorator function
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                name = operation_name or func.__name__
                
                # Temporarily override max_retries if specified
                original_max_retries = self.config.max_retries
                if max_retries is not None:
                    self.config.max_retries = max_retries
                
                try:
                    return self.execute_with_retry(
                        lambda: func(*args, **kwargs),
                        context={'function': func.__name__},
                        operation_name=name
                    )
                finally:
                    # Restore original max_retries
                    self.config.max_retries = original_max_retries
            
            return wrapper
        return decorator