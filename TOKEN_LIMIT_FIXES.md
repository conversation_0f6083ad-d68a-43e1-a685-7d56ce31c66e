# Token Limit Fixes for AutoSchemaKG

## Problem: `finish_reason='length'` Errors

If you're experiencing errors like:
```
Warning: API returned None content for model gemini-2.5-flash  
finish_reason='length' with completion_tokens=0
```

This indicates your prompts are too large for your API tier's token limits.

## Root Cause Analysis

The issue occurs when:
- **Input prompts** consume too many tokens (4000+ tokens observed)
- **Tier 1 limitations** have stricter context windows
- **Large max_new_tokens** settings leave no room for actual output
- **Batch processing** creates very large combined prompts

## Solutions Implemented

### 1. Updated Default Configuration
- `max_new_tokens`: 8192 → **1500** (more conservative)
- `batch_size_triple`: 16 → **2** (smaller batches)
- Enhanced error messages with token usage details

### 2. Tier 1 Optimized Configuration
Use `--tier1` flag for ultra-conservative settings:
- `max_new_tokens`: **1000** (token limit safe)
- `batch_size_triple`: **1** (smallest possible batches)
- `max_workers`: **1** (avoid rate limits)
- `batch_size_concept`: **8** (reduced load)

### 3. Enhanced Error Handling
The system now provides detailed feedback:
```
🚨 TOKEN LIMIT HIT: Input tokens (4095) + max_new_tokens (1500) likely exceeded context window
💡 Solution: Reduce batch size, input text length, or max_new_tokens parameter
```

## Usage

### For Tier 1 Users (Recommended)
```bash
python atlas_graphml_pipeline.py --tier1
```

### For Standard Use
```bash
python atlas_graphml_pipeline.py
# Now uses the updated, more conservative defaults
```

### Test Your Configuration
```bash
python test_token_fixes.py
```

## Configuration Comparison

| Parameter | Standard | Tier 1 | Impact |
|-----------|----------|--------|---------|
| max_new_tokens | 4096 | 1000 | Token limit safe |
| batch_size_triple | 4 | 1 | Smaller prompts |
| max_workers | 2 | 1 | No rate limits |
| batch_size_concept | 16 | 8 | Reduced load |

## Further Troubleshooting

If you still experience token limit issues:

1. **Reduce max_new_tokens further**: Try 800 or 600
2. **Check document size**: Very large documents may need pre-splitting
3. **Enable debug mode**: Add `debug_mode=True` for detailed analysis
4. **Consider API tier upgrade**: Higher tiers have better token limits

## Programmatic Usage

```python
from setup_processing_config import create_processing_config_tier1

# Create Tier 1 optimized configuration
config = create_processing_config_tier1(
    dataset_name="my_dataset",
    filename_pattern="my_files"
)

# The config will have token-safe settings automatically
```

## Monitoring

Watch for these log messages:
- ✅ **Normal**: API responses with actual content
- ⚠️ **Warning**: "TOKEN LIMIT HIT" messages
- ❌ **Error**: Repeated `finish_reason='length'` with `completion_tokens=0`

## Performance Impact

**Tier 1 Mode Trade-offs:**
- ➕ **Reliability**: Eliminates token limit errors
- ➕ **Cost per call**: Lower tokens per request
- ➖ **Speed**: More API calls needed (slower overall)
- ➖ **Total cost**: More API calls (potentially higher total cost)

The system now prioritizes **reliability over speed** for API-limited users.