"""
PipelineStateManager - Manages checkpoint creation/loading and progress tracking for resumable KG extraction pipeline.

This module provides robust state management for long-running knowledge graph extraction pipelines,
enabling resume functionality and comprehensive progress tracking.
"""

import json
import os
import glob
import time
from datetime import datetime
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class ProcessingStage:
    """Represents a processing stage in the pipeline."""
    name: str
    status: str  # 'pending', 'in_progress', 'completed', 'failed'
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    files_completed: int = 0
    files_total: int = 0
    last_processed_file: str = ""
    error_message: str = ""
    
    @property
    def duration(self) -> float:
        """Calculate stage duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return time.time() - self.start_time
        return 0.0
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage for this stage."""
        if self.files_total == 0:
            return 0.0
        return (self.files_completed / self.files_total) * 100


@dataclass
class FileStatus:
    """Represents the processing status of an individual file."""
    filename: str
    status: str  # 'pending', 'processing', 'completed', 'failed'
    stages_completed: List[str]
    last_processed_stage: str = ""
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: str = ""
    document_count: int = 0
    last_document_id: str = ""
    
    @property
    def duration(self) -> float:
        """Calculate file processing duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return time.time() - self.start_time
        return 0.0


@dataclass
class PipelineState:
    """Complete state of the pipeline processing."""
    dataset_name: str
    pipeline_version: str
    created_at: str
    updated_at: str
    overall_status: str  # 'not_started', 'in_progress', 'completed', 'failed'
    total_files: int
    completed_files: int
    failed_files: int
    stages: Dict[str, ProcessingStage]
    files: Dict[str, FileStatus]
    config: Dict[str, Any]
    resume_point: Dict[str, Any]  # Information needed to resume
    
    @property
    def overall_progress_percentage(self) -> float:
        """Calculate overall pipeline progress percentage."""
        if self.total_files == 0:
            return 0.0
        return (self.completed_files / self.total_files) * 100


class PipelineStateManager:
    """Manages pipeline state for resumable knowledge graph extraction."""
    
    def __init__(self, dataset_name: str, state_file: str = None):
        """
        Initialize the pipeline state manager.
        
        Args:
            dataset_name: Name of the dataset being processed
            state_file: Path to the state file (defaults to pipeline_state_{dataset_name}.json)
        """
        self.dataset_name = dataset_name
        self.state_file = state_file or f"pipeline_state_{dataset_name}.json"
        self.state: Optional[PipelineState] = None
        self.backup_interval = 300  # Backup every 5 minutes
        self.last_backup_time = 0
        
        # Define standard pipeline stages
        self.STANDARD_STAGES = [
            "initialization", "triple_extraction", "csv_conversion", 
            "concept_generation", "concept_csv_creation", "numeric_id_addition",
            "graphml_conversion", "embedding_generation", "faiss_index_creation"
        ]
    
    def create_new_state(self, input_files: List[str], config: Dict[str, Any] = None) -> PipelineState:
        """
        Create a new pipeline state.
        
        Args:
            input_files: List of input files to process
            config: Pipeline configuration dictionary
            
        Returns:
            New PipelineState instance
        """
        now = datetime.now().isoformat()
        
        # Initialize stages
        stages = {}
        for stage_name in self.STANDARD_STAGES:
            stages[stage_name] = ProcessingStage(
                name=stage_name,
                status='pending',
                files_total=len(input_files)
            )
        
        # Initialize file statuses
        files = {}
        for filename in input_files:
            files[filename] = FileStatus(
                filename=filename,
                status='pending',
                stages_completed=[]
            )
        
        self.state = PipelineState(
            dataset_name=self.dataset_name,
            pipeline_version="2.0",
            created_at=now,
            updated_at=now,
            overall_status='not_started',
            total_files=len(input_files),
            completed_files=0,
            failed_files=0,
            stages=stages,
            files=files,
            config=config or {},
            resume_point={}
        )
        
        return self.state
    
    def load_state(self) -> Optional[PipelineState]:
        """
        Load pipeline state from file.
        
        Returns:
            PipelineState if file exists and is valid, None otherwise
        """
        if not os.path.exists(self.state_file):
            return None
        
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state_dict = json.load(f)
            
            # Convert dictionaries back to dataclass instances
            stages = {}
            for stage_name, stage_data in state_dict['stages'].items():
                stages[stage_name] = ProcessingStage(**stage_data)
            
            files = {}
            for filename, file_data in state_dict['files'].items():
                files[filename] = FileStatus(**file_data)
            
            state_dict['stages'] = stages
            state_dict['files'] = files
            
            self.state = PipelineState(**state_dict)
            return self.state
            
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"⚠️  Warning: Could not load state file {self.state_file}: {e}")
            return None
    
    def save_state(self, force: bool = False):
        """
        Save current pipeline state to file.
        
        Args:
            force: Force save even if backup interval hasn't passed
        """
        if not self.state:
            return
        
        current_time = time.time()
        if not force and (current_time - self.last_backup_time) < self.backup_interval:
            return
        
        try:
            # Update timestamp
            self.state.updated_at = datetime.now().isoformat()
            
            # Convert to dictionary
            state_dict = asdict(self.state)
            
            # Create backup of existing file
            if os.path.exists(self.state_file):
                backup_file = f"{self.state_file}.backup"
                os.rename(self.state_file, backup_file)
            
            # Save new state
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_dict, f, indent=2, ensure_ascii=False)
            
            self.last_backup_time = current_time
            
        except Exception as e:
            print(f"❌ Error saving state file: {e}")
    
    def detect_existing_progress(self, dataset_name: str) -> Dict[str, Any]:
        """
        Detect existing progress by scanning output directories.
        
        Args:
            dataset_name: Name of the dataset
            
        Returns:
            Dictionary with detected progress information
        """
        progress = {
            'extraction_files': [],
            'csv_files': [],
            'concept_files': [],
            'graphml_files': [],
            'embedding_files': [],
            'index_files': [],
            'processed_documents': set(),
            'last_extraction_file': None
        }
        
        base_dir = f"import/{dataset_name}"
        
        # Check for extraction files
        extraction_dir = os.path.join(base_dir, "kg_extraction")
        if os.path.exists(extraction_dir):
            extraction_files = glob.glob(os.path.join(extraction_dir, "*.json"))
            progress['extraction_files'] = sorted(extraction_files)
            
            # Find the most recent extraction file
            if extraction_files:
                progress['last_extraction_file'] = max(extraction_files, key=os.path.getmtime)
                
                # Count processed documents
                processed_docs = self._count_processed_documents(progress['last_extraction_file'])
                progress['processed_documents'] = processed_docs
        
        # Check for CSV files
        csv_dir = os.path.join(base_dir, "triples_csv")
        if os.path.exists(csv_dir):
            progress['csv_files'] = glob.glob(os.path.join(csv_dir, "*.csv"))
        
        # Check for concept files
        concept_dir = os.path.join(base_dir, "concept_csv")
        if os.path.exists(concept_dir):
            progress['concept_files'] = glob.glob(os.path.join(concept_dir, "*.csv"))
        
        # Check for GraphML files
        graphml_dir = os.path.join(base_dir, "kg_graphml")
        if os.path.exists(graphml_dir):
            progress['graphml_files'] = glob.glob(os.path.join(graphml_dir, "*.graphml"))
        
        # Check for embedding files
        if os.path.exists(base_dir):
            progress['embedding_files'] = glob.glob(os.path.join(base_dir, "*_embeddings.pkl"))
        
        # Check for index files
        index_dir = os.path.join(base_dir, "vector_index")
        if os.path.exists(index_dir):
            progress['index_files'] = glob.glob(os.path.join(index_dir, "*.index"))
        
        return progress
    
    def _count_processed_documents(self, json_file: str) -> Set[str]:
        """
        Count processed documents in a JSON extraction file.
        
        Args:
            json_file: Path to the JSON extraction file
            
        Returns:
            Set of processed document IDs
        """
        processed_docs = set()
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        doc = json.loads(line)
                        if 'id' in doc:
                            processed_docs.add(doc['id'])
        except Exception as e:
            print(f"⚠️  Warning: Could not count documents in {json_file}: {e}")
        
        return processed_docs
    
    def can_resume(self) -> bool:
        """
        Check if the pipeline can be resumed.
        
        Returns:
            True if resumable, False otherwise
        """
        if not self.state:
            return False
        
        return (self.state.overall_status in ['in_progress', 'failed'] and 
                self.state.completed_files < self.state.total_files)
    
    def get_resume_point(self) -> Dict[str, Any]:
        """
        Get information about where to resume processing.
        
        Returns:
            Dictionary with resume point information
        """
        if not self.state:
            return {}
        
        resume_info = {
            'completed_files': [f for f, status in self.state.files.items() 
                               if status.status == 'completed'],
            'failed_files': [f for f, status in self.state.files.items() 
                            if status.status == 'failed'],
            'pending_files': [f for f, status in self.state.files.items() 
                             if status.status == 'pending'],
            'last_completed_stage': None,
            'current_stage': None
        }
        
        # Determine which stage to resume from
        for stage_name, stage in self.state.stages.items():
            if stage.status == 'completed':
                resume_info['last_completed_stage'] = stage_name
            elif stage.status == 'in_progress':
                resume_info['current_stage'] = stage_name
                break
        
        return resume_info
    
    def start_stage(self, stage_name: str):
        """Mark a stage as started."""
        if self.state and stage_name in self.state.stages:
            stage = self.state.stages[stage_name]
            stage.status = 'in_progress' 
            stage.start_time = time.time()
            self.save_state()
    
    def complete_stage(self, stage_name: str):
        """Mark a stage as completed."""
        if self.state and stage_name in self.state.stages:
            stage = self.state.stages[stage_name]
            stage.status = 'completed'
            stage.end_time = time.time()
            stage.files_completed = stage.files_total
            self.save_state()
    
    def fail_stage(self, stage_name: str, error_message: str = ""):
        """Mark a stage as failed."""
        if self.state and stage_name in self.state.stages:
            stage = self.state.stages[stage_name]
            stage.status = 'failed'
            stage.end_time = time.time()
            stage.error_message = error_message
            self.save_state()
    
    def start_file_processing(self, filename: str, stage_name: str):
        """Mark a file as being processed in a specific stage."""
        if self.state and filename in self.state.files:
            file_status = self.state.files[filename]
            file_status.status = 'processing'
            file_status.last_processed_stage = stage_name
            if not file_status.start_time:
                file_status.start_time = time.time()
            self.save_state()
    
    def complete_file_processing(self, filename: str, stage_name: str, document_count: int = 0, last_doc_id: str = ""):
        """Mark a file as completed for a specific stage."""
        if self.state and filename in self.state.files:
            file_status = self.state.files[filename]
            
            if stage_name not in file_status.stages_completed:
                file_status.stages_completed.append(stage_name)
            
            # If this is the final stage, mark file as completed
            if len(file_status.stages_completed) >= len([s for s in self.STANDARD_STAGES if s != 'initialization']):
                file_status.status = 'completed'
                file_status.end_time = time.time()
                self.state.completed_files += 1
            
            if document_count > 0:
                file_status.document_count = document_count
            if last_doc_id:
                file_status.last_document_id = last_doc_id
                
            self.save_state()
    
    def fail_file_processing(self, filename: str, stage_name: str, error_message: str = ""):
        """Mark a file as failed during processing."""
        if self.state and filename in self.state.files:
            file_status = self.state.files[filename]
            file_status.status = 'failed'
            file_status.error_message = error_message
            file_status.end_time = time.time()
            
            # Update stage progress
            if stage_name in self.state.stages:
                stage = self.state.stages[stage_name]
                stage.last_processed_file = filename
            
            self.state.failed_files += 1
            self.save_state()
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """
        Get a comprehensive summary of processing status.
        
        Returns:
            Dictionary with detailed processing summary
        """
        if not self.state:
            return {}
        
        summary = {
            'dataset_name': self.state.dataset_name,
            'overall_status': self.state.overall_status,
            'overall_progress': self.state.overall_progress_percentage,
            'total_files': self.state.total_files,
            'completed_files': self.state.completed_files,
            'failed_files': self.state.failed_files,
            'pending_files': self.state.total_files - self.state.completed_files - self.state.failed_files,
            'stages': {},
            'estimated_time_remaining': 0,
            'total_processing_time': 0
        }
        
        # Stage summaries
        for stage_name, stage in self.state.stages.items():
            summary['stages'][stage_name] = {
                'status': stage.status,
                'progress': stage.progress_percentage,
                'duration': stage.duration,
                'files_completed': stage.files_completed,
                'files_total': stage.files_total
            }
        
        # Calculate estimated time remaining
        completed_stages = [s for s in self.state.stages.values() if s.status == 'completed']
        if completed_stages:
            avg_stage_time = sum(s.duration for s in completed_stages) / len(completed_stages)
            remaining_stages = len([s for s in self.state.stages.values() if s.status == 'pending'])
            summary['estimated_time_remaining'] = avg_stage_time * remaining_stages
        
        return summary
    
    def cleanup_state_files(self):
        """Remove state files (useful for fresh starts)."""
        files_to_remove = [self.state_file, f"{self.state_file}.backup"]
        
        for file_path in files_to_remove:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"🗑️  Removed state file: {file_path}")
                except Exception as e:
                    print(f"⚠️  Warning: Could not remove {file_path}: {e}")