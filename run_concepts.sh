#!/bin/bash

# Enhanced script to run concept generation with Ollama
echo "🧠 AutoSchemaKG Concept Generation with Ollama"
echo "=============================================="
echo ""

# Check if performance mode is provided as argument
PERFORMANCE_MODE=${1:-"high"}

# Validate performance mode
case $PERFORMANCE_MODE in
    low|medium|high|max)
        echo "✅ Performance mode: $PERFORMANCE_MODE"
        ;;
    *)
        echo "❌ Invalid performance mode: $PERFORMANCE_MODE"
        echo "Valid options: low, medium, high, max"
        echo ""
        echo "Usage: $0 [performance_mode]"
        echo "  low    - Conservative (batch: 16, workers: 2)"
        echo "  medium - Balanced (batch: 64, workers: 8)"  
        echo "  high   - Fast (batch: 128, workers: 16) [DEFAULT]"
        echo "  max    - Maximum (batch: 256, workers: 32)"
        exit 1
        ;;
esac

echo "🔧 Configuration:"
echo "   Backend: Ollama (qwen3:30b-a3b-instruct-2507-q4_K_M)"
echo "   Performance: $PERFORMANCE_MODE mode"
echo "   Source: config.ini settings"
echo ""

# Show performance details
case $PERFORMANCE_MODE in
    low)
        echo "💡 LOW mode: batch_size=16, workers=2 (conservative)"
        ;;
    medium)
        echo "💡 MEDIUM mode: batch_size=64, workers=8 (balanced)"
        ;;
    high)
        echo "💡 HIGH mode: batch_size=128, workers=16 (recommended for Ollama)"
        ;;
    max)
        echo "💡 MAX mode: batch_size=256, workers=32 (high-end systems only)"
        echo "⚠️  Ensure sufficient CPU/memory for MAX mode"
        ;;
esac

echo ""
echo "🚀 Starting concept generation..."
echo "=================================="

# Create logs directory
mkdir -p logs

# Run concept generation with specified performance mode
python pdf_kg_extraction_pipeline_robust.py --stage concepts --performance $PERFORMANCE_MODE