#!/usr/bin/env python3
"""
Verification script for 360t_guide_direct_api_v2 Neo4j import.
This script connects to the autoschemakg database and runs verification queries.
"""

from neo4j import GraphDatabase
import sys

def connect_to_neo4j():
    """
    Connect to the Neo4j autoschemakg database.
    
    Returns:
        neo4j.Driver: Database driver instance
    """
    
    try:
        # Connect to Neo4j with default database using correct credentials
        driver = GraphDatabase.driver(
            "bolt://localhost:7687",
            auth=("neo4j", "1979@rabu")  # Correct credentials from config.ini
        )
        
        # Test connection
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()["test"]
            if test_value == 1:
                print("✅ Successfully connected to autoschemakg database")
                return driver
            else:
                print("❌ Connection test failed")
                return None
                
    except Exception as e:
        print(f"❌ Failed to connect to Neo4j: {e}")
        print("   Make sure Neo4j is running and autoschemakg database exists")
        return None

def run_verification_queries(driver):
    """
    Run verification queries to check the import results.
    
    Args:
        driver: Neo4j driver instance
        
    Returns:
        dict: Results of verification queries
    """
    
    verification_results = {}
    
    queries = {
        "node_labels": "MATCH (n) RETURN labels(n)[0] as label, count(n) as count ORDER BY label",
        "relationship_types": "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count ORDER BY type",
        "concept_nodes": "MATCH (n:Concept) RETURN count(n) as count",
        "regular_nodes": "MATCH (n:Node) RETURN count(n) as count", 
        "text_nodes": "MATCH (n:Text) RETURN count(n) as count",
        "total_nodes": "MATCH (n) RETURN count(n) as count",
        "total_relationships": "MATCH ()-[r]->() RETURN count(r) as count",
        "concept_relationships": "MATCH ()-[r:Concept]->() RETURN count(r) as count",
        "relation_relationships": "MATCH ()-[r:Relation]->() RETURN count(r) as count"
    }
    
    print("🔍 Running verification queries...")
    print("-" * 50)
    
    with driver.session() as session:
        for query_name, query in queries.items():
            try:
                result = session.run(query)
                
                if query_name in ["node_labels", "relationship_types"]:
                    # These return multiple rows
                    rows = list(result)
                    verification_results[query_name] = rows
                    
                    print(f"\n📊 {query_name.replace('_', ' ').title()}:")
                    for row in rows:
                        if query_name == "node_labels":
                            print(f"   {row['label']}: {row['count']:,}")
                        else:
                            print(f"   {row['type']}: {row['count']:,}")
                else:
                    # These return single values
                    count = result.single()["count"]
                    verification_results[query_name] = count
                    print(f"✅ {query_name.replace('_', ' ').title()}: {count:,}")
                    
            except Exception as e:
                print(f"❌ Error running {query_name}: {e}")
                verification_results[query_name] = None
    
    return verification_results

def validate_import_results(results):
    """
    Validate the import results against expected values.
    
    Args:
        results (dict): Verification query results
        
    Returns:
        bool: True if validation passes, False otherwise
    """
    
    print("\n🎯 Validating Import Results...")
    print("-" * 50)
    
    # Expected values from our analysis
    expected = {
        "total_nodes": 1090,
        "total_relationships": 2901,
        "concept_nodes": 599,  # concept_nodes__from_json_with_concept.csv had 599 records
        "regular_nodes": 479,  # triple_nodes__from_json_without_emb.csv had 479 records  
        "text_nodes": 12      # text_nodes__from_json.csv had 12 records
    }
    
    validation_passed = True
    
    for key, expected_value in expected.items():
        actual_value = results.get(key)
        
        if actual_value is None:
            print(f"❌ {key}: No data retrieved")
            validation_passed = False
        elif actual_value == expected_value:
            print(f"✅ {key}: {actual_value:,} (matches expected)")
        else:
            print(f"⚠️  {key}: {actual_value:,} (expected {expected_value:,})")
            # Allow small differences due to potential data variations
            if abs(actual_value - expected_value) <= 5:
                print(f"   → Within acceptable range (±5)")
            else:
                validation_passed = False
    
    # Check total entities
    if results.get("total_nodes") and results.get("total_relationships"):
        total_entities = results["total_nodes"] + results["total_relationships"]
        expected_total = 1090 + 2901  # 3991
        
        if total_entities == expected_total:
            print(f"✅ Total entities: {total_entities:,} (matches expected)")
        else:
            print(f"⚠️  Total entities: {total_entities:,} (expected {expected_total:,})")
    
    return validation_passed

def sample_data_queries(driver):
    """
    Run sample queries to verify data integrity.
    
    Args:
        driver: Neo4j driver instance
    """
    
    print("\n🔬 Sample Data Verification...")
    print("-" * 50)
    
    sample_queries = {
        "Sample Concept Nodes": "MATCH (n:Concept) RETURN n.name LIMIT 5",
        "Sample Text Nodes": "MATCH (n:Text) RETURN substring(n.original_text, 0, 100) + '...' as text_preview LIMIT 3",
        "Sample Relationships": "MATCH (a)-[r]->(b) RETURN labels(a)[0] as from_label, type(r) as relation, labels(b)[0] as to_label LIMIT 5",
        "Concept Relationships": "MATCH ()-[r:Concept]->() RETURN r.relation LIMIT 5"
    }
    
    with driver.session() as session:
        for query_name, query in sample_queries.items():
            try:
                print(f"\n📋 {query_name}:")
                result = session.run(query)
                rows = list(result)
                
                if not rows:
                    print("   No data found")
                else:
                    for i, row in enumerate(rows, 1):
                        if query_name == "Sample Concept Nodes":
                            print(f"   {i}. {row['name']}")
                        elif query_name == "Sample Text Nodes":
                            print(f"   {i}. {row['text_preview']}")
                        elif query_name == "Sample Relationships":
                            print(f"   {i}. {row['from_label']} --[{row['relation']}]--> {row['to_label']}")
                        elif query_name == "Concept Relationships":
                            print(f"   {i}. {row['relation']}")
                            
            except Exception as e:
                print(f"   ❌ Error: {e}")

def main():
    """
    Main verification function.
    """
    
    print("🔍 Neo4j Import Verification for 360t_guide_direct_api_v2")
    print("=" * 70)
    print("Database: neo4j (default)")
    print()
    
    # Connect to Neo4j
    driver = connect_to_neo4j()
    if not driver:
        print("❌ Cannot proceed without database connection")
        return False
    
    try:
        # Run verification queries
        results = run_verification_queries(driver)
        
        # Validate results
        validation_passed = validate_import_results(results)
        
        # Sample data verification
        sample_data_queries(driver)
        
        # Final summary
        print("\n🎉 VERIFICATION SUMMARY")
        print("=" * 70)
        
        if validation_passed:
            print("✅ Import verification PASSED!")
            print("✅ All expected data counts match")
            print("✅ Database structure is correct")
            print("✅ Sample queries return valid data")
            print()
            print("🎯 The 360T guide knowledge graph has been successfully imported")
            print("   into the autoschemakg database and is ready for use.")
        else:
            print("⚠️  Import verification had some issues")
            print("   Please review the results above for details")
            print("   The import may still be usable depending on the specific issues")
        
        return validation_passed
        
    finally:
        driver.close()
        print("\n🔌 Database connection closed")

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Verification failed: {str(e)}")
        sys.exit(1)
