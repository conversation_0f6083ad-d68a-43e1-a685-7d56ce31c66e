#!/usr/bin/env python3
"""
Test script to verify the Gemini-compatible CSV converter functionality.
"""

import os
import sys
from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from setup_processing_config import create_processing_config
from robust_kg_extractor import RobustKGExtractor

def test_format_detection():
    """Test the format detection functionality."""
    print("🔍 Testing format detection...")
    
    # Setup
    llm_generator = setup_gemini_llm_generator_direct()
    processing_config = create_processing_config("pdf_dataset", "")
    
    # Create robust extractor
    robust_extractor = RobustKGExtractor(
        model=llm_generator,
        config=processing_config,
        resume_enabled=True,
        progress_log_level="INFO"
    )
    
    # Test format detection
    needs_gemini = robust_extractor._needs_gemini_converter()
    print(f"   Format detection result: {'Gemini format' if needs_gemini else 'Original JSONL format'}")
    
    return needs_gemini

def test_csv_conversion():
    """Test the CSV conversion functionality."""
    print("🔄 Testing CSV conversion...")
    
    # Setup
    llm_generator = setup_gemini_llm_generator_direct()
    processing_config = create_processing_config("pdf_dataset", "")
    
    # Create robust extractor
    robust_extractor = RobustKGExtractor(
        model=llm_generator,
        config=processing_config,
        resume_enabled=True,
        progress_log_level="INFO"
    )
    
    # Test CSV conversion
    success = robust_extractor.convert_json_to_csv()
    print(f"   CSV conversion result: {'✅ Success' if success else '❌ Failed'}")
    
    return success

if __name__ == "__main__":
    print("🧪 Testing Gemini-compatible CSV Converter")
    print("=" * 50)
    
    try:
        # Test format detection
        format_result = test_format_detection()
        
        # Test CSV conversion
        conversion_result = test_csv_conversion()
        
        print("\n📊 Test Results:")
        print(f"   Format Detection: {'✅ Pass' if format_result is not None else '❌ Fail'}")
        print(f"   CSV Conversion: {'✅ Pass' if conversion_result else '❌ Fail'}")
        
        if conversion_result:
            print("\n🎉 All tests passed! The Gemini-compatible converter is working correctly.")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)
            
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
