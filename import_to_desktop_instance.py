#!/usr/bin/env python3
"""
Import 360t_guide_direct_api_v2 knowledge graph data into Neo4j Desktop instance.
This script generates the correct import command for your specific Neo4j Desktop instance.
"""

import os
import sys
from pathlib import Path

def find_neo4j_desktop_instances():
    """
    Find Neo4j Desktop instances on macOS.
    """
    desktop_path = Path.home() / "Library/Application Support/neo4j-desktop/Application/Data/dbmss"
    
    if not desktop_path.exists():
        print(f"❌ Neo4j Desktop data directory not found at: {desktop_path}")
        return []
    
    instances = []
    for instance_dir in desktop_path.iterdir():
        if instance_dir.is_dir() and instance_dir.name.startswith("dbms-"):
            conf_file = instance_dir / "conf" / "neo4j.conf"
            if conf_file.exists():
                instances.append({
                    'id': instance_dir.name,
                    'path': instance_dir,
                    'conf': conf_file
                })
    
    return instances

def generate_import_command(instance_path, csv_base_path):
    """
    Generate the Neo4j admin import command for the specific instance.
    """
    
    # CSV files to import
    csv_files = {
        'concept_nodes': 'concept_csv/concept_nodes__from_json_with_concept.csv',
        'triple_nodes': 'triples_csv/triple_nodes__from_json_without_emb.csv', 
        'text_nodes': 'triples_csv/text_nodes__from_json.csv',
        'concept_edges': 'concept_csv/concept_edges__from_json_with_concept.csv',
        'triple_edges': 'triples_csv/triple_edges__from_json_without_emb.csv',
        'text_edges': 'triples_csv/text_edges__from_json.csv'
    }
    
    # Build the import command
    cmd_parts = [
        "neo4j-admin database import full autoschemakg",
        f"--nodes={csv_base_path}/{csv_files['concept_nodes']}",
        f"--nodes={csv_base_path}/{csv_files['triple_nodes']}",
        f"--nodes={csv_base_path}/{csv_files['text_nodes']}",
        f"--relationships={csv_base_path}/{csv_files['concept_edges']}",
        f"--relationships={csv_base_path}/{csv_files['triple_edges']}",
        f"--relationships={csv_base_path}/{csv_files['text_edges']}",
        "--overwrite-destination",
        "--multiline-fields=true",
        "--id-type=string",
        "--verbose",
        "--skip-bad-relationships=true",
        "--format=standard"
    ]
    
    return " \\\n    ".join(cmd_parts)

def main():
    """
    Main function to generate import instructions.
    """
    print("🔍 Neo4j Desktop Instance Import Helper")
    print("=" * 60)
    
    # Find Neo4j Desktop instances
    instances = find_neo4j_desktop_instances()
    
    if not instances:
        print("❌ No Neo4j Desktop instances found!")
        return
    
    print(f"✅ Found {len(instances)} Neo4j Desktop instance(s):")
    for i, instance in enumerate(instances, 1):
        print(f"   {i}. {instance['id']}")
    
    # Get current working directory for CSV files
    current_dir = Path.cwd()
    csv_base_path = current_dir / "import" / "360t_guide_direct_api_v2"
    
    if not csv_base_path.exists():
        print(f"❌ CSV data directory not found: {csv_base_path}")
        return
    
    print(f"\n📁 CSV data found at: {csv_base_path}")
    
    # Generate import instructions
    print("\n" + "=" * 60)
    print("📋 IMPORT INSTRUCTIONS")
    print("=" * 60)
    
    print("\n1️⃣ **Stop your Neo4j Desktop instance first**")
    print("   - In Neo4j Desktop, click 'Stop' on 360TKnowledgeBaseInstance")
    print("   - Wait for it to show 'STOPPED' status")
    
    print("\n2️⃣ **Run the import command**")
    print("   Open Terminal and run:")
    print()
    
    # Use the first instance (assuming it's the target)
    if instances:
        import_cmd = generate_import_command(instances[0]['path'], csv_base_path)
        print(f"   {import_cmd}")
    
    print("\n3️⃣ **Start your instance and verify**")
    print("   - In Neo4j Desktop, click 'Start' on 360TKnowledgeBaseInstance")
    print("   - Open Neo4j Browser")
    print("   - Run: :use autoschemakg")
    print("   - Run: MATCH (n) RETURN count(n) as total_nodes")
    print("   - Expected result: 1,090 nodes")
    
    print("\n4️⃣ **Verification queries**")
    print("   MATCH ()-[r]->() RETURN count(r) as total_relationships  // Should be 3,380 (2,901 + 479 text edges)")
    print("   MATCH (n:Concept) RETURN count(n)  // Should be 599")
    print("   MATCH (n:Node) RETURN count(n)     // Should be 479")
    print("   MATCH (n:Text) RETURN count(n)     // Should be 12")
    print("   MATCH ()-[r:Source]->() RETURN count(r) as text_source_relationships  // Should be 479")
    
    print("\n✅ Import preparation complete!")

if __name__ == "__main__":
    main()
