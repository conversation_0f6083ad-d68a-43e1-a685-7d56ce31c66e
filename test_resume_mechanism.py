#!/usr/bin/env python3
"""
Test Resume Mechanism - Validates the resume extraction functionality.

This script tests the resume extraction mechanism to ensure it works correctly
with your specific extraction file and setup.
"""

import os
import sys
import json
from resume_extraction_analyzer import ResumeExtractionAnalyzer, ResumePoint


def test_file_access():
    """Test that we can access the required files."""
    print("🧪 Test 1: File Access")
    print("-" * 30)
    
    # Test paths
    existing_output = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    data_directory = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data"
    
    # Test existing output file
    if os.path.exists(existing_output):
        file_size = os.path.getsize(existing_output) / (1024 * 1024)  # MB
        print(f"✅ Existing output file found: {file_size:.1f} MB")
    else:
        print(f"❌ Existing output file not found: {existing_output}")
        return False
    
    # Test data directory
    if os.path.exists(data_directory):
        json_files = [f for f in os.listdir(data_directory) if f.endswith('.json')]
        print(f"✅ Data directory found: {len(json_files)} JSON files")
    else:
        print(f"❌ Data directory not found: {data_directory}")
        return False
    
    return True


def test_json_parsing():
    """Test that we can parse the existing JSON output."""
    print("\n🧪 Test 2: JSON Parsing")
    print("-" * 30)
    
    existing_output = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    
    try:
        chunks_loaded = 0
        sample_chunk = None
        
        with open(existing_output, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f):
                if line.strip():
                    try:
                        chunk = json.loads(line)
                        chunks_loaded += 1
                        
                        # Store first chunk as sample
                        if chunks_loaded == 1:
                            sample_chunk = chunk
                        
                        # Stop after checking first 10 lines for speed
                        if line_num >= 10:
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON parsing error at line {line_num + 1}: {e}")
                        return False
        
        print(f"✅ JSON parsing successful: {chunks_loaded} chunks in first 10 lines")
        
        # Validate chunk structure
        if sample_chunk:
            required_fields = ['id', 'original_text', 'entity_relation_dict']
            missing_fields = [field for field in required_fields if field not in sample_chunk]
            
            if missing_fields:
                print(f"⚠️  Sample chunk missing fields: {missing_fields}")
            else:
                print("✅ Chunk structure validation passed")
                
                # Show sample data
                text_length = len(sample_chunk.get('original_text', ''))
                entity_count = len(sample_chunk.get('entity_relation_dict', []))
                print(f"   Sample chunk: {text_length} chars, {entity_count} entities")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON parsing failed: {e}")
        return False


def test_resume_analysis():
    """Test the resume point analysis."""
    print("\n🧪 Test 3: Resume Point Analysis")
    print("-" * 30)
    
    existing_output = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    data_directory = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data"
    
    try:
        # Create analyzer
        analyzer = ResumeExtractionAnalyzer(existing_output, data_directory)
        
        # Test loading existing output
        chunks = analyzer.load_existing_output()
        print(f"✅ Loaded {len(chunks)} chunks from existing output")
        
        # Test discovering input files
        input_files = analyzer.discover_input_files()
        print(f"✅ Discovered {len(input_files)} input files")
        
        # Test character count calculation
        file_counts = analyzer.calculate_file_character_counts()
        total_chars = sum(file_counts.values())
        print(f"✅ Calculated character counts: {total_chars:,} total characters")
        
        # Test resume point calculation
        resume_point = analyzer.calculate_resume_point(batch_size=16)
        print(f"✅ Resume point calculated: batch {resume_point.batch_resume_index}")
        
        # Validate resume point
        if resume_point.total_chunks_processed > 0:
            print(f"   Chunks processed: {resume_point.total_chunks_processed:,}")
            print(f"   Characters processed: {resume_point.total_characters_processed:,}")
            print(f"   Current file: {resume_point.current_file}")
            print(f"   Files completed: {len(resume_point.files_completed)}")
            print(f"   Files remaining: {len(resume_point.files_remaining)}")
        else:
            print("⚠️  No chunks found in resume analysis")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Resume analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_batch_calculation():
    """Test batch index calculation accuracy."""
    print("\n🧪 Test 4: Batch Calculation")
    print("-" * 30)
    
    existing_output = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    data_directory = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data"
    
    try:
        analyzer = ResumeExtractionAnalyzer(existing_output, data_directory)
        
        # Test different batch sizes
        batch_sizes = [8, 16, 32]
        
        for batch_size in batch_sizes:
            resume_point = analyzer.calculate_resume_point(batch_size=batch_size)
            expected_batches = resume_point.total_chunks_processed // batch_size
            
            print(f"   Batch size {batch_size}: {expected_batches} batches, index {resume_point.batch_resume_index}")
            
            # Validate calculation
            if resume_point.batch_resume_index != expected_batches:
                print(f"⚠️  Batch calculation mismatch for size {batch_size}")
            else:
                print(f"✅ Batch calculation correct for size {batch_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Batch calculation test failed: {e}")
        return False


def test_file_mapping():
    """Test mapping of chunks to source files."""
    print("\n🧪 Test 5: File Mapping")
    print("-" * 30)
    
    existing_output = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    data_directory = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data"
    
    try:
        analyzer = ResumeExtractionAnalyzer(existing_output, data_directory)
        
        # Load data
        analyzer.load_existing_output()
        analyzer.discover_input_files()
        analyzer.calculate_file_character_counts()
        
        # Test file mapping
        file_mapping = analyzer.map_chunks_to_files()
        
        print(f"✅ File mapping completed")
        print(f"   Current file: {file_mapping['current_file']}")
        print(f"   Current position: {file_mapping['current_file_position']:,}")
        print(f"   Total processed: {file_mapping['total_chars_processed']:,}")
        
        # Show file status breakdown
        status_counts = {}
        for filename, info in file_mapping['file_mapping'].items():
            status = info['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"   File status: {dict(status_counts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ File mapping test failed: {e}")
        return False


def run_comprehensive_test():
    """Run all tests and provide summary."""
    print("🧪 Resume Mechanism Test Suite")
    print("=" * 50)
    print("Testing the resume extraction functionality with your specific files.")
    print()
    
    tests = [
        ("File Access", test_file_access),
        ("JSON Parsing", test_json_parsing),
        ("Resume Analysis", test_resume_analysis),
        ("Batch Calculation", test_batch_calculation),
        ("File Mapping", test_file_mapping)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 30)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print()
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Your resume mechanism is ready to use.")
        print()
        print("📋 Next Steps:")
        print("1. Run: python resume_extraction.py --analyze")
        print("2. Then: python resume_extraction.py")
        
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        print("❌ Resume mechanism needs attention before use.")
        print()
        print("🔧 Troubleshooting:")
        print("1. Check file paths and permissions")
        print("2. Verify JSON file format and content")
        print("3. Ensure data directory contains expected files")
        
        return False


def show_resume_status():
    """Show current resume status without running tests."""
    print("📊 Current Resume Status")
    print("=" * 50)
    
    existing_output = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/kg_extraction/gemini-2.5-flash__output_20250730231207_1_in_1.json"
    data_directory = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/example_data"
    
    try:
        analyzer = ResumeExtractionAnalyzer(existing_output, data_directory)
        resume_point = analyzer.calculate_resume_point(batch_size=16)
        analyzer.print_resume_analysis(resume_point)
        
    except Exception as e:
        print(f"❌ Could not analyze resume status: {e}")


def main():
    """Main test runner."""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--status":
            show_resume_status()
            return
        elif sys.argv[1] == "--help":
            print("🧪 Resume Mechanism Test")
            print("=" * 30)
            print("Usage:")
            print("  python test_resume_mechanism.py           # Run all tests")
            print("  python test_resume_mechanism.py --status  # Show current status")
            print("  python test_resume_mechanism.py --help    # Show this help")
            return
    
    # Run comprehensive test
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()