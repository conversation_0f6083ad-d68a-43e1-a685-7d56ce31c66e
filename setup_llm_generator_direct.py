"""
Direct API LLM Generator Setup - Compatible with existing AutoSchemaKG pipeline
This module provides a drop-in replacement for setup_llm_generator that uses direct Google Gen AI API
"""

from setup_gemini_direct import DirectGeminiLLMGenerator, setup_gemini_direct_api
from atlas_rag.llm_generator import LL<PERSON><PERSON>ator
from configparser import Config<PERSON><PERSON><PERSON>
import json
from typing import List, Dict, Any
from tenacity import retry, stop_after_attempt, stop_after_delay, wait_exponential, wait_random

# Retry decorator matching the original LLMGenerator
retry_decorator = retry(
    stop=(stop_after_delay(120) | stop_after_attempt(5)),  # Max 2 minutes or 5 attempts
    wait=wait_exponential(multiplier=1, min=2, max=30) + wait_random(min=0, max=2),
)

class DirectGeminiLLMGeneratorAdapter:
    """
    Adapter that makes DirectGeminiLLMGenerator fully compatible with LLMGenerator interface.
    This allows direct API usage without changing the AutoSchemaKG pipeline code.
    """
    
    def __init__(self, direct_generator: DirectGeminiLLMGenerator):
        self.direct_generator = direct_generator
        self.model_name = direct_generator.model_name
        self.inference_type = direct_generator.inference_type
        
    @retry_decorator
    def _api_inference(self, message, max_new_tokens=8192,
                       temperature=0.7,
                       frequency_penalty=None,
                       response_format={"type": "text"},
                       return_text_only=True,
                       return_thinking=False,
                       reasoning_effort=None,
                       **kwargs):
        """
        Compatible interface with LLMGenerator._api_inference method.
        This is the main method called by the AutoSchemaKG pipeline.
        """
        print(f"🔄 Direct API inference - max_new_tokens: {max_new_tokens}, temp: {temperature}")
        
        # Use the direct generator
        response = self.direct_generator.generate_response(
            message,
            max_new_tokens=max_new_tokens,
            temperature=temperature,
            **kwargs
        )
        
        if return_text_only:
            return response
        else:
            # Return response in the expected format if needed
            return {
                'content': response,
                'usage': {'completion_tokens': len(response.split()), 'prompt_tokens': 'unknown', 'total_tokens': 'unknown'}
            }
    
    def generate_cot(self, question: str, max_new_tokens: int = 512) -> str:
        """Chain-of-Thought generation."""
        return self.direct_generator.generate_cot(question, max_new_tokens)
    
    def generate_with_context(self, question: str, context: str, max_new_tokens: int = 256) -> str:
        """Generate response with context."""
        return self.direct_generator.generate_with_context(question, context, max_new_tokens)
    
    def ner(self, text: str) -> List[str]:
        """Named Entity Recognition."""
        return self.direct_generator.ner(text)
    
    def generate_response(self, messages, return_text_only=True, **kwargs):
        """
        Generate response from messages - concept generation compatible.
        Handles both single message and batch processing for concept generation.
        """
        print(f"🔍 Direct API generate_response called with {len(messages) if isinstance(messages, list) else 'single'} message(s)")
        
        # Handle both single message and batch cases
        if isinstance(messages, list) and len(messages) > 0:
            if isinstance(messages[0], list):
                # Batch of message lists - process each batch item
                results = []
                for message_list in messages:
                    # Add retry logic for rate limits
                    max_retries = 3
                    retry_delay = 5  # seconds
                    
                    for attempt in range(max_retries):
                        try:
                            # Add delay between calls to avoid rate limits
                            if len(results) > 0:  # Not the first call
                                import time
                                time.sleep(2)  # 2 second delay between API calls
                            
                            response = self.direct_generator.generate_response(message_list, **kwargs)
                            if return_text_only:
                                results.append(response)
                            else:
                                # Return tuple of (response, usage_info) for recording
                                results.append([response, {"completion_tokens": len(response.split())}])
                            break  # Success, exit retry loop
                        except Exception as e:
                            if "rate" in str(e).lower() or "quota" in str(e).lower() or "429" in str(e):
                                if attempt < max_retries - 1:
                                    print(f"⏱️ Rate limit hit, retrying in {retry_delay} seconds... (attempt {attempt + 1}/{max_retries})")
                                    import time
                                    time.sleep(retry_delay)
                                    retry_delay *= 2  # Exponential backoff
                                    continue
                            
                            print(f"❌ Direct API error: {str(e)}")
                            print(f"Error type: {type(e)}")
                            if return_text_only:
                                results.append("")  # Empty response on error
                            else:
                                results.append(["", {"completion_tokens": 0}])
                            break
                return results
            else:
                # Single message list
                try:
                    response = self.direct_generator.generate_response(messages, **kwargs)
                    if return_text_only:
                        return response
                    else:
                        return [response, {"completion_tokens": len(response.split())}]
                except Exception as e:
                    print(f"❌ Direct API error: {str(e)}")
                    print(f"Error type: {type(e)}")
                    if return_text_only:
                        return ""
                    else:
                        return ["", {"completion_tokens": 0}]
        else:
            # Single message case
            try:
                response = self.direct_generator.generate_response(messages, **kwargs)
                if return_text_only:
                    return response
                else:
                    return [response, {"completion_tokens": len(response.split())}]
            except Exception as e:
                print(f"❌ Direct API error: {str(e)}")
                print(f"Error type: {type(e)}")
                if return_text_only:
                    return ""
                else:
                    return ["", {"completion_tokens": 0}]
    
    def triple_extraction(self, messages, max_tokens=4096, stage=None, record=False, allow_empty=True):
        """
        Triple extraction method compatible with AutoSchemaKG pipeline.
        This is the key method called by KnowledgeGraphExtractor.
        """
        from atlas_rag.llm_generator.format.validate_json_output import validate_output, fix_triple_extraction_response
        from atlas_rag.llm_generator.format.validate_json_schema import stage_to_schema
        
        print(f"🔍 Direct API Triple Extraction - Stage: {stage}, Max tokens: {max_tokens}")
        
        if isinstance(messages[0], dict):
            messages = [messages]
        
        # Setup validation parameters (matching original LLMGenerator)
        stage_to_prompt_type = {
            1: "entity_relation",
            2: "event_entity", 
            3: "event_relation",
        }
        
        validate_kwargs = {
            'schema': stage_to_schema.get(stage, None),
            'fix_function': fix_triple_extraction_response,
            'prompt_type': stage_to_prompt_type.get(stage, None),
            'allow_empty': allow_empty
        }
        
        try:
            # Process all message batches
            results = []
            
            for message_batch in messages:
                try:
                    # Use direct API for generation
                    response = self.direct_generator.generate_response(
                        message_batch, 
                        max_new_tokens=max_tokens,
                        temperature=0.0  # Use deterministic generation for extraction
                    )
                    
                    # Apply validation if response is not empty
                    if response and response.strip():
                        try:
                            validated_response = validate_output(response, **validate_kwargs)
                            results.append(validated_response if not record else (validated_response, {'completion_tokens': len(response.split()), 'time': 1.0}))
                        except Exception as validation_error:
                            print(f"⚠️  Validation failed: {validation_error}")
                            if allow_empty:
                                results.append("[]" if not record else ("[]", {'completion_tokens': 0, 'time': 0}))
                            else:
                                raise validation_error
                    else:
                        # Empty response
                        if allow_empty:
                            results.append("[]" if not record else ("[]", {'completion_tokens': 0, 'time': 0}))
                        else:
                            raise ValueError("Empty response from API")
                            
                except Exception as generation_error:
                    print(f"❌ Generation failed for batch: {generation_error}")
                    if allow_empty:
                        results.append("[]" if not record else ("[]", {'completion_tokens': 0, 'time': 0}))
                    else:
                        raise generation_error
            
            print(f"✅ Triple extraction completed - {len(results)} batches processed")
            return results
            
        except Exception as e:
            print(f"❌ Triple extraction failed: {e}")
            # Return empty result if validation fails and allow_empty is True
            if allow_empty:
                if record:
                    return [], {'completion_tokens': 0, 'time': 0}
                else:
                    return "[]"
            else:
                raise e

def setup_gemini_llm_generator_direct():
    """
    Drop-in replacement for setup_gemini_llm_generator that uses direct Google Gen AI API.
    
    Returns:
        DirectGeminiLLMGeneratorAdapter: LLMGenerator-compatible direct API wrapper
    """
    
    print("🚀 Setting up Direct Google Gen AI API (bypassing OpenAI wrapper)")
    print("This provides access to the full 1M token context window")
    
    # Setup the direct API generator
    direct_generator = setup_gemini_direct_api()
    
    # Wrap it in the adapter for compatibility
    adapter = DirectGeminiLLMGeneratorAdapter(direct_generator)
    
    print(f"✅ Direct API adapter initialized for {adapter.model_name}")
    print(f"Inference type: {adapter.inference_type}")
    print("🎯 Ready for AutoSchemaKG pipeline with full context window access")
    
    return adapter

def test_pipeline_compatibility(llm_generator):
    """
    Test that the adapter works correctly with AutoSchemaKG pipeline patterns.
    """
    print("\n🧪 Testing Pipeline Compatibility...")
    print("=" * 60)
    
    # Test 1: Basic _api_inference call (main pipeline method)
    print("\n1️⃣ Testing _api_inference method:")
    test_messages = [
        {"role": "system", "content": "You are a knowledge extraction expert."},
        {"role": "user", "content": "Extract entities from: Apple Inc. was founded by Steve Jobs."}
    ]
    
    try:
        response = llm_generator._api_inference(test_messages, max_new_tokens=512)
        print(f"✅ _api_inference Success - Response: {response[:100]}...")
    except Exception as e:
        print(f"❌ _api_inference Failed: {str(e)}")
    
    # Test 2: Test with larger context (what was failing before)
    print("\n2️⃣ Testing with larger context (previous failure case):")
    large_context = "Apple Inc. is an American multinational technology company headquartered in Cupertino, California. " * 20  # ~2000 tokens
    
    large_messages = [
        {"role": "system", "content": "You are a knowledge extraction expert. Extract entities, relations, and events from the given text. Be concise and accurate."},
        {"role": "user", "content": f"Extract knowledge from this text: {large_context}"}
    ]
    
    try:
        response = llm_generator._api_inference(large_messages, max_new_tokens=400)
        print(f"✅ Large context Success - Response: {response[:100]}...")
        print(f"   Input was ~{len(large_context.split())} words")
    except Exception as e:
        print(f"❌ Large context Failed: {str(e)}")
    
    # Test 3: Test with validation function (as used in triple extraction)
    print("\n3️⃣ Testing with validation function:")
    def sample_validation(content, **kwargs):
        # Simple validation - just ensure it's not empty
        if not content or content.strip() == "":
            return "[]"  # Return empty JSON array
        return content
    
    try:
        response = llm_generator._api_inference(
            test_messages, 
            max_new_tokens=256,
            validate_function=sample_validation,
            allow_empty=True
        )
        print(f"✅ Validation function Success - Response: {response[:100]}...")
    except Exception as e:
        print(f"❌ Validation function Failed: {str(e)}")
    
    print("\n📊 Pipeline compatibility testing completed")

if __name__ == "__main__":
    print("🚀 Setting up Direct API LLM Generator for AutoSchemaKG...")
    
    # Setup the adapter
    llm_generator = setup_gemini_llm_generator_direct()
    
    # Test pipeline compatibility
    test_pipeline_compatibility(llm_generator)
    
    print("\n✅ Direct API LLM Generator setup completed successfully!")
    print("Ready to replace the OpenAI wrapper in the AutoSchemaKG pipeline.")
    print("\n💡 Usage example:")
    print("from setup_llm_generator_direct import setup_gemini_llm_generator_direct")
    print("llm_generator = setup_gemini_llm_generator_direct()")