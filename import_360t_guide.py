#!/usr/bin/env python3
"""
Safe import script for 360t_guide_direct_api_v2 knowledge graph data into Neo4j autoschemakg database.
This script uses the existing pipeline infrastructure to generate proper Neo4j import commands.
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pdf_kg_extraction_pipeline import generate_neo4j_import_command

def validate_csv_files(dataset_name):
    """
    Validate that all required CSV files exist and have the correct structure.
    
    Args:
        dataset_name (str): Name of the dataset directory
        
    Returns:
        tuple: (bool, list) - (validation_success, csv_file_paths)
    """
    
    import_dir = f"import/{dataset_name}"
    
    if not os.path.exists(import_dir):
        print(f"❌ Import directory not found: {import_dir}")
        return False, []
    
    # Expected CSV files based on our analysis
    expected_files = {
        'concept_nodes': f"{import_dir}/concept_csv/concept_nodes__from_json_with_concept.csv",
        'concept_edges': f"{import_dir}/concept_csv/concept_edges__from_json_with_concept.csv", 
        'triple_nodes': f"{import_dir}/triples_csv/triple_nodes__from_json_without_emb.csv",
        'triple_edges': f"{import_dir}/triples_csv/triple_edges__from_json_without_emb.csv",
        'text_nodes': f"{import_dir}/triples_csv/text_nodes__from_json.csv"
    }
    
    csv_files = []
    missing_files = []
    
    for file_type, file_path in expected_files.items():
        if os.path.exists(file_path):
            csv_files.append(file_path)
            print(f"✅ Found {file_type}: {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ Missing {file_type}: {file_path}")
    
    if missing_files:
        print(f"\n❌ Validation failed. Missing {len(missing_files)} files:")
        for file in missing_files:
            print(f"   - {file}")
        return False, []
    
    # Validate file structure by checking headers
    print("\n🔍 Validating CSV file headers...")
    
    expected_headers = {
        'concept_nodes': ['concept_id:ID', 'name', ':LABEL'],
        'concept_edges': [':START_ID', ':END_ID', 'relation', ':TYPE'],
        'triple_nodes': ['name:ID', 'type', 'concepts', 'synsets', ':LABEL'],
        'triple_edges': [':START_ID', ':END_ID', 'relation', 'concepts', 'synsets', ':TYPE'],
        'text_nodes': ['text_id:ID', 'original_text', ':LABEL']
    }
    
    for i, (file_type, file_path) in enumerate(expected_files.items()):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                header_line = f.readline().strip()
                # Parse CSV header (handle quoted fields)
                import csv
                reader = csv.reader([header_line])
                actual_headers = next(reader)
                
                expected = expected_headers[file_type]
                if actual_headers == expected:
                    print(f"✅ {file_type} headers valid")
                else:
                    print(f"⚠️  {file_type} headers differ:")
                    print(f"   Expected: {expected}")
                    print(f"   Actual:   {actual_headers}")
        except Exception as e:
            print(f"❌ Error reading {file_type}: {e}")
            return False, []
    
    print(f"\n✅ All {len(csv_files)} CSV files validated successfully")
    return True, csv_files

def count_data_entities(csv_files):
    """
    Count nodes and relationships in the CSV files.
    
    Args:
        csv_files (list): List of CSV file paths
        
    Returns:
        dict: Counts of different entity types
    """
    
    counts = {
        'concept_nodes': 0,
        'concept_edges': 0,
        'triple_nodes': 0,
        'triple_edges': 0,
        'text_nodes': 0,
        'total_nodes': 0,
        'total_relationships': 0
    }
    
    print("📊 Counting entities in CSV files...")
    
    for file_path in csv_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # Count lines minus header
                line_count = sum(1 for line in f) - 1
                
                if 'concept_nodes' in file_path:
                    counts['concept_nodes'] = line_count
                    counts['total_nodes'] += line_count
                elif 'concept_edges' in file_path:
                    counts['concept_edges'] = line_count
                    counts['total_relationships'] += line_count
                elif 'triple_nodes' in file_path:
                    counts['triple_nodes'] = line_count
                    counts['total_nodes'] += line_count
                elif 'triple_edges' in file_path:
                    counts['triple_edges'] = line_count
                    counts['total_relationships'] += line_count
                elif 'text_nodes' in file_path:
                    counts['text_nodes'] = line_count
                    counts['total_nodes'] += line_count
                    
                print(f"   {os.path.basename(file_path)}: {line_count} records")
                
        except Exception as e:
            print(f"❌ Error counting {file_path}: {e}")
            return None
    
    print(f"\n📈 Summary:")
    print(f"   Total Nodes: {counts['total_nodes']:,}")
    print(f"   Total Relationships: {counts['total_relationships']:,}")
    print(f"   Total Entities: {counts['total_nodes'] + counts['total_relationships']:,}")
    
    return counts

def generate_import_command(dataset_name, csv_files):
    """
    Generate Neo4j import command using the existing pipeline function.
    
    Args:
        dataset_name (str): Name of the dataset
        csv_files (list): List of CSV file paths
        
    Returns:
        str: Generated Neo4j import command
    """
    
    print(f"\n🔧 Generating Neo4j import command for {dataset_name}...")
    
    try:
        # Use the existing pipeline function
        command = generate_neo4j_import_command(dataset_name, csv_files)
        
        print("✅ Import command generated successfully")
        return command
        
    except Exception as e:
        print(f"❌ Error generating import command: {e}")
        return None

def save_import_instructions(dataset_name, command, counts):
    """
    Save import instructions to a file with safety warnings.
    
    Args:
        dataset_name (str): Name of the dataset
        command (str): Neo4j import command
        counts (dict): Entity counts
    """
    
    instructions_file = f"neo4j_import_{dataset_name}.txt"
    
    with open(instructions_file, 'w') as f:
        f.write(f"Neo4j Import Instructions for {dataset_name}\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("🎯 DATASET SUMMARY:\n")
        f.write(f"   Dataset: {dataset_name}\n")
        f.write(f"   Total Nodes: {counts['total_nodes']:,}\n")
        f.write(f"   Total Relationships: {counts['total_relationships']:,}\n")
        f.write(f"   Total Entities: {counts['total_nodes'] + counts['total_relationships']:,}\n\n")
        
        f.write("⚠️  CRITICAL SAFETY REQUIREMENTS:\n")
        f.write("   1. This command targets the 'autoschemakg' database ONLY\n")
        f.write("   2. NEVER run this against the default 'neo4j' database\n")
        f.write("   3. STOP your autoschemakg database before running this command\n")
        f.write("   4. Run this command from your Neo4j installation directory\n")
        f.write("   5. START your autoschemakg database after import completes\n\n")
        
        f.write("📋 PRE-IMPORT CHECKLIST:\n")
        f.write("   □ Neo4j autoschemakg database is STOPPED\n")
        f.write("   □ You are in the Neo4j installation directory\n")
        f.write("   □ CSV files are accessible from current directory\n")
        f.write("   □ You have confirmed this targets 'autoschemakg' database\n\n")
        
        f.write("🚀 IMPORT COMMAND:\n")
        f.write("-" * 50 + "\n")
        f.write(command)
        f.write("\n" + "-" * 50 + "\n\n")
        
        f.write("✅ POST-IMPORT STEPS:\n")
        f.write("   1. Check import logs for any errors\n")
        f.write("   2. Start your autoschemakg database\n")
        f.write("   3. Verify node and relationship counts in Neo4j Browser\n")
        f.write("   4. Test basic queries to confirm data integrity\n\n")
        
        f.write("🔍 VERIFICATION QUERIES:\n")
        f.write("   MATCH (n) RETURN labels(n), count(n)\n")
        f.write("   MATCH ()-[r]->() RETURN type(r), count(r)\n")
        f.write("   MATCH (n:Concept) RETURN count(n)  // Should be 601\n")
        f.write("   MATCH (n:Node) RETURN count(n)     // Should be 495\n")
        f.write("   MATCH (n:Text) RETURN count(n)     // Should be 14\n")
    
    print(f"✅ Import instructions saved to: {instructions_file}")

def main():
    """
    Main function to execute the safe import process.
    """
    
    dataset_name = "360t_guide_direct_api_v2"
    
    print("🚀 AutoSchemaKG Safe Import for 360T Guide Dataset")
    print("=" * 60)
    print(f"Dataset: {dataset_name}")
    print(f"Target Database: autoschemakg (NEVER neo4j)")
    print()
    
    # Step 1: Validate CSV files
    print("1️⃣ Validating CSV Files...")
    print("-" * 30)
    
    is_valid, csv_files = validate_csv_files(dataset_name)
    if not is_valid:
        print("❌ Validation failed. Cannot proceed with import.")
        return False
    
    # Step 2: Count entities
    print("\n2️⃣ Analyzing Data...")
    print("-" * 30)
    
    counts = count_data_entities(csv_files)
    if not counts:
        print("❌ Failed to analyze data. Cannot proceed with import.")
        return False
    
    # Step 3: Generate import command
    print("\n3️⃣ Generating Import Command...")
    print("-" * 30)
    
    command = generate_import_command(dataset_name, csv_files)
    if not command:
        print("❌ Failed to generate import command. Cannot proceed.")
        return False
    
    # Step 4: Save instructions
    print("\n4️⃣ Saving Import Instructions...")
    print("-" * 30)
    
    save_import_instructions(dataset_name, command, counts)
    
    print("\n🎉 IMPORT PREPARATION COMPLETED!")
    print("=" * 60)
    print("✅ All validation checks passed")
    print("✅ Import command generated and saved")
    print("✅ Safety instructions documented")
    print()
    print("📋 NEXT STEPS:")
    print(f"1. Review the instructions in neo4j_import_{dataset_name}.txt")
    print("2. STOP your Neo4j autoschemakg database")
    print("3. Run the import command from your Neo4j installation directory")
    print("4. START your Neo4j autoschemakg database")
    print("5. Verify the import using the provided queries")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  Import preparation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Import preparation failed: {str(e)}")
        sys.exit(1)
