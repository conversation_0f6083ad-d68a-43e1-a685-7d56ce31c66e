import json
from typing import List
from pydantic import BaseModel

from marker.processors.llm import PromptData
from marker.processors.llm.llm_image_description import LLMImageDescriptionProcessor
from marker.schema.document import Document
from marker.schema import BlockTypes


class ImageSchema(BaseModel):
    image_description: str

class LLMImageTransformationProcessor(LLMImageDescriptionProcessor):
    """
    Custom image description processor that transforms the image into a JSON schema.
    """

    figure_prompt: str = """You are a data extraction expert specializing in converting charts and figures into structured JSON format.

You will receive an image of a figure/chart. Your task is to:
1. Identify the type of chart (bar chart, line graph, pie chart, table, workflow, etc.)
2. Extract the title and any labels
3. Convert the data into a structured JSON format, which is readable by machine.
4. Provide a brief description

**Input:**
```text
{raw_text}
```

Please respond with JSON containing:
- figure_type: The type of chart/figure
- title: The main title
- data: Structured data from the chart
- description: Brief description of what the figure shows
"""

    image_prompt: str = """You are an intelligent document processor that can decide the best way to represent image content.

You will receive an image that may contain text, graphics, or mixed content. Your task is to:
1. Analyze the content
2. Decide the best output format: description, extracted text, or structured data
3. Provide the output in the chosen format

**Input:**
```text
{raw_text}
```

**Decision guidelines:**
- If image contains primarily text: extract as structured text
- If image is decorative/photo: provide description
- If image contains structured info: convert to structured format

Please respond with JSON containing:
- content_type: "text", "description", or "structured_data"
- output: Your processed content
- reasoning: Why you chose this format
"""
    def block_prompts(self, document: Document) -> List[PromptData]:
        prompt_data = []
        for block_data in self.inference_blocks(document):
            block = block_data["block"]

            #Choose the best prompt based on the block type
            if block.block_type == BlockTypes.Figure:
                prompt = self.figure_prompt.replace("{raw_text}", block.raw_text(document))
                # schema = FigureJsonSchema
            else:
                prompt = self.image_prompt.replace("{raw_text}", block.raw_text(document))
                # schema = ImageFlexibleSchema
            
            image = self.extract_image(document, block)

            prompt_data.append({
                "prompt": prompt,
                "image": image,
                "block": block,
                "schema": ImageSchema,
                "page": block_data["page"],
            })
        return prompt_data
    

    def rewrite_block(self, response: dict, prompt_data: PromptData, document: Document):
        block = prompt_data["block"]

        if not response or "image_description" not in response:
            block.update_metadata(llm_error_count=1)
            return

        # image_description = response["image_description"]
        image_description = json.dumps(response, indent=2, ensure_ascii=False)
        if len(image_description) < 10:
            block.update_metadata(llm_error_count=1)
            return

        # Use the image description from LLM as the block description
        # TODO: Consider using a more structured format if needed
        block.description = image_description
        
