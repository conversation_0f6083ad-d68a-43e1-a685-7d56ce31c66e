{"processed_files": ["data/ ADS_Market Maker Cockpit  (Swing GUI).pdf", "data/ ADS_Market Maker Cockpit (HTML GUI).pdf", "data/ ADS_Market Maker Cockpit - R3 19.pdf", "data/ HTML Auto Dealing Suite.pdf", "data/ PS Mapping (Bridge Admin).pdf", "data/- Accessing the ECN via Supersonic.pdf", "data/ADS.pdf", "data/Bank Basket Configuration (Bridge Admin).pdf", "data/Bridge Administration - SEF Data.pdf", "data/Bridge Administration I-TEX.pdf", "data/Bridge Administration User Manual.pdf", "data/Bridge Counterpart Relationship Management Tool User Guide_Market Maker.pdf", "data/Bridge Order Book User Guide_Market Taker.pdf", "data/Cash management Tool_GCT_configuration.pdf", "data/Change Requests (Bridge Admin).pdf", "data/Data Disclosure Configuration (Bridge Admin).pdf", "data/EMS - csv File extract.pdf", "data/EMS.pdf", "data/FX Futures SupersonicTrader.pdf", "data/Institution Configuration (Bridge Admin).pdf", "data/Limit_REST_API_v2.0.pdf", "data/Limits Monitor.pdf", "data/Limits Monitor_Trading Limits Profile.pdf", "data/Order_MM_FIX_API_v7.0.pdf", "data/Order_MT_XML_API_v2.1.pdf", "data/RFS Market Taker Bridge.pdf", "data/RFS_FIX_MarketTaker_v12.pdf", "data/RFS_FIX_MarketTaker_v13.pdf", "data/RFS_FIX_MarketTaker_v13_DRAFT.pdf", "data/Regulatory Data (Company Admin Control).pdf", "data/SEF Market Maker.pdf", "data/SEF Market Taker.pdf", "data/SupersonicTrader.pdf", "data/Business Configuration Tool BRIDGE.pdf", "data/TWS Market Maker (BRIDGE).pdf", "data/User_Guide_SEF_Market_Maker_Web.pdf", "data/User_Guide_SEF_Market_Taker_Web.pdf"], "failed_files": [], "last_update": "2025-07-30T12:23:48.544945", "total_files": 37, "start_time": "2025-07-30T09:01:47.725265"}