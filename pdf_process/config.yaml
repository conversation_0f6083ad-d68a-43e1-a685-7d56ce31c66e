# PDF processing config file
processing_config:
  # llm_service: "marker.services.azure_openai.AzureOpenAIService" # COMMENTED OUT to use default Gemini server
  other_config:
    use_llm: true
    extract_images: false  # false means not to extract images and use LLM for text description; true means extract images but not generate descriptions
    page_range: null  # null means process all pages, or use List[int] format like [9, 10, 11, 12]
    max_concurrency: 1 # Reduced for Tier 1 stability (was 2)
    gemini_api_key: "AIzaSyAzB7O_owmCvb5hyqlRG3mjDecvu_QxugI"  # Set Gemini API key directly
    #Azure OpenAI API configuration (no need for Gemini)
    # azure_endpoint: <https://your-azure-openai-endpoint>  # Uncomment and set your Azure OpenAI endpoint
    # azure_api_version: "2024-10-21"
    # deployment_name: "gpt-4o"

# API configuration
api:
  api_key_env: "GOOGLE_API_KEY"  # Using Google Gemini API key
  # api_key_env: "AZURE_API_KEY"  # COMMENTED OUT - not using Azure

# Input path configuration - can be a file or folder path
input:
  # Supports relative and absolute paths
  path: "./360T User Guide Bank Basket Configuration (Bridge Admin).pdf"  # User's specific PDF file in current directory
  # path: "data/Apple_Environmental_Progress_Report_2024.pdf"  # Example of a single file
  
  # If it's a folder, you can set file filtering conditions
  file_filters:
    extensions: [".pdf"]  # Only process PDF files
    recursive: true       # Whether to recursively process subfolders
    exclude_patterns:     # Exclude files that match these patterns
      - "*temp*"
      - "*~*"

# Output configuration
output:
  base_dir: "../example_data/md_data"     # Output to the md_data directory
  create_subdirs: false   # Don't create subdirectories for easier processing
  format: "md"           # Output format (md, txt)
  
# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  show_progress: true