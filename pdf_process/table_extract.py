# Extract Table From PDF (Only Table)
import os
from marker.converters.table import TableConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered

file_list = []
INPUT_DIR = './data'
OUTPUT_DIR = './output/tables'

def get_file_list(input_dir):
    global file_list
    if not os.path.exists(input_dir):
        raise FileNotFoundError(f"Input directory '{input_dir}' does not exist.")
    
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.lower().endswith('.pdf'):
                file_list.append(os.path.join(root, file))
    
    if not file_list:
        raise ValueError("No PDF files found in the input directory.")

    return file_list

def table_extract(file_path):
    converter = TableConverter(
        artifact_dict=create_model_dict(),
    )
    print(f"Extracting table from {file_path}...")
    rendered = converter(file_path)
    text, _, images = text_from_rendered(rendered)

    return text, images

def main():
    global file_list
    try:
        file_list = get_file_list(INPUT_DIR)
    except (FileNotFoundError, ValueError) as e:
        print(e)
        return

    for file_path in file_list:
        try:
            text, images = table_extract(file_path)
            output_file = os.path.join(OUTPUT_DIR, (os.path.basename(file_path)).replace('.pdf', '_tables.txt'))
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(text)
            print(f"Extracted table from {file_path} and saved to {output_file}")
        except Exception as e:
            print(f"Failed to extract table from {file_path}: {e}")


if __name__ == "__main__":
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
    main()