import os
import mimetypes
from google import genai
from google.genai.types import Part
from google.api_core import exceptions

def process_image(prompt: str, image_path: str, model_name: str = 'gemini-2.0-flash', api_key: str = None) -> str:
    """
    Sends an image and a prompt to Gemini API and returns streamed response text.

    Args:
        prompt (str): Prompt for the image.
        image_path (str): Local path to image file.
        model_name (str): Gemini model name.
        api_key (str): API key (optional if set in env).
    """
    print(f"Processing image: {image_path} with model: {model_name}")

    # Get the API key from the environment variable
    if not api_key:
        api_key = os.environ.get('GOOGLE_API_KEY')
        if not api_key:
            print("Error: GOOGLE_API_KEY environment variable is not set.")
            return

    if not os.path.exists(image_path):
        print(f"Error: Image file {image_path} does not exist.")
        return

    mime_type, _ = mimetypes.guess_type(image_path)
    if not mime_type or not mime_type.startswith('image/'):
        print(f"Error: {image_path} is not a valid image file.")
        return

    # Prepare the image as a Part object
    try:
        with open(image_path, "rb") as img_file:
            img_bytes = img_file.read()
            image_part = Part.from_bytes(data=img_bytes, mime_type=mime_type)
    except Exception as e:
        print(f"Error reading image file: {e}")
        return

    # Set up the client
    client = genai.Client(api_key=api_key)

    contents = [prompt, image_part]

    # print("\n---- response ----")
    try:
        # for chunk in client.models.generate_content_stream(
        #     model=model_name,
        #     contents=contents,
        # ):
        #     print(chunk.text, end="")
        # print("\n----------------\n")
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
        )
        return response.text
    except exceptions.PermissionDenied as e:
        print(f"Permission denied: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")
