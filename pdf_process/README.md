# PDF Process
## Preliminary

```shell
# you'd better inclue the ‘’ if you use zsh
pip install 'marker-pdf[full]'
pip install google-genai
```

For Mac,​ install required libraries: (need install homebrew first)
```shell
brew install weasyprint
brew install glib pango harfbuzz fontconfig cairo
```

Create Symbolic Links​​ to ensure the system can find the libraries:
```shell
sudo mkdir -p /usr/local/lib
sudo ln -s /opt/homebrew/opt/glib/lib/libgobject-2.0.0.dylib /usr/local/lib/gobject-2.0
sudo ln -s /opt/homebrew/opt/pango/lib/libpango-1.0.dylib /usr/local/lib/pango-1.0
sudo ln -s /opt/homebrew/opt/harfbuzz/lib/libharfbuzz.dylib /usr/local/lib/harfbuzz
sudo ln -s /opt/homebrew/opt/fontconfig/lib/libfontconfig.1.dylib /usr/local/lib/fontconfig-1
sudo ln -s /opt/homebrew/opt/pango/lib/libpangoft2-1.0.dylib /usr/local/lib/pangoft2-1.0
```

Test whether the links works well
```python
from ctypes.util import find_library
print(find_library('gobject-2.0'))  # Should return a valid path
```

## Quick Start
Step 1: Set your file in "config.yaml"

Step 2: Set your Gemini API in "run.sh"

Step 3: run the script
```shell
bash run.sh
```

## Other
If you are not satisify with the Image Description feature, you can adjust your own prompt in "llm_image_transformation.py"


Todo List:

1. split the image description prompt from "llm_image_transformation.py"
2. update current render logic in "llm_image_transformation.py"