import os
import yaml
import glob
import argparse
from pathlib import Path
from typing import List, Dict, Any
from llm_image_transformation import LLMImageTransformationProcessor
from marker.models import create_model_dict
from marker.converters.pdf import PdfConverter
from marker.util import classes_to_strings
from marker.output import text_from_rendered


def create_custom_converter(config=None) -> PdfConverter:
    """create a converter with custom processors"""
    
    # replace processors
    processors = []
    for proc_class in PdfConverter.default_processors:
        if proc_class.__name__ == 'LLMImageDescriptionProcessor':
            processors.append(LLMImageTransformationProcessor)
        else:
            processors.append(proc_class)
    
    processor_names = classes_to_strings(processors)
    
    return PdfConverter(
        artifact_dict=create_model_dict(),
        processor_list=processor_names,
        llm_service=config.get("llm_service", None),
        config=config.get("other_config", {})
    )


def load_config(config_path: str) -> Dict[str, Any]:
    """Load YAML configuration file"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"Config file {config_path} do not exist")
    except yaml.YAMLError as e:
        raise ValueError(f"Config file has format error: {e}")


def get_pdf_files(input_config: Dict[str, Any]) -> List[str]:
    """根据配置获取需要处理的PDF文件列表"""
    path = input_config.get('path', '')
    
    # 转换为Path对象以便处理
    input_path = Path(path)
    
    # 如果路径不存在，抛出异常
    if not input_path.exists():
        raise FileNotFoundError(f"Input path {path} do not exist")
    
    pdf_files = []
    
    if input_path.is_file():
        # 单个文件
        if input_path.suffix.lower() == '.pdf':
            pdf_files.append(str(input_path))
        else:
            raise ValueError(f"Input file {path} is not PDF file")
    
    elif input_path.is_dir():
        # 文件夹
        file_filters = input_config.get('file_filters', {})
        extensions = file_filters.get('extensions', ['.pdf'])
        recursive = file_filters.get('recursive', True)
        exclude_patterns = file_filters.get('exclude_patterns', [])
        
        # 构建搜索模式
        if recursive:
            search_patterns = [str(input_path / "**" / f"*{ext}") for ext in extensions]
        else:
            search_patterns = [str(input_path / f"*{ext}") for ext in extensions]
        
        # 搜索文件
        for pattern in search_patterns:
            found_files = glob.glob(pattern, recursive=recursive)
            pdf_files.extend(found_files)
        
        # 排除匹配排除模式的文件
        if exclude_patterns:
            filtered_files = []
            for file_path in pdf_files:
                file_name = os.path.basename(file_path)
                exclude = False
                for exclude_pattern in exclude_patterns:
                    if exclude_pattern.replace('*', '') in file_name:
                        exclude = True
                        break
                if not exclude:
                    filtered_files.append(file_path)
            pdf_files = filtered_files
        
        # 去重并排序
        pdf_files = sorted(list(set(pdf_files)))
    
    if not pdf_files:
        raise ValueError(f"Do not find PDF in directory {path}")
    
    return pdf_files


def process_single_pdf(pdf_path: str, converter, output_config: Dict[str, Any]) -> str:
    """处理单个PDF文件"""
    print(f"⌛️ Start processing: {pdf_path}")
    
    try:
        rendered = converter(pdf_path)
        text, _, images = text_from_rendered(rendered)
        
        # 如果你需要document对象，可以使用下面这行（可选）
        # document = converter.build_document(rendered)
        
        print(f"✅ Transformation Done: {pdf_path}")
        
        # 构建输出文件路径
        base_dir = Path(output_config.get('base_dir', 'output'))
        file_format = output_config.get('format', 'md')
        create_subdirs = output_config.get('create_subdirs', True)
        
        # 确保输出目录存在
        base_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成输出文件名
        pdf_name = Path(pdf_path).stem
        if create_subdirs:
            output_file = base_dir / pdf_name / f"{pdf_name}.{file_format}"
            output_file.parent.mkdir(parents=True, exist_ok=True)
        else:
            output_file = base_dir / f"{pdf_name}.{file_format}"
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(text)
        
        print(f"💾 result has saved to: {output_file}")
        return str(output_file)
        
    except Exception as e:
        print(f"❌ Process failed: {pdf_path}, Error: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(description='PDF转换工具')
    parser.add_argument('--config', '-c', default='config.yaml', 
                       help='配置文件路径 (默认: config.yaml)')
    args = parser.parse_args()
    
    # 加载配置
    try:
        config = load_config(args.config)
        print(f"✅ Config Load Success: {args.config}")
    except Exception as e:
        print(f"❌ Config Load Failed: {e}")
        return 1
    
    # 获取API密钥
    api_config = config.get('api', {})
    api_key_env = api_config.get('api_key_env', 'GEMINI_API_KEY')
    api_key = os.getenv(api_key_env)
    
    if not api_key:
        print(f"❌ Env {api_key_env} did not set")
        return 1
    
    # 构建处理配置
    processing_config = config.get('processing_config', {})
    processing_config['other_config'][api_key_env.lower()] = api_key
    
    # 获取需要处理的PDF文件
    try:
        input_config = config.get('input', {})
        pdf_files = get_pdf_files(input_config)
        print(f"📄 Find {len(pdf_files)} PDFs")
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"  {i}. {pdf_file}")
    except Exception as e:
        print(f"❌ Failed to get PDF: {e}")
        return 1
    
    # 创建转换器
    try:
        converter = create_custom_converter(processing_config)
        print("✅ Converter created successfully")
    except Exception as e:
        print(f"❌ Converter created failed: {e}")
        return 1
    
    # 处理所有PDF文件
    output_config = config.get('output', {})
    success_count = 0
    failed_files = []
    
    for pdf_file in pdf_files:
        try:
            process_single_pdf(pdf_file, converter, output_config)
            success_count += 1
        except Exception as e:
            failed_files.append((pdf_file, str(e)))
            continue
    
    # 输出处理结果统计
    print(f"\n📊 处理完成统计:")
    print(f"  ✅ 成功: {success_count}")
    print(f"  ❌ 失败: {len(failed_files)}")
    
    if failed_files:
        print(f"\n失败的文件:")
        for file_path, error in failed_files:
            print(f"  - {file_path}: {error}")
    
    return 0 if len(failed_files) == 0 else 1


if __name__ == "__main__":
    exit(main())

