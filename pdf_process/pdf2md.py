# Enhanced PDF to Markdown converter with batch processing and retry mechanisms
# Supports processing 30+ PDFs with checkpoint resume capability

import os
import json
import time
import gc
import multiprocessing
from datetime import datetime
from pathlib import Path
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered


INPUT_DIR = './data'
OUTPUT_DIR = './output'
CHECKPOINT_FILE = './pdf_conversion_checkpoint.json'


class PDFBatchProcessor:
    """Enhanced PDF batch processor with retry and checkpoint capabilities."""
    
    def __init__(self, input_dir=INPUT_DIR, output_dir=OUTPUT_DIR, checkpoint_file=CHECKPOINT_FILE):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.checkpoint_file = Path(checkpoint_file)
        self.converter = None
        
    def get_file_list(self):
        """Get list of PDF files to process."""
        file_list = []
        if not self.input_dir.exists():
            raise FileNotFoundError(f"Input directory '{self.input_dir}' does not exist.")
        
        for root, _, files in os.walk(self.input_dir):
            for file in files:
                if file.lower().endswith('.pdf'):
                    file_list.append(Path(root) / file)
        
        if not file_list:
            raise ValueError("No PDF files found in the input directory.")
        
        return sorted(file_list)  # Sort for consistent processing order
    
    def load_checkpoint(self):
        """Load processing checkpoint."""
        if self.checkpoint_file.exists():
            with open(self.checkpoint_file, 'r') as f:
                return json.load(f)
        return {
            "processed_files": [],
            "failed_files": [],
            "last_update": None,
            "total_files": 0,
            "start_time": None
        }
    
    def save_checkpoint(self, checkpoint_data):
        """Save processing checkpoint."""
        checkpoint_data["last_update"] = datetime.now().isoformat()
        with open(self.checkpoint_file, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
    
    def initialize_converter(self):
        """Initialize the PDF converter (lazy initialization)."""
        if self.converter is None:
            print("🔧 Initializing PDF converter...")
            self.converter = PdfConverter(artifact_dict=create_model_dict())
            print("✅ PDF converter initialized")


    def pdf_to_md(self, file_path, max_retries=3, page_range=None):
        """Convert PDF to Markdown with retry mechanism and optional page range."""
        self.initialize_converter()
        
        for attempt in range(max_retries):
            try:
                if page_range:
                    print(f"📄 Converting {file_path.name} pages {page_range} (attempt {attempt + 1}/{max_retries})...")
                else:
                    print(f"📄 Converting {file_path.name} (attempt {attempt + 1}/{max_retries})...")
                
                # Use page_range if provided
                if page_range:
                    rendered = self.converter(str(file_path), page_range=page_range)
                else:
                    rendered = self.converter(str(file_path))
                
                text, _, images = text_from_rendered(rendered)
                return text, images
                
            except Exception as e:
                error_msg = str(e)
                print(f"⚠️ Attempt {attempt + 1} failed: {error_msg}")
                
                # Handle system crashes specially
                if "Abort trap" in error_msg or "objc" in error_msg or "SIGABRT" in error_msg:
                    print(f"🚨 System-level crash detected on {file_path.name}")
                    if page_range:
                        print(f"💥 Crash occurred in page range: {page_range}")
                    break  # Don't retry system crashes
                
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # Exponential backoff
                    print(f"⏳ Waiting {wait_time}s before retry...")
                    time.sleep(wait_time)
                else:
                    raise e
    
    def get_pdf_page_count(self, file_path):
        """Get the total number of pages in a PDF."""
        try:
            import pypdfium2 as pdfium
            pdf = pdfium.PdfDocument(str(file_path))
            page_count = len(pdf)
            pdf.close()
            return page_count
        except Exception as e:
            print(f"⚠️ Could not determine page count for {file_path.name}: {e}")
            return None
    
    def process_pdf_in_chunks(self, file_path, chunk_size=500):
        """Process PDF in chunks to handle large files and crashes."""
        page_count = self.get_pdf_page_count(file_path)
        if not page_count:
            # Fallback to full processing if we can't get page count
            return self.pdf_to_md(file_path)
        
        print(f"📖 PDF has {page_count} pages, processing in chunks of {chunk_size}")
        
        all_text_chunks = []
        successful_ranges = []
        failed_ranges = []
        
        # Process in chunks
        for start_page in range(0, page_count, chunk_size):
            end_page = min(start_page + chunk_size - 1, page_count - 1)
            page_range = f"{start_page}-{end_page}"
            
            try:
                print(f"📄 Processing chunk: pages {page_range}")
                text, images = self.pdf_to_md(file_path, page_range=page_range)
                all_text_chunks.append(f"\n\n<!-- Pages {page_range} -->\n\n{text}")
                successful_ranges.append(page_range)
                
                # Memory cleanup after each chunk
                gc.collect()
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ Chunk {page_range} failed: {error_msg}")
                failed_ranges.append(page_range)
                
                # Try smaller chunks if this chunk failed
                if chunk_size > 50:
                    print(f"🔄 Retrying {page_range} with smaller chunks...")
                    smaller_chunks = self.process_chunk_recursively(file_path, start_page, end_page, chunk_size // 2)
                    all_text_chunks.extend(smaller_chunks)
                else:
                    # Add placeholder for failed chunk
                    all_text_chunks.append(f"\n\n<!-- Pages {page_range} - FAILED TO PROCESS -->\n\n")
        
        # Combine all successful chunks
        combined_text = "\n".join(all_text_chunks)
        
        print(f"📊 Processing Summary:")
        print(f"  ✅ Successful ranges: {successful_ranges}")
        if failed_ranges:
            print(f"  ❌ Failed ranges: {failed_ranges}")
        
        return combined_text, []  # Return empty images list for now
    
    def process_chunk_recursively(self, file_path, start_page, end_page, chunk_size):
        """Recursively process a failed chunk with smaller size."""
        chunks = []
        
        for page in range(start_page, end_page + 1, chunk_size):
            chunk_end = min(page + chunk_size - 1, end_page)
            page_range = f"{page}-{chunk_end}"
            
            try:
                print(f"🔄 Retry processing: pages {page_range}")
                text, images = self.pdf_to_md(file_path, page_range=page_range)
                chunks.append(f"\n\n<!-- Pages {page_range} -->\n\n{text}")
                
            except Exception as e:
                print(f"❌ Even smaller chunk {page_range} failed: {str(e)}")
                chunks.append(f"\n\n<!-- Pages {page_range} - FAILED TO PROCESS -->\n\n")
        
        return chunks

    def process_single_file(self, file_path, checkpoint_data):
        """Process a single PDF file with enhanced error handling and smart skip logic."""
        try:
            # Smart skip: Check if output .md file already exists and is valid
            output_file = self.output_dir / f"{file_path.stem}.md"
            if output_file.exists() and output_file.stat().st_size > 100:  # At least 100 bytes
                print(f"✅ {file_path.name} already converted to {output_file.name} - skipping")
                # Update checkpoint to reflect existing file
                if str(file_path) not in checkpoint_data["processed_files"]:
                    checkpoint_data["processed_files"].append(str(file_path))
                if str(file_path) in checkpoint_data["failed_files"]:
                    checkpoint_data["failed_files"].remove(str(file_path))
                return True
            
            # Double-check checkpoint for redundancy
            if str(file_path) in checkpoint_data["processed_files"]:
                print(f"✅ {file_path.name} in checkpoint as processed - skipping")
                return True
            
            print(f"🔄 Processing {file_path.name}...")
            
            # Try full processing first
            try:
                text, images = self.pdf_to_md(file_path)
                print(f"✅ Full processing successful")
                
            except Exception as e:
                error_msg = str(e)
                print(f"⚠️ Full processing failed: {error_msg}")
                
                # If full processing fails, try chunk processing
                if "Abort trap" in error_msg or "objc" in error_msg or "SIGABRT" in error_msg:
                    print(f"🚨 System crash detected, switching to chunk processing...")
                    text, images = self.process_pdf_in_chunks(file_path)
                else:
                    raise e  # Re-raise non-crash errors
            
            # Save markdown file
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(text)
            
            # Update checkpoint
            checkpoint_data["processed_files"].append(str(file_path))
            if str(file_path) in checkpoint_data["failed_files"]:
                checkpoint_data["failed_files"].remove(str(file_path))
            
            # Memory cleanup to prevent accumulation
            gc.collect()
            
            print(f"✅ Converted {file_path.name} → {output_file.name}")
            return True
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ Failed to convert {file_path.name}: {error_msg}")
            
            # Handle specific crash types
            if "Abort trap" in error_msg or "objc" in error_msg:
                print(f"⚠️  System-level crash detected on {file_path.name}")
                print(f"💡 This file may need special handling or alternative processing")
            
            if str(file_path) not in checkpoint_data["failed_files"]:
                checkpoint_data["failed_files"].append(str(file_path))
            
            # Force garbage collection after failures
            gc.collect()
            return False
    
    def process_batch(self):
        """Process all PDF files in batch with checkpoint resume."""
        print("🚀 Enhanced PDF Batch Processor")
        print("=" * 50)
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        
        # Get file list and load checkpoint
        file_list = self.get_file_list()
        checkpoint_data = self.load_checkpoint()
        
        # Initialize checkpoint if needed
        if not checkpoint_data.get("start_time"):
            checkpoint_data["start_time"] = datetime.now().isoformat()
            checkpoint_data["total_files"] = len(file_list)
        
        print(f"📊 Found {len(file_list)} PDF files to process")
        print(f"📋 Already processed: {len(checkpoint_data['processed_files'])}")
        print(f"❌ Previous failures: {len(checkpoint_data['failed_files'])}")
        print()
        
        # Process files
        start_time = time.time()
        processed_count = 0
        
        for i, file_path in enumerate(file_list, 1):
            print(f"[{i}/{len(file_list)}] Processing {file_path.name}...")
            
            success = self.process_single_file(file_path, checkpoint_data)
            if success:
                processed_count += 1
            
            # Save checkpoint after each file
            self.save_checkpoint(checkpoint_data)
            
            # Progress update
            elapsed = time.time() - start_time
            if i > 0:
                avg_time = elapsed / i
                remaining = (len(file_list) - i) * avg_time
                print(f"⏱️  Progress: {i}/{len(file_list)} ({i/len(file_list)*100:.1f}%) - ETA: {remaining/60:.1f}min")
            print()
        
        # Final summary
        total_time = time.time() - start_time
        print("🎉 Batch Processing Complete!")
        print("=" * 50)
        print(f"📊 Total files: {len(file_list)}")
        print(f"✅ Successfully processed: {len(checkpoint_data['processed_files'])}")
        print(f"❌ Failed files: {len(checkpoint_data['failed_files'])}")
        print(f"⏱️  Total time: {total_time/60:.1f} minutes")
        
        if checkpoint_data["failed_files"]:
            print(f"\n⚠️  Failed files:")
            for failed_file in checkpoint_data["failed_files"]:
                print(f"   - {Path(failed_file).name}")
            print(f"💡 Run the script again to retry failed files")
        
        return len(checkpoint_data["failed_files"]) == 0

def main():
    """Main function with enhanced batch processing."""
    processor = PDFBatchProcessor()
    
    try:
        success = processor.process_batch()
        if success:
            print("\n🎯 All PDFs processed successfully!")
            print("Next step: Convert markdown files to JSON for knowledge graph extraction")
        else:
            print("\n⚠️  Some files failed to process. Check the logs above and retry.")
            
    except (FileNotFoundError, ValueError) as e:
        print(f"❌ Error: {e}")
    except KeyboardInterrupt:
        print("\n⏹️  Processing interrupted by user")
        print("💾 Progress saved in checkpoint - run again to resume")
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")

if __name__ == "__main__":
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    main()