#!/usr/bin/env python3
"""
Dedicated PDF processor for problematic files that crash during normal processing.
Processes PDFs page by page, skips crash-causing pages, combines successful results.

Usage: python process_problematic_pdf.py "path/to/problematic.pdf"
"""

import os
import sys
import time
import gc
import signal
import subprocess
from pathlib import Path
from datetime import datetime
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered

class ProblematicPDFProcessor:
    """Dedicated processor for PDFs that crash during normal processing."""
    
    def __init__(self, input_dir='./data', output_dir='./output'):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.converter = None
        self.output_dir.mkdir(exist_ok=True)
        
    def initialize_converter(self):
        """Initialize the PDF converter (lazy initialization)."""
        if self.converter is None:
            print("🔧 Initializing PDF converter for page-by-page processing...")
            self.converter = PdfConverter(artifact_dict=create_model_dict())
            print("✅ PDF converter initialized")
    
    def get_pdf_page_count(self, file_path):
        """Get the total number of pages in a PDF."""
        try:
            import pypdfium2 as pdfium
            pdf = pdfium.PdfDocument(str(file_path))
            page_count = len(pdf)
            pdf.close()
            return page_count
        except Exception as e:
            print(f"⚠️ Could not determine page count for {file_path.name}: {e}")
            return None
    
    def extract_single_page_pdf(self, file_path, page_num, temp_dir):
        """Extract a single page to a temporary PDF file."""
        try:
            import pypdfium2 as pdfium
            
            # Open source PDF
            source_pdf = pdfium.PdfDocument(str(file_path))
            
            # Create new PDF with just one page
            temp_pdf_path = temp_dir / f"page_{page_num}.pdf"
            
            # Extract the single page (pypdfium2 uses 0-based indexing)
            page = source_pdf[page_num]
            
            # Create new document with this page
            new_pdf = pdfium.PdfDocument.new()
            new_pdf.import_pages(source_pdf, [page_num])
            
            # Save temporary single-page PDF
            new_pdf.save(str(temp_pdf_path))
            
            # Clean up
            new_pdf.close()
            source_pdf.close()
            
            return temp_pdf_path
            
        except Exception as e:
            print(f"⚠️ Failed to extract page {page_num}: {e}")
            return None

    def process_single_page_safe(self, file_path, page_num, temp_dir):
        """
        Process a single page with crash protection.
        Returns (success, text, error_message)
        """
        temp_pdf_path = None
        try:
            print(f"📄 Processing page {page_num}...")
            
            # Extract single page to temporary PDF
            temp_pdf_path = self.extract_single_page_pdf(file_path, page_num, temp_dir)
            if not temp_pdf_path:
                return False, None, "Failed to extract page"
            
            # Process the single-page PDF
            rendered = self.converter(str(temp_pdf_path))
            text, _, images = text_from_rendered(rendered)
            
            # Clean up memory immediately
            gc.collect()
            
            print(f"✅ Page {page_num} processed successfully")
            return True, text, None
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ Page {page_num} failed: {error_msg}")
            
            # Check for system-level crashes
            if any(crash_indicator in error_msg.lower() for crash_indicator in 
                   ['abort', 'segmentation', 'sigabrt', 'sigsegv', 'objc']):
                print(f"💥 System crash detected on page {page_num}")
            
            # Force cleanup after error
            gc.collect()
            
            return False, None, error_msg
            
        finally:
            # Clean up temporary file
            if temp_pdf_path and temp_pdf_path.exists():
                try:
                    temp_pdf_path.unlink()
                except:
                    pass
    
    def process_pdf_page_by_page(self, file_path):
        """
        Process PDF page by page, skipping problematic pages.
        Returns combined markdown text and processing summary.
        """
        print(f"🚀 Processing {file_path.name} page by page...")
        print("=" * 60)
        
        # Initialize converter
        self.initialize_converter()
        
        # Get page count
        page_count = self.get_pdf_page_count(file_path)
        if not page_count:
            raise ValueError(f"Cannot determine page count for {file_path.name}")
        
        print(f"📖 PDF has {page_count} pages")
        print()
        
        # Create temporary directory for single-page PDFs
        import tempfile
        temp_dir = Path(tempfile.mkdtemp(prefix="pdf_pages_"))
        
        try:
            # Process each page individually
            successful_pages = []
            failed_pages = []
            all_text_parts = []
            
            start_time = time.time()
            
            for page_num in range(page_count):
                print(f"[{page_num + 1}/{page_count}] Processing page {page_num}...")
                
                success, text, error = self.process_single_page_safe(file_path, page_num, temp_dir)
                
                if success:
                    successful_pages.append(page_num)
                    # Add page marker and content
                    page_text = f"\n\n<!-- PAGE {page_num} -->\n\n{text}\n"
                    all_text_parts.append(page_text)
                    
                else:
                    failed_pages.append(page_num)
                    # Add failure marker
                    failure_text = f"\n\n<!-- PAGE {page_num} - FAILED TO PROCESS -->\n"
                    failure_text += f"<!-- Error: {error} -->\n\n"
                    all_text_parts.append(failure_text)
                
                # Progress update
                elapsed = time.time() - start_time
                avg_time_per_page = elapsed / (page_num + 1)
                remaining_pages = page_count - (page_num + 1)
                eta_minutes = (remaining_pages * avg_time_per_page) / 60
                
                print(f"⏱️  Progress: {page_num + 1}/{page_count} ({((page_num + 1)/page_count)*100:.1f}%) - ETA: {eta_minutes:.1f}min")
                print()
            
            # Combine all parts
            combined_text = "".join(all_text_parts)
            
            # Processing summary
            total_time = time.time() - start_time
            success_rate = (len(successful_pages) / page_count) * 100
            
            summary = {
                'total_pages': page_count,
                'successful_pages': successful_pages,
                'failed_pages': failed_pages,
                'success_rate': success_rate,
                'processing_time': total_time
            }
            
            print("🎉 Page-by-page processing completed!")
            print("=" * 60)
            print(f"📊 Processing Summary:")
            print(f"  📖 Total pages: {page_count}")
            print(f"  ✅ Successful pages: {len(successful_pages)} ({success_rate:.1f}%)")
            print(f"  ❌ Failed pages: {len(failed_pages)}")
            print(f"  ⏱️  Total time: {total_time/60:.1f} minutes")
            
            if failed_pages:
                print(f"\n⚠️  Failed pages: {failed_pages}")
                print("💡 These pages contained content that caused system crashes")
            
            return combined_text, summary
            
        finally:
            # Clean up temporary directory
            import shutil
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
    
    def process_file(self, pdf_file):
        """Process a single problematic PDF file."""
        if isinstance(pdf_file, str):
            pdf_file = Path(pdf_file)
        
        if not pdf_file.exists():
            # Try looking in input directory
            pdf_file = self.input_dir / pdf_file.name
            if not pdf_file.exists():
                raise FileNotFoundError(f"PDF file not found: {pdf_file}")
        
        print(f"🎯 Processing problematic PDF: {pdf_file.name}")
        print(f"📁 Input: {pdf_file}")
        
        # Output file
        output_file = self.output_dir / f"{pdf_file.stem}.md"
        print(f"📁 Output: {output_file}")
        print()
        
        # Process page by page
        try:
            combined_text, summary = self.process_pdf_page_by_page(pdf_file)
            
            # Save combined markdown
            with open(output_file, 'w', encoding='utf-8') as f:
                # Add header with processing info
                header = f"# {pdf_file.stem}\n\n"
                header += f"*Processed page-by-page on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
                header += f"*Success rate: {summary['success_rate']:.1f}% ({len(summary['successful_pages'])}/{summary['total_pages']} pages)*\n\n"
                
                if summary['failed_pages']:
                    header += f"**Note**: Pages {summary['failed_pages']} failed to process due to system crashes.\n\n"
                
                header += "---\n\n"
                
                f.write(header + combined_text)
            
            print(f"\n✅ Successfully saved: {output_file}")
            print(f"📊 Extracted {summary['success_rate']:.1f}% of content")
            
            # Save processing log
            log_file = self.output_dir / f"{pdf_file.stem}_processing_log.txt"
            with open(log_file, 'w') as f:
                f.write(f"Processing Log for {pdf_file.name}\n")
                f.write("=" * 50 + "\n")
                f.write(f"Processed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total pages: {summary['total_pages']}\n")
                f.write(f"Successful pages: {summary['successful_pages']}\n")
                f.write(f"Failed pages: {summary['failed_pages']}\n")
                f.write(f"Success rate: {summary['success_rate']:.1f}%\n")
                f.write(f"Processing time: {summary['processing_time']/60:.1f} minutes\n")
            
            return True, summary
            
        except Exception as e:
            print(f"💥 Fatal error processing {pdf_file.name}: {str(e)}")
            return False, None

def main():
    """Main function with command line interface."""
    if len(sys.argv) != 2:
        print("Usage: python process_problematic_pdf.py <pdf_file>")
        print("Example: python process_problematic_pdf.py 'Business Configuration Tool BRIDGE.pdf'")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    
    print("🚨 Problematic PDF Processor")
    print("Designed for PDFs that crash during normal processing")
    print("=" * 60)
    
    processor = ProblematicPDFProcessor()
    
    try:
        success, summary = processor.process_file(pdf_file)
        
        if success:
            print("\n🎯 Mission accomplished!")
            print("The problematic PDF has been processed page-by-page.")
            print("Check the output directory for the markdown file and processing log.")
        else:
            print("\n💥 Processing failed.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Processing interrupted by user")
    except Exception as e:
        print(f"\n💥 Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()