#!/usr/bin/env python3
"""
Verify the 360t_guide_direct_api_v2 import in Neo4j Desktop autoschemakg database.
"""

from neo4j import GraphDatabase
import sys

def verify_import():
    """
    Verify the import results in the autoschemakg database.
    """
    print("🔍 Verifying 360t_guide_direct_api_v2 Import")
    print("=" * 60)
    print("Database: autoschemakg")
    print("Connection: neo4j://127.0.0.1:7687")
    print()
    
    try:
        # Connect to Neo4j Desktop instance
        driver = GraphDatabase.driver(
            "neo4j://127.0.0.1:7687",
            auth=("neo4j", "1979@rabu"),
            database="autoschemakg"
        )
        
        print("✅ Successfully connected to autoschemakg database")
        
        with driver.session() as session:
            # Verify node counts
            print("\n📊 Node Counts:")
            
            # Total nodes
            result = session.run("MATCH (n) RETURN count(n) as total")
            total_nodes = result.single()["total"]
            print(f"   Total nodes: {total_nodes:,}")
            
            # Concept nodes
            result = session.run("MATCH (n:Concept) RETURN count(n) as count")
            concept_nodes = result.single()["count"]
            print(f"   Concept nodes: {concept_nodes:,}")
            
            # Regular nodes
            result = session.run("MATCH (n:Node) RETURN count(n) as count")
            regular_nodes = result.single()["count"]
            print(f"   Regular nodes: {regular_nodes:,}")
            
            # Text nodes
            result = session.run("MATCH (n:Text) RETURN count(n) as count")
            text_nodes = result.single()["count"]
            print(f"   Text nodes: {text_nodes:,}")
            
            # Verify relationship counts
            print("\n📊 Relationship Counts:")
            
            # Total relationships
            result = session.run("MATCH ()-[r]->() RETURN count(r) as total")
            total_rels = result.single()["total"]
            print(f"   Total relationships: {total_rels:,}")
            
            # Concept relationships
            result = session.run("MATCH ()-[r:Concept]->() RETURN count(r) as count")
            concept_rels = result.single()["count"]
            print(f"   Concept relationships: {concept_rels:,}")
            
            # Relation relationships
            result = session.run("MATCH ()-[r:Relation]->() RETURN count(r) as count")
            relation_rels = result.single()["count"]
            print(f"   Relation relationships: {relation_rels:,}")
            
            # Validation
            print("\n🎯 Validation Results:")
            
            expected = {
                'total_nodes': 1090,
                'concept_nodes': 599,
                'regular_nodes': 479,
                'text_nodes': 12,
                'total_relationships': 2901,
                'concept_relationships': 2364,
                'relation_relationships': 537
            }
            
            actual = {
                'total_nodes': total_nodes,
                'concept_nodes': concept_nodes,
                'regular_nodes': regular_nodes,
                'text_nodes': text_nodes,
                'total_relationships': total_rels,
                'concept_relationships': concept_rels,
                'relation_relationships': relation_rels
            }
            
            all_correct = True
            for key, expected_val in expected.items():
                actual_val = actual[key]
                if actual_val == expected_val:
                    print(f"   ✅ {key}: {actual_val:,} (matches expected)")
                else:
                    print(f"   ❌ {key}: {actual_val:,} (expected {expected_val:,})")
                    all_correct = False
            
            # Sample data verification
            print("\n🔬 Sample Data:")
            
            # Sample concept nodes
            result = session.run("MATCH (n:Concept) RETURN n.name as name LIMIT 3")
            concept_samples = [record["name"] for record in result if record["name"]]
            if concept_samples:
                print("   Sample Concept nodes:")
                for i, name in enumerate(concept_samples, 1):
                    print(f"      {i}. {name}")
            
            # Sample text nodes
            result = session.run("MATCH (n:Text) RETURN substring(n.content, 0, 100) as content LIMIT 2")
            text_samples = [record["content"] for record in result if record["content"]]
            if text_samples:
                print("   Sample Text nodes:")
                for i, content in enumerate(text_samples, 1):
                    print(f"      {i}. {content}...")
            
            # Final summary
            print("\n" + "=" * 60)
            if all_correct:
                print("🎉 IMPORT VERIFICATION SUCCESSFUL!")
                print("✅ All data counts match expected values")
                print("✅ The 360t_guide_direct_api_v2 knowledge graph is ready for use")
            else:
                print("❌ IMPORT VERIFICATION FAILED!")
                print("Some data counts don't match expected values")
                return False
            
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Error connecting to Neo4j: {e}")
        print("   Make sure:")
        print("   - Neo4j Desktop instance is running")
        print("   - autoschemakg database exists")
        print("   - Credentials are correct (neo4j/1979@rabu)")
        return False

def main():
    """
    Main verification function.
    """
    success = verify_import()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
