Neo4j Import Commands for AutoSchemaKG (Enhanced Robust Pipeline)
============================================================

IMPORTANT: Stop your autoschemakg database before running this command\!

Command to run in your Neo4j installation directory:
--------------------------------------------------
neo4j-admin database import full autoschemakg \
    --nodes ./import/pdf_dataset/triples_csv/text_nodes__from_json.csv \
    --nodes ./import/pdf_dataset/triples_csv/triple_nodes__from_json_without_emb.csv \
    --relationships ./import/pdf_dataset/triples_csv/triple_edges__from_json_without_emb.csv \
    --relationships ./import/pdf_dataset/triples_csv/text_edges__from_json.csv \
    --overwrite-destination \
    --multiline-fields=true \
    --id-type=string \
    --verbose \
    --skip-bad-relationships=true

After import completes, start your autoschemakg database again.

Generated by Enhanced Robust PDF KG Extraction Pipeline
Timestamp: 2025-08-02 08:06:21
