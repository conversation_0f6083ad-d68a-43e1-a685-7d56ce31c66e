2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 📝 Logging setup complete - Logs: pipeline_log_pdf_dataset_20250802_102027.log, Errors: pipeline_errors_pdf_dataset_20250802_102027.log
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 🚀 Progress tracking initialized for dataset: pdf_dataset
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 📊 Total files to process: 0
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 🛡️ <PERSON><PERSON>rHandler initialized with robust recovery strategies
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 🛡️ RobustKGExtractor initialized with enhanced error handling and resumability
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 🎯 Starting Stage: concept_generation
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 📋 Description: Generating concepts from triples
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | ============================================================
2025-08-02 10:20:27 | INFO     | pipeline_pdf_dataset | 🧠 Using batch size: 16
2025-08-02 10:20:27 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: api_error
2025-08-02 10:20:27 | ERROR    | pipeline_pdf_dataset |    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
2025-08-02 10:20:27 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:20:27 | WARNING  | pipeline_pdf_dataset | 🔄 Retrying concept_generation in 1.1s (attempt 1/3)
2025-08-02 10:20:28 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: api_error
2025-08-02 10:20:28 | ERROR    | pipeline_pdf_dataset |    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
2025-08-02 10:20:28 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:20:28 | WARNING  | pipeline_pdf_dataset | 🔄 Retrying concept_generation in 2.9s (attempt 2/3)
2025-08-02 10:20:31 | ERROR    | pipeline_pdf_dataset | 🚨 Error recorded: api_error
2025-08-02 10:20:31 | ERROR    | pipeline_pdf_dataset |    Message: Ollama transport error: 400 Client Error: Bad Request for url: http://localhost:11434/api/generate
2025-08-02 10:20:31 | ERROR    | pipeline_pdf_dataset |    Context: {'stage': 'concept_generation', 'batch_size': 16, 'operation': 'concept_generation'}
2025-08-02 10:20:31 | WARNING  | pipeline_pdf_dataset | 🔄 Retrying concept_generation in 7.4s (attempt 3/3)
