"""
Test Script for Robust PDF Knowledge Graph Extraction Pipeline

This script validates the enhanced pipeline functionality including:
- State management and resumability
- Error handling and recovery
- Progress tracking
- Component integration
"""

import os
import sys
import tempfile
import json
import time
from pathlib import Path

# Import our enhanced components
from pipeline_state_manager import PipelineStateManager, PipelineState
from progress_tracker import ProgressTracker
from error_handler import ErrorHandler, ErrorConfig, ErrorCategory
from robust_kg_extractor import RobustKGExtractor


def create_test_data():
    """Create sample test data for pipeline testing."""
    test_dir = tempfile.mkdtemp(prefix="robust_pipeline_test_")
    
    # Create example_data directory
    example_data_dir = os.path.join(test_dir, "example_data")
    os.makedirs(example_data_dir, exist_ok=True)
    
    # Create sample JSON files
    sample_documents = [
        {
            "id": "doc1",
            "metadata": {"lang": "en", "source": "test"},
            "text": "Apple Inc. is a technology company founded by <PERSON>. The company develops innovative products like the iPhone and iPad."
        },
        {
            "id": "doc2", 
            "metadata": {"lang": "en", "source": "test"},
            "text": "Google LLC is a search engine company. Larry Page and Sergey Brin founded Google at Stanford University."
        },
        {
            "id": "doc3",
            "metadata": {"lang": "en", "source": "test"},
            "text": "Microsoft Corporation was founded by Bill Gates and Paul Allen. The company is known for Windows operating system and Office suite."
        }
    ]
    
    for i, doc in enumerate(sample_documents, 1):
        filename = f"test_doc_{i}.json"
        filepath = os.path.join(example_data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(doc, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Created test data in: {test_dir}")
    return test_dir


def test_pipeline_state_manager():
    """Test PipelineStateManager functionality."""
    print("\n🧪 Testing PipelineStateManager...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create state manager
        state_manager = PipelineStateManager("test_dataset")
        state_manager.state_file = os.path.join(temp_dir, "test_state.json")
        
        # Test creating new state
        input_files = ["file1.json", "file2.json", "file3.json"]
        config = {"batch_size": 16, "max_tokens": 8192}
        
        state = state_manager.create_new_state(input_files, config)
        assert state.dataset_name == "test_dataset"
        assert state.total_files == 3
        assert len(state.files) == 3
        assert len(state.stages) > 0
        
        # Test saving and loading state
        state_manager.save_state(force=True)
        assert os.path.exists(state_manager.state_file)
        
        # Create new manager and load state
        new_state_manager = PipelineStateManager("test_dataset")
        new_state_manager.state_file = state_manager.state_file
        loaded_state = new_state_manager.load_state()
        
        assert loaded_state is not None
        assert loaded_state.dataset_name == "test_dataset"
        assert loaded_state.total_files == 3
        
        # Test stage operations
        state_manager.start_stage("triple_extraction")
        assert state_manager.state.stages["triple_extraction"].status == "in_progress"
        
        state_manager.complete_stage("triple_extraction")
        assert state_manager.state.stages["triple_extraction"].status == "completed"
        
        # Test file operations
        state_manager.start_file_processing("file1.json", "triple_extraction")
        assert state_manager.state.files["file1.json"].status == "processing"
        
        state_manager.complete_file_processing("file1.json", "triple_extraction", 10, "doc_10")
        file_status = state_manager.state.files["file1.json"]
        assert "triple_extraction" in file_status.stages_completed
        assert file_status.document_count == 10
        
        print("✅ PipelineStateManager tests passed")


def test_progress_tracker():
    """Test ProgressTracker functionality."""
    print("\n🧪 Testing ProgressTracker...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        os.chdir(temp_dir)  # Change to temp dir for log files
        
        # Create progress tracker
        tracker = ProgressTracker("test_dataset", total_files=3, log_level="INFO")
        
        # Test stage operations
        tracker.start_stage("triple_extraction", "Testing triple extraction")
        
        # Test file processing
        tracker.start_file_processing("file1.json", total_documents=10)
        
        # Simulate processing progress
        for i in range(1, 11):
            tracker.update_file_progress("file1.json", i, f"document_{i}")
            time.sleep(0.1)  # Small delay to simulate processing
        
        tracker.complete_file_processing("file1.json", 10)
        tracker.complete_stage("triple_extraction")
        
        # Test summary generation
        summary = tracker.get_processing_summary()
        assert summary['dataset_name'] == "test_dataset"
        assert summary['total_files'] == 3
        assert summary['completed_files'] == 1
        
        print("✅ ProgressTracker tests passed")


def test_error_handler():
    """Test ErrorHandler functionality."""
    print("\n🧪 Testing ErrorHandler...")
    
    # Create error handler
    config = ErrorConfig(max_retries=2, base_delay=0.1)  # Fast for testing
    error_handler = ErrorHandler(config)
    
    # Test error categorization
    api_error = Exception("API rate limit exceeded")
    category = error_handler.categorize_error(api_error)
    assert category == ErrorCategory.API_RATE_LIMIT
    
    timeout_error = Exception("Request timed out")
    category = error_handler.categorize_error(timeout_error)
    assert category == ErrorCategory.API_TIMEOUT
    
    # Test error recording
    error_record = error_handler.record_error(api_error, {"operation": "test"})
    assert error_record.error_category == ErrorCategory.API_RATE_LIMIT
    assert "rate limit" in error_record.error_message.lower()
    
    # Test retry logic
    attempt_count = 0
    
    def failing_operation():
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            raise Exception("Temporary failure")
        return "success"
    
    try:
        result = error_handler.execute_with_retry(
            operation=failing_operation,
            context={"test": True},
            operation_name="test_operation"
        )
        assert result == "success"
        assert attempt_count == 3  # Should have retried twice
    except Exception:
        assert False, "Operation should have succeeded after retries"
    
    # Test circuit breaker
    def always_failing_operation():
        raise Exception("Always fails")
    
    try:
        # This should eventually trigger circuit breaker
        for i in range(10):
            try:
                error_handler.execute_with_retry(
                    operation=always_failing_operation,
                    context={"test": True},
                    operation_name="circuit_test"
                )
            except:
                pass  # Expected to fail
    except Exception:
        pass
    
    # Check if circuit is open
    assert "circuit_test" in error_handler.circuit_breakers
    
    print("✅ ErrorHandler tests passed")


def test_integration():
    """Test integration of all components."""
    print("\n🧪 Testing Component Integration...")
    
    # This is a simplified integration test since we can't easily test
    # the full pipeline without actual LLM/API setup
    
    # Test that components can be initialized together
    state_manager = PipelineStateManager("integration_test")
    progress_tracker = ProgressTracker("integration_test", 5)
    error_handler = ErrorHandler()
    
    # Test state creation and management
    input_files = [f"file_{i}.json" for i in range(5)]
    state = state_manager.create_new_state(input_files, {})
    
    # Test progress tracking with state updates
    for filename in input_files[:2]:  # Process first 2 files
        progress_tracker.start_file_processing(filename, 10)
        state_manager.start_file_processing(filename, "test_stage")
        
        progress_tracker.complete_file_processing(filename, 10)
        state_manager.complete_file_processing(filename, "test_stage", 10)
    
    # Verify state consistency
    assert state_manager.state.completed_files == 2
    assert progress_tracker.get_processing_summary()['completed_files'] == 2
    
    print("✅ Integration tests passed")


def run_basic_functionality_test():
    """Run basic functionality tests without requiring full pipeline setup."""
    print("🧪 Running Robust Pipeline Component Tests")
    print("=" * 50)
    
    try:
        test_pipeline_state_manager()
        test_progress_tracker() 
        test_error_handler()
        test_integration()
        
        print("\n🎉 All component tests passed successfully!")
        print("✅ Enhanced pipeline components are working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_pipeline_features():
    """Display the enhanced pipeline features."""
    print("\n🚀 Enhanced Pipeline Features")
    print("=" * 50)
    print("✅ Resume Functionality:")
    print("   - Automatic checkpoint creation every 5 minutes")
    print("   - Resume from any failure point")
    print("   - Detect and skip already processed documents")
    print()
    print("✅ Error Handling:")
    print("   - Automatic retry with exponential backoff")
    print("   - Circuit breaker pattern for persistent failures")
    print("   - Categorized error handling (API, network, parsing, etc.)")
    print("   - Optional email notifications for critical errors")
    print()
    print("✅ Progress Tracking:")
    print("   - Real-time progress bars for each file")
    print("   - ETA calculations based on processing history")
    print("   - Comprehensive logging (console + file)")
    print("   - Memory usage monitoring")
    print()
    print("✅ State Management:")
    print("   - Persistent state across restarts")
    print("   - Stage-by-stage completion tracking")
    print("   - File-level processing status")
    print("   - Processing statistics and summaries")
    print()
    print("🎯 Usage:")
    print("   python pdf_kg_extraction_pipeline_robust.py --help")


def main():
    """Main test runner."""
    print("🧪 Robust PDF KG Pipeline - Test Suite")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--features":
        show_pipeline_features()
        return
    
    print("This test suite validates the enhanced pipeline components:")
    print("- PipelineStateManager (checkpoint/resume)")
    print("- ProgressTracker (real-time monitoring)")
    print("- ErrorHandler (retry logic and recovery)")
    print("- Component integration")
    print()
    
    success = run_basic_functionality_test()
    
    if success:
        print("\n📋 Next Steps:")
        print("1. The enhanced pipeline is ready for use")
        print("2. Run: python pdf_kg_extraction_pipeline_robust.py")
        print("3. Use --help to see all available options")
        print("4. The pipeline will automatically resume from failures")
        
        show_pipeline_features()
        
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()