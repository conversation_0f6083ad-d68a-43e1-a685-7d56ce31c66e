#!/usr/bin/env python3
"""
Test script for ATLAS GraphML Pipeline
Tests the complete pipeline with a small subset of data to ensure everything works correctly.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from atlas_graphml_pipeline import ATLASGraphMLPipeline

def test_pipeline():
    """
    Test the complete ATLAS GraphML pipeline with validation.
    """
    print("🧪 Testing ATLAS GraphML Pipeline")
    print("=" * 50)
    
    # Create pipeline instance
    pipeline = ATLASGraphMLPipeline(dataset_name="test_atlas_graphml")
    
    # Test 1: Prerequisites validation
    print("\n1️⃣ Testing Prerequisites Validation...")
    if not pipeline.validate_prerequisites():
        print("❌ Prerequisites validation failed")
        return False
    print("✅ Prerequisites validation passed")
    
    # Test 2: Model initialization
    print("\n2️⃣ Testing Model Initialization...")
    if not pipeline.setup_models():
        print("❌ Model initialization failed")
        return False
    print("✅ Model initialization passed")
    
    # Test 3: Configuration setup
    print("\n3️⃣ Testing Configuration Setup...")
    if not pipeline.setup_configuration():
        print("❌ Configuration setup failed")
        return False
    print("✅ Configuration setup passed")
    
    print("\n🎉 All basic tests passed!")
    print("💡 The pipeline is ready for full execution")
    
    # Ask user if they want to run the full pipeline
    print("\n" + "=" * 50)
    print("🚀 Ready to run the complete pipeline?")
    print("This will process all JSON files and create GraphML output.")
    print("Estimated time: 10-30 minutes depending on document size")
    
    response = input("\nRun full pipeline? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n🚀 Running complete pipeline...")
        success = pipeline.run_complete_pipeline()
        
        if success:
            print("\n🎉 Complete pipeline test successful!")
            return True
        else:
            print("\n❌ Complete pipeline test failed!")
            return False
    else:
        print("\n✅ Basic tests completed successfully!")
        print("💡 Run 'python atlas_graphml_pipeline.py' when ready for full execution")
        return True

def main():
    """
    Main test function.
    """
    try:
        success = test_pipeline()
        if success:
            print("\n✅ All tests passed!")
            sys.exit(0)
        else:
            print("\n❌ Tests failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
