#!/usr/bin/env python3
"""
Concept Generation Resume Script
Resumes concept generation from where we left off (batch 3 of 69)
"""

from atlas_rag.kg_construction.triple_extraction import KnowledgeGraphExtractor
from atlas_rag.kg_construction.triple_config import ProcessingConfig
from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from configparser import ConfigParser
import os
import csv

def create_resume_config(dataset_name="360t_guide_direct_api_v2"):
    """
    Create processing configuration for resuming concept generation.
    Even more conservative settings to avoid timeouts.
    """
    
    config = ConfigParser()
    config.read('config.ini')
    model_name = config['settings']['LLM_MODEL']
    
    print(f"🔧 Creating resume configuration for {dataset_name}")
    print(f"Model: {model_name}")
    print(f"Ultra-conservative settings to avoid timeouts")
    
    # Ultra-conservative configuration
    processing_config = ProcessingConfig(
        model_path=model_name,
        data_directory="example_data",
        filename_pattern="",
        output_directory=f"import/{dataset_name}",
        
        # Ultra-small batches to avoid rate limits
        batch_size_triple=16,       
        batch_size_concept=4,       # Reduced from 8 to 4 (ultra-conservative)
        
        # Processing control
        total_shards_triple=1,
        current_shard_triple=0,
        total_shards_concept=1,
        current_shard_concept=0,
        
        # Conservative performance settings
        max_new_tokens=1024,        # Reduced from 2048 to 1024
        max_workers=1,              # Sequential processing
        
        # Processing options
        use_8bit=False,
        debug_mode=False,
        resume_from=0,
        record=True,
        remove_doc_spaces=True
    )
    
    print("✅ Ultra-conservative resume configuration created:")
    print(f"  - Batch size (concepts): {processing_config.batch_size_concept} (ultra-small)")
    print(f"  - Max tokens: {processing_config.max_new_tokens} (conservative)")
    print(f"  - Max workers: {processing_config.max_workers} (sequential)")
    
    return processing_config

def get_resume_position(concept_file):
    """
    Analyze existing concept file to determine resume position.
    """
    if not os.path.exists(concept_file):
        print("❌ Concept file not found, cannot resume")
        return 0, []
    
    existing_concepts = []
    with open(concept_file, 'r') as f:
        reader = csv.reader(f)
        header = next(reader)  # Skip header
        for row in reader:
            if len(row) >= 3:  # Ensure row has required columns
                existing_concepts.append(row[0])  # node column
    
    print(f"📊 Found {len(existing_concepts)} existing concepts")
    return len(existing_concepts), existing_concepts

def resume_concept_generation():
    """
    Resume concept generation from where we left off.
    """
    
    dataset_name = "360t_guide_direct_api_v2"
    concept_file = f"import/{dataset_name}/concepts/concept_shard_0.csv"
    
    print("🔄 AutoSchemaKG Concept Generation Resume")
    print(f"Dataset: {dataset_name}")
    print(f"Resume from existing progress")
    print()
    
    # Check existing progress
    existing_count, existing_concepts = get_resume_position(concept_file)
    if existing_count == 0:
        print("❌ No existing concepts found, use regular concept generation script")
        return False
    
    print(f"✅ Resuming from {existing_count} existing concepts")
    
    # Setup direct Gemini API
    print("🔌 Setting up direct Gemini API connection...")
    try:
        llm_generator = setup_gemini_llm_generator_direct()
        print("✅ Direct Gemini API connection established")
    except Exception as e:
        print(f"❌ Failed to setup Gemini API: {str(e)}")
        return False
    
    # Create resume configuration
    processing_config = create_resume_config(dataset_name)
    
    # Initialize knowledge graph extractor
    print("🏗️ Initializing knowledge graph extractor...")
    kg_extractor = KnowledgeGraphExtractor(
        model=llm_generator,
        config=processing_config
    )
    print("✅ Knowledge graph extractor initialized")
    
    try:
        print("\\n🔄 Resuming concept generation from existing progress...")
        print(f"⏭️ Skipping first {existing_count} concepts already processed")
        
        # This will continue from where we left off
        # The generate_concept_csv_temp method should handle existing data
        kg_extractor.generate_concept_csv_temp(language='en')
        print("✅ Concept generation resumed and completed")
        
        print("\\n3️⃣ Creating final concept CSV files...")
        kg_extractor.create_concept_csv()
        print("✅ Concept CSV files updated")
        
        print("\\n🎉 Concept generation resume completed successfully!")
        
        # Show final results
        if os.path.exists(concept_file):
            final_count, _ = get_resume_position(concept_file)
            print(f"\\n📊 Final results:")
            print(f"  - Started with: {existing_count} concepts")
            print(f"  - Final count: {final_count} concepts")
            print(f"  - New concepts added: {final_count - existing_count}")
        
        return True
        
    except Exception as e:
        print(f"\\n❌ Concept generation resume failed: {str(e)}")
        print("\\n🔧 Troubleshooting tips:")
        print("1. Check Gemini API key and connectivity")
        print("2. Verify existing concept files are valid")
        print("3. Check available API quota")
        return False

if __name__ == "__main__":
    print("🎯 AutoSchemaKG Concept Generation Resume")
    print("Ultra-conservative settings to avoid rate limits")
    print("=" * 60)
    
    success = resume_concept_generation()
    
    if success:
        print("\\n✅ Concept generation resume completed successfully!")
        print("\\n🎯 Next steps:")
        print("1. Re-generate GraphML with all concepts included")
        print("2. Import complete knowledge graph into Neo4j")
        print("3. Verify concept nodes and edges in the final graph")
    else:
        print("\\n❌ Concept generation resume failed")
        print("Consider using even smaller batch sizes or checking API limits")